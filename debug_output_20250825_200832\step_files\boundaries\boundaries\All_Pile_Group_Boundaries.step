ISO-10303-21;
HEADER;
FILE_DESCRIPTION(('Open CASCADE Model'),'2;1');
FILE_NAME('Pile_Group_Boundaries','2025-08-25T20:08:38',('Author'),(
    'Open CASCADE'),'Open CASCADE STEP processor 7.8','build123d',
  'Unknown');
FILE_SCHEMA(('AUTOMOTIVE_DESIGN { 1 0 10303 214 1 1 1 1 }'));
ENDSEC;
DATA;
#1 = APPLICATION_PROTOCOL_DEFINITION('international standard',
  'automotive_design',2000,#2);
#2 = APPLICATION_CONTEXT(
  'core data for automotive mechanical design processes');
#3 = SHAPE_DEFINITION_REPRESENTATION(#4,#10);
#4 = PRODUCT_DEFINITION_SHAPE('','',#5);
#5 = PRODUCT_DEFINITION('design','',#6,#9);
#6 = PRODUCT_DEFINITION_FORMATION('','',#7);
#7 = PRODUCT('Pile_Group_Boundaries','Pile_Group_Boundaries','',(#8));
#8 = PRODUCT_CONTEXT('',#2,'mechanical');
#9 = PRODUCT_DEFINITION_CONTEXT('part definition',#2,'design');
#10 = SHAPE_REPRESENTATION('',(#11,#15,#19,#23),#27);
#11 = AXIS2_PLACEMENT_3D('',#12,#13,#14);
#12 = CARTESIAN_POINT('',(0.,0.,0.));
#13 = DIRECTION('',(0.,0.,1.));
#14 = DIRECTION('',(1.,0.,-0.));
#15 = AXIS2_PLACEMENT_3D('',#16,#17,#18);
#16 = CARTESIAN_POINT('',(0.,0.,0.));
#17 = DIRECTION('',(0.,0.,1.));
#18 = DIRECTION('',(1.,0.,-0.));
#19 = AXIS2_PLACEMENT_3D('',#20,#21,#22);
#20 = CARTESIAN_POINT('',(0.,0.,0.));
#21 = DIRECTION('',(0.,0.,1.));
#22 = DIRECTION('',(1.,0.,-0.));
#23 = AXIS2_PLACEMENT_3D('',#24,#25,#26);
#24 = CARTESIAN_POINT('',(0.,0.,0.));
#25 = DIRECTION('',(0.,0.,1.));
#26 = DIRECTION('',(1.,0.,-0.));
#27 = ( GEOMETRIC_REPRESENTATION_CONTEXT(3) 
GLOBAL_UNCERTAINTY_ASSIGNED_CONTEXT((#31)) GLOBAL_UNIT_ASSIGNED_CONTEXT(
(#28,#29,#30)) REPRESENTATION_CONTEXT('Context #1',
  '3D Context with UNIT and UNCERTAINTY') );
#28 = ( LENGTH_UNIT() NAMED_UNIT(*) SI_UNIT(.MILLI.,.METRE.) );
#29 = ( NAMED_UNIT(*) PLANE_ANGLE_UNIT() SI_UNIT($,.RADIAN.) );
#30 = ( NAMED_UNIT(*) SI_UNIT($,.STERADIAN.) SOLID_ANGLE_UNIT() );
#31 = UNCERTAINTY_MEASURE_WITH_UNIT(LENGTH_MEASURE(1.E-07),#28,
  'distance_accuracy_value','confusion accuracy');
#32 = PRODUCT_RELATED_PRODUCT_CATEGORY('part',$,(#7));
#33 = SHAPE_DEFINITION_REPRESENTATION(#34,#40);
#34 = PRODUCT_DEFINITION_SHAPE('','',#35);
#35 = PRODUCT_DEFINITION('design','',#36,#39);
#36 = PRODUCT_DEFINITION_FORMATION('','',#37);
#37 = PRODUCT('Pile_Group_Boundary_1','Pile_Group_Boundary_1','',(#38));
#38 = PRODUCT_CONTEXT('',#2,'mechanical');
#39 = PRODUCT_DEFINITION_CONTEXT('part definition',#2,'design');
#40 = SHAPE_REPRESENTATION('',(#11,#41),#45);
#41 = AXIS2_PLACEMENT_3D('',#42,#43,#44);
#42 = CARTESIAN_POINT('',(0.,0.,0.));
#43 = DIRECTION('',(0.,0.,1.));
#44 = DIRECTION('',(1.,0.,-0.));
#45 = ( GEOMETRIC_REPRESENTATION_CONTEXT(3) 
GLOBAL_UNCERTAINTY_ASSIGNED_CONTEXT((#49)) GLOBAL_UNIT_ASSIGNED_CONTEXT(
(#46,#47,#48)) REPRESENTATION_CONTEXT('Context #1',
  '3D Context with UNIT and UNCERTAINTY') );
#46 = ( LENGTH_UNIT() NAMED_UNIT(*) SI_UNIT(.MILLI.,.METRE.) );
#47 = ( NAMED_UNIT(*) PLANE_ANGLE_UNIT() SI_UNIT($,.RADIAN.) );
#48 = ( NAMED_UNIT(*) SI_UNIT($,.STERADIAN.) SOLID_ANGLE_UNIT() );
#49 = UNCERTAINTY_MEASURE_WITH_UNIT(LENGTH_MEASURE(1.E-07),#46,
  'distance_accuracy_value','confusion accuracy');
#50 = CONTEXT_DEPENDENT_SHAPE_REPRESENTATION(#51,#53);
#51 = ( REPRESENTATION_RELATIONSHIP('','',#40,#10) 
REPRESENTATION_RELATIONSHIP_WITH_TRANSFORMATION(#52) 
SHAPE_REPRESENTATION_RELATIONSHIP() );
#52 = ITEM_DEFINED_TRANSFORMATION('','',#11,#15);
#53 = PRODUCT_DEFINITION_SHAPE('Placement','Placement of an item',#54);
#54 = NEXT_ASSEMBLY_USAGE_OCCURRENCE('214','=>[0:1:1:2]','',#5,#35,$);
#55 = PRODUCT_RELATED_PRODUCT_CATEGORY('part',$,(#37));
#56 = SHAPE_DEFINITION_REPRESENTATION(#57,#63);
#57 = PRODUCT_DEFINITION_SHAPE('','',#58);
#58 = PRODUCT_DEFINITION('design','',#59,#62);
#59 = PRODUCT_DEFINITION_FORMATION('','',#60);
#60 = PRODUCT('SOLID','SOLID','',(#61));
#61 = PRODUCT_CONTEXT('',#2,'mechanical');
#62 = PRODUCT_DEFINITION_CONTEXT('part definition',#2,'design');
#63 = ADVANCED_BREP_SHAPE_REPRESENTATION('',(#11,#64),#1174);
#64 = MANIFOLD_SOLID_BREP('',#65);
#65 = CLOSED_SHELL('',(#66,#186,#262,#338,#414,#490,#566,#642,#718,#794,
    #870,#946,#1022,#1093,#1140,#1157));
#66 = ADVANCED_FACE('',(#67),#81,.F.);
#67 = FACE_BOUND('',#68,.F.);
#68 = EDGE_LOOP('',(#69,#104,#132,#160));
#69 = ORIENTED_EDGE('',*,*,#70,.T.);
#70 = EDGE_CURVE('',#71,#73,#75,.T.);
#71 = VERTEX_POINT('',#72);
#72 = CARTESIAN_POINT('',(46.945771,143.583803,28.));
#73 = VERTEX_POINT('',#74);
#74 = CARTESIAN_POINT('',(46.945771,143.583803,-13.767115));
#75 = SURFACE_CURVE('',#76,(#80,#92),.PCURVE_S1.);
#76 = LINE('',#77,#78);
#77 = CARTESIAN_POINT('',(46.945771,143.583803,28.));
#78 = VECTOR('',#79,1.);
#79 = DIRECTION('',(0.,0.,-1.));
#80 = PCURVE('',#81,#86);
#81 = PLANE('',#82);
#82 = AXIS2_PLACEMENT_3D('',#83,#84,#85);
#83 = CARTESIAN_POINT('',(46.945771,143.583803,28.));
#84 = DIRECTION('',(0.132487231294,-0.991184712122,0.));
#85 = DIRECTION('',(0.991184712122,0.132487231294,0.));
#86 = DEFINITIONAL_REPRESENTATION('',(#87),#91);
#87 = LINE('',#88,#89);
#88 = CARTESIAN_POINT('',(0.,0.));
#89 = VECTOR('',#90,1.);
#90 = DIRECTION('',(0.,-1.));
#91 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#92 = PCURVE('',#93,#98);
#93 = PLANE('',#94);
#94 = AXIS2_PLACEMENT_3D('',#95,#96,#97);
#95 = CARTESIAN_POINT('',(48.301331,131.800116,28.));
#96 = DIRECTION('',(0.993448200572,0.11428330053,0.));
#97 = DIRECTION('',(-0.11428330053,0.993448200572,0.));
#98 = DEFINITIONAL_REPRESENTATION('',(#99),#103);
#99 = LINE('',#100,#101);
#100 = CARTESIAN_POINT('',(11.861400517121,0.));
#101 = VECTOR('',#102,1.);
#102 = DIRECTION('',(0.,-1.));
#103 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#104 = ORIENTED_EDGE('',*,*,#105,.T.);
#105 = EDGE_CURVE('',#73,#106,#108,.T.);
#106 = VERTEX_POINT('',#107);
#107 = CARTESIAN_POINT('',(71.189538,146.824359,-13.767115));
#108 = SURFACE_CURVE('',#109,(#113,#120),.PCURVE_S1.);
#109 = LINE('',#110,#111);
#110 = CARTESIAN_POINT('',(46.945771,143.583803,-13.767115));
#111 = VECTOR('',#112,1.);
#112 = DIRECTION('',(0.991184712122,0.132487231294,0.));
#113 = PCURVE('',#81,#114);
#114 = DEFINITIONAL_REPRESENTATION('',(#115),#119);
#115 = LINE('',#116,#117);
#116 = CARTESIAN_POINT('',(-7.042790969946E-15,-41.767115));
#117 = VECTOR('',#118,1.);
#118 = DIRECTION('',(1.,0.));
#119 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#120 = PCURVE('',#121,#126);
#121 = PLANE('',#122);
#122 = AXIS2_PLACEMENT_3D('',#123,#124,#125);
#123 = CARTESIAN_POINT('',(80.007160191567,135.247798948,-13.767115));
#124 = DIRECTION('',(0.,0.,-1.));
#125 = DIRECTION('',(-1.,0.,0.));
#126 = DEFINITIONAL_REPRESENTATION('',(#127),#131);
#127 = LINE('',#128,#129);
#128 = CARTESIAN_POINT('',(33.061389191567,8.336004051997));
#129 = VECTOR('',#130,1.);
#130 = DIRECTION('',(-0.991184712122,0.132487231294));
#131 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#132 = ORIENTED_EDGE('',*,*,#133,.F.);
#133 = EDGE_CURVE('',#134,#106,#136,.T.);
#134 = VERTEX_POINT('',#135);
#135 = CARTESIAN_POINT('',(71.189538,146.824359,28.));
#136 = SURFACE_CURVE('',#137,(#141,#148),.PCURVE_S1.);
#137 = LINE('',#138,#139);
#138 = CARTESIAN_POINT('',(71.189538,146.824359,28.));
#139 = VECTOR('',#140,1.);
#140 = DIRECTION('',(0.,0.,-1.));
#141 = PCURVE('',#81,#142);
#142 = DEFINITIONAL_REPRESENTATION('',(#143),#147);
#143 = LINE('',#144,#145);
#144 = CARTESIAN_POINT('',(24.459383506937,0.));
#145 = VECTOR('',#146,1.);
#146 = DIRECTION('',(0.,-1.));
#147 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#148 = PCURVE('',#149,#154);
#149 = PLANE('',#150);
#150 = AXIS2_PLACEMENT_3D('',#151,#152,#153);
#151 = CARTESIAN_POINT('',(71.189538,146.824359,28.));
#152 = DIRECTION('',(0.993746145311,0.111662879591,0.));
#153 = DIRECTION('',(-0.111662879591,0.993746145311,0.));
#154 = DEFINITIONAL_REPRESENTATION('',(#155),#159);
#155 = LINE('',#156,#157);
#156 = CARTESIAN_POINT('',(0.,0.));
#157 = VECTOR('',#158,1.);
#158 = DIRECTION('',(0.,-1.));
#159 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#160 = ORIENTED_EDGE('',*,*,#161,.F.);
#161 = EDGE_CURVE('',#71,#134,#162,.T.);
#162 = SURFACE_CURVE('',#163,(#167,#174),.PCURVE_S1.);
#163 = LINE('',#164,#165);
#164 = CARTESIAN_POINT('',(46.945771,143.583803,28.));
#165 = VECTOR('',#166,1.);
#166 = DIRECTION('',(0.991184712122,0.132487231294,0.));
#167 = PCURVE('',#81,#168);
#168 = DEFINITIONAL_REPRESENTATION('',(#169),#173);
#169 = LINE('',#170,#171);
#170 = CARTESIAN_POINT('',(-7.042790969946E-15,0.));
#171 = VECTOR('',#172,1.);
#172 = DIRECTION('',(1.,0.));
#173 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#174 = PCURVE('',#175,#180);
#175 = PLANE('',#176);
#176 = AXIS2_PLACEMENT_3D('',#177,#178,#179);
#177 = CARTESIAN_POINT('',(80.007160191567,135.247798948,28.));
#178 = DIRECTION('',(0.,0.,-1.));
#179 = DIRECTION('',(-1.,0.,0.));
#180 = DEFINITIONAL_REPRESENTATION('',(#181),#185);
#181 = LINE('',#182,#183);
#182 = CARTESIAN_POINT('',(33.061389191567,8.336004051997));
#183 = VECTOR('',#184,1.);
#184 = DIRECTION('',(-0.991184712122,0.132487231294));
#185 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#186 = ADVANCED_FACE('',(#187),#149,.F.);
#187 = FACE_BOUND('',#188,.F.);
#188 = EDGE_LOOP('',(#189,#190,#213,#241));
#189 = ORIENTED_EDGE('',*,*,#133,.T.);
#190 = ORIENTED_EDGE('',*,*,#191,.T.);
#191 = EDGE_CURVE('',#106,#192,#194,.T.);
#192 = VERTEX_POINT('',#193);
#193 = CARTESIAN_POINT('',(71.017293,148.357257,-13.767115));
#194 = SURFACE_CURVE('',#195,(#199,#206),.PCURVE_S1.);
#195 = LINE('',#196,#197);
#196 = CARTESIAN_POINT('',(71.189538,146.824359,-13.767115));
#197 = VECTOR('',#198,1.);
#198 = DIRECTION('',(-0.111662879591,0.993746145311,0.));
#199 = PCURVE('',#149,#200);
#200 = DEFINITIONAL_REPRESENTATION('',(#201),#205);
#201 = LINE('',#202,#203);
#202 = CARTESIAN_POINT('',(-2.824396418963E-14,-41.767115));
#203 = VECTOR('',#204,1.);
#204 = DIRECTION('',(1.,0.));
#205 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#206 = PCURVE('',#121,#207);
#207 = DEFINITIONAL_REPRESENTATION('',(#208),#212);
#208 = LINE('',#209,#210);
#209 = CARTESIAN_POINT('',(8.817622191567,11.576560051997));
#210 = VECTOR('',#211,1.);
#211 = DIRECTION('',(0.111662879591,0.993746145311));
#212 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#213 = ORIENTED_EDGE('',*,*,#214,.F.);
#214 = EDGE_CURVE('',#215,#192,#217,.T.);
#215 = VERTEX_POINT('',#216);
#216 = CARTESIAN_POINT('',(71.017293,148.357257,28.));
#217 = SURFACE_CURVE('',#218,(#222,#229),.PCURVE_S1.);
#218 = LINE('',#219,#220);
#219 = CARTESIAN_POINT('',(71.017293,148.357257,28.));
#220 = VECTOR('',#221,1.);
#221 = DIRECTION('',(0.,0.,-1.));
#222 = PCURVE('',#149,#223);
#223 = DEFINITIONAL_REPRESENTATION('',(#224),#228);
#224 = LINE('',#225,#226);
#225 = CARTESIAN_POINT('',(1.542544851351,0.));
#226 = VECTOR('',#227,1.);
#227 = DIRECTION('',(0.,-1.));
#228 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#229 = PCURVE('',#230,#235);
#230 = PLANE('',#231);
#231 = AXIS2_PLACEMENT_3D('',#232,#233,#234);
#232 = CARTESIAN_POINT('',(71.017293,148.357257,28.));
#233 = DIRECTION('',(-7.3194503953E-03,-0.999973212464,-0.));
#234 = DIRECTION('',(0.999973212464,-7.3194503953E-03,0.));
#235 = DEFINITIONAL_REPRESENTATION('',(#236),#240);
#236 = LINE('',#237,#238);
#237 = CARTESIAN_POINT('',(0.,0.));
#238 = VECTOR('',#239,1.);
#239 = DIRECTION('',(0.,-1.));
#240 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#241 = ORIENTED_EDGE('',*,*,#242,.F.);
#242 = EDGE_CURVE('',#134,#215,#243,.T.);
#243 = SURFACE_CURVE('',#244,(#248,#255),.PCURVE_S1.);
#244 = LINE('',#245,#246);
#245 = CARTESIAN_POINT('',(71.189538,146.824359,28.));
#246 = VECTOR('',#247,1.);
#247 = DIRECTION('',(-0.111662879591,0.993746145311,0.));
#248 = PCURVE('',#149,#249);
#249 = DEFINITIONAL_REPRESENTATION('',(#250),#254);
#250 = LINE('',#251,#252);
#251 = CARTESIAN_POINT('',(-2.824396418963E-14,0.));
#252 = VECTOR('',#253,1.);
#253 = DIRECTION('',(1.,0.));
#254 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#255 = PCURVE('',#175,#256);
#256 = DEFINITIONAL_REPRESENTATION('',(#257),#261);
#257 = LINE('',#258,#259);
#258 = CARTESIAN_POINT('',(8.817622191567,11.576560051997));
#259 = VECTOR('',#260,1.);
#260 = DIRECTION('',(0.111662879591,0.993746145311));
#261 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#262 = ADVANCED_FACE('',(#263),#230,.F.);
#263 = FACE_BOUND('',#264,.F.);
#264 = EDGE_LOOP('',(#265,#266,#289,#317));
#265 = ORIENTED_EDGE('',*,*,#214,.T.);
#266 = ORIENTED_EDGE('',*,*,#267,.T.);
#267 = EDGE_CURVE('',#192,#268,#270,.T.);
#268 = VERTEX_POINT('',#269);
#269 = CARTESIAN_POINT('',(91.496834,148.207354,-13.767115));
#270 = SURFACE_CURVE('',#271,(#275,#282),.PCURVE_S1.);
#271 = LINE('',#272,#273);
#272 = CARTESIAN_POINT('',(71.017293,148.357257,-13.767115));
#273 = VECTOR('',#274,1.);
#274 = DIRECTION('',(0.999973212464,-7.3194503953E-03,0.));
#275 = PCURVE('',#230,#276);
#276 = DEFINITIONAL_REPRESENTATION('',(#277),#281);
#277 = LINE('',#278,#279);
#278 = CARTESIAN_POINT('',(-1.421047404142E-14,-41.767115));
#279 = VECTOR('',#280,1.);
#280 = DIRECTION('',(1.,0.));
#281 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#282 = PCURVE('',#121,#283);
#283 = DEFINITIONAL_REPRESENTATION('',(#284),#288);
#284 = LINE('',#285,#286);
#285 = CARTESIAN_POINT('',(8.989867191567,13.109458051997));
#286 = VECTOR('',#287,1.);
#287 = DIRECTION('',(-0.999973212464,-7.3194503953E-03));
#288 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#289 = ORIENTED_EDGE('',*,*,#290,.F.);
#290 = EDGE_CURVE('',#291,#268,#293,.T.);
#291 = VERTEX_POINT('',#292);
#292 = CARTESIAN_POINT('',(91.496834,148.207354,28.));
#293 = SURFACE_CURVE('',#294,(#298,#305),.PCURVE_S1.);
#294 = LINE('',#295,#296);
#295 = CARTESIAN_POINT('',(91.496834,148.207354,28.));
#296 = VECTOR('',#297,1.);
#297 = DIRECTION('',(0.,0.,-1.));
#298 = PCURVE('',#230,#299);
#299 = DEFINITIONAL_REPRESENTATION('',(#300),#304);
#300 = LINE('',#301,#302);
#301 = CARTESIAN_POINT('',(20.480089611134,0.));
#302 = VECTOR('',#303,1.);
#303 = DIRECTION('',(0.,-1.));
#304 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#305 = PCURVE('',#306,#311);
#306 = PLANE('',#307);
#307 = AXIS2_PLACEMENT_3D('',#308,#309,#310);
#308 = CARTESIAN_POINT('',(91.496834,148.207354,28.));
#309 = DIRECTION('',(-0.175155373413,-0.984540804215,-0.));
#310 = DIRECTION('',(0.984540804215,-0.175155373413,0.));
#311 = DEFINITIONAL_REPRESENTATION('',(#312),#316);
#312 = LINE('',#313,#314);
#313 = CARTESIAN_POINT('',(0.,0.));
#314 = VECTOR('',#315,1.);
#315 = DIRECTION('',(0.,-1.));
#316 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#317 = ORIENTED_EDGE('',*,*,#318,.F.);
#318 = EDGE_CURVE('',#215,#291,#319,.T.);
#319 = SURFACE_CURVE('',#320,(#324,#331),.PCURVE_S1.);
#320 = LINE('',#321,#322);
#321 = CARTESIAN_POINT('',(71.017293,148.357257,28.));
#322 = VECTOR('',#323,1.);
#323 = DIRECTION('',(0.999973212464,-7.3194503953E-03,0.));
#324 = PCURVE('',#230,#325);
#325 = DEFINITIONAL_REPRESENTATION('',(#326),#330);
#326 = LINE('',#327,#328);
#327 = CARTESIAN_POINT('',(-1.421047404142E-14,0.));
#328 = VECTOR('',#329,1.);
#329 = DIRECTION('',(1.,0.));
#330 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#331 = PCURVE('',#175,#332);
#332 = DEFINITIONAL_REPRESENTATION('',(#333),#337);
#333 = LINE('',#334,#335);
#334 = CARTESIAN_POINT('',(8.989867191567,13.109458051997));
#335 = VECTOR('',#336,1.);
#336 = DIRECTION('',(-0.999973212464,-7.3194503953E-03));
#337 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#338 = ADVANCED_FACE('',(#339),#306,.F.);
#339 = FACE_BOUND('',#340,.F.);
#340 = EDGE_LOOP('',(#341,#342,#365,#393));
#341 = ORIENTED_EDGE('',*,*,#290,.T.);
#342 = ORIENTED_EDGE('',*,*,#343,.T.);
#343 = EDGE_CURVE('',#268,#344,#346,.T.);
#344 = VERTEX_POINT('',#345);
#345 = CARTESIAN_POINT('',(96.153156,147.378968,-13.767115));
#346 = SURFACE_CURVE('',#347,(#351,#358),.PCURVE_S1.);
#347 = LINE('',#348,#349);
#348 = CARTESIAN_POINT('',(91.496834,148.207354,-13.767115));
#349 = VECTOR('',#350,1.);
#350 = DIRECTION('',(0.984540804215,-0.175155373413,0.));
#351 = PCURVE('',#306,#352);
#352 = DEFINITIONAL_REPRESENTATION('',(#353),#357);
#353 = LINE('',#354,#355);
#354 = CARTESIAN_POINT('',(1.399116632989E-14,-41.767115));
#355 = VECTOR('',#356,1.);
#356 = DIRECTION('',(1.,0.));
#357 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#358 = PCURVE('',#121,#359);
#359 = DEFINITIONAL_REPRESENTATION('',(#360),#364);
#360 = LINE('',#361,#362);
#361 = CARTESIAN_POINT('',(-11.48967380843,12.959555051997));
#362 = VECTOR('',#363,1.);
#363 = DIRECTION('',(-0.984540804215,-0.175155373413));
#364 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#365 = ORIENTED_EDGE('',*,*,#366,.F.);
#366 = EDGE_CURVE('',#367,#344,#369,.T.);
#367 = VERTEX_POINT('',#368);
#368 = CARTESIAN_POINT('',(96.153156,147.378968,28.));
#369 = SURFACE_CURVE('',#370,(#374,#381),.PCURVE_S1.);
#370 = LINE('',#371,#372);
#371 = CARTESIAN_POINT('',(96.153156,147.378968,28.));
#372 = VECTOR('',#373,1.);
#373 = DIRECTION('',(0.,0.,-1.));
#374 = PCURVE('',#306,#375);
#375 = DEFINITIONAL_REPRESENTATION('',(#376),#380);
#376 = LINE('',#377,#378);
#377 = CARTESIAN_POINT('',(4.729435265725,0.));
#378 = VECTOR('',#379,1.);
#379 = DIRECTION('',(0.,-1.));
#380 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#381 = PCURVE('',#382,#387);
#382 = PLANE('',#383);
#383 = AXIS2_PLACEMENT_3D('',#384,#385,#386);
#384 = CARTESIAN_POINT('',(96.153156,147.378968,28.));
#385 = DIRECTION('',(-3.993835685532E-03,-0.999992024606,-0.));
#386 = DIRECTION('',(0.999992024606,-3.993835685532E-03,0.));
#387 = DEFINITIONAL_REPRESENTATION('',(#388),#392);
#388 = LINE('',#389,#390);
#389 = CARTESIAN_POINT('',(0.,0.));
#390 = VECTOR('',#391,1.);
#391 = DIRECTION('',(0.,-1.));
#392 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#393 = ORIENTED_EDGE('',*,*,#394,.F.);
#394 = EDGE_CURVE('',#291,#367,#395,.T.);
#395 = SURFACE_CURVE('',#396,(#400,#407),.PCURVE_S1.);
#396 = LINE('',#397,#398);
#397 = CARTESIAN_POINT('',(91.496834,148.207354,28.));
#398 = VECTOR('',#399,1.);
#399 = DIRECTION('',(0.984540804215,-0.175155373413,0.));
#400 = PCURVE('',#306,#401);
#401 = DEFINITIONAL_REPRESENTATION('',(#402),#406);
#402 = LINE('',#403,#404);
#403 = CARTESIAN_POINT('',(1.399116632989E-14,0.));
#404 = VECTOR('',#405,1.);
#405 = DIRECTION('',(1.,0.));
#406 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#407 = PCURVE('',#175,#408);
#408 = DEFINITIONAL_REPRESENTATION('',(#409),#413);
#409 = LINE('',#410,#411);
#410 = CARTESIAN_POINT('',(-11.48967380843,12.959555051997));
#411 = VECTOR('',#412,1.);
#412 = DIRECTION('',(-0.984540804215,-0.175155373413));
#413 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#414 = ADVANCED_FACE('',(#415),#382,.F.);
#415 = FACE_BOUND('',#416,.F.);
#416 = EDGE_LOOP('',(#417,#418,#441,#469));
#417 = ORIENTED_EDGE('',*,*,#366,.T.);
#418 = ORIENTED_EDGE('',*,*,#419,.T.);
#419 = EDGE_CURVE('',#344,#420,#422,.T.);
#420 = VERTEX_POINT('',#421);
#421 = CARTESIAN_POINT('',(112.117381,147.315209,-13.767115));
#422 = SURFACE_CURVE('',#423,(#427,#434),.PCURVE_S1.);
#423 = LINE('',#424,#425);
#424 = CARTESIAN_POINT('',(96.153156,147.378968,-13.767115));
#425 = VECTOR('',#426,1.);
#426 = DIRECTION('',(0.999992024606,-3.993835685532E-03,0.));
#427 = PCURVE('',#382,#428);
#428 = DEFINITIONAL_REPRESENTATION('',(#429),#433);
#429 = LINE('',#430,#431);
#430 = CARTESIAN_POINT('',(1.421074137804E-14,-41.767115));
#431 = VECTOR('',#432,1.);
#432 = DIRECTION('',(1.,0.));
#433 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#434 = PCURVE('',#121,#435);
#435 = DEFINITIONAL_REPRESENTATION('',(#436),#440);
#436 = LINE('',#437,#438);
#437 = CARTESIAN_POINT('',(-16.14599580843,12.131169051997));
#438 = VECTOR('',#439,1.);
#439 = DIRECTION('',(-0.999992024606,-3.993835685532E-03));
#440 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#441 = ORIENTED_EDGE('',*,*,#442,.F.);
#442 = EDGE_CURVE('',#443,#420,#445,.T.);
#443 = VERTEX_POINT('',#444);
#444 = CARTESIAN_POINT('',(112.117381,147.315209,28.));
#445 = SURFACE_CURVE('',#446,(#450,#457),.PCURVE_S1.);
#446 = LINE('',#447,#448);
#447 = CARTESIAN_POINT('',(112.117381,147.315209,28.));
#448 = VECTOR('',#449,1.);
#449 = DIRECTION('',(0.,0.,-1.));
#450 = PCURVE('',#382,#451);
#451 = DEFINITIONAL_REPRESENTATION('',(#452),#456);
#452 = LINE('',#453,#454);
#453 = CARTESIAN_POINT('',(15.964352321992,0.));
#454 = VECTOR('',#455,1.);
#455 = DIRECTION('',(0.,-1.));
#456 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#457 = PCURVE('',#458,#463);
#458 = PLANE('',#459);
#459 = AXIS2_PLACEMENT_3D('',#460,#461,#462);
#460 = CARTESIAN_POINT('',(112.117381,147.315209,28.));
#461 = DIRECTION('',(-0.999997968285,-2.015794036709E-03,-0.));
#462 = DIRECTION('',(2.015794036709E-03,-0.999997968285,0.));
#463 = DEFINITIONAL_REPRESENTATION('',(#464),#468);
#464 = LINE('',#465,#466);
#465 = CARTESIAN_POINT('',(0.,0.));
#466 = VECTOR('',#467,1.);
#467 = DIRECTION('',(0.,-1.));
#468 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#469 = ORIENTED_EDGE('',*,*,#470,.F.);
#470 = EDGE_CURVE('',#367,#443,#471,.T.);
#471 = SURFACE_CURVE('',#472,(#476,#483),.PCURVE_S1.);
#472 = LINE('',#473,#474);
#473 = CARTESIAN_POINT('',(96.153156,147.378968,28.));
#474 = VECTOR('',#475,1.);
#475 = DIRECTION('',(0.999992024606,-3.993835685532E-03,0.));
#476 = PCURVE('',#382,#477);
#477 = DEFINITIONAL_REPRESENTATION('',(#478),#482);
#478 = LINE('',#479,#480);
#479 = CARTESIAN_POINT('',(1.421074137804E-14,0.));
#480 = VECTOR('',#481,1.);
#481 = DIRECTION('',(1.,0.));
#482 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#483 = PCURVE('',#175,#484);
#484 = DEFINITIONAL_REPRESENTATION('',(#485),#489);
#485 = LINE('',#486,#487);
#486 = CARTESIAN_POINT('',(-16.14599580843,12.131169051997));
#487 = VECTOR('',#488,1.);
#488 = DIRECTION('',(-0.999992024606,-3.993835685532E-03));
#489 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#490 = ADVANCED_FACE('',(#491),#458,.F.);
#491 = FACE_BOUND('',#492,.F.);
#492 = EDGE_LOOP('',(#493,#494,#517,#545));
#493 = ORIENTED_EDGE('',*,*,#442,.T.);
#494 = ORIENTED_EDGE('',*,*,#495,.T.);
#495 = EDGE_CURVE('',#420,#496,#498,.T.);
#496 = VERTEX_POINT('',#497);
#497 = CARTESIAN_POINT('',(112.160196,126.075483,-13.767115));
#498 = SURFACE_CURVE('',#499,(#503,#510),.PCURVE_S1.);
#499 = LINE('',#500,#501);
#500 = CARTESIAN_POINT('',(112.117381,147.315209,-13.767115));
#501 = VECTOR('',#502,1.);
#502 = DIRECTION('',(2.015794036709E-03,-0.999997968285,0.));
#503 = PCURVE('',#458,#504);
#504 = DEFINITIONAL_REPRESENTATION('',(#505),#509);
#505 = LINE('',#506,#507);
#506 = CARTESIAN_POINT('',(2.842165168559E-14,-41.767115));
#507 = VECTOR('',#508,1.);
#508 = DIRECTION('',(1.,0.));
#509 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#510 = PCURVE('',#121,#511);
#511 = DEFINITIONAL_REPRESENTATION('',(#512),#516);
#512 = LINE('',#513,#514);
#513 = CARTESIAN_POINT('',(-32.11022080843,12.067410051997));
#514 = VECTOR('',#515,1.);
#515 = DIRECTION('',(-2.015794036709E-03,-0.999997968285));
#516 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#517 = ORIENTED_EDGE('',*,*,#518,.F.);
#518 = EDGE_CURVE('',#519,#496,#521,.T.);
#519 = VERTEX_POINT('',#520);
#520 = CARTESIAN_POINT('',(112.160196,126.075483,28.));
#521 = SURFACE_CURVE('',#522,(#526,#533),.PCURVE_S1.);
#522 = LINE('',#523,#524);
#523 = CARTESIAN_POINT('',(112.160196,126.075483,28.));
#524 = VECTOR('',#525,1.);
#525 = DIRECTION('',(0.,0.,-1.));
#526 = PCURVE('',#458,#527);
#527 = DEFINITIONAL_REPRESENTATION('',(#528),#532);
#528 = LINE('',#529,#530);
#529 = CARTESIAN_POINT('',(21.239769153155,0.));
#530 = VECTOR('',#531,1.);
#531 = DIRECTION('',(0.,-1.));
#532 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#533 = PCURVE('',#534,#539);
#534 = PLANE('',#535);
#535 = AXIS2_PLACEMENT_3D('',#536,#537,#538);
#536 = CARTESIAN_POINT('',(112.160196,126.075483,28.));
#537 = DIRECTION('',(0.,1.,0.));
#538 = DIRECTION('',(-1.,0.,0.));
#539 = DEFINITIONAL_REPRESENTATION('',(#540),#544);
#540 = LINE('',#541,#542);
#541 = CARTESIAN_POINT('',(0.,0.));
#542 = VECTOR('',#543,1.);
#543 = DIRECTION('',(0.,-1.));
#544 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#545 = ORIENTED_EDGE('',*,*,#546,.F.);
#546 = EDGE_CURVE('',#443,#519,#547,.T.);
#547 = SURFACE_CURVE('',#548,(#552,#559),.PCURVE_S1.);
#548 = LINE('',#549,#550);
#549 = CARTESIAN_POINT('',(112.117381,147.315209,28.));
#550 = VECTOR('',#551,1.);
#551 = DIRECTION('',(2.015794036709E-03,-0.999997968285,0.));
#552 = PCURVE('',#458,#553);
#553 = DEFINITIONAL_REPRESENTATION('',(#554),#558);
#554 = LINE('',#555,#556);
#555 = CARTESIAN_POINT('',(2.842165168559E-14,0.));
#556 = VECTOR('',#557,1.);
#557 = DIRECTION('',(1.,0.));
#558 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#559 = PCURVE('',#175,#560);
#560 = DEFINITIONAL_REPRESENTATION('',(#561),#565);
#561 = LINE('',#562,#563);
#562 = CARTESIAN_POINT('',(-32.11022080843,12.067410051997));
#563 = VECTOR('',#564,1.);
#564 = DIRECTION('',(-2.015794036709E-03,-0.999997968285));
#565 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#566 = ADVANCED_FACE('',(#567),#534,.F.);
#567 = FACE_BOUND('',#568,.F.);
#568 = EDGE_LOOP('',(#569,#570,#593,#621));
#569 = ORIENTED_EDGE('',*,*,#518,.T.);
#570 = ORIENTED_EDGE('',*,*,#571,.T.);
#571 = EDGE_CURVE('',#496,#572,#574,.T.);
#572 = VERTEX_POINT('',#573);
#573 = CARTESIAN_POINT('',(85.917422,126.075483,-13.767115));
#574 = SURFACE_CURVE('',#575,(#579,#586),.PCURVE_S1.);
#575 = LINE('',#576,#577);
#576 = CARTESIAN_POINT('',(112.160196,126.075483,-13.767115));
#577 = VECTOR('',#578,1.);
#578 = DIRECTION('',(-1.,0.,0.));
#579 = PCURVE('',#534,#580);
#580 = DEFINITIONAL_REPRESENTATION('',(#581),#585);
#581 = LINE('',#582,#583);
#582 = CARTESIAN_POINT('',(0.,-41.767115));
#583 = VECTOR('',#584,1.);
#584 = DIRECTION('',(1.,0.));
#585 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#586 = PCURVE('',#121,#587);
#587 = DEFINITIONAL_REPRESENTATION('',(#588),#592);
#588 = LINE('',#589,#590);
#589 = CARTESIAN_POINT('',(-32.15303580843,-9.172315948003));
#590 = VECTOR('',#591,1.);
#591 = DIRECTION('',(1.,0.));
#592 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#593 = ORIENTED_EDGE('',*,*,#594,.F.);
#594 = EDGE_CURVE('',#595,#572,#597,.T.);
#595 = VERTEX_POINT('',#596);
#596 = CARTESIAN_POINT('',(85.917422,126.075483,28.));
#597 = SURFACE_CURVE('',#598,(#602,#609),.PCURVE_S1.);
#598 = LINE('',#599,#600);
#599 = CARTESIAN_POINT('',(85.917422,126.075483,28.));
#600 = VECTOR('',#601,1.);
#601 = DIRECTION('',(0.,0.,-1.));
#602 = PCURVE('',#534,#603);
#603 = DEFINITIONAL_REPRESENTATION('',(#604),#608);
#604 = LINE('',#605,#606);
#605 = CARTESIAN_POINT('',(26.242774,0.));
#606 = VECTOR('',#607,1.);
#607 = DIRECTION('',(0.,-1.));
#608 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#609 = PCURVE('',#610,#615);
#610 = PLANE('',#611);
#611 = AXIS2_PLACEMENT_3D('',#612,#613,#614);
#612 = CARTESIAN_POINT('',(85.917422,126.075483,28.));
#613 = DIRECTION('',(-1.,-0.,-0.));
#614 = DIRECTION('',(0.,-1.,0.));
#615 = DEFINITIONAL_REPRESENTATION('',(#616),#620);
#616 = LINE('',#617,#618);
#617 = CARTESIAN_POINT('',(0.,0.));
#618 = VECTOR('',#619,1.);
#619 = DIRECTION('',(0.,-1.));
#620 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#621 = ORIENTED_EDGE('',*,*,#622,.F.);
#622 = EDGE_CURVE('',#519,#595,#623,.T.);
#623 = SURFACE_CURVE('',#624,(#628,#635),.PCURVE_S1.);
#624 = LINE('',#625,#626);
#625 = CARTESIAN_POINT('',(112.160196,126.075483,28.));
#626 = VECTOR('',#627,1.);
#627 = DIRECTION('',(-1.,0.,0.));
#628 = PCURVE('',#534,#629);
#629 = DEFINITIONAL_REPRESENTATION('',(#630),#634);
#630 = LINE('',#631,#632);
#631 = CARTESIAN_POINT('',(0.,0.));
#632 = VECTOR('',#633,1.);
#633 = DIRECTION('',(1.,0.));
#634 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#635 = PCURVE('',#175,#636);
#636 = DEFINITIONAL_REPRESENTATION('',(#637),#641);
#637 = LINE('',#638,#639);
#638 = CARTESIAN_POINT('',(-32.15303580843,-9.172315948003));
#639 = VECTOR('',#640,1.);
#640 = DIRECTION('',(1.,0.));
#641 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#642 = ADVANCED_FACE('',(#643),#610,.F.);
#643 = FACE_BOUND('',#644,.F.);
#644 = EDGE_LOOP('',(#645,#646,#669,#697));
#645 = ORIENTED_EDGE('',*,*,#594,.T.);
#646 = ORIENTED_EDGE('',*,*,#647,.T.);
#647 = EDGE_CURVE('',#572,#648,#650,.T.);
#648 = VERTEX_POINT('',#649);
#649 = CARTESIAN_POINT('',(85.917422,122.856505,-13.767115));
#650 = SURFACE_CURVE('',#651,(#655,#662),.PCURVE_S1.);
#651 = LINE('',#652,#653);
#652 = CARTESIAN_POINT('',(85.917422,126.075483,-13.767115));
#653 = VECTOR('',#654,1.);
#654 = DIRECTION('',(0.,-1.,0.));
#655 = PCURVE('',#610,#656);
#656 = DEFINITIONAL_REPRESENTATION('',(#657),#661);
#657 = LINE('',#658,#659);
#658 = CARTESIAN_POINT('',(-1.42108547152E-14,-41.767115));
#659 = VECTOR('',#660,1.);
#660 = DIRECTION('',(1.,0.));
#661 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#662 = PCURVE('',#121,#663);
#663 = DEFINITIONAL_REPRESENTATION('',(#664),#668);
#664 = LINE('',#665,#666);
#665 = CARTESIAN_POINT('',(-5.910261808433,-9.172315948003));
#666 = VECTOR('',#667,1.);
#667 = DIRECTION('',(0.,-1.));
#668 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#669 = ORIENTED_EDGE('',*,*,#670,.F.);
#670 = EDGE_CURVE('',#671,#648,#673,.T.);
#671 = VERTEX_POINT('',#672);
#672 = CARTESIAN_POINT('',(85.917422,122.856505,28.));
#673 = SURFACE_CURVE('',#674,(#678,#685),.PCURVE_S1.);
#674 = LINE('',#675,#676);
#675 = CARTESIAN_POINT('',(85.917422,122.856505,28.));
#676 = VECTOR('',#677,1.);
#677 = DIRECTION('',(0.,0.,-1.));
#678 = PCURVE('',#610,#679);
#679 = DEFINITIONAL_REPRESENTATION('',(#680),#684);
#680 = LINE('',#681,#682);
#681 = CARTESIAN_POINT('',(3.218978,0.));
#682 = VECTOR('',#683,1.);
#683 = DIRECTION('',(0.,-1.));
#684 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#685 = PCURVE('',#686,#691);
#686 = PLANE('',#687);
#687 = AXIS2_PLACEMENT_3D('',#688,#689,#690);
#688 = CARTESIAN_POINT('',(85.917422,122.856505,28.));
#689 = DIRECTION('',(0.,1.,0.));
#690 = DIRECTION('',(-1.,0.,0.));
#691 = DEFINITIONAL_REPRESENTATION('',(#692),#696);
#692 = LINE('',#693,#694);
#693 = CARTESIAN_POINT('',(0.,0.));
#694 = VECTOR('',#695,1.);
#695 = DIRECTION('',(0.,-1.));
#696 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#697 = ORIENTED_EDGE('',*,*,#698,.F.);
#698 = EDGE_CURVE('',#595,#671,#699,.T.);
#699 = SURFACE_CURVE('',#700,(#704,#711),.PCURVE_S1.);
#700 = LINE('',#701,#702);
#701 = CARTESIAN_POINT('',(85.917422,126.075483,28.));
#702 = VECTOR('',#703,1.);
#703 = DIRECTION('',(0.,-1.,0.));
#704 = PCURVE('',#610,#705);
#705 = DEFINITIONAL_REPRESENTATION('',(#706),#710);
#706 = LINE('',#707,#708);
#707 = CARTESIAN_POINT('',(-1.42108547152E-14,0.));
#708 = VECTOR('',#709,1.);
#709 = DIRECTION('',(1.,0.));
#710 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#711 = PCURVE('',#175,#712);
#712 = DEFINITIONAL_REPRESENTATION('',(#713),#717);
#713 = LINE('',#714,#715);
#714 = CARTESIAN_POINT('',(-5.910261808433,-9.172315948003));
#715 = VECTOR('',#716,1.);
#716 = DIRECTION('',(0.,-1.));
#717 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#718 = ADVANCED_FACE('',(#719),#686,.F.);
#719 = FACE_BOUND('',#720,.F.);
#720 = EDGE_LOOP('',(#721,#722,#745,#773));
#721 = ORIENTED_EDGE('',*,*,#670,.T.);
#722 = ORIENTED_EDGE('',*,*,#723,.T.);
#723 = EDGE_CURVE('',#648,#724,#726,.T.);
#724 = VERTEX_POINT('',#725);
#725 = CARTESIAN_POINT('',(77.717435,122.856505,-13.767115));
#726 = SURFACE_CURVE('',#727,(#731,#738),.PCURVE_S1.);
#727 = LINE('',#728,#729);
#728 = CARTESIAN_POINT('',(85.917422,122.856505,-13.767115));
#729 = VECTOR('',#730,1.);
#730 = DIRECTION('',(-1.,0.,0.));
#731 = PCURVE('',#686,#732);
#732 = DEFINITIONAL_REPRESENTATION('',(#733),#737);
#733 = LINE('',#734,#735);
#734 = CARTESIAN_POINT('',(0.,-41.767115));
#735 = VECTOR('',#736,1.);
#736 = DIRECTION('',(1.,0.));
#737 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#738 = PCURVE('',#121,#739);
#739 = DEFINITIONAL_REPRESENTATION('',(#740),#744);
#740 = LINE('',#741,#742);
#741 = CARTESIAN_POINT('',(-5.910261808433,-12.391293948));
#742 = VECTOR('',#743,1.);
#743 = DIRECTION('',(1.,0.));
#744 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#745 = ORIENTED_EDGE('',*,*,#746,.F.);
#746 = EDGE_CURVE('',#747,#724,#749,.T.);
#747 = VERTEX_POINT('',#748);
#748 = CARTESIAN_POINT('',(77.717435,122.856505,28.));
#749 = SURFACE_CURVE('',#750,(#754,#761),.PCURVE_S1.);
#750 = LINE('',#751,#752);
#751 = CARTESIAN_POINT('',(77.717435,122.856505,28.));
#752 = VECTOR('',#753,1.);
#753 = DIRECTION('',(0.,0.,-1.));
#754 = PCURVE('',#686,#755);
#755 = DEFINITIONAL_REPRESENTATION('',(#756),#760);
#756 = LINE('',#757,#758);
#757 = CARTESIAN_POINT('',(8.199987,0.));
#758 = VECTOR('',#759,1.);
#759 = DIRECTION('',(0.,-1.));
#760 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#761 = PCURVE('',#762,#767);
#762 = PLANE('',#763);
#763 = AXIS2_PLACEMENT_3D('',#764,#765,#766);
#764 = CARTESIAN_POINT('',(77.717435,122.856505,28.));
#765 = DIRECTION('',(-0.999999999978,6.670306744902E-06,0.));
#766 = DIRECTION('',(-6.670306744902E-06,-0.999999999978,0.));
#767 = DEFINITIONAL_REPRESENTATION('',(#768),#772);
#768 = LINE('',#769,#770);
#769 = CARTESIAN_POINT('',(0.,0.));
#770 = VECTOR('',#771,1.);
#771 = DIRECTION('',(-0.,-1.));
#772 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#773 = ORIENTED_EDGE('',*,*,#774,.F.);
#774 = EDGE_CURVE('',#671,#747,#775,.T.);
#775 = SURFACE_CURVE('',#776,(#780,#787),.PCURVE_S1.);
#776 = LINE('',#777,#778);
#777 = CARTESIAN_POINT('',(85.917422,122.856505,28.));
#778 = VECTOR('',#779,1.);
#779 = DIRECTION('',(-1.,0.,0.));
#780 = PCURVE('',#686,#781);
#781 = DEFINITIONAL_REPRESENTATION('',(#782),#786);
#782 = LINE('',#783,#784);
#783 = CARTESIAN_POINT('',(0.,0.));
#784 = VECTOR('',#785,1.);
#785 = DIRECTION('',(1.,0.));
#786 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#787 = PCURVE('',#175,#788);
#788 = DEFINITIONAL_REPRESENTATION('',(#789),#793);
#789 = LINE('',#790,#791);
#790 = CARTESIAN_POINT('',(-5.910261808433,-12.391293948));
#791 = VECTOR('',#792,1.);
#792 = DIRECTION('',(1.,0.));
#793 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#794 = ADVANCED_FACE('',(#795),#762,.F.);
#795 = FACE_BOUND('',#796,.F.);
#796 = EDGE_LOOP('',(#797,#798,#821,#849));
#797 = ORIENTED_EDGE('',*,*,#746,.T.);
#798 = ORIENTED_EDGE('',*,*,#799,.T.);
#799 = EDGE_CURVE('',#724,#800,#802,.T.);
#800 = VERTEX_POINT('',#801);
#801 = CARTESIAN_POINT('',(77.717428,121.807078,-13.767115));
#802 = SURFACE_CURVE('',#803,(#807,#814),.PCURVE_S1.);
#803 = LINE('',#804,#805);
#804 = CARTESIAN_POINT('',(77.717435,122.856505,-13.767115));
#805 = VECTOR('',#806,1.);
#806 = DIRECTION('',(-6.670306744902E-06,-0.999999999978,0.));
#807 = PCURVE('',#762,#808);
#808 = DEFINITIONAL_REPRESENTATION('',(#809),#813);
#809 = LINE('',#810,#811);
#810 = CARTESIAN_POINT('',(2.842170942977E-14,-41.767115));
#811 = VECTOR('',#812,1.);
#812 = DIRECTION('',(1.,0.));
#813 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#814 = PCURVE('',#121,#815);
#815 = DEFINITIONAL_REPRESENTATION('',(#816),#820);
#816 = LINE('',#817,#818);
#817 = CARTESIAN_POINT('',(2.289725191567,-12.391293948));
#818 = VECTOR('',#819,1.);
#819 = DIRECTION('',(6.670306744902E-06,-0.999999999978));
#820 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#821 = ORIENTED_EDGE('',*,*,#822,.F.);
#822 = EDGE_CURVE('',#823,#800,#825,.T.);
#823 = VERTEX_POINT('',#824);
#824 = CARTESIAN_POINT('',(77.717428,121.807078,28.));
#825 = SURFACE_CURVE('',#826,(#830,#837),.PCURVE_S1.);
#826 = LINE('',#827,#828);
#827 = CARTESIAN_POINT('',(77.717428,121.807078,28.));
#828 = VECTOR('',#829,1.);
#829 = DIRECTION('',(0.,0.,-1.));
#830 = PCURVE('',#762,#831);
#831 = DEFINITIONAL_REPRESENTATION('',(#832),#836);
#832 = LINE('',#833,#834);
#833 = CARTESIAN_POINT('',(1.049427000023,0.));
#834 = VECTOR('',#835,1.);
#835 = DIRECTION('',(-0.,-1.));
#836 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#837 = PCURVE('',#838,#843);
#838 = PLANE('',#839);
#839 = AXIS2_PLACEMENT_3D('',#840,#841,#842);
#840 = CARTESIAN_POINT('',(77.717428,121.807078,28.));
#841 = DIRECTION('',(-5.196848995186E-05,0.99999999865,0.));
#842 = DIRECTION('',(-0.99999999865,-5.196848995186E-05,0.));
#843 = DEFINITIONAL_REPRESENTATION('',(#844),#848);
#844 = LINE('',#845,#846);
#845 = CARTESIAN_POINT('',(0.,0.));
#846 = VECTOR('',#847,1.);
#847 = DIRECTION('',(-0.,-1.));
#848 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#849 = ORIENTED_EDGE('',*,*,#850,.F.);
#850 = EDGE_CURVE('',#747,#823,#851,.T.);
#851 = SURFACE_CURVE('',#852,(#856,#863),.PCURVE_S1.);
#852 = LINE('',#853,#854);
#853 = CARTESIAN_POINT('',(77.717435,122.856505,28.));
#854 = VECTOR('',#855,1.);
#855 = DIRECTION('',(-6.670306744902E-06,-0.999999999978,0.));
#856 = PCURVE('',#762,#857);
#857 = DEFINITIONAL_REPRESENTATION('',(#858),#862);
#858 = LINE('',#859,#860);
#859 = CARTESIAN_POINT('',(2.842170942977E-14,0.));
#860 = VECTOR('',#861,1.);
#861 = DIRECTION('',(1.,0.));
#862 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#863 = PCURVE('',#175,#864);
#864 = DEFINITIONAL_REPRESENTATION('',(#865),#869);
#865 = LINE('',#866,#867);
#866 = CARTESIAN_POINT('',(2.289725191567,-12.391293948));
#867 = VECTOR('',#868,1.);
#868 = DIRECTION('',(6.670306744902E-06,-0.999999999978));
#869 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#870 = ADVANCED_FACE('',(#871),#838,.F.);
#871 = FACE_BOUND('',#872,.F.);
#872 = EDGE_LOOP('',(#873,#874,#897,#925));
#873 = ORIENTED_EDGE('',*,*,#822,.T.);
#874 = ORIENTED_EDGE('',*,*,#875,.T.);
#875 = EDGE_CURVE('',#800,#876,#878,.T.);
#876 = VERTEX_POINT('',#877);
#877 = CARTESIAN_POINT('',(51.008936,121.80569,-13.767115));
#878 = SURFACE_CURVE('',#879,(#883,#890),.PCURVE_S1.);
#879 = LINE('',#880,#881);
#880 = CARTESIAN_POINT('',(77.717428,121.807078,-13.767115));
#881 = VECTOR('',#882,1.);
#882 = DIRECTION('',(-0.99999999865,-5.196848995186E-05,0.));
#883 = PCURVE('',#838,#884);
#884 = DEFINITIONAL_REPRESENTATION('',(#885),#889);
#885 = LINE('',#886,#887);
#886 = CARTESIAN_POINT('',(-0.,-41.767115));
#887 = VECTOR('',#888,1.);
#888 = DIRECTION('',(1.,0.));
#889 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#890 = PCURVE('',#121,#891);
#891 = DEFINITIONAL_REPRESENTATION('',(#892),#896);
#892 = LINE('',#893,#894);
#893 = CARTESIAN_POINT('',(2.289732191567,-13.440720948));
#894 = VECTOR('',#895,1.);
#895 = DIRECTION('',(0.99999999865,-5.196848995186E-05));
#896 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#897 = ORIENTED_EDGE('',*,*,#898,.F.);
#898 = EDGE_CURVE('',#899,#876,#901,.T.);
#899 = VERTEX_POINT('',#900);
#900 = CARTESIAN_POINT('',(51.008936,121.80569,28.));
#901 = SURFACE_CURVE('',#902,(#906,#913),.PCURVE_S1.);
#902 = LINE('',#903,#904);
#903 = CARTESIAN_POINT('',(51.008936,121.80569,28.));
#904 = VECTOR('',#905,1.);
#905 = DIRECTION('',(0.,0.,-1.));
#906 = PCURVE('',#838,#907);
#907 = DEFINITIONAL_REPRESENTATION('',(#908),#912);
#908 = LINE('',#909,#910);
#909 = CARTESIAN_POINT('',(26.708492036066,0.));
#910 = VECTOR('',#911,1.);
#911 = DIRECTION('',(-0.,-1.));
#912 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#913 = PCURVE('',#914,#919);
#914 = PLANE('',#915);
#915 = AXIS2_PLACEMENT_3D('',#916,#917,#918);
#916 = CARTESIAN_POINT('',(51.008936,121.80569,28.));
#917 = DIRECTION('',(0.998464005442,5.540424023454E-02,0.));
#918 = DIRECTION('',(-5.540424023454E-02,0.998464005442,0.));
#919 = DEFINITIONAL_REPRESENTATION('',(#920),#924);
#920 = LINE('',#921,#922);
#921 = CARTESIAN_POINT('',(0.,0.));
#922 = VECTOR('',#923,1.);
#923 = DIRECTION('',(0.,-1.));
#924 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#925 = ORIENTED_EDGE('',*,*,#926,.F.);
#926 = EDGE_CURVE('',#823,#899,#927,.T.);
#927 = SURFACE_CURVE('',#928,(#932,#939),.PCURVE_S1.);
#928 = LINE('',#929,#930);
#929 = CARTESIAN_POINT('',(77.717428,121.807078,28.));
#930 = VECTOR('',#931,1.);
#931 = DIRECTION('',(-0.99999999865,-5.196848995186E-05,0.));
#932 = PCURVE('',#838,#933);
#933 = DEFINITIONAL_REPRESENTATION('',(#934),#938);
#934 = LINE('',#935,#936);
#935 = CARTESIAN_POINT('',(0.,0.));
#936 = VECTOR('',#937,1.);
#937 = DIRECTION('',(1.,0.));
#938 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#939 = PCURVE('',#175,#940);
#940 = DEFINITIONAL_REPRESENTATION('',(#941),#945);
#941 = LINE('',#942,#943);
#942 = CARTESIAN_POINT('',(2.289732191567,-13.440720948));
#943 = VECTOR('',#944,1.);
#944 = DIRECTION('',(0.99999999865,-5.196848995186E-05));
#945 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#946 = ADVANCED_FACE('',(#947),#914,.F.);
#947 = FACE_BOUND('',#948,.F.);
#948 = EDGE_LOOP('',(#949,#950,#973,#1001));
#949 = ORIENTED_EDGE('',*,*,#898,.T.);
#950 = ORIENTED_EDGE('',*,*,#951,.T.);
#951 = EDGE_CURVE('',#876,#952,#954,.T.);
#952 = VERTEX_POINT('',#953);
#953 = CARTESIAN_POINT('',(50.449147,131.893892,-13.767115));
#954 = SURFACE_CURVE('',#955,(#959,#966),.PCURVE_S1.);
#955 = LINE('',#956,#957);
#956 = CARTESIAN_POINT('',(51.008936,121.80569,-13.767115));
#957 = VECTOR('',#958,1.);
#958 = DIRECTION('',(-5.540424023454E-02,0.998464005442,0.));
#959 = PCURVE('',#914,#960);
#960 = DEFINITIONAL_REPRESENTATION('',(#961),#965);
#961 = LINE('',#962,#963);
#962 = CARTESIAN_POINT('',(2.83780538394E-14,-41.767115));
#963 = VECTOR('',#964,1.);
#964 = DIRECTION('',(1.,0.));
#965 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#966 = PCURVE('',#121,#967);
#967 = DEFINITIONAL_REPRESENTATION('',(#968),#972);
#968 = LINE('',#969,#970);
#969 = CARTESIAN_POINT('',(28.998224191567,-13.442108948));
#970 = VECTOR('',#971,1.);
#971 = DIRECTION('',(5.540424023454E-02,0.998464005442));
#972 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#973 = ORIENTED_EDGE('',*,*,#974,.F.);
#974 = EDGE_CURVE('',#975,#952,#977,.T.);
#975 = VERTEX_POINT('',#976);
#976 = CARTESIAN_POINT('',(50.449147,131.893892,28.));
#977 = SURFACE_CURVE('',#978,(#982,#989),.PCURVE_S1.);
#978 = LINE('',#979,#980);
#979 = CARTESIAN_POINT('',(50.449147,131.893892,28.));
#980 = VECTOR('',#981,1.);
#981 = DIRECTION('',(0.,0.,-1.));
#982 = PCURVE('',#914,#983);
#983 = DEFINITIONAL_REPRESENTATION('',(#984),#988);
#984 = LINE('',#985,#986);
#985 = CARTESIAN_POINT('',(10.103721260868,0.));
#986 = VECTOR('',#987,1.);
#987 = DIRECTION('',(0.,-1.));
#988 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#989 = PCURVE('',#990,#995);
#990 = PLANE('',#991);
#991 = AXIS2_PLACEMENT_3D('',#992,#993,#994);
#992 = CARTESIAN_POINT('',(50.449147,131.893892,28.));
#993 = DIRECTION('',(-4.361953975718E-02,0.999048214928,0.));
#994 = DIRECTION('',(-0.999048214928,-4.361953975718E-02,0.));
#995 = DEFINITIONAL_REPRESENTATION('',(#996),#1000);
#996 = LINE('',#997,#998);
#997 = CARTESIAN_POINT('',(0.,0.));
#998 = VECTOR('',#999,1.);
#999 = DIRECTION('',(-0.,-1.));
#1000 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1001 = ORIENTED_EDGE('',*,*,#1002,.F.);
#1002 = EDGE_CURVE('',#899,#975,#1003,.T.);
#1003 = SURFACE_CURVE('',#1004,(#1008,#1015),.PCURVE_S1.);
#1004 = LINE('',#1005,#1006);
#1005 = CARTESIAN_POINT('',(51.008936,121.80569,28.));
#1006 = VECTOR('',#1007,1.);
#1007 = DIRECTION('',(-5.540424023454E-02,0.998464005442,0.));
#1008 = PCURVE('',#914,#1009);
#1009 = DEFINITIONAL_REPRESENTATION('',(#1010),#1014);
#1010 = LINE('',#1011,#1012);
#1011 = CARTESIAN_POINT('',(2.83780538394E-14,0.));
#1012 = VECTOR('',#1013,1.);
#1013 = DIRECTION('',(1.,0.));
#1014 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1015 = PCURVE('',#175,#1016);
#1016 = DEFINITIONAL_REPRESENTATION('',(#1017),#1021);
#1017 = LINE('',#1018,#1019);
#1018 = CARTESIAN_POINT('',(28.998224191567,-13.442108948));
#1019 = VECTOR('',#1020,1.);
#1020 = DIRECTION('',(5.540424023454E-02,0.998464005442));
#1021 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1022 = ADVANCED_FACE('',(#1023),#990,.F.);
#1023 = FACE_BOUND('',#1024,.F.);
#1024 = EDGE_LOOP('',(#1025,#1026,#1049,#1072));
#1025 = ORIENTED_EDGE('',*,*,#974,.T.);
#1026 = ORIENTED_EDGE('',*,*,#1027,.T.);
#1027 = EDGE_CURVE('',#952,#1028,#1030,.T.);
#1028 = VERTEX_POINT('',#1029);
#1029 = CARTESIAN_POINT('',(48.301331,131.800116,-13.767115));
#1030 = SURFACE_CURVE('',#1031,(#1035,#1042),.PCURVE_S1.);
#1031 = LINE('',#1032,#1033);
#1032 = CARTESIAN_POINT('',(50.449147,131.893892,-13.767115));
#1033 = VECTOR('',#1034,1.);
#1034 = DIRECTION('',(-0.999048214928,-4.361953975718E-02,0.));
#1035 = PCURVE('',#990,#1036);
#1036 = DEFINITIONAL_REPRESENTATION('',(#1037),#1041);
#1037 = LINE('',#1038,#1039);
#1038 = CARTESIAN_POINT('',(7.098664517914E-15,-41.767115));
#1039 = VECTOR('',#1040,1.);
#1040 = DIRECTION('',(1.,0.));
#1041 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1042 = PCURVE('',#121,#1043);
#1043 = DEFINITIONAL_REPRESENTATION('',(#1044),#1048);
#1044 = LINE('',#1045,#1046);
#1045 = CARTESIAN_POINT('',(29.558013191567,-3.353906948003));
#1046 = VECTOR('',#1047,1.);
#1047 = DIRECTION('',(0.999048214928,-4.361953975718E-02));
#1048 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1049 = ORIENTED_EDGE('',*,*,#1050,.F.);
#1050 = EDGE_CURVE('',#1051,#1028,#1053,.T.);
#1051 = VERTEX_POINT('',#1052);
#1052 = CARTESIAN_POINT('',(48.301331,131.800116,28.));
#1053 = SURFACE_CURVE('',#1054,(#1058,#1065),.PCURVE_S1.);
#1054 = LINE('',#1055,#1056);
#1055 = CARTESIAN_POINT('',(48.301331,131.800116,28.));
#1056 = VECTOR('',#1057,1.);
#1057 = DIRECTION('',(0.,0.,-1.));
#1058 = PCURVE('',#990,#1059);
#1059 = DEFINITIONAL_REPRESENTATION('',(#1060),#1064);
#1060 = LINE('',#1061,#1062);
#1061 = CARTESIAN_POINT('',(2.149862206755,0.));
#1062 = VECTOR('',#1063,1.);
#1063 = DIRECTION('',(-0.,-1.));
#1064 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1065 = PCURVE('',#93,#1066);
#1066 = DEFINITIONAL_REPRESENTATION('',(#1067),#1071);
#1067 = LINE('',#1068,#1069);
#1068 = CARTESIAN_POINT('',(0.,0.));
#1069 = VECTOR('',#1070,1.);
#1070 = DIRECTION('',(0.,-1.));
#1071 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1072 = ORIENTED_EDGE('',*,*,#1073,.F.);
#1073 = EDGE_CURVE('',#975,#1051,#1074,.T.);
#1074 = SURFACE_CURVE('',#1075,(#1079,#1086),.PCURVE_S1.);
#1075 = LINE('',#1076,#1077);
#1076 = CARTESIAN_POINT('',(50.449147,131.893892,28.));
#1077 = VECTOR('',#1078,1.);
#1078 = DIRECTION('',(-0.999048214928,-4.361953975718E-02,0.));
#1079 = PCURVE('',#990,#1080);
#1080 = DEFINITIONAL_REPRESENTATION('',(#1081),#1085);
#1081 = LINE('',#1082,#1083);
#1082 = CARTESIAN_POINT('',(7.098664517914E-15,0.));
#1083 = VECTOR('',#1084,1.);
#1084 = DIRECTION('',(1.,0.));
#1085 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1086 = PCURVE('',#175,#1087);
#1087 = DEFINITIONAL_REPRESENTATION('',(#1088),#1092);
#1088 = LINE('',#1089,#1090);
#1089 = CARTESIAN_POINT('',(29.558013191567,-3.353906948003));
#1090 = VECTOR('',#1091,1.);
#1091 = DIRECTION('',(0.999048214928,-4.361953975718E-02));
#1092 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1093 = ADVANCED_FACE('',(#1094),#93,.F.);
#1094 = FACE_BOUND('',#1095,.F.);
#1095 = EDGE_LOOP('',(#1096,#1097,#1118,#1119));
#1096 = ORIENTED_EDGE('',*,*,#1050,.T.);
#1097 = ORIENTED_EDGE('',*,*,#1098,.T.);
#1098 = EDGE_CURVE('',#1028,#73,#1099,.T.);
#1099 = SURFACE_CURVE('',#1100,(#1104,#1111),.PCURVE_S1.);
#1100 = LINE('',#1101,#1102);
#1101 = CARTESIAN_POINT('',(48.301331,131.800116,-13.767115));
#1102 = VECTOR('',#1103,1.);
#1103 = DIRECTION('',(-0.11428330053,0.993448200572,0.));
#1104 = PCURVE('',#93,#1105);
#1105 = DEFINITIONAL_REPRESENTATION('',(#1106),#1110);
#1106 = LINE('',#1107,#1108);
#1107 = CARTESIAN_POINT('',(0.,-41.767115));
#1108 = VECTOR('',#1109,1.);
#1109 = DIRECTION('',(1.,0.));
#1110 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1111 = PCURVE('',#121,#1112);
#1112 = DEFINITIONAL_REPRESENTATION('',(#1113),#1117);
#1113 = LINE('',#1114,#1115);
#1114 = CARTESIAN_POINT('',(31.705829191567,-3.447682948003));
#1115 = VECTOR('',#1116,1.);
#1116 = DIRECTION('',(0.11428330053,0.993448200572));
#1117 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1118 = ORIENTED_EDGE('',*,*,#70,.F.);
#1119 = ORIENTED_EDGE('',*,*,#1120,.F.);
#1120 = EDGE_CURVE('',#1051,#71,#1121,.T.);
#1121 = SURFACE_CURVE('',#1122,(#1126,#1133),.PCURVE_S1.);
#1122 = LINE('',#1123,#1124);
#1123 = CARTESIAN_POINT('',(48.301331,131.800116,28.));
#1124 = VECTOR('',#1125,1.);
#1125 = DIRECTION('',(-0.11428330053,0.993448200572,0.));
#1126 = PCURVE('',#93,#1127);
#1127 = DEFINITIONAL_REPRESENTATION('',(#1128),#1132);
#1128 = LINE('',#1129,#1130);
#1129 = CARTESIAN_POINT('',(0.,0.));
#1130 = VECTOR('',#1131,1.);
#1131 = DIRECTION('',(1.,0.));
#1132 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1133 = PCURVE('',#175,#1134);
#1134 = DEFINITIONAL_REPRESENTATION('',(#1135),#1139);
#1135 = LINE('',#1136,#1137);
#1136 = CARTESIAN_POINT('',(31.705829191567,-3.447682948003));
#1137 = VECTOR('',#1138,1.);
#1138 = DIRECTION('',(0.11428330053,0.993448200572));
#1139 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1140 = ADVANCED_FACE('',(#1141),#175,.F.);
#1141 = FACE_BOUND('',#1142,.F.);
#1142 = EDGE_LOOP('',(#1143,#1144,#1145,#1146,#1147,#1148,#1149,#1150,
    #1151,#1152,#1153,#1154,#1155,#1156));
#1143 = ORIENTED_EDGE('',*,*,#161,.T.);
#1144 = ORIENTED_EDGE('',*,*,#242,.T.);
#1145 = ORIENTED_EDGE('',*,*,#318,.T.);
#1146 = ORIENTED_EDGE('',*,*,#394,.T.);
#1147 = ORIENTED_EDGE('',*,*,#470,.T.);
#1148 = ORIENTED_EDGE('',*,*,#546,.T.);
#1149 = ORIENTED_EDGE('',*,*,#622,.T.);
#1150 = ORIENTED_EDGE('',*,*,#698,.T.);
#1151 = ORIENTED_EDGE('',*,*,#774,.T.);
#1152 = ORIENTED_EDGE('',*,*,#850,.T.);
#1153 = ORIENTED_EDGE('',*,*,#926,.T.);
#1154 = ORIENTED_EDGE('',*,*,#1002,.T.);
#1155 = ORIENTED_EDGE('',*,*,#1073,.T.);
#1156 = ORIENTED_EDGE('',*,*,#1120,.T.);
#1157 = ADVANCED_FACE('',(#1158),#121,.T.);
#1158 = FACE_BOUND('',#1159,.T.);
#1159 = EDGE_LOOP('',(#1160,#1161,#1162,#1163,#1164,#1165,#1166,#1167,
    #1168,#1169,#1170,#1171,#1172,#1173));
#1160 = ORIENTED_EDGE('',*,*,#105,.T.);
#1161 = ORIENTED_EDGE('',*,*,#191,.T.);
#1162 = ORIENTED_EDGE('',*,*,#267,.T.);
#1163 = ORIENTED_EDGE('',*,*,#343,.T.);
#1164 = ORIENTED_EDGE('',*,*,#419,.T.);
#1165 = ORIENTED_EDGE('',*,*,#495,.T.);
#1166 = ORIENTED_EDGE('',*,*,#571,.T.);
#1167 = ORIENTED_EDGE('',*,*,#647,.T.);
#1168 = ORIENTED_EDGE('',*,*,#723,.T.);
#1169 = ORIENTED_EDGE('',*,*,#799,.T.);
#1170 = ORIENTED_EDGE('',*,*,#875,.T.);
#1171 = ORIENTED_EDGE('',*,*,#951,.T.);
#1172 = ORIENTED_EDGE('',*,*,#1027,.T.);
#1173 = ORIENTED_EDGE('',*,*,#1098,.T.);
#1174 = ( GEOMETRIC_REPRESENTATION_CONTEXT(3) 
GLOBAL_UNCERTAINTY_ASSIGNED_CONTEXT((#1178)) 
GLOBAL_UNIT_ASSIGNED_CONTEXT((#1175,#1176,#1177)) REPRESENTATION_CONTEXT
('Context #1','3D Context with UNIT and UNCERTAINTY') );
#1175 = ( LENGTH_UNIT() NAMED_UNIT(*) SI_UNIT(.MILLI.,.METRE.) );
#1176 = ( NAMED_UNIT(*) PLANE_ANGLE_UNIT() SI_UNIT($,.RADIAN.) );
#1177 = ( NAMED_UNIT(*) SI_UNIT($,.STERADIAN.) SOLID_ANGLE_UNIT() );
#1178 = UNCERTAINTY_MEASURE_WITH_UNIT(LENGTH_MEASURE(1.E-07),#1175,
  'distance_accuracy_value','confusion accuracy');
#1179 = CONTEXT_DEPENDENT_SHAPE_REPRESENTATION(#1180,#1182);
#1180 = ( REPRESENTATION_RELATIONSHIP('','',#63,#40) 
REPRESENTATION_RELATIONSHIP_WITH_TRANSFORMATION(#1181) 
SHAPE_REPRESENTATION_RELATIONSHIP() );
#1181 = ITEM_DEFINED_TRANSFORMATION('','',#11,#41);
#1182 = PRODUCT_DEFINITION_SHAPE('Placement','Placement of an item',
  #1183);
#1183 = NEXT_ASSEMBLY_USAGE_OCCURRENCE('213','=>[0:1:1:3]','',#35,#58,$
  );
#1184 = PRODUCT_RELATED_PRODUCT_CATEGORY('part',$,(#60));
#1185 = SHAPE_DEFINITION_REPRESENTATION(#1186,#1192);
#1186 = PRODUCT_DEFINITION_SHAPE('','',#1187);
#1187 = PRODUCT_DEFINITION('design','',#1188,#1191);
#1188 = PRODUCT_DEFINITION_FORMATION('','',#1189);
#1189 = PRODUCT('Pile_Group_Boundary_2','Pile_Group_Boundary_2','',(
    #1190));
#1190 = PRODUCT_CONTEXT('',#2,'mechanical');
#1191 = PRODUCT_DEFINITION_CONTEXT('part definition',#2,'design');
#1192 = SHAPE_REPRESENTATION('',(#11,#1193),#1197);
#1193 = AXIS2_PLACEMENT_3D('',#1194,#1195,#1196);
#1194 = CARTESIAN_POINT('',(0.,0.,0.));
#1195 = DIRECTION('',(0.,0.,1.));
#1196 = DIRECTION('',(1.,0.,-0.));
#1197 = ( GEOMETRIC_REPRESENTATION_CONTEXT(3) 
GLOBAL_UNCERTAINTY_ASSIGNED_CONTEXT((#1201)) 
GLOBAL_UNIT_ASSIGNED_CONTEXT((#1198,#1199,#1200)) REPRESENTATION_CONTEXT
('Context #1','3D Context with UNIT and UNCERTAINTY') );
#1198 = ( LENGTH_UNIT() NAMED_UNIT(*) SI_UNIT(.MILLI.,.METRE.) );
#1199 = ( NAMED_UNIT(*) PLANE_ANGLE_UNIT() SI_UNIT($,.RADIAN.) );
#1200 = ( NAMED_UNIT(*) SI_UNIT($,.STERADIAN.) SOLID_ANGLE_UNIT() );
#1201 = UNCERTAINTY_MEASURE_WITH_UNIT(LENGTH_MEASURE(1.E-07),#1198,
  'distance_accuracy_value','confusion accuracy');
#1202 = CONTEXT_DEPENDENT_SHAPE_REPRESENTATION(#1203,#1205);
#1203 = ( REPRESENTATION_RELATIONSHIP('','',#1192,#10) 
REPRESENTATION_RELATIONSHIP_WITH_TRANSFORMATION(#1204) 
SHAPE_REPRESENTATION_RELATIONSHIP() );
#1204 = ITEM_DEFINED_TRANSFORMATION('','',#11,#19);
#1205 = PRODUCT_DEFINITION_SHAPE('Placement','Placement of an item',
  #1206);
#1206 = NEXT_ASSEMBLY_USAGE_OCCURRENCE('216','=>[0:1:1:4]','',#5,#1187,$
  );
#1207 = PRODUCT_RELATED_PRODUCT_CATEGORY('part',$,(#1189));
#1208 = SHAPE_DEFINITION_REPRESENTATION(#1209,#1215);
#1209 = PRODUCT_DEFINITION_SHAPE('','',#1210);
#1210 = PRODUCT_DEFINITION('design','',#1211,#1214);
#1211 = PRODUCT_DEFINITION_FORMATION('','',#1212);
#1212 = PRODUCT('SOLID','SOLID','',(#1213));
#1213 = PRODUCT_CONTEXT('',#2,'mechanical');
#1214 = PRODUCT_DEFINITION_CONTEXT('part definition',#2,'design');
#1215 = ADVANCED_BREP_SHAPE_REPRESENTATION('',(#11,#1216),#2482);
#1216 = MANIFOLD_SOLID_BREP('',#1217);
#1217 = CLOSED_SHELL('',(#1218,#1338,#1414,#1490,#1566,#1642,#1718,#1794
    ,#1870,#1946,#2022,#2098,#2174,#2250,#2326,#2397,#2444,#2463));
#1218 = ADVANCED_FACE('',(#1219),#1233,.F.);
#1219 = FACE_BOUND('',#1220,.F.);
#1220 = EDGE_LOOP('',(#1221,#1256,#1284,#1312));
#1221 = ORIENTED_EDGE('',*,*,#1222,.T.);
#1222 = EDGE_CURVE('',#1223,#1225,#1227,.T.);
#1223 = VERTEX_POINT('',#1224);
#1224 = CARTESIAN_POINT('',(85.917422,126.075483,42.5));
#1225 = VERTEX_POINT('',#1226);
#1226 = CARTESIAN_POINT('',(85.917422,126.075483,-4.866559));
#1227 = SURFACE_CURVE('',#1228,(#1232,#1244),.PCURVE_S1.);
#1228 = LINE('',#1229,#1230);
#1229 = CARTESIAN_POINT('',(85.917422,126.075483,42.5));
#1230 = VECTOR('',#1231,1.);
#1231 = DIRECTION('',(0.,0.,-1.));
#1232 = PCURVE('',#1233,#1238);
#1233 = PLANE('',#1234);
#1234 = AXIS2_PLACEMENT_3D('',#1235,#1236,#1237);
#1235 = CARTESIAN_POINT('',(85.917422,126.075483,42.5));
#1236 = DIRECTION('',(0.,-1.,0.));
#1237 = DIRECTION('',(1.,0.,0.));
#1238 = DEFINITIONAL_REPRESENTATION('',(#1239),#1243);
#1239 = LINE('',#1240,#1241);
#1240 = CARTESIAN_POINT('',(0.,0.));
#1241 = VECTOR('',#1242,1.);
#1242 = DIRECTION('',(0.,-1.));
#1243 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1244 = PCURVE('',#1245,#1250);
#1245 = PLANE('',#1246);
#1246 = AXIS2_PLACEMENT_3D('',#1247,#1248,#1249);
#1247 = CARTESIAN_POINT('',(85.917422,122.856505,42.5));
#1248 = DIRECTION('',(1.,-0.,0.));
#1249 = DIRECTION('',(0.,1.,0.));
#1250 = DEFINITIONAL_REPRESENTATION('',(#1251),#1255);
#1251 = LINE('',#1252,#1253);
#1252 = CARTESIAN_POINT('',(3.218978,0.));
#1253 = VECTOR('',#1254,1.);
#1254 = DIRECTION('',(0.,-1.));
#1255 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1256 = ORIENTED_EDGE('',*,*,#1257,.T.);
#1257 = EDGE_CURVE('',#1225,#1258,#1260,.T.);
#1258 = VERTEX_POINT('',#1259);
#1259 = CARTESIAN_POINT('',(112.160196,126.075483,-4.866559));
#1260 = SURFACE_CURVE('',#1261,(#1265,#1272),.PCURVE_S1.);
#1261 = LINE('',#1262,#1263);
#1262 = CARTESIAN_POINT('',(85.917422,126.075483,-4.866559));
#1263 = VECTOR('',#1264,1.);
#1264 = DIRECTION('',(1.,0.,0.));
#1265 = PCURVE('',#1233,#1266);
#1266 = DEFINITIONAL_REPRESENTATION('',(#1267),#1271);
#1267 = LINE('',#1268,#1269);
#1268 = CARTESIAN_POINT('',(0.,-47.366559));
#1269 = VECTOR('',#1270,1.);
#1270 = DIRECTION('',(1.,0.));
#1271 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1272 = PCURVE('',#1273,#1278);
#1273 = PLANE('',#1274);
#1274 = AXIS2_PLACEMENT_3D('',#1275,#1276,#1277);
#1275 = CARTESIAN_POINT('',(86.921861882064,112.03332182876,-4.866559));
#1276 = DIRECTION('',(0.,0.,-1.));
#1277 = DIRECTION('',(-1.,0.,0.));
#1278 = DEFINITIONAL_REPRESENTATION('',(#1279),#1283);
#1279 = LINE('',#1280,#1281);
#1280 = CARTESIAN_POINT('',(1.004439882064,14.042161171239));
#1281 = VECTOR('',#1282,1.);
#1282 = DIRECTION('',(-1.,0.));
#1283 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1284 = ORIENTED_EDGE('',*,*,#1285,.F.);
#1285 = EDGE_CURVE('',#1286,#1258,#1288,.T.);
#1286 = VERTEX_POINT('',#1287);
#1287 = CARTESIAN_POINT('',(112.160196,126.075483,42.5));
#1288 = SURFACE_CURVE('',#1289,(#1293,#1300),.PCURVE_S1.);
#1289 = LINE('',#1290,#1291);
#1290 = CARTESIAN_POINT('',(112.160196,126.075483,42.5));
#1291 = VECTOR('',#1292,1.);
#1292 = DIRECTION('',(0.,0.,-1.));
#1293 = PCURVE('',#1233,#1294);
#1294 = DEFINITIONAL_REPRESENTATION('',(#1295),#1299);
#1295 = LINE('',#1296,#1297);
#1296 = CARTESIAN_POINT('',(26.242774,0.));
#1297 = VECTOR('',#1298,1.);
#1298 = DIRECTION('',(0.,-1.));
#1299 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1300 = PCURVE('',#1301,#1306);
#1301 = PLANE('',#1302);
#1302 = AXIS2_PLACEMENT_3D('',#1303,#1304,#1305);
#1303 = CARTESIAN_POINT('',(112.160196,126.075483,42.5));
#1304 = DIRECTION('',(-0.999997968291,-2.015791161217E-03,-0.));
#1305 = DIRECTION('',(2.015791161217E-03,-0.999997968291,0.));
#1306 = DEFINITIONAL_REPRESENTATION('',(#1307),#1311);
#1307 = LINE('',#1308,#1309);
#1308 = CARTESIAN_POINT('',(0.,0.));
#1309 = VECTOR('',#1310,1.);
#1310 = DIRECTION('',(0.,-1.));
#1311 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1312 = ORIENTED_EDGE('',*,*,#1313,.F.);
#1313 = EDGE_CURVE('',#1223,#1286,#1314,.T.);
#1314 = SURFACE_CURVE('',#1315,(#1319,#1326),.PCURVE_S1.);
#1315 = LINE('',#1316,#1317);
#1316 = CARTESIAN_POINT('',(85.917422,126.075483,42.5));
#1317 = VECTOR('',#1318,1.);
#1318 = DIRECTION('',(1.,0.,0.));
#1319 = PCURVE('',#1233,#1320);
#1320 = DEFINITIONAL_REPRESENTATION('',(#1321),#1325);
#1321 = LINE('',#1322,#1323);
#1322 = CARTESIAN_POINT('',(0.,0.));
#1323 = VECTOR('',#1324,1.);
#1324 = DIRECTION('',(1.,0.));
#1325 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1326 = PCURVE('',#1327,#1332);
#1327 = PLANE('',#1328);
#1328 = AXIS2_PLACEMENT_3D('',#1329,#1330,#1331);
#1329 = CARTESIAN_POINT('',(86.921861882064,112.03332182876,42.5));
#1330 = DIRECTION('',(0.,0.,-1.));
#1331 = DIRECTION('',(-1.,0.,0.));
#1332 = DEFINITIONAL_REPRESENTATION('',(#1333),#1337);
#1333 = LINE('',#1334,#1335);
#1334 = CARTESIAN_POINT('',(1.004439882064,14.042161171239));
#1335 = VECTOR('',#1336,1.);
#1336 = DIRECTION('',(-1.,0.));
#1337 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1338 = ADVANCED_FACE('',(#1339),#1301,.F.);
#1339 = FACE_BOUND('',#1340,.F.);
#1340 = EDGE_LOOP('',(#1341,#1342,#1365,#1393));
#1341 = ORIENTED_EDGE('',*,*,#1285,.T.);
#1342 = ORIENTED_EDGE('',*,*,#1343,.T.);
#1343 = EDGE_CURVE('',#1258,#1344,#1346,.T.);
#1344 = VERTEX_POINT('',#1345);
#1345 = CARTESIAN_POINT('',(112.233326,89.796997,-4.866559));
#1346 = SURFACE_CURVE('',#1347,(#1351,#1358),.PCURVE_S1.);
#1347 = LINE('',#1348,#1349);
#1348 = CARTESIAN_POINT('',(112.160196,126.075483,-4.866559));
#1349 = VECTOR('',#1350,1.);
#1350 = DIRECTION('',(2.015791161217E-03,-0.999997968291,0.));
#1351 = PCURVE('',#1301,#1352);
#1352 = DEFINITIONAL_REPRESENTATION('',(#1353),#1357);
#1353 = LINE('',#1354,#1355);
#1354 = CARTESIAN_POINT('',(0.,-47.366559));
#1355 = VECTOR('',#1356,1.);
#1356 = DIRECTION('',(1.,0.));
#1357 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1358 = PCURVE('',#1273,#1359);
#1359 = DEFINITIONAL_REPRESENTATION('',(#1360),#1364);
#1360 = LINE('',#1361,#1362);
#1361 = CARTESIAN_POINT('',(-25.23833411793,14.042161171239));
#1362 = VECTOR('',#1363,1.);
#1363 = DIRECTION('',(-2.015791161217E-03,-0.999997968291));
#1364 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1365 = ORIENTED_EDGE('',*,*,#1366,.F.);
#1366 = EDGE_CURVE('',#1367,#1344,#1369,.T.);
#1367 = VERTEX_POINT('',#1368);
#1368 = CARTESIAN_POINT('',(112.233326,89.796997,42.5));
#1369 = SURFACE_CURVE('',#1370,(#1374,#1381),.PCURVE_S1.);
#1370 = LINE('',#1371,#1372);
#1371 = CARTESIAN_POINT('',(112.233326,89.796997,42.5));
#1372 = VECTOR('',#1373,1.);
#1373 = DIRECTION('',(0.,0.,-1.));
#1374 = PCURVE('',#1301,#1375);
#1375 = DEFINITIONAL_REPRESENTATION('',(#1376),#1380);
#1376 = LINE('',#1377,#1378);
#1377 = CARTESIAN_POINT('',(36.278559707479,0.));
#1378 = VECTOR('',#1379,1.);
#1379 = DIRECTION('',(0.,-1.));
#1380 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1381 = PCURVE('',#1382,#1387);
#1382 = PLANE('',#1383);
#1383 = AXIS2_PLACEMENT_3D('',#1384,#1385,#1386);
#1384 = CARTESIAN_POINT('',(112.233326,89.796997,42.5));
#1385 = DIRECTION('',(-5.882771802765E-03,0.999982696348,0.));
#1386 = DIRECTION('',(-0.999982696348,-5.882771802765E-03,0.));
#1387 = DEFINITIONAL_REPRESENTATION('',(#1388),#1392);
#1388 = LINE('',#1389,#1390);
#1389 = CARTESIAN_POINT('',(0.,0.));
#1390 = VECTOR('',#1391,1.);
#1391 = DIRECTION('',(-0.,-1.));
#1392 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1393 = ORIENTED_EDGE('',*,*,#1394,.F.);
#1394 = EDGE_CURVE('',#1286,#1367,#1395,.T.);
#1395 = SURFACE_CURVE('',#1396,(#1400,#1407),.PCURVE_S1.);
#1396 = LINE('',#1397,#1398);
#1397 = CARTESIAN_POINT('',(112.160196,126.075483,42.5));
#1398 = VECTOR('',#1399,1.);
#1399 = DIRECTION('',(2.015791161217E-03,-0.999997968291,0.));
#1400 = PCURVE('',#1301,#1401);
#1401 = DEFINITIONAL_REPRESENTATION('',(#1402),#1406);
#1402 = LINE('',#1403,#1404);
#1403 = CARTESIAN_POINT('',(0.,0.));
#1404 = VECTOR('',#1405,1.);
#1405 = DIRECTION('',(1.,0.));
#1406 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1407 = PCURVE('',#1327,#1408);
#1408 = DEFINITIONAL_REPRESENTATION('',(#1409),#1413);
#1409 = LINE('',#1410,#1411);
#1410 = CARTESIAN_POINT('',(-25.23833411793,14.042161171239));
#1411 = VECTOR('',#1412,1.);
#1412 = DIRECTION('',(-2.015791161217E-03,-0.999997968291));
#1413 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1414 = ADVANCED_FACE('',(#1415),#1382,.F.);
#1415 = FACE_BOUND('',#1416,.F.);
#1416 = EDGE_LOOP('',(#1417,#1418,#1441,#1469));
#1417 = ORIENTED_EDGE('',*,*,#1366,.T.);
#1418 = ORIENTED_EDGE('',*,*,#1419,.T.);
#1419 = EDGE_CURVE('',#1344,#1420,#1422,.T.);
#1420 = VERTEX_POINT('',#1421);
#1421 = CARTESIAN_POINT('',(101.285615,89.732593,-4.866559));
#1422 = SURFACE_CURVE('',#1423,(#1427,#1434),.PCURVE_S1.);
#1423 = LINE('',#1424,#1425);
#1424 = CARTESIAN_POINT('',(112.233326,89.796997,-4.866559));
#1425 = VECTOR('',#1426,1.);
#1426 = DIRECTION('',(-0.999982696348,-5.882771802765E-03,0.));
#1427 = PCURVE('',#1382,#1428);
#1428 = DEFINITIONAL_REPRESENTATION('',(#1429),#1433);
#1429 = LINE('',#1430,#1431);
#1430 = CARTESIAN_POINT('',(-1.421060881552E-14,-47.366559));
#1431 = VECTOR('',#1432,1.);
#1432 = DIRECTION('',(1.,0.));
#1433 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1434 = PCURVE('',#1273,#1435);
#1435 = DEFINITIONAL_REPRESENTATION('',(#1436),#1440);
#1436 = LINE('',#1437,#1438);
#1437 = CARTESIAN_POINT('',(-25.31146411793,-22.23632482876));
#1438 = VECTOR('',#1439,1.);
#1439 = DIRECTION('',(0.999982696348,-5.882771802765E-03));
#1440 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1441 = ORIENTED_EDGE('',*,*,#1442,.F.);
#1442 = EDGE_CURVE('',#1443,#1420,#1445,.T.);
#1443 = VERTEX_POINT('',#1444);
#1444 = CARTESIAN_POINT('',(101.285615,89.732593,42.5));
#1445 = SURFACE_CURVE('',#1446,(#1450,#1457),.PCURVE_S1.);
#1446 = LINE('',#1447,#1448);
#1447 = CARTESIAN_POINT('',(101.285615,89.732593,42.5));
#1448 = VECTOR('',#1449,1.);
#1449 = DIRECTION('',(0.,0.,-1.));
#1450 = PCURVE('',#1382,#1451);
#1451 = DEFINITIONAL_REPRESENTATION('',(#1452),#1456);
#1452 = LINE('',#1453,#1454);
#1453 = CARTESIAN_POINT('',(10.947900438657,0.));
#1454 = VECTOR('',#1455,1.);
#1455 = DIRECTION('',(-0.,-1.));
#1456 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1457 = PCURVE('',#1458,#1463);
#1458 = PLANE('',#1459);
#1459 = AXIS2_PLACEMENT_3D('',#1460,#1461,#1462);
#1460 = CARTESIAN_POINT('',(101.285615,89.732593,42.5));
#1461 = DIRECTION('',(0.177817912754,0.984063407461,0.));
#1462 = DIRECTION('',(-0.984063407461,0.177817912754,0.));
#1463 = DEFINITIONAL_REPRESENTATION('',(#1464),#1468);
#1464 = LINE('',#1465,#1466);
#1465 = CARTESIAN_POINT('',(0.,0.));
#1466 = VECTOR('',#1467,1.);
#1467 = DIRECTION('',(0.,-1.));
#1468 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1469 = ORIENTED_EDGE('',*,*,#1470,.F.);
#1470 = EDGE_CURVE('',#1367,#1443,#1471,.T.);
#1471 = SURFACE_CURVE('',#1472,(#1476,#1483),.PCURVE_S1.);
#1472 = LINE('',#1473,#1474);
#1473 = CARTESIAN_POINT('',(112.233326,89.796997,42.5));
#1474 = VECTOR('',#1475,1.);
#1475 = DIRECTION('',(-0.999982696348,-5.882771802765E-03,0.));
#1476 = PCURVE('',#1382,#1477);
#1477 = DEFINITIONAL_REPRESENTATION('',(#1478),#1482);
#1478 = LINE('',#1479,#1480);
#1479 = CARTESIAN_POINT('',(-1.421060881552E-14,0.));
#1480 = VECTOR('',#1481,1.);
#1481 = DIRECTION('',(1.,0.));
#1482 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1483 = PCURVE('',#1327,#1484);
#1484 = DEFINITIONAL_REPRESENTATION('',(#1485),#1489);
#1485 = LINE('',#1486,#1487);
#1486 = CARTESIAN_POINT('',(-25.31146411793,-22.23632482876));
#1487 = VECTOR('',#1488,1.);
#1488 = DIRECTION('',(0.999982696348,-5.882771802765E-03));
#1489 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1490 = ADVANCED_FACE('',(#1491),#1458,.F.);
#1491 = FACE_BOUND('',#1492,.F.);
#1492 = EDGE_LOOP('',(#1493,#1494,#1517,#1545));
#1493 = ORIENTED_EDGE('',*,*,#1442,.T.);
#1494 = ORIENTED_EDGE('',*,*,#1495,.T.);
#1495 = EDGE_CURVE('',#1420,#1496,#1498,.T.);
#1496 = VERTEX_POINT('',#1497);
#1497 = CARTESIAN_POINT('',(90.352654,91.708153,-4.866559));
#1498 = SURFACE_CURVE('',#1499,(#1503,#1510),.PCURVE_S1.);
#1499 = LINE('',#1500,#1501);
#1500 = CARTESIAN_POINT('',(101.285615,89.732593,-4.866559));
#1501 = VECTOR('',#1502,1.);
#1502 = DIRECTION('',(-0.984063407461,0.177817912754,0.));
#1503 = PCURVE('',#1458,#1504);
#1504 = DEFINITIONAL_REPRESENTATION('',(#1505),#1509);
#1505 = LINE('',#1506,#1507);
#1506 = CARTESIAN_POINT('',(1.398438211397E-14,-47.366559));
#1507 = VECTOR('',#1508,1.);
#1508 = DIRECTION('',(1.,0.));
#1509 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1510 = PCURVE('',#1273,#1511);
#1511 = DEFINITIONAL_REPRESENTATION('',(#1512),#1516);
#1512 = LINE('',#1513,#1514);
#1513 = CARTESIAN_POINT('',(-14.36375311793,-22.30072882876));
#1514 = VECTOR('',#1515,1.);
#1515 = DIRECTION('',(0.984063407461,0.177817912754));
#1516 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1517 = ORIENTED_EDGE('',*,*,#1518,.F.);
#1518 = EDGE_CURVE('',#1519,#1496,#1521,.T.);
#1519 = VERTEX_POINT('',#1520);
#1520 = CARTESIAN_POINT('',(90.352654,91.708153,42.5));
#1521 = SURFACE_CURVE('',#1522,(#1526,#1533),.PCURVE_S1.);
#1522 = LINE('',#1523,#1524);
#1523 = CARTESIAN_POINT('',(90.352654,91.708153,42.5));
#1524 = VECTOR('',#1525,1.);
#1525 = DIRECTION('',(0.,0.,-1.));
#1526 = PCURVE('',#1458,#1527);
#1527 = DEFINITIONAL_REPRESENTATION('',(#1528),#1532);
#1528 = LINE('',#1529,#1530);
#1529 = CARTESIAN_POINT('',(11.110016811019,0.));
#1530 = VECTOR('',#1531,1.);
#1531 = DIRECTION('',(0.,-1.));
#1532 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1533 = PCURVE('',#1534,#1539);
#1534 = PLANE('',#1535);
#1535 = AXIS2_PLACEMENT_3D('',#1536,#1537,#1538);
#1536 = CARTESIAN_POINT('',(90.352654,91.708153,42.5));
#1537 = DIRECTION('',(0.918584192038,0.395225356142,0.));
#1538 = DIRECTION('',(-0.395225356142,0.918584192038,0.));
#1539 = DEFINITIONAL_REPRESENTATION('',(#1540),#1544);
#1540 = LINE('',#1541,#1542);
#1541 = CARTESIAN_POINT('',(0.,0.));
#1542 = VECTOR('',#1543,1.);
#1543 = DIRECTION('',(0.,-1.));
#1544 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1545 = ORIENTED_EDGE('',*,*,#1546,.F.);
#1546 = EDGE_CURVE('',#1443,#1519,#1547,.T.);
#1547 = SURFACE_CURVE('',#1548,(#1552,#1559),.PCURVE_S1.);
#1548 = LINE('',#1549,#1550);
#1549 = CARTESIAN_POINT('',(101.285615,89.732593,42.5));
#1550 = VECTOR('',#1551,1.);
#1551 = DIRECTION('',(-0.984063407461,0.177817912754,0.));
#1552 = PCURVE('',#1458,#1553);
#1553 = DEFINITIONAL_REPRESENTATION('',(#1554),#1558);
#1554 = LINE('',#1555,#1556);
#1555 = CARTESIAN_POINT('',(1.398438211397E-14,0.));
#1556 = VECTOR('',#1557,1.);
#1557 = DIRECTION('',(1.,0.));
#1558 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1559 = PCURVE('',#1327,#1560);
#1560 = DEFINITIONAL_REPRESENTATION('',(#1561),#1565);
#1561 = LINE('',#1562,#1563);
#1562 = CARTESIAN_POINT('',(-14.36375311793,-22.30072882876));
#1563 = VECTOR('',#1564,1.);
#1564 = DIRECTION('',(0.984063407461,0.177817912754));
#1565 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1566 = ADVANCED_FACE('',(#1567),#1534,.F.);
#1567 = FACE_BOUND('',#1568,.F.);
#1568 = EDGE_LOOP('',(#1569,#1570,#1593,#1621));
#1569 = ORIENTED_EDGE('',*,*,#1518,.T.);
#1570 = ORIENTED_EDGE('',*,*,#1571,.T.);
#1571 = EDGE_CURVE('',#1496,#1572,#1574,.T.);
#1572 = VERTEX_POINT('',#1573);
#1573 = CARTESIAN_POINT('',(89.283511,94.193059,-4.866559));
#1574 = SURFACE_CURVE('',#1575,(#1579,#1586),.PCURVE_S1.);
#1575 = LINE('',#1576,#1577);
#1576 = CARTESIAN_POINT('',(90.352654,91.708153,-4.866559));
#1577 = VECTOR('',#1578,1.);
#1578 = DIRECTION('',(-0.395225356142,0.918584192038,0.));
#1579 = PCURVE('',#1534,#1580);
#1580 = DEFINITIONAL_REPRESENTATION('',(#1581),#1585);
#1581 = LINE('',#1582,#1583);
#1582 = CARTESIAN_POINT('',(0.,-47.366559));
#1583 = VECTOR('',#1584,1.);
#1584 = DIRECTION('',(1.,0.));
#1585 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1586 = PCURVE('',#1273,#1587);
#1587 = DEFINITIONAL_REPRESENTATION('',(#1588),#1592);
#1588 = LINE('',#1589,#1590);
#1589 = CARTESIAN_POINT('',(-3.430792117936,-20.32516882876));
#1590 = VECTOR('',#1591,1.);
#1591 = DIRECTION('',(0.395225356142,0.918584192038));
#1592 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1593 = ORIENTED_EDGE('',*,*,#1594,.F.);
#1594 = EDGE_CURVE('',#1595,#1572,#1597,.T.);
#1595 = VERTEX_POINT('',#1596);
#1596 = CARTESIAN_POINT('',(89.283511,94.193059,42.5));
#1597 = SURFACE_CURVE('',#1598,(#1602,#1609),.PCURVE_S1.);
#1598 = LINE('',#1599,#1600);
#1599 = CARTESIAN_POINT('',(89.283511,94.193059,42.5));
#1600 = VECTOR('',#1601,1.);
#1601 = DIRECTION('',(0.,0.,-1.));
#1602 = PCURVE('',#1534,#1603);
#1603 = DEFINITIONAL_REPRESENTATION('',(#1604),#1608);
#1604 = LINE('',#1605,#1606);
#1605 = CARTESIAN_POINT('',(2.705147793243,0.));
#1606 = VECTOR('',#1607,1.);
#1607 = DIRECTION('',(0.,-1.));
#1608 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1609 = PCURVE('',#1610,#1615);
#1610 = PLANE('',#1611);
#1611 = AXIS2_PLACEMENT_3D('',#1612,#1613,#1614);
#1612 = CARTESIAN_POINT('',(89.283511,94.193059,42.5));
#1613 = DIRECTION('',(0.999970353247,-7.700170607099E-03,0.));
#1614 = DIRECTION('',(7.700170607099E-03,0.999970353247,0.));
#1615 = DEFINITIONAL_REPRESENTATION('',(#1616),#1620);
#1616 = LINE('',#1617,#1618);
#1617 = CARTESIAN_POINT('',(0.,0.));
#1618 = VECTOR('',#1619,1.);
#1619 = DIRECTION('',(0.,-1.));
#1620 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1621 = ORIENTED_EDGE('',*,*,#1622,.F.);
#1622 = EDGE_CURVE('',#1519,#1595,#1623,.T.);
#1623 = SURFACE_CURVE('',#1624,(#1628,#1635),.PCURVE_S1.);
#1624 = LINE('',#1625,#1626);
#1625 = CARTESIAN_POINT('',(90.352654,91.708153,42.5));
#1626 = VECTOR('',#1627,1.);
#1627 = DIRECTION('',(-0.395225356142,0.918584192038,0.));
#1628 = PCURVE('',#1534,#1629);
#1629 = DEFINITIONAL_REPRESENTATION('',(#1630),#1634);
#1630 = LINE('',#1631,#1632);
#1631 = CARTESIAN_POINT('',(0.,0.));
#1632 = VECTOR('',#1633,1.);
#1633 = DIRECTION('',(1.,0.));
#1634 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1635 = PCURVE('',#1327,#1636);
#1636 = DEFINITIONAL_REPRESENTATION('',(#1637),#1641);
#1637 = LINE('',#1638,#1639);
#1638 = CARTESIAN_POINT('',(-3.430792117936,-20.32516882876));
#1639 = VECTOR('',#1640,1.);
#1640 = DIRECTION('',(0.395225356142,0.918584192038));
#1641 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1642 = ADVANCED_FACE('',(#1643),#1610,.F.);
#1643 = FACE_BOUND('',#1644,.F.);
#1644 = EDGE_LOOP('',(#1645,#1646,#1669,#1697));
#1645 = ORIENTED_EDGE('',*,*,#1594,.T.);
#1646 = ORIENTED_EDGE('',*,*,#1647,.T.);
#1647 = EDGE_CURVE('',#1572,#1648,#1650,.T.);
#1648 = VERTEX_POINT('',#1649);
#1649 = CARTESIAN_POINT('',(89.317494,98.606207,-4.866559));
#1650 = SURFACE_CURVE('',#1651,(#1655,#1662),.PCURVE_S1.);
#1651 = LINE('',#1652,#1653);
#1652 = CARTESIAN_POINT('',(89.283511,94.193059,-4.866559));
#1653 = VECTOR('',#1654,1.);
#1654 = DIRECTION('',(7.700170607099E-03,0.999970353247,0.));
#1655 = PCURVE('',#1610,#1656);
#1656 = DEFINITIONAL_REPRESENTATION('',(#1657),#1661);
#1657 = LINE('',#1658,#1659);
#1658 = CARTESIAN_POINT('',(1.42104334095E-14,-47.366559));
#1659 = VECTOR('',#1660,1.);
#1660 = DIRECTION('',(1.,0.));
#1661 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1662 = PCURVE('',#1273,#1663);
#1663 = DEFINITIONAL_REPRESENTATION('',(#1664),#1668);
#1664 = LINE('',#1665,#1666);
#1665 = CARTESIAN_POINT('',(-2.361649117936,-17.84026282876));
#1666 = VECTOR('',#1667,1.);
#1667 = DIRECTION('',(-7.700170607099E-03,0.999970353247));
#1668 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1669 = ORIENTED_EDGE('',*,*,#1670,.F.);
#1670 = EDGE_CURVE('',#1671,#1648,#1673,.T.);
#1671 = VERTEX_POINT('',#1672);
#1672 = CARTESIAN_POINT('',(89.317494,98.606207,42.5));
#1673 = SURFACE_CURVE('',#1674,(#1678,#1685),.PCURVE_S1.);
#1674 = LINE('',#1675,#1676);
#1675 = CARTESIAN_POINT('',(89.317494,98.606207,42.5));
#1676 = VECTOR('',#1677,1.);
#1677 = DIRECTION('',(0.,0.,-1.));
#1678 = PCURVE('',#1610,#1679);
#1679 = DEFINITIONAL_REPRESENTATION('',(#1680),#1684);
#1680 = LINE('',#1681,#1682);
#1681 = CARTESIAN_POINT('',(4.413278839388,0.));
#1682 = VECTOR('',#1683,1.);
#1683 = DIRECTION('',(0.,-1.));
#1684 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1685 = PCURVE('',#1686,#1691);
#1686 = PLANE('',#1687);
#1687 = AXIS2_PLACEMENT_3D('',#1688,#1689,#1690);
#1688 = CARTESIAN_POINT('',(89.317494,98.606207,42.5));
#1689 = DIRECTION('',(1.,-0.,0.));
#1690 = DIRECTION('',(0.,1.,0.));
#1691 = DEFINITIONAL_REPRESENTATION('',(#1692),#1696);
#1692 = LINE('',#1693,#1694);
#1693 = CARTESIAN_POINT('',(0.,0.));
#1694 = VECTOR('',#1695,1.);
#1695 = DIRECTION('',(0.,-1.));
#1696 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1697 = ORIENTED_EDGE('',*,*,#1698,.F.);
#1698 = EDGE_CURVE('',#1595,#1671,#1699,.T.);
#1699 = SURFACE_CURVE('',#1700,(#1704,#1711),.PCURVE_S1.);
#1700 = LINE('',#1701,#1702);
#1701 = CARTESIAN_POINT('',(89.283511,94.193059,42.5));
#1702 = VECTOR('',#1703,1.);
#1703 = DIRECTION('',(7.700170607099E-03,0.999970353247,0.));
#1704 = PCURVE('',#1610,#1705);
#1705 = DEFINITIONAL_REPRESENTATION('',(#1706),#1710);
#1706 = LINE('',#1707,#1708);
#1707 = CARTESIAN_POINT('',(1.42104334095E-14,0.));
#1708 = VECTOR('',#1709,1.);
#1709 = DIRECTION('',(1.,0.));
#1710 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1711 = PCURVE('',#1327,#1712);
#1712 = DEFINITIONAL_REPRESENTATION('',(#1713),#1717);
#1713 = LINE('',#1714,#1715);
#1714 = CARTESIAN_POINT('',(-2.361649117936,-17.84026282876));
#1715 = VECTOR('',#1716,1.);
#1716 = DIRECTION('',(-7.700170607099E-03,0.999970353247));
#1717 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1718 = ADVANCED_FACE('',(#1719),#1686,.F.);
#1719 = FACE_BOUND('',#1720,.F.);
#1720 = EDGE_LOOP('',(#1721,#1722,#1745,#1773));
#1721 = ORIENTED_EDGE('',*,*,#1670,.T.);
#1722 = ORIENTED_EDGE('',*,*,#1723,.T.);
#1723 = EDGE_CURVE('',#1648,#1724,#1726,.T.);
#1724 = VERTEX_POINT('',#1725);
#1725 = CARTESIAN_POINT('',(89.317494,112.30648,-4.866559));
#1726 = SURFACE_CURVE('',#1727,(#1731,#1738),.PCURVE_S1.);
#1727 = LINE('',#1728,#1729);
#1728 = CARTESIAN_POINT('',(89.317494,98.606207,-4.866559));
#1729 = VECTOR('',#1730,1.);
#1730 = DIRECTION('',(0.,1.,0.));
#1731 = PCURVE('',#1686,#1732);
#1732 = DEFINITIONAL_REPRESENTATION('',(#1733),#1737);
#1733 = LINE('',#1734,#1735);
#1734 = CARTESIAN_POINT('',(0.,-47.366559));
#1735 = VECTOR('',#1736,1.);
#1736 = DIRECTION('',(1.,0.));
#1737 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1738 = PCURVE('',#1273,#1739);
#1739 = DEFINITIONAL_REPRESENTATION('',(#1740),#1744);
#1740 = LINE('',#1741,#1742);
#1741 = CARTESIAN_POINT('',(-2.395632117936,-13.42711482876));
#1742 = VECTOR('',#1743,1.);
#1743 = DIRECTION('',(0.,1.));
#1744 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1745 = ORIENTED_EDGE('',*,*,#1746,.F.);
#1746 = EDGE_CURVE('',#1747,#1724,#1749,.T.);
#1747 = VERTEX_POINT('',#1748);
#1748 = CARTESIAN_POINT('',(89.317494,112.30648,42.5));
#1749 = SURFACE_CURVE('',#1750,(#1754,#1761),.PCURVE_S1.);
#1750 = LINE('',#1751,#1752);
#1751 = CARTESIAN_POINT('',(89.317494,112.30648,42.5));
#1752 = VECTOR('',#1753,1.);
#1753 = DIRECTION('',(0.,0.,-1.));
#1754 = PCURVE('',#1686,#1755);
#1755 = DEFINITIONAL_REPRESENTATION('',(#1756),#1760);
#1756 = LINE('',#1757,#1758);
#1757 = CARTESIAN_POINT('',(13.700273,0.));
#1758 = VECTOR('',#1759,1.);
#1759 = DIRECTION('',(0.,-1.));
#1760 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1761 = PCURVE('',#1762,#1767);
#1762 = PLANE('',#1763);
#1763 = AXIS2_PLACEMENT_3D('',#1764,#1765,#1766);
#1764 = CARTESIAN_POINT('',(89.317494,112.30648,42.5));
#1765 = DIRECTION('',(0.,1.,0.));
#1766 = DIRECTION('',(-1.,0.,0.));
#1767 = DEFINITIONAL_REPRESENTATION('',(#1768),#1772);
#1768 = LINE('',#1769,#1770);
#1769 = CARTESIAN_POINT('',(0.,0.));
#1770 = VECTOR('',#1771,1.);
#1771 = DIRECTION('',(0.,-1.));
#1772 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1773 = ORIENTED_EDGE('',*,*,#1774,.F.);
#1774 = EDGE_CURVE('',#1671,#1747,#1775,.T.);
#1775 = SURFACE_CURVE('',#1776,(#1780,#1787),.PCURVE_S1.);
#1776 = LINE('',#1777,#1778);
#1777 = CARTESIAN_POINT('',(89.317494,98.606207,42.5));
#1778 = VECTOR('',#1779,1.);
#1779 = DIRECTION('',(0.,1.,0.));
#1780 = PCURVE('',#1686,#1781);
#1781 = DEFINITIONAL_REPRESENTATION('',(#1782),#1786);
#1782 = LINE('',#1783,#1784);
#1783 = CARTESIAN_POINT('',(0.,0.));
#1784 = VECTOR('',#1785,1.);
#1785 = DIRECTION('',(1.,0.));
#1786 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1787 = PCURVE('',#1327,#1788);
#1788 = DEFINITIONAL_REPRESENTATION('',(#1789),#1793);
#1789 = LINE('',#1790,#1791);
#1790 = CARTESIAN_POINT('',(-2.395632117936,-13.42711482876));
#1791 = VECTOR('',#1792,1.);
#1792 = DIRECTION('',(0.,1.));
#1793 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1794 = ADVANCED_FACE('',(#1795),#1762,.F.);
#1795 = FACE_BOUND('',#1796,.F.);
#1796 = EDGE_LOOP('',(#1797,#1798,#1821,#1849));
#1797 = ORIENTED_EDGE('',*,*,#1746,.T.);
#1798 = ORIENTED_EDGE('',*,*,#1799,.T.);
#1799 = EDGE_CURVE('',#1724,#1800,#1802,.T.);
#1800 = VERTEX_POINT('',#1801);
#1801 = CARTESIAN_POINT('',(72.917494,112.30648,-4.866559));
#1802 = SURFACE_CURVE('',#1803,(#1807,#1814),.PCURVE_S1.);
#1803 = LINE('',#1804,#1805);
#1804 = CARTESIAN_POINT('',(89.317494,112.30648,-4.866559));
#1805 = VECTOR('',#1806,1.);
#1806 = DIRECTION('',(-1.,0.,0.));
#1807 = PCURVE('',#1762,#1808);
#1808 = DEFINITIONAL_REPRESENTATION('',(#1809),#1813);
#1809 = LINE('',#1810,#1811);
#1810 = CARTESIAN_POINT('',(0.,-47.366559));
#1811 = VECTOR('',#1812,1.);
#1812 = DIRECTION('',(1.,0.));
#1813 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1814 = PCURVE('',#1273,#1815);
#1815 = DEFINITIONAL_REPRESENTATION('',(#1816),#1820);
#1816 = LINE('',#1817,#1818);
#1817 = CARTESIAN_POINT('',(-2.395632117936,0.273158171239));
#1818 = VECTOR('',#1819,1.);
#1819 = DIRECTION('',(1.,0.));
#1820 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1821 = ORIENTED_EDGE('',*,*,#1822,.F.);
#1822 = EDGE_CURVE('',#1823,#1800,#1825,.T.);
#1823 = VERTEX_POINT('',#1824);
#1824 = CARTESIAN_POINT('',(72.917494,112.30648,42.5));
#1825 = SURFACE_CURVE('',#1826,(#1830,#1837),.PCURVE_S1.);
#1826 = LINE('',#1827,#1828);
#1827 = CARTESIAN_POINT('',(72.917494,112.30648,42.5));
#1828 = VECTOR('',#1829,1.);
#1829 = DIRECTION('',(0.,0.,-1.));
#1830 = PCURVE('',#1762,#1831);
#1831 = DEFINITIONAL_REPRESENTATION('',(#1832),#1836);
#1832 = LINE('',#1833,#1834);
#1833 = CARTESIAN_POINT('',(16.4,0.));
#1834 = VECTOR('',#1835,1.);
#1835 = DIRECTION('',(0.,-1.));
#1836 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1837 = PCURVE('',#1838,#1843);
#1838 = PLANE('',#1839);
#1839 = AXIS2_PLACEMENT_3D('',#1840,#1841,#1842);
#1840 = CARTESIAN_POINT('',(72.917494,112.30648,42.5));
#1841 = DIRECTION('',(1.,-0.,0.));
#1842 = DIRECTION('',(0.,1.,0.));
#1843 = DEFINITIONAL_REPRESENTATION('',(#1844),#1848);
#1844 = LINE('',#1845,#1846);
#1845 = CARTESIAN_POINT('',(0.,0.));
#1846 = VECTOR('',#1847,1.);
#1847 = DIRECTION('',(0.,-1.));
#1848 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1849 = ORIENTED_EDGE('',*,*,#1850,.F.);
#1850 = EDGE_CURVE('',#1747,#1823,#1851,.T.);
#1851 = SURFACE_CURVE('',#1852,(#1856,#1863),.PCURVE_S1.);
#1852 = LINE('',#1853,#1854);
#1853 = CARTESIAN_POINT('',(89.317494,112.30648,42.5));
#1854 = VECTOR('',#1855,1.);
#1855 = DIRECTION('',(-1.,0.,0.));
#1856 = PCURVE('',#1762,#1857);
#1857 = DEFINITIONAL_REPRESENTATION('',(#1858),#1862);
#1858 = LINE('',#1859,#1860);
#1859 = CARTESIAN_POINT('',(0.,0.));
#1860 = VECTOR('',#1861,1.);
#1861 = DIRECTION('',(1.,0.));
#1862 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1863 = PCURVE('',#1327,#1864);
#1864 = DEFINITIONAL_REPRESENTATION('',(#1865),#1869);
#1865 = LINE('',#1866,#1867);
#1866 = CARTESIAN_POINT('',(-2.395632117936,0.273158171239));
#1867 = VECTOR('',#1868,1.);
#1868 = DIRECTION('',(1.,0.));
#1869 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1870 = ADVANCED_FACE('',(#1871),#1838,.F.);
#1871 = FACE_BOUND('',#1872,.F.);
#1872 = EDGE_LOOP('',(#1873,#1874,#1897,#1925));
#1873 = ORIENTED_EDGE('',*,*,#1822,.T.);
#1874 = ORIENTED_EDGE('',*,*,#1875,.T.);
#1875 = EDGE_CURVE('',#1800,#1876,#1878,.T.);
#1876 = VERTEX_POINT('',#1877);
#1877 = CARTESIAN_POINT('',(72.917494,112.906445,-4.866559));
#1878 = SURFACE_CURVE('',#1879,(#1883,#1890),.PCURVE_S1.);
#1879 = LINE('',#1880,#1881);
#1880 = CARTESIAN_POINT('',(72.917494,112.30648,-4.866559));
#1881 = VECTOR('',#1882,1.);
#1882 = DIRECTION('',(0.,1.,0.));
#1883 = PCURVE('',#1838,#1884);
#1884 = DEFINITIONAL_REPRESENTATION('',(#1885),#1889);
#1885 = LINE('',#1886,#1887);
#1886 = CARTESIAN_POINT('',(-1.42108547152E-14,-47.366559));
#1887 = VECTOR('',#1888,1.);
#1888 = DIRECTION('',(1.,0.));
#1889 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1890 = PCURVE('',#1273,#1891);
#1891 = DEFINITIONAL_REPRESENTATION('',(#1892),#1896);
#1892 = LINE('',#1893,#1894);
#1893 = CARTESIAN_POINT('',(14.004367882064,0.273158171239));
#1894 = VECTOR('',#1895,1.);
#1895 = DIRECTION('',(0.,1.));
#1896 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1897 = ORIENTED_EDGE('',*,*,#1898,.F.);
#1898 = EDGE_CURVE('',#1899,#1876,#1901,.T.);
#1899 = VERTEX_POINT('',#1900);
#1900 = CARTESIAN_POINT('',(72.917494,112.906445,42.5));
#1901 = SURFACE_CURVE('',#1902,(#1906,#1913),.PCURVE_S1.);
#1902 = LINE('',#1903,#1904);
#1903 = CARTESIAN_POINT('',(72.917494,112.906445,42.5));
#1904 = VECTOR('',#1905,1.);
#1905 = DIRECTION('',(0.,0.,-1.));
#1906 = PCURVE('',#1838,#1907);
#1907 = DEFINITIONAL_REPRESENTATION('',(#1908),#1912);
#1908 = LINE('',#1909,#1910);
#1909 = CARTESIAN_POINT('',(0.599965,0.));
#1910 = VECTOR('',#1911,1.);
#1911 = DIRECTION('',(0.,-1.));
#1912 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1913 = PCURVE('',#1914,#1919);
#1914 = PLANE('',#1915);
#1915 = AXIS2_PLACEMENT_3D('',#1916,#1917,#1918);
#1916 = CARTESIAN_POINT('',(72.917494,112.906445,42.5));
#1917 = DIRECTION('',(0.,1.,0.));
#1918 = DIRECTION('',(-1.,0.,0.));
#1919 = DEFINITIONAL_REPRESENTATION('',(#1920),#1924);
#1920 = LINE('',#1921,#1922);
#1921 = CARTESIAN_POINT('',(0.,0.));
#1922 = VECTOR('',#1923,1.);
#1923 = DIRECTION('',(0.,-1.));
#1924 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1925 = ORIENTED_EDGE('',*,*,#1926,.F.);
#1926 = EDGE_CURVE('',#1823,#1899,#1927,.T.);
#1927 = SURFACE_CURVE('',#1928,(#1932,#1939),.PCURVE_S1.);
#1928 = LINE('',#1929,#1930);
#1929 = CARTESIAN_POINT('',(72.917494,112.30648,42.5));
#1930 = VECTOR('',#1931,1.);
#1931 = DIRECTION('',(0.,1.,0.));
#1932 = PCURVE('',#1838,#1933);
#1933 = DEFINITIONAL_REPRESENTATION('',(#1934),#1938);
#1934 = LINE('',#1935,#1936);
#1935 = CARTESIAN_POINT('',(-1.42108547152E-14,0.));
#1936 = VECTOR('',#1937,1.);
#1937 = DIRECTION('',(1.,0.));
#1938 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1939 = PCURVE('',#1327,#1940);
#1940 = DEFINITIONAL_REPRESENTATION('',(#1941),#1945);
#1941 = LINE('',#1942,#1943);
#1942 = CARTESIAN_POINT('',(14.004367882064,0.273158171239));
#1943 = VECTOR('',#1944,1.);
#1944 = DIRECTION('',(0.,1.));
#1945 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1946 = ADVANCED_FACE('',(#1947),#1914,.F.);
#1947 = FACE_BOUND('',#1948,.F.);
#1948 = EDGE_LOOP('',(#1949,#1950,#1973,#2001));
#1949 = ORIENTED_EDGE('',*,*,#1898,.T.);
#1950 = ORIENTED_EDGE('',*,*,#1951,.T.);
#1951 = EDGE_CURVE('',#1876,#1952,#1954,.T.);
#1952 = VERTEX_POINT('',#1953);
#1953 = CARTESIAN_POINT('',(51.710833,112.906445,-4.866559));
#1954 = SURFACE_CURVE('',#1955,(#1959,#1966),.PCURVE_S1.);
#1955 = LINE('',#1956,#1957);
#1956 = CARTESIAN_POINT('',(72.917494,112.906445,-4.866559));
#1957 = VECTOR('',#1958,1.);
#1958 = DIRECTION('',(-1.,0.,0.));
#1959 = PCURVE('',#1914,#1960);
#1960 = DEFINITIONAL_REPRESENTATION('',(#1961),#1965);
#1961 = LINE('',#1962,#1963);
#1962 = CARTESIAN_POINT('',(-1.42108547152E-14,-47.366559));
#1963 = VECTOR('',#1964,1.);
#1964 = DIRECTION('',(1.,0.));
#1965 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1966 = PCURVE('',#1273,#1967);
#1967 = DEFINITIONAL_REPRESENTATION('',(#1968),#1972);
#1968 = LINE('',#1969,#1970);
#1969 = CARTESIAN_POINT('',(14.004367882064,0.873123171239));
#1970 = VECTOR('',#1971,1.);
#1971 = DIRECTION('',(1.,0.));
#1972 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1973 = ORIENTED_EDGE('',*,*,#1974,.F.);
#1974 = EDGE_CURVE('',#1975,#1952,#1977,.T.);
#1975 = VERTEX_POINT('',#1976);
#1976 = CARTESIAN_POINT('',(51.710833,112.906445,42.5));
#1977 = SURFACE_CURVE('',#1978,(#1982,#1989),.PCURVE_S1.);
#1978 = LINE('',#1979,#1980);
#1979 = CARTESIAN_POINT('',(51.710833,112.906445,42.5));
#1980 = VECTOR('',#1981,1.);
#1981 = DIRECTION('',(0.,0.,-1.));
#1982 = PCURVE('',#1914,#1983);
#1983 = DEFINITIONAL_REPRESENTATION('',(#1984),#1988);
#1984 = LINE('',#1985,#1986);
#1985 = CARTESIAN_POINT('',(21.206661,0.));
#1986 = VECTOR('',#1987,1.);
#1987 = DIRECTION('',(0.,-1.));
#1988 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1989 = PCURVE('',#1990,#1995);
#1990 = PLANE('',#1991);
#1991 = AXIS2_PLACEMENT_3D('',#1992,#1993,#1994);
#1992 = CARTESIAN_POINT('',(51.710833,112.906445,42.5));
#1993 = DIRECTION('',(0.931596580528,0.363493894237,0.));
#1994 = DIRECTION('',(-0.363493894237,0.931596580528,0.));
#1995 = DEFINITIONAL_REPRESENTATION('',(#1996),#2000);
#1996 = LINE('',#1997,#1998);
#1997 = CARTESIAN_POINT('',(0.,0.));
#1998 = VECTOR('',#1999,1.);
#1999 = DIRECTION('',(0.,-1.));
#2000 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2001 = ORIENTED_EDGE('',*,*,#2002,.F.);
#2002 = EDGE_CURVE('',#1899,#1975,#2003,.T.);
#2003 = SURFACE_CURVE('',#2004,(#2008,#2015),.PCURVE_S1.);
#2004 = LINE('',#2005,#2006);
#2005 = CARTESIAN_POINT('',(72.917494,112.906445,42.5));
#2006 = VECTOR('',#2007,1.);
#2007 = DIRECTION('',(-1.,0.,0.));
#2008 = PCURVE('',#1914,#2009);
#2009 = DEFINITIONAL_REPRESENTATION('',(#2010),#2014);
#2010 = LINE('',#2011,#2012);
#2011 = CARTESIAN_POINT('',(-1.42108547152E-14,0.));
#2012 = VECTOR('',#2013,1.);
#2013 = DIRECTION('',(1.,0.));
#2014 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2015 = PCURVE('',#1327,#2016);
#2016 = DEFINITIONAL_REPRESENTATION('',(#2017),#2021);
#2017 = LINE('',#2018,#2019);
#2018 = CARTESIAN_POINT('',(14.004367882064,0.873123171239));
#2019 = VECTOR('',#2020,1.);
#2020 = DIRECTION('',(1.,0.));
#2021 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2022 = ADVANCED_FACE('',(#2023),#1990,.F.);
#2023 = FACE_BOUND('',#2024,.F.);
#2024 = EDGE_LOOP('',(#2025,#2026,#2049,#2077));
#2025 = ORIENTED_EDGE('',*,*,#1974,.T.);
#2026 = ORIENTED_EDGE('',*,*,#2027,.T.);
#2027 = EDGE_CURVE('',#1952,#2028,#2030,.T.);
#2028 = VERTEX_POINT('',#2029);
#2029 = CARTESIAN_POINT('',(51.468253,113.528152,-4.866559));
#2030 = SURFACE_CURVE('',#2031,(#2035,#2042),.PCURVE_S1.);
#2031 = LINE('',#2032,#2033);
#2032 = CARTESIAN_POINT('',(51.710833,112.906445,-4.866559));
#2033 = VECTOR('',#2034,1.);
#2034 = DIRECTION('',(-0.363493894237,0.931596580528,0.));
#2035 = PCURVE('',#1990,#2036);
#2036 = DEFINITIONAL_REPRESENTATION('',(#2037),#2041);
#2037 = LINE('',#2038,#2039);
#2038 = CARTESIAN_POINT('',(0.,-47.366559));
#2039 = VECTOR('',#2040,1.);
#2040 = DIRECTION('',(1.,0.));
#2041 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2042 = PCURVE('',#1273,#2043);
#2043 = DEFINITIONAL_REPRESENTATION('',(#2044),#2048);
#2044 = LINE('',#2045,#2046);
#2045 = CARTESIAN_POINT('',(35.211028882064,0.873123171239));
#2046 = VECTOR('',#2047,1.);
#2047 = DIRECTION('',(0.363493894237,0.931596580528));
#2048 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2049 = ORIENTED_EDGE('',*,*,#2050,.F.);
#2050 = EDGE_CURVE('',#2051,#2028,#2053,.T.);
#2051 = VERTEX_POINT('',#2052);
#2052 = CARTESIAN_POINT('',(51.468253,113.528152,42.5));
#2053 = SURFACE_CURVE('',#2054,(#2058,#2065),.PCURVE_S1.);
#2054 = LINE('',#2055,#2056);
#2055 = CARTESIAN_POINT('',(51.468253,113.528152,42.5));
#2056 = VECTOR('',#2057,1.);
#2057 = DIRECTION('',(0.,0.,-1.));
#2058 = PCURVE('',#1990,#2059);
#2059 = DEFINITIONAL_REPRESENTATION('',(#2060),#2064);
#2060 = LINE('',#2061,#2062);
#2061 = CARTESIAN_POINT('',(0.667356464155,0.));
#2062 = VECTOR('',#2063,1.);
#2063 = DIRECTION('',(0.,-1.));
#2064 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2065 = PCURVE('',#2066,#2071);
#2066 = PLANE('',#2067);
#2067 = AXIS2_PLACEMENT_3D('',#2068,#2069,#2070);
#2068 = CARTESIAN_POINT('',(51.468253,113.528152,42.5));
#2069 = DIRECTION('',(0.998464000178,5.54043351018E-02,0.));
#2070 = DIRECTION('',(-5.54043351018E-02,0.998464000178,0.));
#2071 = DEFINITIONAL_REPRESENTATION('',(#2072),#2076);
#2072 = LINE('',#2073,#2074);
#2073 = CARTESIAN_POINT('',(0.,0.));
#2074 = VECTOR('',#2075,1.);
#2075 = DIRECTION('',(0.,-1.));
#2076 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2077 = ORIENTED_EDGE('',*,*,#2078,.F.);
#2078 = EDGE_CURVE('',#1975,#2051,#2079,.T.);
#2079 = SURFACE_CURVE('',#2080,(#2084,#2091),.PCURVE_S1.);
#2080 = LINE('',#2081,#2082);
#2081 = CARTESIAN_POINT('',(51.710833,112.906445,42.5));
#2082 = VECTOR('',#2083,1.);
#2083 = DIRECTION('',(-0.363493894237,0.931596580528,0.));
#2084 = PCURVE('',#1990,#2085);
#2085 = DEFINITIONAL_REPRESENTATION('',(#2086),#2090);
#2086 = LINE('',#2087,#2088);
#2087 = CARTESIAN_POINT('',(0.,0.));
#2088 = VECTOR('',#2089,1.);
#2089 = DIRECTION('',(1.,0.));
#2090 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2091 = PCURVE('',#1327,#2092);
#2092 = DEFINITIONAL_REPRESENTATION('',(#2093),#2097);
#2093 = LINE('',#2094,#2095);
#2094 = CARTESIAN_POINT('',(35.211028882064,0.873123171239));
#2095 = VECTOR('',#2096,1.);
#2096 = DIRECTION('',(0.363493894237,0.931596580528));
#2097 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2098 = ADVANCED_FACE('',(#2099),#2066,.F.);
#2099 = FACE_BOUND('',#2100,.F.);
#2100 = EDGE_LOOP('',(#2101,#2102,#2125,#2153));
#2101 = ORIENTED_EDGE('',*,*,#2050,.T.);
#2102 = ORIENTED_EDGE('',*,*,#2103,.T.);
#2103 = EDGE_CURVE('',#2028,#2104,#2106,.T.);
#2104 = VERTEX_POINT('',#2105);
#2105 = CARTESIAN_POINT('',(51.008936,121.80569,-4.866559));
#2106 = SURFACE_CURVE('',#2107,(#2111,#2118),.PCURVE_S1.);
#2107 = LINE('',#2108,#2109);
#2108 = CARTESIAN_POINT('',(51.468253,113.528152,-4.866559));
#2109 = VECTOR('',#2110,1.);
#2110 = DIRECTION('',(-5.54043351018E-02,0.998464000178,0.));
#2111 = PCURVE('',#2066,#2112);
#2112 = DEFINITIONAL_REPRESENTATION('',(#2113),#2117);
#2113 = LINE('',#2114,#2115);
#2114 = CARTESIAN_POINT('',(-1.418902684489E-14,-47.366559));
#2115 = VECTOR('',#2116,1.);
#2116 = DIRECTION('',(1.,0.));
#2117 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2118 = PCURVE('',#1273,#2119);
#2119 = DEFINITIONAL_REPRESENTATION('',(#2120),#2124);
#2120 = LINE('',#2121,#2122);
#2121 = CARTESIAN_POINT('',(35.453608882064,1.494830171239));
#2122 = VECTOR('',#2123,1.);
#2123 = DIRECTION('',(5.54043351018E-02,0.998464000178));
#2124 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2125 = ORIENTED_EDGE('',*,*,#2126,.F.);
#2126 = EDGE_CURVE('',#2127,#2104,#2129,.T.);
#2127 = VERTEX_POINT('',#2128);
#2128 = CARTESIAN_POINT('',(51.008936,121.80569,42.5));
#2129 = SURFACE_CURVE('',#2130,(#2134,#2141),.PCURVE_S1.);
#2130 = LINE('',#2131,#2132);
#2131 = CARTESIAN_POINT('',(51.008936,121.80569,42.5));
#2132 = VECTOR('',#2133,1.);
#2133 = DIRECTION('',(0.,0.,-1.));
#2134 = PCURVE('',#2066,#2135);
#2135 = DEFINITIONAL_REPRESENTATION('',(#2136),#2140);
#2136 = LINE('',#2137,#2138);
#2137 = CARTESIAN_POINT('',(8.290271856093,0.));
#2138 = VECTOR('',#2139,1.);
#2139 = DIRECTION('',(0.,-1.));
#2140 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2141 = PCURVE('',#2142,#2147);
#2142 = PLANE('',#2143);
#2143 = AXIS2_PLACEMENT_3D('',#2144,#2145,#2146);
#2144 = CARTESIAN_POINT('',(51.008936,121.80569,42.5));
#2145 = DIRECTION('',(5.196848995186E-05,-0.99999999865,0.));
#2146 = DIRECTION('',(0.99999999865,5.196848995186E-05,0.));
#2147 = DEFINITIONAL_REPRESENTATION('',(#2148),#2152);
#2148 = LINE('',#2149,#2150);
#2149 = CARTESIAN_POINT('',(0.,0.));
#2150 = VECTOR('',#2151,1.);
#2151 = DIRECTION('',(0.,-1.));
#2152 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2153 = ORIENTED_EDGE('',*,*,#2154,.F.);
#2154 = EDGE_CURVE('',#2051,#2127,#2155,.T.);
#2155 = SURFACE_CURVE('',#2156,(#2160,#2167),.PCURVE_S1.);
#2156 = LINE('',#2157,#2158);
#2157 = CARTESIAN_POINT('',(51.468253,113.528152,42.5));
#2158 = VECTOR('',#2159,1.);
#2159 = DIRECTION('',(-5.54043351018E-02,0.998464000178,0.));
#2160 = PCURVE('',#2066,#2161);
#2161 = DEFINITIONAL_REPRESENTATION('',(#2162),#2166);
#2162 = LINE('',#2163,#2164);
#2163 = CARTESIAN_POINT('',(-1.418902684489E-14,0.));
#2164 = VECTOR('',#2165,1.);
#2165 = DIRECTION('',(1.,0.));
#2166 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2167 = PCURVE('',#1327,#2168);
#2168 = DEFINITIONAL_REPRESENTATION('',(#2169),#2173);
#2169 = LINE('',#2170,#2171);
#2170 = CARTESIAN_POINT('',(35.453608882064,1.494830171239));
#2171 = VECTOR('',#2172,1.);
#2172 = DIRECTION('',(5.54043351018E-02,0.998464000178));
#2173 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2174 = ADVANCED_FACE('',(#2175),#2142,.F.);
#2175 = FACE_BOUND('',#2176,.F.);
#2176 = EDGE_LOOP('',(#2177,#2178,#2201,#2229));
#2177 = ORIENTED_EDGE('',*,*,#2126,.T.);
#2178 = ORIENTED_EDGE('',*,*,#2179,.T.);
#2179 = EDGE_CURVE('',#2104,#2180,#2182,.T.);
#2180 = VERTEX_POINT('',#2181);
#2181 = CARTESIAN_POINT('',(77.717428,121.807078,-4.866559));
#2182 = SURFACE_CURVE('',#2183,(#2187,#2194),.PCURVE_S1.);
#2183 = LINE('',#2184,#2185);
#2184 = CARTESIAN_POINT('',(51.008936,121.80569,-4.866559));
#2185 = VECTOR('',#2186,1.);
#2186 = DIRECTION('',(0.99999999865,5.196848995186E-05,0.));
#2187 = PCURVE('',#2142,#2188);
#2188 = DEFINITIONAL_REPRESENTATION('',(#2189),#2193);
#2189 = LINE('',#2190,#2191);
#2190 = CARTESIAN_POINT('',(-7.105427348006E-15,-47.366559));
#2191 = VECTOR('',#2192,1.);
#2192 = DIRECTION('',(1.,0.));
#2193 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2194 = PCURVE('',#1273,#2195);
#2195 = DEFINITIONAL_REPRESENTATION('',(#2196),#2200);
#2196 = LINE('',#2197,#2198);
#2197 = CARTESIAN_POINT('',(35.912925882064,9.772368171239));
#2198 = VECTOR('',#2199,1.);
#2199 = DIRECTION('',(-0.99999999865,5.196848995186E-05));
#2200 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2201 = ORIENTED_EDGE('',*,*,#2202,.F.);
#2202 = EDGE_CURVE('',#2203,#2180,#2205,.T.);
#2203 = VERTEX_POINT('',#2204);
#2204 = CARTESIAN_POINT('',(77.717428,121.807078,42.5));
#2205 = SURFACE_CURVE('',#2206,(#2210,#2217),.PCURVE_S1.);
#2206 = LINE('',#2207,#2208);
#2207 = CARTESIAN_POINT('',(77.717428,121.807078,42.5));
#2208 = VECTOR('',#2209,1.);
#2209 = DIRECTION('',(0.,0.,-1.));
#2210 = PCURVE('',#2142,#2211);
#2211 = DEFINITIONAL_REPRESENTATION('',(#2212),#2216);
#2212 = LINE('',#2213,#2214);
#2213 = CARTESIAN_POINT('',(26.708492036066,0.));
#2214 = VECTOR('',#2215,1.);
#2215 = DIRECTION('',(0.,-1.));
#2216 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2217 = PCURVE('',#2218,#2223);
#2218 = PLANE('',#2219);
#2219 = AXIS2_PLACEMENT_3D('',#2220,#2221,#2222);
#2220 = CARTESIAN_POINT('',(77.717428,121.807078,42.5));
#2221 = DIRECTION('',(0.999999999978,-6.670306744902E-06,0.));
#2222 = DIRECTION('',(6.670306744902E-06,0.999999999978,0.));
#2223 = DEFINITIONAL_REPRESENTATION('',(#2224),#2228);
#2224 = LINE('',#2225,#2226);
#2225 = CARTESIAN_POINT('',(0.,0.));
#2226 = VECTOR('',#2227,1.);
#2227 = DIRECTION('',(0.,-1.));
#2228 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2229 = ORIENTED_EDGE('',*,*,#2230,.F.);
#2230 = EDGE_CURVE('',#2127,#2203,#2231,.T.);
#2231 = SURFACE_CURVE('',#2232,(#2236,#2243),.PCURVE_S1.);
#2232 = LINE('',#2233,#2234);
#2233 = CARTESIAN_POINT('',(51.008936,121.80569,42.5));
#2234 = VECTOR('',#2235,1.);
#2235 = DIRECTION('',(0.99999999865,5.196848995186E-05,0.));
#2236 = PCURVE('',#2142,#2237);
#2237 = DEFINITIONAL_REPRESENTATION('',(#2238),#2242);
#2238 = LINE('',#2239,#2240);
#2239 = CARTESIAN_POINT('',(-7.105427348006E-15,0.));
#2240 = VECTOR('',#2241,1.);
#2241 = DIRECTION('',(1.,0.));
#2242 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2243 = PCURVE('',#1327,#2244);
#2244 = DEFINITIONAL_REPRESENTATION('',(#2245),#2249);
#2245 = LINE('',#2246,#2247);
#2246 = CARTESIAN_POINT('',(35.912925882064,9.772368171239));
#2247 = VECTOR('',#2248,1.);
#2248 = DIRECTION('',(-0.99999999865,5.196848995186E-05));
#2249 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2250 = ADVANCED_FACE('',(#2251),#2218,.F.);
#2251 = FACE_BOUND('',#2252,.F.);
#2252 = EDGE_LOOP('',(#2253,#2254,#2277,#2305));
#2253 = ORIENTED_EDGE('',*,*,#2202,.T.);
#2254 = ORIENTED_EDGE('',*,*,#2255,.T.);
#2255 = EDGE_CURVE('',#2180,#2256,#2258,.T.);
#2256 = VERTEX_POINT('',#2257);
#2257 = CARTESIAN_POINT('',(77.717435,122.856505,-4.866559));
#2258 = SURFACE_CURVE('',#2259,(#2263,#2270),.PCURVE_S1.);
#2259 = LINE('',#2260,#2261);
#2260 = CARTESIAN_POINT('',(77.717428,121.807078,-4.866559));
#2261 = VECTOR('',#2262,1.);
#2262 = DIRECTION('',(6.670306744902E-06,0.999999999978,0.));
#2263 = PCURVE('',#2218,#2264);
#2264 = DEFINITIONAL_REPRESENTATION('',(#2265),#2269);
#2265 = LINE('',#2266,#2267);
#2266 = CARTESIAN_POINT('',(0.,-47.366559));
#2267 = VECTOR('',#2268,1.);
#2268 = DIRECTION('',(1.,0.));
#2269 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2270 = PCURVE('',#1273,#2271);
#2271 = DEFINITIONAL_REPRESENTATION('',(#2272),#2276);
#2272 = LINE('',#2273,#2274);
#2273 = CARTESIAN_POINT('',(9.204433882064,9.773756171239));
#2274 = VECTOR('',#2275,1.);
#2275 = DIRECTION('',(-6.670306744902E-06,0.999999999978));
#2276 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2277 = ORIENTED_EDGE('',*,*,#2278,.F.);
#2278 = EDGE_CURVE('',#2279,#2256,#2281,.T.);
#2279 = VERTEX_POINT('',#2280);
#2280 = CARTESIAN_POINT('',(77.717435,122.856505,42.5));
#2281 = SURFACE_CURVE('',#2282,(#2286,#2293),.PCURVE_S1.);
#2282 = LINE('',#2283,#2284);
#2283 = CARTESIAN_POINT('',(77.717435,122.856505,42.5));
#2284 = VECTOR('',#2285,1.);
#2285 = DIRECTION('',(0.,0.,-1.));
#2286 = PCURVE('',#2218,#2287);
#2287 = DEFINITIONAL_REPRESENTATION('',(#2288),#2292);
#2288 = LINE('',#2289,#2290);
#2289 = CARTESIAN_POINT('',(1.049427000023,0.));
#2290 = VECTOR('',#2291,1.);
#2291 = DIRECTION('',(0.,-1.));
#2292 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2293 = PCURVE('',#2294,#2299);
#2294 = PLANE('',#2295);
#2295 = AXIS2_PLACEMENT_3D('',#2296,#2297,#2298);
#2296 = CARTESIAN_POINT('',(77.717435,122.856505,42.5));
#2297 = DIRECTION('',(0.,-1.,0.));
#2298 = DIRECTION('',(1.,0.,0.));
#2299 = DEFINITIONAL_REPRESENTATION('',(#2300),#2304);
#2300 = LINE('',#2301,#2302);
#2301 = CARTESIAN_POINT('',(0.,0.));
#2302 = VECTOR('',#2303,1.);
#2303 = DIRECTION('',(0.,-1.));
#2304 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2305 = ORIENTED_EDGE('',*,*,#2306,.F.);
#2306 = EDGE_CURVE('',#2203,#2279,#2307,.T.);
#2307 = SURFACE_CURVE('',#2308,(#2312,#2319),.PCURVE_S1.);
#2308 = LINE('',#2309,#2310);
#2309 = CARTESIAN_POINT('',(77.717428,121.807078,42.5));
#2310 = VECTOR('',#2311,1.);
#2311 = DIRECTION('',(6.670306744902E-06,0.999999999978,0.));
#2312 = PCURVE('',#2218,#2313);
#2313 = DEFINITIONAL_REPRESENTATION('',(#2314),#2318);
#2314 = LINE('',#2315,#2316);
#2315 = CARTESIAN_POINT('',(0.,0.));
#2316 = VECTOR('',#2317,1.);
#2317 = DIRECTION('',(1.,0.));
#2318 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2319 = PCURVE('',#1327,#2320);
#2320 = DEFINITIONAL_REPRESENTATION('',(#2321),#2325);
#2321 = LINE('',#2322,#2323);
#2322 = CARTESIAN_POINT('',(9.204433882064,9.773756171239));
#2323 = VECTOR('',#2324,1.);
#2324 = DIRECTION('',(-6.670306744902E-06,0.999999999978));
#2325 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2326 = ADVANCED_FACE('',(#2327),#2294,.F.);
#2327 = FACE_BOUND('',#2328,.F.);
#2328 = EDGE_LOOP('',(#2329,#2330,#2353,#2376));
#2329 = ORIENTED_EDGE('',*,*,#2278,.T.);
#2330 = ORIENTED_EDGE('',*,*,#2331,.T.);
#2331 = EDGE_CURVE('',#2256,#2332,#2334,.T.);
#2332 = VERTEX_POINT('',#2333);
#2333 = CARTESIAN_POINT('',(85.917422,122.856505,-4.866559));
#2334 = SURFACE_CURVE('',#2335,(#2339,#2346),.PCURVE_S1.);
#2335 = LINE('',#2336,#2337);
#2336 = CARTESIAN_POINT('',(77.717435,122.856505,-4.866559));
#2337 = VECTOR('',#2338,1.);
#2338 = DIRECTION('',(1.,0.,0.));
#2339 = PCURVE('',#2294,#2340);
#2340 = DEFINITIONAL_REPRESENTATION('',(#2341),#2345);
#2341 = LINE('',#2342,#2343);
#2342 = CARTESIAN_POINT('',(1.42108547152E-14,-47.366559));
#2343 = VECTOR('',#2344,1.);
#2344 = DIRECTION('',(1.,0.));
#2345 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2346 = PCURVE('',#1273,#2347);
#2347 = DEFINITIONAL_REPRESENTATION('',(#2348),#2352);
#2348 = LINE('',#2349,#2350);
#2349 = CARTESIAN_POINT('',(9.204426882064,10.823183171239));
#2350 = VECTOR('',#2351,1.);
#2351 = DIRECTION('',(-1.,0.));
#2352 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2353 = ORIENTED_EDGE('',*,*,#2354,.F.);
#2354 = EDGE_CURVE('',#2355,#2332,#2357,.T.);
#2355 = VERTEX_POINT('',#2356);
#2356 = CARTESIAN_POINT('',(85.917422,122.856505,42.5));
#2357 = SURFACE_CURVE('',#2358,(#2362,#2369),.PCURVE_S1.);
#2358 = LINE('',#2359,#2360);
#2359 = CARTESIAN_POINT('',(85.917422,122.856505,42.5));
#2360 = VECTOR('',#2361,1.);
#2361 = DIRECTION('',(0.,0.,-1.));
#2362 = PCURVE('',#2294,#2363);
#2363 = DEFINITIONAL_REPRESENTATION('',(#2364),#2368);
#2364 = LINE('',#2365,#2366);
#2365 = CARTESIAN_POINT('',(8.199987,0.));
#2366 = VECTOR('',#2367,1.);
#2367 = DIRECTION('',(0.,-1.));
#2368 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2369 = PCURVE('',#1245,#2370);
#2370 = DEFINITIONAL_REPRESENTATION('',(#2371),#2375);
#2371 = LINE('',#2372,#2373);
#2372 = CARTESIAN_POINT('',(0.,0.));
#2373 = VECTOR('',#2374,1.);
#2374 = DIRECTION('',(0.,-1.));
#2375 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2376 = ORIENTED_EDGE('',*,*,#2377,.F.);
#2377 = EDGE_CURVE('',#2279,#2355,#2378,.T.);
#2378 = SURFACE_CURVE('',#2379,(#2383,#2390),.PCURVE_S1.);
#2379 = LINE('',#2380,#2381);
#2380 = CARTESIAN_POINT('',(77.717435,122.856505,42.5));
#2381 = VECTOR('',#2382,1.);
#2382 = DIRECTION('',(1.,0.,0.));
#2383 = PCURVE('',#2294,#2384);
#2384 = DEFINITIONAL_REPRESENTATION('',(#2385),#2389);
#2385 = LINE('',#2386,#2387);
#2386 = CARTESIAN_POINT('',(1.42108547152E-14,0.));
#2387 = VECTOR('',#2388,1.);
#2388 = DIRECTION('',(1.,0.));
#2389 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2390 = PCURVE('',#1327,#2391);
#2391 = DEFINITIONAL_REPRESENTATION('',(#2392),#2396);
#2392 = LINE('',#2393,#2394);
#2393 = CARTESIAN_POINT('',(9.204426882064,10.823183171239));
#2394 = VECTOR('',#2395,1.);
#2395 = DIRECTION('',(-1.,0.));
#2396 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2397 = ADVANCED_FACE('',(#2398),#1245,.F.);
#2398 = FACE_BOUND('',#2399,.F.);
#2399 = EDGE_LOOP('',(#2400,#2401,#2422,#2423));
#2400 = ORIENTED_EDGE('',*,*,#2354,.T.);
#2401 = ORIENTED_EDGE('',*,*,#2402,.T.);
#2402 = EDGE_CURVE('',#2332,#1225,#2403,.T.);
#2403 = SURFACE_CURVE('',#2404,(#2408,#2415),.PCURVE_S1.);
#2404 = LINE('',#2405,#2406);
#2405 = CARTESIAN_POINT('',(85.917422,122.856505,-4.866559));
#2406 = VECTOR('',#2407,1.);
#2407 = DIRECTION('',(0.,1.,0.));
#2408 = PCURVE('',#1245,#2409);
#2409 = DEFINITIONAL_REPRESENTATION('',(#2410),#2414);
#2410 = LINE('',#2411,#2412);
#2411 = CARTESIAN_POINT('',(-1.42108547152E-14,-47.366559));
#2412 = VECTOR('',#2413,1.);
#2413 = DIRECTION('',(1.,0.));
#2414 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2415 = PCURVE('',#1273,#2416);
#2416 = DEFINITIONAL_REPRESENTATION('',(#2417),#2421);
#2417 = LINE('',#2418,#2419);
#2418 = CARTESIAN_POINT('',(1.004439882064,10.823183171239));
#2419 = VECTOR('',#2420,1.);
#2420 = DIRECTION('',(0.,1.));
#2421 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2422 = ORIENTED_EDGE('',*,*,#1222,.F.);
#2423 = ORIENTED_EDGE('',*,*,#2424,.F.);
#2424 = EDGE_CURVE('',#2355,#1223,#2425,.T.);
#2425 = SURFACE_CURVE('',#2426,(#2430,#2437),.PCURVE_S1.);
#2426 = LINE('',#2427,#2428);
#2427 = CARTESIAN_POINT('',(85.917422,122.856505,42.5));
#2428 = VECTOR('',#2429,1.);
#2429 = DIRECTION('',(0.,1.,0.));
#2430 = PCURVE('',#1245,#2431);
#2431 = DEFINITIONAL_REPRESENTATION('',(#2432),#2436);
#2432 = LINE('',#2433,#2434);
#2433 = CARTESIAN_POINT('',(-1.42108547152E-14,0.));
#2434 = VECTOR('',#2435,1.);
#2435 = DIRECTION('',(1.,0.));
#2436 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2437 = PCURVE('',#1327,#2438);
#2438 = DEFINITIONAL_REPRESENTATION('',(#2439),#2443);
#2439 = LINE('',#2440,#2441);
#2440 = CARTESIAN_POINT('',(1.004439882064,10.823183171239));
#2441 = VECTOR('',#2442,1.);
#2442 = DIRECTION('',(0.,1.));
#2443 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2444 = ADVANCED_FACE('',(#2445),#1327,.F.);
#2445 = FACE_BOUND('',#2446,.F.);
#2446 = EDGE_LOOP('',(#2447,#2448,#2449,#2450,#2451,#2452,#2453,#2454,
    #2455,#2456,#2457,#2458,#2459,#2460,#2461,#2462));
#2447 = ORIENTED_EDGE('',*,*,#1313,.T.);
#2448 = ORIENTED_EDGE('',*,*,#1394,.T.);
#2449 = ORIENTED_EDGE('',*,*,#1470,.T.);
#2450 = ORIENTED_EDGE('',*,*,#1546,.T.);
#2451 = ORIENTED_EDGE('',*,*,#1622,.T.);
#2452 = ORIENTED_EDGE('',*,*,#1698,.T.);
#2453 = ORIENTED_EDGE('',*,*,#1774,.T.);
#2454 = ORIENTED_EDGE('',*,*,#1850,.T.);
#2455 = ORIENTED_EDGE('',*,*,#1926,.T.);
#2456 = ORIENTED_EDGE('',*,*,#2002,.T.);
#2457 = ORIENTED_EDGE('',*,*,#2078,.T.);
#2458 = ORIENTED_EDGE('',*,*,#2154,.T.);
#2459 = ORIENTED_EDGE('',*,*,#2230,.T.);
#2460 = ORIENTED_EDGE('',*,*,#2306,.T.);
#2461 = ORIENTED_EDGE('',*,*,#2377,.T.);
#2462 = ORIENTED_EDGE('',*,*,#2424,.T.);
#2463 = ADVANCED_FACE('',(#2464),#1273,.T.);
#2464 = FACE_BOUND('',#2465,.T.);
#2465 = EDGE_LOOP('',(#2466,#2467,#2468,#2469,#2470,#2471,#2472,#2473,
    #2474,#2475,#2476,#2477,#2478,#2479,#2480,#2481));
#2466 = ORIENTED_EDGE('',*,*,#1257,.T.);
#2467 = ORIENTED_EDGE('',*,*,#1343,.T.);
#2468 = ORIENTED_EDGE('',*,*,#1419,.T.);
#2469 = ORIENTED_EDGE('',*,*,#1495,.T.);
#2470 = ORIENTED_EDGE('',*,*,#1571,.T.);
#2471 = ORIENTED_EDGE('',*,*,#1647,.T.);
#2472 = ORIENTED_EDGE('',*,*,#1723,.T.);
#2473 = ORIENTED_EDGE('',*,*,#1799,.T.);
#2474 = ORIENTED_EDGE('',*,*,#1875,.T.);
#2475 = ORIENTED_EDGE('',*,*,#1951,.T.);
#2476 = ORIENTED_EDGE('',*,*,#2027,.T.);
#2477 = ORIENTED_EDGE('',*,*,#2103,.T.);
#2478 = ORIENTED_EDGE('',*,*,#2179,.T.);
#2479 = ORIENTED_EDGE('',*,*,#2255,.T.);
#2480 = ORIENTED_EDGE('',*,*,#2331,.T.);
#2481 = ORIENTED_EDGE('',*,*,#2402,.T.);
#2482 = ( GEOMETRIC_REPRESENTATION_CONTEXT(3) 
GLOBAL_UNCERTAINTY_ASSIGNED_CONTEXT((#2486)) 
GLOBAL_UNIT_ASSIGNED_CONTEXT((#2483,#2484,#2485)) REPRESENTATION_CONTEXT
('Context #1','3D Context with UNIT and UNCERTAINTY') );
#2483 = ( LENGTH_UNIT() NAMED_UNIT(*) SI_UNIT(.MILLI.,.METRE.) );
#2484 = ( NAMED_UNIT(*) PLANE_ANGLE_UNIT() SI_UNIT($,.RADIAN.) );
#2485 = ( NAMED_UNIT(*) SI_UNIT($,.STERADIAN.) SOLID_ANGLE_UNIT() );
#2486 = UNCERTAINTY_MEASURE_WITH_UNIT(LENGTH_MEASURE(1.E-07),#2483,
  'distance_accuracy_value','confusion accuracy');
#2487 = CONTEXT_DEPENDENT_SHAPE_REPRESENTATION(#2488,#2490);
#2488 = ( REPRESENTATION_RELATIONSHIP('','',#1215,#1192) 
REPRESENTATION_RELATIONSHIP_WITH_TRANSFORMATION(#2489) 
SHAPE_REPRESENTATION_RELATIONSHIP() );
#2489 = ITEM_DEFINED_TRANSFORMATION('','',#11,#1193);
#2490 = PRODUCT_DEFINITION_SHAPE('Placement','Placement of an item',
  #2491);
#2491 = NEXT_ASSEMBLY_USAGE_OCCURRENCE('215','=>[0:1:1:5]','',#1187,
  #1210,$);
#2492 = PRODUCT_RELATED_PRODUCT_CATEGORY('part',$,(#1212));
#2493 = SHAPE_DEFINITION_REPRESENTATION(#2494,#2500);
#2494 = PRODUCT_DEFINITION_SHAPE('','',#2495);
#2495 = PRODUCT_DEFINITION('design','',#2496,#2499);
#2496 = PRODUCT_DEFINITION_FORMATION('','',#2497);
#2497 = PRODUCT('Pile_Group_Boundary_3','Pile_Group_Boundary_3','',(
    #2498));
#2498 = PRODUCT_CONTEXT('',#2,'mechanical');
#2499 = PRODUCT_DEFINITION_CONTEXT('part definition',#2,'design');
#2500 = SHAPE_REPRESENTATION('',(#11,#2501),#2505);
#2501 = AXIS2_PLACEMENT_3D('',#2502,#2503,#2504);
#2502 = CARTESIAN_POINT('',(0.,0.,0.));
#2503 = DIRECTION('',(0.,0.,1.));
#2504 = DIRECTION('',(1.,0.,-0.));
#2505 = ( GEOMETRIC_REPRESENTATION_CONTEXT(3) 
GLOBAL_UNCERTAINTY_ASSIGNED_CONTEXT((#2509)) 
GLOBAL_UNIT_ASSIGNED_CONTEXT((#2506,#2507,#2508)) REPRESENTATION_CONTEXT
('Context #1','3D Context with UNIT and UNCERTAINTY') );
#2506 = ( LENGTH_UNIT() NAMED_UNIT(*) SI_UNIT(.MILLI.,.METRE.) );
#2507 = ( NAMED_UNIT(*) PLANE_ANGLE_UNIT() SI_UNIT($,.RADIAN.) );
#2508 = ( NAMED_UNIT(*) SI_UNIT($,.STERADIAN.) SOLID_ANGLE_UNIT() );
#2509 = UNCERTAINTY_MEASURE_WITH_UNIT(LENGTH_MEASURE(1.E-07),#2506,
  'distance_accuracy_value','confusion accuracy');
#2510 = CONTEXT_DEPENDENT_SHAPE_REPRESENTATION(#2511,#2513);
#2511 = ( REPRESENTATION_RELATIONSHIP('','',#2500,#10) 
REPRESENTATION_RELATIONSHIP_WITH_TRANSFORMATION(#2512) 
SHAPE_REPRESENTATION_RELATIONSHIP() );
#2512 = ITEM_DEFINED_TRANSFORMATION('','',#11,#23);
#2513 = PRODUCT_DEFINITION_SHAPE('Placement','Placement of an item',
  #2514);
#2514 = NEXT_ASSEMBLY_USAGE_OCCURRENCE('218','=>[0:1:1:6]','',#5,#2495,$
  );
#2515 = PRODUCT_RELATED_PRODUCT_CATEGORY('part',$,(#2497));
#2516 = SHAPE_DEFINITION_REPRESENTATION(#2517,#2523);
#2517 = PRODUCT_DEFINITION_SHAPE('','',#2518);
#2518 = PRODUCT_DEFINITION('design','',#2519,#2522);
#2519 = PRODUCT_DEFINITION_FORMATION('','',#2520);
#2520 = PRODUCT('SOLID','SOLID','',(#2521));
#2521 = PRODUCT_CONTEXT('',#2,'mechanical');
#2522 = PRODUCT_DEFINITION_CONTEXT('part definition',#2,'design');
#2523 = ADVANCED_BREP_SHAPE_REPRESENTATION('',(#11,#2524),#3478);
#2524 = MANIFOLD_SOLID_BREP('',#2525);
#2525 = CLOSED_SHELL('',(#2526,#2646,#2722,#2798,#2874,#2950,#3026,#3102
    ,#3178,#3254,#3330,#3401,#3448,#3463));
#2526 = ADVANCED_FACE('',(#2527),#2541,.F.);
#2527 = FACE_BOUND('',#2528,.F.);
#2528 = EDGE_LOOP('',(#2529,#2564,#2592,#2620));
#2529 = ORIENTED_EDGE('',*,*,#2530,.T.);
#2530 = EDGE_CURVE('',#2531,#2533,#2535,.T.);
#2531 = VERTEX_POINT('',#2532);
#2532 = CARTESIAN_POINT('',(51.710833,112.906445,47.5));
#2533 = VERTEX_POINT('',#2534);
#2534 = CARTESIAN_POINT('',(51.710833,112.906445,2.973447));
#2535 = SURFACE_CURVE('',#2536,(#2540,#2552),.PCURVE_S1.);
#2536 = LINE('',#2537,#2538);
#2537 = CARTESIAN_POINT('',(51.710833,112.906445,47.5));
#2538 = VECTOR('',#2539,1.);
#2539 = DIRECTION('',(0.,0.,-1.));
#2540 = PCURVE('',#2541,#2546);
#2541 = PLANE('',#2542);
#2542 = AXIS2_PLACEMENT_3D('',#2543,#2544,#2545);
#2543 = CARTESIAN_POINT('',(51.710833,112.906445,47.5));
#2544 = DIRECTION('',(0.,-1.,0.));
#2545 = DIRECTION('',(1.,0.,0.));
#2546 = DEFINITIONAL_REPRESENTATION('',(#2547),#2551);
#2547 = LINE('',#2548,#2549);
#2548 = CARTESIAN_POINT('',(0.,0.));
#2549 = VECTOR('',#2550,1.);
#2550 = DIRECTION('',(0.,-1.));
#2551 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2552 = PCURVE('',#2553,#2558);
#2553 = PLANE('',#2554);
#2554 = AXIS2_PLACEMENT_3D('',#2555,#2556,#2557);
#2555 = CARTESIAN_POINT('',(53.594997,108.077543,47.5));
#2556 = DIRECTION('',(0.931596275714,0.363494675443,0.));
#2557 = DIRECTION('',(-0.363494675443,0.931596275714,0.));
#2558 = DEFINITIONAL_REPRESENTATION('',(#2559),#2563);
#2559 = LINE('',#2560,#2561);
#2560 = CARTESIAN_POINT('',(5.18347070065,0.));
#2561 = VECTOR('',#2562,1.);
#2562 = DIRECTION('',(0.,-1.));
#2563 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2564 = ORIENTED_EDGE('',*,*,#2565,.T.);
#2565 = EDGE_CURVE('',#2533,#2566,#2568,.T.);
#2566 = VERTEX_POINT('',#2567);
#2567 = CARTESIAN_POINT('',(72.917494,112.906445,2.973447));
#2568 = SURFACE_CURVE('',#2569,(#2573,#2580),.PCURVE_S1.);
#2569 = LINE('',#2570,#2571);
#2570 = CARTESIAN_POINT('',(51.710833,112.906445,2.973447));
#2571 = VECTOR('',#2572,1.);
#2572 = DIRECTION('',(1.,0.,0.));
#2573 = PCURVE('',#2541,#2574);
#2574 = DEFINITIONAL_REPRESENTATION('',(#2575),#2579);
#2575 = LINE('',#2576,#2577);
#2576 = CARTESIAN_POINT('',(7.105427357601E-15,-44.526553));
#2577 = VECTOR('',#2578,1.);
#2578 = DIRECTION('',(1.,0.));
#2579 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2580 = PCURVE('',#2581,#2586);
#2581 = PLANE('',#2582);
#2582 = AXIS2_PLACEMENT_3D('',#2583,#2584,#2585);
#2583 = CARTESIAN_POINT('',(72.632159577627,105.24663858753,2.973447));
#2584 = DIRECTION('',(0.,0.,-1.));
#2585 = DIRECTION('',(-1.,0.,0.));
#2586 = DEFINITIONAL_REPRESENTATION('',(#2587),#2591);
#2587 = LINE('',#2588,#2589);
#2588 = CARTESIAN_POINT('',(20.921326577627,7.659806412461));
#2589 = VECTOR('',#2590,1.);
#2590 = DIRECTION('',(-1.,0.));
#2591 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2592 = ORIENTED_EDGE('',*,*,#2593,.F.);
#2593 = EDGE_CURVE('',#2594,#2566,#2596,.T.);
#2594 = VERTEX_POINT('',#2595);
#2595 = CARTESIAN_POINT('',(72.917494,112.906445,47.5));
#2596 = SURFACE_CURVE('',#2597,(#2601,#2608),.PCURVE_S1.);
#2597 = LINE('',#2598,#2599);
#2598 = CARTESIAN_POINT('',(72.917494,112.906445,47.5));
#2599 = VECTOR('',#2600,1.);
#2600 = DIRECTION('',(0.,0.,-1.));
#2601 = PCURVE('',#2541,#2602);
#2602 = DEFINITIONAL_REPRESENTATION('',(#2603),#2607);
#2603 = LINE('',#2604,#2605);
#2604 = CARTESIAN_POINT('',(21.206661,0.));
#2605 = VECTOR('',#2606,1.);
#2606 = DIRECTION('',(0.,-1.));
#2607 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2608 = PCURVE('',#2609,#2614);
#2609 = PLANE('',#2610);
#2610 = AXIS2_PLACEMENT_3D('',#2611,#2612,#2613);
#2611 = CARTESIAN_POINT('',(72.917494,112.906445,47.5));
#2612 = DIRECTION('',(-1.,-0.,-0.));
#2613 = DIRECTION('',(0.,-1.,0.));
#2614 = DEFINITIONAL_REPRESENTATION('',(#2615),#2619);
#2615 = LINE('',#2616,#2617);
#2616 = CARTESIAN_POINT('',(0.,0.));
#2617 = VECTOR('',#2618,1.);
#2618 = DIRECTION('',(0.,-1.));
#2619 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2620 = ORIENTED_EDGE('',*,*,#2621,.F.);
#2621 = EDGE_CURVE('',#2531,#2594,#2622,.T.);
#2622 = SURFACE_CURVE('',#2623,(#2627,#2634),.PCURVE_S1.);
#2623 = LINE('',#2624,#2625);
#2624 = CARTESIAN_POINT('',(51.710833,112.906445,47.5));
#2625 = VECTOR('',#2626,1.);
#2626 = DIRECTION('',(1.,0.,0.));
#2627 = PCURVE('',#2541,#2628);
#2628 = DEFINITIONAL_REPRESENTATION('',(#2629),#2633);
#2629 = LINE('',#2630,#2631);
#2630 = CARTESIAN_POINT('',(7.105427357601E-15,0.));
#2631 = VECTOR('',#2632,1.);
#2632 = DIRECTION('',(1.,0.));
#2633 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2634 = PCURVE('',#2635,#2640);
#2635 = PLANE('',#2636);
#2636 = AXIS2_PLACEMENT_3D('',#2637,#2638,#2639);
#2637 = CARTESIAN_POINT('',(72.632159577627,105.24663858753,47.5));
#2638 = DIRECTION('',(0.,0.,-1.));
#2639 = DIRECTION('',(-1.,0.,0.));
#2640 = DEFINITIONAL_REPRESENTATION('',(#2641),#2645);
#2641 = LINE('',#2642,#2643);
#2642 = CARTESIAN_POINT('',(20.921326577627,7.659806412461));
#2643 = VECTOR('',#2644,1.);
#2644 = DIRECTION('',(-1.,0.));
#2645 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2646 = ADVANCED_FACE('',(#2647),#2609,.F.);
#2647 = FACE_BOUND('',#2648,.F.);
#2648 = EDGE_LOOP('',(#2649,#2650,#2673,#2701));
#2649 = ORIENTED_EDGE('',*,*,#2593,.T.);
#2650 = ORIENTED_EDGE('',*,*,#2651,.T.);
#2651 = EDGE_CURVE('',#2566,#2652,#2654,.T.);
#2652 = VERTEX_POINT('',#2653);
#2653 = CARTESIAN_POINT('',(72.917494,112.30648,2.973447));
#2654 = SURFACE_CURVE('',#2655,(#2659,#2666),.PCURVE_S1.);
#2655 = LINE('',#2656,#2657);
#2656 = CARTESIAN_POINT('',(72.917494,112.906445,2.973447));
#2657 = VECTOR('',#2658,1.);
#2658 = DIRECTION('',(0.,-1.,0.));
#2659 = PCURVE('',#2609,#2660);
#2660 = DEFINITIONAL_REPRESENTATION('',(#2661),#2665);
#2661 = LINE('',#2662,#2663);
#2662 = CARTESIAN_POINT('',(-1.42108547152E-14,-44.526553));
#2663 = VECTOR('',#2664,1.);
#2664 = DIRECTION('',(1.,0.));
#2665 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2666 = PCURVE('',#2581,#2667);
#2667 = DEFINITIONAL_REPRESENTATION('',(#2668),#2672);
#2668 = LINE('',#2669,#2670);
#2669 = CARTESIAN_POINT('',(-0.285334422373,7.659806412461));
#2670 = VECTOR('',#2671,1.);
#2671 = DIRECTION('',(0.,-1.));
#2672 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2673 = ORIENTED_EDGE('',*,*,#2674,.F.);
#2674 = EDGE_CURVE('',#2675,#2652,#2677,.T.);
#2675 = VERTEX_POINT('',#2676);
#2676 = CARTESIAN_POINT('',(72.917494,112.30648,47.5));
#2677 = SURFACE_CURVE('',#2678,(#2682,#2689),.PCURVE_S1.);
#2678 = LINE('',#2679,#2680);
#2679 = CARTESIAN_POINT('',(72.917494,112.30648,47.5));
#2680 = VECTOR('',#2681,1.);
#2681 = DIRECTION('',(0.,0.,-1.));
#2682 = PCURVE('',#2609,#2683);
#2683 = DEFINITIONAL_REPRESENTATION('',(#2684),#2688);
#2684 = LINE('',#2685,#2686);
#2685 = CARTESIAN_POINT('',(0.599965,0.));
#2686 = VECTOR('',#2687,1.);
#2687 = DIRECTION('',(0.,-1.));
#2688 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2689 = PCURVE('',#2690,#2695);
#2690 = PLANE('',#2691);
#2691 = AXIS2_PLACEMENT_3D('',#2692,#2693,#2694);
#2692 = CARTESIAN_POINT('',(72.917494,112.30648,47.5));
#2693 = DIRECTION('',(0.,-1.,0.));
#2694 = DIRECTION('',(1.,0.,0.));
#2695 = DEFINITIONAL_REPRESENTATION('',(#2696),#2700);
#2696 = LINE('',#2697,#2698);
#2697 = CARTESIAN_POINT('',(0.,0.));
#2698 = VECTOR('',#2699,1.);
#2699 = DIRECTION('',(0.,-1.));
#2700 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2701 = ORIENTED_EDGE('',*,*,#2702,.F.);
#2702 = EDGE_CURVE('',#2594,#2675,#2703,.T.);
#2703 = SURFACE_CURVE('',#2704,(#2708,#2715),.PCURVE_S1.);
#2704 = LINE('',#2705,#2706);
#2705 = CARTESIAN_POINT('',(72.917494,112.906445,47.5));
#2706 = VECTOR('',#2707,1.);
#2707 = DIRECTION('',(0.,-1.,0.));
#2708 = PCURVE('',#2609,#2709);
#2709 = DEFINITIONAL_REPRESENTATION('',(#2710),#2714);
#2710 = LINE('',#2711,#2712);
#2711 = CARTESIAN_POINT('',(-1.42108547152E-14,0.));
#2712 = VECTOR('',#2713,1.);
#2713 = DIRECTION('',(1.,0.));
#2714 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2715 = PCURVE('',#2635,#2716);
#2716 = DEFINITIONAL_REPRESENTATION('',(#2717),#2721);
#2717 = LINE('',#2718,#2719);
#2718 = CARTESIAN_POINT('',(-0.285334422373,7.659806412461));
#2719 = VECTOR('',#2720,1.);
#2720 = DIRECTION('',(0.,-1.));
#2721 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2722 = ADVANCED_FACE('',(#2723),#2690,.F.);
#2723 = FACE_BOUND('',#2724,.F.);
#2724 = EDGE_LOOP('',(#2725,#2726,#2749,#2777));
#2725 = ORIENTED_EDGE('',*,*,#2674,.T.);
#2726 = ORIENTED_EDGE('',*,*,#2727,.T.);
#2727 = EDGE_CURVE('',#2652,#2728,#2730,.T.);
#2728 = VERTEX_POINT('',#2729);
#2729 = CARTESIAN_POINT('',(89.317494,112.30648,2.973447));
#2730 = SURFACE_CURVE('',#2731,(#2735,#2742),.PCURVE_S1.);
#2731 = LINE('',#2732,#2733);
#2732 = CARTESIAN_POINT('',(72.917494,112.30648,2.973447));
#2733 = VECTOR('',#2734,1.);
#2734 = DIRECTION('',(1.,0.,0.));
#2735 = PCURVE('',#2690,#2736);
#2736 = DEFINITIONAL_REPRESENTATION('',(#2737),#2741);
#2737 = LINE('',#2738,#2739);
#2738 = CARTESIAN_POINT('',(0.,-44.526553));
#2739 = VECTOR('',#2740,1.);
#2740 = DIRECTION('',(1.,0.));
#2741 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2742 = PCURVE('',#2581,#2743);
#2743 = DEFINITIONAL_REPRESENTATION('',(#2744),#2748);
#2744 = LINE('',#2745,#2746);
#2745 = CARTESIAN_POINT('',(-0.285334422373,7.059841412461));
#2746 = VECTOR('',#2747,1.);
#2747 = DIRECTION('',(-1.,0.));
#2748 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2749 = ORIENTED_EDGE('',*,*,#2750,.F.);
#2750 = EDGE_CURVE('',#2751,#2728,#2753,.T.);
#2751 = VERTEX_POINT('',#2752);
#2752 = CARTESIAN_POINT('',(89.317494,112.30648,47.5));
#2753 = SURFACE_CURVE('',#2754,(#2758,#2765),.PCURVE_S1.);
#2754 = LINE('',#2755,#2756);
#2755 = CARTESIAN_POINT('',(89.317494,112.30648,47.5));
#2756 = VECTOR('',#2757,1.);
#2757 = DIRECTION('',(0.,0.,-1.));
#2758 = PCURVE('',#2690,#2759);
#2759 = DEFINITIONAL_REPRESENTATION('',(#2760),#2764);
#2760 = LINE('',#2761,#2762);
#2761 = CARTESIAN_POINT('',(16.4,0.));
#2762 = VECTOR('',#2763,1.);
#2763 = DIRECTION('',(0.,-1.));
#2764 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2765 = PCURVE('',#2766,#2771);
#2766 = PLANE('',#2767);
#2767 = AXIS2_PLACEMENT_3D('',#2768,#2769,#2770);
#2768 = CARTESIAN_POINT('',(89.317494,112.30648,47.5));
#2769 = DIRECTION('',(-1.,-0.,-0.));
#2770 = DIRECTION('',(0.,-1.,0.));
#2771 = DEFINITIONAL_REPRESENTATION('',(#2772),#2776);
#2772 = LINE('',#2773,#2774);
#2773 = CARTESIAN_POINT('',(0.,0.));
#2774 = VECTOR('',#2775,1.);
#2775 = DIRECTION('',(0.,-1.));
#2776 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2777 = ORIENTED_EDGE('',*,*,#2778,.F.);
#2778 = EDGE_CURVE('',#2675,#2751,#2779,.T.);
#2779 = SURFACE_CURVE('',#2780,(#2784,#2791),.PCURVE_S1.);
#2780 = LINE('',#2781,#2782);
#2781 = CARTESIAN_POINT('',(72.917494,112.30648,47.5));
#2782 = VECTOR('',#2783,1.);
#2783 = DIRECTION('',(1.,0.,0.));
#2784 = PCURVE('',#2690,#2785);
#2785 = DEFINITIONAL_REPRESENTATION('',(#2786),#2790);
#2786 = LINE('',#2787,#2788);
#2787 = CARTESIAN_POINT('',(0.,0.));
#2788 = VECTOR('',#2789,1.);
#2789 = DIRECTION('',(1.,0.));
#2790 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2791 = PCURVE('',#2635,#2792);
#2792 = DEFINITIONAL_REPRESENTATION('',(#2793),#2797);
#2793 = LINE('',#2794,#2795);
#2794 = CARTESIAN_POINT('',(-0.285334422373,7.059841412461));
#2795 = VECTOR('',#2796,1.);
#2796 = DIRECTION('',(-1.,0.));
#2797 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2798 = ADVANCED_FACE('',(#2799),#2766,.F.);
#2799 = FACE_BOUND('',#2800,.F.);
#2800 = EDGE_LOOP('',(#2801,#2802,#2825,#2853));
#2801 = ORIENTED_EDGE('',*,*,#2750,.T.);
#2802 = ORIENTED_EDGE('',*,*,#2803,.T.);
#2803 = EDGE_CURVE('',#2728,#2804,#2806,.T.);
#2804 = VERTEX_POINT('',#2805);
#2805 = CARTESIAN_POINT('',(89.317494,98.606207,2.973447));
#2806 = SURFACE_CURVE('',#2807,(#2811,#2818),.PCURVE_S1.);
#2807 = LINE('',#2808,#2809);
#2808 = CARTESIAN_POINT('',(89.317494,112.30648,2.973447));
#2809 = VECTOR('',#2810,1.);
#2810 = DIRECTION('',(0.,-1.,0.));
#2811 = PCURVE('',#2766,#2812);
#2812 = DEFINITIONAL_REPRESENTATION('',(#2813),#2817);
#2813 = LINE('',#2814,#2815);
#2814 = CARTESIAN_POINT('',(0.,-44.526553));
#2815 = VECTOR('',#2816,1.);
#2816 = DIRECTION('',(1.,0.));
#2817 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2818 = PCURVE('',#2581,#2819);
#2819 = DEFINITIONAL_REPRESENTATION('',(#2820),#2824);
#2820 = LINE('',#2821,#2822);
#2821 = CARTESIAN_POINT('',(-16.68533442237,7.059841412461));
#2822 = VECTOR('',#2823,1.);
#2823 = DIRECTION('',(0.,-1.));
#2824 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2825 = ORIENTED_EDGE('',*,*,#2826,.F.);
#2826 = EDGE_CURVE('',#2827,#2804,#2829,.T.);
#2827 = VERTEX_POINT('',#2828);
#2828 = CARTESIAN_POINT('',(89.317494,98.606207,47.5));
#2829 = SURFACE_CURVE('',#2830,(#2834,#2841),.PCURVE_S1.);
#2830 = LINE('',#2831,#2832);
#2831 = CARTESIAN_POINT('',(89.317494,98.606207,47.5));
#2832 = VECTOR('',#2833,1.);
#2833 = DIRECTION('',(0.,0.,-1.));
#2834 = PCURVE('',#2766,#2835);
#2835 = DEFINITIONAL_REPRESENTATION('',(#2836),#2840);
#2836 = LINE('',#2837,#2838);
#2837 = CARTESIAN_POINT('',(13.700273,0.));
#2838 = VECTOR('',#2839,1.);
#2839 = DIRECTION('',(0.,-1.));
#2840 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2841 = PCURVE('',#2842,#2847);
#2842 = PLANE('',#2843);
#2843 = AXIS2_PLACEMENT_3D('',#2844,#2845,#2846);
#2844 = CARTESIAN_POINT('',(89.317494,98.606207,47.5));
#2845 = DIRECTION('',(-0.999970353247,7.700170607099E-03,0.));
#2846 = DIRECTION('',(-7.700170607099E-03,-0.999970353247,0.));
#2847 = DEFINITIONAL_REPRESENTATION('',(#2848),#2852);
#2848 = LINE('',#2849,#2850);
#2849 = CARTESIAN_POINT('',(0.,0.));
#2850 = VECTOR('',#2851,1.);
#2851 = DIRECTION('',(-0.,-1.));
#2852 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2853 = ORIENTED_EDGE('',*,*,#2854,.F.);
#2854 = EDGE_CURVE('',#2751,#2827,#2855,.T.);
#2855 = SURFACE_CURVE('',#2856,(#2860,#2867),.PCURVE_S1.);
#2856 = LINE('',#2857,#2858);
#2857 = CARTESIAN_POINT('',(89.317494,112.30648,47.5));
#2858 = VECTOR('',#2859,1.);
#2859 = DIRECTION('',(0.,-1.,0.));
#2860 = PCURVE('',#2766,#2861);
#2861 = DEFINITIONAL_REPRESENTATION('',(#2862),#2866);
#2862 = LINE('',#2863,#2864);
#2863 = CARTESIAN_POINT('',(0.,0.));
#2864 = VECTOR('',#2865,1.);
#2865 = DIRECTION('',(1.,0.));
#2866 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2867 = PCURVE('',#2635,#2868);
#2868 = DEFINITIONAL_REPRESENTATION('',(#2869),#2873);
#2869 = LINE('',#2870,#2871);
#2870 = CARTESIAN_POINT('',(-16.68533442237,7.059841412461));
#2871 = VECTOR('',#2872,1.);
#2872 = DIRECTION('',(0.,-1.));
#2873 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2874 = ADVANCED_FACE('',(#2875),#2842,.F.);
#2875 = FACE_BOUND('',#2876,.F.);
#2876 = EDGE_LOOP('',(#2877,#2878,#2901,#2929));
#2877 = ORIENTED_EDGE('',*,*,#2826,.T.);
#2878 = ORIENTED_EDGE('',*,*,#2879,.T.);
#2879 = EDGE_CURVE('',#2804,#2880,#2882,.T.);
#2880 = VERTEX_POINT('',#2881);
#2881 = CARTESIAN_POINT('',(89.283511,94.193059,2.973447));
#2882 = SURFACE_CURVE('',#2883,(#2887,#2894),.PCURVE_S1.);
#2883 = LINE('',#2884,#2885);
#2884 = CARTESIAN_POINT('',(89.317494,98.606207,2.973447));
#2885 = VECTOR('',#2886,1.);
#2886 = DIRECTION('',(-7.700170607099E-03,-0.999970353247,0.));
#2887 = PCURVE('',#2842,#2888);
#2888 = DEFINITIONAL_REPRESENTATION('',(#2889),#2893);
#2889 = LINE('',#2890,#2891);
#2890 = CARTESIAN_POINT('',(1.42104334095E-14,-44.526553));
#2891 = VECTOR('',#2892,1.);
#2892 = DIRECTION('',(1.,0.));
#2893 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2894 = PCURVE('',#2581,#2895);
#2895 = DEFINITIONAL_REPRESENTATION('',(#2896),#2900);
#2896 = LINE('',#2897,#2898);
#2897 = CARTESIAN_POINT('',(-16.68533442237,-6.640431587539));
#2898 = VECTOR('',#2899,1.);
#2899 = DIRECTION('',(7.700170607099E-03,-0.999970353247));
#2900 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2901 = ORIENTED_EDGE('',*,*,#2902,.F.);
#2902 = EDGE_CURVE('',#2903,#2880,#2905,.T.);
#2903 = VERTEX_POINT('',#2904);
#2904 = CARTESIAN_POINT('',(89.283511,94.193059,47.5));
#2905 = SURFACE_CURVE('',#2906,(#2910,#2917),.PCURVE_S1.);
#2906 = LINE('',#2907,#2908);
#2907 = CARTESIAN_POINT('',(89.283511,94.193059,47.5));
#2908 = VECTOR('',#2909,1.);
#2909 = DIRECTION('',(0.,0.,-1.));
#2910 = PCURVE('',#2842,#2911);
#2911 = DEFINITIONAL_REPRESENTATION('',(#2912),#2916);
#2912 = LINE('',#2913,#2914);
#2913 = CARTESIAN_POINT('',(4.413278839388,0.));
#2914 = VECTOR('',#2915,1.);
#2915 = DIRECTION('',(-0.,-1.));
#2916 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2917 = PCURVE('',#2918,#2923);
#2918 = PLANE('',#2919);
#2919 = AXIS2_PLACEMENT_3D('',#2920,#2921,#2922);
#2920 = CARTESIAN_POINT('',(89.283511,94.193059,47.5));
#2921 = DIRECTION('',(0.918584115575,0.395225533858,0.));
#2922 = DIRECTION('',(-0.395225533858,0.918584115575,0.));
#2923 = DEFINITIONAL_REPRESENTATION('',(#2924),#2928);
#2924 = LINE('',#2925,#2926);
#2925 = CARTESIAN_POINT('',(0.,0.));
#2926 = VECTOR('',#2927,1.);
#2927 = DIRECTION('',(0.,-1.));
#2928 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2929 = ORIENTED_EDGE('',*,*,#2930,.F.);
#2930 = EDGE_CURVE('',#2827,#2903,#2931,.T.);
#2931 = SURFACE_CURVE('',#2932,(#2936,#2943),.PCURVE_S1.);
#2932 = LINE('',#2933,#2934);
#2933 = CARTESIAN_POINT('',(89.317494,98.606207,47.5));
#2934 = VECTOR('',#2935,1.);
#2935 = DIRECTION('',(-7.700170607099E-03,-0.999970353247,0.));
#2936 = PCURVE('',#2842,#2937);
#2937 = DEFINITIONAL_REPRESENTATION('',(#2938),#2942);
#2938 = LINE('',#2939,#2940);
#2939 = CARTESIAN_POINT('',(1.42104334095E-14,0.));
#2940 = VECTOR('',#2941,1.);
#2941 = DIRECTION('',(1.,0.));
#2942 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2943 = PCURVE('',#2635,#2944);
#2944 = DEFINITIONAL_REPRESENTATION('',(#2945),#2949);
#2945 = LINE('',#2946,#2947);
#2946 = CARTESIAN_POINT('',(-16.68533442237,-6.640431587539));
#2947 = VECTOR('',#2948,1.);
#2948 = DIRECTION('',(7.700170607099E-03,-0.999970353247));
#2949 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2950 = ADVANCED_FACE('',(#2951),#2918,.F.);
#2951 = FACE_BOUND('',#2952,.F.);
#2952 = EDGE_LOOP('',(#2953,#2954,#2977,#3005));
#2953 = ORIENTED_EDGE('',*,*,#2902,.T.);
#2954 = ORIENTED_EDGE('',*,*,#2955,.T.);
#2955 = EDGE_CURVE('',#2880,#2956,#2958,.T.);
#2956 = VERTEX_POINT('',#2957);
#2957 = CARTESIAN_POINT('',(88.214367,96.677966,2.973447));
#2958 = SURFACE_CURVE('',#2959,(#2963,#2970),.PCURVE_S1.);
#2959 = LINE('',#2960,#2961);
#2960 = CARTESIAN_POINT('',(89.283511,94.193059,2.973447));
#2961 = VECTOR('',#2962,1.);
#2962 = DIRECTION('',(-0.395225533858,0.918584115575,0.));
#2963 = PCURVE('',#2918,#2964);
#2964 = DEFINITIONAL_REPRESENTATION('',(#2965),#2969);
#2965 = LINE('',#2966,#2967);
#2966 = CARTESIAN_POINT('',(0.,-44.526553));
#2967 = VECTOR('',#2968,1.);
#2968 = DIRECTION('',(1.,0.));
#2969 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2970 = PCURVE('',#2581,#2971);
#2971 = DEFINITIONAL_REPRESENTATION('',(#2972),#2976);
#2972 = LINE('',#2973,#2974);
#2973 = CARTESIAN_POINT('',(-16.65135142237,-11.05357958753));
#2974 = VECTOR('',#2975,1.);
#2975 = DIRECTION('',(0.395225533858,0.918584115575));
#2976 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2977 = ORIENTED_EDGE('',*,*,#2978,.F.);
#2978 = EDGE_CURVE('',#2979,#2956,#2981,.T.);
#2979 = VERTEX_POINT('',#2980);
#2980 = CARTESIAN_POINT('',(88.214367,96.677966,47.5));
#2981 = SURFACE_CURVE('',#2982,(#2986,#2993),.PCURVE_S1.);
#2982 = LINE('',#2983,#2984);
#2983 = CARTESIAN_POINT('',(88.214367,96.677966,47.5));
#2984 = VECTOR('',#2985,1.);
#2985 = DIRECTION('',(0.,0.,-1.));
#2986 = PCURVE('',#2918,#2987);
#2987 = DEFINITIONAL_REPRESENTATION('',(#2988),#2992);
#2988 = LINE('',#2989,#2990);
#2989 = CARTESIAN_POINT('',(2.705149107052,0.));
#2990 = VECTOR('',#2991,1.);
#2991 = DIRECTION('',(0.,-1.));
#2992 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2993 = PCURVE('',#2994,#2999);
#2994 = PLANE('',#2995);
#2995 = AXIS2_PLACEMENT_3D('',#2996,#2997,#2998);
#2996 = CARTESIAN_POINT('',(88.214367,96.677966,47.5));
#2997 = DIRECTION('',(-6.761582055227E-02,0.997711431633,0.));
#2998 = DIRECTION('',(-0.997711431633,-6.761582055227E-02,0.));
#2999 = DEFINITIONAL_REPRESENTATION('',(#3000),#3004);
#3000 = LINE('',#3001,#3002);
#3001 = CARTESIAN_POINT('',(0.,0.));
#3002 = VECTOR('',#3003,1.);
#3003 = DIRECTION('',(-0.,-1.));
#3004 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3005 = ORIENTED_EDGE('',*,*,#3006,.F.);
#3006 = EDGE_CURVE('',#2903,#2979,#3007,.T.);
#3007 = SURFACE_CURVE('',#3008,(#3012,#3019),.PCURVE_S1.);
#3008 = LINE('',#3009,#3010);
#3009 = CARTESIAN_POINT('',(89.283511,94.193059,47.5));
#3010 = VECTOR('',#3011,1.);
#3011 = DIRECTION('',(-0.395225533858,0.918584115575,0.));
#3012 = PCURVE('',#2918,#3013);
#3013 = DEFINITIONAL_REPRESENTATION('',(#3014),#3018);
#3014 = LINE('',#3015,#3016);
#3015 = CARTESIAN_POINT('',(0.,0.));
#3016 = VECTOR('',#3017,1.);
#3017 = DIRECTION('',(1.,0.));
#3018 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3019 = PCURVE('',#2635,#3020);
#3020 = DEFINITIONAL_REPRESENTATION('',(#3021),#3025);
#3021 = LINE('',#3022,#3023);
#3022 = CARTESIAN_POINT('',(-16.65135142237,-11.05357958753));
#3023 = VECTOR('',#3024,1.);
#3024 = DIRECTION('',(0.395225533858,0.918584115575));
#3025 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3026 = ADVANCED_FACE('',(#3027),#2994,.F.);
#3027 = FACE_BOUND('',#3028,.F.);
#3028 = EDGE_LOOP('',(#3029,#3030,#3053,#3081));
#3029 = ORIENTED_EDGE('',*,*,#2978,.T.);
#3030 = ORIENTED_EDGE('',*,*,#3031,.T.);
#3031 = EDGE_CURVE('',#2956,#3032,#3034,.T.);
#3032 = VERTEX_POINT('',#3033);
#3033 = CARTESIAN_POINT('',(66.252779,95.189609,2.973447));
#3034 = SURFACE_CURVE('',#3035,(#3039,#3046),.PCURVE_S1.);
#3035 = LINE('',#3036,#3037);
#3036 = CARTESIAN_POINT('',(88.214367,96.677966,2.973447));
#3037 = VECTOR('',#3038,1.);
#3038 = DIRECTION('',(-0.997711431633,-6.761582055227E-02,0.));
#3039 = PCURVE('',#2994,#3040);
#3040 = DEFINITIONAL_REPRESENTATION('',(#3041),#3045);
#3041 = LINE('',#3042,#3043);
#3042 = CARTESIAN_POINT('',(1.417833220263E-14,-44.526553));
#3043 = VECTOR('',#3044,1.);
#3044 = DIRECTION('',(1.,0.));
#3045 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3046 = PCURVE('',#2581,#3047);
#3047 = DEFINITIONAL_REPRESENTATION('',(#3048),#3052);
#3048 = LINE('',#3049,#3050);
#3049 = CARTESIAN_POINT('',(-15.58220742237,-8.568672587539));
#3050 = VECTOR('',#3051,1.);
#3051 = DIRECTION('',(0.997711431633,-6.761582055227E-02));
#3052 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3053 = ORIENTED_EDGE('',*,*,#3054,.F.);
#3054 = EDGE_CURVE('',#3055,#3032,#3057,.T.);
#3055 = VERTEX_POINT('',#3056);
#3056 = CARTESIAN_POINT('',(66.252779,95.189609,47.5));
#3057 = SURFACE_CURVE('',#3058,(#3062,#3069),.PCURVE_S1.);
#3058 = LINE('',#3059,#3060);
#3059 = CARTESIAN_POINT('',(66.252779,95.189609,47.5));
#3060 = VECTOR('',#3061,1.);
#3061 = DIRECTION('',(0.,0.,-1.));
#3062 = PCURVE('',#2994,#3063);
#3063 = DEFINITIONAL_REPRESENTATION('',(#3064),#3068);
#3064 = LINE('',#3065,#3066);
#3065 = CARTESIAN_POINT('',(22.011963884242,0.));
#3066 = VECTOR('',#3067,1.);
#3067 = DIRECTION('',(-0.,-1.));
#3068 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3069 = PCURVE('',#3070,#3075);
#3070 = PLANE('',#3071);
#3071 = AXIS2_PLACEMENT_3D('',#3072,#3073,#3074);
#3072 = CARTESIAN_POINT('',(66.252779,95.189609,47.5));
#3073 = DIRECTION('',(0.999268509488,3.824193960457E-02,0.));
#3074 = DIRECTION('',(-3.824193960457E-02,0.999268509488,0.));
#3075 = DEFINITIONAL_REPRESENTATION('',(#3076),#3080);
#3076 = LINE('',#3077,#3078);
#3077 = CARTESIAN_POINT('',(0.,0.));
#3078 = VECTOR('',#3079,1.);
#3079 = DIRECTION('',(0.,-1.));
#3080 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3081 = ORIENTED_EDGE('',*,*,#3082,.F.);
#3082 = EDGE_CURVE('',#2979,#3055,#3083,.T.);
#3083 = SURFACE_CURVE('',#3084,(#3088,#3095),.PCURVE_S1.);
#3084 = LINE('',#3085,#3086);
#3085 = CARTESIAN_POINT('',(88.214367,96.677966,47.5));
#3086 = VECTOR('',#3087,1.);
#3087 = DIRECTION('',(-0.997711431633,-6.761582055227E-02,0.));
#3088 = PCURVE('',#2994,#3089);
#3089 = DEFINITIONAL_REPRESENTATION('',(#3090),#3094);
#3090 = LINE('',#3091,#3092);
#3091 = CARTESIAN_POINT('',(1.417833220263E-14,0.));
#3092 = VECTOR('',#3093,1.);
#3093 = DIRECTION('',(1.,0.));
#3094 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3095 = PCURVE('',#2635,#3096);
#3096 = DEFINITIONAL_REPRESENTATION('',(#3097),#3101);
#3097 = LINE('',#3098,#3099);
#3098 = CARTESIAN_POINT('',(-15.58220742237,-8.568672587539));
#3099 = VECTOR('',#3100,1.);
#3100 = DIRECTION('',(0.997711431633,-6.761582055227E-02));
#3101 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3102 = ADVANCED_FACE('',(#3103),#3070,.F.);
#3103 = FACE_BOUND('',#3104,.F.);
#3104 = EDGE_LOOP('',(#3105,#3106,#3129,#3157));
#3105 = ORIENTED_EDGE('',*,*,#3054,.T.);
#3106 = ORIENTED_EDGE('',*,*,#3107,.T.);
#3107 = EDGE_CURVE('',#3032,#3108,#3110,.T.);
#3108 = VERTEX_POINT('',#3109);
#3109 = CARTESIAN_POINT('',(66.084004,99.599729,2.973447));
#3110 = SURFACE_CURVE('',#3111,(#3115,#3122),.PCURVE_S1.);
#3111 = LINE('',#3112,#3113);
#3112 = CARTESIAN_POINT('',(66.252779,95.189609,2.973447));
#3113 = VECTOR('',#3114,1.);
#3114 = DIRECTION('',(-3.824193960457E-02,0.999268509488,0.));
#3115 = PCURVE('',#3070,#3116);
#3116 = DEFINITIONAL_REPRESENTATION('',(#3117),#3121);
#3117 = LINE('',#3118,#3119);
#3118 = CARTESIAN_POINT('',(1.420045960982E-14,-44.526553));
#3119 = VECTOR('',#3120,1.);
#3120 = DIRECTION('',(1.,0.));
#3121 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3122 = PCURVE('',#2581,#3123);
#3123 = DEFINITIONAL_REPRESENTATION('',(#3124),#3128);
#3124 = LINE('',#3125,#3126);
#3125 = CARTESIAN_POINT('',(6.379380577627,-10.05702958753));
#3126 = VECTOR('',#3127,1.);
#3127 = DIRECTION('',(3.824193960457E-02,0.999268509488));
#3128 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3129 = ORIENTED_EDGE('',*,*,#3130,.F.);
#3130 = EDGE_CURVE('',#3131,#3108,#3133,.T.);
#3131 = VERTEX_POINT('',#3132);
#3132 = CARTESIAN_POINT('',(66.084004,99.599729,47.5));
#3133 = SURFACE_CURVE('',#3134,(#3138,#3145),.PCURVE_S1.);
#3134 = LINE('',#3135,#3136);
#3135 = CARTESIAN_POINT('',(66.084004,99.599729,47.5));
#3136 = VECTOR('',#3137,1.);
#3137 = DIRECTION('',(0.,0.,-1.));
#3138 = PCURVE('',#3070,#3139);
#3139 = DEFINITIONAL_REPRESENTATION('',(#3140),#3144);
#3140 = LINE('',#3141,#3142);
#3141 = CARTESIAN_POINT('',(4.413348322422,0.));
#3142 = VECTOR('',#3143,1.);
#3143 = DIRECTION('',(0.,-1.));
#3144 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3145 = PCURVE('',#3146,#3151);
#3146 = PLANE('',#3147);
#3147 = AXIS2_PLACEMENT_3D('',#3148,#3149,#3150);
#3148 = CARTESIAN_POINT('',(66.084004,99.599729,47.5));
#3149 = DIRECTION('',(0.473439417635,0.880826383477,0.));
#3150 = DIRECTION('',(-0.880826383477,0.473439417635,0.));
#3151 = DEFINITIONAL_REPRESENTATION('',(#3152),#3156);
#3152 = LINE('',#3153,#3154);
#3153 = CARTESIAN_POINT('',(0.,0.));
#3154 = VECTOR('',#3155,1.);
#3155 = DIRECTION('',(0.,-1.));
#3156 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3157 = ORIENTED_EDGE('',*,*,#3158,.F.);
#3158 = EDGE_CURVE('',#3055,#3131,#3159,.T.);
#3159 = SURFACE_CURVE('',#3160,(#3164,#3171),.PCURVE_S1.);
#3160 = LINE('',#3161,#3162);
#3161 = CARTESIAN_POINT('',(66.252779,95.189609,47.5));
#3162 = VECTOR('',#3163,1.);
#3163 = DIRECTION('',(-3.824193960457E-02,0.999268509488,0.));
#3164 = PCURVE('',#3070,#3165);
#3165 = DEFINITIONAL_REPRESENTATION('',(#3166),#3170);
#3166 = LINE('',#3167,#3168);
#3167 = CARTESIAN_POINT('',(1.420045960982E-14,0.));
#3168 = VECTOR('',#3169,1.);
#3169 = DIRECTION('',(1.,0.));
#3170 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3171 = PCURVE('',#2635,#3172);
#3172 = DEFINITIONAL_REPRESENTATION('',(#3173),#3177);
#3173 = LINE('',#3174,#3175);
#3174 = CARTESIAN_POINT('',(6.379380577627,-10.05702958753));
#3175 = VECTOR('',#3176,1.);
#3176 = DIRECTION('',(3.824193960457E-02,0.999268509488));
#3177 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3178 = ADVANCED_FACE('',(#3179),#3146,.F.);
#3179 = FACE_BOUND('',#3180,.F.);
#3180 = EDGE_LOOP('',(#3181,#3182,#3205,#3233));
#3181 = ORIENTED_EDGE('',*,*,#3130,.T.);
#3182 = ORIENTED_EDGE('',*,*,#3183,.T.);
#3183 = EDGE_CURVE('',#3108,#3184,#3186,.T.);
#3184 = VERTEX_POINT('',#3185);
#3185 = CARTESIAN_POINT('',(59.910247,102.91809,2.973447));
#3186 = SURFACE_CURVE('',#3187,(#3191,#3198),.PCURVE_S1.);
#3187 = LINE('',#3188,#3189);
#3188 = CARTESIAN_POINT('',(66.084004,99.599729,2.973447));
#3189 = VECTOR('',#3190,1.);
#3190 = DIRECTION('',(-0.880826383477,0.473439417635,0.));
#3191 = PCURVE('',#3146,#3192);
#3192 = DEFINITIONAL_REPRESENTATION('',(#3193),#3197);
#3193 = LINE('',#3194,#3195);
#3194 = CARTESIAN_POINT('',(0.,-44.526553));
#3195 = VECTOR('',#3196,1.);
#3196 = DIRECTION('',(1.,0.));
#3197 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3198 = PCURVE('',#2581,#3199);
#3199 = DEFINITIONAL_REPRESENTATION('',(#3200),#3204);
#3200 = LINE('',#3201,#3202);
#3201 = CARTESIAN_POINT('',(6.548155577627,-5.646909587539));
#3202 = VECTOR('',#3203,1.);
#3203 = DIRECTION('',(0.880826383477,0.473439417635));
#3204 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3205 = ORIENTED_EDGE('',*,*,#3206,.F.);
#3206 = EDGE_CURVE('',#3207,#3184,#3209,.T.);
#3207 = VERTEX_POINT('',#3208);
#3208 = CARTESIAN_POINT('',(59.910247,102.91809,47.5));
#3209 = SURFACE_CURVE('',#3210,(#3214,#3221),.PCURVE_S1.);
#3210 = LINE('',#3211,#3212);
#3211 = CARTESIAN_POINT('',(59.910247,102.91809,47.5));
#3212 = VECTOR('',#3213,1.);
#3213 = DIRECTION('',(0.,0.,-1.));
#3214 = PCURVE('',#3146,#3215);
#3215 = DEFINITIONAL_REPRESENTATION('',(#3216),#3220);
#3216 = LINE('',#3217,#3218);
#3217 = CARTESIAN_POINT('',(7.009050950119,0.));
#3218 = VECTOR('',#3219,1.);
#3219 = DIRECTION('',(0.,-1.));
#3220 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3221 = PCURVE('',#3222,#3227);
#3222 = PLANE('',#3223);
#3223 = AXIS2_PLACEMENT_3D('',#3224,#3225,#3226);
#3224 = CARTESIAN_POINT('',(59.910247,102.91809,47.5));
#3225 = DIRECTION('',(0.999261289289,3.843014089773E-02,0.));
#3226 = DIRECTION('',(-3.843014089773E-02,0.999261289289,0.));
#3227 = DEFINITIONAL_REPRESENTATION('',(#3228),#3232);
#3228 = LINE('',#3229,#3230);
#3229 = CARTESIAN_POINT('',(0.,0.));
#3230 = VECTOR('',#3231,1.);
#3231 = DIRECTION('',(0.,-1.));
#3232 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3233 = ORIENTED_EDGE('',*,*,#3234,.F.);
#3234 = EDGE_CURVE('',#3131,#3207,#3235,.T.);
#3235 = SURFACE_CURVE('',#3236,(#3240,#3247),.PCURVE_S1.);
#3236 = LINE('',#3237,#3238);
#3237 = CARTESIAN_POINT('',(66.084004,99.599729,47.5));
#3238 = VECTOR('',#3239,1.);
#3239 = DIRECTION('',(-0.880826383477,0.473439417635,0.));
#3240 = PCURVE('',#3146,#3241);
#3241 = DEFINITIONAL_REPRESENTATION('',(#3242),#3246);
#3242 = LINE('',#3243,#3244);
#3243 = CARTESIAN_POINT('',(0.,0.));
#3244 = VECTOR('',#3245,1.);
#3245 = DIRECTION('',(1.,0.));
#3246 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3247 = PCURVE('',#2635,#3248);
#3248 = DEFINITIONAL_REPRESENTATION('',(#3249),#3253);
#3249 = LINE('',#3250,#3251);
#3250 = CARTESIAN_POINT('',(6.548155577627,-5.646909587539));
#3251 = VECTOR('',#3252,1.);
#3252 = DIRECTION('',(0.880826383477,0.473439417635));
#3253 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3254 = ADVANCED_FACE('',(#3255),#3222,.F.);
#3255 = FACE_BOUND('',#3256,.F.);
#3256 = EDGE_LOOP('',(#3257,#3258,#3281,#3309));
#3257 = ORIENTED_EDGE('',*,*,#3206,.T.);
#3258 = ORIENTED_EDGE('',*,*,#3259,.T.);
#3259 = EDGE_CURVE('',#3184,#3260,#3262,.T.);
#3260 = VERTEX_POINT('',#3261);
#3261 = CARTESIAN_POINT('',(59.699843,108.389019,2.973447));
#3262 = SURFACE_CURVE('',#3263,(#3267,#3274),.PCURVE_S1.);
#3263 = LINE('',#3264,#3265);
#3264 = CARTESIAN_POINT('',(59.910247,102.91809,2.973447));
#3265 = VECTOR('',#3266,1.);
#3266 = DIRECTION('',(-3.843014089773E-02,0.999261289289,0.));
#3267 = PCURVE('',#3222,#3268);
#3268 = DEFINITIONAL_REPRESENTATION('',(#3269),#3273);
#3269 = LINE('',#3270,#3271);
#3270 = CARTESIAN_POINT('',(0.,-44.526553));
#3271 = VECTOR('',#3272,1.);
#3272 = DIRECTION('',(1.,0.));
#3273 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3274 = PCURVE('',#2581,#3275);
#3275 = DEFINITIONAL_REPRESENTATION('',(#3276),#3280);
#3276 = LINE('',#3277,#3278);
#3277 = CARTESIAN_POINT('',(12.721912577627,-2.328548587539));
#3278 = VECTOR('',#3279,1.);
#3279 = DIRECTION('',(3.843014089773E-02,0.999261289289));
#3280 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3281 = ORIENTED_EDGE('',*,*,#3282,.F.);
#3282 = EDGE_CURVE('',#3283,#3260,#3285,.T.);
#3283 = VERTEX_POINT('',#3284);
#3284 = CARTESIAN_POINT('',(59.699843,108.389019,47.5));
#3285 = SURFACE_CURVE('',#3286,(#3290,#3297),.PCURVE_S1.);
#3286 = LINE('',#3287,#3288);
#3287 = CARTESIAN_POINT('',(59.699843,108.389019,47.5));
#3288 = VECTOR('',#3289,1.);
#3289 = DIRECTION('',(0.,0.,-1.));
#3290 = PCURVE('',#3222,#3291);
#3291 = DEFINITIONAL_REPRESENTATION('',(#3292),#3296);
#3292 = LINE('',#3293,#3294);
#3293 = CARTESIAN_POINT('',(5.474973421511,0.));
#3294 = VECTOR('',#3295,1.);
#3295 = DIRECTION('',(0.,-1.));
#3296 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3297 = PCURVE('',#3298,#3303);
#3298 = PLANE('',#3299);
#3299 = AXIS2_PLACEMENT_3D('',#3300,#3301,#3302);
#3300 = CARTESIAN_POINT('',(59.699843,108.389019,47.5));
#3301 = DIRECTION('',(-5.095482832812E-02,0.998700958981,0.));
#3302 = DIRECTION('',(-0.998700958981,-5.095482832812E-02,0.));
#3303 = DEFINITIONAL_REPRESENTATION('',(#3304),#3308);
#3304 = LINE('',#3305,#3306);
#3305 = CARTESIAN_POINT('',(0.,0.));
#3306 = VECTOR('',#3307,1.);
#3307 = DIRECTION('',(-0.,-1.));
#3308 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3309 = ORIENTED_EDGE('',*,*,#3310,.F.);
#3310 = EDGE_CURVE('',#3207,#3283,#3311,.T.);
#3311 = SURFACE_CURVE('',#3312,(#3316,#3323),.PCURVE_S1.);
#3312 = LINE('',#3313,#3314);
#3313 = CARTESIAN_POINT('',(59.910247,102.91809,47.5));
#3314 = VECTOR('',#3315,1.);
#3315 = DIRECTION('',(-3.843014089773E-02,0.999261289289,0.));
#3316 = PCURVE('',#3222,#3317);
#3317 = DEFINITIONAL_REPRESENTATION('',(#3318),#3322);
#3318 = LINE('',#3319,#3320);
#3319 = CARTESIAN_POINT('',(0.,0.));
#3320 = VECTOR('',#3321,1.);
#3321 = DIRECTION('',(1.,0.));
#3322 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3323 = PCURVE('',#2635,#3324);
#3324 = DEFINITIONAL_REPRESENTATION('',(#3325),#3329);
#3325 = LINE('',#3326,#3327);
#3326 = CARTESIAN_POINT('',(12.721912577627,-2.328548587539));
#3327 = VECTOR('',#3328,1.);
#3328 = DIRECTION('',(3.843014089773E-02,0.999261289289));
#3329 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3330 = ADVANCED_FACE('',(#3331),#3298,.F.);
#3331 = FACE_BOUND('',#3332,.F.);
#3332 = EDGE_LOOP('',(#3333,#3334,#3357,#3380));
#3333 = ORIENTED_EDGE('',*,*,#3282,.T.);
#3334 = ORIENTED_EDGE('',*,*,#3335,.T.);
#3335 = EDGE_CURVE('',#3260,#3336,#3338,.T.);
#3336 = VERTEX_POINT('',#3337);
#3337 = CARTESIAN_POINT('',(53.594997,108.077543,2.973447));
#3338 = SURFACE_CURVE('',#3339,(#3343,#3350),.PCURVE_S1.);
#3339 = LINE('',#3340,#3341);
#3340 = CARTESIAN_POINT('',(59.699843,108.389019,2.973447));
#3341 = VECTOR('',#3342,1.);
#3342 = DIRECTION('',(-0.998700958981,-5.095482832812E-02,0.));
#3343 = PCURVE('',#3298,#3344);
#3344 = DEFINITIONAL_REPRESENTATION('',(#3345),#3349);
#3345 = LINE('',#3346,#3347);
#3346 = CARTESIAN_POINT('',(1.419239423202E-14,-44.526553));
#3347 = VECTOR('',#3348,1.);
#3348 = DIRECTION('',(1.,0.));
#3349 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3350 = PCURVE('',#2581,#3351);
#3351 = DEFINITIONAL_REPRESENTATION('',(#3352),#3356);
#3352 = LINE('',#3353,#3354);
#3353 = CARTESIAN_POINT('',(12.932316577627,3.142380412461));
#3354 = VECTOR('',#3355,1.);
#3355 = DIRECTION('',(0.998700958981,-5.095482832812E-02));
#3356 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3357 = ORIENTED_EDGE('',*,*,#3358,.F.);
#3358 = EDGE_CURVE('',#3359,#3336,#3361,.T.);
#3359 = VERTEX_POINT('',#3360);
#3360 = CARTESIAN_POINT('',(53.594997,108.077543,47.5));
#3361 = SURFACE_CURVE('',#3362,(#3366,#3373),.PCURVE_S1.);
#3362 = LINE('',#3363,#3364);
#3363 = CARTESIAN_POINT('',(53.594997,108.077543,47.5));
#3364 = VECTOR('',#3365,1.);
#3365 = DIRECTION('',(0.,0.,-1.));
#3366 = PCURVE('',#3298,#3367);
#3367 = DEFINITIONAL_REPRESENTATION('',(#3368),#3372);
#3368 = LINE('',#3369,#3370);
#3369 = CARTESIAN_POINT('',(6.112786760741,0.));
#3370 = VECTOR('',#3371,1.);
#3371 = DIRECTION('',(-0.,-1.));
#3372 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3373 = PCURVE('',#2553,#3374);
#3374 = DEFINITIONAL_REPRESENTATION('',(#3375),#3379);
#3375 = LINE('',#3376,#3377);
#3376 = CARTESIAN_POINT('',(0.,0.));
#3377 = VECTOR('',#3378,1.);
#3378 = DIRECTION('',(0.,-1.));
#3379 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3380 = ORIENTED_EDGE('',*,*,#3381,.F.);
#3381 = EDGE_CURVE('',#3283,#3359,#3382,.T.);
#3382 = SURFACE_CURVE('',#3383,(#3387,#3394),.PCURVE_S1.);
#3383 = LINE('',#3384,#3385);
#3384 = CARTESIAN_POINT('',(59.699843,108.389019,47.5));
#3385 = VECTOR('',#3386,1.);
#3386 = DIRECTION('',(-0.998700958981,-5.095482832812E-02,0.));
#3387 = PCURVE('',#3298,#3388);
#3388 = DEFINITIONAL_REPRESENTATION('',(#3389),#3393);
#3389 = LINE('',#3390,#3391);
#3390 = CARTESIAN_POINT('',(1.419239423202E-14,0.));
#3391 = VECTOR('',#3392,1.);
#3392 = DIRECTION('',(1.,0.));
#3393 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3394 = PCURVE('',#2635,#3395);
#3395 = DEFINITIONAL_REPRESENTATION('',(#3396),#3400);
#3396 = LINE('',#3397,#3398);
#3397 = CARTESIAN_POINT('',(12.932316577627,3.142380412461));
#3398 = VECTOR('',#3399,1.);
#3399 = DIRECTION('',(0.998700958981,-5.095482832812E-02));
#3400 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3401 = ADVANCED_FACE('',(#3402),#2553,.F.);
#3402 = FACE_BOUND('',#3403,.F.);
#3403 = EDGE_LOOP('',(#3404,#3405,#3426,#3427));
#3404 = ORIENTED_EDGE('',*,*,#3358,.T.);
#3405 = ORIENTED_EDGE('',*,*,#3406,.T.);
#3406 = EDGE_CURVE('',#3336,#2533,#3407,.T.);
#3407 = SURFACE_CURVE('',#3408,(#3412,#3419),.PCURVE_S1.);
#3408 = LINE('',#3409,#3410);
#3409 = CARTESIAN_POINT('',(53.594997,108.077543,2.973447));
#3410 = VECTOR('',#3411,1.);
#3411 = DIRECTION('',(-0.363494675443,0.931596275714,0.));
#3412 = PCURVE('',#2553,#3413);
#3413 = DEFINITIONAL_REPRESENTATION('',(#3414),#3418);
#3414 = LINE('',#3415,#3416);
#3415 = CARTESIAN_POINT('',(-1.582156433863E-14,-44.526553));
#3416 = VECTOR('',#3417,1.);
#3417 = DIRECTION('',(1.,0.));
#3418 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3419 = PCURVE('',#2581,#3420);
#3420 = DEFINITIONAL_REPRESENTATION('',(#3421),#3425);
#3421 = LINE('',#3422,#3423);
#3422 = CARTESIAN_POINT('',(19.037162577627,2.830904412461));
#3423 = VECTOR('',#3424,1.);
#3424 = DIRECTION('',(0.363494675443,0.931596275714));
#3425 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3426 = ORIENTED_EDGE('',*,*,#2530,.F.);
#3427 = ORIENTED_EDGE('',*,*,#3428,.F.);
#3428 = EDGE_CURVE('',#3359,#2531,#3429,.T.);
#3429 = SURFACE_CURVE('',#3430,(#3434,#3441),.PCURVE_S1.);
#3430 = LINE('',#3431,#3432);
#3431 = CARTESIAN_POINT('',(53.594997,108.077543,47.5));
#3432 = VECTOR('',#3433,1.);
#3433 = DIRECTION('',(-0.363494675443,0.931596275714,0.));
#3434 = PCURVE('',#2553,#3435);
#3435 = DEFINITIONAL_REPRESENTATION('',(#3436),#3440);
#3436 = LINE('',#3437,#3438);
#3437 = CARTESIAN_POINT('',(-1.582156433863E-14,0.));
#3438 = VECTOR('',#3439,1.);
#3439 = DIRECTION('',(1.,0.));
#3440 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3441 = PCURVE('',#2635,#3442);
#3442 = DEFINITIONAL_REPRESENTATION('',(#3443),#3447);
#3443 = LINE('',#3444,#3445);
#3444 = CARTESIAN_POINT('',(19.037162577627,2.830904412461));
#3445 = VECTOR('',#3446,1.);
#3446 = DIRECTION('',(0.363494675443,0.931596275714));
#3447 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3448 = ADVANCED_FACE('',(#3449),#2635,.F.);
#3449 = FACE_BOUND('',#3450,.F.);
#3450 = EDGE_LOOP('',(#3451,#3452,#3453,#3454,#3455,#3456,#3457,#3458,
    #3459,#3460,#3461,#3462));
#3451 = ORIENTED_EDGE('',*,*,#2621,.T.);
#3452 = ORIENTED_EDGE('',*,*,#2702,.T.);
#3453 = ORIENTED_EDGE('',*,*,#2778,.T.);
#3454 = ORIENTED_EDGE('',*,*,#2854,.T.);
#3455 = ORIENTED_EDGE('',*,*,#2930,.T.);
#3456 = ORIENTED_EDGE('',*,*,#3006,.T.);
#3457 = ORIENTED_EDGE('',*,*,#3082,.T.);
#3458 = ORIENTED_EDGE('',*,*,#3158,.T.);
#3459 = ORIENTED_EDGE('',*,*,#3234,.T.);
#3460 = ORIENTED_EDGE('',*,*,#3310,.T.);
#3461 = ORIENTED_EDGE('',*,*,#3381,.T.);
#3462 = ORIENTED_EDGE('',*,*,#3428,.T.);
#3463 = ADVANCED_FACE('',(#3464),#2581,.T.);
#3464 = FACE_BOUND('',#3465,.T.);
#3465 = EDGE_LOOP('',(#3466,#3467,#3468,#3469,#3470,#3471,#3472,#3473,
    #3474,#3475,#3476,#3477));
#3466 = ORIENTED_EDGE('',*,*,#2565,.T.);
#3467 = ORIENTED_EDGE('',*,*,#2651,.T.);
#3468 = ORIENTED_EDGE('',*,*,#2727,.T.);
#3469 = ORIENTED_EDGE('',*,*,#2803,.T.);
#3470 = ORIENTED_EDGE('',*,*,#2879,.T.);
#3471 = ORIENTED_EDGE('',*,*,#2955,.T.);
#3472 = ORIENTED_EDGE('',*,*,#3031,.T.);
#3473 = ORIENTED_EDGE('',*,*,#3107,.T.);
#3474 = ORIENTED_EDGE('',*,*,#3183,.T.);
#3475 = ORIENTED_EDGE('',*,*,#3259,.T.);
#3476 = ORIENTED_EDGE('',*,*,#3335,.T.);
#3477 = ORIENTED_EDGE('',*,*,#3406,.T.);
#3478 = ( GEOMETRIC_REPRESENTATION_CONTEXT(3) 
GLOBAL_UNCERTAINTY_ASSIGNED_CONTEXT((#3482)) 
GLOBAL_UNIT_ASSIGNED_CONTEXT((#3479,#3480,#3481)) REPRESENTATION_CONTEXT
('Context #1','3D Context with UNIT and UNCERTAINTY') );
#3479 = ( LENGTH_UNIT() NAMED_UNIT(*) SI_UNIT(.MILLI.,.METRE.) );
#3480 = ( NAMED_UNIT(*) PLANE_ANGLE_UNIT() SI_UNIT($,.RADIAN.) );
#3481 = ( NAMED_UNIT(*) SI_UNIT($,.STERADIAN.) SOLID_ANGLE_UNIT() );
#3482 = UNCERTAINTY_MEASURE_WITH_UNIT(LENGTH_MEASURE(1.E-07),#3479,
  'distance_accuracy_value','confusion accuracy');
#3483 = CONTEXT_DEPENDENT_SHAPE_REPRESENTATION(#3484,#3486);
#3484 = ( REPRESENTATION_RELATIONSHIP('','',#2523,#2500) 
REPRESENTATION_RELATIONSHIP_WITH_TRANSFORMATION(#3485) 
SHAPE_REPRESENTATION_RELATIONSHIP() );
#3485 = ITEM_DEFINED_TRANSFORMATION('','',#11,#2501);
#3486 = PRODUCT_DEFINITION_SHAPE('Placement','Placement of an item',
  #3487);
#3487 = NEXT_ASSEMBLY_USAGE_OCCURRENCE('217','=>[0:1:1:7]','',#2495,
  #2518,$);
#3488 = PRODUCT_RELATED_PRODUCT_CATEGORY('part',$,(#2520));
#3489 = MECHANICAL_DESIGN_GEOMETRIC_PRESENTATION_REPRESENTATION('',(
    #3490),#2482);
#3490 = STYLED_ITEM('color',(#3491),#1216);
#3491 = PRESENTATION_STYLE_ASSIGNMENT((#3492));
#3492 = SURFACE_STYLE_USAGE(.BOTH.,#3493);
#3493 = SURFACE_SIDE_STYLE('',(#3494));
#3494 = SURFACE_STYLE_FILL_AREA(#3495);
#3495 = FILL_AREA_STYLE('',(#3496));
#3496 = FILL_AREA_STYLE_COLOUR('',#3497);
#3497 = DRAUGHTING_PRE_DEFINED_COLOUR('cyan');
#3498 = MECHANICAL_DESIGN_GEOMETRIC_PRESENTATION_REPRESENTATION('',(
    #3499),#1174);
#3499 = STYLED_ITEM('color',(#3500),#64);
#3500 = PRESENTATION_STYLE_ASSIGNMENT((#3501));
#3501 = SURFACE_STYLE_USAGE(.BOTH.,#3502);
#3502 = SURFACE_SIDE_STYLE('',(#3503));
#3503 = SURFACE_STYLE_FILL_AREA(#3504);
#3504 = FILL_AREA_STYLE('',(#3505));
#3505 = FILL_AREA_STYLE_COLOUR('',#3497);
#3506 = MECHANICAL_DESIGN_GEOMETRIC_PRESENTATION_REPRESENTATION('',(
    #3507),#3478);
#3507 = STYLED_ITEM('color',(#3508),#2524);
#3508 = PRESENTATION_STYLE_ASSIGNMENT((#3509));
#3509 = SURFACE_STYLE_USAGE(.BOTH.,#3510);
#3510 = SURFACE_SIDE_STYLE('',(#3511));
#3511 = SURFACE_STYLE_FILL_AREA(#3512);
#3512 = FILL_AREA_STYLE('',(#3513));
#3513 = FILL_AREA_STYLE_COLOUR('',#3497);
ENDSEC;
END-ISO-10303-21;
