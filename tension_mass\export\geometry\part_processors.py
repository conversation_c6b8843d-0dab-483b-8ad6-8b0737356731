#!/usr/bin/env python3
"""
Part Processors Module

Handles processing of Part2 and Part3 geometry components.
Includes overlap detection, clustering, and sub-object creation.
"""

from typing import Any, List, Optional, Dict
import logging


class PartProcessors:
    """Process Part2 and Part3 geometry components with overlap handling."""
    
    def __init__(self, logger: Optional[logging.Logger] = None):
        """Initialize part processors."""
        self.logger = logger or logging.getLogger(__name__)
        
        # Will be injected by main class
        self.union_components = None
        self.difference_operation = None
        self._extract_solids_safely = None
        self._detect_overlapping_components = None
        self._cluster_overlapping_components = None
        self._union_component_cluster = None
    
    def _process_part2_components(self,
                                part2_list: List[Any],
                                union_part1: Any,
                                group_members: List[str]) -> Dict[str, Any]:
        """
        Process Part 2 components with clustering for overlapping parts.

        REFACTORED: Reduced deep nesting by breaking down into focused helper methods:
        - _trim_part2_components_by_part1(): Apply Part1 trimming to all components
        - _cluster_part2_components(): Detect overlaps and cluster components
        - _process_part2_clusters(): Process individual clusters (union vs individual)
        - _verify_and_fix_final_overlaps(): Handle final overlap verification and re-clustering
        - _create_part2_sub_objects_from_results(): Create sub-objects from final results
        """
        if not part2_list:
            return {'parts': [], 'sub_objects': []}

        # 🔍 PART2 DEBUG: Log initial Part2 processing state
        initial_volume = sum(getattr(comp, 'volume', 0.0) for comp in part2_list)
        self.logger.info(f"🔍 PART2 DEBUG: Starting Part2 processing with {len(part2_list)} components, {initial_volume:.3f} m³ total")

        # Step 1: Apply Part1 trimming to all components
        self.logger.info(f"🔍 PART2 DEBUG: Step 1 - Applying Part1 trimming...")
        trimmed_components = self._trim_part2_components_by_part1(part2_list, union_part1)

        # 🔍 PART2 DEBUG: Log trimming results
        trimmed_volume = sum(getattr(comp, 'volume', 0.0) for comp in trimmed_components)
        self.logger.info(f"🔍 PART2 DEBUG: After Part1 trimming - {len(trimmed_components)} components, {trimmed_volume:.3f} m³ total")

        if len(trimmed_components) < len(part2_list):
            self.logger.warning(f"🔍 PART2 DEBUG: Component count reduced during trimming! {len(part2_list)} → {len(trimmed_components)}")
        if initial_volume > 0.1 and trimmed_volume < 0.001:
            self.logger.error(f"🔍 PART2 DEBUG: CRITICAL - Part2 volume eliminated during Part1 trimming! {initial_volume:.3f} → {trimmed_volume:.3f} m³")
        
        # Step 2: Detect overlaps and cluster components
        self.logger.info(f"🔍 PART2 DEBUG: Step 2 - Detecting overlaps and clustering...")
        clusters = self._cluster_part2_components(trimmed_components)
        self.logger.info(f"🔍 PART2 DEBUG: Found {len(clusters)} clusters from {len(trimmed_components)} components")

        # Step 3: Process clusters (individual vs union)
        self.logger.info(f"🔍 PART2 DEBUG: Step 3 - Processing clusters...")
        final_parts, cluster_info = self._process_part2_clusters(trimmed_components, clusters)

        # 🔍 PART2 DEBUG: Log cluster processing results
        cluster_volume = sum(getattr(part, 'volume', 0.0) for part in final_parts)
        self.logger.info(f"🔍 PART2 DEBUG: After cluster processing - {len(final_parts)} final parts, {cluster_volume:.3f} m³ total")

        if trimmed_volume > 0.1 and cluster_volume < 0.001:
            self.logger.error(f"🔍 PART2 DEBUG: CRITICAL - Part2 volume eliminated during cluster processing! {trimmed_volume:.3f} → {cluster_volume:.3f} m³")

        # Step 4: Verify and fix final overlaps
        self.logger.info(f"🔍 PART2 DEBUG: Step 4 - Verifying and fixing final overlaps...")
        final_parts, cluster_info = self._verify_and_fix_final_overlaps(final_parts, cluster_info)
        
        # 🔍 PART2 DEBUG: Log final overlap verification results
        final_volume = sum(getattr(part, 'volume', 0.0) for part in final_parts)
        self.logger.info(f"🔍 PART2 DEBUG: After final overlap verification - {len(final_parts)} final parts, {final_volume:.3f} m³ total")

        # Step 5: Create sub-objects from final results
        self.logger.info(f"🔍 PART2 DEBUG: Step 5 - Creating sub-objects...")
        sub_objects = self._create_part2_sub_objects_from_results(final_parts, group_members, cluster_info)

        # 🔍 PART2 DEBUG: Log final Part2 processing results
        sub_objects_volume = sum(obj.get('volume', 0.0) for obj in sub_objects)
        self.logger.info(f"🔍 PART2 DEBUG: Final Part2 results - {len(final_parts)} parts, {len(sub_objects)} sub-objects")
        self.logger.info(f"🔍 PART2 DEBUG: Final volumes - Parts: {final_volume:.3f} m³, Sub-objects: {sub_objects_volume:.3f} m³")

        if initial_volume > 0.1 and final_volume < 0.001:
            self.logger.error(f"🔍 PART2 DEBUG: CRITICAL - Part2 COMPLETELY ELIMINATED! Initial: {initial_volume:.3f} → Final: {final_volume:.3f} m³")
        elif abs(initial_volume - final_volume) > initial_volume * 0.5:
            self.logger.warning(f"🔍 PART2 DEBUG: Significant Part2 volume change! Initial: {initial_volume:.3f} → Final: {final_volume:.3f} m³")

        return {'parts': final_parts, 'sub_objects': sub_objects}
    
    def _trim_part2_components_by_part1(self, part2_list: List[Any], union_part1: Any) -> List[Any]:
        """
        Apply Part1 trimming to all Part2 components.
        
        REFACTORED: Reduced deep nesting by breaking down into focused helper methods:
        - _process_single_part2_component(): Process individual components
        - _apply_part1_trimming(): Handle trimming operations  
        - _handle_trimming_failure(): Handle error cases
        
        Args:
            part2_list: List of Part2 components to trim
            union_part1: Part1 union for trimming operations
            
        Returns:
            List of trimmed components
        """
        trimmed_components = []
        
        for i, part2_component in enumerate(part2_list):
            processed_component = self._process_single_part2_component(
                part2_component, union_part1, i
            )
            trimmed_components.append(processed_component)
        
        return trimmed_components

    def _process_single_part2_component(self, component: Any, union_part1: Any, component_index: int) -> Any:
        """
        Process a single Part2 component with error handling.
        
        Args:
            component: Part2 component to process
            union_part1: Part1 union for trimming operations
            component_index: Index of component for logging
            
        Returns:
            Processed component (trimmed if possible, original if failed)
        """
        try:
            return self._apply_part1_trimming(component, union_part1, component_index)
        except Exception as e:
            return self._handle_component_processing_failure(component, e, component_index)
    
    def _apply_part1_trimming(self, component: Any, union_part1: Any, component_index: int) -> Any:
        """
        Skip Part1 trimming - already done during individual pile processing.
        
        During individual pile creation, Part 2 and Part 3 components already have
        their own pile cylinder subtracted. Group-level Part 1 trimming would be
        double-trimming, which can cause geometry failures and is mathematically incorrect.
        
        Args:
            component: Component to process (no longer trimmed)
            union_part1: Part1 union (ignored)
            component_index: Index for logging
            
        Returns:
            Original component without additional trimming
        """
        # Skip Part 1 trimming to avoid double-trimming
        # Individual processing already subtracted own pile cylinder
        return component
    
    def _handle_trimming_failure(self, original_component: Any, error: Exception, component_index: int) -> Any:
        """
        Handle failure during Part1 trimming operation.
        
        Args:
            original_component: Original component to fall back to
            error: Exception that occurred during trimming
            component_index: Index for logging
            
        Returns:
            Original component as fallback
        """
        self.logger.warning(f"Failed to trim Part2 component {component_index+1} by Part1: {error}")
        return original_component
    
    def _handle_component_processing_failure(self, original_component: Any, error: Exception, component_index: int) -> Any:
        """
        Handle failure during component processing.
        
        Args:
            original_component: Original component to fall back to
            error: Exception that occurred during processing
            component_index: Index for logging
            
        Returns:
            Original component as fallback
        """
        self.logger.warning(f"Failed to process Part2 component {component_index+1}: {error}")
        return original_component
    
    def _cluster_part2_components(self, trimmed_components: List[Any]) -> List[List[int]]:
        """
        Detect overlaps and cluster Part2 components.
        
        Args:
            trimmed_components: List of trimmed Part2 components
            
        Returns:
            List of clusters (each cluster is a list of component indices)
        """
        # Detect overlapping components
        overlapping_pairs = self._detect_overlapping_components(trimmed_components)
        
        # Cluster overlapping components using Union-Find
        clusters = self._cluster_overlapping_components(len(trimmed_components), overlapping_pairs)
        
        return clusters
    
    def _process_part2_clusters(self, trimmed_components: List[Any], 
                               clusters: List[List[int]]) -> tuple[List[Any], List[Dict]]:
        """
        Process Part2 clusters (union if multiple, keep individual if single).
        
        Args:
            trimmed_components: List of trimmed Part2 components
            clusters: List of clusters to process
            
        Returns:
            Tuple of (final_parts, cluster_info)
        """
        final_parts = []
        cluster_info = []  # Track cluster information for sub-objects
        
        for cluster_idx, cluster in enumerate(clusters):
            if len(cluster) == 1:
                # Individual component - no union needed
                component = trimmed_components[cluster[0]]
                component_solids = self._extract_solids_safely(component)
                final_parts.extend(component_solids)
                
                # Track as individual component
                cluster_info.append({
                    'type': 'individual',
                    'original_indices': cluster,
                    'solid_count': len(component_solids),
                    'cluster_index': cluster_idx
                })
                
                component_volume = sum(getattr(s, 'volume', 0.0) for s in component_solids)
                # Removed verbose individual component log
                
            else:
                # Multiple overlapping components - union them with Part2 protection
                cluster_components = [trimmed_components[i] for i in cluster]
                
                # Track original metrics for Part2 protection
                original_count = len(cluster_components)
                original_volumes = []
                for comp in cluster_components:
                    comp_solids = self._extract_solids_safely(comp)
                    comp_volume = sum(getattr(s, 'volume', 0.0) for s in comp_solids)
                    original_volumes.append(comp_volume)
                original_total_volume = sum(original_volumes)
                
                # Attempt union operation
                union_result = self._union_component_cluster(trimmed_components, cluster)
                union_solids = self._extract_solids_safely(union_result)
                
                # Calculate result metrics
                result_count = len(union_solids)
                result_volume = sum(getattr(s, 'volume', 0.0) for s in union_solids)
                
                # Apply Part2 protection logic
                component_loss_rate = (original_count - result_count) / original_count if original_count > 0 else 0
                volume_loss_rate = (original_total_volume - result_volume) / original_total_volume if original_total_volume > 0 else 0
                
                # Protection thresholds: >50% component loss OR >95% volume loss
                if component_loss_rate > 0.5 or volume_loss_rate > 0.95:
                    # Part2 elimination detected - preserve original components
                    self.logger.warning(f"🛡️ Part2 protection activated for cluster {cluster_idx+1}:")
                    self.logger.warning(f"   Component reduction: {original_count} → {result_count} ({component_loss_rate:.1%})")
                    self.logger.warning(f"   Volume reduction: {original_total_volume:.2f} → {result_volume:.2f} m³ ({volume_loss_rate:.1%})")
                    self.logger.warning(f"   Preserving {original_count} original components instead of union result")
                    
                    # Preserve original individual components
                    for comp in cluster_components:
                        comp_solids = self._extract_solids_safely(comp)
                        final_parts.extend(comp_solids)
                    
                    # Track as protected individual components
                    cluster_info.append({
                        'type': 'protected_individual',
                        'original_indices': cluster,
                        'solid_count': sum(len(self._extract_solids_safely(comp)) for comp in cluster_components),
                        'cluster_index': cluster_idx,
                        'protection_reason': f"Component loss: {component_loss_rate:.1%}, Volume loss: {volume_loss_rate:.1%}"
                    })
                    
                else:
                    # Union successful - use result
                    final_parts.extend(union_solids)
                    
                    # Track as union group
                    cluster_info.append({
                        'type': 'union',
                        'original_indices': cluster,
                        'solid_count': len(union_solids),
                        'cluster_index': cluster_idx
                    })
                    
                    self.logger.debug(f"Union group {cluster_idx+1}: components {cluster} -> {len(union_solids)} solids, volume = {result_volume:.3f} m³")
        
        self.logger.debug(f"🔧 Part2 clustering complete: {len(final_parts)} final parts from {len(clusters)} clusters")
        
        return final_parts, cluster_info
    
    def _verify_and_fix_final_overlaps(self, final_parts: List[Any], 
                                      cluster_info: List[Dict]) -> tuple[List[Any], List[Dict]]:
        """
        Verify and fix final overlaps between Part2 objects.
        
        This ensures that Part2_Union objects from different clusters don't overlap.
        
        Args:
            final_parts: List of final Part2 parts
            cluster_info: List of cluster information
            
        Returns:
            Tuple of (updated_final_parts, updated_cluster_info)
        """
        # Only perform verification if there are multiple parts
        if len(final_parts) <= 1:
            return final_parts, cluster_info
        
        self.logger.debug(f"🔧 Performing final overlap verification among {len(final_parts)} Part2 objects")
        
        # Detect overlaps among final parts
        final_overlapping_pairs = self._detect_overlapping_components(final_parts)
        
        if not final_overlapping_pairs:
            self.logger.debug("🔧 Final overlap verification passed: No overlaps detected between Part2 objects")
            return final_parts, cluster_info
        
        # Handle detected overlaps
        return self._handle_detected_final_overlaps(final_parts, cluster_info, final_overlapping_pairs)
    
    def _handle_detected_final_overlaps(self, final_parts: List[Any], cluster_info: List[Dict],
                                       final_overlapping_pairs: List[tuple]) -> tuple[List[Any], List[Dict]]:
        """
        Handle detected overlaps in final Part2 parts by re-clustering.
        
        Args:
            final_parts: List of final Part2 parts
            cluster_info: List of cluster information
            final_overlapping_pairs: List of overlapping pairs
            
        Returns:
            Tuple of (re_processed_parts, updated_cluster_info)
        """
        self.logger.warning(f"🚨 DETECTED {len(final_overlapping_pairs)} OVERLAPS between final Part2 objects! Re-clustering required...")
        print(f"⚠️  OVERLAP WARNING: Found {len(final_overlapping_pairs)} overlaps between final Part2 objects")
        
        # Re-cluster the final parts
        final_clusters = self._cluster_overlapping_components(len(final_parts), final_overlapping_pairs)
        
        # Re-process clusters that need additional unions
        re_processed_parts = []
        updated_cluster_info = []
        
        for cluster_idx, cluster in enumerate(final_clusters):
            if len(cluster) == 1:
                # No additional union needed
                part = final_parts[cluster[0]]
                re_processed_parts.append(part)
                updated_cluster_info.append({
                    'type': 'individual',
                    'original_indices': [cluster[0]],  # Index in final_parts
                    'solid_count': 1,
                    'cluster_index': cluster_idx
                })
                
            else:
                # Need to union overlapping final parts
                self.logger.warning(f"🔧 Re-unioning final parts {cluster} due to detected overlaps")
                cluster_parts = [final_parts[i] for i in cluster]
                
                # Use union_components to merge overlapping final parts
                re_union_result = self.union_components(cluster_parts)
                re_union_solids = self._extract_solids_safely(re_union_result)
                re_processed_parts.extend(re_union_solids)
                
                updated_cluster_info.append({
                    'type': 'union',
                    'original_indices': cluster,  # Indices in final_parts
                    'solid_count': len(re_union_solids),
                    'cluster_index': cluster_idx
                })
                
                re_union_volume = sum(getattr(s, 'volume', 0.0) for s in re_union_solids)
                self.logger.debug(f"🔧 Re-union group {cluster_idx+1}: final parts {cluster} -> {len(re_union_solids)} solids, volume = {re_union_volume:.3f} m³")
        
        self.logger.debug(f"🔧 Final overlap verification complete: {len(re_processed_parts)} non-overlapping Part2 objects")
        
        return re_processed_parts, updated_cluster_info
    
    def _create_part2_sub_objects_from_results(self, final_parts: List[Any], 
                                              group_members: List[str],
                                              cluster_info: List[Dict]) -> List[Dict]:
        """
        Create Part2 sub-objects from final results with cluster information.
        
        Args:
            final_parts: Final processed Part2 geometry parts
            group_members: Contributing pile IDs
            cluster_info: List of cluster information dictionaries
            
        Returns:
            List of sub-object dictionaries
        """
        # Use the existing clustered sub-objects creation method
        return self._create_part2_sub_objects_clustered(final_parts, group_members, cluster_info)

    def _create_part2_sub_objects(self, 
                                final_parts: List[Any], 
                                group_members: List[str]) -> List[Dict]:
        """
        Create Part2 sub-objects preserving individual pile geometries.
        
        This method creates separate sub-objects for each Part2 geometry component
        to preserve the individual pile information instead of merging them all
        into a single entity.
        
        Args:
            final_parts: Processed Part2 geometry parts (individual solids)
            group_members: Contributing pile IDs
            
        Returns:
            List of sub-object dictionaries (one per final_part)
        """
        sub_objects = []
        contributing_piles = group_members or []
        
        if not final_parts:
            self.logger.warning("No final parts provided for Part2 sub-objects creation")
            return sub_objects
        
        self.logger.debug(f"🔧 Creating Part2 sub-objects for {len(final_parts)} geometry parts from {len(contributing_piles)} piles")
        
        # Create individual Part2 sub-objects for each geometry part
        # This preserves the individual pile information instead of merging
        total_volume = 0.0
        for i, part in enumerate(final_parts):
            part_volume = getattr(part, 'volume', 0.0)
            
            if part_volume <= 0:
                self.logger.warning(f"Part2 geometry {i+1} has zero or negative volume: {part_volume}")
                continue  # Skip invalid parts
            
            total_volume += part_volume
            
            # Create descriptive label for multiple parts
            if len(final_parts) == 1:
                label = 'Part2'
            else:
                label = f'Part2_{i+1}'
            
            sub_objects.append({
                'volume': part_volume,
                'material': 'Mixed',  # Generic material designation
                'label': label,
                'contributing_piles': contributing_piles.copy(),
                'part_type': 'Part2',
                'material_type': 'Mixed',
                'geometry_index': i  # Track which geometry this represents
            })
            
            # Removed verbose part sub-object log
        
        self.logger.debug(f"🔧 Created {len(sub_objects)} Part2 sub-objects with total volume: {total_volume:.3f} m³")
        
        return sub_objects

    def _create_part2_sub_objects_clustered(self, 
                                          final_parts: List[Any], 
                                          group_members: List[str],
                                          cluster_info: List[Dict]) -> List[Dict]:
        """
        Create Part2 sub-objects with clustering information.

        This enhanced method creates sub-objects that reflect the clustering
        decisions: individual components vs union groups.

        Args:
            final_parts: Processed Part2 geometry parts (individual solids or union results)
            group_members: Contributing pile IDs
            cluster_info: List of cluster information dictionaries
            
        Returns:
            List of sub-object dictionaries with cluster information
        """
        # Early validation
        if not final_parts:
            self.logger.warning("No final parts provided for Part2 sub-objects creation")
            return []

        contributing_piles = group_members or []
        self.logger.debug(f"🔧 Creating clustered Part2 sub-objects for {len(final_parts)} geometry parts from {len(cluster_info)} clusters")

        # Process all clusters and collect results
        sub_objects = []
        part_index = 0
        total_volume = 0.0
        union_object_counter = 1

        for cluster in cluster_info:
            cluster_results = self._process_cluster_parts(
                cluster, final_parts, part_index, union_object_counter, contributing_piles
            )
            
            sub_objects.extend(cluster_results['sub_objects'])
            part_index = cluster_results['next_part_index']
            total_volume += cluster_results['cluster_volume']
            union_object_counter = cluster_results['next_union_counter']

        self.logger.debug(f"🔧 Created {len(sub_objects)} clustered Part2 sub-objects with total volume: {total_volume:.3f} m³")
        return sub_objects

    def _process_cluster_parts(self, cluster: Dict, final_parts: List[Any], 
                              start_part_index: int, union_counter: int, 
                              contributing_piles: List[str]) -> Dict:
        """
        Process all parts within a single cluster.
        
        Returns:
            Dict with sub_objects, next_part_index, cluster_volume, and next_union_counter
        """
        cluster_type = cluster['type']
        original_indices = cluster['original_indices']
        solid_count = cluster['solid_count']
        cluster_index = cluster['cluster_index']
        
        sub_objects = []
        part_index = start_part_index
        cluster_volume = 0.0
        
        for i in range(solid_count):
            if part_index >= len(final_parts):
                self.logger.warning(f"Part index {part_index} out of range for final_parts")
                break

            part = final_parts[part_index]
            
            # Validate part volume
            if not self._validate_part_volume(part, part_index):
                part_index += 1
                continue

            part_volume = getattr(part, 'volume', 0.0)
            cluster_volume += part_volume

            # Create sub-object
            label = self._create_part2_label(cluster_type, cluster_index, union_counter, solid_count, i)
            sub_object = self._create_part2_sub_object(
                part_volume, label, contributing_piles, part_index,
                cluster_type, cluster_index, original_indices
            )
            
            sub_objects.append(sub_object)
            # Removed verbose clustered sub-object log
            part_index += 1

        # Update union counter if this was a union cluster
        next_union_counter = union_counter + 1 if cluster_type == 'union' else union_counter
        
        return {
            'sub_objects': sub_objects,
            'next_part_index': part_index,
            'cluster_volume': cluster_volume,
            'next_union_counter': next_union_counter
        }

    def _validate_part_volume(self, part: Any, part_index: int) -> bool:
        """Validate that a part has positive volume."""
        part_volume = getattr(part, 'volume', 0.0)
        if part_volume <= 0:
            self.logger.warning(f"Part2 geometry {part_index+1} has zero or negative volume: {part_volume}")
            return False
        return True

    def _create_part2_label(self, cluster_type: str, cluster_index: int, 
                           union_counter: int, solid_count: int, solid_index: int) -> str:
        """Create descriptive label for Part2 sub-object."""
        if cluster_type == 'union':
            if solid_count == 1:
                return f'Part2_Union_{union_counter}'
            else:
                return f'Part2_Union_{union_counter}_{solid_index+1}'
        else:  # individual type
            if solid_count == 1:
                return f'Part2_Individual_{cluster_index+1}'
            else:
                return f'Part2_Individual_{cluster_index+1}_{solid_index+1}'

    def _create_part2_sub_object(self, volume: float, label: str, contributing_piles: List[str],
                                geometry_index: int, cluster_type: str, cluster_index: int,
                                original_indices: List[int]) -> Dict[str, Any]:
        """Create a Part2 sub-object dictionary with all required metadata."""
        return {
            'volume': volume,
            'material': 'Mixed',  # Generic material designation
            'label': label,
            'contributing_piles': contributing_piles.copy(),
            'part_type': 'Part2',
            'material_type': 'Mixed',
            'geometry_index': geometry_index,  # Track which geometry this represents
            'cluster_type': cluster_type,  # 'individual' or 'union'
            'cluster_index': cluster_index,  # Which cluster this belongs to
            'original_components': original_indices,  # Original component indices that formed this
            'cluster_size': len(original_indices)  # Number of original components in cluster
        }
    
    def _process_part3_components_simple(self, 
                                       part3_list: List[Any], 
                                       group_members: List[str] = None) -> Dict[str, Any]:
        """
        Simplified Part 3 processing - just union components without intermediate trimming.
        
        The final group-level trimming will handle all necessary geometry cuts:
        Part 3 Final = Part 3 Union - Part 2 Final - Part 1 Final
        
        This approach is more efficient and mathematically equivalent to the complex
        two-stage trimming process.
        
        Args:
            part3_list: Raw Part 3 components to process
            group_members: List of contributing pile IDs
            
        Returns:
            Dictionary with processed parts and sub-objects
        """
        if not part3_list:
            return {'parts': [], 'sub_objects': []}
        
        self.logger.info(f"🔧 Starting union operation with {len(part3_list)} components...")
        
        # Simply union Part 3 components - no intermediate trimming
        union_part3 = self.union_components(part3_list)
        
        # Log union result details
        union_type = type(union_part3).__name__
        self.logger.info(f"📊 Union operation result: {union_type}")
        
        # Extract individual solids from union result
        # This allows legitimate multiple objects while fixing attribution
        self.logger.info("🔍 Extracting individual solids from union result...")
        final_parts = self._extract_solids_safely(union_part3)
        
        # Enhanced logging for multiple union parts
        if len(final_parts) > 1:
            self.logger.info(f"📦 MULTIPLE UNION PARTS CREATED: {len(final_parts)} separate solid objects")
            self.logger.info("📋 This occurs when progressive union operations cannot fully merge all geometry:")
            self.logger.info("   • Some pile geometries have complex overlaps that resist merging")
            self.logger.info("   • Boolean union operations partially fail and create compound objects")
            self.logger.info("   • Each separate solid becomes a distinct 'Part3_Union_X' export")
            
            # Log volume details for each part
            for i, part in enumerate(final_parts):
                part_volume = getattr(part, 'volume', 0.0)
                part_type = type(part).__name__
                self.logger.info(f"   📊 Part3_Union_{i+1}: {part_volume:.3f} m³ ({part_type})")
        else:
            self.logger.info(f"✅ Single unified Part3 solid created: {getattr(final_parts[0], 'volume', 0.0):.3f} m³")
        
        # Create sub-object metadata for Part 3 with proper pile attribution
        sub_objects = self._create_part3_sub_objects_with_attribution(
            final_parts, part3_list, group_members or []
        )
        
        self.logger.debug(f"Part 3 simple processing: {len(part3_list)} components -> {len(final_parts)} parts, {sum(getattr(p, 'volume', 0.0) for p in final_parts):.3f} m³")
        
        return {
            'parts': final_parts,
            'sub_objects': sub_objects
        }
    
    def _create_part3_sub_objects_with_attribution(self, final_parts: List[Any], 
                                              original_components: List[Any],
                                              group_members: List[str]) -> List[Dict]:
        """
        Create Part 3 sub-objects with proper pile attribution.
        
        When union operations fail and create multiple solids, this ensures that
        each solid gets attributed only to the piles whose geometry it actually contains,
        preventing the overlap issue where the same piles appear in multiple Part 3 objects.
        
        Args:
            final_parts: Extracted final Part 3 solids 
            original_components: Original Part 3 components before union
            group_members: All group member pile IDs
            
        Returns:
            List of sub-object dictionaries with proper pile attribution
        """
        sub_objects = []
        
        self.logger.info(f"🏗️  Creating sub-objects for {len(final_parts)} Part3 union parts...")
        if len(final_parts) > 1:
            self.logger.info("📝 Multiple Part3 union parts detected - this typically indicates:")
            self.logger.info("   • Complex pile geometry overlaps that couldn't be fully merged")
            self.logger.info("   • Progressive union created separate solid clusters")
            self.logger.info("   • Each cluster becomes a separate 'Part3_Union_X' export")
        
        for i, part in enumerate(final_parts):
            part_volume = getattr(part, 'volume', 0.0)
            
            # Create descriptive label for multiple parts
            if len(final_parts) == 1:
                label = 'Part3_Union_1'
            else:
                label = f'Part3_Union_{i+1}'
            
            # 🔍 ENHANCED LOGGING: Track Part3_Union creation
            self.logger.info(f"🏗️  CREATING PART3 COMPONENT: {label}")
            self.logger.info(f"   📊 Index: {i+1}/{len(final_parts)}")
            self.logger.info(f"   📊 Volume: {part_volume:.3f} m³")
            self.logger.info(f"   📊 Part type: {type(part).__name__}")
            self.logger.info(f"   📊 Group members count: {len(group_members)}")
            
            # Implement spatial overlap detection to determine which specific
            # piles contributed to each final solid
            self.logger.debug(f"🔍 Determining contributing piles for {label}...")
            contributing_piles = self._determine_contributing_piles_for_solid(
                part, group_members, original_components
            )
            
            if not contributing_piles:
                self.logger.warning(f"⚠️  NO PILES ATTRIBUTED to {label} (volume: {part_volume:.3f} m³)")
                self.logger.warning("   This occurs when:")
                self.logger.warning("   • Spatial overlap detection fails due to geometry complexity")
                self.logger.warning("   • Union operations create 'orphaned' geometry not linked to original piles")
                self.logger.warning("   • Component geometry references are lost during progressive union")
                self.logger.warning(f"   Original components: {len(original_components)}, Group members: {len(group_members)}")
            else:
                self.logger.info(f"✅ {label}: {part_volume:.3f} m³ attributed to {len(contributing_piles)} piles")
                self.logger.debug(f"   Contributing piles: {', '.join(contributing_piles[:5])}{'...' if len(contributing_piles) > 5 else ''}")
            
            sub_objects.append({
                'volume': part_volume,
                'material': 'Soil',
                'label': label,
                'contributing_piles': contributing_piles,
                'part_type': 'Part3',
                'material_type': 'Soil',
                'geometry': part  # Add geometry reference for debug access
            })
        
        return sub_objects

    def _determine_contributing_piles_for_solid(self, solid, all_group_members, original_components):
        """
        Determine which piles actually contributed to a specific Part3 solid through spatial overlap.
        
        Args:
            solid: The Part3 solid to check
            all_group_members: List of all pile names in the group
            original_components: List of original Part3 component objects
            
        Returns:
            List of pile names that spatially overlap with the solid
        """
        contributing_piles = []
        
        # 🔧 FIXED APPROACH: Create a proper mapping between components and pile names
        # Since original_components are organized in the same order as group_members
        # (from _organize_components_by_type), we can create a direct mapping
        
        self.logger.debug(f"   🔍 Creating component-pile mapping for {len(all_group_members)} piles, {len(original_components)} components")
        
        # Create a list of (pile_name, component) pairs, filtering out None components
        pile_component_pairs = []
        for i, pile_name in enumerate(all_group_members):
            if i < len(original_components) and original_components[i] is not None:
                pile_component_pairs.append((pile_name, original_components[i]))
            else:
                self.logger.debug(f"   ⚠️ No valid component for pile {pile_name} at index {i}")
        
        self.logger.debug(f"   � Testing spatial overlap for {len(pile_component_pairs)} valid pile-component pairs...")
        
        for pile_name, pile_geometry in pile_component_pairs:
            try:
                # Test spatial overlap using geometric intersection
                try:
                    # Check if pile's geometry intersects with the solid
                    intersection = pile_geometry & solid
                    
                    # If intersection exists and has volume, this pile contributes
                    if intersection is not None and hasattr(intersection, 'volume') and intersection.volume > 1e-6:
                        contributing_piles.append(pile_name)
                        self.logger.debug(f"   ✓ {pile_name} intersects (volume: {intersection.volume:.3f} m³)")
                    else:
                        self.logger.debug(f"   ✗ {pile_name} no intersection")
                        
                except Exception as geom_e:
                    # Fallback to bounding box overlap if geometric intersection fails
                    self.logger.debug(f"   ⚠️ Geometric test failed for {pile_name}: {geom_e}")
                    try:
                        pile_bbox = pile_geometry.bounding_box()
                        solid_bbox = solid.bounding_box()
                        
                        # Check 3D bounding box overlap
                        overlap_x = (pile_bbox.min.x <= solid_bbox.max.x and pile_bbox.max.x >= solid_bbox.min.x)
                        overlap_y = (pile_bbox.min.y <= solid_bbox.max.y and pile_bbox.max.y >= solid_bbox.min.y)
                        overlap_z = (pile_bbox.min.z <= solid_bbox.max.z and pile_bbox.max.z >= solid_bbox.min.z)
                        
                        if overlap_x and overlap_y and overlap_z:
                            contributing_piles.append(pile_name)
                            self.logger.debug(f"   ✓ {pile_name} bounding box overlap")
                        else:
                            self.logger.debug(f"   ✗ {pile_name} no bounding box overlap")
                            
                    except Exception as bbox_e:
                        self.logger.warning(f"   ❌ Both tests failed for {pile_name}: {bbox_e}")
                        continue
                        
            except Exception as e:
                self.logger.warning(f"   ❌ Error processing {pile_name}: {e}")
                continue
        
        # 🔧 ENHANCED DEBUG: If no piles were attributed, log additional debugging info
        if not contributing_piles:
            self.logger.warning(f"   🔍 ENHANCED DEBUG: No piles attributed to union solid")
            self.logger.warning(f"   📊 Input data: {len(all_group_members)} group members, {len(original_components)} components")
            self.logger.warning(f"   📊 Valid pairs created: {len(pile_component_pairs)}")
            self.logger.warning(f"   📊 Union solid volume: {getattr(solid, 'volume', 'N/A')} m³")
            self.logger.warning(f"   📊 Union solid type: {type(solid).__name__}")
            
            # For debugging, let's try a simpler approach: attribute all group members
            # This ensures the union parts get proper attribution even if spatial tests fail
            self.logger.warning(f"   🔧 FALLBACK: Attributing all group members to avoid orphaned geometry")
            contributing_piles = all_group_members.copy()
        
        self.logger.info(f"   📊 Attributed {len(contributing_piles)} of {len(all_group_members)} piles: {', '.join(contributing_piles[:5])}{'...' if len(contributing_piles) > 5 else ''}")
        return contributing_piles
