ISO-10303-21;
HEADER;
FILE_DESCRIPTION(('Open CASCADE Model'),'2;1');
FILE_NAME('Pile_PC1-P3','2025-08-25T20:07:48',('Author'),('Open CASCADE'
    ),'Open CASCADE STEP processor 7.8','build123d','Unknown');
FILE_SCHEMA(('AUTOMOTIVE_DESIGN { 1 0 10303 214 1 1 1 1 }'));
ENDSEC;
DATA;
#1 = APPLICATION_PROTOCOL_DEFINITION('international standard',
  'automotive_design',2000,#2);
#2 = APPLICATION_CONTEXT(
  'core data for automotive mechanical design processes');
#3 = SHAPE_DEFINITION_REPRESENTATION(#4,#10);
#4 = PRODUCT_DEFINITION_SHAPE('','',#5);
#5 = PRODUCT_DEFINITION('design','',#6,#9);
#6 = PRODUCT_DEFINITION_FORMATION('','',#7);
#7 = PRODUCT('Pile_PC1-P3','Pile_PC1-P3','',(#8));
#8 = PRODUCT_CONTEXT('',#2,'mechanical');
#9 = PRODUCT_DEFINITION_CONTEXT('part definition',#2,'design');
#10 = SHAPE_REPRESENTATION('',(#11,#15,#19,#23),#27);
#11 = AXIS2_PLACEMENT_3D('',#12,#13,#14);
#12 = CARTESIAN_POINT('',(0.,0.,0.));
#13 = DIRECTION('',(0.,0.,1.));
#14 = DIRECTION('',(1.,0.,-0.));
#15 = AXIS2_PLACEMENT_3D('',#16,#17,#18);
#16 = CARTESIAN_POINT('',(51.614918,130.739863,2.424596));
#17 = DIRECTION('',(0.,0.,1.));
#18 = DIRECTION('',(1.,0.,0.));
#19 = AXIS2_PLACEMENT_3D('',#20,#21,#22);
#20 = CARTESIAN_POINT('',(0.,0.,0.));
#21 = DIRECTION('',(0.,0.,1.));
#22 = DIRECTION('',(1.,0.,-0.));
#23 = AXIS2_PLACEMENT_3D('',#24,#25,#26);
#24 = CARTESIAN_POINT('',(0.,0.,0.));
#25 = DIRECTION('',(0.,0.,1.));
#26 = DIRECTION('',(1.,0.,-0.));
#27 = ( GEOMETRIC_REPRESENTATION_CONTEXT(3) 
GLOBAL_UNCERTAINTY_ASSIGNED_CONTEXT((#31)) GLOBAL_UNIT_ASSIGNED_CONTEXT(
(#28,#29,#30)) REPRESENTATION_CONTEXT('Context #1',
  '3D Context with UNIT and UNCERTAINTY') );
#28 = ( LENGTH_UNIT() NAMED_UNIT(*) SI_UNIT(.MILLI.,.METRE.) );
#29 = ( NAMED_UNIT(*) PLANE_ANGLE_UNIT() SI_UNIT($,.RADIAN.) );
#30 = ( NAMED_UNIT(*) SI_UNIT($,.STERADIAN.) SOLID_ANGLE_UNIT() );
#31 = UNCERTAINTY_MEASURE_WITH_UNIT(LENGTH_MEASURE(1.E-07),#28,
  'distance_accuracy_value','confusion accuracy');
#32 = PRODUCT_RELATED_PRODUCT_CATEGORY('part',$,(#7));
#33 = SHAPE_DEFINITION_REPRESENTATION(#34,#40);
#34 = PRODUCT_DEFINITION_SHAPE('','',#35);
#35 = PRODUCT_DEFINITION('design','',#36,#39);
#36 = PRODUCT_DEFINITION_FORMATION('','',#37);
#37 = PRODUCT('PC1-P3_Part1','PC1-P3_Part1','',(#38));
#38 = PRODUCT_CONTEXT('',#2,'mechanical');
#39 = PRODUCT_DEFINITION_CONTEXT('part definition',#2,'design');
#40 = ADVANCED_BREP_SHAPE_REPRESENTATION('',(#11,#41),#139);
#41 = MANIFOLD_SOLID_BREP('',#42);
#42 = CLOSED_SHELL('',(#43,#131,#135));
#43 = ADVANCED_FACE('',(#44),#57,.T.);
#44 = FACE_BOUND('',#45,.T.);
#45 = EDGE_LOOP('',(#46,#80,#103,#130));
#46 = ORIENTED_EDGE('',*,*,#47,.F.);
#47 = EDGE_CURVE('',#48,#48,#50,.T.);
#48 = VERTEX_POINT('',#49);
#49 = CARTESIAN_POINT('',(0.275,-6.73555739531E-17,15.575404));
#50 = SURFACE_CURVE('',#51,(#56,#68),.PCURVE_S1.);
#51 = CIRCLE('',#52,0.275);
#52 = AXIS2_PLACEMENT_3D('',#53,#54,#55);
#53 = CARTESIAN_POINT('',(0.,0.,15.575404));
#54 = DIRECTION('',(0.,0.,1.));
#55 = DIRECTION('',(1.,0.,-0.));
#56 = PCURVE('',#57,#62);
#57 = CYLINDRICAL_SURFACE('',#58,0.275);
#58 = AXIS2_PLACEMENT_3D('',#59,#60,#61);
#59 = CARTESIAN_POINT('',(0.,0.,0.));
#60 = DIRECTION('',(0.,0.,1.));
#61 = DIRECTION('',(1.,0.,-0.));
#62 = DEFINITIONAL_REPRESENTATION('',(#63),#67);
#63 = LINE('',#64,#65);
#64 = CARTESIAN_POINT('',(0.,15.575404));
#65 = VECTOR('',#66,1.);
#66 = DIRECTION('',(1.,0.));
#67 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#68 = PCURVE('',#69,#74);
#69 = PLANE('',#70);
#70 = AXIS2_PLACEMENT_3D('',#71,#72,#73);
#71 = CARTESIAN_POINT('',(0.,0.,15.575404));
#72 = DIRECTION('',(0.,0.,1.));
#73 = DIRECTION('',(1.,0.,-0.));
#74 = DEFINITIONAL_REPRESENTATION('',(#75),#79);
#75 = CIRCLE('',#76,0.275);
#76 = AXIS2_PLACEMENT_2D('',#77,#78);
#77 = CARTESIAN_POINT('',(0.,0.));
#78 = DIRECTION('',(1.,0.));
#79 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#80 = ORIENTED_EDGE('',*,*,#81,.F.);
#81 = EDGE_CURVE('',#82,#48,#84,.T.);
#82 = VERTEX_POINT('',#83);
#83 = CARTESIAN_POINT('',(0.275,-6.73555739531E-17,0.));
#84 = SEAM_CURVE('',#85,(#89,#96),.PCURVE_S1.);
#85 = LINE('',#86,#87);
#86 = CARTESIAN_POINT('',(0.275,-6.73555739531E-17,0.));
#87 = VECTOR('',#88,1.);
#88 = DIRECTION('',(0.,0.,1.));
#89 = PCURVE('',#57,#90);
#90 = DEFINITIONAL_REPRESENTATION('',(#91),#95);
#91 = LINE('',#92,#93);
#92 = CARTESIAN_POINT('',(6.28318530718,-0.));
#93 = VECTOR('',#94,1.);
#94 = DIRECTION('',(0.,1.));
#95 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#96 = PCURVE('',#57,#97);
#97 = DEFINITIONAL_REPRESENTATION('',(#98),#102);
#98 = LINE('',#99,#100);
#99 = CARTESIAN_POINT('',(0.,-0.));
#100 = VECTOR('',#101,1.);
#101 = DIRECTION('',(0.,1.));
#102 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#103 = ORIENTED_EDGE('',*,*,#104,.T.);
#104 = EDGE_CURVE('',#82,#82,#105,.T.);
#105 = SURFACE_CURVE('',#106,(#111,#118),.PCURVE_S1.);
#106 = CIRCLE('',#107,0.275);
#107 = AXIS2_PLACEMENT_3D('',#108,#109,#110);
#108 = CARTESIAN_POINT('',(0.,0.,0.));
#109 = DIRECTION('',(0.,0.,1.));
#110 = DIRECTION('',(1.,0.,-0.));
#111 = PCURVE('',#57,#112);
#112 = DEFINITIONAL_REPRESENTATION('',(#113),#117);
#113 = LINE('',#114,#115);
#114 = CARTESIAN_POINT('',(0.,0.));
#115 = VECTOR('',#116,1.);
#116 = DIRECTION('',(1.,0.));
#117 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#118 = PCURVE('',#119,#124);
#119 = PLANE('',#120);
#120 = AXIS2_PLACEMENT_3D('',#121,#122,#123);
#121 = CARTESIAN_POINT('',(0.,0.,0.));
#122 = DIRECTION('',(0.,0.,1.));
#123 = DIRECTION('',(1.,0.,-0.));
#124 = DEFINITIONAL_REPRESENTATION('',(#125),#129);
#125 = CIRCLE('',#126,0.275);
#126 = AXIS2_PLACEMENT_2D('',#127,#128);
#127 = CARTESIAN_POINT('',(0.,0.));
#128 = DIRECTION('',(1.,0.));
#129 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#130 = ORIENTED_EDGE('',*,*,#81,.T.);
#131 = ADVANCED_FACE('',(#132),#69,.T.);
#132 = FACE_BOUND('',#133,.T.);
#133 = EDGE_LOOP('',(#134));
#134 = ORIENTED_EDGE('',*,*,#47,.T.);
#135 = ADVANCED_FACE('',(#136),#119,.F.);
#136 = FACE_BOUND('',#137,.T.);
#137 = EDGE_LOOP('',(#138));
#138 = ORIENTED_EDGE('',*,*,#104,.F.);
#139 = ( GEOMETRIC_REPRESENTATION_CONTEXT(3) 
GLOBAL_UNCERTAINTY_ASSIGNED_CONTEXT((#143)) GLOBAL_UNIT_ASSIGNED_CONTEXT
((#140,#141,#142)) REPRESENTATION_CONTEXT('Context #1',
  '3D Context with UNIT and UNCERTAINTY') );
#140 = ( LENGTH_UNIT() NAMED_UNIT(*) SI_UNIT(.MILLI.,.METRE.) );
#141 = ( NAMED_UNIT(*) PLANE_ANGLE_UNIT() SI_UNIT($,.RADIAN.) );
#142 = ( NAMED_UNIT(*) SI_UNIT($,.STERADIAN.) SOLID_ANGLE_UNIT() );
#143 = UNCERTAINTY_MEASURE_WITH_UNIT(LENGTH_MEASURE(1.E-07),#140,
  'distance_accuracy_value','confusion accuracy');
#144 = CONTEXT_DEPENDENT_SHAPE_REPRESENTATION(#145,#147);
#145 = ( REPRESENTATION_RELATIONSHIP('','',#40,#10) 
REPRESENTATION_RELATIONSHIP_WITH_TRANSFORMATION(#146) 
SHAPE_REPRESENTATION_RELATIONSHIP() );
#146 = ITEM_DEFINED_TRANSFORMATION('','',#11,#15);
#147 = PRODUCT_DEFINITION_SHAPE('Placement','Placement of an item',#148
  );
#148 = NEXT_ASSEMBLY_USAGE_OCCURRENCE('39','=>[0:1:1:2]','',#5,#35,$);
#149 = PRODUCT_RELATED_PRODUCT_CATEGORY('part',$,(#37));
#150 = SHAPE_DEFINITION_REPRESENTATION(#151,#157);
#151 = PRODUCT_DEFINITION_SHAPE('','',#152);
#152 = PRODUCT_DEFINITION('design','',#153,#156);
#153 = PRODUCT_DEFINITION_FORMATION('','',#154);
#154 = PRODUCT('PC1-P3_Part2','PC1-P3_Part2','',(#155));
#155 = PRODUCT_CONTEXT('',#2,'mechanical');
#156 = PRODUCT_DEFINITION_CONTEXT('part definition',#2,'design');
#157 = ADVANCED_BREP_SHAPE_REPRESENTATION('',(#11,#158),#574);
#158 = MANIFOLD_SOLID_BREP('',#159);
#159 = CLOSED_SHELL('',(#160,#396,#446,#515,#541,#568));
#160 = ADVANCED_FACE('',(#161),#175,.T.);
#161 = FACE_BOUND('',#162,.T.);
#162 = EDGE_LOOP('',(#163,#193,#222,#278,#313,#347,#369,#370));
#163 = ORIENTED_EDGE('',*,*,#164,.T.);
#164 = EDGE_CURVE('',#165,#167,#169,.T.);
#165 = VERTEX_POINT('',#166);
#166 = CARTESIAN_POINT('',(51.889918,130.739863,2.424596));
#167 = VERTEX_POINT('',#168);
#168 = CARTESIAN_POINT('',(55.931369884327,130.739863,9.424596));
#169 = SEAM_CURVE('',#170,(#174,#186),.PCURVE_S1.);
#170 = LINE('',#171,#172);
#171 = CARTESIAN_POINT('',(51.889918,130.739863,2.424596));
#172 = VECTOR('',#173,1.);
#173 = DIRECTION('',(0.5,-1.224646799147E-16,0.866025403784));
#174 = PCURVE('',#175,#180);
#175 = CONICAL_SURFACE('',#176,0.275,0.523598775598);
#176 = AXIS2_PLACEMENT_3D('',#177,#178,#179);
#177 = CARTESIAN_POINT('',(51.614918,130.739863,2.424596));
#178 = DIRECTION('',(0.,0.,1.));
#179 = DIRECTION('',(1.,0.,0.));
#180 = DEFINITIONAL_REPRESENTATION('',(#181),#185);
#181 = LINE('',#182,#183);
#182 = CARTESIAN_POINT('',(6.28318530718,-0.));
#183 = VECTOR('',#184,1.);
#184 = DIRECTION('',(0.,1.));
#185 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#186 = PCURVE('',#175,#187);
#187 = DEFINITIONAL_REPRESENTATION('',(#188),#192);
#188 = LINE('',#189,#190);
#189 = CARTESIAN_POINT('',(0.,-0.));
#190 = VECTOR('',#191,1.);
#191 = DIRECTION('',(0.,1.));
#192 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#193 = ORIENTED_EDGE('',*,*,#194,.F.);
#194 = EDGE_CURVE('',#195,#167,#197,.T.);
#195 = VERTEX_POINT('',#196);
#196 = CARTESIAN_POINT('',(50.747818612774,126.51140045169,9.424596));
#197 = SURFACE_CURVE('',#198,(#203,#210),.PCURVE_S1.);
#198 = CIRCLE('',#199,4.316451884327);
#199 = AXIS2_PLACEMENT_3D('',#200,#201,#202);
#200 = CARTESIAN_POINT('',(51.614918,130.739863,9.424596));
#201 = DIRECTION('',(0.,0.,1.));
#202 = DIRECTION('',(1.,0.,0.));
#203 = PCURVE('',#175,#204);
#204 = DEFINITIONAL_REPRESENTATION('',(#205),#209);
#205 = LINE('',#206,#207);
#206 = CARTESIAN_POINT('',(0.,7.));
#207 = VECTOR('',#208,1.);
#208 = DIRECTION('',(1.,0.));
#209 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#210 = PCURVE('',#211,#216);
#211 = PLANE('',#212);
#212 = AXIS2_PLACEMENT_3D('',#213,#214,#215);
#213 = CARTESIAN_POINT('',(51.614918,130.739863,9.424596));
#214 = DIRECTION('',(0.,0.,1.));
#215 = DIRECTION('',(1.,0.,0.));
#216 = DEFINITIONAL_REPRESENTATION('',(#217),#221);
#217 = CIRCLE('',#218,4.316451884327);
#218 = AXIS2_PLACEMENT_2D('',#219,#220);
#219 = CARTESIAN_POINT('',(0.,0.));
#220 = DIRECTION('',(1.,0.));
#221 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#222 = ORIENTED_EDGE('',*,*,#223,.F.);
#223 = EDGE_CURVE('',#224,#195,#226,.T.);
#224 = VERTEX_POINT('',#225);
#225 = CARTESIAN_POINT('',(50.449147,131.893892,4.78948157597));
#226 = SURFACE_CURVE('',#227,(#232,#266),.PCURVE_S1.);
#227 = HYPERBOLA('',#228,1.905329123132,1.100042282135);
#228 = AXIS2_PLACEMENT_3D('',#229,#230,#231);
#229 = CARTESIAN_POINT('',(50.516565376824,130.67891599313,
    1.948282027919));
#230 = DIRECTION('',(0.998464005442,5.540424023454E-02,0.));
#231 = DIRECTION('',(-0.,0.,1.));
#232 = PCURVE('',#175,#233);
#233 = DEFINITIONAL_REPRESENTATION('',(#234),#265);
#234 = B_SPLINE_CURVE_WITH_KNOTS('',8,(#235,#236,#237,#238,#239,#240,
    #241,#242,#243,#244,#245,#246,#247,#248,#249,#250,#251,#252,#253,
    #254,#255,#256,#257,#258,#259,#260,#261,#262,#263,#264),
  .UNSPECIFIED.,.F.,.F.,(9,7,7,7,9),(-1.454611933972,-0.580062707922,
    7.584921161589E-02,1.059717090922,2.043584970229),.UNSPECIFIED.);
#235 = CARTESIAN_POINT('',(2.084991776078,3.826216497337));
#236 = CARTESIAN_POINT('',(2.133402360321,3.404503028328));
#237 = CARTESIAN_POINT('',(2.187235206409,3.041552595386));
#238 = CARTESIAN_POINT('',(2.247029876181,2.729685837248));
#239 = CARTESIAN_POINT('',(2.313287249843,2.462720711108));
#240 = CARTESIAN_POINT('',(2.38644216475,2.235681299671));
#241 = CARTESIAN_POINT('',(2.466692426268,2.04459569829));
#242 = CARTESIAN_POINT('',(2.553802149193,1.886360591951));
#243 = CARTESIAN_POINT('',(2.716893817381,1.662867286903));
#244 = CARTESIAN_POINT('',(2.790213904787,1.584254569486));
#245 = CARTESIAN_POINT('',(2.866631520057,1.521830856214));
#246 = CARTESIAN_POINT('',(2.945661026305,1.4748612029));
#247 = CARTESIAN_POINT('',(3.026628064863,1.442835895372));
#248 = CARTESIAN_POINT('',(3.108714718804,1.42545793544));
#249 = CARTESIAN_POINT('',(3.1910481577,1.422638330653));
#250 = CARTESIAN_POINT('',(3.395432428765,1.4522888951));
#251 = CARTESIAN_POINT('',(3.516757523913,1.50310885786));
#252 = CARTESIAN_POINT('',(3.633988858155,1.587368458872));
#253 = CARTESIAN_POINT('',(3.744517844225,1.706543510819));
#254 = CARTESIAN_POINT('',(3.846693796466,1.863195282619));
#255 = CARTESIAN_POINT('',(3.940014657523,2.061076772616));
#256 = CARTESIAN_POINT('',(4.024517613553,2.30532670751));
#257 = CARTESIAN_POINT('',(4.176719793761,2.900275543187));
#258 = CARTESIAN_POINT('',(4.244418554647,3.250974330946));
#259 = CARTESIAN_POINT('',(4.304124693534,3.661754304637));
#260 = CARTESIAN_POINT('',(4.356604778668,4.141186672423));
#261 = CARTESIAN_POINT('',(4.402695914404,4.699897531347));
#262 = CARTESIAN_POINT('',(4.443186234077,5.351026108887));
#263 = CARTESIAN_POINT('',(4.478788155006,6.110896668707));
#264 = CARTESIAN_POINT('',(4.510130339984,7.));
#265 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#266 = PCURVE('',#267,#272);
#267 = PLANE('',#268);
#268 = AXIS2_PLACEMENT_3D('',#269,#270,#271);
#269 = CARTESIAN_POINT('',(51.008936,121.80569,28.));
#270 = DIRECTION('',(0.998464005442,5.540424023454E-02,0.));
#271 = DIRECTION('',(-5.540424023454E-02,0.998464005442,0.));
#272 = DEFINITIONAL_REPRESENTATION('',(#273),#277);
#273 = ( BOUNDED_CURVE() B_SPLINE_CURVE(2,(#274,#275,#276),
.UNSPECIFIED.,.F.,.F.) B_SPLINE_CURVE_WITH_KNOTS((3,3),(-1.454611933972,
2.043584970229),.PIECEWISE_BEZIER_KNOTS.) CURVE() 
GEOMETRIC_REPRESENTATION_ITEM() RATIONAL_B_SPLINE_CURVE((1.,
2.961673821806,1.)) REPRESENTATION_ITEM('') );
#274 = CARTESIAN_POINT('',(11.114093386955,-21.74918750266));
#275 = CARTESIAN_POINT('',(8.775908459009,-25.38029184428));
#276 = CARTESIAN_POINT('',(4.712949516507,-18.575404));
#277 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#278 = ORIENTED_EDGE('',*,*,#279,.F.);
#279 = EDGE_CURVE('',#280,#224,#282,.T.);
#280 = VERTEX_POINT('',#281);
#281 = CARTESIAN_POINT('',(48.301331,131.800116,7.97422475016));
#282 = SURFACE_CURVE('',#283,(#288,#301),.PCURVE_S1.);
#283 = HYPERBOLA('',#284,2.085009865151,1.203781006908);
#284 = AXIS2_PLACEMENT_3D('',#285,#286,#287);
#285 = CARTESIAN_POINT('',(51.56240962651,131.94249826611,1.948282027919
    ));
#286 = DIRECTION('',(-4.361953975718E-02,0.999048214928,0.));
#287 = DIRECTION('',(0.,0.,1.));
#288 = PCURVE('',#175,#289);
#289 = DEFINITIONAL_REPRESENTATION('',(#290),#300);
#290 = B_SPLINE_CURVE_WITH_KNOTS('',8,(#291,#292,#293,#294,#295,#296,
    #297,#298,#299),.UNSPECIFIED.,.F.,.F.,(9,9),(-1.783141685478,
    -0.690820382625),.PIECEWISE_BEZIER_KNOTS.);
#291 = CARTESIAN_POINT('',(2.852122306177,5.900297373404));
#292 = CARTESIAN_POINT('',(2.80747671448,5.07749241588));
#293 = CARTESIAN_POINT('',(2.756246999356,4.390550927783));
#294 = CARTESIAN_POINT('',(2.697438802036,3.816098158881));
#295 = CARTESIAN_POINT('',(2.629949610366,3.336162529567));
#296 = CARTESIAN_POINT('',(2.552690686191,2.936782895451));
#297 = CARTESIAN_POINT('',(2.46462154647,2.607146418428));
#298 = CARTESIAN_POINT('',(2.36545262595,2.339005117766));
#299 = CARTESIAN_POINT('',(2.256068082192,2.126316863728));
#300 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#301 = PCURVE('',#302,#307);
#302 = PLANE('',#303);
#303 = AXIS2_PLACEMENT_3D('',#304,#305,#306);
#304 = CARTESIAN_POINT('',(50.449147,131.893892,28.));
#305 = DIRECTION('',(-4.361953975718E-02,0.999048214928,0.));
#306 = DIRECTION('',(-0.999048214928,-4.361953975718E-02,0.));
#307 = DEFINITIONAL_REPRESENTATION('',(#308),#312);
#308 = ( BOUNDED_CURVE() B_SPLINE_CURVE(2,(#309,#310,#311),
.UNSPECIFIED.,.F.,.F.) B_SPLINE_CURVE_WITH_KNOTS((3,3),(-1.783141685478,
-0.690820382625),.PIECEWISE_BEZIER_KNOTS.) CURVE() 
GEOMETRIC_REPRESENTATION_ITEM() RATIONAL_B_SPLINE_CURVE((1.,
1.152890196588,1.)) REPRESENTATION_ITEM('') );
#309 = CARTESIAN_POINT('',(2.36484842743,-19.67510662659));
#310 = CARTESIAN_POINT('',(0.532778130801,-22.67391901695));
#311 = CARTESIAN_POINT('',(-0.214986220675,-23.44908713627));
#312 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#313 = ORIENTED_EDGE('',*,*,#314,.F.);
#314 = EDGE_CURVE('',#315,#280,#317,.T.);
#315 = VERTEX_POINT('',#316);
#316 = CARTESIAN_POINT('',(48.130261339781,133.28719981128,9.424596));
#317 = SURFACE_CURVE('',#318,(#323,#335),.PCURVE_S1.);
#318 = HYPERBOLA('',#319,5.491827058128,3.170707830353);
#319 = AXIS2_PLACEMENT_3D('',#320,#321,#322);
#320 = CARTESIAN_POINT('',(48.464984011396,130.37750404413,
    1.948282027919));
#321 = DIRECTION('',(0.993448200572,0.11428330053,0.));
#322 = DIRECTION('',(-0.,0.,1.));
#323 = PCURVE('',#175,#324);
#324 = DEFINITIONAL_REPRESENTATION('',(#325),#334);
#325 = B_SPLINE_CURVE_WITH_KNOTS('',7,(#326,#327,#328,#329,#330,#331,
    #332,#333),.UNSPECIFIED.,.F.,.F.,(8,8),(-0.826403252807,
    -7.74616775129E-02),.PIECEWISE_BEZIER_KNOTS.);
#326 = CARTESIAN_POINT('',(2.510352974945,7.));
#327 = CARTESIAN_POINT('',(2.588945134196,6.457233690983));
#328 = CARTESIAN_POINT('',(2.674194350869,6.014314151979));
#329 = CARTESIAN_POINT('',(2.765980640173,5.661093407713));
#330 = CARTESIAN_POINT('',(2.863782837416,5.39022281414));
#331 = CARTESIAN_POINT('',(2.966428910373,5.196682472771));
#332 = CARTESIAN_POINT('',(3.072070406774,5.077558099702));
#333 = CARTESIAN_POINT('',(3.17874186933,5.03199766277));
#334 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#335 = PCURVE('',#336,#341);
#336 = PLANE('',#337);
#337 = AXIS2_PLACEMENT_3D('',#338,#339,#340);
#338 = CARTESIAN_POINT('',(48.301331,131.800116,28.));
#339 = DIRECTION('',(0.993448200572,0.11428330053,0.));
#340 = DIRECTION('',(-0.11428330053,0.993448200572,0.));
#341 = DEFINITIONAL_REPRESENTATION('',(#342),#346);
#342 = ( BOUNDED_CURVE() B_SPLINE_CURVE(2,(#343,#344,#345),
.UNSPECIFIED.,.F.,.F.) B_SPLINE_CURVE_WITH_KNOTS((3,3),(-0.826403252807,
-7.74616775129E-02),.PIECEWISE_BEZIER_KNOTS.) CURVE() 
GEOMETRIC_REPRESENTATION_ITEM() RATIONAL_B_SPLINE_CURVE((1.,
1.070937357963,1.)) REPRESENTATION_ITEM('') );
#343 = CARTESIAN_POINT('',(1.496891141807,-18.575404));
#344 = CARTESIAN_POINT('',(-4.794998083026E-02,-20.39100285228));
#345 = CARTESIAN_POINT('',(-1.186140051712,-20.54340633723));
#346 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#347 = ORIENTED_EDGE('',*,*,#348,.F.);
#348 = EDGE_CURVE('',#167,#315,#349,.T.);
#349 = SURFACE_CURVE('',#350,(#355,#362),.PCURVE_S1.);
#350 = CIRCLE('',#351,4.316451884327);
#351 = AXIS2_PLACEMENT_3D('',#352,#353,#354);
#352 = CARTESIAN_POINT('',(51.614918,130.739863,9.424596));
#353 = DIRECTION('',(0.,0.,1.));
#354 = DIRECTION('',(1.,0.,0.));
#355 = PCURVE('',#175,#356);
#356 = DEFINITIONAL_REPRESENTATION('',(#357),#361);
#357 = LINE('',#358,#359);
#358 = CARTESIAN_POINT('',(0.,7.));
#359 = VECTOR('',#360,1.);
#360 = DIRECTION('',(1.,0.));
#361 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#362 = PCURVE('',#211,#363);
#363 = DEFINITIONAL_REPRESENTATION('',(#364),#368);
#364 = CIRCLE('',#365,4.316451884327);
#365 = AXIS2_PLACEMENT_2D('',#366,#367);
#366 = CARTESIAN_POINT('',(0.,0.));
#367 = DIRECTION('',(1.,0.));
#368 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#369 = ORIENTED_EDGE('',*,*,#164,.F.);
#370 = ORIENTED_EDGE('',*,*,#371,.T.);
#371 = EDGE_CURVE('',#165,#165,#372,.T.);
#372 = SURFACE_CURVE('',#373,(#378,#385),.PCURVE_S1.);
#373 = CIRCLE('',#374,0.275);
#374 = AXIS2_PLACEMENT_3D('',#375,#376,#377);
#375 = CARTESIAN_POINT('',(51.614918,130.739863,2.424596));
#376 = DIRECTION('',(0.,0.,1.));
#377 = DIRECTION('',(1.,0.,0.));
#378 = PCURVE('',#175,#379);
#379 = DEFINITIONAL_REPRESENTATION('',(#380),#384);
#380 = LINE('',#381,#382);
#381 = CARTESIAN_POINT('',(0.,0.));
#382 = VECTOR('',#383,1.);
#383 = DIRECTION('',(1.,0.));
#384 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#385 = PCURVE('',#386,#391);
#386 = CYLINDRICAL_SURFACE('',#387,0.275);
#387 = AXIS2_PLACEMENT_3D('',#388,#389,#390);
#388 = CARTESIAN_POINT('',(51.614918,130.739863,2.424596));
#389 = DIRECTION('',(0.,0.,1.));
#390 = DIRECTION('',(1.,0.,0.));
#391 = DEFINITIONAL_REPRESENTATION('',(#392),#395);
#392 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#393,#394),.UNSPECIFIED.,.F.,.F.,
  (2,2),(0.,6.28318530718),.PIECEWISE_BEZIER_KNOTS.);
#393 = CARTESIAN_POINT('',(0.,0.));
#394 = CARTESIAN_POINT('',(6.28318530718,0.));
#395 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#396 = ADVANCED_FACE('',(#397),#386,.F.);
#397 = FACE_BOUND('',#398,.F.);
#398 = EDGE_LOOP('',(#399,#423,#444,#445));
#399 = ORIENTED_EDGE('',*,*,#400,.F.);
#400 = EDGE_CURVE('',#401,#401,#403,.T.);
#401 = VERTEX_POINT('',#402);
#402 = CARTESIAN_POINT('',(51.889918,130.739863,9.424596));
#403 = SURFACE_CURVE('',#404,(#409,#416),.PCURVE_S1.);
#404 = CIRCLE('',#405,0.275);
#405 = AXIS2_PLACEMENT_3D('',#406,#407,#408);
#406 = CARTESIAN_POINT('',(51.614918,130.739863,9.424596));
#407 = DIRECTION('',(0.,0.,1.));
#408 = DIRECTION('',(1.,0.,0.));
#409 = PCURVE('',#386,#410);
#410 = DEFINITIONAL_REPRESENTATION('',(#411),#415);
#411 = LINE('',#412,#413);
#412 = CARTESIAN_POINT('',(0.,7.));
#413 = VECTOR('',#414,1.);
#414 = DIRECTION('',(1.,0.));
#415 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#416 = PCURVE('',#211,#417);
#417 = DEFINITIONAL_REPRESENTATION('',(#418),#422);
#418 = CIRCLE('',#419,0.275);
#419 = AXIS2_PLACEMENT_2D('',#420,#421);
#420 = CARTESIAN_POINT('',(0.,0.));
#421 = DIRECTION('',(1.,0.));
#422 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#423 = ORIENTED_EDGE('',*,*,#424,.F.);
#424 = EDGE_CURVE('',#165,#401,#425,.T.);
#425 = SEAM_CURVE('',#426,(#430,#437),.PCURVE_S1.);
#426 = LINE('',#427,#428);
#427 = CARTESIAN_POINT('',(51.889918,130.739863,2.424596));
#428 = VECTOR('',#429,1.);
#429 = DIRECTION('',(0.,0.,1.));
#430 = PCURVE('',#386,#431);
#431 = DEFINITIONAL_REPRESENTATION('',(#432),#436);
#432 = LINE('',#433,#434);
#433 = CARTESIAN_POINT('',(6.28318530718,-0.));
#434 = VECTOR('',#435,1.);
#435 = DIRECTION('',(0.,1.));
#436 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#437 = PCURVE('',#386,#438);
#438 = DEFINITIONAL_REPRESENTATION('',(#439),#443);
#439 = LINE('',#440,#441);
#440 = CARTESIAN_POINT('',(0.,-0.));
#441 = VECTOR('',#442,1.);
#442 = DIRECTION('',(0.,1.));
#443 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#444 = ORIENTED_EDGE('',*,*,#371,.T.);
#445 = ORIENTED_EDGE('',*,*,#424,.T.);
#446 = ADVANCED_FACE('',(#447,#512),#211,.T.);
#447 = FACE_BOUND('',#448,.T.);
#448 = EDGE_LOOP('',(#449,#450,#451,#472,#493));
#449 = ORIENTED_EDGE('',*,*,#194,.T.);
#450 = ORIENTED_EDGE('',*,*,#348,.T.);
#451 = ORIENTED_EDGE('',*,*,#452,.F.);
#452 = EDGE_CURVE('',#453,#315,#455,.T.);
#453 = VERTEX_POINT('',#454);
#454 = CARTESIAN_POINT('',(48.301331,131.800116,9.424596));
#455 = SURFACE_CURVE('',#456,(#460,#466),.PCURVE_S1.);
#456 = LINE('',#457,#458);
#457 = CARTESIAN_POINT('',(48.383157505698,131.08881002206,9.424596));
#458 = VECTOR('',#459,1.);
#459 = DIRECTION('',(-0.11428330053,0.993448200572,0.));
#460 = PCURVE('',#211,#461);
#461 = DEFINITIONAL_REPRESENTATION('',(#462),#465);
#462 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#463,#464),.UNSPECIFIED.,.F.,.F.,
  (2,2),(0.715997046977,3.993670590954),.PIECEWISE_BEZIER_KNOTS.);
#463 = CARTESIAN_POINT('',(-3.313587,1.060253));
#464 = CARTESIAN_POINT('',(-3.688170350664,4.316451884327));
#465 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#466 = PCURVE('',#336,#467);
#467 = DEFINITIONAL_REPRESENTATION('',(#468),#471);
#468 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#469,#470),.UNSPECIFIED.,.F.,.F.,
  (2,2),(0.715997046977,3.993670590954),.PIECEWISE_BEZIER_KNOTS.);
#469 = CARTESIAN_POINT('',(0.,-18.575404));
#470 = CARTESIAN_POINT('',(3.277673543977,-18.575404));
#471 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#472 = ORIENTED_EDGE('',*,*,#473,.F.);
#473 = EDGE_CURVE('',#474,#453,#476,.T.);
#474 = VERTEX_POINT('',#475);
#475 = CARTESIAN_POINT('',(50.449147,131.893892,9.424596));
#476 = SURFACE_CURVE('',#477,(#481,#487),.PCURVE_S1.);
#477 = LINE('',#478,#479);
#478 = CARTESIAN_POINT('',(51.005778313255,131.91819513305,9.424596));
#479 = VECTOR('',#480,1.);
#480 = DIRECTION('',(-0.999048214928,-4.361953975718E-02,0.));
#481 = PCURVE('',#211,#482);
#482 = DEFINITIONAL_REPRESENTATION('',(#483),#486);
#483 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#484,#485),.UNSPECIFIED.,.F.,.F.,
  (2,2),(0.557161611359,2.707023818114),.PIECEWISE_BEZIER_KNOTS.);
#484 = CARTESIAN_POINT('',(-1.165771,1.154029));
#485 = CARTESIAN_POINT('',(-3.313587,1.060253));
#486 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#487 = PCURVE('',#302,#488);
#488 = DEFINITIONAL_REPRESENTATION('',(#489),#492);
#489 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#490,#491),.UNSPECIFIED.,.F.,.F.,
  (2,2),(0.557161611359,2.707023818114),.PIECEWISE_BEZIER_KNOTS.);
#490 = CARTESIAN_POINT('',(0.,-18.575404));
#491 = CARTESIAN_POINT('',(2.149862206755,-18.575404));
#492 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#493 = ORIENTED_EDGE('',*,*,#494,.F.);
#494 = EDGE_CURVE('',#195,#474,#495,.T.);
#495 = SURFACE_CURVE('',#496,(#500,#506),.PCURVE_S1.);
#496 = LINE('',#497,#498);
#497 = CARTESIAN_POINT('',(50.762750688412,126.24230299656,9.424596));
#498 = VECTOR('',#499,1.);
#499 = DIRECTION('',(-5.540424023454E-02,0.998464005442,0.));
#500 = PCURVE('',#211,#501);
#501 = DEFINITIONAL_REPRESENTATION('',(#502),#505);
#502 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#503,#504),.UNSPECIFIED.,.F.,.F.,
  (2,2),(0.181386728134,5.660283167574),.PIECEWISE_BEZIER_KNOTS.);
#503 = CARTESIAN_POINT('',(-0.862216905449,-4.316451884327));
#504 = CARTESIAN_POINT('',(-1.165771,1.154029));
#505 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#506 = PCURVE('',#267,#507);
#507 = DEFINITIONAL_REPRESENTATION('',(#508),#511);
#508 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#509,#510),.UNSPECIFIED.,.F.,.F.,
  (2,2),(0.181386728134,5.660283167574),.PIECEWISE_BEZIER_KNOTS.);
#509 = CARTESIAN_POINT('',(4.624824821428,-18.575404));
#510 = CARTESIAN_POINT('',(10.103721260868,-18.575404));
#511 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#512 = FACE_BOUND('',#513,.T.);
#513 = EDGE_LOOP('',(#514));
#514 = ORIENTED_EDGE('',*,*,#400,.F.);
#515 = ADVANCED_FACE('',(#516),#336,.F.);
#516 = FACE_BOUND('',#517,.F.);
#517 = EDGE_LOOP('',(#518,#519,#540));
#518 = ORIENTED_EDGE('',*,*,#452,.F.);
#519 = ORIENTED_EDGE('',*,*,#520,.T.);
#520 = EDGE_CURVE('',#453,#280,#521,.T.);
#521 = SURFACE_CURVE('',#522,(#526,#533),.PCURVE_S1.);
#522 = LINE('',#523,#524);
#523 = CARTESIAN_POINT('',(48.301331,131.800116,28.));
#524 = VECTOR('',#525,1.);
#525 = DIRECTION('',(0.,0.,-1.));
#526 = PCURVE('',#336,#527);
#527 = DEFINITIONAL_REPRESENTATION('',(#528),#532);
#528 = LINE('',#529,#530);
#529 = CARTESIAN_POINT('',(0.,0.));
#530 = VECTOR('',#531,1.);
#531 = DIRECTION('',(0.,-1.));
#532 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#533 = PCURVE('',#302,#534);
#534 = DEFINITIONAL_REPRESENTATION('',(#535),#539);
#535 = LINE('',#536,#537);
#536 = CARTESIAN_POINT('',(2.149862206755,0.));
#537 = VECTOR('',#538,1.);
#538 = DIRECTION('',(-0.,-1.));
#539 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#540 = ORIENTED_EDGE('',*,*,#314,.F.);
#541 = ADVANCED_FACE('',(#542),#302,.F.);
#542 = FACE_BOUND('',#543,.F.);
#543 = EDGE_LOOP('',(#544,#545,#566,#567));
#544 = ORIENTED_EDGE('',*,*,#473,.F.);
#545 = ORIENTED_EDGE('',*,*,#546,.T.);
#546 = EDGE_CURVE('',#474,#224,#547,.T.);
#547 = SURFACE_CURVE('',#548,(#552,#559),.PCURVE_S1.);
#548 = LINE('',#549,#550);
#549 = CARTESIAN_POINT('',(50.449147,131.893892,28.));
#550 = VECTOR('',#551,1.);
#551 = DIRECTION('',(0.,0.,-1.));
#552 = PCURVE('',#302,#553);
#553 = DEFINITIONAL_REPRESENTATION('',(#554),#558);
#554 = LINE('',#555,#556);
#555 = CARTESIAN_POINT('',(0.,0.));
#556 = VECTOR('',#557,1.);
#557 = DIRECTION('',(-0.,-1.));
#558 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#559 = PCURVE('',#267,#560);
#560 = DEFINITIONAL_REPRESENTATION('',(#561),#565);
#561 = LINE('',#562,#563);
#562 = CARTESIAN_POINT('',(10.103721260868,0.));
#563 = VECTOR('',#564,1.);
#564 = DIRECTION('',(0.,-1.));
#565 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#566 = ORIENTED_EDGE('',*,*,#279,.F.);
#567 = ORIENTED_EDGE('',*,*,#520,.F.);
#568 = ADVANCED_FACE('',(#569),#267,.F.);
#569 = FACE_BOUND('',#570,.F.);
#570 = EDGE_LOOP('',(#571,#572,#573));
#571 = ORIENTED_EDGE('',*,*,#546,.F.);
#572 = ORIENTED_EDGE('',*,*,#494,.F.);
#573 = ORIENTED_EDGE('',*,*,#223,.F.);
#574 = ( GEOMETRIC_REPRESENTATION_CONTEXT(3) 
GLOBAL_UNCERTAINTY_ASSIGNED_CONTEXT((#578)) GLOBAL_UNIT_ASSIGNED_CONTEXT
((#575,#576,#577)) REPRESENTATION_CONTEXT('Context #1',
  '3D Context with UNIT and UNCERTAINTY') );
#575 = ( LENGTH_UNIT() NAMED_UNIT(*) SI_UNIT(.MILLI.,.METRE.) );
#576 = ( NAMED_UNIT(*) PLANE_ANGLE_UNIT() SI_UNIT($,.RADIAN.) );
#577 = ( NAMED_UNIT(*) SI_UNIT($,.STERADIAN.) SOLID_ANGLE_UNIT() );
#578 = UNCERTAINTY_MEASURE_WITH_UNIT(LENGTH_MEASURE(1.E-07),#575,
  'distance_accuracy_value','confusion accuracy');
#579 = CONTEXT_DEPENDENT_SHAPE_REPRESENTATION(#580,#582);
#580 = ( REPRESENTATION_RELATIONSHIP('','',#157,#10) 
REPRESENTATION_RELATIONSHIP_WITH_TRANSFORMATION(#581) 
SHAPE_REPRESENTATION_RELATIONSHIP() );
#581 = ITEM_DEFINED_TRANSFORMATION('','',#11,#19);
#582 = PRODUCT_DEFINITION_SHAPE('Placement','Placement of an item',#583
  );
#583 = NEXT_ASSEMBLY_USAGE_OCCURRENCE('40','=>[0:1:1:3]','',#5,#152,$);
#584 = PRODUCT_RELATED_PRODUCT_CATEGORY('part',$,(#154));
#585 = SHAPE_DEFINITION_REPRESENTATION(#586,#592);
#586 = PRODUCT_DEFINITION_SHAPE('','',#587);
#587 = PRODUCT_DEFINITION('design','',#588,#591);
#588 = PRODUCT_DEFINITION_FORMATION('','',#589);
#589 = PRODUCT('PC1-P3_Part3','PC1-P3_Part3','',(#590));
#590 = PRODUCT_CONTEXT('',#2,'mechanical');
#591 = PRODUCT_DEFINITION_CONTEXT('part definition',#2,'design');
#592 = ADVANCED_BREP_SHAPE_REPRESENTATION('',(#11,#593),#997);
#593 = MANIFOLD_SOLID_BREP('',#594);
#594 = CLOSED_SHELL('',(#595,#717,#818,#865,#936,#963,#970));
#595 = ADVANCED_FACE('',(#596),#611,.T.);
#596 = FACE_BOUND('',#597,.T.);
#597 = EDGE_LOOP('',(#598,#634,#660,#693));
#598 = ORIENTED_EDGE('',*,*,#599,.T.);
#599 = EDGE_CURVE('',#600,#602,#604,.T.);
#600 = VERTEX_POINT('',#601);
#601 = CARTESIAN_POINT('',(50.747818612774,126.51140045169,9.424596));
#602 = VERTEX_POINT('',#603);
#603 = CARTESIAN_POINT('',(48.130261339781,133.28719981128,9.424596));
#604 = SURFACE_CURVE('',#605,(#610,#622),.PCURVE_S1.);
#605 = CIRCLE('',#606,4.316451884327);
#606 = AXIS2_PLACEMENT_3D('',#607,#608,#609);
#607 = CARTESIAN_POINT('',(51.614918,130.739863,9.424596));
#608 = DIRECTION('',(0.,0.,1.));
#609 = DIRECTION('',(-0.20088244013,-0.979615355763,0.));
#610 = PCURVE('',#611,#616);
#611 = CYLINDRICAL_SURFACE('',#612,4.316451884327);
#612 = AXIS2_PLACEMENT_3D('',#613,#614,#615);
#613 = CARTESIAN_POINT('',(51.614918,130.739863,9.424596));
#614 = DIRECTION('',(0.,0.,1.));
#615 = DIRECTION('',(1.,0.,0.));
#616 = DEFINITIONAL_REPRESENTATION('',(#617),#621);
#617 = LINE('',#618,#619);
#618 = CARTESIAN_POINT('',(4.510130339984,0.));
#619 = VECTOR('',#620,1.);
#620 = DIRECTION('',(1.,0.));
#621 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#622 = PCURVE('',#623,#628);
#623 = PLANE('',#624);
#624 = AXIS2_PLACEMENT_3D('',#625,#626,#627);
#625 = CARTESIAN_POINT('',(51.614918,130.739863,9.424596));
#626 = DIRECTION('',(0.,0.,1.));
#627 = DIRECTION('',(1.,0.,0.));
#628 = DEFINITIONAL_REPRESENTATION('',(#629),#633);
#629 = CIRCLE('',#630,4.316451884327);
#630 = AXIS2_PLACEMENT_2D('',#631,#632);
#631 = CARTESIAN_POINT('',(0.,0.));
#632 = DIRECTION('',(-0.20088244013,-0.979615355763));
#633 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#634 = ORIENTED_EDGE('',*,*,#635,.T.);
#635 = EDGE_CURVE('',#602,#636,#638,.T.);
#636 = VERTEX_POINT('',#637);
#637 = CARTESIAN_POINT('',(48.130261339781,133.28719981128,18.));
#638 = SURFACE_CURVE('',#639,(#643,#649),.PCURVE_S1.);
#639 = LINE('',#640,#641);
#640 = CARTESIAN_POINT('',(48.130261339781,133.28719981128,9.424596));
#641 = VECTOR('',#642,1.);
#642 = DIRECTION('',(0.,0.,1.));
#643 = PCURVE('',#611,#644);
#644 = DEFINITIONAL_REPRESENTATION('',(#645),#648);
#645 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#646,#647),.UNSPECIFIED.,.F.,.F.,
  (2,2),(0.,8.575404),.PIECEWISE_BEZIER_KNOTS.);
#646 = CARTESIAN_POINT('',(8.793538282124,0.));
#647 = CARTESIAN_POINT('',(8.793538282124,8.575404));
#648 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#649 = PCURVE('',#650,#655);
#650 = PLANE('',#651);
#651 = AXIS2_PLACEMENT_3D('',#652,#653,#654);
#652 = CARTESIAN_POINT('',(48.301331,131.800116,28.));
#653 = DIRECTION('',(0.993448200572,0.11428330053,0.));
#654 = DIRECTION('',(-0.11428330053,0.993448200572,0.));
#655 = DEFINITIONAL_REPRESENTATION('',(#656),#659);
#656 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#657,#658),.UNSPECIFIED.,.F.,.F.,
  (2,2),(-5.999999999062E-07,8.5754046),.PIECEWISE_BEZIER_KNOTS.);
#657 = CARTESIAN_POINT('',(1.496891141807,-18.5754046));
#658 = CARTESIAN_POINT('',(1.496891141807,-9.9999994));
#659 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#660 = ORIENTED_EDGE('',*,*,#661,.T.);
#661 = EDGE_CURVE('',#636,#662,#664,.T.);
#662 = VERTEX_POINT('',#663);
#663 = CARTESIAN_POINT('',(50.747818612774,126.51140045169,18.));
#664 = SURFACE_CURVE('',#665,(#670,#677),.PCURVE_S1.);
#665 = CIRCLE('',#666,4.316451884327);
#666 = AXIS2_PLACEMENT_3D('',#667,#668,#669);
#667 = CARTESIAN_POINT('',(51.614918,130.739863,18.));
#668 = DIRECTION('',(0.,0.,-1.));
#669 = DIRECTION('',(-0.807296537434,0.590145999433,0.));
#670 = PCURVE('',#611,#671);
#671 = DEFINITIONAL_REPRESENTATION('',(#672),#676);
#672 = LINE('',#673,#674);
#673 = CARTESIAN_POINT('',(8.793538282124,8.575404));
#674 = VECTOR('',#675,1.);
#675 = DIRECTION('',(-1.,-0.));
#676 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#677 = PCURVE('',#678,#683);
#678 = PLANE('',#679);
#679 = AXIS2_PLACEMENT_3D('',#680,#681,#682);
#680 = CARTESIAN_POINT('',(51.614918,130.739863,18.));
#681 = DIRECTION('',(0.,0.,1.));
#682 = DIRECTION('',(1.,0.,0.));
#683 = DEFINITIONAL_REPRESENTATION('',(#684),#692);
#684 = ( BOUNDED_CURVE() B_SPLINE_CURVE(2,(#685,#686,#687,#688,#689,#690
,#691),.UNSPECIFIED.,.T.,.F.) B_SPLINE_CURVE_WITH_KNOTS((1,2,2,2,2,1),(
    -2.094395102393,0.,2.094395102393,4.188790204786,6.28318530718,
8.377580409573),.UNSPECIFIED.) CURVE() GEOMETRIC_REPRESENTATION_ITEM() 
RATIONAL_B_SPLINE_CURVE((1.,0.5,1.,0.5,1.,0.5,1.)) REPRESENTATION_ITEM(
  '') );
#685 = CARTESIAN_POINT('',(-3.484656660219,2.54733681128));
#686 = CARTESIAN_POINT('',(0.927460120909,8.582939193713));
#687 = CARTESIAN_POINT('',(3.948386720674,1.744132785576));
#688 = CARTESIAN_POINT('',(6.969313320438,-5.09467362256));
#689 = CARTESIAN_POINT('',(-0.463730060454,-4.291469596857));
#690 = CARTESIAN_POINT('',(-7.896773441347,-3.488265571153));
#691 = CARTESIAN_POINT('',(-3.484656660219,2.54733681128));
#692 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#693 = ORIENTED_EDGE('',*,*,#694,.F.);
#694 = EDGE_CURVE('',#600,#662,#695,.T.);
#695 = SURFACE_CURVE('',#696,(#700,#706),.PCURVE_S1.);
#696 = LINE('',#697,#698);
#697 = CARTESIAN_POINT('',(50.747818612774,126.51140045169,9.424596));
#698 = VECTOR('',#699,1.);
#699 = DIRECTION('',(0.,0.,1.));
#700 = PCURVE('',#611,#701);
#701 = DEFINITIONAL_REPRESENTATION('',(#702),#705);
#702 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#703,#704),.UNSPECIFIED.,.F.,.F.,
  (2,2),(0.,8.575404),.PIECEWISE_BEZIER_KNOTS.);
#703 = CARTESIAN_POINT('',(4.510130339984,0.));
#704 = CARTESIAN_POINT('',(4.510130339984,8.575404));
#705 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#706 = PCURVE('',#707,#712);
#707 = PLANE('',#708);
#708 = AXIS2_PLACEMENT_3D('',#709,#710,#711);
#709 = CARTESIAN_POINT('',(51.008936,121.80569,28.));
#710 = DIRECTION('',(0.998464005442,5.540424023454E-02,0.));
#711 = DIRECTION('',(-5.540424023454E-02,0.998464005442,0.));
#712 = DEFINITIONAL_REPRESENTATION('',(#713),#716);
#713 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#714,#715),.UNSPECIFIED.,.F.,.F.,
  (2,2),(-5.999999999062E-07,8.5754046),.PIECEWISE_BEZIER_KNOTS.);
#714 = CARTESIAN_POINT('',(4.712949516507,-18.5754046));
#715 = CARTESIAN_POINT('',(4.712949516507,-9.9999994));
#716 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#717 = ADVANCED_FACE('',(#718,#787),#623,.F.);
#718 = FACE_BOUND('',#719,.F.);
#719 = EDGE_LOOP('',(#720,#721,#742,#768));
#720 = ORIENTED_EDGE('',*,*,#599,.T.);
#721 = ORIENTED_EDGE('',*,*,#722,.F.);
#722 = EDGE_CURVE('',#723,#602,#725,.T.);
#723 = VERTEX_POINT('',#724);
#724 = CARTESIAN_POINT('',(48.301331,131.800116,9.424596));
#725 = SURFACE_CURVE('',#726,(#730,#736),.PCURVE_S1.);
#726 = LINE('',#727,#728);
#727 = CARTESIAN_POINT('',(48.383157505698,131.08881002206,9.424596));
#728 = VECTOR('',#729,1.);
#729 = DIRECTION('',(-0.11428330053,0.993448200572,0.));
#730 = PCURVE('',#623,#731);
#731 = DEFINITIONAL_REPRESENTATION('',(#732),#735);
#732 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#733,#734),.UNSPECIFIED.,.F.,.F.,
  (2,2),(0.715997046977,3.993670590954),.PIECEWISE_BEZIER_KNOTS.);
#733 = CARTESIAN_POINT('',(-3.313587,1.060253));
#734 = CARTESIAN_POINT('',(-3.688170350664,4.316451884327));
#735 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#736 = PCURVE('',#650,#737);
#737 = DEFINITIONAL_REPRESENTATION('',(#738),#741);
#738 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#739,#740),.UNSPECIFIED.,.F.,.F.,
  (2,2),(0.715997046977,3.993670590954),.PIECEWISE_BEZIER_KNOTS.);
#739 = CARTESIAN_POINT('',(0.,-18.575404));
#740 = CARTESIAN_POINT('',(3.277673543977,-18.575404));
#741 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#742 = ORIENTED_EDGE('',*,*,#743,.F.);
#743 = EDGE_CURVE('',#744,#723,#746,.T.);
#744 = VERTEX_POINT('',#745);
#745 = CARTESIAN_POINT('',(50.449147,131.893892,9.424596));
#746 = SURFACE_CURVE('',#747,(#751,#757),.PCURVE_S1.);
#747 = LINE('',#748,#749);
#748 = CARTESIAN_POINT('',(51.005778313255,131.91819513305,9.424596));
#749 = VECTOR('',#750,1.);
#750 = DIRECTION('',(-0.999048214928,-4.361953975718E-02,0.));
#751 = PCURVE('',#623,#752);
#752 = DEFINITIONAL_REPRESENTATION('',(#753),#756);
#753 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#754,#755),.UNSPECIFIED.,.F.,.F.,
  (2,2),(0.557161611359,2.707023818114),.PIECEWISE_BEZIER_KNOTS.);
#754 = CARTESIAN_POINT('',(-1.165771,1.154029));
#755 = CARTESIAN_POINT('',(-3.313587,1.060253));
#756 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#757 = PCURVE('',#758,#763);
#758 = PLANE('',#759);
#759 = AXIS2_PLACEMENT_3D('',#760,#761,#762);
#760 = CARTESIAN_POINT('',(50.449147,131.893892,28.));
#761 = DIRECTION('',(-4.361953975718E-02,0.999048214928,0.));
#762 = DIRECTION('',(-0.999048214928,-4.361953975718E-02,0.));
#763 = DEFINITIONAL_REPRESENTATION('',(#764),#767);
#764 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#765,#766),.UNSPECIFIED.,.F.,.F.,
  (2,2),(0.557161611359,2.707023818114),.PIECEWISE_BEZIER_KNOTS.);
#765 = CARTESIAN_POINT('',(0.,-18.575404));
#766 = CARTESIAN_POINT('',(2.149862206755,-18.575404));
#767 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#768 = ORIENTED_EDGE('',*,*,#769,.F.);
#769 = EDGE_CURVE('',#600,#744,#770,.T.);
#770 = SURFACE_CURVE('',#771,(#775,#781),.PCURVE_S1.);
#771 = LINE('',#772,#773);
#772 = CARTESIAN_POINT('',(50.762750688412,126.24230299656,9.424596));
#773 = VECTOR('',#774,1.);
#774 = DIRECTION('',(-5.540424023454E-02,0.998464005442,0.));
#775 = PCURVE('',#623,#776);
#776 = DEFINITIONAL_REPRESENTATION('',(#777),#780);
#777 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#778,#779),.UNSPECIFIED.,.F.,.F.,
  (2,2),(0.181386728134,5.660283167574),.PIECEWISE_BEZIER_KNOTS.);
#778 = CARTESIAN_POINT('',(-0.862216905449,-4.316451884327));
#779 = CARTESIAN_POINT('',(-1.165771,1.154029));
#780 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#781 = PCURVE('',#707,#782);
#782 = DEFINITIONAL_REPRESENTATION('',(#783),#786);
#783 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#784,#785),.UNSPECIFIED.,.F.,.F.,
  (2,2),(0.181386728134,5.660283167574),.PIECEWISE_BEZIER_KNOTS.);
#784 = CARTESIAN_POINT('',(4.624824821428,-18.575404));
#785 = CARTESIAN_POINT('',(10.103721260868,-18.575404));
#786 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#787 = FACE_BOUND('',#788,.F.);
#788 = EDGE_LOOP('',(#789));
#789 = ORIENTED_EDGE('',*,*,#790,.F.);
#790 = EDGE_CURVE('',#791,#791,#793,.T.);
#791 = VERTEX_POINT('',#792);
#792 = CARTESIAN_POINT('',(51.889918,130.739863,9.424596));
#793 = SURFACE_CURVE('',#794,(#799,#806),.PCURVE_S1.);
#794 = CIRCLE('',#795,0.275);
#795 = AXIS2_PLACEMENT_3D('',#796,#797,#798);
#796 = CARTESIAN_POINT('',(51.614918,130.739863,9.424596));
#797 = DIRECTION('',(0.,0.,1.));
#798 = DIRECTION('',(1.,0.,0.));
#799 = PCURVE('',#623,#800);
#800 = DEFINITIONAL_REPRESENTATION('',(#801),#805);
#801 = CIRCLE('',#802,0.275);
#802 = AXIS2_PLACEMENT_2D('',#803,#804);
#803 = CARTESIAN_POINT('',(0.,0.));
#804 = DIRECTION('',(1.,0.));
#805 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#806 = PCURVE('',#807,#812);
#807 = CYLINDRICAL_SURFACE('',#808,0.275);
#808 = AXIS2_PLACEMENT_3D('',#809,#810,#811);
#809 = CARTESIAN_POINT('',(51.614918,130.739863,9.424596));
#810 = DIRECTION('',(0.,0.,1.));
#811 = DIRECTION('',(1.,0.,0.));
#812 = DEFINITIONAL_REPRESENTATION('',(#813),#817);
#813 = LINE('',#814,#815);
#814 = CARTESIAN_POINT('',(0.,0.));
#815 = VECTOR('',#816,1.);
#816 = DIRECTION('',(1.,0.));
#817 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#818 = ADVANCED_FACE('',(#819),#707,.F.);
#819 = FACE_BOUND('',#820,.F.);
#820 = EDGE_LOOP('',(#821,#844,#863,#864));
#821 = ORIENTED_EDGE('',*,*,#822,.F.);
#822 = EDGE_CURVE('',#823,#744,#825,.T.);
#823 = VERTEX_POINT('',#824);
#824 = CARTESIAN_POINT('',(50.449147,131.893892,18.));
#825 = SURFACE_CURVE('',#826,(#830,#837),.PCURVE_S1.);
#826 = LINE('',#827,#828);
#827 = CARTESIAN_POINT('',(50.449147,131.893892,28.));
#828 = VECTOR('',#829,1.);
#829 = DIRECTION('',(0.,0.,-1.));
#830 = PCURVE('',#707,#831);
#831 = DEFINITIONAL_REPRESENTATION('',(#832),#836);
#832 = LINE('',#833,#834);
#833 = CARTESIAN_POINT('',(10.103721260868,0.));
#834 = VECTOR('',#835,1.);
#835 = DIRECTION('',(0.,-1.));
#836 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#837 = PCURVE('',#758,#838);
#838 = DEFINITIONAL_REPRESENTATION('',(#839),#843);
#839 = LINE('',#840,#841);
#840 = CARTESIAN_POINT('',(0.,0.));
#841 = VECTOR('',#842,1.);
#842 = DIRECTION('',(-0.,-1.));
#843 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#844 = ORIENTED_EDGE('',*,*,#845,.F.);
#845 = EDGE_CURVE('',#662,#823,#846,.T.);
#846 = SURFACE_CURVE('',#847,(#851,#857),.PCURVE_S1.);
#847 = LINE('',#848,#849);
#848 = CARTESIAN_POINT('',(50.762750688412,126.24230299656,18.));
#849 = VECTOR('',#850,1.);
#850 = DIRECTION('',(-5.540424023454E-02,0.998464005442,0.));
#851 = PCURVE('',#707,#852);
#852 = DEFINITIONAL_REPRESENTATION('',(#853),#856);
#853 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#854,#855),.UNSPECIFIED.,.F.,.F.,
  (2,2),(0.181386728134,5.660283167574),.PIECEWISE_BEZIER_KNOTS.);
#854 = CARTESIAN_POINT('',(4.624824821428,-10.));
#855 = CARTESIAN_POINT('',(10.103721260868,-10.));
#856 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#857 = PCURVE('',#678,#858);
#858 = DEFINITIONAL_REPRESENTATION('',(#859),#862);
#859 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#860,#861),.UNSPECIFIED.,.F.,.F.,
  (2,2),(0.181386728134,5.660283167574),.PIECEWISE_BEZIER_KNOTS.);
#860 = CARTESIAN_POINT('',(-0.862216905449,-4.316451884327));
#861 = CARTESIAN_POINT('',(-1.165771,1.154029));
#862 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#863 = ORIENTED_EDGE('',*,*,#694,.F.);
#864 = ORIENTED_EDGE('',*,*,#769,.T.);
#865 = ADVANCED_FACE('',(#866,#910),#678,.T.);
#866 = FACE_BOUND('',#867,.T.);
#867 = EDGE_LOOP('',(#868,#869,#870,#891));
#868 = ORIENTED_EDGE('',*,*,#845,.F.);
#869 = ORIENTED_EDGE('',*,*,#661,.F.);
#870 = ORIENTED_EDGE('',*,*,#871,.F.);
#871 = EDGE_CURVE('',#872,#636,#874,.T.);
#872 = VERTEX_POINT('',#873);
#873 = CARTESIAN_POINT('',(48.301331,131.800116,18.));
#874 = SURFACE_CURVE('',#875,(#879,#885),.PCURVE_S1.);
#875 = LINE('',#876,#877);
#876 = CARTESIAN_POINT('',(48.383157505698,131.08881002206,18.));
#877 = VECTOR('',#878,1.);
#878 = DIRECTION('',(-0.11428330053,0.993448200572,0.));
#879 = PCURVE('',#678,#880);
#880 = DEFINITIONAL_REPRESENTATION('',(#881),#884);
#881 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#882,#883),.UNSPECIFIED.,.F.,.F.,
  (2,2),(0.715997046977,3.993670590954),.PIECEWISE_BEZIER_KNOTS.);
#882 = CARTESIAN_POINT('',(-3.313587,1.060253));
#883 = CARTESIAN_POINT('',(-3.688170350664,4.316451884327));
#884 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#885 = PCURVE('',#650,#886);
#886 = DEFINITIONAL_REPRESENTATION('',(#887),#890);
#887 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#888,#889),.UNSPECIFIED.,.F.,.F.,
  (2,2),(0.715997046977,3.993670590954),.PIECEWISE_BEZIER_KNOTS.);
#888 = CARTESIAN_POINT('',(0.,-10.));
#889 = CARTESIAN_POINT('',(3.277673543977,-10.));
#890 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#891 = ORIENTED_EDGE('',*,*,#892,.F.);
#892 = EDGE_CURVE('',#823,#872,#893,.T.);
#893 = SURFACE_CURVE('',#894,(#898,#904),.PCURVE_S1.);
#894 = LINE('',#895,#896);
#895 = CARTESIAN_POINT('',(51.005778313255,131.91819513305,18.));
#896 = VECTOR('',#897,1.);
#897 = DIRECTION('',(-0.999048214928,-4.361953975718E-02,0.));
#898 = PCURVE('',#678,#899);
#899 = DEFINITIONAL_REPRESENTATION('',(#900),#903);
#900 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#901,#902),.UNSPECIFIED.,.F.,.F.,
  (2,2),(0.557161611359,2.707023818114),.PIECEWISE_BEZIER_KNOTS.);
#901 = CARTESIAN_POINT('',(-1.165771,1.154029));
#902 = CARTESIAN_POINT('',(-3.313587,1.060253));
#903 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#904 = PCURVE('',#758,#905);
#905 = DEFINITIONAL_REPRESENTATION('',(#906),#909);
#906 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#907,#908),.UNSPECIFIED.,.F.,.F.,
  (2,2),(0.557161611359,2.707023818114),.PIECEWISE_BEZIER_KNOTS.);
#907 = CARTESIAN_POINT('',(0.,-10.));
#908 = CARTESIAN_POINT('',(2.149862206755,-10.));
#909 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#910 = FACE_BOUND('',#911,.T.);
#911 = EDGE_LOOP('',(#912));
#912 = ORIENTED_EDGE('',*,*,#913,.F.);
#913 = EDGE_CURVE('',#914,#914,#916,.T.);
#914 = VERTEX_POINT('',#915);
#915 = CARTESIAN_POINT('',(51.889918,130.739863,18.));
#916 = SURFACE_CURVE('',#917,(#922,#929),.PCURVE_S1.);
#917 = CIRCLE('',#918,0.275);
#918 = AXIS2_PLACEMENT_3D('',#919,#920,#921);
#919 = CARTESIAN_POINT('',(51.614918,130.739863,18.));
#920 = DIRECTION('',(0.,0.,1.));
#921 = DIRECTION('',(1.,0.,0.));
#922 = PCURVE('',#678,#923);
#923 = DEFINITIONAL_REPRESENTATION('',(#924),#928);
#924 = CIRCLE('',#925,0.275);
#925 = AXIS2_PLACEMENT_2D('',#926,#927);
#926 = CARTESIAN_POINT('',(0.,0.));
#927 = DIRECTION('',(1.,0.));
#928 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#929 = PCURVE('',#807,#930);
#930 = DEFINITIONAL_REPRESENTATION('',(#931),#935);
#931 = LINE('',#932,#933);
#932 = CARTESIAN_POINT('',(0.,8.575404));
#933 = VECTOR('',#934,1.);
#934 = DIRECTION('',(1.,0.));
#935 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#936 = ADVANCED_FACE('',(#937),#650,.F.);
#937 = FACE_BOUND('',#938,.F.);
#938 = EDGE_LOOP('',(#939,#940,#961,#962));
#939 = ORIENTED_EDGE('',*,*,#871,.F.);
#940 = ORIENTED_EDGE('',*,*,#941,.T.);
#941 = EDGE_CURVE('',#872,#723,#942,.T.);
#942 = SURFACE_CURVE('',#943,(#947,#954),.PCURVE_S1.);
#943 = LINE('',#944,#945);
#944 = CARTESIAN_POINT('',(48.301331,131.800116,28.));
#945 = VECTOR('',#946,1.);
#946 = DIRECTION('',(0.,0.,-1.));
#947 = PCURVE('',#650,#948);
#948 = DEFINITIONAL_REPRESENTATION('',(#949),#953);
#949 = LINE('',#950,#951);
#950 = CARTESIAN_POINT('',(0.,0.));
#951 = VECTOR('',#952,1.);
#952 = DIRECTION('',(0.,-1.));
#953 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#954 = PCURVE('',#758,#955);
#955 = DEFINITIONAL_REPRESENTATION('',(#956),#960);
#956 = LINE('',#957,#958);
#957 = CARTESIAN_POINT('',(2.149862206755,0.));
#958 = VECTOR('',#959,1.);
#959 = DIRECTION('',(-0.,-1.));
#960 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#961 = ORIENTED_EDGE('',*,*,#722,.T.);
#962 = ORIENTED_EDGE('',*,*,#635,.T.);
#963 = ADVANCED_FACE('',(#964),#758,.F.);
#964 = FACE_BOUND('',#965,.F.);
#965 = EDGE_LOOP('',(#966,#967,#968,#969));
#966 = ORIENTED_EDGE('',*,*,#892,.F.);
#967 = ORIENTED_EDGE('',*,*,#822,.T.);
#968 = ORIENTED_EDGE('',*,*,#743,.T.);
#969 = ORIENTED_EDGE('',*,*,#941,.F.);
#970 = ADVANCED_FACE('',(#971),#807,.F.);
#971 = FACE_BOUND('',#972,.F.);
#972 = EDGE_LOOP('',(#973,#974,#995,#996));
#973 = ORIENTED_EDGE('',*,*,#913,.F.);
#974 = ORIENTED_EDGE('',*,*,#975,.F.);
#975 = EDGE_CURVE('',#791,#914,#976,.T.);
#976 = SEAM_CURVE('',#977,(#981,#988),.PCURVE_S1.);
#977 = LINE('',#978,#979);
#978 = CARTESIAN_POINT('',(51.889918,130.739863,9.424596));
#979 = VECTOR('',#980,1.);
#980 = DIRECTION('',(0.,0.,1.));
#981 = PCURVE('',#807,#982);
#982 = DEFINITIONAL_REPRESENTATION('',(#983),#987);
#983 = LINE('',#984,#985);
#984 = CARTESIAN_POINT('',(6.28318530718,-0.));
#985 = VECTOR('',#986,1.);
#986 = DIRECTION('',(0.,1.));
#987 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#988 = PCURVE('',#807,#989);
#989 = DEFINITIONAL_REPRESENTATION('',(#990),#994);
#990 = LINE('',#991,#992);
#991 = CARTESIAN_POINT('',(0.,-0.));
#992 = VECTOR('',#993,1.);
#993 = DIRECTION('',(0.,1.));
#994 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#995 = ORIENTED_EDGE('',*,*,#790,.T.);
#996 = ORIENTED_EDGE('',*,*,#975,.T.);
#997 = ( GEOMETRIC_REPRESENTATION_CONTEXT(3) 
GLOBAL_UNCERTAINTY_ASSIGNED_CONTEXT((#1001)) 
GLOBAL_UNIT_ASSIGNED_CONTEXT((#998,#999,#1000)) REPRESENTATION_CONTEXT(
  'Context #1','3D Context with UNIT and UNCERTAINTY') );
#998 = ( LENGTH_UNIT() NAMED_UNIT(*) SI_UNIT(.MILLI.,.METRE.) );
#999 = ( NAMED_UNIT(*) PLANE_ANGLE_UNIT() SI_UNIT($,.RADIAN.) );
#1000 = ( NAMED_UNIT(*) SI_UNIT($,.STERADIAN.) SOLID_ANGLE_UNIT() );
#1001 = UNCERTAINTY_MEASURE_WITH_UNIT(LENGTH_MEASURE(1.E-07),#998,
  'distance_accuracy_value','confusion accuracy');
#1002 = CONTEXT_DEPENDENT_SHAPE_REPRESENTATION(#1003,#1005);
#1003 = ( REPRESENTATION_RELATIONSHIP('','',#592,#10) 
REPRESENTATION_RELATIONSHIP_WITH_TRANSFORMATION(#1004) 
SHAPE_REPRESENTATION_RELATIONSHIP() );
#1004 = ITEM_DEFINED_TRANSFORMATION('','',#11,#23);
#1005 = PRODUCT_DEFINITION_SHAPE('Placement','Placement of an item',
  #1006);
#1006 = NEXT_ASSEMBLY_USAGE_OCCURRENCE('41','=>[0:1:1:4]','',#5,#587,$);
#1007 = PRODUCT_RELATED_PRODUCT_CATEGORY('part',$,(#589));
#1008 = MECHANICAL_DESIGN_GEOMETRIC_PRESENTATION_REPRESENTATION('',(
    #1009),#139);
#1009 = STYLED_ITEM('color',(#1010),#41);
#1010 = PRESENTATION_STYLE_ASSIGNMENT((#1011));
#1011 = SURFACE_STYLE_USAGE(.BOTH.,#1012);
#1012 = SURFACE_SIDE_STYLE('',(#1013));
#1013 = SURFACE_STYLE_FILL_AREA(#1014);
#1014 = FILL_AREA_STYLE('',(#1015));
#1015 = FILL_AREA_STYLE_COLOUR('',#1016);
#1016 = DRAUGHTING_PRE_DEFINED_COLOUR('blue');
#1017 = MECHANICAL_DESIGN_GEOMETRIC_PRESENTATION_REPRESENTATION('',(
    #1018),#574);
#1018 = STYLED_ITEM('color',(#1019),#158);
#1019 = PRESENTATION_STYLE_ASSIGNMENT((#1020));
#1020 = SURFACE_STYLE_USAGE(.BOTH.,#1021);
#1021 = SURFACE_SIDE_STYLE('',(#1022));
#1022 = SURFACE_STYLE_FILL_AREA(#1023);
#1023 = FILL_AREA_STYLE('',(#1024));
#1024 = FILL_AREA_STYLE_COLOUR('',#1025);
#1025 = DRAUGHTING_PRE_DEFINED_COLOUR('green');
#1026 = MECHANICAL_DESIGN_GEOMETRIC_PRESENTATION_REPRESENTATION('',(
    #1027),#997);
#1027 = STYLED_ITEM('color',(#1028),#593);
#1028 = PRESENTATION_STYLE_ASSIGNMENT((#1029));
#1029 = SURFACE_STYLE_USAGE(.BOTH.,#1030);
#1030 = SURFACE_SIDE_STYLE('',(#1031));
#1031 = SURFACE_STYLE_FILL_AREA(#1032);
#1032 = FILL_AREA_STYLE('',(#1033));
#1033 = FILL_AREA_STYLE_COLOUR('',#1034);
#1034 = DRAUGHTING_PRE_DEFINED_COLOUR('red');
ENDSEC;
END-ISO-10303-21;
