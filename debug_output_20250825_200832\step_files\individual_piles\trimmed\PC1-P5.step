ISO-10303-21;
HEADER;
FILE_DESCRIPTION(('Open CASCADE Model'),'2;1');
FILE_NAME('Pile_PC1-P5','2025-08-25T20:08:32',('Author'),('Open CASCADE'
    ),'Open CASCADE STEP processor 7.8','build123d','Unknown');
FILE_SCHEMA(('AUTOMOTIVE_DESIGN { 1 0 10303 214 1 1 1 1 }'));
ENDSEC;
DATA;
#1 = APPLICATION_PROTOCOL_DEFINITION('international standard',
  'automotive_design',2000,#2);
#2 = APPLICATION_CONTEXT(
  'core data for automotive mechanical design processes');
#3 = SHAPE_DEFINITION_REPRESENTATION(#4,#10);
#4 = PRODUCT_DEFINITION_SHAPE('','',#5);
#5 = PRODUCT_DEFINITION('design','',#6,#9);
#6 = PRODUCT_DEFINITION_FORMATION('','',#7);
#7 = PRODUCT('Pile_PC1-P5','Pile_PC1-P5','',(#8));
#8 = PRODUCT_CONTEXT('',#2,'mechanical');
#9 = PRODUCT_DEFINITION_CONTEXT('part definition',#2,'design');
#10 = SHAPE_REPRESENTATION('',(#11,#15,#19,#23),#27);
#11 = AXIS2_PLACEMENT_3D('',#12,#13,#14);
#12 = CARTESIAN_POINT('',(0.,0.,0.));
#13 = DIRECTION('',(0.,0.,1.));
#14 = DIRECTION('',(1.,0.,-0.));
#15 = AXIS2_PLACEMENT_3D('',#16,#17,#18);
#16 = CARTESIAN_POINT('',(51.797763,127.444732,3.620574));
#17 = DIRECTION('',(0.,0.,1.));
#18 = DIRECTION('',(1.,0.,0.));
#19 = AXIS2_PLACEMENT_3D('',#20,#21,#22);
#20 = CARTESIAN_POINT('',(0.,0.,0.));
#21 = DIRECTION('',(0.,0.,1.));
#22 = DIRECTION('',(1.,0.,-0.));
#23 = AXIS2_PLACEMENT_3D('',#24,#25,#26);
#24 = CARTESIAN_POINT('',(0.,0.,0.));
#25 = DIRECTION('',(0.,0.,1.));
#26 = DIRECTION('',(1.,0.,-0.));
#27 = ( GEOMETRIC_REPRESENTATION_CONTEXT(3) 
GLOBAL_UNCERTAINTY_ASSIGNED_CONTEXT((#31)) GLOBAL_UNIT_ASSIGNED_CONTEXT(
(#28,#29,#30)) REPRESENTATION_CONTEXT('Context #1',
  '3D Context with UNIT and UNCERTAINTY') );
#28 = ( LENGTH_UNIT() NAMED_UNIT(*) SI_UNIT(.MILLI.,.METRE.) );
#29 = ( NAMED_UNIT(*) PLANE_ANGLE_UNIT() SI_UNIT($,.RADIAN.) );
#30 = ( NAMED_UNIT(*) SI_UNIT($,.STERADIAN.) SOLID_ANGLE_UNIT() );
#31 = UNCERTAINTY_MEASURE_WITH_UNIT(LENGTH_MEASURE(1.E-07),#28,
  'distance_accuracy_value','confusion accuracy');
#32 = PRODUCT_RELATED_PRODUCT_CATEGORY('part',$,(#7));
#33 = SHAPE_DEFINITION_REPRESENTATION(#34,#40);
#34 = PRODUCT_DEFINITION_SHAPE('','',#35);
#35 = PRODUCT_DEFINITION('design','',#36,#39);
#36 = PRODUCT_DEFINITION_FORMATION('','',#37);
#37 = PRODUCT('PC1-P5_Part1','PC1-P5_Part1','',(#38));
#38 = PRODUCT_CONTEXT('',#2,'mechanical');
#39 = PRODUCT_DEFINITION_CONTEXT('part definition',#2,'design');
#40 = ADVANCED_BREP_SHAPE_REPRESENTATION('',(#11,#41),#139);
#41 = MANIFOLD_SOLID_BREP('',#42);
#42 = CLOSED_SHELL('',(#43,#131,#135));
#43 = ADVANCED_FACE('',(#44),#57,.T.);
#44 = FACE_BOUND('',#45,.T.);
#45 = EDGE_LOOP('',(#46,#80,#103,#130));
#46 = ORIENTED_EDGE('',*,*,#47,.F.);
#47 = EDGE_CURVE('',#48,#48,#50,.T.);
#48 = VERTEX_POINT('',#49);
#49 = CARTESIAN_POINT('',(0.275,-6.73555739531E-17,14.379426));
#50 = SURFACE_CURVE('',#51,(#56,#68),.PCURVE_S1.);
#51 = CIRCLE('',#52,0.275);
#52 = AXIS2_PLACEMENT_3D('',#53,#54,#55);
#53 = CARTESIAN_POINT('',(0.,0.,14.379426));
#54 = DIRECTION('',(0.,0.,1.));
#55 = DIRECTION('',(1.,0.,-0.));
#56 = PCURVE('',#57,#62);
#57 = CYLINDRICAL_SURFACE('',#58,0.275);
#58 = AXIS2_PLACEMENT_3D('',#59,#60,#61);
#59 = CARTESIAN_POINT('',(0.,0.,0.));
#60 = DIRECTION('',(0.,0.,1.));
#61 = DIRECTION('',(1.,0.,-0.));
#62 = DEFINITIONAL_REPRESENTATION('',(#63),#67);
#63 = LINE('',#64,#65);
#64 = CARTESIAN_POINT('',(0.,14.379426));
#65 = VECTOR('',#66,1.);
#66 = DIRECTION('',(1.,0.));
#67 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#68 = PCURVE('',#69,#74);
#69 = PLANE('',#70);
#70 = AXIS2_PLACEMENT_3D('',#71,#72,#73);
#71 = CARTESIAN_POINT('',(0.,0.,14.379426));
#72 = DIRECTION('',(0.,0.,1.));
#73 = DIRECTION('',(1.,0.,-0.));
#74 = DEFINITIONAL_REPRESENTATION('',(#75),#79);
#75 = CIRCLE('',#76,0.275);
#76 = AXIS2_PLACEMENT_2D('',#77,#78);
#77 = CARTESIAN_POINT('',(0.,0.));
#78 = DIRECTION('',(1.,0.));
#79 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#80 = ORIENTED_EDGE('',*,*,#81,.F.);
#81 = EDGE_CURVE('',#82,#48,#84,.T.);
#82 = VERTEX_POINT('',#83);
#83 = CARTESIAN_POINT('',(0.275,-6.73555739531E-17,0.));
#84 = SEAM_CURVE('',#85,(#89,#96),.PCURVE_S1.);
#85 = LINE('',#86,#87);
#86 = CARTESIAN_POINT('',(0.275,-6.73555739531E-17,0.));
#87 = VECTOR('',#88,1.);
#88 = DIRECTION('',(0.,0.,1.));
#89 = PCURVE('',#57,#90);
#90 = DEFINITIONAL_REPRESENTATION('',(#91),#95);
#91 = LINE('',#92,#93);
#92 = CARTESIAN_POINT('',(6.28318530718,-0.));
#93 = VECTOR('',#94,1.);
#94 = DIRECTION('',(0.,1.));
#95 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#96 = PCURVE('',#57,#97);
#97 = DEFINITIONAL_REPRESENTATION('',(#98),#102);
#98 = LINE('',#99,#100);
#99 = CARTESIAN_POINT('',(0.,-0.));
#100 = VECTOR('',#101,1.);
#101 = DIRECTION('',(0.,1.));
#102 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#103 = ORIENTED_EDGE('',*,*,#104,.T.);
#104 = EDGE_CURVE('',#82,#82,#105,.T.);
#105 = SURFACE_CURVE('',#106,(#111,#118),.PCURVE_S1.);
#106 = CIRCLE('',#107,0.275);
#107 = AXIS2_PLACEMENT_3D('',#108,#109,#110);
#108 = CARTESIAN_POINT('',(0.,0.,0.));
#109 = DIRECTION('',(0.,0.,1.));
#110 = DIRECTION('',(1.,0.,-0.));
#111 = PCURVE('',#57,#112);
#112 = DEFINITIONAL_REPRESENTATION('',(#113),#117);
#113 = LINE('',#114,#115);
#114 = CARTESIAN_POINT('',(0.,0.));
#115 = VECTOR('',#116,1.);
#116 = DIRECTION('',(1.,0.));
#117 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#118 = PCURVE('',#119,#124);
#119 = PLANE('',#120);
#120 = AXIS2_PLACEMENT_3D('',#121,#122,#123);
#121 = CARTESIAN_POINT('',(0.,0.,0.));
#122 = DIRECTION('',(0.,0.,1.));
#123 = DIRECTION('',(1.,0.,-0.));
#124 = DEFINITIONAL_REPRESENTATION('',(#125),#129);
#125 = CIRCLE('',#126,0.275);
#126 = AXIS2_PLACEMENT_2D('',#127,#128);
#127 = CARTESIAN_POINT('',(0.,0.));
#128 = DIRECTION('',(1.,0.));
#129 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#130 = ORIENTED_EDGE('',*,*,#81,.T.);
#131 = ADVANCED_FACE('',(#132),#69,.T.);
#132 = FACE_BOUND('',#133,.T.);
#133 = EDGE_LOOP('',(#134));
#134 = ORIENTED_EDGE('',*,*,#47,.T.);
#135 = ADVANCED_FACE('',(#136),#119,.F.);
#136 = FACE_BOUND('',#137,.T.);
#137 = EDGE_LOOP('',(#138));
#138 = ORIENTED_EDGE('',*,*,#104,.F.);
#139 = ( GEOMETRIC_REPRESENTATION_CONTEXT(3) 
GLOBAL_UNCERTAINTY_ASSIGNED_CONTEXT((#143)) GLOBAL_UNIT_ASSIGNED_CONTEXT
((#140,#141,#142)) REPRESENTATION_CONTEXT('Context #1',
  '3D Context with UNIT and UNCERTAINTY') );
#140 = ( LENGTH_UNIT() NAMED_UNIT(*) SI_UNIT(.MILLI.,.METRE.) );
#141 = ( NAMED_UNIT(*) PLANE_ANGLE_UNIT() SI_UNIT($,.RADIAN.) );
#142 = ( NAMED_UNIT(*) SI_UNIT($,.STERADIAN.) SOLID_ANGLE_UNIT() );
#143 = UNCERTAINTY_MEASURE_WITH_UNIT(LENGTH_MEASURE(1.E-07),#140,
  'distance_accuracy_value','confusion accuracy');
#144 = CONTEXT_DEPENDENT_SHAPE_REPRESENTATION(#145,#147);
#145 = ( REPRESENTATION_RELATIONSHIP('','',#40,#10) 
REPRESENTATION_RELATIONSHIP_WITH_TRANSFORMATION(#146) 
SHAPE_REPRESENTATION_RELATIONSHIP() );
#146 = ITEM_DEFINED_TRANSFORMATION('','',#11,#15);
#147 = PRODUCT_DEFINITION_SHAPE('Placement','Placement of an item',#148
  );
#148 = NEXT_ASSEMBLY_USAGE_OCCURRENCE('93','=>[0:1:1:2]','',#5,#35,$);
#149 = PRODUCT_RELATED_PRODUCT_CATEGORY('part',$,(#37));
#150 = SHAPE_DEFINITION_REPRESENTATION(#151,#157);
#151 = PRODUCT_DEFINITION_SHAPE('','',#152);
#152 = PRODUCT_DEFINITION('design','',#153,#156);
#153 = PRODUCT_DEFINITION_FORMATION('','',#154);
#154 = PRODUCT('PC1-P5_Part2','PC1-P5_Part2','',(#155));
#155 = PRODUCT_CONTEXT('',#2,'mechanical');
#156 = PRODUCT_DEFINITION_CONTEXT('part definition',#2,'design');
#157 = ADVANCED_BREP_SHAPE_REPRESENTATION('',(#11,#158),#409);
#158 = MANIFOLD_SOLID_BREP('',#159);
#159 = CLOSED_SHELL('',(#160,#327,#377,#404));
#160 = ADVANCED_FACE('',(#161),#175,.T.);
#161 = FACE_BOUND('',#162,.T.);
#162 = EDGE_LOOP('',(#163,#193,#222,#278,#300,#301));
#163 = ORIENTED_EDGE('',*,*,#164,.T.);
#164 = EDGE_CURVE('',#165,#167,#169,.T.);
#165 = VERTEX_POINT('',#166);
#166 = CARTESIAN_POINT('',(52.072763,127.444732,3.620574));
#167 = VERTEX_POINT('',#168);
#168 = CARTESIAN_POINT('',(56.114214884327,127.444732,10.620574));
#169 = SEAM_CURVE('',#170,(#174,#186),.PCURVE_S1.);
#170 = LINE('',#171,#172);
#171 = CARTESIAN_POINT('',(52.072763,127.444732,3.620574));
#172 = VECTOR('',#173,1.);
#173 = DIRECTION('',(0.5,-1.224646799147E-16,0.866025403784));
#174 = PCURVE('',#175,#180);
#175 = CONICAL_SURFACE('',#176,0.275,0.523598775598);
#176 = AXIS2_PLACEMENT_3D('',#177,#178,#179);
#177 = CARTESIAN_POINT('',(51.797763,127.444732,3.620574));
#178 = DIRECTION('',(0.,0.,1.));
#179 = DIRECTION('',(1.,0.,0.));
#180 = DEFINITIONAL_REPRESENTATION('',(#181),#185);
#181 = LINE('',#182,#183);
#182 = CARTESIAN_POINT('',(6.28318530718,-0.));
#183 = VECTOR('',#184,1.);
#184 = DIRECTION('',(0.,1.));
#185 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#186 = PCURVE('',#175,#187);
#187 = DEFINITIONAL_REPRESENTATION('',(#188),#192);
#188 = LINE('',#189,#190);
#189 = CARTESIAN_POINT('',(0.,-0.));
#190 = VECTOR('',#191,1.);
#191 = DIRECTION('',(0.,1.));
#192 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#193 = ORIENTED_EDGE('',*,*,#194,.F.);
#194 = EDGE_CURVE('',#195,#167,#197,.T.);
#195 = VERTEX_POINT('',#196);
#196 = CARTESIAN_POINT('',(50.930663692252,123.2162694354,10.620574));
#197 = SURFACE_CURVE('',#198,(#203,#210),.PCURVE_S1.);
#198 = CIRCLE('',#199,4.316451884327);
#199 = AXIS2_PLACEMENT_3D('',#200,#201,#202);
#200 = CARTESIAN_POINT('',(51.797763,127.444732,10.620574));
#201 = DIRECTION('',(0.,0.,1.));
#202 = DIRECTION('',(1.,0.,0.));
#203 = PCURVE('',#175,#204);
#204 = DEFINITIONAL_REPRESENTATION('',(#205),#209);
#205 = LINE('',#206,#207);
#206 = CARTESIAN_POINT('',(0.,7.));
#207 = VECTOR('',#208,1.);
#208 = DIRECTION('',(1.,0.));
#209 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#210 = PCURVE('',#211,#216);
#211 = PLANE('',#212);
#212 = AXIS2_PLACEMENT_3D('',#213,#214,#215);
#213 = CARTESIAN_POINT('',(51.797763,127.444732,10.620574));
#214 = DIRECTION('',(0.,0.,1.));
#215 = DIRECTION('',(1.,0.,0.));
#216 = DEFINITIONAL_REPRESENTATION('',(#217),#221);
#217 = CIRCLE('',#218,4.316451884327);
#218 = AXIS2_PLACEMENT_2D('',#219,#220);
#219 = CARTESIAN_POINT('',(0.,0.));
#220 = DIRECTION('',(1.,0.));
#221 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#222 = ORIENTED_EDGE('',*,*,#223,.F.);
#223 = EDGE_CURVE('',#224,#195,#226,.T.);
#224 = VERTEX_POINT('',#225);
#225 = CARTESIAN_POINT('',(50.46815721806,131.55130055955,10.620574));
#226 = SURFACE_CURVE('',#227,(#232,#266),.PCURVE_S1.);
#227 = HYPERBOLA('',#228,1.905328987247,1.100042203682);
#228 = AXIS2_PLACEMENT_3D('',#229,#230,#231);
#229 = CARTESIAN_POINT('',(50.699410455156,127.38378499747,
    3.144260027919));
#230 = DIRECTION('',(0.998464005442,5.540424023454E-02,0.));
#231 = DIRECTION('',(-0.,0.,1.));
#232 = PCURVE('',#175,#233);
#233 = DEFINITIONAL_REPRESENTATION('',(#234),#265);
#234 = B_SPLINE_CURVE_WITH_KNOTS('',8,(#235,#236,#237,#238,#239,#240,
    #241,#242,#243,#244,#245,#246,#247,#248,#249,#250,#251,#252,#253,
    #254,#255,#256,#257,#258,#259,#260,#261,#262,#263,#264),
  .UNSPECIFIED.,.F.,.F.,(9,7,7,7,9),(-2.043585043983,-1.021792521991,
    -0.255448130498,0.894068456742,2.043585043983),.UNSPECIFIED.);
#235 = CARTESIAN_POINT('',(1.88392019749,7.));
#236 = CARTESIAN_POINT('',(1.916470511026,6.076624861121));
#237 = CARTESIAN_POINT('',(1.953615242995,5.292637417378));
#238 = CARTESIAN_POINT('',(1.9960588345,4.625085004212));
#239 = CARTESIAN_POINT('',(2.04460238642,4.055861434679));
#240 = CARTESIAN_POINT('',(2.100135705889,3.570522499041));
#241 = CARTESIAN_POINT('',(2.163602910691,3.157487695876));
#242 = CARTESIAN_POINT('',(2.235848408752,2.807505683108));
#243 = CARTESIAN_POINT('',(2.378301315839,2.292577378061));
#244 = CARTESIAN_POINT('',(2.44450303977,2.103246108879));
#245 = CARTESIAN_POINT('',(2.516015493445,1.942181378034));
#246 = CARTESIAN_POINT('',(2.592853173862,1.806910973652));
#247 = CARTESIAN_POINT('',(2.67481033825,1.695486265018));
#248 = CARTESIAN_POINT('',(2.761349011278,1.606420616361));
#249 = CARTESIAN_POINT('',(2.85156042425,1.538651135636));
#250 = CARTESIAN_POINT('',(3.08343642873,1.420820098332));
#251 = CARTESIAN_POINT('',(3.228274784649,1.39655489347));
#252 = CARTESIAN_POINT('',(3.374991349167,1.416498793948));
#253 = CARTESIAN_POINT('',(3.518625850975,1.480472799166));
#254 = CARTESIAN_POINT('',(3.653831981199,1.590195911812));
#255 = CARTESIAN_POINT('',(3.778180771564,1.749364150964));
#256 = CARTESIAN_POINT('',(3.890667920948,1.963920532406));
#257 = CARTESIAN_POINT('',(4.092051641345,2.521347075926));
#258 = CARTESIAN_POINT('',(4.180948405499,2.864216873609));
#259 = CARTESIAN_POINT('',(4.258098117641,3.280014433319));
#260 = CARTESIAN_POINT('',(4.324599077022,3.78032732669));
#261 = CARTESIAN_POINT('',(4.381824308015,4.380166479684));
#262 = CARTESIAN_POINT('',(4.431077790375,5.098818298292));
#263 = CARTESIAN_POINT('',(4.473511256052,5.961202968761));
#264 = CARTESIAN_POINT('',(4.51013035878,7.));
#265 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#266 = PCURVE('',#267,#272);
#267 = PLANE('',#268);
#268 = AXIS2_PLACEMENT_3D('',#269,#270,#271);
#269 = CARTESIAN_POINT('',(51.008936,121.80569,28.));
#270 = DIRECTION('',(0.998464005442,5.540424023454E-02,0.));
#271 = DIRECTION('',(-5.540424023454E-02,0.998464005442,0.));
#272 = DEFINITIONAL_REPRESENTATION('',(#273),#277);
#273 = ( BOUNDED_CURVE() B_SPLINE_CURVE(2,(#274,#275,#276),
.UNSPECIFIED.,.F.,.F.) B_SPLINE_CURVE_WITH_KNOTS((3,3),(-2.043585043983,
2.043585043983),.PIECEWISE_BEZIER_KNOTS.) CURVE() 
GEOMETRIC_REPRESENTATION_ITEM() RATIONAL_B_SPLINE_CURVE((1.,
3.923896619494,1.)) REPRESENTATION_ITEM('') );
#274 = CARTESIAN_POINT('',(9.760602792326,-17.379426));
#275 = CARTESIAN_POINT('',(5.586676101566,-24.3701693335));
#276 = CARTESIAN_POINT('',(1.412749410807,-17.379426));
#277 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#278 = ORIENTED_EDGE('',*,*,#279,.F.);
#279 = EDGE_CURVE('',#167,#224,#280,.T.);
#280 = SURFACE_CURVE('',#281,(#286,#293),.PCURVE_S1.);
#281 = CIRCLE('',#282,4.316451884327);
#282 = AXIS2_PLACEMENT_3D('',#283,#284,#285);
#283 = CARTESIAN_POINT('',(51.797763,127.444732,10.620574));
#284 = DIRECTION('',(0.,0.,1.));
#285 = DIRECTION('',(1.,0.,0.));
#286 = PCURVE('',#175,#287);
#287 = DEFINITIONAL_REPRESENTATION('',(#288),#292);
#288 = LINE('',#289,#290);
#289 = CARTESIAN_POINT('',(0.,7.));
#290 = VECTOR('',#291,1.);
#291 = DIRECTION('',(1.,0.));
#292 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#293 = PCURVE('',#211,#294);
#294 = DEFINITIONAL_REPRESENTATION('',(#295),#299);
#295 = CIRCLE('',#296,4.316451884327);
#296 = AXIS2_PLACEMENT_2D('',#297,#298);
#297 = CARTESIAN_POINT('',(0.,0.));
#298 = DIRECTION('',(1.,0.));
#299 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#300 = ORIENTED_EDGE('',*,*,#164,.F.);
#301 = ORIENTED_EDGE('',*,*,#302,.T.);
#302 = EDGE_CURVE('',#165,#165,#303,.T.);
#303 = SURFACE_CURVE('',#304,(#309,#316),.PCURVE_S1.);
#304 = CIRCLE('',#305,0.275);
#305 = AXIS2_PLACEMENT_3D('',#306,#307,#308);
#306 = CARTESIAN_POINT('',(51.797763,127.444732,3.620574));
#307 = DIRECTION('',(0.,0.,1.));
#308 = DIRECTION('',(1.,0.,0.));
#309 = PCURVE('',#175,#310);
#310 = DEFINITIONAL_REPRESENTATION('',(#311),#315);
#311 = LINE('',#312,#313);
#312 = CARTESIAN_POINT('',(0.,0.));
#313 = VECTOR('',#314,1.);
#314 = DIRECTION('',(1.,0.));
#315 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#316 = PCURVE('',#317,#322);
#317 = CYLINDRICAL_SURFACE('',#318,0.275);
#318 = AXIS2_PLACEMENT_3D('',#319,#320,#321);
#319 = CARTESIAN_POINT('',(51.797763,127.444732,3.620574));
#320 = DIRECTION('',(0.,0.,1.));
#321 = DIRECTION('',(1.,0.,0.));
#322 = DEFINITIONAL_REPRESENTATION('',(#323),#326);
#323 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#324,#325),.UNSPECIFIED.,.F.,.F.,
  (2,2),(0.,6.28318530718),.PIECEWISE_BEZIER_KNOTS.);
#324 = CARTESIAN_POINT('',(0.,0.));
#325 = CARTESIAN_POINT('',(6.28318530718,0.));
#326 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#327 = ADVANCED_FACE('',(#328),#317,.F.);
#328 = FACE_BOUND('',#329,.F.);
#329 = EDGE_LOOP('',(#330,#354,#375,#376));
#330 = ORIENTED_EDGE('',*,*,#331,.F.);
#331 = EDGE_CURVE('',#332,#332,#334,.T.);
#332 = VERTEX_POINT('',#333);
#333 = CARTESIAN_POINT('',(52.072763,127.444732,10.620574));
#334 = SURFACE_CURVE('',#335,(#340,#347),.PCURVE_S1.);
#335 = CIRCLE('',#336,0.275);
#336 = AXIS2_PLACEMENT_3D('',#337,#338,#339);
#337 = CARTESIAN_POINT('',(51.797763,127.444732,10.620574));
#338 = DIRECTION('',(0.,0.,1.));
#339 = DIRECTION('',(1.,0.,0.));
#340 = PCURVE('',#317,#341);
#341 = DEFINITIONAL_REPRESENTATION('',(#342),#346);
#342 = LINE('',#343,#344);
#343 = CARTESIAN_POINT('',(0.,7.));
#344 = VECTOR('',#345,1.);
#345 = DIRECTION('',(1.,0.));
#346 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#347 = PCURVE('',#211,#348);
#348 = DEFINITIONAL_REPRESENTATION('',(#349),#353);
#349 = CIRCLE('',#350,0.275);
#350 = AXIS2_PLACEMENT_2D('',#351,#352);
#351 = CARTESIAN_POINT('',(0.,0.));
#352 = DIRECTION('',(1.,0.));
#353 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#354 = ORIENTED_EDGE('',*,*,#355,.F.);
#355 = EDGE_CURVE('',#165,#332,#356,.T.);
#356 = SEAM_CURVE('',#357,(#361,#368),.PCURVE_S1.);
#357 = LINE('',#358,#359);
#358 = CARTESIAN_POINT('',(52.072763,127.444732,3.620574));
#359 = VECTOR('',#360,1.);
#360 = DIRECTION('',(0.,0.,1.));
#361 = PCURVE('',#317,#362);
#362 = DEFINITIONAL_REPRESENTATION('',(#363),#367);
#363 = LINE('',#364,#365);
#364 = CARTESIAN_POINT('',(6.28318530718,-0.));
#365 = VECTOR('',#366,1.);
#366 = DIRECTION('',(0.,1.));
#367 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#368 = PCURVE('',#317,#369);
#369 = DEFINITIONAL_REPRESENTATION('',(#370),#374);
#370 = LINE('',#371,#372);
#371 = CARTESIAN_POINT('',(0.,-0.));
#372 = VECTOR('',#373,1.);
#373 = DIRECTION('',(0.,1.));
#374 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#375 = ORIENTED_EDGE('',*,*,#302,.T.);
#376 = ORIENTED_EDGE('',*,*,#355,.T.);
#377 = ADVANCED_FACE('',(#378,#401),#211,.T.);
#378 = FACE_BOUND('',#379,.T.);
#379 = EDGE_LOOP('',(#380,#381,#382));
#380 = ORIENTED_EDGE('',*,*,#194,.T.);
#381 = ORIENTED_EDGE('',*,*,#279,.T.);
#382 = ORIENTED_EDGE('',*,*,#383,.F.);
#383 = EDGE_CURVE('',#195,#224,#384,.T.);
#384 = SURFACE_CURVE('',#385,(#389,#395),.PCURVE_S1.);
#385 = LINE('',#386,#387);
#386 = CARTESIAN_POINT('',(50.854173227578,124.59473749874,10.620574));
#387 = VECTOR('',#388,1.);
#388 = DIRECTION('',(-5.540424023454E-02,0.998464005442,0.));
#389 = PCURVE('',#211,#390);
#390 = DEFINITIONAL_REPRESENTATION('',(#391),#394);
#391 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#392,#393),.UNSPECIFIED.,.F.,.F.,
  (2,2),(-1.468713318731,7.177470941892),.PIECEWISE_BEZIER_KNOTS.);
#392 = CARTESIAN_POINT('',(-0.862216826875,-4.316451884327));
#393 = CARTESIAN_POINT('',(-1.341252096763,4.316451884327));
#394 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#395 = PCURVE('',#267,#396);
#396 = DEFINITIONAL_REPRESENTATION('',(#397),#400);
#397 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#398,#399),.UNSPECIFIED.,.F.,.F.,
  (2,2),(-1.468713318731,7.177470941892),.PIECEWISE_BEZIER_KNOTS.);
#398 = CARTESIAN_POINT('',(1.324624732052,-17.379426));
#399 = CARTESIAN_POINT('',(9.970808992675,-17.379426));
#400 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#401 = FACE_BOUND('',#402,.T.);
#402 = EDGE_LOOP('',(#403));
#403 = ORIENTED_EDGE('',*,*,#331,.F.);
#404 = ADVANCED_FACE('',(#405),#267,.F.);
#405 = FACE_BOUND('',#406,.F.);
#406 = EDGE_LOOP('',(#407,#408));
#407 = ORIENTED_EDGE('',*,*,#223,.F.);
#408 = ORIENTED_EDGE('',*,*,#383,.F.);
#409 = ( GEOMETRIC_REPRESENTATION_CONTEXT(3) 
GLOBAL_UNCERTAINTY_ASSIGNED_CONTEXT((#413)) GLOBAL_UNIT_ASSIGNED_CONTEXT
((#410,#411,#412)) REPRESENTATION_CONTEXT('Context #1',
  '3D Context with UNIT and UNCERTAINTY') );
#410 = ( LENGTH_UNIT() NAMED_UNIT(*) SI_UNIT(.MILLI.,.METRE.) );
#411 = ( NAMED_UNIT(*) PLANE_ANGLE_UNIT() SI_UNIT($,.RADIAN.) );
#412 = ( NAMED_UNIT(*) SI_UNIT($,.STERADIAN.) SOLID_ANGLE_UNIT() );
#413 = UNCERTAINTY_MEASURE_WITH_UNIT(LENGTH_MEASURE(1.E-07),#410,
  'distance_accuracy_value','confusion accuracy');
#414 = CONTEXT_DEPENDENT_SHAPE_REPRESENTATION(#415,#417);
#415 = ( REPRESENTATION_RELATIONSHIP('','',#157,#10) 
REPRESENTATION_RELATIONSHIP_WITH_TRANSFORMATION(#416) 
SHAPE_REPRESENTATION_RELATIONSHIP() );
#416 = ITEM_DEFINED_TRANSFORMATION('','',#11,#19);
#417 = PRODUCT_DEFINITION_SHAPE('Placement','Placement of an item',#418
  );
#418 = NEXT_ASSEMBLY_USAGE_OCCURRENCE('94','=>[0:1:1:3]','',#5,#152,$);
#419 = PRODUCT_RELATED_PRODUCT_CATEGORY('part',$,(#154));
#420 = SHAPE_DEFINITION_REPRESENTATION(#421,#427);
#421 = PRODUCT_DEFINITION_SHAPE('','',#422);
#422 = PRODUCT_DEFINITION('design','',#423,#426);
#423 = PRODUCT_DEFINITION_FORMATION('','',#424);
#424 = PRODUCT('PC1-P5_Part3','PC1-P5_Part3','',(#425));
#425 = PRODUCT_CONTEXT('',#2,'mechanical');
#426 = PRODUCT_DEFINITION_CONTEXT('part definition',#2,'design');
#427 = ADVANCED_BREP_SHAPE_REPRESENTATION('',(#11,#428),#684);
#428 = MANIFOLD_SOLID_BREP('',#429);
#429 = CLOSED_SHELL('',(#430,#547,#601,#626,#657));
#430 = ADVANCED_FACE('',(#431),#446,.T.);
#431 = FACE_BOUND('',#432,.T.);
#432 = EDGE_LOOP('',(#433,#469,#495,#528));
#433 = ORIENTED_EDGE('',*,*,#434,.T.);
#434 = EDGE_CURVE('',#435,#437,#439,.T.);
#435 = VERTEX_POINT('',#436);
#436 = CARTESIAN_POINT('',(50.930663692252,123.2162694354,10.620574));
#437 = VERTEX_POINT('',#438);
#438 = CARTESIAN_POINT('',(50.46815721806,131.55130055955,10.620574));
#439 = SURFACE_CURVE('',#440,(#445,#457),.PCURVE_S1.);
#440 = CIRCLE('',#441,4.316451884327);
#441 = AXIS2_PLACEMENT_3D('',#442,#443,#444);
#442 = CARTESIAN_POINT('',(51.797763,127.444732,10.620574));
#443 = DIRECTION('',(0.,0.,1.));
#444 = DIRECTION('',(-0.200882421717,-0.979615359539,0.));
#445 = PCURVE('',#446,#451);
#446 = CYLINDRICAL_SURFACE('',#447,4.316451884327);
#447 = AXIS2_PLACEMENT_3D('',#448,#449,#450);
#448 = CARTESIAN_POINT('',(51.797763,127.444732,10.620574));
#449 = DIRECTION('',(0.,0.,1.));
#450 = DIRECTION('',(1.,0.,0.));
#451 = DEFINITIONAL_REPRESENTATION('',(#452),#456);
#452 = LINE('',#453,#454);
#453 = CARTESIAN_POINT('',(4.51013035878,0.));
#454 = VECTOR('',#455,1.);
#455 = DIRECTION('',(1.,0.));
#456 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#457 = PCURVE('',#458,#463);
#458 = PLANE('',#459);
#459 = AXIS2_PLACEMENT_3D('',#460,#461,#462);
#460 = CARTESIAN_POINT('',(51.797763,127.444732,10.620574));
#461 = DIRECTION('',(0.,0.,1.));
#462 = DIRECTION('',(1.,0.,0.));
#463 = DEFINITIONAL_REPRESENTATION('',(#464),#468);
#464 = CIRCLE('',#465,4.316451884327);
#465 = AXIS2_PLACEMENT_2D('',#466,#467);
#466 = CARTESIAN_POINT('',(0.,0.));
#467 = DIRECTION('',(-0.200882421717,-0.979615359539));
#468 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#469 = ORIENTED_EDGE('',*,*,#470,.T.);
#470 = EDGE_CURVE('',#437,#471,#473,.T.);
#471 = VERTEX_POINT('',#472);
#472 = CARTESIAN_POINT('',(50.46815721806,131.55130055955,18.));
#473 = SURFACE_CURVE('',#474,(#478,#484),.PCURVE_S1.);
#474 = LINE('',#475,#476);
#475 = CARTESIAN_POINT('',(50.46815721806,131.55130055955,10.620574));
#476 = VECTOR('',#477,1.);
#477 = DIRECTION('',(0.,0.,1.));
#478 = PCURVE('',#446,#479);
#479 = DEFINITIONAL_REPRESENTATION('',(#480),#483);
#480 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#481,#482),.UNSPECIFIED.,.F.,.F.,
  (2,2),(0.,7.379426),.PIECEWISE_BEZIER_KNOTS.);
#481 = CARTESIAN_POINT('',(8.167105504669,0.));
#482 = CARTESIAN_POINT('',(8.167105504669,7.379426));
#483 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#484 = PCURVE('',#485,#490);
#485 = PLANE('',#486);
#486 = AXIS2_PLACEMENT_3D('',#487,#488,#489);
#487 = CARTESIAN_POINT('',(51.008936,121.80569,28.));
#488 = DIRECTION('',(0.998464005442,5.540424023454E-02,0.));
#489 = DIRECTION('',(-5.540424023454E-02,0.998464005442,0.));
#490 = DEFINITIONAL_REPRESENTATION('',(#491),#494);
#491 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#492,#493),.UNSPECIFIED.,.F.,.F.,
  (2,2),(-5.999999999062E-07,7.3794266),.PIECEWISE_BEZIER_KNOTS.);
#492 = CARTESIAN_POINT('',(9.760602792326,-17.3794266));
#493 = CARTESIAN_POINT('',(9.760602792326,-9.9999994));
#494 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#495 = ORIENTED_EDGE('',*,*,#496,.T.);
#496 = EDGE_CURVE('',#471,#497,#499,.T.);
#497 = VERTEX_POINT('',#498);
#498 = CARTESIAN_POINT('',(50.930663692252,123.2162694354,18.));
#499 = SURFACE_CURVE('',#500,(#505,#512),.PCURVE_S1.);
#500 = CIRCLE('',#501,4.316451884327);
#501 = AXIS2_PLACEMENT_3D('',#502,#503,#504);
#502 = CARTESIAN_POINT('',(51.797763,127.444732,18.));
#503 = DIRECTION('',(0.,0.,-1.));
#504 = DIRECTION('',(-0.308032110069,0.951375960999,0.));
#505 = PCURVE('',#446,#506);
#506 = DEFINITIONAL_REPRESENTATION('',(#507),#511);
#507 = LINE('',#508,#509);
#508 = CARTESIAN_POINT('',(8.167105504669,7.379426));
#509 = VECTOR('',#510,1.);
#510 = DIRECTION('',(-1.,-0.));
#511 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#512 = PCURVE('',#513,#518);
#513 = PLANE('',#514);
#514 = AXIS2_PLACEMENT_3D('',#515,#516,#517);
#515 = CARTESIAN_POINT('',(51.797763,127.444732,18.));
#516 = DIRECTION('',(0.,0.,1.));
#517 = DIRECTION('',(1.,0.,0.));
#518 = DEFINITIONAL_REPRESENTATION('',(#519),#527);
#519 = ( BOUNDED_CURVE() B_SPLINE_CURVE(2,(#520,#521,#522,#523,#524,#525
,#526),.UNSPECIFIED.,.T.,.F.) B_SPLINE_CURVE_WITH_KNOTS((1,2,2,2,2,1),(
    -2.094395102393,0.,2.094395102393,4.188790204786,6.28318530718,
8.377580409573),.UNSPECIFIED.) CURVE() GEOMETRIC_REPRESENTATION_ITEM() 
RATIONAL_B_SPLINE_CURVE((1.,0.5,1.,0.5,1.,0.5,1.)) REPRESENTATION_ITEM(
  '') );
#520 = CARTESIAN_POINT('',(-1.32960578194,4.106568559557));
#521 = CARTESIAN_POINT('',(5.783179607978,6.409513327915));
#522 = CARTESIAN_POINT('',(4.221195585929,-0.9018118956));
#523 = CARTESIAN_POINT('',(2.65921156388,-8.213137119115));
#524 = CARTESIAN_POINT('',(-2.891589803989,-3.204756663957));
#525 = CARTESIAN_POINT('',(-8.442391171858,1.8036237912));
#526 = CARTESIAN_POINT('',(-1.32960578194,4.106568559557));
#527 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#528 = ORIENTED_EDGE('',*,*,#529,.F.);
#529 = EDGE_CURVE('',#435,#497,#530,.T.);
#530 = SURFACE_CURVE('',#531,(#535,#541),.PCURVE_S1.);
#531 = LINE('',#532,#533);
#532 = CARTESIAN_POINT('',(50.930663692252,123.2162694354,10.620574));
#533 = VECTOR('',#534,1.);
#534 = DIRECTION('',(0.,0.,1.));
#535 = PCURVE('',#446,#536);
#536 = DEFINITIONAL_REPRESENTATION('',(#537),#540);
#537 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#538,#539),.UNSPECIFIED.,.F.,.F.,
  (2,2),(0.,7.379426),.PIECEWISE_BEZIER_KNOTS.);
#538 = CARTESIAN_POINT('',(4.51013035878,0.));
#539 = CARTESIAN_POINT('',(4.51013035878,7.379426));
#540 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#541 = PCURVE('',#485,#542);
#542 = DEFINITIONAL_REPRESENTATION('',(#543),#546);
#543 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#544,#545),.UNSPECIFIED.,.F.,.F.,
  (2,2),(-5.999999999062E-07,7.3794266),.PIECEWISE_BEZIER_KNOTS.);
#544 = CARTESIAN_POINT('',(1.412749410807,-17.3794266));
#545 = CARTESIAN_POINT('',(1.412749410807,-9.9999994));
#546 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#547 = ADVANCED_FACE('',(#548,#570),#458,.F.);
#548 = FACE_BOUND('',#549,.F.);
#549 = EDGE_LOOP('',(#550,#551));
#550 = ORIENTED_EDGE('',*,*,#434,.T.);
#551 = ORIENTED_EDGE('',*,*,#552,.F.);
#552 = EDGE_CURVE('',#435,#437,#553,.T.);
#553 = SURFACE_CURVE('',#554,(#558,#564),.PCURVE_S1.);
#554 = LINE('',#555,#556);
#555 = CARTESIAN_POINT('',(50.854173227578,124.59473749874,10.620574));
#556 = VECTOR('',#557,1.);
#557 = DIRECTION('',(-5.540424023454E-02,0.998464005442,0.));
#558 = PCURVE('',#458,#559);
#559 = DEFINITIONAL_REPRESENTATION('',(#560),#563);
#560 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#561,#562),.UNSPECIFIED.,.F.,.F.,
  (2,2),(-1.468713318731,7.177470941892),.PIECEWISE_BEZIER_KNOTS.);
#561 = CARTESIAN_POINT('',(-0.862216826875,-4.316451884327));
#562 = CARTESIAN_POINT('',(-1.341252096763,4.316451884327));
#563 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#564 = PCURVE('',#485,#565);
#565 = DEFINITIONAL_REPRESENTATION('',(#566),#569);
#566 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#567,#568),.UNSPECIFIED.,.F.,.F.,
  (2,2),(-1.468713318731,7.177470941892),.PIECEWISE_BEZIER_KNOTS.);
#567 = CARTESIAN_POINT('',(1.324624732052,-17.379426));
#568 = CARTESIAN_POINT('',(9.970808992675,-17.379426));
#569 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#570 = FACE_BOUND('',#571,.F.);
#571 = EDGE_LOOP('',(#572));
#572 = ORIENTED_EDGE('',*,*,#573,.F.);
#573 = EDGE_CURVE('',#574,#574,#576,.T.);
#574 = VERTEX_POINT('',#575);
#575 = CARTESIAN_POINT('',(52.072763,127.444732,10.620574));
#576 = SURFACE_CURVE('',#577,(#582,#589),.PCURVE_S1.);
#577 = CIRCLE('',#578,0.275);
#578 = AXIS2_PLACEMENT_3D('',#579,#580,#581);
#579 = CARTESIAN_POINT('',(51.797763,127.444732,10.620574));
#580 = DIRECTION('',(0.,0.,1.));
#581 = DIRECTION('',(1.,0.,0.));
#582 = PCURVE('',#458,#583);
#583 = DEFINITIONAL_REPRESENTATION('',(#584),#588);
#584 = CIRCLE('',#585,0.275);
#585 = AXIS2_PLACEMENT_2D('',#586,#587);
#586 = CARTESIAN_POINT('',(0.,0.));
#587 = DIRECTION('',(1.,0.));
#588 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#589 = PCURVE('',#590,#595);
#590 = CYLINDRICAL_SURFACE('',#591,0.275);
#591 = AXIS2_PLACEMENT_3D('',#592,#593,#594);
#592 = CARTESIAN_POINT('',(51.797763,127.444732,10.620574));
#593 = DIRECTION('',(0.,0.,1.));
#594 = DIRECTION('',(1.,0.,0.));
#595 = DEFINITIONAL_REPRESENTATION('',(#596),#600);
#596 = LINE('',#597,#598);
#597 = CARTESIAN_POINT('',(0.,0.));
#598 = VECTOR('',#599,1.);
#599 = DIRECTION('',(1.,0.));
#600 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#601 = ADVANCED_FACE('',(#602),#485,.F.);
#602 = FACE_BOUND('',#603,.F.);
#603 = EDGE_LOOP('',(#604,#605,#606,#607));
#604 = ORIENTED_EDGE('',*,*,#529,.F.);
#605 = ORIENTED_EDGE('',*,*,#552,.T.);
#606 = ORIENTED_EDGE('',*,*,#470,.T.);
#607 = ORIENTED_EDGE('',*,*,#608,.F.);
#608 = EDGE_CURVE('',#497,#471,#609,.T.);
#609 = SURFACE_CURVE('',#610,(#614,#620),.PCURVE_S1.);
#610 = LINE('',#611,#612);
#611 = CARTESIAN_POINT('',(50.854173227578,124.59473749874,18.));
#612 = VECTOR('',#613,1.);
#613 = DIRECTION('',(-5.540424023454E-02,0.998464005442,0.));
#614 = PCURVE('',#485,#615);
#615 = DEFINITIONAL_REPRESENTATION('',(#616),#619);
#616 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#617,#618),.UNSPECIFIED.,.F.,.F.,
  (2,2),(-1.468713318731,7.177470941892),.PIECEWISE_BEZIER_KNOTS.);
#617 = CARTESIAN_POINT('',(1.324624732052,-10.));
#618 = CARTESIAN_POINT('',(9.970808992675,-10.));
#619 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#620 = PCURVE('',#513,#621);
#621 = DEFINITIONAL_REPRESENTATION('',(#622),#625);
#622 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#623,#624),.UNSPECIFIED.,.F.,.F.,
  (2,2),(-1.468713318731,7.177470941892),.PIECEWISE_BEZIER_KNOTS.);
#623 = CARTESIAN_POINT('',(-0.862216826875,-4.316451884327));
#624 = CARTESIAN_POINT('',(-1.341252096763,4.316451884327));
#625 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#626 = ADVANCED_FACE('',(#627,#631),#513,.T.);
#627 = FACE_BOUND('',#628,.T.);
#628 = EDGE_LOOP('',(#629,#630));
#629 = ORIENTED_EDGE('',*,*,#608,.F.);
#630 = ORIENTED_EDGE('',*,*,#496,.F.);
#631 = FACE_BOUND('',#632,.T.);
#632 = EDGE_LOOP('',(#633));
#633 = ORIENTED_EDGE('',*,*,#634,.F.);
#634 = EDGE_CURVE('',#635,#635,#637,.T.);
#635 = VERTEX_POINT('',#636);
#636 = CARTESIAN_POINT('',(52.072763,127.444732,18.));
#637 = SURFACE_CURVE('',#638,(#643,#650),.PCURVE_S1.);
#638 = CIRCLE('',#639,0.275);
#639 = AXIS2_PLACEMENT_3D('',#640,#641,#642);
#640 = CARTESIAN_POINT('',(51.797763,127.444732,18.));
#641 = DIRECTION('',(0.,0.,1.));
#642 = DIRECTION('',(1.,0.,0.));
#643 = PCURVE('',#513,#644);
#644 = DEFINITIONAL_REPRESENTATION('',(#645),#649);
#645 = CIRCLE('',#646,0.275);
#646 = AXIS2_PLACEMENT_2D('',#647,#648);
#647 = CARTESIAN_POINT('',(0.,0.));
#648 = DIRECTION('',(1.,0.));
#649 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#650 = PCURVE('',#590,#651);
#651 = DEFINITIONAL_REPRESENTATION('',(#652),#656);
#652 = LINE('',#653,#654);
#653 = CARTESIAN_POINT('',(0.,7.379426));
#654 = VECTOR('',#655,1.);
#655 = DIRECTION('',(1.,0.));
#656 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#657 = ADVANCED_FACE('',(#658),#590,.F.);
#658 = FACE_BOUND('',#659,.F.);
#659 = EDGE_LOOP('',(#660,#661,#682,#683));
#660 = ORIENTED_EDGE('',*,*,#634,.F.);
#661 = ORIENTED_EDGE('',*,*,#662,.F.);
#662 = EDGE_CURVE('',#574,#635,#663,.T.);
#663 = SEAM_CURVE('',#664,(#668,#675),.PCURVE_S1.);
#664 = LINE('',#665,#666);
#665 = CARTESIAN_POINT('',(52.072763,127.444732,10.620574));
#666 = VECTOR('',#667,1.);
#667 = DIRECTION('',(0.,0.,1.));
#668 = PCURVE('',#590,#669);
#669 = DEFINITIONAL_REPRESENTATION('',(#670),#674);
#670 = LINE('',#671,#672);
#671 = CARTESIAN_POINT('',(6.28318530718,-0.));
#672 = VECTOR('',#673,1.);
#673 = DIRECTION('',(0.,1.));
#674 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#675 = PCURVE('',#590,#676);
#676 = DEFINITIONAL_REPRESENTATION('',(#677),#681);
#677 = LINE('',#678,#679);
#678 = CARTESIAN_POINT('',(0.,-0.));
#679 = VECTOR('',#680,1.);
#680 = DIRECTION('',(0.,1.));
#681 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#682 = ORIENTED_EDGE('',*,*,#573,.T.);
#683 = ORIENTED_EDGE('',*,*,#662,.T.);
#684 = ( GEOMETRIC_REPRESENTATION_CONTEXT(3) 
GLOBAL_UNCERTAINTY_ASSIGNED_CONTEXT((#688)) GLOBAL_UNIT_ASSIGNED_CONTEXT
((#685,#686,#687)) REPRESENTATION_CONTEXT('Context #1',
  '3D Context with UNIT and UNCERTAINTY') );
#685 = ( LENGTH_UNIT() NAMED_UNIT(*) SI_UNIT(.MILLI.,.METRE.) );
#686 = ( NAMED_UNIT(*) PLANE_ANGLE_UNIT() SI_UNIT($,.RADIAN.) );
#687 = ( NAMED_UNIT(*) SI_UNIT($,.STERADIAN.) SOLID_ANGLE_UNIT() );
#688 = UNCERTAINTY_MEASURE_WITH_UNIT(LENGTH_MEASURE(1.E-07),#685,
  'distance_accuracy_value','confusion accuracy');
#689 = CONTEXT_DEPENDENT_SHAPE_REPRESENTATION(#690,#692);
#690 = ( REPRESENTATION_RELATIONSHIP('','',#427,#10) 
REPRESENTATION_RELATIONSHIP_WITH_TRANSFORMATION(#691) 
SHAPE_REPRESENTATION_RELATIONSHIP() );
#691 = ITEM_DEFINED_TRANSFORMATION('','',#11,#23);
#692 = PRODUCT_DEFINITION_SHAPE('Placement','Placement of an item',#693
  );
#693 = NEXT_ASSEMBLY_USAGE_OCCURRENCE('95','=>[0:1:1:4]','',#5,#422,$);
#694 = PRODUCT_RELATED_PRODUCT_CATEGORY('part',$,(#424));
#695 = MECHANICAL_DESIGN_GEOMETRIC_PRESENTATION_REPRESENTATION('',(#696)
  ,#139);
#696 = STYLED_ITEM('color',(#697),#41);
#697 = PRESENTATION_STYLE_ASSIGNMENT((#698));
#698 = SURFACE_STYLE_USAGE(.BOTH.,#699);
#699 = SURFACE_SIDE_STYLE('',(#700));
#700 = SURFACE_STYLE_FILL_AREA(#701);
#701 = FILL_AREA_STYLE('',(#702));
#702 = FILL_AREA_STYLE_COLOUR('',#703);
#703 = DRAUGHTING_PRE_DEFINED_COLOUR('blue');
#704 = MECHANICAL_DESIGN_GEOMETRIC_PRESENTATION_REPRESENTATION('',(#705)
  ,#684);
#705 = STYLED_ITEM('color',(#706),#428);
#706 = PRESENTATION_STYLE_ASSIGNMENT((#707));
#707 = SURFACE_STYLE_USAGE(.BOTH.,#708);
#708 = SURFACE_SIDE_STYLE('',(#709));
#709 = SURFACE_STYLE_FILL_AREA(#710);
#710 = FILL_AREA_STYLE('',(#711));
#711 = FILL_AREA_STYLE_COLOUR('',#712);
#712 = DRAUGHTING_PRE_DEFINED_COLOUR('red');
#713 = MECHANICAL_DESIGN_GEOMETRIC_PRESENTATION_REPRESENTATION('',(#714)
  ,#409);
#714 = STYLED_ITEM('color',(#715),#158);
#715 = PRESENTATION_STYLE_ASSIGNMENT((#716));
#716 = SURFACE_STYLE_USAGE(.BOTH.,#717);
#717 = SURFACE_SIDE_STYLE('',(#718));
#718 = SURFACE_STYLE_FILL_AREA(#719);
#719 = FILL_AREA_STYLE('',(#720));
#720 = FILL_AREA_STYLE_COLOUR('',#721);
#721 = DRAUGHTING_PRE_DEFINED_COLOUR('green');
ENDSEC;
END-ISO-10303-21;
