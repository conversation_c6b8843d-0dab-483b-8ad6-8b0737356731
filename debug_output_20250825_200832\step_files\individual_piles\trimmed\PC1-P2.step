ISO-10303-21;
HEADER;
FILE_DESCRIPTION(('Open CASCADE Model'),'2;1');
FILE_NAME('Pile_PC1-P2','2025-08-25T20:08:32',('Author'),('Open CASCADE'
    ),'Open CASCADE STEP processor 7.8','build123d','Unknown');
FILE_SCHEMA(('AUTOMOTIVE_DESIGN { 1 0 10303 214 1 1 1 1 }'));
ENDSEC;
DATA;
#1 = APPLICATION_PROTOCOL_DEFINITION('international standard',
  'automotive_design',2000,#2);
#2 = APPLICATION_CONTEXT(
  'core data for automotive mechanical design processes');
#3 = SHAPE_DEFINITION_REPRESENTATION(#4,#10);
#4 = PRODUCT_DEFINITION_SHAPE('','',#5);
#5 = PRODUCT_DEFINITION('design','',#6,#9);
#6 = PRODUCT_DEFINITION_FORMATION('','',#7);
#7 = PRODUCT('Pile_PC1-P2','Pile_PC1-P2','',(#8));
#8 = PRODUCT_CONTEXT('',#2,'mechanical');
#9 = PRODUCT_DEFINITION_CONTEXT('part definition',#2,'design');
#10 = SHAPE_REPRESENTATION('',(#11,#15,#19,#23),#27);
#11 = AXIS2_PLACEMENT_3D('',#12,#13,#14);
#12 = CARTESIAN_POINT('',(0.,0.,0.));
#13 = DIRECTION('',(0.,0.,1.));
#14 = DIRECTION('',(1.,0.,-0.));
#15 = AXIS2_PLACEMENT_3D('',#16,#17,#18);
#16 = CARTESIAN_POINT('',(51.53192,135.691161,0.97226));
#17 = DIRECTION('',(0.,0.,1.));
#18 = DIRECTION('',(1.,0.,0.));
#19 = AXIS2_PLACEMENT_3D('',#20,#21,#22);
#20 = CARTESIAN_POINT('',(0.,0.,0.));
#21 = DIRECTION('',(0.,0.,1.));
#22 = DIRECTION('',(1.,0.,-0.));
#23 = AXIS2_PLACEMENT_3D('',#24,#25,#26);
#24 = CARTESIAN_POINT('',(0.,0.,0.));
#25 = DIRECTION('',(0.,0.,1.));
#26 = DIRECTION('',(1.,0.,-0.));
#27 = ( GEOMETRIC_REPRESENTATION_CONTEXT(3) 
GLOBAL_UNCERTAINTY_ASSIGNED_CONTEXT((#31)) GLOBAL_UNIT_ASSIGNED_CONTEXT(
(#28,#29,#30)) REPRESENTATION_CONTEXT('Context #1',
  '3D Context with UNIT and UNCERTAINTY') );
#28 = ( LENGTH_UNIT() NAMED_UNIT(*) SI_UNIT(.MILLI.,.METRE.) );
#29 = ( NAMED_UNIT(*) PLANE_ANGLE_UNIT() SI_UNIT($,.RADIAN.) );
#30 = ( NAMED_UNIT(*) SI_UNIT($,.STERADIAN.) SOLID_ANGLE_UNIT() );
#31 = UNCERTAINTY_MEASURE_WITH_UNIT(LENGTH_MEASURE(1.E-07),#28,
  'distance_accuracy_value','confusion accuracy');
#32 = PRODUCT_RELATED_PRODUCT_CATEGORY('part',$,(#7));
#33 = SHAPE_DEFINITION_REPRESENTATION(#34,#40);
#34 = PRODUCT_DEFINITION_SHAPE('','',#35);
#35 = PRODUCT_DEFINITION('design','',#36,#39);
#36 = PRODUCT_DEFINITION_FORMATION('','',#37);
#37 = PRODUCT('PC1-P2_Part1','PC1-P2_Part1','',(#38));
#38 = PRODUCT_CONTEXT('',#2,'mechanical');
#39 = PRODUCT_DEFINITION_CONTEXT('part definition',#2,'design');
#40 = ADVANCED_BREP_SHAPE_REPRESENTATION('',(#11,#41),#139);
#41 = MANIFOLD_SOLID_BREP('',#42);
#42 = CLOSED_SHELL('',(#43,#131,#135));
#43 = ADVANCED_FACE('',(#44),#57,.T.);
#44 = FACE_BOUND('',#45,.T.);
#45 = EDGE_LOOP('',(#46,#80,#103,#130));
#46 = ORIENTED_EDGE('',*,*,#47,.F.);
#47 = EDGE_CURVE('',#48,#48,#50,.T.);
#48 = VERTEX_POINT('',#49);
#49 = CARTESIAN_POINT('',(0.275,-6.73555739531E-17,17.02774));
#50 = SURFACE_CURVE('',#51,(#56,#68),.PCURVE_S1.);
#51 = CIRCLE('',#52,0.275);
#52 = AXIS2_PLACEMENT_3D('',#53,#54,#55);
#53 = CARTESIAN_POINT('',(0.,0.,17.02774));
#54 = DIRECTION('',(0.,0.,1.));
#55 = DIRECTION('',(1.,0.,-0.));
#56 = PCURVE('',#57,#62);
#57 = CYLINDRICAL_SURFACE('',#58,0.275);
#58 = AXIS2_PLACEMENT_3D('',#59,#60,#61);
#59 = CARTESIAN_POINT('',(0.,0.,0.));
#60 = DIRECTION('',(0.,0.,1.));
#61 = DIRECTION('',(1.,0.,-0.));
#62 = DEFINITIONAL_REPRESENTATION('',(#63),#67);
#63 = LINE('',#64,#65);
#64 = CARTESIAN_POINT('',(0.,17.02774));
#65 = VECTOR('',#66,1.);
#66 = DIRECTION('',(1.,0.));
#67 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#68 = PCURVE('',#69,#74);
#69 = PLANE('',#70);
#70 = AXIS2_PLACEMENT_3D('',#71,#72,#73);
#71 = CARTESIAN_POINT('',(0.,0.,17.02774));
#72 = DIRECTION('',(0.,0.,1.));
#73 = DIRECTION('',(1.,0.,-0.));
#74 = DEFINITIONAL_REPRESENTATION('',(#75),#79);
#75 = CIRCLE('',#76,0.275);
#76 = AXIS2_PLACEMENT_2D('',#77,#78);
#77 = CARTESIAN_POINT('',(0.,0.));
#78 = DIRECTION('',(1.,0.));
#79 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#80 = ORIENTED_EDGE('',*,*,#81,.F.);
#81 = EDGE_CURVE('',#82,#48,#84,.T.);
#82 = VERTEX_POINT('',#83);
#83 = CARTESIAN_POINT('',(0.275,-6.73555739531E-17,0.));
#84 = SEAM_CURVE('',#85,(#89,#96),.PCURVE_S1.);
#85 = LINE('',#86,#87);
#86 = CARTESIAN_POINT('',(0.275,-6.73555739531E-17,0.));
#87 = VECTOR('',#88,1.);
#88 = DIRECTION('',(0.,0.,1.));
#89 = PCURVE('',#57,#90);
#90 = DEFINITIONAL_REPRESENTATION('',(#91),#95);
#91 = LINE('',#92,#93);
#92 = CARTESIAN_POINT('',(6.28318530718,-0.));
#93 = VECTOR('',#94,1.);
#94 = DIRECTION('',(0.,1.));
#95 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#96 = PCURVE('',#57,#97);
#97 = DEFINITIONAL_REPRESENTATION('',(#98),#102);
#98 = LINE('',#99,#100);
#99 = CARTESIAN_POINT('',(0.,-0.));
#100 = VECTOR('',#101,1.);
#101 = DIRECTION('',(0.,1.));
#102 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#103 = ORIENTED_EDGE('',*,*,#104,.T.);
#104 = EDGE_CURVE('',#82,#82,#105,.T.);
#105 = SURFACE_CURVE('',#106,(#111,#118),.PCURVE_S1.);
#106 = CIRCLE('',#107,0.275);
#107 = AXIS2_PLACEMENT_3D('',#108,#109,#110);
#108 = CARTESIAN_POINT('',(0.,0.,0.));
#109 = DIRECTION('',(0.,0.,1.));
#110 = DIRECTION('',(1.,0.,-0.));
#111 = PCURVE('',#57,#112);
#112 = DEFINITIONAL_REPRESENTATION('',(#113),#117);
#113 = LINE('',#114,#115);
#114 = CARTESIAN_POINT('',(0.,0.));
#115 = VECTOR('',#116,1.);
#116 = DIRECTION('',(1.,0.));
#117 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#118 = PCURVE('',#119,#124);
#119 = PLANE('',#120);
#120 = AXIS2_PLACEMENT_3D('',#121,#122,#123);
#121 = CARTESIAN_POINT('',(0.,0.,0.));
#122 = DIRECTION('',(0.,0.,1.));
#123 = DIRECTION('',(1.,0.,-0.));
#124 = DEFINITIONAL_REPRESENTATION('',(#125),#129);
#125 = CIRCLE('',#126,0.275);
#126 = AXIS2_PLACEMENT_2D('',#127,#128);
#127 = CARTESIAN_POINT('',(0.,0.));
#128 = DIRECTION('',(1.,0.));
#129 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#130 = ORIENTED_EDGE('',*,*,#81,.T.);
#131 = ADVANCED_FACE('',(#132),#69,.T.);
#132 = FACE_BOUND('',#133,.T.);
#133 = EDGE_LOOP('',(#134));
#134 = ORIENTED_EDGE('',*,*,#47,.T.);
#135 = ADVANCED_FACE('',(#136),#119,.F.);
#136 = FACE_BOUND('',#137,.T.);
#137 = EDGE_LOOP('',(#138));
#138 = ORIENTED_EDGE('',*,*,#104,.F.);
#139 = ( GEOMETRIC_REPRESENTATION_CONTEXT(3) 
GLOBAL_UNCERTAINTY_ASSIGNED_CONTEXT((#143)) GLOBAL_UNIT_ASSIGNED_CONTEXT
((#140,#141,#142)) REPRESENTATION_CONTEXT('Context #1',
  '3D Context with UNIT and UNCERTAINTY') );
#140 = ( LENGTH_UNIT() NAMED_UNIT(*) SI_UNIT(.MILLI.,.METRE.) );
#141 = ( NAMED_UNIT(*) PLANE_ANGLE_UNIT() SI_UNIT($,.RADIAN.) );
#142 = ( NAMED_UNIT(*) SI_UNIT($,.STERADIAN.) SOLID_ANGLE_UNIT() );
#143 = UNCERTAINTY_MEASURE_WITH_UNIT(LENGTH_MEASURE(1.E-07),#140,
  'distance_accuracy_value','confusion accuracy');
#144 = CONTEXT_DEPENDENT_SHAPE_REPRESENTATION(#145,#147);
#145 = ( REPRESENTATION_RELATIONSHIP('','',#40,#10) 
REPRESENTATION_RELATIONSHIP_WITH_TRANSFORMATION(#146) 
SHAPE_REPRESENTATION_RELATIONSHIP() );
#146 = ITEM_DEFINED_TRANSFORMATION('','',#11,#15);
#147 = PRODUCT_DEFINITION_SHAPE('Placement','Placement of an item',#148
  );
#148 = NEXT_ASSEMBLY_USAGE_OCCURRENCE('84','=>[0:1:1:2]','',#5,#35,$);
#149 = PRODUCT_RELATED_PRODUCT_CATEGORY('part',$,(#37));
#150 = SHAPE_DEFINITION_REPRESENTATION(#151,#157);
#151 = PRODUCT_DEFINITION_SHAPE('','',#152);
#152 = PRODUCT_DEFINITION('design','',#153,#156);
#153 = PRODUCT_DEFINITION_FORMATION('','',#154);
#154 = PRODUCT('PC1-P2_Part2','PC1-P2_Part2','',(#155));
#155 = PRODUCT_CONTEXT('',#2,'mechanical');
#156 = PRODUCT_DEFINITION_CONTEXT('part definition',#2,'design');
#157 = ADVANCED_BREP_SHAPE_REPRESENTATION('',(#11,#158),#552);
#158 = MANIFOLD_SOLID_BREP('',#159);
#159 = CLOSED_SHELL('',(#160,#397,#447,#515,#520,#546));
#160 = ADVANCED_FACE('',(#161),#175,.T.);
#161 = FACE_BOUND('',#162,.T.);
#162 = EDGE_LOOP('',(#163,#193,#222,#254,#286,#310,#348,#370,#371));
#163 = ORIENTED_EDGE('',*,*,#164,.T.);
#164 = EDGE_CURVE('',#165,#167,#169,.T.);
#165 = VERTEX_POINT('',#166);
#166 = CARTESIAN_POINT('',(51.80692,135.691161,0.97226));
#167 = VERTEX_POINT('',#168);
#168 = CARTESIAN_POINT('',(55.848371884327,135.691161,7.97226));
#169 = SEAM_CURVE('',#170,(#174,#186),.PCURVE_S1.);
#170 = LINE('',#171,#172);
#171 = CARTESIAN_POINT('',(51.80692,135.691161,0.97226));
#172 = VECTOR('',#173,1.);
#173 = DIRECTION('',(0.5,-1.224646799147E-16,0.866025403784));
#174 = PCURVE('',#175,#180);
#175 = CONICAL_SURFACE('',#176,0.275,0.523598775598);
#176 = AXIS2_PLACEMENT_3D('',#177,#178,#179);
#177 = CARTESIAN_POINT('',(51.53192,135.691161,0.97226));
#178 = DIRECTION('',(0.,0.,1.));
#179 = DIRECTION('',(1.,0.,0.));
#180 = DEFINITIONAL_REPRESENTATION('',(#181),#185);
#181 = LINE('',#182,#183);
#182 = CARTESIAN_POINT('',(6.28318530718,-0.));
#183 = VECTOR('',#184,1.);
#184 = DIRECTION('',(0.,1.));
#185 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#186 = PCURVE('',#175,#187);
#187 = DEFINITIONAL_REPRESENTATION('',(#188),#192);
#188 = LINE('',#189,#190);
#189 = CARTESIAN_POINT('',(0.,-0.));
#190 = VECTOR('',#191,1.);
#191 = DIRECTION('',(0.,1.));
#192 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#193 = ORIENTED_EDGE('',*,*,#194,.F.);
#194 = EDGE_CURVE('',#195,#167,#197,.T.);
#195 = VERTEX_POINT('',#196);
#196 = CARTESIAN_POINT('',(50.470603233643,131.50721984468,7.97226));
#197 = SURFACE_CURVE('',#198,(#203,#210),.PCURVE_S1.);
#198 = CIRCLE('',#199,4.316451884327);
#199 = AXIS2_PLACEMENT_3D('',#200,#201,#202);
#200 = CARTESIAN_POINT('',(51.53192,135.691161,7.97226));
#201 = DIRECTION('',(0.,0.,1.));
#202 = DIRECTION('',(1.,0.,0.));
#203 = PCURVE('',#175,#204);
#204 = DEFINITIONAL_REPRESENTATION('',(#205),#209);
#205 = LINE('',#206,#207);
#206 = CARTESIAN_POINT('',(0.,7.));
#207 = VECTOR('',#208,1.);
#208 = DIRECTION('',(1.,0.));
#209 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#210 = PCURVE('',#211,#216);
#211 = PLANE('',#212);
#212 = AXIS2_PLACEMENT_3D('',#213,#214,#215);
#213 = CARTESIAN_POINT('',(51.53192,135.691161,7.97226));
#214 = DIRECTION('',(0.,0.,1.));
#215 = DIRECTION('',(1.,0.,0.));
#216 = DEFINITIONAL_REPRESENTATION('',(#217),#221);
#217 = CIRCLE('',#218,4.316451884327);
#218 = AXIS2_PLACEMENT_2D('',#219,#220);
#219 = CARTESIAN_POINT('',(0.,0.));
#220 = DIRECTION('',(1.,0.));
#221 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#222 = ORIENTED_EDGE('',*,*,#223,.F.);
#223 = EDGE_CURVE('',#224,#195,#226,.T.);
#224 = VERTEX_POINT('',#225);
#225 = CARTESIAN_POINT('',(50.449147,131.893892,7.335167305496));
#226 = SURFACE_CURVE('',#227,(#232,#242),.PCURVE_S1.);
#227 = HYPERBOLA('',#228,2.236934386969,1.291494670476);
#228 = AXIS2_PLACEMENT_3D('',#229,#230,#231);
#229 = CARTESIAN_POINT('',(50.242409058309,135.61960671901,
    0.495946027919));
#230 = DIRECTION('',(0.998464005442,5.540424023454E-02,0.));
#231 = DIRECTION('',(-0.,0.,1.));
#232 = PCURVE('',#175,#233);
#233 = DEFINITIONAL_REPRESENTATION('',(#234),#241);
#234 = B_SPLINE_CURVE_WITH_KNOTS('',5,(#235,#236,#237,#238,#239,#240),
  .UNSPECIFIED.,.F.,.F.,(6,6),(1.490453364319,1.876609079679),
  .PIECEWISE_BEZIER_KNOTS.);
#235 = CARTESIAN_POINT('',(4.3246778132,4.740639909249));
#236 = CARTESIAN_POINT('',(4.357793114506,5.104633263599));
#237 = CARTESIAN_POINT('',(4.388020127502,5.507522627771));
#238 = CARTESIAN_POINT('',(4.415620175954,5.953835122136));
#239 = CARTESIAN_POINT('',(4.440856712557,6.449046887062));
#240 = CARTESIAN_POINT('',(4.463964490043,7.));
#241 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#242 = PCURVE('',#243,#248);
#243 = PLANE('',#244);
#244 = AXIS2_PLACEMENT_3D('',#245,#246,#247);
#245 = CARTESIAN_POINT('',(51.008936,121.80569,28.));
#246 = DIRECTION('',(0.998464005442,5.540424023454E-02,0.));
#247 = DIRECTION('',(-5.540424023454E-02,0.998464005442,0.));
#248 = DEFINITIONAL_REPRESENTATION('',(#249),#253);
#249 = ( BOUNDED_CURVE() B_SPLINE_CURVE(2,(#250,#251,#252),
.UNSPECIFIED.,.F.,.F.) B_SPLINE_CURVE_WITH_KNOTS((3,3),(1.490453364319,
1.876609079679),.PIECEWISE_BEZIER_KNOTS.) CURVE() 
GEOMETRIC_REPRESENTATION_ITEM() RATIONAL_B_SPLINE_CURVE((1.,
1.01869750691,1.)) REPRESENTATION_ITEM('') );
#250 = CARTESIAN_POINT('',(11.114093386955,-22.28710009075));
#251 = CARTESIAN_POINT('',(10.539661418505,-21.38825836883));
#252 = CARTESIAN_POINT('',(9.716454265553,-20.02774));
#253 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#254 = ORIENTED_EDGE('',*,*,#255,.F.);
#255 = EDGE_CURVE('',#256,#224,#258,.T.);
#256 = VERTEX_POINT('',#257);
#257 = CARTESIAN_POINT('',(49.553535824782,131.85478863473,7.97226));
#258 = SURFACE_CURVE('',#259,(#264,#274),.PCURVE_S1.);
#259 = HYPERBOLA('',#260,6.488998024353,3.746424756131);
#260 = AXIS2_PLACEMENT_3D('',#261,#262,#263);
#261 = CARTESIAN_POINT('',(51.695337323597,131.94830203502,
    0.495946027919));
#262 = DIRECTION('',(-4.361953975718E-02,0.999048214928,0.));
#263 = DIRECTION('',(0.,0.,1.));
#264 = PCURVE('',#175,#265);
#265 = DEFINITIONAL_REPRESENTATION('',(#266),#273);
#266 = B_SPLINE_CURVE_WITH_KNOTS('',5,(#267,#268,#269,#270,#271,#272),
  .UNSPECIFIED.,.F.,.F.,(6,6),(-0.544872819069,-0.27219350445),
  .PIECEWISE_BEZIER_KNOTS.);
#267 = CARTESIAN_POINT('',(4.236267209909,7.));
#268 = CARTESIAN_POINT('',(4.283601111093,6.797495076471));
#269 = CARTESIAN_POINT('',(4.332537472006,6.622784760042));
#270 = CARTESIAN_POINT('',(4.382928739189,6.474614879899));
#271 = CARTESIAN_POINT('',(4.434552874931,6.352073079516));
#272 = CARTESIAN_POINT('',(4.487129018099,6.254554544388));
#273 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#274 = PCURVE('',#275,#280);
#275 = PLANE('',#276);
#276 = AXIS2_PLACEMENT_3D('',#277,#278,#279);
#277 = CARTESIAN_POINT('',(50.449147,131.893892,28.));
#278 = DIRECTION('',(-4.361953975718E-02,0.999048214928,0.));
#279 = DIRECTION('',(-0.999048214928,-4.361953975718E-02,0.));
#280 = DEFINITIONAL_REPRESENTATION('',(#281),#285);
#281 = ( BOUNDED_CURVE() B_SPLINE_CURVE(2,(#282,#283,#284),
.UNSPECIFIED.,.F.,.F.) B_SPLINE_CURVE_WITH_KNOTS((3,3),(-0.544872819069,
-0.27219350445),.PIECEWISE_BEZIER_KNOTS.) CURVE() 
GEOMETRIC_REPRESENTATION_ITEM() RATIONAL_B_SPLINE_CURVE((1.,
1.009308657185,1.)) REPRESENTATION_ITEM('') );
#282 = CARTESIAN_POINT('',(0.896464416667,-20.02774));
#283 = CARTESIAN_POINT('',(0.311580402125,-20.53088857264));
#284 = CARTESIAN_POINT('',(-0.214986220675,-20.77318545561));
#285 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#286 = ORIENTED_EDGE('',*,*,#287,.F.);
#287 = EDGE_CURVE('',#288,#256,#290,.T.);
#288 = VERTEX_POINT('',#289);
#289 = CARTESIAN_POINT('',(48.16434175799,132.99094366547,7.97226));
#290 = SURFACE_CURVE('',#291,(#296,#303),.PCURVE_S1.);
#291 = CIRCLE('',#292,4.316451884327);
#292 = AXIS2_PLACEMENT_3D('',#293,#294,#295);
#293 = CARTESIAN_POINT('',(51.53192,135.691161,7.97226));
#294 = DIRECTION('',(0.,0.,1.));
#295 = DIRECTION('',(1.,0.,0.));
#296 = PCURVE('',#175,#297);
#297 = DEFINITIONAL_REPRESENTATION('',(#298),#302);
#298 = LINE('',#299,#300);
#299 = CARTESIAN_POINT('',(0.,7.));
#300 = VECTOR('',#301,1.);
#301 = DIRECTION('',(1.,0.));
#302 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#303 = PCURVE('',#211,#304);
#304 = DEFINITIONAL_REPRESENTATION('',(#305),#309);
#305 = CIRCLE('',#306,4.316451884327);
#306 = AXIS2_PLACEMENT_2D('',#307,#308);
#307 = CARTESIAN_POINT('',(0.,0.));
#308 = DIRECTION('',(1.,0.));
#309 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#310 = ORIENTED_EDGE('',*,*,#311,.F.);
#311 = EDGE_CURVE('',#312,#288,#314,.T.);
#312 = VERTEX_POINT('',#313);
#313 = CARTESIAN_POINT('',(47.63917157096,137.55617213613,7.97226));
#314 = SURFACE_CURVE('',#315,(#320,#336),.PCURVE_S1.);
#315 = HYPERBOLA('',#316,6.329094293272,3.654104293947);
#316 = AXIS2_PLACEMENT_3D('',#317,#318,#319);
#317 = CARTESIAN_POINT('',(47.901756664475,135.2735579008,0.495946027919
    ));
#318 = DIRECTION('',(0.993448200572,0.11428330053,0.));
#319 = DIRECTION('',(-0.,0.,1.));
#320 = PCURVE('',#175,#321);
#321 = DEFINITIONAL_REPRESENTATION('',(#322),#335);
#322 = B_SPLINE_CURVE_WITH_KNOTS('',6,(#323,#324,#325,#326,#327,#328,
    #329,#330,#331,#332,#333,#334),.UNSPECIFIED.,.F.,.F.,(7,5,7),(
    -0.593355754776,3.10862446895E-15,0.593355754776),.UNSPECIFIED.);
#323 = CARTESIAN_POINT('',(2.694805373304,7.));
#324 = CARTESIAN_POINT('',(2.778523199482,6.606439204532));
#325 = CARTESIAN_POINT('',(2.867529158094,6.300618137545));
#326 = CARTESIAN_POINT('',(2.961186074241,6.075609202424));
#327 = CARTESIAN_POINT('',(3.058341549055,5.92705673564));
#328 = CARTESIAN_POINT('',(3.157233571351,5.852780321191));
#329 = CARTESIAN_POINT('',(3.355018822943,5.852780321191));
#330 = CARTESIAN_POINT('',(3.453910845239,5.92705673564));
#331 = CARTESIAN_POINT('',(3.551066320053,6.075609202423));
#332 = CARTESIAN_POINT('',(3.6447232362,6.300618137545));
#333 = CARTESIAN_POINT('',(3.733729194811,6.606439204532));
#334 = CARTESIAN_POINT('',(3.81744702099,7.));
#335 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#336 = PCURVE('',#337,#342);
#337 = PLANE('',#338);
#338 = AXIS2_PLACEMENT_3D('',#339,#340,#341);
#339 = CARTESIAN_POINT('',(48.301331,131.800116,28.));
#340 = DIRECTION('',(0.993448200572,0.11428330053,0.));
#341 = DIRECTION('',(-0.11428330053,0.993448200572,0.));
#342 = DEFINITIONAL_REPRESENTATION('',(#343),#347);
#343 = ( BOUNDED_CURVE() B_SPLINE_CURVE(2,(#344,#345,#346),
.UNSPECIFIED.,.F.,.F.) B_SPLINE_CURVE_WITH_KNOTS((3,3),(-0.593355754776,
0.593355754776),.PIECEWISE_BEZIER_KNOTS.) CURVE() 
GEOMETRIC_REPRESENTATION_ITEM() RATIONAL_B_SPLINE_CURVE((1.,
1.181261271463,1.)) REPRESENTATION_ITEM('') );
#344 = CARTESIAN_POINT('',(5.794017375867,-20.02774));
#345 = CARTESIAN_POINT('',(3.496349280021,-22.14614167429));
#346 = CARTESIAN_POINT('',(1.198681184175,-20.02774));
#347 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#348 = ORIENTED_EDGE('',*,*,#349,.F.);
#349 = EDGE_CURVE('',#167,#312,#350,.T.);
#350 = SURFACE_CURVE('',#351,(#356,#363),.PCURVE_S1.);
#351 = CIRCLE('',#352,4.316451884327);
#352 = AXIS2_PLACEMENT_3D('',#353,#354,#355);
#353 = CARTESIAN_POINT('',(51.53192,135.691161,7.97226));
#354 = DIRECTION('',(0.,0.,1.));
#355 = DIRECTION('',(1.,0.,0.));
#356 = PCURVE('',#175,#357);
#357 = DEFINITIONAL_REPRESENTATION('',(#358),#362);
#358 = LINE('',#359,#360);
#359 = CARTESIAN_POINT('',(0.,7.));
#360 = VECTOR('',#361,1.);
#361 = DIRECTION('',(1.,0.));
#362 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#363 = PCURVE('',#211,#364);
#364 = DEFINITIONAL_REPRESENTATION('',(#365),#369);
#365 = CIRCLE('',#366,4.316451884327);
#366 = AXIS2_PLACEMENT_2D('',#367,#368);
#367 = CARTESIAN_POINT('',(0.,0.));
#368 = DIRECTION('',(1.,0.));
#369 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#370 = ORIENTED_EDGE('',*,*,#164,.F.);
#371 = ORIENTED_EDGE('',*,*,#372,.T.);
#372 = EDGE_CURVE('',#165,#165,#373,.T.);
#373 = SURFACE_CURVE('',#374,(#379,#386),.PCURVE_S1.);
#374 = CIRCLE('',#375,0.275);
#375 = AXIS2_PLACEMENT_3D('',#376,#377,#378);
#376 = CARTESIAN_POINT('',(51.53192,135.691161,0.97226));
#377 = DIRECTION('',(0.,0.,1.));
#378 = DIRECTION('',(1.,0.,0.));
#379 = PCURVE('',#175,#380);
#380 = DEFINITIONAL_REPRESENTATION('',(#381),#385);
#381 = LINE('',#382,#383);
#382 = CARTESIAN_POINT('',(0.,0.));
#383 = VECTOR('',#384,1.);
#384 = DIRECTION('',(1.,0.));
#385 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#386 = PCURVE('',#387,#392);
#387 = CYLINDRICAL_SURFACE('',#388,0.275);
#388 = AXIS2_PLACEMENT_3D('',#389,#390,#391);
#389 = CARTESIAN_POINT('',(51.53192,135.691161,0.97226));
#390 = DIRECTION('',(0.,0.,1.));
#391 = DIRECTION('',(1.,0.,0.));
#392 = DEFINITIONAL_REPRESENTATION('',(#393),#396);
#393 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#394,#395),.UNSPECIFIED.,.F.,.F.,
  (2,2),(0.,6.28318530718),.PIECEWISE_BEZIER_KNOTS.);
#394 = CARTESIAN_POINT('',(0.,0.));
#395 = CARTESIAN_POINT('',(6.28318530718,0.));
#396 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#397 = ADVANCED_FACE('',(#398),#387,.F.);
#398 = FACE_BOUND('',#399,.F.);
#399 = EDGE_LOOP('',(#400,#424,#445,#446));
#400 = ORIENTED_EDGE('',*,*,#401,.F.);
#401 = EDGE_CURVE('',#402,#402,#404,.T.);
#402 = VERTEX_POINT('',#403);
#403 = CARTESIAN_POINT('',(51.80692,135.691161,7.97226));
#404 = SURFACE_CURVE('',#405,(#410,#417),.PCURVE_S1.);
#405 = CIRCLE('',#406,0.275);
#406 = AXIS2_PLACEMENT_3D('',#407,#408,#409);
#407 = CARTESIAN_POINT('',(51.53192,135.691161,7.97226));
#408 = DIRECTION('',(0.,0.,1.));
#409 = DIRECTION('',(1.,0.,0.));
#410 = PCURVE('',#387,#411);
#411 = DEFINITIONAL_REPRESENTATION('',(#412),#416);
#412 = LINE('',#413,#414);
#413 = CARTESIAN_POINT('',(0.,7.));
#414 = VECTOR('',#415,1.);
#415 = DIRECTION('',(1.,0.));
#416 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#417 = PCURVE('',#211,#418);
#418 = DEFINITIONAL_REPRESENTATION('',(#419),#423);
#419 = CIRCLE('',#420,0.275);
#420 = AXIS2_PLACEMENT_2D('',#421,#422);
#421 = CARTESIAN_POINT('',(0.,0.));
#422 = DIRECTION('',(1.,0.));
#423 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#424 = ORIENTED_EDGE('',*,*,#425,.F.);
#425 = EDGE_CURVE('',#165,#402,#426,.T.);
#426 = SEAM_CURVE('',#427,(#431,#438),.PCURVE_S1.);
#427 = LINE('',#428,#429);
#428 = CARTESIAN_POINT('',(51.80692,135.691161,0.97226));
#429 = VECTOR('',#430,1.);
#430 = DIRECTION('',(0.,0.,1.));
#431 = PCURVE('',#387,#432);
#432 = DEFINITIONAL_REPRESENTATION('',(#433),#437);
#433 = LINE('',#434,#435);
#434 = CARTESIAN_POINT('',(6.28318530718,-0.));
#435 = VECTOR('',#436,1.);
#436 = DIRECTION('',(0.,1.));
#437 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#438 = PCURVE('',#387,#439);
#439 = DEFINITIONAL_REPRESENTATION('',(#440),#444);
#440 = LINE('',#441,#442);
#441 = CARTESIAN_POINT('',(0.,-0.));
#442 = VECTOR('',#443,1.);
#443 = DIRECTION('',(0.,1.));
#444 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#445 = ORIENTED_EDGE('',*,*,#372,.T.);
#446 = ORIENTED_EDGE('',*,*,#425,.T.);
#447 = ADVANCED_FACE('',(#448,#512),#211,.T.);
#448 = FACE_BOUND('',#449,.T.);
#449 = EDGE_LOOP('',(#450,#451,#452,#471,#472,#493));
#450 = ORIENTED_EDGE('',*,*,#194,.T.);
#451 = ORIENTED_EDGE('',*,*,#349,.T.);
#452 = ORIENTED_EDGE('',*,*,#453,.F.);
#453 = EDGE_CURVE('',#288,#312,#454,.T.);
#454 = SURFACE_CURVE('',#455,(#459,#465),.PCURVE_S1.);
#455 = LINE('',#456,#457);
#456 = CARTESIAN_POINT('',(48.101543832237,133.5368369504,7.97226));
#457 = VECTOR('',#458,1.);
#458 = DIRECTION('',(-0.11428330053,0.993448200572,0.));
#459 = PCURVE('',#211,#460);
#460 = DEFINITIONAL_REPRESENTATION('',(#461),#464);
#461 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#462,#463),.UNSPECIFIED.,.F.,.F.,
  (2,2),(-1.74817464001,6.513450756865),.PIECEWISE_BEZIER_KNOTS.);
#462 = CARTESIAN_POINT('',(-3.230589,-3.891045));
#463 = CARTESIAN_POINT('',(-4.174754818094,4.316451884327));
#464 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#465 = PCURVE('',#337,#466);
#466 = DEFINITIONAL_REPRESENTATION('',(#467),#470);
#467 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#468,#469),.UNSPECIFIED.,.F.,.F.,
  (2,2),(-1.74817464001,6.513450756865),.PIECEWISE_BEZIER_KNOTS.);
#468 = CARTESIAN_POINT('',(0.,-20.02774));
#469 = CARTESIAN_POINT('',(8.261625396876,-20.02774));
#470 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#471 = ORIENTED_EDGE('',*,*,#287,.T.);
#472 = ORIENTED_EDGE('',*,*,#473,.F.);
#473 = EDGE_CURVE('',#474,#256,#476,.T.);
#474 = VERTEX_POINT('',#475);
#475 = CARTESIAN_POINT('',(50.449147,131.893892,7.97226));
#476 = SURFACE_CURVE('',#477,(#481,#487),.PCURVE_S1.);
#477 = LINE('',#478,#479);
#478 = CARTESIAN_POINT('',(51.072242161799,131.92109701751,7.97226));
#479 = VECTOR('',#480,1.);
#480 = DIRECTION('',(-0.999048214928,-4.361953975718E-02,0.));
#481 = PCURVE('',#211,#482);
#482 = DEFINITIONAL_REPRESENTATION('',(#483),#486);
#483 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#484,#485),.UNSPECIFIED.,.F.,.F.,
  (2,2),(0.623688779468,2.773550986223),.PIECEWISE_BEZIER_KNOTS.);
#484 = CARTESIAN_POINT('',(-1.082773,-3.797269));
#485 = CARTESIAN_POINT('',(-3.230589,-3.891045));
#486 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#487 = PCURVE('',#275,#488);
#488 = DEFINITIONAL_REPRESENTATION('',(#489),#492);
#489 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#490,#491),.UNSPECIFIED.,.F.,.F.,
  (2,2),(0.623688779468,2.773550986223),.PIECEWISE_BEZIER_KNOTS.);
#490 = CARTESIAN_POINT('',(0.,-20.02774));
#491 = CARTESIAN_POINT('',(2.149862206755,-20.02774));
#492 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#493 = ORIENTED_EDGE('',*,*,#494,.F.);
#494 = EDGE_CURVE('',#195,#474,#495,.T.);
#495 = SURFACE_CURVE('',#496,(#500,#506),.PCURVE_S1.);
#496 = LINE('',#497,#498);
#497 = CARTESIAN_POINT('',(50.625672529155,128.7126483595,7.97226));
#498 = VECTOR('',#499,1.);
#499 = DIRECTION('',(-5.540424023454E-02,0.998464005442,0.));
#500 = PCURVE('',#211,#501);
#501 = DEFINITIONAL_REPRESENTATION('',(#502),#505);
#502 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#503,#504),.UNSPECIFIED.,.F.,.F.,
  (2,2),(2.666155957205,3.186137530399),.PIECEWISE_BEZIER_KNOTS.);
#503 = CARTESIAN_POINT('',(-1.053963816001,-4.316451884327));
#504 = CARTESIAN_POINT('',(-1.082773,-3.797269));
#505 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#506 = PCURVE('',#243,#507);
#507 = DEFINITIONAL_REPRESENTATION('',(#508),#511);
#508 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#509,#510),.UNSPECIFIED.,.F.,.F.,
  (2,2),(2.666155957205,3.186137530399),.PIECEWISE_BEZIER_KNOTS.);
#509 = CARTESIAN_POINT('',(9.583739687675,-20.02774));
#510 = CARTESIAN_POINT('',(10.103721260868,-20.02774));
#511 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#512 = FACE_BOUND('',#513,.T.);
#513 = EDGE_LOOP('',(#514));
#514 = ORIENTED_EDGE('',*,*,#401,.F.);
#515 = ADVANCED_FACE('',(#516),#337,.F.);
#516 = FACE_BOUND('',#517,.F.);
#517 = EDGE_LOOP('',(#518,#519));
#518 = ORIENTED_EDGE('',*,*,#311,.F.);
#519 = ORIENTED_EDGE('',*,*,#453,.F.);
#520 = ADVANCED_FACE('',(#521),#275,.F.);
#521 = FACE_BOUND('',#522,.F.);
#522 = EDGE_LOOP('',(#523,#524,#545));
#523 = ORIENTED_EDGE('',*,*,#473,.F.);
#524 = ORIENTED_EDGE('',*,*,#525,.T.);
#525 = EDGE_CURVE('',#474,#224,#526,.T.);
#526 = SURFACE_CURVE('',#527,(#531,#538),.PCURVE_S1.);
#527 = LINE('',#528,#529);
#528 = CARTESIAN_POINT('',(50.449147,131.893892,28.));
#529 = VECTOR('',#530,1.);
#530 = DIRECTION('',(0.,0.,-1.));
#531 = PCURVE('',#275,#532);
#532 = DEFINITIONAL_REPRESENTATION('',(#533),#537);
#533 = LINE('',#534,#535);
#534 = CARTESIAN_POINT('',(0.,0.));
#535 = VECTOR('',#536,1.);
#536 = DIRECTION('',(-0.,-1.));
#537 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#538 = PCURVE('',#243,#539);
#539 = DEFINITIONAL_REPRESENTATION('',(#540),#544);
#540 = LINE('',#541,#542);
#541 = CARTESIAN_POINT('',(10.103721260868,0.));
#542 = VECTOR('',#543,1.);
#543 = DIRECTION('',(0.,-1.));
#544 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#545 = ORIENTED_EDGE('',*,*,#255,.F.);
#546 = ADVANCED_FACE('',(#547),#243,.F.);
#547 = FACE_BOUND('',#548,.F.);
#548 = EDGE_LOOP('',(#549,#550,#551));
#549 = ORIENTED_EDGE('',*,*,#525,.F.);
#550 = ORIENTED_EDGE('',*,*,#494,.F.);
#551 = ORIENTED_EDGE('',*,*,#223,.F.);
#552 = ( GEOMETRIC_REPRESENTATION_CONTEXT(3) 
GLOBAL_UNCERTAINTY_ASSIGNED_CONTEXT((#556)) GLOBAL_UNIT_ASSIGNED_CONTEXT
((#553,#554,#555)) REPRESENTATION_CONTEXT('Context #1',
  '3D Context with UNIT and UNCERTAINTY') );
#553 = ( LENGTH_UNIT() NAMED_UNIT(*) SI_UNIT(.MILLI.,.METRE.) );
#554 = ( NAMED_UNIT(*) PLANE_ANGLE_UNIT() SI_UNIT($,.RADIAN.) );
#555 = ( NAMED_UNIT(*) SI_UNIT($,.STERADIAN.) SOLID_ANGLE_UNIT() );
#556 = UNCERTAINTY_MEASURE_WITH_UNIT(LENGTH_MEASURE(1.E-07),#553,
  'distance_accuracy_value','confusion accuracy');
#557 = CONTEXT_DEPENDENT_SHAPE_REPRESENTATION(#558,#560);
#558 = ( REPRESENTATION_RELATIONSHIP('','',#157,#10) 
REPRESENTATION_RELATIONSHIP_WITH_TRANSFORMATION(#559) 
SHAPE_REPRESENTATION_RELATIONSHIP() );
#559 = ITEM_DEFINED_TRANSFORMATION('','',#11,#19);
#560 = PRODUCT_DEFINITION_SHAPE('Placement','Placement of an item',#561
  );
#561 = NEXT_ASSEMBLY_USAGE_OCCURRENCE('85','=>[0:1:1:3]','',#5,#152,$);
#562 = PRODUCT_RELATED_PRODUCT_CATEGORY('part',$,(#154));
#563 = SHAPE_DEFINITION_REPRESENTATION(#564,#570);
#564 = PRODUCT_DEFINITION_SHAPE('','',#565);
#565 = PRODUCT_DEFINITION('design','',#566,#569);
#566 = PRODUCT_DEFINITION_FORMATION('','',#567);
#567 = PRODUCT('PC1-P2_Part3','PC1-P2_Part3','',(#568));
#568 = PRODUCT_CONTEXT('',#2,'mechanical');
#569 = PRODUCT_DEFINITION_CONTEXT('part definition',#2,'design');
#570 = ADVANCED_BREP_SHAPE_REPRESENTATION('',(#11,#571),#1051);
#571 = MANIFOLD_SOLID_BREP('',#572);
#572 = CLOSED_SHELL('',(#573,#695,#825,#872,#967,#992,#1017,#1024));
#573 = ADVANCED_FACE('',(#574),#589,.T.);
#574 = FACE_BOUND('',#575,.T.);
#575 = EDGE_LOOP('',(#576,#612,#638,#671));
#576 = ORIENTED_EDGE('',*,*,#577,.T.);
#577 = EDGE_CURVE('',#578,#580,#582,.T.);
#578 = VERTEX_POINT('',#579);
#579 = CARTESIAN_POINT('',(50.470603233643,131.50721984468,7.97226));
#580 = VERTEX_POINT('',#581);
#581 = CARTESIAN_POINT('',(47.63917157096,137.55617213613,7.97226));
#582 = SURFACE_CURVE('',#583,(#588,#600),.PCURVE_S1.);
#583 = CIRCLE('',#584,4.316451884327);
#584 = AXIS2_PLACEMENT_3D('',#585,#586,#587);
#585 = CARTESIAN_POINT('',(51.53192,135.691161,7.97226));
#586 = DIRECTION('',(0.,0.,1.));
#587 = DIRECTION('',(-0.245877121951,-0.969301006345,0.));
#588 = PCURVE('',#589,#594);
#589 = CYLINDRICAL_SURFACE('',#590,4.316451884327);
#590 = AXIS2_PLACEMENT_3D('',#591,#592,#593);
#591 = CARTESIAN_POINT('',(51.53192,135.691161,7.97226));
#592 = DIRECTION('',(0.,0.,1.));
#593 = DIRECTION('',(1.,0.,0.));
#594 = DEFINITIONAL_REPRESENTATION('',(#595),#599);
#595 = LINE('',#596,#597);
#596 = CARTESIAN_POINT('',(4.463964490043,0.));
#597 = VECTOR('',#598,1.);
#598 = DIRECTION('',(1.,0.));
#599 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#600 = PCURVE('',#601,#606);
#601 = PLANE('',#602);
#602 = AXIS2_PLACEMENT_3D('',#603,#604,#605);
#603 = CARTESIAN_POINT('',(51.53192,135.691161,7.97226));
#604 = DIRECTION('',(0.,0.,1.));
#605 = DIRECTION('',(1.,0.,0.));
#606 = DEFINITIONAL_REPRESENTATION('',(#607),#611);
#607 = CIRCLE('',#608,4.316451884327);
#608 = AXIS2_PLACEMENT_2D('',#609,#610);
#609 = CARTESIAN_POINT('',(0.,0.));
#610 = DIRECTION('',(-0.245877121951,-0.969301006345));
#611 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#612 = ORIENTED_EDGE('',*,*,#613,.T.);
#613 = EDGE_CURVE('',#580,#614,#616,.T.);
#614 = VERTEX_POINT('',#615);
#615 = CARTESIAN_POINT('',(47.63917157096,137.55617213613,18.));
#616 = SURFACE_CURVE('',#617,(#621,#627),.PCURVE_S1.);
#617 = LINE('',#618,#619);
#618 = CARTESIAN_POINT('',(47.63917157096,137.55617213613,7.97226));
#619 = VECTOR('',#620,1.);
#620 = DIRECTION('',(0.,0.,1.));
#621 = PCURVE('',#589,#622);
#622 = DEFINITIONAL_REPRESENTATION('',(#623),#626);
#623 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#624,#625),.UNSPECIFIED.,.F.,.F.,
  (2,2),(0.,10.02774),.PIECEWISE_BEZIER_KNOTS.);
#624 = CARTESIAN_POINT('',(8.977990680483,0.));
#625 = CARTESIAN_POINT('',(8.977990680483,10.02774));
#626 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#627 = PCURVE('',#628,#633);
#628 = PLANE('',#629);
#629 = AXIS2_PLACEMENT_3D('',#630,#631,#632);
#630 = CARTESIAN_POINT('',(48.301331,131.800116,28.));
#631 = DIRECTION('',(0.993448200572,0.11428330053,0.));
#632 = DIRECTION('',(-0.11428330053,0.993448200572,0.));
#633 = DEFINITIONAL_REPRESENTATION('',(#634),#637);
#634 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#635,#636),.UNSPECIFIED.,.F.,.F.,
  (2,2),(-5.999999999062E-07,10.0277406),.PIECEWISE_BEZIER_KNOTS.);
#635 = CARTESIAN_POINT('',(5.794017375867,-20.0277406));
#636 = CARTESIAN_POINT('',(5.794017375867,-9.9999994));
#637 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#638 = ORIENTED_EDGE('',*,*,#639,.T.);
#639 = EDGE_CURVE('',#614,#640,#642,.T.);
#640 = VERTEX_POINT('',#641);
#641 = CARTESIAN_POINT('',(50.470603233643,131.50721984468,18.));
#642 = SURFACE_CURVE('',#643,(#648,#655),.PCURVE_S1.);
#643 = CIRCLE('',#644,4.316451884327);
#644 = AXIS2_PLACEMENT_3D('',#645,#646,#647);
#645 = CARTESIAN_POINT('',(51.53192,135.691161,18.));
#646 = DIRECTION('',(0.,0.,-1.));
#647 = DIRECTION('',(-0.901839875286,0.43207041017,0.));
#648 = PCURVE('',#589,#649);
#649 = DEFINITIONAL_REPRESENTATION('',(#650),#654);
#650 = LINE('',#651,#652);
#651 = CARTESIAN_POINT('',(8.977990680483,10.02774));
#652 = VECTOR('',#653,1.);
#653 = DIRECTION('',(-1.,-0.));
#654 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#655 = PCURVE('',#656,#661);
#656 = PLANE('',#657);
#657 = AXIS2_PLACEMENT_3D('',#658,#659,#660);
#658 = CARTESIAN_POINT('',(51.53192,135.691161,18.));
#659 = DIRECTION('',(0.,0.,1.));
#660 = DIRECTION('',(1.,0.,0.));
#661 = DEFINITIONAL_REPRESENTATION('',(#662),#670);
#662 = ( BOUNDED_CURVE() B_SPLINE_CURVE(2,(#663,#664,#665,#666,#667,#668
,#669),.UNSPECIFIED.,.T.,.F.) B_SPLINE_CURVE_WITH_KNOTS((1,2,2,2,2,1),(
    -2.094395102393,0.,2.094395102393,4.188790204786,6.28318530718,
8.377580409573),.UNSPECIFIED.) CURVE() GEOMETRIC_REPRESENTATION_ITEM() 
RATIONAL_B_SPLINE_CURVE((1.,0.5,1.,0.5,1.,0.5,1.)) REPRESENTATION_ITEM(
  '') );
#663 = CARTESIAN_POINT('',(-3.89274842904,1.865011136139));
#664 = CARTESIAN_POINT('',(-0.662454384566,8.60744919632));
#665 = CARTESIAN_POINT('',(3.561521236757,2.438713462021));
#666 = CARTESIAN_POINT('',(7.78549685808,-3.730022272277));
#667 = CARTESIAN_POINT('',(0.331227192283,-4.30372459816));
#668 = CARTESIAN_POINT('',(-7.123042473514,-4.877426924042));
#669 = CARTESIAN_POINT('',(-3.89274842904,1.865011136139));
#670 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#671 = ORIENTED_EDGE('',*,*,#672,.F.);
#672 = EDGE_CURVE('',#578,#640,#673,.T.);
#673 = SURFACE_CURVE('',#674,(#678,#684),.PCURVE_S1.);
#674 = LINE('',#675,#676);
#675 = CARTESIAN_POINT('',(50.470603233643,131.50721984468,7.97226));
#676 = VECTOR('',#677,1.);
#677 = DIRECTION('',(0.,0.,1.));
#678 = PCURVE('',#589,#679);
#679 = DEFINITIONAL_REPRESENTATION('',(#680),#683);
#680 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#681,#682),.UNSPECIFIED.,.F.,.F.,
  (2,2),(0.,10.02774),.PIECEWISE_BEZIER_KNOTS.);
#681 = CARTESIAN_POINT('',(4.463964490043,0.));
#682 = CARTESIAN_POINT('',(4.463964490043,10.02774));
#683 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#684 = PCURVE('',#685,#690);
#685 = PLANE('',#686);
#686 = AXIS2_PLACEMENT_3D('',#687,#688,#689);
#687 = CARTESIAN_POINT('',(51.008936,121.80569,28.));
#688 = DIRECTION('',(0.998464005442,5.540424023454E-02,0.));
#689 = DIRECTION('',(-5.540424023454E-02,0.998464005442,0.));
#690 = DEFINITIONAL_REPRESENTATION('',(#691),#694);
#691 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#692,#693),.UNSPECIFIED.,.F.,.F.,
  (2,2),(-5.999999999062E-07,10.0277406),.PIECEWISE_BEZIER_KNOTS.);
#692 = CARTESIAN_POINT('',(9.716454265553,-20.0277406));
#693 = CARTESIAN_POINT('',(9.716454265553,-9.9999994));
#694 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#695 = ADVANCED_FACE('',(#696,#794),#601,.F.);
#696 = FACE_BOUND('',#697,.F.);
#697 = EDGE_LOOP('',(#698,#699,#720,#749,#775));
#698 = ORIENTED_EDGE('',*,*,#577,.T.);
#699 = ORIENTED_EDGE('',*,*,#700,.F.);
#700 = EDGE_CURVE('',#701,#580,#703,.T.);
#701 = VERTEX_POINT('',#702);
#702 = CARTESIAN_POINT('',(48.16434175799,132.99094366547,7.97226));
#703 = SURFACE_CURVE('',#704,(#708,#714),.PCURVE_S1.);
#704 = LINE('',#705,#706);
#705 = CARTESIAN_POINT('',(48.101543832237,133.5368369504,7.97226));
#706 = VECTOR('',#707,1.);
#707 = DIRECTION('',(-0.11428330053,0.993448200572,0.));
#708 = PCURVE('',#601,#709);
#709 = DEFINITIONAL_REPRESENTATION('',(#710),#713);
#710 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#711,#712),.UNSPECIFIED.,.F.,.F.,
  (2,2),(-1.74817464001,6.513450756865),.PIECEWISE_BEZIER_KNOTS.);
#711 = CARTESIAN_POINT('',(-3.230589,-3.891045));
#712 = CARTESIAN_POINT('',(-4.174754818094,4.316451884327));
#713 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#714 = PCURVE('',#628,#715);
#715 = DEFINITIONAL_REPRESENTATION('',(#716),#719);
#716 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#717,#718),.UNSPECIFIED.,.F.,.F.,
  (2,2),(-1.74817464001,6.513450756865),.PIECEWISE_BEZIER_KNOTS.);
#717 = CARTESIAN_POINT('',(0.,-20.02774));
#718 = CARTESIAN_POINT('',(8.261625396876,-20.02774));
#719 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#720 = ORIENTED_EDGE('',*,*,#721,.T.);
#721 = EDGE_CURVE('',#701,#722,#724,.T.);
#722 = VERTEX_POINT('',#723);
#723 = CARTESIAN_POINT('',(49.553535824782,131.85478863473,7.97226));
#724 = SURFACE_CURVE('',#725,(#730,#737),.PCURVE_S1.);
#725 = CIRCLE('',#726,4.316451884327);
#726 = AXIS2_PLACEMENT_3D('',#727,#728,#729);
#727 = CARTESIAN_POINT('',(51.53192,135.691161,7.97226));
#728 = DIRECTION('',(0.,0.,1.));
#729 = DIRECTION('',(1.,0.,0.));
#730 = PCURVE('',#601,#731);
#731 = DEFINITIONAL_REPRESENTATION('',(#732),#736);
#732 = CIRCLE('',#733,4.316451884327);
#733 = AXIS2_PLACEMENT_2D('',#734,#735);
#734 = CARTESIAN_POINT('',(0.,0.));
#735 = DIRECTION('',(1.,0.));
#736 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#737 = PCURVE('',#738,#743);
#738 = CYLINDRICAL_SURFACE('',#739,4.316451884327);
#739 = AXIS2_PLACEMENT_3D('',#740,#741,#742);
#740 = CARTESIAN_POINT('',(51.53192,135.691161,7.97226));
#741 = DIRECTION('',(0.,0.,1.));
#742 = DIRECTION('',(1.,0.,0.));
#743 = DEFINITIONAL_REPRESENTATION('',(#744),#748);
#744 = LINE('',#745,#746);
#745 = CARTESIAN_POINT('',(0.,0.));
#746 = VECTOR('',#747,1.);
#747 = DIRECTION('',(1.,0.));
#748 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#749 = ORIENTED_EDGE('',*,*,#750,.F.);
#750 = EDGE_CURVE('',#751,#722,#753,.T.);
#751 = VERTEX_POINT('',#752);
#752 = CARTESIAN_POINT('',(50.449147,131.893892,7.97226));
#753 = SURFACE_CURVE('',#754,(#758,#764),.PCURVE_S1.);
#754 = LINE('',#755,#756);
#755 = CARTESIAN_POINT('',(51.072242161799,131.92109701751,7.97226));
#756 = VECTOR('',#757,1.);
#757 = DIRECTION('',(-0.999048214928,-4.361953975718E-02,0.));
#758 = PCURVE('',#601,#759);
#759 = DEFINITIONAL_REPRESENTATION('',(#760),#763);
#760 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#761,#762),.UNSPECIFIED.,.F.,.F.,
  (2,2),(0.623688779468,2.773550986223),.PIECEWISE_BEZIER_KNOTS.);
#761 = CARTESIAN_POINT('',(-1.082773,-3.797269));
#762 = CARTESIAN_POINT('',(-3.230589,-3.891045));
#763 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#764 = PCURVE('',#765,#770);
#765 = PLANE('',#766);
#766 = AXIS2_PLACEMENT_3D('',#767,#768,#769);
#767 = CARTESIAN_POINT('',(50.449147,131.893892,28.));
#768 = DIRECTION('',(-4.361953975718E-02,0.999048214928,0.));
#769 = DIRECTION('',(-0.999048214928,-4.361953975718E-02,0.));
#770 = DEFINITIONAL_REPRESENTATION('',(#771),#774);
#771 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#772,#773),.UNSPECIFIED.,.F.,.F.,
  (2,2),(0.623688779468,2.773550986223),.PIECEWISE_BEZIER_KNOTS.);
#772 = CARTESIAN_POINT('',(0.,-20.02774));
#773 = CARTESIAN_POINT('',(2.149862206755,-20.02774));
#774 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#775 = ORIENTED_EDGE('',*,*,#776,.F.);
#776 = EDGE_CURVE('',#578,#751,#777,.T.);
#777 = SURFACE_CURVE('',#778,(#782,#788),.PCURVE_S1.);
#778 = LINE('',#779,#780);
#779 = CARTESIAN_POINT('',(50.625672529155,128.7126483595,7.97226));
#780 = VECTOR('',#781,1.);
#781 = DIRECTION('',(-5.540424023454E-02,0.998464005442,0.));
#782 = PCURVE('',#601,#783);
#783 = DEFINITIONAL_REPRESENTATION('',(#784),#787);
#784 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#785,#786),.UNSPECIFIED.,.F.,.F.,
  (2,2),(2.666155957205,3.186137530399),.PIECEWISE_BEZIER_KNOTS.);
#785 = CARTESIAN_POINT('',(-1.053963816001,-4.316451884327));
#786 = CARTESIAN_POINT('',(-1.082773,-3.797269));
#787 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#788 = PCURVE('',#685,#789);
#789 = DEFINITIONAL_REPRESENTATION('',(#790),#793);
#790 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#791,#792),.UNSPECIFIED.,.F.,.F.,
  (2,2),(2.666155957205,3.186137530399),.PIECEWISE_BEZIER_KNOTS.);
#791 = CARTESIAN_POINT('',(9.583739687675,-20.02774));
#792 = CARTESIAN_POINT('',(10.103721260868,-20.02774));
#793 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#794 = FACE_BOUND('',#795,.F.);
#795 = EDGE_LOOP('',(#796));
#796 = ORIENTED_EDGE('',*,*,#797,.F.);
#797 = EDGE_CURVE('',#798,#798,#800,.T.);
#798 = VERTEX_POINT('',#799);
#799 = CARTESIAN_POINT('',(51.80692,135.691161,7.97226));
#800 = SURFACE_CURVE('',#801,(#806,#813),.PCURVE_S1.);
#801 = CIRCLE('',#802,0.275);
#802 = AXIS2_PLACEMENT_3D('',#803,#804,#805);
#803 = CARTESIAN_POINT('',(51.53192,135.691161,7.97226));
#804 = DIRECTION('',(0.,0.,1.));
#805 = DIRECTION('',(1.,0.,0.));
#806 = PCURVE('',#601,#807);
#807 = DEFINITIONAL_REPRESENTATION('',(#808),#812);
#808 = CIRCLE('',#809,0.275);
#809 = AXIS2_PLACEMENT_2D('',#810,#811);
#810 = CARTESIAN_POINT('',(0.,0.));
#811 = DIRECTION('',(1.,0.));
#812 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#813 = PCURVE('',#814,#819);
#814 = CYLINDRICAL_SURFACE('',#815,0.275);
#815 = AXIS2_PLACEMENT_3D('',#816,#817,#818);
#816 = CARTESIAN_POINT('',(51.53192,135.691161,7.97226));
#817 = DIRECTION('',(0.,0.,1.));
#818 = DIRECTION('',(1.,0.,0.));
#819 = DEFINITIONAL_REPRESENTATION('',(#820),#824);
#820 = LINE('',#821,#822);
#821 = CARTESIAN_POINT('',(0.,0.));
#822 = VECTOR('',#823,1.);
#823 = DIRECTION('',(1.,0.));
#824 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#825 = ADVANCED_FACE('',(#826),#685,.F.);
#826 = FACE_BOUND('',#827,.F.);
#827 = EDGE_LOOP('',(#828,#851,#870,#871));
#828 = ORIENTED_EDGE('',*,*,#829,.F.);
#829 = EDGE_CURVE('',#830,#751,#832,.T.);
#830 = VERTEX_POINT('',#831);
#831 = CARTESIAN_POINT('',(50.449147,131.893892,18.));
#832 = SURFACE_CURVE('',#833,(#837,#844),.PCURVE_S1.);
#833 = LINE('',#834,#835);
#834 = CARTESIAN_POINT('',(50.449147,131.893892,28.));
#835 = VECTOR('',#836,1.);
#836 = DIRECTION('',(0.,0.,-1.));
#837 = PCURVE('',#685,#838);
#838 = DEFINITIONAL_REPRESENTATION('',(#839),#843);
#839 = LINE('',#840,#841);
#840 = CARTESIAN_POINT('',(10.103721260868,0.));
#841 = VECTOR('',#842,1.);
#842 = DIRECTION('',(0.,-1.));
#843 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#844 = PCURVE('',#765,#845);
#845 = DEFINITIONAL_REPRESENTATION('',(#846),#850);
#846 = LINE('',#847,#848);
#847 = CARTESIAN_POINT('',(0.,0.));
#848 = VECTOR('',#849,1.);
#849 = DIRECTION('',(-0.,-1.));
#850 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#851 = ORIENTED_EDGE('',*,*,#852,.F.);
#852 = EDGE_CURVE('',#640,#830,#853,.T.);
#853 = SURFACE_CURVE('',#854,(#858,#864),.PCURVE_S1.);
#854 = LINE('',#855,#856);
#855 = CARTESIAN_POINT('',(50.625672529155,128.7126483595,18.));
#856 = VECTOR('',#857,1.);
#857 = DIRECTION('',(-5.540424023454E-02,0.998464005442,0.));
#858 = PCURVE('',#685,#859);
#859 = DEFINITIONAL_REPRESENTATION('',(#860),#863);
#860 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#861,#862),.UNSPECIFIED.,.F.,.F.,
  (2,2),(2.666155957205,3.186137530399),.PIECEWISE_BEZIER_KNOTS.);
#861 = CARTESIAN_POINT('',(9.583739687675,-10.));
#862 = CARTESIAN_POINT('',(10.103721260868,-10.));
#863 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#864 = PCURVE('',#656,#865);
#865 = DEFINITIONAL_REPRESENTATION('',(#866),#869);
#866 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#867,#868),.UNSPECIFIED.,.F.,.F.,
  (2,2),(2.666155957205,3.186137530399),.PIECEWISE_BEZIER_KNOTS.);
#867 = CARTESIAN_POINT('',(-1.053963816001,-4.316451884327));
#868 = CARTESIAN_POINT('',(-1.082773,-3.797269));
#869 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#870 = ORIENTED_EDGE('',*,*,#672,.F.);
#871 = ORIENTED_EDGE('',*,*,#776,.T.);
#872 = ADVANCED_FACE('',(#873,#941),#656,.T.);
#873 = FACE_BOUND('',#874,.T.);
#874 = EDGE_LOOP('',(#875,#876,#877,#898,#922));
#875 = ORIENTED_EDGE('',*,*,#852,.F.);
#876 = ORIENTED_EDGE('',*,*,#639,.F.);
#877 = ORIENTED_EDGE('',*,*,#878,.F.);
#878 = EDGE_CURVE('',#879,#614,#881,.T.);
#879 = VERTEX_POINT('',#880);
#880 = CARTESIAN_POINT('',(48.16434175799,132.99094366547,18.));
#881 = SURFACE_CURVE('',#882,(#886,#892),.PCURVE_S1.);
#882 = LINE('',#883,#884);
#883 = CARTESIAN_POINT('',(48.101543832237,133.5368369504,18.));
#884 = VECTOR('',#885,1.);
#885 = DIRECTION('',(-0.11428330053,0.993448200572,0.));
#886 = PCURVE('',#656,#887);
#887 = DEFINITIONAL_REPRESENTATION('',(#888),#891);
#888 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#889,#890),.UNSPECIFIED.,.F.,.F.,
  (2,2),(-1.74817464001,6.513450756865),.PIECEWISE_BEZIER_KNOTS.);
#889 = CARTESIAN_POINT('',(-3.230589,-3.891045));
#890 = CARTESIAN_POINT('',(-4.174754818094,4.316451884327));
#891 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#892 = PCURVE('',#628,#893);
#893 = DEFINITIONAL_REPRESENTATION('',(#894),#897);
#894 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#895,#896),.UNSPECIFIED.,.F.,.F.,
  (2,2),(-1.74817464001,6.513450756865),.PIECEWISE_BEZIER_KNOTS.);
#895 = CARTESIAN_POINT('',(0.,-10.));
#896 = CARTESIAN_POINT('',(8.261625396876,-10.));
#897 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#898 = ORIENTED_EDGE('',*,*,#899,.T.);
#899 = EDGE_CURVE('',#879,#900,#902,.T.);
#900 = VERTEX_POINT('',#901);
#901 = CARTESIAN_POINT('',(49.553535824782,131.85478863473,18.));
#902 = SURFACE_CURVE('',#903,(#908,#915),.PCURVE_S1.);
#903 = CIRCLE('',#904,4.316451884327);
#904 = AXIS2_PLACEMENT_3D('',#905,#906,#907);
#905 = CARTESIAN_POINT('',(51.53192,135.691161,18.));
#906 = DIRECTION('',(0.,0.,1.));
#907 = DIRECTION('',(1.,0.,0.));
#908 = PCURVE('',#656,#909);
#909 = DEFINITIONAL_REPRESENTATION('',(#910),#914);
#910 = CIRCLE('',#911,4.316451884327);
#911 = AXIS2_PLACEMENT_2D('',#912,#913);
#912 = CARTESIAN_POINT('',(0.,0.));
#913 = DIRECTION('',(1.,0.));
#914 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#915 = PCURVE('',#738,#916);
#916 = DEFINITIONAL_REPRESENTATION('',(#917),#921);
#917 = LINE('',#918,#919);
#918 = CARTESIAN_POINT('',(0.,10.02774));
#919 = VECTOR('',#920,1.);
#920 = DIRECTION('',(1.,0.));
#921 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#922 = ORIENTED_EDGE('',*,*,#923,.F.);
#923 = EDGE_CURVE('',#830,#900,#924,.T.);
#924 = SURFACE_CURVE('',#925,(#929,#935),.PCURVE_S1.);
#925 = LINE('',#926,#927);
#926 = CARTESIAN_POINT('',(51.072242161799,131.92109701751,18.));
#927 = VECTOR('',#928,1.);
#928 = DIRECTION('',(-0.999048214928,-4.361953975718E-02,0.));
#929 = PCURVE('',#656,#930);
#930 = DEFINITIONAL_REPRESENTATION('',(#931),#934);
#931 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#932,#933),.UNSPECIFIED.,.F.,.F.,
  (2,2),(0.623688779468,2.773550986223),.PIECEWISE_BEZIER_KNOTS.);
#932 = CARTESIAN_POINT('',(-1.082773,-3.797269));
#933 = CARTESIAN_POINT('',(-3.230589,-3.891045));
#934 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#935 = PCURVE('',#765,#936);
#936 = DEFINITIONAL_REPRESENTATION('',(#937),#940);
#937 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#938,#939),.UNSPECIFIED.,.F.,.F.,
  (2,2),(0.623688779468,2.773550986223),.PIECEWISE_BEZIER_KNOTS.);
#938 = CARTESIAN_POINT('',(0.,-10.));
#939 = CARTESIAN_POINT('',(2.149862206755,-10.));
#940 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#941 = FACE_BOUND('',#942,.T.);
#942 = EDGE_LOOP('',(#943));
#943 = ORIENTED_EDGE('',*,*,#944,.F.);
#944 = EDGE_CURVE('',#945,#945,#947,.T.);
#945 = VERTEX_POINT('',#946);
#946 = CARTESIAN_POINT('',(51.80692,135.691161,18.));
#947 = SURFACE_CURVE('',#948,(#953,#960),.PCURVE_S1.);
#948 = CIRCLE('',#949,0.275);
#949 = AXIS2_PLACEMENT_3D('',#950,#951,#952);
#950 = CARTESIAN_POINT('',(51.53192,135.691161,18.));
#951 = DIRECTION('',(0.,0.,1.));
#952 = DIRECTION('',(1.,0.,0.));
#953 = PCURVE('',#656,#954);
#954 = DEFINITIONAL_REPRESENTATION('',(#955),#959);
#955 = CIRCLE('',#956,0.275);
#956 = AXIS2_PLACEMENT_2D('',#957,#958);
#957 = CARTESIAN_POINT('',(0.,0.));
#958 = DIRECTION('',(1.,0.));
#959 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#960 = PCURVE('',#814,#961);
#961 = DEFINITIONAL_REPRESENTATION('',(#962),#966);
#962 = LINE('',#963,#964);
#963 = CARTESIAN_POINT('',(0.,10.02774));
#964 = VECTOR('',#965,1.);
#965 = DIRECTION('',(1.,0.));
#966 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#967 = ADVANCED_FACE('',(#968),#628,.F.);
#968 = FACE_BOUND('',#969,.F.);
#969 = EDGE_LOOP('',(#970,#989,#990,#991));
#970 = ORIENTED_EDGE('',*,*,#971,.F.);
#971 = EDGE_CURVE('',#701,#879,#972,.T.);
#972 = SURFACE_CURVE('',#973,(#977,#983),.PCURVE_S1.);
#973 = LINE('',#974,#975);
#974 = CARTESIAN_POINT('',(48.16434175799,132.99094366547,7.97226));
#975 = VECTOR('',#976,1.);
#976 = DIRECTION('',(0.,0.,1.));
#977 = PCURVE('',#628,#978);
#978 = DEFINITIONAL_REPRESENTATION('',(#979),#982);
#979 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#980,#981),.UNSPECIFIED.,.F.,.F.,
  (2,2),(-5.999999999062E-07,10.0277406),.PIECEWISE_BEZIER_KNOTS.);
#980 = CARTESIAN_POINT('',(1.198681184175,-20.0277406));
#981 = CARTESIAN_POINT('',(1.198681184175,-9.9999994));
#982 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#983 = PCURVE('',#738,#984);
#984 = DEFINITIONAL_REPRESENTATION('',(#985),#988);
#985 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#986,#987),.UNSPECIFIED.,.F.,.F.,
  (2,2),(-5.999999999062E-07,10.0277406),.PIECEWISE_BEZIER_KNOTS.);
#986 = CARTESIAN_POINT('',(3.81744702099,-5.999999999062E-07));
#987 = CARTESIAN_POINT('',(3.81744702099,10.0277406));
#988 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#989 = ORIENTED_EDGE('',*,*,#700,.T.);
#990 = ORIENTED_EDGE('',*,*,#613,.T.);
#991 = ORIENTED_EDGE('',*,*,#878,.F.);
#992 = ADVANCED_FACE('',(#993),#765,.F.);
#993 = FACE_BOUND('',#994,.F.);
#994 = EDGE_LOOP('',(#995,#996,#997,#998));
#995 = ORIENTED_EDGE('',*,*,#923,.F.);
#996 = ORIENTED_EDGE('',*,*,#829,.T.);
#997 = ORIENTED_EDGE('',*,*,#750,.T.);
#998 = ORIENTED_EDGE('',*,*,#999,.T.);
#999 = EDGE_CURVE('',#722,#900,#1000,.T.);
#1000 = SURFACE_CURVE('',#1001,(#1005,#1011),.PCURVE_S1.);
#1001 = LINE('',#1002,#1003);
#1002 = CARTESIAN_POINT('',(49.553535824782,131.85478863473,7.97226));
#1003 = VECTOR('',#1004,1.);
#1004 = DIRECTION('',(0.,0.,1.));
#1005 = PCURVE('',#765,#1006);
#1006 = DEFINITIONAL_REPRESENTATION('',(#1007),#1010);
#1007 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#1008,#1009),.UNSPECIFIED.,.F.,
  .F.,(2,2),(-5.999999999062E-07,10.0277406),.PIECEWISE_BEZIER_KNOTS.);
#1008 = CARTESIAN_POINT('',(0.896464416667,-20.0277406));
#1009 = CARTESIAN_POINT('',(0.896464416667,-9.9999994));
#1010 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1011 = PCURVE('',#738,#1012);
#1012 = DEFINITIONAL_REPRESENTATION('',(#1013),#1016);
#1013 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#1014,#1015),.UNSPECIFIED.,.F.,
  .F.,(2,2),(-5.999999999062E-07,10.0277406),.PIECEWISE_BEZIER_KNOTS.);
#1014 = CARTESIAN_POINT('',(4.236267209909,-5.999999999062E-07));
#1015 = CARTESIAN_POINT('',(4.236267209909,10.0277406));
#1016 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1017 = ADVANCED_FACE('',(#1018),#738,.T.);
#1018 = FACE_BOUND('',#1019,.T.);
#1019 = EDGE_LOOP('',(#1020,#1021,#1022,#1023));
#1020 = ORIENTED_EDGE('',*,*,#899,.F.);
#1021 = ORIENTED_EDGE('',*,*,#971,.F.);
#1022 = ORIENTED_EDGE('',*,*,#721,.T.);
#1023 = ORIENTED_EDGE('',*,*,#999,.T.);
#1024 = ADVANCED_FACE('',(#1025),#814,.F.);
#1025 = FACE_BOUND('',#1026,.F.);
#1026 = EDGE_LOOP('',(#1027,#1028,#1049,#1050));
#1027 = ORIENTED_EDGE('',*,*,#944,.F.);
#1028 = ORIENTED_EDGE('',*,*,#1029,.F.);
#1029 = EDGE_CURVE('',#798,#945,#1030,.T.);
#1030 = SEAM_CURVE('',#1031,(#1035,#1042),.PCURVE_S1.);
#1031 = LINE('',#1032,#1033);
#1032 = CARTESIAN_POINT('',(51.80692,135.691161,7.97226));
#1033 = VECTOR('',#1034,1.);
#1034 = DIRECTION('',(0.,0.,1.));
#1035 = PCURVE('',#814,#1036);
#1036 = DEFINITIONAL_REPRESENTATION('',(#1037),#1041);
#1037 = LINE('',#1038,#1039);
#1038 = CARTESIAN_POINT('',(6.28318530718,-0.));
#1039 = VECTOR('',#1040,1.);
#1040 = DIRECTION('',(0.,1.));
#1041 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1042 = PCURVE('',#814,#1043);
#1043 = DEFINITIONAL_REPRESENTATION('',(#1044),#1048);
#1044 = LINE('',#1045,#1046);
#1045 = CARTESIAN_POINT('',(0.,-0.));
#1046 = VECTOR('',#1047,1.);
#1047 = DIRECTION('',(0.,1.));
#1048 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1049 = ORIENTED_EDGE('',*,*,#797,.T.);
#1050 = ORIENTED_EDGE('',*,*,#1029,.T.);
#1051 = ( GEOMETRIC_REPRESENTATION_CONTEXT(3) 
GLOBAL_UNCERTAINTY_ASSIGNED_CONTEXT((#1055)) 
GLOBAL_UNIT_ASSIGNED_CONTEXT((#1052,#1053,#1054)) REPRESENTATION_CONTEXT
('Context #1','3D Context with UNIT and UNCERTAINTY') );
#1052 = ( LENGTH_UNIT() NAMED_UNIT(*) SI_UNIT(.MILLI.,.METRE.) );
#1053 = ( NAMED_UNIT(*) PLANE_ANGLE_UNIT() SI_UNIT($,.RADIAN.) );
#1054 = ( NAMED_UNIT(*) SI_UNIT($,.STERADIAN.) SOLID_ANGLE_UNIT() );
#1055 = UNCERTAINTY_MEASURE_WITH_UNIT(LENGTH_MEASURE(1.E-07),#1052,
  'distance_accuracy_value','confusion accuracy');
#1056 = CONTEXT_DEPENDENT_SHAPE_REPRESENTATION(#1057,#1059);
#1057 = ( REPRESENTATION_RELATIONSHIP('','',#570,#10) 
REPRESENTATION_RELATIONSHIP_WITH_TRANSFORMATION(#1058) 
SHAPE_REPRESENTATION_RELATIONSHIP() );
#1058 = ITEM_DEFINED_TRANSFORMATION('','',#11,#23);
#1059 = PRODUCT_DEFINITION_SHAPE('Placement','Placement of an item',
  #1060);
#1060 = NEXT_ASSEMBLY_USAGE_OCCURRENCE('86','=>[0:1:1:4]','',#5,#565,$);
#1061 = PRODUCT_RELATED_PRODUCT_CATEGORY('part',$,(#567));
#1062 = MECHANICAL_DESIGN_GEOMETRIC_PRESENTATION_REPRESENTATION('',(
    #1063),#139);
#1063 = STYLED_ITEM('color',(#1064),#41);
#1064 = PRESENTATION_STYLE_ASSIGNMENT((#1065));
#1065 = SURFACE_STYLE_USAGE(.BOTH.,#1066);
#1066 = SURFACE_SIDE_STYLE('',(#1067));
#1067 = SURFACE_STYLE_FILL_AREA(#1068);
#1068 = FILL_AREA_STYLE('',(#1069));
#1069 = FILL_AREA_STYLE_COLOUR('',#1070);
#1070 = DRAUGHTING_PRE_DEFINED_COLOUR('blue');
#1071 = MECHANICAL_DESIGN_GEOMETRIC_PRESENTATION_REPRESENTATION('',(
    #1072),#1051);
#1072 = STYLED_ITEM('color',(#1073),#571);
#1073 = PRESENTATION_STYLE_ASSIGNMENT((#1074));
#1074 = SURFACE_STYLE_USAGE(.BOTH.,#1075);
#1075 = SURFACE_SIDE_STYLE('',(#1076));
#1076 = SURFACE_STYLE_FILL_AREA(#1077);
#1077 = FILL_AREA_STYLE('',(#1078));
#1078 = FILL_AREA_STYLE_COLOUR('',#1079);
#1079 = DRAUGHTING_PRE_DEFINED_COLOUR('red');
#1080 = MECHANICAL_DESIGN_GEOMETRIC_PRESENTATION_REPRESENTATION('',(
    #1081),#552);
#1081 = STYLED_ITEM('color',(#1082),#158);
#1082 = PRESENTATION_STYLE_ASSIGNMENT((#1083));
#1083 = SURFACE_STYLE_USAGE(.BOTH.,#1084);
#1084 = SURFACE_SIDE_STYLE('',(#1085));
#1085 = SURFACE_STYLE_FILL_AREA(#1086);
#1086 = FILL_AREA_STYLE('',(#1087));
#1087 = FILL_AREA_STYLE_COLOUR('',#1088);
#1088 = DRAUGHTING_PRE_DEFINED_COLOUR('green');
ENDSEC;
END-ISO-10303-21;
