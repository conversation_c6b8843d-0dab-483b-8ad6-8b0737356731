ISO-10303-21;
HEADER;
FILE_DESCRIPTION(('Open CASCADE Model'),'2;1');
FILE_NAME('Group_3_(PC3-P1_PC3-P2_PC3-P3_and_1_more)',
  '2025-08-25T20:08:38',('Author'),('Open CASCADE'),
  'Open CASCADE STEP processor 7.8','build123d','Unknown');
FILE_SCHEMA(('AUTOMOTIVE_DESIGN { 1 0 10303 214 1 1 1 1 }'));
ENDSEC;
DATA;
#1 = APPLICATION_PROTOCOL_DEFINITION('international standard',
  'automotive_design',2000,#2);
#2 = APPLICATION_CONTEXT(
  'core data for automotive mechanical design processes');
#3 = SHAPE_DEFINITION_REPRESENTATION(#4,#10);
#4 = PRODUCT_DEFINITION_SHAPE('','',#5);
#5 = PRODUCT_DEFINITION('design','',#6,#9);
#6 = PRODUCT_DEFINITION_FORMATION('','',#7);
#7 = PRODUCT('Group_3_(PC3-P1_PC3-P2_PC3-P3_and_1_more)',
  'Group_3_(PC3-P1_PC3-P2_PC3-P3_and_1_more)','',(#8));
#8 = PRODUCT_CONTEXT('',#2,'mechanical');
#9 = PRODUCT_DEFINITION_CONTEXT('part definition',#2,'design');
#10 = SHAPE_REPRESENTATION('',(#11,#15,#19),#23);
#11 = AXIS2_PLACEMENT_3D('',#12,#13,#14);
#12 = CARTESIAN_POINT('',(0.,0.,0.));
#13 = DIRECTION('',(0.,0.,1.));
#14 = DIRECTION('',(1.,0.,-0.));
#15 = AXIS2_PLACEMENT_3D('',#16,#17,#18);
#16 = CARTESIAN_POINT('',(0.,0.,0.));
#17 = DIRECTION('',(0.,0.,1.));
#18 = DIRECTION('',(1.,0.,-0.));
#19 = AXIS2_PLACEMENT_3D('',#20,#21,#22);
#20 = CARTESIAN_POINT('',(0.,0.,0.));
#21 = DIRECTION('',(0.,0.,1.));
#22 = DIRECTION('',(1.,0.,-0.));
#23 = ( GEOMETRIC_REPRESENTATION_CONTEXT(3) 
GLOBAL_UNCERTAINTY_ASSIGNED_CONTEXT((#27)) GLOBAL_UNIT_ASSIGNED_CONTEXT(
(#24,#25,#26)) REPRESENTATION_CONTEXT('Context #1',
  '3D Context with UNIT and UNCERTAINTY') );
#24 = ( LENGTH_UNIT() NAMED_UNIT(*) SI_UNIT(.MILLI.,.METRE.) );
#25 = ( NAMED_UNIT(*) PLANE_ANGLE_UNIT() SI_UNIT($,.RADIAN.) );
#26 = ( NAMED_UNIT(*) SI_UNIT($,.STERADIAN.) SOLID_ANGLE_UNIT() );
#27 = UNCERTAINTY_MEASURE_WITH_UNIT(LENGTH_MEASURE(1.E-07),#24,
  'distance_accuracy_value','confusion accuracy');
#28 = PRODUCT_RELATED_PRODUCT_CATEGORY('part',$,(#7));
#29 = SHAPE_DEFINITION_REPRESENTATION(#30,#36);
#30 = PRODUCT_DEFINITION_SHAPE('','',#31);
#31 = PRODUCT_DEFINITION('design','',#32,#35);
#32 = PRODUCT_DEFINITION_FORMATION('','',#33);
#33 = PRODUCT('Group_3_Part2_1','Group_3_Part2_1','',(#34));
#34 = PRODUCT_CONTEXT('',#2,'mechanical');
#35 = PRODUCT_DEFINITION_CONTEXT('part definition',#2,'design');
#36 = ADVANCED_BREP_SHAPE_REPRESENTATION('',(#11,#37),#2491);
#37 = MANIFOLD_SOLID_BREP('',#38);
#38 = CLOSED_SHELL('',(#39,#314,#365,#511,#794,#855,#1157,#1498,#1656,
    #1683,#1710,#1968,#1992,#2020,#2084,#2110,#2137,#2145,#2172,#2464));
#39 = ADVANCED_FACE('',(#40),#55,.T.);
#40 = FACE_BOUND('',#41,.T.);
#41 = EDGE_LOOP('',(#42,#78,#101,#128,#129,#153,#236,#259,#285,#286));
#42 = ORIENTED_EDGE('',*,*,#43,.T.);
#43 = EDGE_CURVE('',#44,#46,#48,.T.);
#44 = VERTEX_POINT('',#45);
#45 = CARTESIAN_POINT('',(76.602741688399,102.35667563262,24.720607));
#46 = VERTEX_POINT('',#47);
#47 = CARTESIAN_POINT('',(76.633391516969,102.80648,24.720607));
#48 = SURFACE_CURVE('',#49,(#54,#66),.PCURVE_S1.);
#49 = CIRCLE('',#50,3.315897516969);
#50 = AXIS2_PLACEMENT_3D('',#51,#52,#53);
#51 = CARTESIAN_POINT('',(73.317494,102.80648,24.720607));
#52 = DIRECTION('',(0.,0.,1.));
#53 = DIRECTION('',(1.,0.,-0.));
#54 = PCURVE('',#55,#60);
#55 = CONICAL_SURFACE('',#56,0.275,0.523598775598);
#56 = AXIS2_PLACEMENT_3D('',#57,#58,#59);
#57 = CARTESIAN_POINT('',(73.317494,102.80648,19.453618));
#58 = DIRECTION('',(0.,0.,1.));
#59 = DIRECTION('',(1.,0.,0.));
#60 = DEFINITIONAL_REPRESENTATION('',(#61),#65);
#61 = LINE('',#62,#63);
#62 = CARTESIAN_POINT('',(0.,5.266989));
#63 = VECTOR('',#64,1.);
#64 = DIRECTION('',(1.,0.));
#65 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#66 = PCURVE('',#67,#72);
#67 = PLANE('',#68);
#68 = AXIS2_PLACEMENT_3D('',#69,#70,#71);
#69 = CARTESIAN_POINT('',(73.317494,105.15648,24.720607));
#70 = DIRECTION('',(0.,0.,1.));
#71 = DIRECTION('',(1.,0.,0.));
#72 = DEFINITIONAL_REPRESENTATION('',(#73),#77);
#73 = CIRCLE('',#74,3.315897516969);
#74 = AXIS2_PLACEMENT_2D('',#75,#76);
#75 = CARTESIAN_POINT('',(0.,-2.35));
#76 = DIRECTION('',(1.,0.));
#77 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#78 = ORIENTED_EDGE('',*,*,#79,.T.);
#79 = EDGE_CURVE('',#46,#80,#82,.T.);
#80 = VERTEX_POINT('',#81);
#81 = CARTESIAN_POINT('',(77.633945884327,102.80648,26.453618));
#82 = SEAM_CURVE('',#83,(#87,#94),.PCURVE_S1.);
#83 = LINE('',#84,#85);
#84 = CARTESIAN_POINT('',(73.592494,102.80648,19.453618));
#85 = VECTOR('',#86,1.);
#86 = DIRECTION('',(0.5,-1.224646799147E-16,0.866025403784));
#87 = PCURVE('',#55,#88);
#88 = DEFINITIONAL_REPRESENTATION('',(#89),#93);
#89 = LINE('',#90,#91);
#90 = CARTESIAN_POINT('',(0.,-0.));
#91 = VECTOR('',#92,1.);
#92 = DIRECTION('',(0.,1.));
#93 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#94 = PCURVE('',#55,#95);
#95 = DEFINITIONAL_REPRESENTATION('',(#96),#100);
#96 = LINE('',#97,#98);
#97 = CARTESIAN_POINT('',(6.28318530718,-0.));
#98 = VECTOR('',#99,1.);
#99 = DIRECTION('',(0.,1.));
#100 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#101 = ORIENTED_EDGE('',*,*,#102,.F.);
#102 = EDGE_CURVE('',#80,#80,#103,.T.);
#103 = SURFACE_CURVE('',#104,(#109,#116),.PCURVE_S1.);
#104 = CIRCLE('',#105,4.316451884327);
#105 = AXIS2_PLACEMENT_3D('',#106,#107,#108);
#106 = CARTESIAN_POINT('',(73.317494,102.80648,26.453618));
#107 = DIRECTION('',(0.,0.,1.));
#108 = DIRECTION('',(1.,0.,0.));
#109 = PCURVE('',#55,#110);
#110 = DEFINITIONAL_REPRESENTATION('',(#111),#115);
#111 = LINE('',#112,#113);
#112 = CARTESIAN_POINT('',(0.,7.));
#113 = VECTOR('',#114,1.);
#114 = DIRECTION('',(1.,0.));
#115 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#116 = PCURVE('',#117,#122);
#117 = PLANE('',#118);
#118 = AXIS2_PLACEMENT_3D('',#119,#120,#121);
#119 = CARTESIAN_POINT('',(73.317494,102.80648,26.453618));
#120 = DIRECTION('',(0.,0.,1.));
#121 = DIRECTION('',(1.,0.,0.));
#122 = DEFINITIONAL_REPRESENTATION('',(#123),#127);
#123 = CIRCLE('',#124,4.316451884327);
#124 = AXIS2_PLACEMENT_2D('',#125,#126);
#125 = CARTESIAN_POINT('',(0.,0.));
#126 = DIRECTION('',(1.,0.));
#127 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#128 = ORIENTED_EDGE('',*,*,#79,.F.);
#129 = ORIENTED_EDGE('',*,*,#130,.T.);
#130 = EDGE_CURVE('',#46,#131,#133,.T.);
#131 = VERTEX_POINT('',#132);
#132 = CARTESIAN_POINT('',(70.032246311601,102.35667563262,24.720607));
#133 = SURFACE_CURVE('',#134,(#139,#146),.PCURVE_S1.);
#134 = CIRCLE('',#135,3.315897516969);
#135 = AXIS2_PLACEMENT_3D('',#136,#137,#138);
#136 = CARTESIAN_POINT('',(73.317494,102.80648,24.720607));
#137 = DIRECTION('',(0.,0.,1.));
#138 = DIRECTION('',(1.,0.,-0.));
#139 = PCURVE('',#55,#140);
#140 = DEFINITIONAL_REPRESENTATION('',(#141),#145);
#141 = LINE('',#142,#143);
#142 = CARTESIAN_POINT('',(0.,5.266989));
#143 = VECTOR('',#144,1.);
#144 = DIRECTION('',(1.,0.));
#145 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#146 = PCURVE('',#67,#147);
#147 = DEFINITIONAL_REPRESENTATION('',(#148),#152);
#148 = CIRCLE('',#149,3.315897516969);
#149 = AXIS2_PLACEMENT_2D('',#150,#151);
#150 = CARTESIAN_POINT('',(0.,-2.35));
#151 = DIRECTION('',(1.,0.));
#152 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#153 = ORIENTED_EDGE('',*,*,#154,.F.);
#154 = EDGE_CURVE('',#155,#131,#157,.T.);
#155 = VERTEX_POINT('',#156);
#156 = CARTESIAN_POINT('',(75.576936917578,102.80648,22.890773957965));
#157 = SURFACE_CURVE('',#158,(#163,#204),.PCURVE_S1.);
#158 = HYPERBOLA('',#159,2.095746229988,1.063178131589);
#159 = AXIS2_PLACEMENT_3D('',#160,#161,#162);
#160 = CARTESIAN_POINT('',(73.317494,103.98148,18.110798527919));
#161 = DIRECTION('',(-0.,-0.971090712116,-0.238710763985));
#162 = DIRECTION('',(0.,-0.238710763985,0.971090712116));
#163 = PCURVE('',#55,#164);
#164 = DEFINITIONAL_REPRESENTATION('',(#165),#203);
#165 = B_SPLINE_CURVE_WITH_KNOTS('',8,(#166,#167,#168,#169,#170,#171,
    #172,#173,#174,#175,#176,#177,#178,#179,#180,#181,#182,#183,#184,
    #185,#186,#187,#188,#189,#190,#191,#192,#193,#194,#195,#196,#197,
    #198,#199,#200,#201,#202),.UNSPECIFIED.,.F.,.F.,(9,7,7,7,7,9),(
    -1.498255167626,-0.662057112332,-3.490857086133E-02,0.435452835242,
    1.140994944396,1.84653705355),.UNSPECIFIED.);
#166 = CARTESIAN_POINT('',(-1.06581410364E-13,3.437155957966));
#167 = CARTESIAN_POINT('',(4.918399791978E-02,2.985078347324));
#168 = CARTESIAN_POINT('',(0.104860903672,2.592684441427));
#169 = CARTESIAN_POINT('',(0.168033180095,2.252448092393));
#170 = CARTESIAN_POINT('',(0.239847701879,1.958233594101));
#171 = CARTESIAN_POINT('',(0.321596318667,1.705034498227));
#172 = CARTESIAN_POINT('',(0.414651814134,1.48878848002));
#173 = CARTESIAN_POINT('',(0.520138512911,1.306249329783));
#174 = CARTESIAN_POINT('',(0.726635492883,1.041383008102));
#175 = CARTESIAN_POINT('',(0.822172294235,0.945411551361));
#176 = CARTESIAN_POINT('',(0.924786271077,0.865919755711));
#177 = CARTESIAN_POINT('',(1.034186852773,0.802074593965));
#178 = CARTESIAN_POINT('',(1.149638253985,0.753252129418));
#179 = CARTESIAN_POINT('',(1.269689493663,0.719024141398));
#180 = CARTESIAN_POINT('',(1.392413616695,0.69915094836));
#181 = CARTESIAN_POINT('',(1.608356300634,0.689402460754));
#182 = CARTESIAN_POINT('',(1.701280111742,0.693269767279));
#183 = CARTESIAN_POINT('',(1.793740015837,0.705160290556));
#184 = CARTESIAN_POINT('',(1.884856033972,0.725111362056));
#185 = CARTESIAN_POINT('',(1.973826662506,0.753219390811));
#186 = CARTESIAN_POINT('',(2.060001873866,0.789640714452));
#187 = CARTESIAN_POINT('',(2.142911517376,0.834593568212));
#188 = CARTESIAN_POINT('',(2.341255524342,0.969012577438));
#189 = CARTESIAN_POINT('',(2.452224083857,1.06949717362));
#190 = CARTESIAN_POINT('',(2.554114659412,1.190770851052));
#191 = CARTESIAN_POINT('',(2.646706718352,1.334118596106));
#192 = CARTESIAN_POINT('',(2.730459819974,1.501178266964));
#193 = CARTESIAN_POINT('',(2.806029216314,1.693978076434));
#194 = CARTESIAN_POINT('',(2.874163350427,1.914989566959));
#195 = CARTESIAN_POINT('',(2.997033863695,2.41941441995));
#196 = CARTESIAN_POINT('',(3.051770259447,2.702827774364));
#197 = CARTESIAN_POINT('',(3.100546007275,3.020431367282));
#198 = CARTESIAN_POINT('',(3.144090316834,3.37573201221));
#199 = CARTESIAN_POINT('',(3.183051246313,3.772829053022));
#200 = CARTESIAN_POINT('',(3.217987566297,4.216509348935));
#201 = CARTESIAN_POINT('',(3.249385727491,4.712371550492));
#202 = CARTESIAN_POINT('',(3.277663024118,5.266988999999));
#203 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#204 = PCURVE('',#205,#210);
#205 = CONICAL_SURFACE('',#206,0.275,0.523598775598);
#206 = AXIS2_PLACEMENT_3D('',#207,#208,#209);
#207 = CARTESIAN_POINT('',(73.317494,105.15648,17.720607));
#208 = DIRECTION('',(0.,0.,1.));
#209 = DIRECTION('',(1.,0.,0.));
#210 = DEFINITIONAL_REPRESENTATION('',(#211),#235);
#211 = B_SPLINE_CURVE_WITH_KNOTS('',8,(#212,#213,#214,#215,#216,#217,
    #218,#219,#220,#221,#222,#223,#224,#225,#226,#227,#228,#229,#230,
    #231,#232,#233,#234),.UNSPECIFIED.,.F.,.F.,(9,7,7,9),(
    -1.498255167626,-0.662057112332,0.592239970609,1.84653705355),
  .UNSPECIFIED.);
#212 = CARTESIAN_POINT('',(5.478143677941,5.170166957966));
#213 = CARTESIAN_POINT('',(5.444055172324,4.718089347324));
#214 = CARTESIAN_POINT('',(5.406847587937,4.325695441426));
#215 = CARTESIAN_POINT('',(5.366334975283,3.985459092394));
#216 = CARTESIAN_POINT('',(5.322373714122,3.691244594101));
#217 = CARTESIAN_POINT('',(5.274886652503,3.438045498225));
#218 = CARTESIAN_POINT('',(5.223910702977,3.221799480021));
#219 = CARTESIAN_POINT('',(5.169638489751,3.039260329783));
#220 = CARTESIAN_POINT('',(5.026603866779,2.660879870239));
#221 = CARTESIAN_POINT('',(4.934162697194,2.5040219588));
#222 = CARTESIAN_POINT('',(4.836309454085,2.408832703716));
#223 = CARTESIAN_POINT('',(4.735006418408,2.370480590551));
#224 = CARTESIAN_POINT('',(4.633202462649,2.387164407697));
#225 = CARTESIAN_POINT('',(4.533947287857,2.45988243571));
#226 = CARTESIAN_POINT('',(4.439448320056,2.592601978672));
#227 = CARTESIAN_POINT('',(4.262814322372,2.993039020489));
#228 = CARTESIAN_POINT('',(4.180679053863,3.260756903998));
#229 = CARTESIAN_POINT('',(4.106146845032,3.603473826894));
#230 = CARTESIAN_POINT('',(4.039833789255,4.03223046047));
#231 = CARTESIAN_POINT('',(3.981466439091,4.562193644354));
#232 = CARTESIAN_POINT('',(3.930439614809,5.213722443434));
#233 = CARTESIAN_POINT('',(3.886001030408,6.014013423098));
#234 = CARTESIAN_POINT('',(3.847383050154,6.999999999999));
#235 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#236 = ORIENTED_EDGE('',*,*,#237,.F.);
#237 = EDGE_CURVE('',#238,#155,#240,.T.);
#238 = VERTEX_POINT('',#239);
#239 = CARTESIAN_POINT('',(73.592494,102.80648,19.453618));
#240 = SEAM_CURVE('',#241,(#245,#252),.PCURVE_S1.);
#241 = LINE('',#242,#243);
#242 = CARTESIAN_POINT('',(73.592494,102.80648,19.453618));
#243 = VECTOR('',#244,1.);
#244 = DIRECTION('',(0.5,-1.224646799147E-16,0.866025403784));
#245 = PCURVE('',#55,#246);
#246 = DEFINITIONAL_REPRESENTATION('',(#247),#251);
#247 = LINE('',#248,#249);
#248 = CARTESIAN_POINT('',(6.28318530718,-0.));
#249 = VECTOR('',#250,1.);
#250 = DIRECTION('',(0.,1.));
#251 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#252 = PCURVE('',#55,#253);
#253 = DEFINITIONAL_REPRESENTATION('',(#254),#258);
#254 = LINE('',#255,#256);
#255 = CARTESIAN_POINT('',(0.,-0.));
#256 = VECTOR('',#257,1.);
#257 = DIRECTION('',(0.,1.));
#258 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#259 = ORIENTED_EDGE('',*,*,#260,.T.);
#260 = EDGE_CURVE('',#238,#238,#261,.T.);
#261 = SURFACE_CURVE('',#262,(#267,#274),.PCURVE_S1.);
#262 = CIRCLE('',#263,0.275);
#263 = AXIS2_PLACEMENT_3D('',#264,#265,#266);
#264 = CARTESIAN_POINT('',(73.317494,102.80648,19.453618));
#265 = DIRECTION('',(0.,0.,1.));
#266 = DIRECTION('',(1.,0.,0.));
#267 = PCURVE('',#55,#268);
#268 = DEFINITIONAL_REPRESENTATION('',(#269),#273);
#269 = LINE('',#270,#271);
#270 = CARTESIAN_POINT('',(0.,0.));
#271 = VECTOR('',#272,1.);
#272 = DIRECTION('',(1.,0.));
#273 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#274 = PCURVE('',#275,#280);
#275 = CYLINDRICAL_SURFACE('',#276,0.275);
#276 = AXIS2_PLACEMENT_3D('',#277,#278,#279);
#277 = CARTESIAN_POINT('',(73.317494,102.80648,19.453618));
#278 = DIRECTION('',(0.,0.,1.));
#279 = DIRECTION('',(1.,0.,0.));
#280 = DEFINITIONAL_REPRESENTATION('',(#281),#284);
#281 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#282,#283),.UNSPECIFIED.,.F.,.F.,
  (2,2),(0.,6.28318530718),.PIECEWISE_BEZIER_KNOTS.);
#282 = CARTESIAN_POINT('',(0.,0.));
#283 = CARTESIAN_POINT('',(6.28318530718,0.));
#284 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#285 = ORIENTED_EDGE('',*,*,#237,.T.);
#286 = ORIENTED_EDGE('',*,*,#287,.F.);
#287 = EDGE_CURVE('',#44,#155,#288,.T.);
#288 = SURFACE_CURVE('',#289,(#294,#304),.PCURVE_S1.);
#289 = HYPERBOLA('',#290,2.095746229988,1.063178131589);
#290 = AXIS2_PLACEMENT_3D('',#291,#292,#293);
#291 = CARTESIAN_POINT('',(73.317494,103.98148,18.110798527919));
#292 = DIRECTION('',(-0.,-0.971090712116,-0.238710763985));
#293 = DIRECTION('',(0.,-0.238710763985,0.971090712116));
#294 = PCURVE('',#55,#295);
#295 = DEFINITIONAL_REPRESENTATION('',(#296),#303);
#296 = B_SPLINE_CURVE_WITH_KNOTS('',5,(#297,#298,#299,#300,#301,#302),
  .UNSPECIFIED.,.F.,.F.,(6,6),(-1.84653705355,-1.498255167626),
  .PIECEWISE_BEZIER_KNOTS.);
#297 = CARTESIAN_POINT('',(6.147114936652,5.266988999999));
#298 = CARTESIAN_POINT('',(6.169448901007,4.828941243474));
#299 = CARTESIAN_POINT('',(6.193912232583,4.430981754767));
#300 = CARTESIAN_POINT('',(6.220785999918,4.068684649144));
#301 = CARTESIAN_POINT('',(6.250408583159,3.738425145229));
#302 = CARTESIAN_POINT('',(6.283185307179,3.437155957966));
#303 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#304 = PCURVE('',#205,#305);
#305 = DEFINITIONAL_REPRESENTATION('',(#306),#313);
#306 = B_SPLINE_CURVE_WITH_KNOTS('',5,(#307,#308,#309,#310,#311,#312),
  .UNSPECIFIED.,.F.,.F.,(6,6),(-1.84653705355,-1.498255167626),
  .PIECEWISE_BEZIER_KNOTS.);
#307 = CARTESIAN_POINT('',(5.577394910616,6.999999999999));
#308 = CARTESIAN_POINT('',(5.560237963444,6.561952243474));
#309 = CARTESIAN_POINT('',(5.541824418945,6.163992754767));
#310 = CARTESIAN_POINT('',(5.522062422943,5.801695649144));
#311 = CARTESIAN_POINT('',(5.500860610035,5.471436145229));
#312 = CARTESIAN_POINT('',(5.478143677941,5.170166957966));
#313 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#314 = ADVANCED_FACE('',(#315),#67,.T.);
#315 = FACE_BOUND('',#316,.T.);
#316 = EDGE_LOOP('',(#317,#341,#363,#364));
#317 = ORIENTED_EDGE('',*,*,#318,.T.);
#318 = EDGE_CURVE('',#44,#319,#321,.T.);
#319 = VERTEX_POINT('',#320);
#320 = CARTESIAN_POINT('',(77.633945884327,105.15648,24.720607));
#321 = SURFACE_CURVE('',#322,(#327,#334),.PCURVE_S1.);
#322 = CIRCLE('',#323,4.316451884327);
#323 = AXIS2_PLACEMENT_3D('',#324,#325,#326);
#324 = CARTESIAN_POINT('',(73.317494,105.15648,24.720607));
#325 = DIRECTION('',(0.,0.,1.));
#326 = DIRECTION('',(1.,0.,0.));
#327 = PCURVE('',#67,#328);
#328 = DEFINITIONAL_REPRESENTATION('',(#329),#333);
#329 = CIRCLE('',#330,4.316451884327);
#330 = AXIS2_PLACEMENT_2D('',#331,#332);
#331 = CARTESIAN_POINT('',(0.,0.));
#332 = DIRECTION('',(1.,0.));
#333 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#334 = PCURVE('',#205,#335);
#335 = DEFINITIONAL_REPRESENTATION('',(#336),#340);
#336 = LINE('',#337,#338);
#337 = CARTESIAN_POINT('',(0.,7.));
#338 = VECTOR('',#339,1.);
#339 = DIRECTION('',(1.,0.));
#340 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#341 = ORIENTED_EDGE('',*,*,#342,.T.);
#342 = EDGE_CURVE('',#319,#131,#343,.T.);
#343 = SURFACE_CURVE('',#344,(#349,#356),.PCURVE_S1.);
#344 = CIRCLE('',#345,4.316451884327);
#345 = AXIS2_PLACEMENT_3D('',#346,#347,#348);
#346 = CARTESIAN_POINT('',(73.317494,105.15648,24.720607));
#347 = DIRECTION('',(0.,0.,1.));
#348 = DIRECTION('',(1.,0.,0.));
#349 = PCURVE('',#67,#350);
#350 = DEFINITIONAL_REPRESENTATION('',(#351),#355);
#351 = CIRCLE('',#352,4.316451884327);
#352 = AXIS2_PLACEMENT_2D('',#353,#354);
#353 = CARTESIAN_POINT('',(0.,0.));
#354 = DIRECTION('',(1.,0.));
#355 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#356 = PCURVE('',#205,#357);
#357 = DEFINITIONAL_REPRESENTATION('',(#358),#362);
#358 = LINE('',#359,#360);
#359 = CARTESIAN_POINT('',(0.,7.));
#360 = VECTOR('',#361,1.);
#361 = DIRECTION('',(1.,0.));
#362 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#363 = ORIENTED_EDGE('',*,*,#130,.F.);
#364 = ORIENTED_EDGE('',*,*,#43,.F.);
#365 = ADVANCED_FACE('',(#366,#422),#205,.T.);
#366 = FACE_BOUND('',#367,.T.);
#367 = EDGE_LOOP('',(#368,#396,#417,#418,#419,#420,#421));
#368 = ORIENTED_EDGE('',*,*,#369,.T.);
#369 = EDGE_CURVE('',#370,#370,#372,.T.);
#370 = VERTEX_POINT('',#371);
#371 = CARTESIAN_POINT('',(73.592494,105.15648,17.720607));
#372 = SURFACE_CURVE('',#373,(#378,#385),.PCURVE_S1.);
#373 = CIRCLE('',#374,0.275);
#374 = AXIS2_PLACEMENT_3D('',#375,#376,#377);
#375 = CARTESIAN_POINT('',(73.317494,105.15648,17.720607));
#376 = DIRECTION('',(0.,0.,1.));
#377 = DIRECTION('',(1.,0.,0.));
#378 = PCURVE('',#205,#379);
#379 = DEFINITIONAL_REPRESENTATION('',(#380),#384);
#380 = LINE('',#381,#382);
#381 = CARTESIAN_POINT('',(0.,0.));
#382 = VECTOR('',#383,1.);
#383 = DIRECTION('',(1.,0.));
#384 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#385 = PCURVE('',#386,#391);
#386 = CYLINDRICAL_SURFACE('',#387,0.275);
#387 = AXIS2_PLACEMENT_3D('',#388,#389,#390);
#388 = CARTESIAN_POINT('',(73.317494,105.15648,17.720607));
#389 = DIRECTION('',(0.,0.,1.));
#390 = DIRECTION('',(1.,0.,0.));
#391 = DEFINITIONAL_REPRESENTATION('',(#392),#395);
#392 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#393,#394),.UNSPECIFIED.,.F.,.F.,
  (2,2),(0.,6.28318530718),.PIECEWISE_BEZIER_KNOTS.);
#393 = CARTESIAN_POINT('',(0.,0.));
#394 = CARTESIAN_POINT('',(6.28318530718,0.));
#395 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#396 = ORIENTED_EDGE('',*,*,#397,.T.);
#397 = EDGE_CURVE('',#370,#319,#398,.T.);
#398 = SEAM_CURVE('',#399,(#403,#410),.PCURVE_S1.);
#399 = LINE('',#400,#401);
#400 = CARTESIAN_POINT('',(73.592494,105.15648,17.720607));
#401 = VECTOR('',#402,1.);
#402 = DIRECTION('',(0.5,-1.224646799147E-16,0.866025403784));
#403 = PCURVE('',#205,#404);
#404 = DEFINITIONAL_REPRESENTATION('',(#405),#409);
#405 = LINE('',#406,#407);
#406 = CARTESIAN_POINT('',(0.,-0.));
#407 = VECTOR('',#408,1.);
#408 = DIRECTION('',(0.,1.));
#409 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#410 = PCURVE('',#205,#411);
#411 = DEFINITIONAL_REPRESENTATION('',(#412),#416);
#412 = LINE('',#413,#414);
#413 = CARTESIAN_POINT('',(6.28318530718,-0.));
#414 = VECTOR('',#415,1.);
#415 = DIRECTION('',(0.,1.));
#416 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#417 = ORIENTED_EDGE('',*,*,#318,.F.);
#418 = ORIENTED_EDGE('',*,*,#287,.T.);
#419 = ORIENTED_EDGE('',*,*,#154,.T.);
#420 = ORIENTED_EDGE('',*,*,#342,.F.);
#421 = ORIENTED_EDGE('',*,*,#397,.F.);
#422 = FACE_BOUND('',#423,.T.);
#423 = EDGE_LOOP('',(#424,#484));
#424 = ORIENTED_EDGE('',*,*,#425,.F.);
#425 = EDGE_CURVE('',#426,#428,#430,.T.);
#426 = VERTEX_POINT('',#427);
#427 = CARTESIAN_POINT('',(75.486074304206,106.12110125878,21.355218));
#428 = VERTEX_POINT('',#429);
#429 = CARTESIAN_POINT('',(71.148913695794,106.12110125878,21.355218));
#430 = SURFACE_CURVE('',#431,(#436,#463),.PCURVE_S1.);
#431 = HYPERBOLA('',#432,4.181941424934,2.138017231077);
#432 = AXIS2_PLACEMENT_3D('',#433,#434,#435);
#433 = CARTESIAN_POINT('',(73.317494,107.5048705,15.561598527919));
#434 = DIRECTION('',(-0.,-0.972641949923,-0.232309356785));
#435 = DIRECTION('',(0.,-0.232309356785,0.972641949923));
#436 = PCURVE('',#205,#437);
#437 = DEFINITIONAL_REPRESENTATION('',(#438),#462);
#438 = B_SPLINE_CURVE_WITH_KNOTS('',8,(#439,#440,#441,#442,#443,#444,
    #445,#446,#447,#448,#449,#450,#451,#452,#453,#454,#455,#456,#457,
    #458,#459,#460,#461),.UNSPECIFIED.,.F.,.F.,(9,7,7,9),(-0.89144567954
    ,-0.44572283977,0.222861419885,0.89144567954),.UNSPECIFIED.);
#439 = CARTESIAN_POINT('',(0.418535340998,3.634611));
#440 = CARTESIAN_POINT('',(0.468724183486,3.404747427555));
#441 = CARTESIAN_POINT('',(0.522120581291,3.195437629241));
#442 = CARTESIAN_POINT('',(0.578866651069,3.005594334226));
#443 = CARTESIAN_POINT('',(0.639078432906,2.83426629359));
#444 = CARTESIAN_POINT('',(0.702819703451,2.680627667527));
#445 = CARTESIAN_POINT('',(0.770076036818,2.543969365978));
#446 = CARTESIAN_POINT('',(0.84072911325,2.423692342667));
#447 = CARTESIAN_POINT('',(1.025234519018,2.162718592416));
#448 = CARTESIAN_POINT('',(1.143024402492,2.041881255755));
#449 = CARTESIAN_POINT('',(1.267069586567,1.9551243106));
#450 = CARTESIAN_POINT('',(1.395925147231,1.901313869055));
#451 = CARTESIAN_POINT('',(1.527398812323,1.879811433702));
#452 = CARTESIAN_POINT('',(1.658751087002,1.890456479973));
#453 = CARTESIAN_POINT('',(1.787599558415,1.933566153469));
#454 = CARTESIAN_POINT('',(2.036554153622,2.086340731014));
#455 = CARTESIAN_POINT('',(2.156661032883,2.196005629748));
#456 = CARTESIAN_POINT('',(2.270532498761,2.339761162257));
#457 = CARTESIAN_POINT('',(2.376844084005,2.518916053642));
#458 = CARTESIAN_POINT('',(2.475071223079,2.735293316237));
#459 = CARTESIAN_POINT('',(2.565275181185,2.991266283724));
#460 = CARTESIAN_POINT('',(2.64777404886,3.289815641333));
#461 = CARTESIAN_POINT('',(2.723057312592,3.634611));
#462 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#463 = PCURVE('',#464,#469);
#464 = CONICAL_SURFACE('',#465,0.275,0.523598775598);
#465 = AXIS2_PLACEMENT_3D('',#466,#467,#468);
#466 = CARTESIAN_POINT('',(73.317494,109.853261,14.355218));
#467 = DIRECTION('',(0.,0.,1.));
#468 = DIRECTION('',(1.,0.,0.));
#469 = DEFINITIONAL_REPRESENTATION('',(#470),#483);
#470 = B_SPLINE_CURVE_WITH_KNOTS('',6,(#471,#472,#473,#474,#475,#476,
    #477,#478,#479,#480,#481,#482),.UNSPECIFIED.,.F.,.F.,(7,5,7),(
    -0.89144567954,0.,0.89144567954),.UNSPECIFIED.);
#471 = CARTESIAN_POINT('',(5.23875988889,7.));
#472 = CARTESIAN_POINT('',(5.165168342155,6.387030473481));
#473 = CARTESIAN_POINT('',(5.084336175163,5.927528109252));
#474 = CARTESIAN_POINT('',(4.996751865817,5.597145405476));
#475 = CARTESIAN_POINT('',(4.903752160067,5.381658646765));
#476 = CARTESIAN_POINT('',(4.808071044633,5.273912189931));
#477 = CARTESIAN_POINT('',(4.616706916136,5.273912189931));
#478 = CARTESIAN_POINT('',(4.521025800703,5.381658646765));
#479 = CARTESIAN_POINT('',(4.428026094951,5.597145405476));
#480 = CARTESIAN_POINT('',(4.340441785606,5.927528109252));
#481 = CARTESIAN_POINT('',(4.259609618614,6.387030473481));
#482 = CARTESIAN_POINT('',(4.18601807188,7.));
#483 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#484 = ORIENTED_EDGE('',*,*,#485,.T.);
#485 = EDGE_CURVE('',#426,#428,#486,.T.);
#486 = SURFACE_CURVE('',#487,(#492,#499),.PCURVE_S1.);
#487 = CIRCLE('',#488,2.37344363925);
#488 = AXIS2_PLACEMENT_3D('',#489,#490,#491);
#489 = CARTESIAN_POINT('',(73.317494,105.15648,21.355218));
#490 = DIRECTION('',(0.,0.,1.));
#491 = DIRECTION('',(1.,0.,-0.));
#492 = PCURVE('',#205,#493);
#493 = DEFINITIONAL_REPRESENTATION('',(#494),#498);
#494 = LINE('',#495,#496);
#495 = CARTESIAN_POINT('',(0.,3.634611));
#496 = VECTOR('',#497,1.);
#497 = DIRECTION('',(1.,0.));
#498 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#499 = PCURVE('',#500,#505);
#500 = PLANE('',#501);
#501 = AXIS2_PLACEMENT_3D('',#502,#503,#504);
#502 = CARTESIAN_POINT('',(73.317494,109.853261,21.355218));
#503 = DIRECTION('',(0.,0.,1.));
#504 = DIRECTION('',(1.,0.,0.));
#505 = DEFINITIONAL_REPRESENTATION('',(#506),#510);
#506 = CIRCLE('',#507,2.37344363925);
#507 = AXIS2_PLACEMENT_2D('',#508,#509);
#508 = CARTESIAN_POINT('',(0.,-4.696781));
#509 = DIRECTION('',(1.,0.));
#510 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#511 = ADVANCED_FACE('',(#512),#275,.F.);
#512 = FACE_BOUND('',#513,.F.);
#513 = EDGE_LOOP('',(#514,#771,#792,#793));
#514 = ORIENTED_EDGE('',*,*,#515,.F.);
#515 = EDGE_CURVE('',#516,#516,#518,.T.);
#516 = VERTEX_POINT('',#517);
#517 = CARTESIAN_POINT('',(73.592494,102.80648,21.342387097117));
#518 = SURFACE_CURVE('',#519,(#582,#648),.PCURVE_S1.);
#519 = B_SPLINE_CURVE_WITH_KNOTS('',7,(#520,#521,#522,#523,#524,#525,
    #526,#527,#528,#529,#530,#531,#532,#533,#534,#535,#536,#537,#538,
    #539,#540,#541,#542,#543,#544,#545,#546,#547,#548,#549,#550,#551,
    #552,#553,#554,#555,#556,#557,#558,#559,#560,#561,#562,#563,#564,
    #565,#566,#567,#568,#569,#570,#571,#572,#573,#574,#575,#576,#577,
    #578,#579,#580,#581),.UNSPECIFIED.,.T.,.F.,(8,6,6,6,6,6,6,6,6,6,8),(
    0.,1.920165779599E-02,0.159574701836,0.253129753271,0.336270303069,
    0.451817879143,0.48732882828,0.648733529805,0.75306621898,
    0.856857970433,1.),.UNSPECIFIED.);
#520 = CARTESIAN_POINT('',(73.592494,102.80648,21.342387097117));
#521 = CARTESIAN_POINT('',(73.592494,102.81070473029,21.335119243306));
#522 = CARTESIAN_POINT('',(73.592418279633,102.8149259266,
    21.327842431661));
#523 = CARTESIAN_POINT('',(73.592267067072,102.8191426095,
    21.320558279987));
#524 = CARTESIAN_POINT('',(73.592040564291,102.82335380561,
    21.313268416162));
#525 = CARTESIAN_POINT('',(73.591738947063,102.82755854754,
    21.305974478131));
#526 = CARTESIAN_POINT('',(73.591362364964,102.83175587394,
    21.298678113915));
#527 = CARTESIAN_POINT('',(73.58761082503,102.86656804131,
    21.238035552454));
#528 = CARTESIAN_POINT('',(73.580317819539,102.89668025624,
    21.184759950672));
#529 = CARTESIAN_POINT('',(73.569099645964,102.92600474685,
    21.132039949271));
#530 = CARTESIAN_POINT('',(73.553880948255,102.95419414851,
    21.080529748711));
#531 = CARTESIAN_POINT('',(73.534542109181,102.98075043201,
    21.031215584471));
#532 = CARTESIAN_POINT('',(73.51094892623,103.00504240802,
    20.985450200862));
#533 = CARTESIAN_POINT('',(73.464329206538,103.04016647646,
    20.918542841032));
#534 = CARTESIAN_POINT('',(73.443731890843,103.0527903771,
    20.894269872526));
#535 = CARTESIAN_POINT('',(73.421209543679,103.06365516282,
    20.873198628044));
#536 = CARTESIAN_POINT('',(73.396907964189,103.07234045654,
    20.856226724923));
#537 = CARTESIAN_POINT('',(73.371151998096,103.0783889152,
    20.844361757416));
#538 = CARTESIAN_POINT('',(73.344550560164,103.08143762873,
    20.838381569068));
#539 = CARTESIAN_POINT('',(73.294269313434,103.08151700729,
    20.838225860144));
#540 = CARTESIAN_POINT('',(73.270600520393,103.07917978721,
    20.842810055467));
#541 = CARTESIAN_POINT('',(73.247509088651,103.07446442039,
    20.85206196586));
#542 = CARTESIAN_POINT('',(73.225445449491,103.06759318644,
    20.865500639064));
#543 = CARTESIAN_POINT('',(73.204698868313,103.05887445206,
    20.882456409617));
#544 = CARTESIAN_POINT('',(73.185415331236,103.04861364771,
    20.902267766874));
#545 = CARTESIAN_POINT('',(73.14297812229,103.02103872442,
    20.955093583669));
#546 = CARTESIAN_POINT('',(73.121232530211,103.00252622686,
    20.990259499485));
#547 = CARTESIAN_POINT('',(73.102432430176,102.98214034186,
    21.028557535225));
#548 = CARTESIAN_POINT('',(73.08651695636,102.96033815471,
    21.069004436671));
#549 = CARTESIAN_POINT('',(73.073396801392,102.93746189373,
    21.110889366509));
#550 = CARTESIAN_POINT('',(73.062995911036,102.91375917519,
    21.153721020014));
#551 = CARTESIAN_POINT('',(73.052922846711,102.88190406878,
    21.21053482695));
#552 = CARTESIAN_POINT('',(73.050810563303,102.87435625056,
    21.22394376014));
#553 = CARTESIAN_POINT('',(73.04895342845,102.86675542654,
    21.237393838084));
#554 = CARTESIAN_POINT('',(73.047351225381,102.85910780001,
    21.250873518786));
#555 = CARTESIAN_POINT('',(73.046004071956,102.85141948701,
    21.26437167519));
#556 = CARTESIAN_POINT('',(73.044912420666,102.84369651632,
    21.277877595185));
#557 = CARTESIAN_POINT('',(73.040280163232,102.800711789,21.352756701124
    ));
#558 = CARTESIAN_POINT('',(73.041785995061,102.76496245378,
    21.413945899063));
#559 = CARTESIAN_POINT('',(73.048614084368,102.72915084814,
    21.474169452337));
#560 = CARTESIAN_POINT('',(73.061036243644,102.69384780544,
    21.532527769246));
#561 = CARTESIAN_POINT('',(73.079376286065,102.65989410123,
    21.587753185226));
#562 = CARTESIAN_POINT('',(73.103903578289,102.62846305495,
    21.638186189079));
#563 = CARTESIAN_POINT('',(73.154378178951,102.58349420844,
    21.709633051785));
#564 = CARTESIAN_POINT('',(73.176757414824,102.5675584702,
    21.734744917873));
#565 = CARTESIAN_POINT('',(73.201609395779,102.55384065239,
    21.756204577505));
#566 = CARTESIAN_POINT('',(73.228686110441,102.54290755395,
    21.773206757375));
#567 = CARTESIAN_POINT('',(73.257506764989,102.53533617896,
    21.784946944155));
#568 = CARTESIAN_POINT('',(73.287281590404,102.53152735337,
    21.790852971735));
#569 = CARTESIAN_POINT('',(73.346683738033,102.53143357573,
    21.790998383162));
#570 = CARTESIAN_POINT('',(73.376323615769,102.53510969973,
    21.785298125962));
#571 = CARTESIAN_POINT('',(73.405041996903,102.54251584297,
    21.773814907004));
#572 = CARTESIAN_POINT('',(73.432055694609,102.55326426499,
    21.757102455022));
#573 = CARTESIAN_POINT('',(73.456882712297,102.56678954959,
    21.735949142303));
#574 = CARTESIAN_POINT('',(73.479271055051,102.58253246814,
    21.711149265441));
#575 = CARTESIAN_POINT('',(73.526510148213,102.62408664317,
    21.645154854212));
#576 = CARTESIAN_POINT('',(73.549091817957,102.65146610684,
    21.601287453382));
#577 = CARTESIAN_POINT('',(73.566795355434,102.68100746389,
    21.553398768485));
#578 = CARTESIAN_POINT('',(73.579783410718,102.71183468392,
    21.502728749479));
#579 = CARTESIAN_POINT('',(73.588281070406,102.74330563157,
    21.450230069662));
#580 = CARTESIAN_POINT('',(73.592494,102.77496732037,21.396598734912));
#581 = CARTESIAN_POINT('',(73.592494,102.80648,21.342387097117));
#582 = PCURVE('',#275,#583);
#583 = DEFINITIONAL_REPRESENTATION('',(#584),#647);
#584 = B_SPLINE_CURVE_WITH_KNOTS('',7,(#585,#586,#587,#588,#589,#590,
    #591,#592,#593,#594,#595,#596,#597,#598,#599,#600,#601,#602,#603,
    #604,#605,#606,#607,#608,#609,#610,#611,#612,#613,#614,#615,#616,
    #617,#618,#619,#620,#621,#622,#623,#624,#625,#626,#627,#628,#629,
    #630,#631,#632,#633,#634,#635,#636,#637,#638,#639,#640,#641,#642,
    #643,#644,#645,#646),.UNSPECIFIED.,.F.,.F.,(8,6,6,6,6,6,6,6,6,6,8),(
    0.,1.920165779599E-02,0.159574701836,0.253129753271,0.336270303069,
    0.451817879143,0.48732882828,0.648733529805,0.75306621898,
    0.856857970433,1.),.UNSPECIFIED.);
#585 = CARTESIAN_POINT('',(0.,1.888769097117));
#586 = CARTESIAN_POINT('',(1.536265562914E-02,1.881501243305));
#587 = CARTESIAN_POINT('',(3.071246924308E-02,1.874224431661));
#588 = CARTESIAN_POINT('',(4.60517805922E-02,1.866940279987));
#589 = CARTESIAN_POINT('',(6.138293246681E-02,1.859650416162));
#590 = CARTESIAN_POINT('',(7.670827069717E-02,1.852356478131));
#591 = CARTESIAN_POINT('',(9.203014415346E-02,1.845060113915));
#592 = CARTESIAN_POINT('',(0.21935278626,1.784417552454));
#593 = CARTESIAN_POINT('',(0.331062192573,1.731141950672));
#594 = CARTESIAN_POINT('',(0.443751920831,1.678421949271));
#595 = CARTESIAN_POINT('',(0.55842089446,1.626911748711));
#596 = CARTESIAN_POINT('',(0.676066146421,1.577597584471));
#597 = CARTESIAN_POINT('',(0.797744017627,1.531832200862));
#598 = CARTESIAN_POINT('',(1.009999857267,1.464924841032));
#599 = CARTESIAN_POINT('',(1.097444290722,1.440651872526));
#600 = CARTESIAN_POINT('',(1.187629017183,1.419580628044));
#601 = CARTESIAN_POINT('',(1.280483375963,1.402608724923));
#602 = CARTESIAN_POINT('',(1.375659059245,1.390743757416));
#603 = CARTESIAN_POINT('',(1.472408713999,1.384763569068));
#604 = CARTESIAN_POINT('',(1.655249839041,1.384607860144));
#605 = CARTESIAN_POINT('',(1.741302684734,1.389192055467));
#606 = CARTESIAN_POINT('',(1.826291389735,1.39844396586));
#607 = CARTESIAN_POINT('',(1.90959584607,1.411882639064));
#608 = CARTESIAN_POINT('',(1.990882433446,1.428838409617));
#609 = CARTESIAN_POINT('',(2.07000972606,1.448649766874));
#610 = CARTESIAN_POINT('',(2.254043251477,1.501475583669));
#611 = CARTESIAN_POINT('',(2.357030455164,1.536641499485));
#612 = CARTESIAN_POINT('',(2.45674194139,1.574939535225));
#613 = CARTESIAN_POINT('',(2.553805635336,1.615386436671));
#614 = CARTESIAN_POINT('',(2.648811144242,1.657271366509));
#615 = CARTESIAN_POINT('',(2.742396669295,1.700103020014));
#616 = CARTESIAN_POINT('',(2.863886834425,1.75691682695));
#617 = CARTESIAN_POINT('',(2.892372276311,1.77032576014));
#618 = CARTESIAN_POINT('',(2.920799532081,1.783775838084));
#619 = CARTESIAN_POINT('',(2.949184618723,1.797255518786));
#620 = CARTESIAN_POINT('',(2.977543200896,1.81075367519));
#621 = CARTESIAN_POINT('',(3.005890590935,1.824259595185));
#622 = CARTESIAN_POINT('',(3.163103699635,1.899138701124));
#623 = CARTESIAN_POINT('',(3.291766768268,1.960327899063));
#624 = CARTESIAN_POINT('',(3.4220726279,2.020551452337));
#625 = CARTESIAN_POINT('',(3.555316005693,2.078909769246));
#626 = CARTESIAN_POINT('',(3.692680673844,2.134135185226));
#627 = CARTESIAN_POINT('',(3.835304905033,2.184568189079));
#628 = CARTESIAN_POINT('',(4.081126380766,2.256015051785));
#629 = CARTESIAN_POINT('',(4.180435990917,2.281126917873));
#630 = CARTESIAN_POINT('',(4.28256409793,2.302586577505));
#631 = CARTESIAN_POINT('',(4.387312420628,2.319588757375));
#632 = CARTESIAN_POINT('',(4.494231689651,2.331328944155));
#633 = CARTESIAN_POINT('',(4.602525537171,2.337234971735));
#634 = CARTESIAN_POINT('',(4.818533615905,2.337380383162));
#635 = CARTESIAN_POINT('',(4.926293789497,2.331680125962));
#636 = CARTESIAN_POINT('',(5.032723017218,2.320196907004));
#637 = CARTESIAN_POINT('',(5.137026333566,2.303484455022));
#638 = CARTESIAN_POINT('',(5.238750314537,2.282331142303));
#639 = CARTESIAN_POINT('',(5.337688321872,2.257531265441));
#640 = CARTESIAN_POINT('',(5.566469682302,2.191536854212));
#641 = CARTESIAN_POINT('',(5.693883723916,2.147669453381));
#642 = CARTESIAN_POINT('',(5.816982025133,2.099780768488));
#643 = CARTESIAN_POINT('',(5.93656424062,2.049110749475));
#644 = CARTESIAN_POINT('',(6.053460020117,1.996612069665));
#645 = CARTESIAN_POINT('',(6.168593744921,1.942980734912));
#646 = CARTESIAN_POINT('',(6.28318530718,1.888769097117));
#647 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#648 = PCURVE('',#649,#654);
#649 = CYLINDRICAL_SURFACE('',#650,0.275);
#650 = AXIS2_PLACEMENT_3D('',#651,#652,#653);
#651 = CARTESIAN_POINT('',(73.317494,102.80648,19.453618));
#652 = DIRECTION('',(0.,0.,1.));
#653 = DIRECTION('',(1.,0.,0.));
#654 = DEFINITIONAL_REPRESENTATION('',(#655),#770);
#655 = B_SPLINE_CURVE_WITH_KNOTS('',9,(#656,#657,#658,#659,#660,#661,
    #662,#663,#664,#665,#666,#667,#668,#669,#670,#671,#672,#673,#674,
    #675,#676,#677,#678,#679,#680,#681,#682,#683,#684,#685,#686,#687,
    #688,#689,#690,#691,#692,#693,#694,#695,#696,#697,#698,#699,#700,
    #701,#702,#703,#704,#705,#706,#707,#708,#709,#710,#711,#712,#713,
    #714,#715,#716,#717,#718,#719,#720,#721,#722,#723,#724,#725,#726,
    #727,#728,#729,#730,#731,#732,#733,#734,#735,#736,#737,#738,#739,
    #740,#741,#742,#743,#744,#745,#746,#747,#748,#749,#750,#751,#752,
    #753,#754,#755,#756,#757,#758,#759,#760,#761,#762,#763,#764,#765,
    #766,#767,#768,#769),.UNSPECIFIED.,.F.,.F.,(10,8,8,8,8,8,8,8,8,8,8,8
    ,8,8,10),(0.,3.125E-02,0.15234375,0.25830078125,0.304656982422,
    0.34811592102,0.429601430893,0.465251341462,0.498673132621,
    0.624004849466,0.671004243282,0.753253182462,0.814939886846,
    0.861204915135,1.),.UNSPECIFIED.);
#656 = CARTESIAN_POINT('',(5.062616992291E-14,1.888769097117));
#657 = CARTESIAN_POINT('',(1.944612719609E-02,1.879569411004));
#658 = CARTESIAN_POINT('',(3.887150871697E-02,1.870356309368));
#659 = CARTESIAN_POINT('',(5.828720137172E-02,1.861129447164));
#660 = CARTESIAN_POINT('',(7.76762349874E-02,1.851901842681));
#661 = CARTESIAN_POINT('',(9.707306219434E-02,1.842662006242));
#662 = CARTESIAN_POINT('',(0.116474640626,1.833416360488));
#663 = CARTESIAN_POINT('',(0.135849827052,1.824184770755));
#664 = CARTESIAN_POINT('',(0.155235093619,1.814954908342));
#665 = CARTESIAN_POINT('',(0.249765308975,1.770003339772));
#666 = CARTESIAN_POINT('',(0.325007230909,1.734399617636));
#667 = CARTESIAN_POINT('',(0.400668004859,1.699068576901));
#668 = CARTESIAN_POINT('',(0.477037098582,1.664191549489));
#669 = CARTESIAN_POINT('',(0.554387710134,1.62999120971));
#670 = CARTESIAN_POINT('',(0.632992137404,1.596740686887));
#671 = CARTESIAN_POINT('',(0.713141922585,1.564776110609));
#672 = CARTESIAN_POINT('',(0.795172771601,1.534512588615));
#673 = CARTESIAN_POINT('',(0.953275540761,1.481920767398));
#674 = CARTESIAN_POINT('',(1.028794300227,1.459078495949));
#675 = CARTESIAN_POINT('',(1.106528526758,1.438220556352));
#676 = CARTESIAN_POINT('',(1.186030273461,1.420008782887));
#677 = CARTESIAN_POINT('',(1.267784347165,1.404814470086));
#678 = CARTESIAN_POINT('',(1.351124564612,1.393379641275));
#679 = CARTESIAN_POINT('',(1.435916095758,1.386127365151));
#680 = CARTESIAN_POINT('',(1.521441670508,1.383378916447));
#681 = CARTESIAN_POINT('',(1.643952576808,1.38574863349));
#682 = CARTESIAN_POINT('',(1.681183999396,1.387311859171));
#683 = CARTESIAN_POINT('',(1.718290987328,1.389709126415));
#684 = CARTESIAN_POINT('',(1.755214787381,1.392921332306));
#685 = CARTESIAN_POINT('',(1.791906713097,1.396920306675));
#686 = CARTESIAN_POINT('',(1.82832703552,1.401670881861));
#687 = CARTESIAN_POINT('',(1.864443873937,1.407132962483));
#688 = CARTESIAN_POINT('',(1.900232086624,1.413263595207));
#689 = CARTESIAN_POINT('',(1.968897231859,1.426352266609));
#690 = CARTESIAN_POINT('',(2.001816248362,1.433234426039));
#691 = CARTESIAN_POINT('',(2.034416071642,1.440630133901));
#692 = CARTESIAN_POINT('',(2.066674730981,1.448498175335));
#693 = CARTESIAN_POINT('',(2.098608683718,1.456808491762));
#694 = CARTESIAN_POINT('',(2.130198984396,1.465520063263));
#695 = CARTESIAN_POINT('',(2.161421999964,1.47459043006));
#696 = CARTESIAN_POINT('',(2.192342425879,1.484005188337));
#697 = CARTESIAN_POINT('',(2.280363571482,1.511973756291));
#698 = CARTESIAN_POINT('',(2.336707170713,1.531318252749));
#699 = CARTESIAN_POINT('',(2.392092910849,1.551594318094));
#700 = CARTESIAN_POINT('',(2.44661773794,1.572650744934));
#701 = CARTESIAN_POINT('',(2.500374337367,1.594356127203));
#702 = CARTESIAN_POINT('',(2.553453563771,1.6165973117));
#703 = CARTESIAN_POINT('',(2.605946870962,1.639277849643));
#704 = CARTESIAN_POINT('',(2.657948741849,1.662316448212));
#705 = CARTESIAN_POINT('',(2.732138658075,1.695851848173));
#706 = CARTESIAN_POINT('',(2.754641818854,1.706113189885));
#707 = CARTESIAN_POINT('',(2.77708720822,1.716428557559));
#708 = CARTESIAN_POINT('',(2.799454145889,1.726779150662));
#709 = CARTESIAN_POINT('',(2.821788515471,1.737177550511));
#710 = CARTESIAN_POINT('',(2.844096357072,1.747618111882));
#711 = CARTESIAN_POINT('',(2.866344105929,1.758076965935));
#712 = CARTESIAN_POINT('',(2.888566063027,1.768561988257));
#713 = CARTESIAN_POINT('',(2.931569459522,1.788911432635));
#714 = CARTESIAN_POINT('',(2.952353455309,1.798773355204));
#715 = CARTESIAN_POINT('',(2.973128083384,1.808651176175));
#716 = CARTESIAN_POINT('',(2.993873379115,1.818528966553));
#717 = CARTESIAN_POINT('',(3.014625022697,1.828417466034));
#718 = CARTESIAN_POINT('',(3.035386775974,1.838312279661));
#719 = CARTESIAN_POINT('',(3.056129689508,1.848193475847));
#720 = CARTESIAN_POINT('',(3.076880826409,1.858067898237));
#721 = CARTESIAN_POINT('',(3.175497928523,1.904915411769));
#722 = CARTESIAN_POINT('',(3.253503284616,1.941735431101));
#723 = CARTESIAN_POINT('',(3.331986882016,1.978232427853));
#724 = CARTESIAN_POINT('',(3.411236373403,2.014227863952));
#725 = CARTESIAN_POINT('',(3.491509877228,2.049516633184));
#726 = CARTESIAN_POINT('',(3.57304728295,2.083860868334));
#727 = CARTESIAN_POINT('',(3.656081556266,2.116983748317));
#728 = CARTESIAN_POINT('',(3.740850044353,2.148563305317));
#729 = CARTESIAN_POINT('',(3.860139182379,2.189349829389));
#730 = CARTESIAN_POINT('',(3.892950473162,2.200203516096));
#731 = CARTESIAN_POINT('',(3.926063857536,2.21077071047));
#732 = CARTESIAN_POINT('',(3.959461471542,2.221021656449));
#733 = CARTESIAN_POINT('',(3.993196317853,2.230946381185));
#734 = CARTESIAN_POINT('',(4.027284692412,2.240521667497));
#735 = CARTESIAN_POINT('',(4.061692157836,2.249708521376));
#736 = CARTESIAN_POINT('',(4.096431014466,2.258483850478));
#737 = CARTESIAN_POINT('',(4.192846294043,2.281400850756));
#738 = CARTESIAN_POINT('',(4.255186193016,2.294631522075));
#739 = CARTESIAN_POINT('',(4.318474177088,2.306354030673));
#740 = CARTESIAN_POINT('',(4.382647545611,2.316411447578));
#741 = CARTESIAN_POINT('',(4.447613798939,2.32465306661));
#742 = CARTESIAN_POINT('',(4.513245273536,2.330944682035));
#743 = CARTESIAN_POINT('',(4.579372146484,2.335181280531));
#744 = CARTESIAN_POINT('',(4.645773809391,2.33730214746));
#745 = CARTESIAN_POINT('',(4.761964713464,2.33731306743));
#746 = CARTESIAN_POINT('',(4.811765653488,2.336128041429));
#747 = CARTESIAN_POINT('',(4.861452773438,2.333753350794));
#748 = CARTESIAN_POINT('',(4.910926274136,2.330207665386));
#749 = CARTESIAN_POINT('',(4.96010327844,2.325525000534));
#750 = CARTESIAN_POINT('',(5.008915894098,2.319751434003));
#751 = CARTESIAN_POINT('',(5.057309276604,2.312941822962));
#752 = CARTESIAN_POINT('',(5.105239692062,2.305156520951));
#753 = CARTESIAN_POINT('',(5.18824724601,2.289934275265));
#754 = CARTESIAN_POINT('',(5.22354339434,2.282896559936));
#755 = CARTESIAN_POINT('',(5.258542330411,2.275372950941));
#756 = CARTESIAN_POINT('',(5.293236559911,2.267389941588));
#757 = CARTESIAN_POINT('',(5.327620438053,2.258974756023));
#758 = CARTESIAN_POINT('',(5.361686311696,2.25015598489));
#759 = CARTESIAN_POINT('',(5.395426154364,2.24096261703));
#760 = CARTESIAN_POINT('',(5.428838694166,2.231421467234));
#761 = CARTESIAN_POINT('',(5.561252055909,2.191947598446));
#762 = CARTESIAN_POINT('',(5.657781200273,2.159395312562));
#763 = CARTESIAN_POINT('',(5.751861972291,2.124483351232));
#764 = CARTESIAN_POINT('',(5.84381101004,2.087716377382));
#765 = CARTESIAN_POINT('',(5.933940042884,2.049521851561));
#766 = CARTESIAN_POINT('',(6.022569858354,2.010257214795));
#767 = CARTESIAN_POINT('',(6.110047278113,1.970218910761));
#768 = CARTESIAN_POINT('',(6.196765143005,1.929653247277));
#769 = CARTESIAN_POINT('',(6.28318530718,1.888769097117));
#770 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#771 = ORIENTED_EDGE('',*,*,#772,.F.);
#772 = EDGE_CURVE('',#238,#516,#773,.T.);
#773 = SEAM_CURVE('',#774,(#778,#785),.PCURVE_S1.);
#774 = LINE('',#775,#776);
#775 = CARTESIAN_POINT('',(73.592494,102.80648,19.453618));
#776 = VECTOR('',#777,1.);
#777 = DIRECTION('',(0.,0.,1.));
#778 = PCURVE('',#275,#779);
#779 = DEFINITIONAL_REPRESENTATION('',(#780),#784);
#780 = LINE('',#781,#782);
#781 = CARTESIAN_POINT('',(6.28318530718,-0.));
#782 = VECTOR('',#783,1.);
#783 = DIRECTION('',(0.,1.));
#784 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#785 = PCURVE('',#275,#786);
#786 = DEFINITIONAL_REPRESENTATION('',(#787),#791);
#787 = LINE('',#788,#789);
#788 = CARTESIAN_POINT('',(0.,-0.));
#789 = VECTOR('',#790,1.);
#790 = DIRECTION('',(0.,1.));
#791 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#792 = ORIENTED_EDGE('',*,*,#260,.T.);
#793 = ORIENTED_EDGE('',*,*,#772,.T.);
#794 = ADVANCED_FACE('',(#795,#798,#824),#117,.T.);
#795 = FACE_BOUND('',#796,.T.);
#796 = EDGE_LOOP('',(#797));
#797 = ORIENTED_EDGE('',*,*,#102,.T.);
#798 = FACE_BOUND('',#799,.T.);
#799 = EDGE_LOOP('',(#800));
#800 = ORIENTED_EDGE('',*,*,#801,.F.);
#801 = EDGE_CURVE('',#802,#802,#804,.T.);
#802 = VERTEX_POINT('',#803);
#803 = CARTESIAN_POINT('',(73.592494,102.80648,26.453618));
#804 = SURFACE_CURVE('',#805,(#810,#817),.PCURVE_S1.);
#805 = CIRCLE('',#806,0.275);
#806 = AXIS2_PLACEMENT_3D('',#807,#808,#809);
#807 = CARTESIAN_POINT('',(73.317494,102.80648,26.453618));
#808 = DIRECTION('',(0.,0.,1.));
#809 = DIRECTION('',(1.,0.,0.));
#810 = PCURVE('',#117,#811);
#811 = DEFINITIONAL_REPRESENTATION('',(#812),#816);
#812 = CIRCLE('',#813,0.275);
#813 = AXIS2_PLACEMENT_2D('',#814,#815);
#814 = CARTESIAN_POINT('',(0.,0.));
#815 = DIRECTION('',(1.,0.));
#816 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#817 = PCURVE('',#649,#818);
#818 = DEFINITIONAL_REPRESENTATION('',(#819),#823);
#819 = LINE('',#820,#821);
#820 = CARTESIAN_POINT('',(0.,7.));
#821 = VECTOR('',#822,1.);
#822 = DIRECTION('',(1.,0.));
#823 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#824 = FACE_BOUND('',#825,.T.);
#825 = EDGE_LOOP('',(#826));
#826 = ORIENTED_EDGE('',*,*,#827,.F.);
#827 = EDGE_CURVE('',#828,#828,#830,.T.);
#828 = VERTEX_POINT('',#829);
#829 = CARTESIAN_POINT('',(73.592494,105.15648,26.453618));
#830 = SURFACE_CURVE('',#831,(#836,#843),.PCURVE_S1.);
#831 = CIRCLE('',#832,0.275);
#832 = AXIS2_PLACEMENT_3D('',#833,#834,#835);
#833 = CARTESIAN_POINT('',(73.317494,105.15648,26.453618));
#834 = DIRECTION('',(0.,0.,1.));
#835 = DIRECTION('',(1.,0.,-0.));
#836 = PCURVE('',#117,#837);
#837 = DEFINITIONAL_REPRESENTATION('',(#838),#842);
#838 = CIRCLE('',#839,0.275);
#839 = AXIS2_PLACEMENT_2D('',#840,#841);
#840 = CARTESIAN_POINT('',(0.,2.35));
#841 = DIRECTION('',(1.,0.));
#842 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#843 = PCURVE('',#844,#849);
#844 = CYLINDRICAL_SURFACE('',#845,0.275);
#845 = AXIS2_PLACEMENT_3D('',#846,#847,#848);
#846 = CARTESIAN_POINT('',(73.317494,105.15648,17.720607));
#847 = DIRECTION('',(0.,0.,1.));
#848 = DIRECTION('',(1.,0.,0.));
#849 = DEFINITIONAL_REPRESENTATION('',(#850),#854);
#850 = LINE('',#851,#852);
#851 = CARTESIAN_POINT('',(0.,8.733011));
#852 = VECTOR('',#853,1.);
#853 = DIRECTION('',(1.,0.));
#854 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#855 = ADVANCED_FACE('',(#856),#386,.F.);
#856 = FACE_BOUND('',#857,.F.);
#857 = EDGE_LOOP('',(#858,#1134,#1155,#1156));
#858 = ORIENTED_EDGE('',*,*,#859,.F.);
#859 = EDGE_CURVE('',#860,#860,#862,.T.);
#860 = VERTEX_POINT('',#861);
#861 = CARTESIAN_POINT('',(73.592494,105.15648,23.075398097117));
#862 = SURFACE_CURVE('',#863,(#926,#992),.PCURVE_S1.);
#863 = B_SPLINE_CURVE_WITH_KNOTS('',7,(#864,#865,#866,#867,#868,#869,
    #870,#871,#872,#873,#874,#875,#876,#877,#878,#879,#880,#881,#882,
    #883,#884,#885,#886,#887,#888,#889,#890,#891,#892,#893,#894,#895,
    #896,#897,#898,#899,#900,#901,#902,#903,#904,#905,#906,#907,#908,
    #909,#910,#911,#912,#913,#914,#915,#916,#917,#918,#919,#920,#921,
    #922,#923,#924,#925),.UNSPECIFIED.,.T.,.F.,(8,6,6,6,6,6,6,6,6,6,8),(
    0.,0.143181081455,0.246568902072,0.351367901661,0.512824817841,
    0.653156997237,0.746684813431,0.8298011598,0.945315098287,
    0.980815709668,1.),.UNSPECIFIED.);
#864 = CARTESIAN_POINT('',(73.592494,105.15648,23.075398097117));
#865 = CARTESIAN_POINT('',(73.592494,105.18799290207,23.129610117597));
#866 = CARTESIAN_POINT('',(73.588281011129,105.21965446395,
    23.183241222235));
#867 = CARTESIAN_POINT('',(73.579783398866,105.25112523273,
    23.235739604726));
#868 = CARTESIAN_POINT('',(73.566795426842,105.28195241826,
    23.286409581336));
#869 = CARTESIAN_POINT('',(73.549091864422,105.31149390506,
    23.33429847498));
#870 = CARTESIAN_POINT('',(73.526510052015,105.33887344144,
    23.378165988602));
#871 = CARTESIAN_POINT('',(73.479353792009,105.38035475175,
    23.444044679461));
#872 = CARTESIAN_POINT('',(73.457069989264,105.39603856812,
    23.468752376836));
#873 = CARTESIAN_POINT('',(73.432367809976,105.40952541825,
    23.489847068759));
#874 = CARTESIAN_POINT('',(73.405494927632,105.42026361107,
    23.506545177678));
#875 = CARTESIAN_POINT('',(73.376924949489,105.42769473304,
    23.51806790534));
#876 = CARTESIAN_POINT('',(73.347427891952,105.43143308631,
    23.523864653522));
#877 = CARTESIAN_POINT('',(73.288025733927,105.43152686397,
    23.524010064975));
#878 = CARTESIAN_POINT('',(73.258107731005,105.42778084954,
    23.518201430515));
#879 = CARTESIAN_POINT('',(73.229138240721,105.42023456622,
    23.506500950162));
#880 = CARTESIAN_POINT('',(73.20192048108,105.40929097375,
    23.489484058534));
#881 = CARTESIAN_POINT('',(73.17694372887,105.39553427801,
    23.467965108604));
#882 = CARTESIAN_POINT('',(73.154460313286,105.37953896669,
    23.442760313041));
#883 = CARTESIAN_POINT('',(73.103903578289,105.33449694504,
    23.371197189078));
#884 = CARTESIAN_POINT('',(73.079376286065,105.30306589876,
    23.320764185225));
#885 = CARTESIAN_POINT('',(73.061036243644,105.26911219456,
    23.265538769246));
#886 = CARTESIAN_POINT('',(73.048614084368,105.23380915185,
    23.207180452334));
#887 = CARTESIAN_POINT('',(73.041785995061,105.19799754621,
    23.146956899063));
#888 = CARTESIAN_POINT('',(73.040280163232,105.162248211,23.085767701123
    ));
#889 = CARTESIAN_POINT('',(73.04737717497,105.09639195868,
    22.971046552453));
#890 = CARTESIAN_POINT('',(73.054670180461,105.06627974375,
    22.917770950671));
#891 = CARTESIAN_POINT('',(73.065888354036,105.03695525314,
    22.86505094927));
#892 = CARTESIAN_POINT('',(73.081107051745,105.00876585149,
    22.813540748711));
#893 = CARTESIAN_POINT('',(73.100445890819,104.98220956798,
    22.76422658447));
#894 = CARTESIAN_POINT('',(73.12403907377,104.95791759197,
    22.718461200861));
#895 = CARTESIAN_POINT('',(73.170658793463,104.92279352353,
    22.651553841031));
#896 = CARTESIAN_POINT('',(73.191256109156,104.91016962289,
    22.627280872526));
#897 = CARTESIAN_POINT('',(73.213778456322,104.89930483717,
    22.606209628041));
#898 = CARTESIAN_POINT('',(73.238080035809,104.89061954345,
    22.589237724923));
#899 = CARTESIAN_POINT('',(73.263836001905,104.88457108479,
    22.577372757415));
#900 = CARTESIAN_POINT('',(73.290437439836,104.88152237126,
    22.571392569067));
#901 = CARTESIAN_POINT('',(73.340718686566,104.8814429927,
    22.571236860143));
#902 = CARTESIAN_POINT('',(73.364387479607,104.88378021278,
    22.575821055466));
#903 = CARTESIAN_POINT('',(73.387478911349,104.8884955796,22.58507296586
    ));
#904 = CARTESIAN_POINT('',(73.409542550509,104.89536681356,
    22.598511639063));
#905 = CARTESIAN_POINT('',(73.430289131687,104.90408554793,
    22.615467409616));
#906 = CARTESIAN_POINT('',(73.449572668764,104.91434635228,
    22.635278766873));
#907 = CARTESIAN_POINT('',(73.49200987771,104.94192127557,
    22.688104583668));
#908 = CARTESIAN_POINT('',(73.513755469789,104.96043377313,
    22.723270499485));
#909 = CARTESIAN_POINT('',(73.532555569824,104.98081965813,
    22.761568535224));
#910 = CARTESIAN_POINT('',(73.54847104364,105.00262184529,
    22.802015436671));
#911 = CARTESIAN_POINT('',(73.561591198608,105.02549810626,
    22.843900366508));
#912 = CARTESIAN_POINT('',(73.571992088964,105.0492008248,
    22.886732020013));
#913 = CARTESIAN_POINT('',(73.582065153289,105.08105593121,
    22.94354582695));
#914 = CARTESIAN_POINT('',(73.584177436697,105.08860374943,
    22.956954760139));
#915 = CARTESIAN_POINT('',(73.58603457155,105.09620457345,
    22.970404838083));
#916 = CARTESIAN_POINT('',(73.587636774619,105.10385219998,
    22.983884518785));
#917 = CARTESIAN_POINT('',(73.588983928044,105.11154051298,
    22.997382675189));
#918 = CARTESIAN_POINT('',(73.590075579334,105.11926348367,
    23.010888595184));
#919 = CARTESIAN_POINT('',(73.591362365359,105.13120412972,
    23.03168912031));
#920 = CARTESIAN_POINT('',(73.59173894771,105.13540145657,
    23.038985485349));
#921 = CARTESIAN_POINT('',(73.592040564979,105.13960620057,
    23.046279426928));
#922 = CARTESIAN_POINT('',(73.592267067602,105.14381740154,
    23.053569299093));
#923 = CARTESIAN_POINT('',(73.592418279883,105.14803408968,
    23.060853459726));
#924 = CARTESIAN_POINT('',(73.592494,105.15225528553,23.068130270548));
#925 = CARTESIAN_POINT('',(73.592494,105.15648,23.075398097117));
#926 = PCURVE('',#386,#927);
#927 = DEFINITIONAL_REPRESENTATION('',(#928),#991);
#928 = B_SPLINE_CURVE_WITH_KNOTS('',7,(#929,#930,#931,#932,#933,#934,
    #935,#936,#937,#938,#939,#940,#941,#942,#943,#944,#945,#946,#947,
    #948,#949,#950,#951,#952,#953,#954,#955,#956,#957,#958,#959,#960,
    #961,#962,#963,#964,#965,#966,#967,#968,#969,#970,#971,#972,#973,
    #974,#975,#976,#977,#978,#979,#980,#981,#982,#983,#984,#985,#986,
    #987,#988,#989,#990),.UNSPECIFIED.,.F.,.F.,(8,6,6,6,6,6,6,6,6,6,8),(
    0.,0.143181081455,0.246568902072,0.351367901661,0.512824817841,
    0.653156997237,0.746684813431,0.8298011598,0.945315098287,
    0.980815709668,1.),.UNSPECIFIED.);
#929 = CARTESIAN_POINT('',(0.,5.354791097116));
#930 = CARTESIAN_POINT('',(0.114592371172,5.409003117597));
#931 = CARTESIAN_POINT('',(0.229725636465,5.462634222237));
#932 = CARTESIAN_POINT('',(0.346620807768,5.515132604722));
#933 = CARTESIAN_POINT('',(0.466202835912,5.565802581338));
#934 = CARTESIAN_POINT('',(0.58930148671,5.613691474979));
#935 = CARTESIAN_POINT('',(0.716716090768,5.657558988602));
#936 = CARTESIAN_POINT('',(0.945096285961,5.723437679461));
#937 = CARTESIAN_POINT('',(1.043606927127,5.748145376836));
#938 = CARTESIAN_POINT('',(1.144879608132,5.769240068759));
#939 = CARTESIAN_POINT('',(1.248713054444,5.785938177678));
#940 = CARTESIAN_POINT('',(1.354662289441,5.79746090534));
#941 = CARTESIAN_POINT('',(1.461945676277,5.803257653522));
#942 = CARTESIAN_POINT('',(1.677953792813,5.803403064975));
#943 = CARTESIAN_POINT('',(1.786724920666,5.797594430515));
#944 = CARTESIAN_POINT('',(1.894124631744,5.785893950162));
#945 = CARTESIAN_POINT('',(1.999343040511,5.768877058534));
#946 = CARTESIAN_POINT('',(2.101922354079,5.747358108604));
#947 = CARTESIAN_POINT('',(2.201658915654,5.722153313041));
#948 = CARTESIAN_POINT('',(2.447880402147,5.650590189078));
#949 = CARTESIAN_POINT('',(2.590504633336,5.600157185225));
#950 = CARTESIAN_POINT('',(2.727869301484,5.544931769246));
#951 = CARTESIAN_POINT('',(2.861112679283,5.486573452334));
#952 = CARTESIAN_POINT('',(2.99141853891,5.426349899063));
#953 = CARTESIAN_POINT('',(3.120081607545,5.365160701123));
#954 = CARTESIAN_POINT('',(3.36094543985,5.250439552453));
#955 = CARTESIAN_POINT('',(3.472654846162,5.197163950671));
#956 = CARTESIAN_POINT('',(3.585344574421,5.14444394927));
#957 = CARTESIAN_POINT('',(3.700013548048,5.092933748711));
#958 = CARTESIAN_POINT('',(3.817658800012,5.04361958447));
#959 = CARTESIAN_POINT('',(3.939336671217,4.997854200861));
#960 = CARTESIAN_POINT('',(4.151592510858,4.930946841031));
#961 = CARTESIAN_POINT('',(4.239036944308,4.906673872526));
#962 = CARTESIAN_POINT('',(4.329221670779,4.885602628041));
#963 = CARTESIAN_POINT('',(4.422076029547,4.868630724923));
#964 = CARTESIAN_POINT('',(4.517251712838,4.856765757415));
#965 = CARTESIAN_POINT('',(4.614001367588,4.850785569067));
#966 = CARTESIAN_POINT('',(4.796842492631,4.850629860143));
#967 = CARTESIAN_POINT('',(4.882895338324,4.855214055466));
#968 = CARTESIAN_POINT('',(4.967884043325,4.86446596586));
#969 = CARTESIAN_POINT('',(5.051188499659,4.877904639063));
#970 = CARTESIAN_POINT('',(5.132475087036,4.894860409616));
#971 = CARTESIAN_POINT('',(5.21160237965,4.914671766873));
#972 = CARTESIAN_POINT('',(5.395635905066,4.967497583668));
#973 = CARTESIAN_POINT('',(5.498623108754,5.002663499485));
#974 = CARTESIAN_POINT('',(5.598334594981,5.040961535224));
#975 = CARTESIAN_POINT('',(5.695398288926,5.081408436671));
#976 = CARTESIAN_POINT('',(5.790403797831,5.123293366508));
#977 = CARTESIAN_POINT('',(5.883989322885,5.166125020013));
#978 = CARTESIAN_POINT('',(6.005479488015,5.22293882695));
#979 = CARTESIAN_POINT('',(6.033964929901,5.236347760139));
#980 = CARTESIAN_POINT('',(6.062392185671,5.249797838083));
#981 = CARTESIAN_POINT('',(6.090777272313,5.263277518785));
#982 = CARTESIAN_POINT('',(6.119135854486,5.276775675189));
#983 = CARTESIAN_POINT('',(6.147483244525,5.290281595184));
#984 = CARTESIAN_POINT('',(6.191155176455,5.31108212031));
#985 = CARTESIAN_POINT('',(6.20647705195,5.318378485349));
#986 = CARTESIAN_POINT('',(6.221802397958,5.325672426928));
#987 = CARTESIAN_POINT('',(6.237133567495,5.332962299093));
#988 = CARTESIAN_POINT('',(6.252472897561,5.340246459726));
#989 = CARTESIAN_POINT('',(6.267822709136,5.347523270547));
#990 = CARTESIAN_POINT('',(6.28318530718,5.354791097116));
#991 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#992 = PCURVE('',#844,#993);
#993 = DEFINITIONAL_REPRESENTATION('',(#994),#1133);
#994 = B_SPLINE_CURVE_WITH_KNOTS('',9,(#995,#996,#997,#998,#999,#1000,
    #1001,#1002,#1003,#1004,#1005,#1006,#1007,#1008,#1009,#1010,#1011,
    #1012,#1013,#1014,#1015,#1016,#1017,#1018,#1019,#1020,#1021,#1022,
    #1023,#1024,#1025,#1026,#1027,#1028,#1029,#1030,#1031,#1032,#1033,
    #1034,#1035,#1036,#1037,#1038,#1039,#1040,#1041,#1042,#1043,#1044,
    #1045,#1046,#1047,#1048,#1049,#1050,#1051,#1052,#1053,#1054,#1055,
    #1056,#1057,#1058,#1059,#1060,#1061,#1062,#1063,#1064,#1065,#1066,
    #1067,#1068,#1069,#1070,#1071,#1072,#1073,#1074,#1075,#1076,#1077,
    #1078,#1079,#1080,#1081,#1082,#1083,#1084,#1085,#1086,#1087,#1088,
    #1089,#1090,#1091,#1092,#1093,#1094,#1095,#1096,#1097,#1098,#1099,
    #1100,#1101,#1102,#1103,#1104,#1105,#1106,#1107,#1108,#1109,#1110,
    #1111,#1112,#1113,#1114,#1115,#1116,#1117,#1118,#1119,#1120,#1121,
    #1122,#1123,#1124,#1125,#1126,#1127,#1128,#1129,#1130,#1131,#1132),
  .UNSPECIFIED.,.F.,.F.,(10,8,8,8,8,8,8,8,8,8,8,8,8,8,8,8,8,10),(0.,
    0.125,0.1796875,0.2822265625,0.327087402344,0.369144439697,
    0.448001384735,0.482501298189,0.514844967052,0.636133725289,
    0.681617009628,0.721414883425,0.756238022997,0.817178517247,
    0.862883887936,0.931441943968,0.965720971984,1.),.UNSPECIFIED.);
#995 = CARTESIAN_POINT('',(0.,5.354791097117));
#996 = CARTESIAN_POINT('',(7.781003510956E-02,5.391601917713));
#997 = CARTESIAN_POINT('',(0.155861711352,5.428153748507));
#998 = CARTESIAN_POINT('',(0.23449002129,5.464295769895));
#999 = CARTESIAN_POINT('',(0.31398806683,5.499850348614));
#1000 = CARTESIAN_POINT('',(0.394617411809,5.534607512931));
#1001 = CARTESIAN_POINT('',(0.476618434568,5.568319427854));
#1002 = CARTESIAN_POINT('',(0.560220680536,5.600694870328));
#1003 = CARTESIAN_POINT('',(0.645653214804,5.631393704435));
#1004 = CARTESIAN_POINT('',(0.77143699467,5.672545954416));
#1005 = CARTESIAN_POINT('',(0.810117722818,5.684675071933));
#1006 = CARTESIAN_POINT('',(0.849199955373,5.696370361542));
#1007 = CARTESIAN_POINT('',(0.888737810592,5.707608634907));
#1008 = CARTESIAN_POINT('',(0.928746613605,5.718348395317));
#1009 = CARTESIAN_POINT('',(0.969203143126,5.728537956768));
#1010 = CARTESIAN_POINT('',(1.01008708429,5.73812972739));
#1011 = CARTESIAN_POINT('',(1.051404795413,5.747082190332));
#1012 = CARTESIAN_POINT('',(1.171355443725,5.770846596254));
#1013 = CARTESIAN_POINT('',(1.251009223674,5.783932206788));
#1014 = CARTESIAN_POINT('',(1.331968904506,5.794314626517));
#1015 = CARTESIAN_POINT('',(1.413997234784,5.801688029867));
#1016 = CARTESIAN_POINT('',(1.49680681769,5.80588393184));
#1017 = CARTESIAN_POINT('',(1.580188108965,5.806635088986));
#1018 = CARTESIAN_POINT('',(1.663082269595,5.80403593737));
#1019 = CARTESIAN_POINT('',(1.745334833149,5.79822862088));
#1020 = CARTESIAN_POINT('',(1.862157224733,5.785585582377));
#1021 = CARTESIAN_POINT('',(1.897524362629,5.78116579376));
#1022 = CARTESIAN_POINT('',(1.932679819045,5.776192616291));
#1023 = CARTESIAN_POINT('',(1.967602976611,5.770686958006));
#1024 = CARTESIAN_POINT('',(2.002276523975,5.764671601574));
#1025 = CARTESIAN_POINT('',(2.036686200721,5.758170689369));
#1026 = CARTESIAN_POINT('',(2.070820542303,5.751209208545));
#1027 = CARTESIAN_POINT('',(2.10467062496,5.743812476111));
#1028 = CARTESIAN_POINT('',(2.169691547234,5.728686700155));
#1029 = CARTESIAN_POINT('',(2.200897325918,5.721007399075));
#1030 = CARTESIAN_POINT('',(2.231842348069,5.712988554861));
#1031 = CARTESIAN_POINT('',(2.262525016538,5.704649923557));
#1032 = CARTESIAN_POINT('',(2.292938444335,5.696013591806));
#1033 = CARTESIAN_POINT('',(2.323079539948,5.687100760271));
#1034 = CARTESIAN_POINT('',(2.352959858795,5.677927515897));
#1035 = CARTESIAN_POINT('',(2.382602410336,5.668505207051));
#1036 = CARTESIAN_POINT('',(2.4671452353,5.640749464188));
#1037 = CARTESIAN_POINT('',(2.521456684448,5.621831108579));
#1038 = CARTESIAN_POINT('',(2.575007007859,5.602192997359));
#1039 = CARTESIAN_POINT('',(2.627857505705,5.58192396176));
#1040 = CARTESIAN_POINT('',(2.680067785322,5.561105215759));
#1041 = CARTESIAN_POINT('',(2.731696818302,5.539810936142));
#1042 = CARTESIAN_POINT('',(2.782803997581,5.518108842577));
#1043 = CARTESIAN_POINT('',(2.833450194526,5.496060777686));
#1044 = CARTESIAN_POINT('',(2.905682587934,5.463950634989));
#1045 = CARTESIAN_POINT('',(2.9275902588,5.454122582405));
#1046 = CARTESIAN_POINT('',(2.949427138971,5.444243863726));
#1047 = CARTESIAN_POINT('',(2.971198628722,5.434319017133));
#1048 = CARTESIAN_POINT('',(2.992910238296,5.424352395785));
#1049 = CARTESIAN_POINT('',(3.014567607929,5.414348178989));
#1050 = CARTESIAN_POINT('',(3.036176527887,5.404310383354));
#1051 = CARTESIAN_POINT('',(3.057742958495,5.394242873965));
#1052 = CARTESIAN_POINT('',(3.099457511116,5.374686720762));
#1053 = CARTESIAN_POINT('',(3.11961128096,5.365200630688));
#1054 = CARTESIAN_POINT('',(3.139733029337,5.355697223968));
#1055 = CARTESIAN_POINT('',(3.159839376377,5.346174033703));
#1056 = CARTESIAN_POINT('',(3.179931232404,5.336636058346));
#1057 = CARTESIAN_POINT('',(3.200004571204,5.327090649673));
#1058 = CARTESIAN_POINT('',(3.220071167826,5.317537656649));
#1059 = CARTESIAN_POINT('',(3.240154831873,5.307971244052));
#1060 = CARTESIAN_POINT('',(3.335505549842,5.262554407127));
#1061 = CARTESIAN_POINT('',(3.410695180804,5.226745212938));
#1062 = CARTESIAN_POINT('',(3.486133445992,5.191113955665));
#1063 = CARTESIAN_POINT('',(3.562121186175,5.155823276315));
#1064 = CARTESIAN_POINT('',(3.638936518491,5.121073663235));
#1065 = CARTESIAN_POINT('',(3.716850475787,5.087112777374));
#1066 = CARTESIAN_POINT('',(3.79614596384,5.054247162945));
#1067 = CARTESIAN_POINT('',(3.877140036447,5.022856343491));
#1068 = CARTESIAN_POINT('',(3.99136053288,4.982366663301));
#1069 = CARTESIAN_POINT('',(4.022801962219,4.971597879199));
#1070 = CARTESIAN_POINT('',(4.054569224951,4.96112307693));
#1071 = CARTESIAN_POINT('',(4.086616163971,4.950989881487));
#1072 = CARTESIAN_POINT('',(4.119125070225,4.941175933389));
#1073 = CARTESIAN_POINT('',(4.15192692561,4.931777420919));
#1074 = CARTESIAN_POINT('',(4.18512536808,4.922797843839));
#1075 = CARTESIAN_POINT('',(4.218681397997,4.914291285058));
#1076 = CARTESIAN_POINT('',(4.282270066168,4.899301077241));
#1077 = CARTESIAN_POINT('',(4.312218320957,4.892697745186));
#1078 = CARTESIAN_POINT('',(4.342435145702,4.886514203065));
#1079 = CARTESIAN_POINT('',(4.372912948198,4.880778789413));
#1080 = CARTESIAN_POINT('',(4.403641132304,4.875519724702));
#1081 = CARTESIAN_POINT('',(4.434605713196,4.870764346408));
#1082 = CARTESIAN_POINT('',(4.465788932614,4.866538344074));
#1083 = CARTESIAN_POINT('',(4.497168874112,4.862864994389));
#1084 = CARTESIAN_POINT('',(4.556325506983,4.85705137287));
#1085 = CARTESIAN_POINT('',(4.584063403049,4.854776879215));
#1086 = CARTESIAN_POINT('',(4.611910811676,4.852954050726));
#1087 = CARTESIAN_POINT('',(4.639850742609,4.85159443223));
#1088 = CARTESIAN_POINT('',(4.667862120941,4.850706996265));
#1089 = CARTESIAN_POINT('',(4.695915843306,4.850297404496));
#1090 = CARTESIAN_POINT('',(4.723975939482,4.850367512193));
#1091 = CARTESIAN_POINT('',(4.752005839408,4.850915115785));
#1092 = CARTESIAN_POINT('',(4.828934081498,4.853716891972));
#1093 = CARTESIAN_POINT('',(4.877716888893,4.856943960979));
#1094 = CARTESIAN_POINT('',(4.92618497172,4.861581612662));
#1095 = CARTESIAN_POINT('',(4.974225013577,4.867567849265));
#1096 = CARTESIAN_POINT('',(5.021750303088,4.874819303072));
#1097 = CARTESIAN_POINT('',(5.06869745925,4.883238326331));
#1098 = CARTESIAN_POINT('',(5.115023156773,4.8927200812));
#1099 = CARTESIAN_POINT('',(5.160700851429,4.903159629682));
#1100 = CARTESIAN_POINT('',(5.239479995861,4.922933568962));
#1101 = CARTESIAN_POINT('',(5.272871067056,4.9318913359));
#1102 = CARTESIAN_POINT('',(5.305889779605,4.941291887864));
#1103 = CARTESIAN_POINT('',(5.338504975356,4.951084362976));
#1104 = CARTESIAN_POINT('',(5.370784485564,4.961248653305));
#1105 = CARTESIAN_POINT('',(5.402754660183,4.971756530217));
#1106 = CARTESIAN_POINT('',(5.434390371503,4.982567540612));
#1107 = CARTESIAN_POINT('',(5.465742355423,4.993663830653));
#1108 = CARTESIAN_POINT('',(5.543425126296,5.02204771745));
#1109 = CARTESIAN_POINT('',(5.589411212844,5.039657909017));
#1110 = CARTESIAN_POINT('',(5.634830717063,5.057764097066));
#1111 = CARTESIAN_POINT('',(5.679738894531,5.076292087919));
#1112 = CARTESIAN_POINT('',(5.724190274398,5.095176716829));
#1113 = CARTESIAN_POINT('',(5.768239671025,5.114361207593));
#1114 = CARTESIAN_POINT('',(5.81194319563,5.133796532155));
#1115 = CARTESIAN_POINT('',(5.85535926793,5.153440770216));
#1116 = CARTESIAN_POINT('',(5.920144807708,5.18316731815));
#1117 = CARTESIAN_POINT('',(5.941685168594,5.193120282388));
#1118 = CARTESIAN_POINT('',(5.963166679857,5.203107429946));
#1119 = CARTESIAN_POINT('',(5.984630438348,5.213140193122));
#1120 = CARTESIAN_POINT('',(6.006049847514,5.223198724772));
#1121 = CARTESIAN_POINT('',(6.027421152836,5.233273970495));
#1122 = CARTESIAN_POINT('',(6.048777645987,5.243374292819));
#1123 = CARTESIAN_POINT('',(6.070108560165,5.253487748885));
#1124 = CARTESIAN_POINT('',(6.112740217882,5.273737027652));
#1125 = CARTESIAN_POINT('',(6.134040392587,5.283872583575));
#1126 = CARTESIAN_POINT('',(6.155330788131,5.294014972403));
#1127 = CARTESIAN_POINT('',(6.176617169619,5.304160182694));
#1128 = CARTESIAN_POINT('',(6.197905210958,5.314304258438));
#1129 = CARTESIAN_POINT('',(6.219200513535,5.324443282292));
#1130 = CARTESIAN_POINT('',(6.240508624905,5.334573358822));
#1131 = CARTESIAN_POINT('',(6.261835057473,5.344690597733));
#1132 = CARTESIAN_POINT('',(6.283185307179,5.354791097117));
#1133 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1134 = ORIENTED_EDGE('',*,*,#1135,.F.);
#1135 = EDGE_CURVE('',#370,#860,#1136,.T.);
#1136 = SEAM_CURVE('',#1137,(#1141,#1148),.PCURVE_S1.);
#1137 = LINE('',#1138,#1139);
#1138 = CARTESIAN_POINT('',(73.592494,105.15648,17.720607));
#1139 = VECTOR('',#1140,1.);
#1140 = DIRECTION('',(0.,0.,1.));
#1141 = PCURVE('',#386,#1142);
#1142 = DEFINITIONAL_REPRESENTATION('',(#1143),#1147);
#1143 = LINE('',#1144,#1145);
#1144 = CARTESIAN_POINT('',(6.28318530718,-0.));
#1145 = VECTOR('',#1146,1.);
#1146 = DIRECTION('',(0.,1.));
#1147 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1148 = PCURVE('',#386,#1149);
#1149 = DEFINITIONAL_REPRESENTATION('',(#1150),#1154);
#1150 = LINE('',#1151,#1152);
#1151 = CARTESIAN_POINT('',(0.,-0.));
#1152 = VECTOR('',#1153,1.);
#1153 = DIRECTION('',(0.,1.));
#1154 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1155 = ORIENTED_EDGE('',*,*,#369,.T.);
#1156 = ORIENTED_EDGE('',*,*,#1135,.T.);
#1157 = ADVANCED_FACE('',(#1158),#464,.T.);
#1158 = FACE_BOUND('',#1159,.T.);
#1159 = EDGE_LOOP('',(#1160,#1199,#1228,#1256,#1278,#1279,#1303,#1343,
    #1372,#1448,#1471,#1497));
#1160 = ORIENTED_EDGE('',*,*,#1161,.T.);
#1161 = EDGE_CURVE('',#1162,#1164,#1166,.T.);
#1162 = VERTEX_POINT('',#1163);
#1163 = CARTESIAN_POINT('',(75.293967335918,109.853261,17.302256265533)
  );
#1164 = VERTEX_POINT('',#1165);
#1165 = CARTESIAN_POINT('',(76.778961021497,109.22128204048,19.973447));
#1166 = SURFACE_CURVE('',#1167,(#1172,#1183),.PCURVE_S1.);
#1167 = HYPERBOLA('',#1168,1.732505350018,0.887908482964);
#1168 = AXIS2_PLACEMENT_3D('',#1169,#1170,#1171);
#1169 = CARTESIAN_POINT('',(73.317494,110.8266515,13.188018527919));
#1170 = DIRECTION('',(0.,0.973135119951,0.230234746115));
#1171 = DIRECTION('',(0.,-0.230234746115,0.973135119951));
#1172 = PCURVE('',#464,#1173);
#1173 = DEFINITIONAL_REPRESENTATION('',(#1174),#1182);
#1174 = B_SPLINE_CURVE_WITH_KNOTS('',6,(#1175,#1176,#1177,#1178,#1179,
    #1180,#1181),.UNSPECIFIED.,.F.,.F.,(7,7),(1.540361937069,
    2.069784241205),.PIECEWISE_BEZIER_KNOTS.);
#1175 = CARTESIAN_POINT('',(6.28318530718,2.94703826553));
#1176 = CARTESIAN_POINT('',(6.243545801605,3.278185756398));
#1177 = CARTESIAN_POINT('',(6.208507663436,3.647772358258));
#1178 = CARTESIAN_POINT('',(6.17740264452,4.060438430888));
#1179 = CARTESIAN_POINT('',(6.149672975281,4.521724319092));
#1180 = CARTESIAN_POINT('',(6.124864671618,5.038278793706));
#1181 = CARTESIAN_POINT('',(6.102598852716,5.618228999997));
#1182 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1183 = PCURVE('',#1184,#1189);
#1184 = CONICAL_SURFACE('',#1185,0.275,0.523598775598);
#1185 = AXIS2_PLACEMENT_3D('',#1186,#1187,#1188);
#1186 = CARTESIAN_POINT('',(73.317494,111.800042,12.973447));
#1187 = DIRECTION('',(0.,0.,1.));
#1188 = DIRECTION('',(1.,0.,0.));
#1189 = DEFINITIONAL_REPRESENTATION('',(#1190),#1198);
#1190 = B_SPLINE_CURVE_WITH_KNOTS('',6,(#1191,#1192,#1193,#1194,#1195,
    #1196,#1197),.UNSPECIFIED.,.F.,.F.,(7,7),(1.540361937069,
    2.069784241205),.PIECEWISE_BEZIER_KNOTS.);
#1191 = CARTESIAN_POINT('',(5.505355291671,4.328809265529));
#1192 = CARTESIAN_POINT('',(5.533595980494,4.659956756398));
#1193 = CARTESIAN_POINT('',(5.559501196213,5.029543358259));
#1194 = CARTESIAN_POINT('',(5.583214970207,5.442209430886));
#1195 = CARTESIAN_POINT('',(5.604907139339,5.903495319093));
#1196 = CARTESIAN_POINT('',(5.624747357115,6.420049793706));
#1197 = CARTESIAN_POINT('',(5.642898011383,6.999999999997));
#1198 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1199 = ORIENTED_EDGE('',*,*,#1200,.T.);
#1200 = EDGE_CURVE('',#1164,#1201,#1203,.T.);
#1201 = VERTEX_POINT('',#1202);
#1202 = CARTESIAN_POINT('',(76.836180025519,109.853261,19.973447));
#1203 = SURFACE_CURVE('',#1204,(#1209,#1216),.PCURVE_S1.);
#1204 = CIRCLE('',#1205,3.518686025519);
#1205 = AXIS2_PLACEMENT_3D('',#1206,#1207,#1208);
#1206 = CARTESIAN_POINT('',(73.317494,109.853261,19.973447));
#1207 = DIRECTION('',(0.,0.,1.));
#1208 = DIRECTION('',(1.,0.,-0.));
#1209 = PCURVE('',#464,#1210);
#1210 = DEFINITIONAL_REPRESENTATION('',(#1211),#1215);
#1211 = LINE('',#1212,#1213);
#1212 = CARTESIAN_POINT('',(0.,5.618229));
#1213 = VECTOR('',#1214,1.);
#1214 = DIRECTION('',(1.,0.));
#1215 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1216 = PCURVE('',#1217,#1222);
#1217 = PLANE('',#1218);
#1218 = AXIS2_PLACEMENT_3D('',#1219,#1220,#1221);
#1219 = CARTESIAN_POINT('',(73.317494,111.800042,19.973447));
#1220 = DIRECTION('',(0.,0.,1.));
#1221 = DIRECTION('',(1.,0.,0.));
#1222 = DEFINITIONAL_REPRESENTATION('',(#1223),#1227);
#1223 = CIRCLE('',#1224,3.518686025519);
#1224 = AXIS2_PLACEMENT_2D('',#1225,#1226);
#1225 = CARTESIAN_POINT('',(0.,-1.946781));
#1226 = DIRECTION('',(1.,0.));
#1227 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1228 = ORIENTED_EDGE('',*,*,#1229,.T.);
#1229 = EDGE_CURVE('',#1201,#1230,#1232,.T.);
#1230 = VERTEX_POINT('',#1231);
#1231 = CARTESIAN_POINT('',(77.633945884327,109.853261,21.355218));
#1232 = SURFACE_CURVE('',#1233,(#1237,#1244),.PCURVE_S1.);
#1233 = LINE('',#1234,#1235);
#1234 = CARTESIAN_POINT('',(73.592494,109.853261,14.355218));
#1235 = VECTOR('',#1236,1.);
#1236 = DIRECTION('',(0.5,-1.224646799147E-16,0.866025403784));
#1237 = PCURVE('',#464,#1238);
#1238 = DEFINITIONAL_REPRESENTATION('',(#1239),#1243);
#1239 = LINE('',#1240,#1241);
#1240 = CARTESIAN_POINT('',(6.28318530718,-0.));
#1241 = VECTOR('',#1242,1.);
#1242 = DIRECTION('',(0.,1.));
#1243 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1244 = PCURVE('',#1245,#1250);
#1245 = CONICAL_SURFACE('',#1246,0.275,0.523598775598);
#1246 = AXIS2_PLACEMENT_3D('',#1247,#1248,#1249);
#1247 = CARTESIAN_POINT('',(73.317494,109.853261,14.355218));
#1248 = DIRECTION('',(0.,0.,1.));
#1249 = DIRECTION('',(1.,0.,0.));
#1250 = DEFINITIONAL_REPRESENTATION('',(#1251),#1255);
#1251 = LINE('',#1252,#1253);
#1252 = CARTESIAN_POINT('',(0.,-0.));
#1253 = VECTOR('',#1254,1.);
#1254 = DIRECTION('',(0.,1.));
#1255 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1256 = ORIENTED_EDGE('',*,*,#1257,.F.);
#1257 = EDGE_CURVE('',#426,#1230,#1258,.T.);
#1258 = SURFACE_CURVE('',#1259,(#1264,#1271),.PCURVE_S1.);
#1259 = CIRCLE('',#1260,4.316451884327);
#1260 = AXIS2_PLACEMENT_3D('',#1261,#1262,#1263);
#1261 = CARTESIAN_POINT('',(73.317494,109.853261,21.355218));
#1262 = DIRECTION('',(0.,0.,1.));
#1263 = DIRECTION('',(1.,0.,0.));
#1264 = PCURVE('',#464,#1265);
#1265 = DEFINITIONAL_REPRESENTATION('',(#1266),#1270);
#1266 = LINE('',#1267,#1268);
#1267 = CARTESIAN_POINT('',(0.,7.));
#1268 = VECTOR('',#1269,1.);
#1269 = DIRECTION('',(1.,0.));
#1270 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1271 = PCURVE('',#500,#1272);
#1272 = DEFINITIONAL_REPRESENTATION('',(#1273),#1277);
#1273 = CIRCLE('',#1274,4.316451884327);
#1274 = AXIS2_PLACEMENT_2D('',#1275,#1276);
#1275 = CARTESIAN_POINT('',(0.,0.));
#1276 = DIRECTION('',(1.,0.));
#1277 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1278 = ORIENTED_EDGE('',*,*,#425,.T.);
#1279 = ORIENTED_EDGE('',*,*,#1280,.F.);
#1280 = EDGE_CURVE('',#1281,#428,#1283,.T.);
#1281 = VERTEX_POINT('',#1282);
#1282 = CARTESIAN_POINT('',(70.266293526111,112.906445,21.355218));
#1283 = SURFACE_CURVE('',#1284,(#1289,#1296),.PCURVE_S1.);
#1284 = CIRCLE('',#1285,4.316451884327);
#1285 = AXIS2_PLACEMENT_3D('',#1286,#1287,#1288);
#1286 = CARTESIAN_POINT('',(73.317494,109.853261,21.355218));
#1287 = DIRECTION('',(0.,0.,1.));
#1288 = DIRECTION('',(1.,0.,0.));
#1289 = PCURVE('',#464,#1290);
#1290 = DEFINITIONAL_REPRESENTATION('',(#1291),#1295);
#1291 = LINE('',#1292,#1293);
#1292 = CARTESIAN_POINT('',(0.,7.));
#1293 = VECTOR('',#1294,1.);
#1294 = DIRECTION('',(1.,0.));
#1295 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1296 = PCURVE('',#500,#1297);
#1297 = DEFINITIONAL_REPRESENTATION('',(#1298),#1302);
#1298 = CIRCLE('',#1299,4.316451884327);
#1299 = AXIS2_PLACEMENT_2D('',#1300,#1301);
#1300 = CARTESIAN_POINT('',(0.,0.));
#1301 = DIRECTION('',(1.,0.));
#1302 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1303 = ORIENTED_EDGE('',*,*,#1304,.F.);
#1304 = EDGE_CURVE('',#1305,#1281,#1307,.T.);
#1305 = VERTEX_POINT('',#1306);
#1306 = CARTESIAN_POINT('',(71.568431734577,112.906445,19.973447));
#1307 = SURFACE_CURVE('',#1308,(#1313,#1331),.PCURVE_S1.);
#1308 = HYPERBOLA('',#1309,5.288269812856,3.053184);
#1309 = AXIS2_PLACEMENT_3D('',#1310,#1311,#1312);
#1310 = CARTESIAN_POINT('',(73.317494,112.906445,13.878904027919));
#1311 = DIRECTION('',(0.,-1.,0.));
#1312 = DIRECTION('',(0.,0.,1.));
#1313 = PCURVE('',#464,#1314);
#1314 = DEFINITIONAL_REPRESENTATION('',(#1315),#1330);
#1315 = B_SPLINE_CURVE_WITH_KNOTS('',7,(#1316,#1317,#1318,#1319,#1320,
    #1321,#1322,#1323,#1324,#1325,#1326,#1327,#1328,#1329),
  .UNSPECIFIED.,.F.,.F.,(8,6,8),(-0.537331753152,0.171791190755,
    0.880914134662),.UNSPECIFIED.);
#1316 = CARTESIAN_POINT('',(1.057598596934,5.593930923854));
#1317 = CARTESIAN_POINT('',(1.145851884008,5.292018679137));
#1318 = CARTESIAN_POINT('',(1.239226607037,5.062783924112));
#1319 = CARTESIAN_POINT('',(1.33695277501,4.90116615996));
#1320 = CARTESIAN_POINT('',(1.437822122059,4.80393167049));
#1321 = CARTESIAN_POINT('',(1.540088773796,4.769463578094));
#1322 = CARTESIAN_POINT('',(1.641922109829,4.797697054788));
#1323 = CARTESIAN_POINT('',(1.841575308708,4.982667378185));
#1324 = CARTESIAN_POINT('',(1.939394507024,5.139404223408));
#1325 = CARTESIAN_POINT('',(2.033632264825,5.36194309185));
#1326 = CARTESIAN_POINT('',(2.122945503615,5.653449211738));
#1327 = CARTESIAN_POINT('',(2.206571989379,6.018769934232));
#1328 = CARTESIAN_POINT('',(2.284214048909,6.46462896889));
#1329 = CARTESIAN_POINT('',(2.355869555541,7.));
#1330 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1331 = PCURVE('',#1332,#1337);
#1332 = PLANE('',#1333);
#1333 = AXIS2_PLACEMENT_3D('',#1334,#1335,#1336);
#1334 = CARTESIAN_POINT('',(51.710833,112.906445,47.5));
#1335 = DIRECTION('',(0.,-1.,0.));
#1336 = DIRECTION('',(1.,0.,0.));
#1337 = DEFINITIONAL_REPRESENTATION('',(#1338),#1342);
#1338 = ( BOUNDED_CURVE() B_SPLINE_CURVE(2,(#1339,#1340,#1341),
.UNSPECIFIED.,.F.,.F.) B_SPLINE_CURVE_WITH_KNOTS((3,3),(-0.537331753152,
0.880914134662),.PIECEWISE_BEZIER_KNOTS.) CURVE() 
GEOMETRIC_REPRESENTATION_ITEM() RATIONAL_B_SPLINE_CURVE((1.,
1.262141851221,1.)) REPRESENTATION_ITEM('') );
#1339 = CARTESIAN_POINT('',(23.3273271,-27.55085107614));
#1340 = CARTESIAN_POINT('',(21.189042462813,-29.36919973444));
#1341 = CARTESIAN_POINT('',(18.555460526111,-26.144782));
#1342 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1343 = ORIENTED_EDGE('',*,*,#1344,.T.);
#1344 = EDGE_CURVE('',#1305,#1345,#1347,.T.);
#1345 = VERTEX_POINT('',#1346);
#1346 = CARTESIAN_POINT('',(69.856026978503,109.22128204048,19.973447));
#1347 = SURFACE_CURVE('',#1348,(#1353,#1360),.PCURVE_S1.);
#1348 = CIRCLE('',#1349,3.518686025519);
#1349 = AXIS2_PLACEMENT_3D('',#1350,#1351,#1352);
#1350 = CARTESIAN_POINT('',(73.317494,109.853261,19.973447));
#1351 = DIRECTION('',(0.,0.,1.));
#1352 = DIRECTION('',(1.,0.,-0.));
#1353 = PCURVE('',#464,#1354);
#1354 = DEFINITIONAL_REPRESENTATION('',(#1355),#1359);
#1355 = LINE('',#1356,#1357);
#1356 = CARTESIAN_POINT('',(0.,5.618229));
#1357 = VECTOR('',#1358,1.);
#1358 = DIRECTION('',(1.,0.));
#1359 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1360 = PCURVE('',#1361,#1366);
#1361 = PLANE('',#1362);
#1362 = AXIS2_PLACEMENT_3D('',#1363,#1364,#1365);
#1363 = CARTESIAN_POINT('',(73.317494,111.800042,19.973447));
#1364 = DIRECTION('',(0.,0.,1.));
#1365 = DIRECTION('',(1.,0.,0.));
#1366 = DEFINITIONAL_REPRESENTATION('',(#1367),#1371);
#1367 = CIRCLE('',#1368,3.518686025519);
#1368 = AXIS2_PLACEMENT_2D('',#1369,#1370);
#1369 = CARTESIAN_POINT('',(0.,-1.946781));
#1370 = DIRECTION('',(1.,0.));
#1371 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1372 = ORIENTED_EDGE('',*,*,#1373,.T.);
#1373 = EDGE_CURVE('',#1345,#1162,#1374,.T.);
#1374 = SURFACE_CURVE('',#1375,(#1380,#1421),.PCURVE_S1.);
#1375 = HYPERBOLA('',#1376,1.732505350018,0.887908482964);
#1376 = AXIS2_PLACEMENT_3D('',#1377,#1378,#1379);
#1377 = CARTESIAN_POINT('',(73.317494,110.8266515,13.188018527919));
#1378 = DIRECTION('',(0.,0.973135119951,0.230234746115));
#1379 = DIRECTION('',(0.,-0.230234746115,0.973135119951));
#1380 = PCURVE('',#464,#1381);
#1381 = DEFINITIONAL_REPRESENTATION('',(#1382),#1420);
#1382 = B_SPLINE_CURVE_WITH_KNOTS('',8,(#1383,#1384,#1385,#1386,#1387,
    #1388,#1389,#1390,#1391,#1392,#1393,#1394,#1395,#1396,#1397,#1398,
    #1399,#1400,#1401,#1402,#1403,#1404,#1405,#1406,#1407,#1408,#1409,
    #1410,#1411,#1412,#1413,#1414,#1415,#1416,#1417,#1418,#1419),
  .UNSPECIFIED.,.F.,.F.,(9,7,7,7,7,9),(-2.069784241205,-1.167247696636,
    -0.49034528821,1.733151810991E-02,0.77884672759,1.540361937069),
  .UNSPECIFIED.);
#1383 = CARTESIAN_POINT('',(3.322179108053,5.618228999997));
#1384 = CARTESIAN_POINT('',(3.293710744853,4.876723233543));
#1385 = CARTESIAN_POINT('',(3.261283437105,4.233917793468));
#1386 = CARTESIAN_POINT('',(3.224230696164,3.67543188441));
#1387 = CARTESIAN_POINT('',(3.181734805744,3.189563082611));
#1388 = CARTESIAN_POINT('',(3.132826274455,2.766706431669));
#1389 = CARTESIAN_POINT('',(3.076306903788,2.398942960677));
#1390 = CARTESIAN_POINT('',(3.010739179933,2.079747801435));
#1391 = CARTESIAN_POINT('',(2.877573836363,1.596783291456));
#1392 = CARTESIAN_POINT('',(2.814582643242,1.414107213443));
#1393 = CARTESIAN_POINT('',(2.745044743618,1.25348177745));
#1394 = CARTESIAN_POINT('',(2.668341755944,1.113020106107));
#1395 = CARTESIAN_POINT('',(2.583878307464,0.991155023536));
#1396 = CARTESIAN_POINT('',(2.4912487739,0.886601125377));
#1397 = CARTESIAN_POINT('',(2.39047065528,0.798327874931));
#1398 = CARTESIAN_POINT('',(2.200996133197,0.67095117694));
#1399 = CARTESIAN_POINT('',(2.115577609193,0.625072356043));
#1400 = CARTESIAN_POINT('',(2.02621804538,0.587569744509));
#1401 = CARTESIAN_POINT('',(1.933384270472,0.558183163677));
#1402 = CARTESIAN_POINT('',(1.83778047137,0.536722998023));
#1403 = CARTESIAN_POINT('',(1.740325831873,0.523067430098));
#1404 = CARTESIAN_POINT('',(1.642065163034,0.517161151767));
#1405 = CARTESIAN_POINT('',(1.396933568349,0.521797151716));
#1406 = CARTESIAN_POINT('',(1.250325368357,0.542040273566));
#1407 = CARTESIAN_POINT('',(1.107615896423,0.579783323063));
#1408 = CARTESIAN_POINT('',(0.972195426936,0.635402243141));
#1409 = CARTESIAN_POINT('',(0.846330313775,0.709611616385));
#1410 = CARTESIAN_POINT('',(0.730714336536,0.803482117132));
#1411 = CARTESIAN_POINT('',(0.625381327908,0.918474298576));
#1412 = CARTESIAN_POINT('',(0.434369737891,1.194513368018));
#1413 = CARTESIAN_POINT('',(0.348692012238,1.355560246778));
#1414 = CARTESIAN_POINT('',(0.272396692541,1.541540205566));
#1415 = CARTESIAN_POINT('',(0.204588234744,1.75480385854));
#1416 = CARTESIAN_POINT('',(0.14429762557,1.998202670835));
#1417 = CARTESIAN_POINT('',(9.062548494135E-02,2.275164700904));
#1418 = CARTESIAN_POINT('',(4.276277107587E-02,2.589799088636));
#1419 = CARTESIAN_POINT('',(4.457803585468E-13,2.947038265529));
#1420 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1421 = PCURVE('',#1184,#1422);
#1422 = DEFINITIONAL_REPRESENTATION('',(#1423),#1447);
#1423 = B_SPLINE_CURVE_WITH_KNOTS('',8,(#1424,#1425,#1426,#1427,#1428,
    #1429,#1430,#1431,#1432,#1433,#1434,#1435,#1436,#1437,#1438,#1439,
    #1440,#1441,#1442,#1443,#1444,#1445,#1446),.UNSPECIFIED.,.F.,.F.,(9,
    7,7,9),(-2.069784241205,-1.167247696636,0.186557120217,
    1.540361937069),.UNSPECIFIED.);
#1424 = CARTESIAN_POINT('',(3.781879949386,6.999999999997));
#1425 = CARTESIAN_POINT('',(3.805086794834,6.258494233543));
#1426 = CARTESIAN_POINT('',(3.83092407558,5.615688793469));
#1427 = CARTESIAN_POINT('',(3.859679302972,5.057202884411));
#1428 = CARTESIAN_POINT('',(3.891652602401,4.571334082612));
#1429 = CARTESIAN_POINT('',(3.927146792245,4.148477431669));
#1430 = CARTESIAN_POINT('',(3.966439885959,3.780713960676));
#1431 = CARTESIAN_POINT('',(4.009739740973,3.461518801435));
#1432 = CARTESIAN_POINT('',(4.128191833626,2.771569501464));
#1433 = CARTESIAN_POINT('',(4.208444491273,2.454834765071));
#1434 = CARTESIAN_POINT('',(4.298139322433,2.217270301514));
#1435 = CARTESIAN_POINT('',(4.396820231705,2.046751076455));
#1436 = CARTESIAN_POINT('',(4.503085510562,1.935439724341));
#1437 = CARTESIAN_POINT('',(4.613542621659,1.879028005523));
#1438 = CARTESIAN_POINT('',(4.724448115453,1.876421753188));
#1439 = CARTESIAN_POINT('',(4.940759968734,1.983492756692));
#1440 = CARTESIAN_POINT('',(5.046164622046,2.093169259031));
#1441 = CARTESIAN_POINT('',(5.145627679223,2.261327908307));
#1442 = CARTESIAN_POINT('',(5.236433058675,2.493720396366));
#1443 = CARTESIAN_POINT('',(5.317463494602,2.799779349861));
#1444 = CARTESIAN_POINT('',(5.388851902572,3.193276934142));
#1445 = CARTESIAN_POINT('',(5.451193830781,3.693717395498));
#1446 = CARTESIAN_POINT('',(5.505355291671,4.328809265529));
#1447 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1448 = ORIENTED_EDGE('',*,*,#1449,.F.);
#1449 = EDGE_CURVE('',#1450,#1162,#1452,.T.);
#1450 = VERTEX_POINT('',#1451);
#1451 = CARTESIAN_POINT('',(73.592494,109.853261,14.355218));
#1452 = SEAM_CURVE('',#1453,(#1457,#1464),.PCURVE_S1.);
#1453 = LINE('',#1454,#1455);
#1454 = CARTESIAN_POINT('',(73.592494,109.853261,14.355218));
#1455 = VECTOR('',#1456,1.);
#1456 = DIRECTION('',(0.5,-1.224646799147E-16,0.866025403784));
#1457 = PCURVE('',#464,#1458);
#1458 = DEFINITIONAL_REPRESENTATION('',(#1459),#1463);
#1459 = LINE('',#1460,#1461);
#1460 = CARTESIAN_POINT('',(0.,-0.));
#1461 = VECTOR('',#1462,1.);
#1462 = DIRECTION('',(0.,1.));
#1463 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1464 = PCURVE('',#464,#1465);
#1465 = DEFINITIONAL_REPRESENTATION('',(#1466),#1470);
#1466 = LINE('',#1467,#1468);
#1467 = CARTESIAN_POINT('',(6.28318530718,-0.));
#1468 = VECTOR('',#1469,1.);
#1469 = DIRECTION('',(0.,1.));
#1470 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1471 = ORIENTED_EDGE('',*,*,#1472,.T.);
#1472 = EDGE_CURVE('',#1450,#1450,#1473,.T.);
#1473 = SURFACE_CURVE('',#1474,(#1479,#1486),.PCURVE_S1.);
#1474 = CIRCLE('',#1475,0.275);
#1475 = AXIS2_PLACEMENT_3D('',#1476,#1477,#1478);
#1476 = CARTESIAN_POINT('',(73.317494,109.853261,14.355218));
#1477 = DIRECTION('',(0.,0.,1.));
#1478 = DIRECTION('',(1.,0.,0.));
#1479 = PCURVE('',#464,#1480);
#1480 = DEFINITIONAL_REPRESENTATION('',(#1481),#1485);
#1481 = LINE('',#1482,#1483);
#1482 = CARTESIAN_POINT('',(0.,0.));
#1483 = VECTOR('',#1484,1.);
#1484 = DIRECTION('',(1.,0.));
#1485 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1486 = PCURVE('',#1487,#1492);
#1487 = CYLINDRICAL_SURFACE('',#1488,0.275);
#1488 = AXIS2_PLACEMENT_3D('',#1489,#1490,#1491);
#1489 = CARTESIAN_POINT('',(73.317494,109.853261,14.355218));
#1490 = DIRECTION('',(0.,0.,1.));
#1491 = DIRECTION('',(1.,0.,0.));
#1492 = DEFINITIONAL_REPRESENTATION('',(#1493),#1496);
#1493 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#1494,#1495),.UNSPECIFIED.,.F.,
  .F.,(2,2),(0.,6.28318530718),.PIECEWISE_BEZIER_KNOTS.);
#1494 = CARTESIAN_POINT('',(0.,0.));
#1495 = CARTESIAN_POINT('',(6.28318530718,0.));
#1496 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1497 = ORIENTED_EDGE('',*,*,#1449,.T.);
#1498 = ADVANCED_FACE('',(#1499,#1599,#1625),#500,.T.);
#1499 = FACE_BOUND('',#1500,.T.);
#1500 = EDGE_LOOP('',(#1501,#1522,#1523,#1524,#1525,#1549,#1575));
#1501 = ORIENTED_EDGE('',*,*,#1502,.F.);
#1502 = EDGE_CURVE('',#1281,#1503,#1505,.T.);
#1503 = VERTEX_POINT('',#1504);
#1504 = CARTESIAN_POINT('',(72.917494,112.906445,21.355218));
#1505 = SURFACE_CURVE('',#1506,(#1510,#1516),.PCURVE_S1.);
#1506 = LINE('',#1507,#1508);
#1507 = CARTESIAN_POINT('',(62.5141635,112.906445,21.355218));
#1508 = VECTOR('',#1509,1.);
#1509 = DIRECTION('',(1.,0.,-0.));
#1510 = PCURVE('',#500,#1511);
#1511 = DEFINITIONAL_REPRESENTATION('',(#1512),#1515);
#1512 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#1513,#1514),.UNSPECIFIED.,.F.,
  .F.,(2,2),(6.486878615673,10.4033305),.PIECEWISE_BEZIER_KNOTS.);
#1513 = CARTESIAN_POINT('',(-4.316451884327,3.053184));
#1514 = CARTESIAN_POINT('',(-0.4,3.053184));
#1515 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1516 = PCURVE('',#1332,#1517);
#1517 = DEFINITIONAL_REPRESENTATION('',(#1518),#1521);
#1518 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#1519,#1520),.UNSPECIFIED.,.F.,
  .F.,(2,2),(6.486878615673,10.4033305),.PIECEWISE_BEZIER_KNOTS.);
#1519 = CARTESIAN_POINT('',(17.290209115673,-26.144782));
#1520 = CARTESIAN_POINT('',(21.206661,-26.144782));
#1521 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1522 = ORIENTED_EDGE('',*,*,#1280,.T.);
#1523 = ORIENTED_EDGE('',*,*,#485,.F.);
#1524 = ORIENTED_EDGE('',*,*,#1257,.T.);
#1525 = ORIENTED_EDGE('',*,*,#1526,.T.);
#1526 = EDGE_CURVE('',#1230,#1527,#1529,.T.);
#1527 = VERTEX_POINT('',#1528);
#1528 = CARTESIAN_POINT('',(76.869039214094,112.30648,21.355218));
#1529 = SURFACE_CURVE('',#1530,(#1535,#1542),.PCURVE_S1.);
#1530 = CIRCLE('',#1531,4.316451884327);
#1531 = AXIS2_PLACEMENT_3D('',#1532,#1533,#1534);
#1532 = CARTESIAN_POINT('',(73.317494,109.853261,21.355218));
#1533 = DIRECTION('',(0.,0.,1.));
#1534 = DIRECTION('',(1.,0.,0.));
#1535 = PCURVE('',#500,#1536);
#1536 = DEFINITIONAL_REPRESENTATION('',(#1537),#1541);
#1537 = CIRCLE('',#1538,4.316451884327);
#1538 = AXIS2_PLACEMENT_2D('',#1539,#1540);
#1539 = CARTESIAN_POINT('',(0.,0.));
#1540 = DIRECTION('',(1.,0.));
#1541 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1542 = PCURVE('',#1245,#1543);
#1543 = DEFINITIONAL_REPRESENTATION('',(#1544),#1548);
#1544 = LINE('',#1545,#1546);
#1545 = CARTESIAN_POINT('',(0.,7.));
#1546 = VECTOR('',#1547,1.);
#1547 = DIRECTION('',(1.,0.));
#1548 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1549 = ORIENTED_EDGE('',*,*,#1550,.F.);
#1550 = EDGE_CURVE('',#1551,#1527,#1553,.T.);
#1551 = VERTEX_POINT('',#1552);
#1552 = CARTESIAN_POINT('',(72.917494,112.30648,21.355218));
#1553 = SURFACE_CURVE('',#1554,(#1558,#1564),.PCURVE_S1.);
#1554 = LINE('',#1555,#1556);
#1555 = CARTESIAN_POINT('',(73.117494,112.30648,21.355218));
#1556 = VECTOR('',#1557,1.);
#1557 = DIRECTION('',(1.,0.,-0.));
#1558 = PCURVE('',#500,#1559);
#1559 = DEFINITIONAL_REPRESENTATION('',(#1560),#1563);
#1560 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#1561,#1562),.UNSPECIFIED.,.F.,
  .F.,(2,2),(-0.2,4.516451884327),.PIECEWISE_BEZIER_KNOTS.);
#1561 = CARTESIAN_POINT('',(-0.4,2.453219));
#1562 = CARTESIAN_POINT('',(4.316451884327,2.453219));
#1563 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1564 = PCURVE('',#1565,#1570);
#1565 = PLANE('',#1566);
#1566 = AXIS2_PLACEMENT_3D('',#1567,#1568,#1569);
#1567 = CARTESIAN_POINT('',(72.917494,112.30648,47.5));
#1568 = DIRECTION('',(0.,-1.,0.));
#1569 = DIRECTION('',(1.,0.,0.));
#1570 = DEFINITIONAL_REPRESENTATION('',(#1571),#1574);
#1571 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#1572,#1573),.UNSPECIFIED.,.F.,
  .F.,(2,2),(-0.2,4.516451884327),.PIECEWISE_BEZIER_KNOTS.);
#1572 = CARTESIAN_POINT('',(0.,-26.144782));
#1573 = CARTESIAN_POINT('',(4.716451884327,-26.144782));
#1574 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1575 = ORIENTED_EDGE('',*,*,#1576,.F.);
#1576 = EDGE_CURVE('',#1503,#1551,#1577,.T.);
#1577 = SURFACE_CURVE('',#1578,(#1582,#1588),.PCURVE_S1.);
#1578 = LINE('',#1579,#1580);
#1579 = CARTESIAN_POINT('',(72.917494,111.379853,21.355218));
#1580 = VECTOR('',#1581,1.);
#1581 = DIRECTION('',(0.,-1.,0.));
#1582 = PCURVE('',#500,#1583);
#1583 = DEFINITIONAL_REPRESENTATION('',(#1584),#1587);
#1584 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#1585,#1586),.UNSPECIFIED.,.F.,
  .F.,(2,2),(-1.526592,-0.926627),.PIECEWISE_BEZIER_KNOTS.);
#1585 = CARTESIAN_POINT('',(-0.4,3.053184));
#1586 = CARTESIAN_POINT('',(-0.4,2.453219));
#1587 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1588 = PCURVE('',#1589,#1594);
#1589 = PLANE('',#1590);
#1590 = AXIS2_PLACEMENT_3D('',#1591,#1592,#1593);
#1591 = CARTESIAN_POINT('',(72.917494,112.906445,47.5));
#1592 = DIRECTION('',(-1.,-0.,-0.));
#1593 = DIRECTION('',(0.,-1.,0.));
#1594 = DEFINITIONAL_REPRESENTATION('',(#1595),#1598);
#1595 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#1596,#1597),.UNSPECIFIED.,.F.,
  .F.,(2,2),(-1.526592,-0.926627),.PIECEWISE_BEZIER_KNOTS.);
#1596 = CARTESIAN_POINT('',(-1.42108547152E-14,-26.144782));
#1597 = CARTESIAN_POINT('',(0.599965,-26.144782));
#1598 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1599 = FACE_BOUND('',#1600,.T.);
#1600 = EDGE_LOOP('',(#1601));
#1601 = ORIENTED_EDGE('',*,*,#1602,.F.);
#1602 = EDGE_CURVE('',#1603,#1603,#1605,.T.);
#1603 = VERTEX_POINT('',#1604);
#1604 = CARTESIAN_POINT('',(73.592494,109.853261,21.355218));
#1605 = SURFACE_CURVE('',#1606,(#1611,#1618),.PCURVE_S1.);
#1606 = CIRCLE('',#1607,0.275);
#1607 = AXIS2_PLACEMENT_3D('',#1608,#1609,#1610);
#1608 = CARTESIAN_POINT('',(73.317494,109.853261,21.355218));
#1609 = DIRECTION('',(0.,0.,1.));
#1610 = DIRECTION('',(1.,0.,0.));
#1611 = PCURVE('',#500,#1612);
#1612 = DEFINITIONAL_REPRESENTATION('',(#1613),#1617);
#1613 = CIRCLE('',#1614,0.275);
#1614 = AXIS2_PLACEMENT_2D('',#1615,#1616);
#1615 = CARTESIAN_POINT('',(0.,0.));
#1616 = DIRECTION('',(1.,0.));
#1617 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1618 = PCURVE('',#1487,#1619);
#1619 = DEFINITIONAL_REPRESENTATION('',(#1620),#1624);
#1620 = LINE('',#1621,#1622);
#1621 = CARTESIAN_POINT('',(0.,7.));
#1622 = VECTOR('',#1623,1.);
#1623 = DIRECTION('',(1.,0.));
#1624 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1625 = FACE_BOUND('',#1626,.T.);
#1626 = EDGE_LOOP('',(#1627));
#1627 = ORIENTED_EDGE('',*,*,#1628,.F.);
#1628 = EDGE_CURVE('',#1629,#1629,#1631,.T.);
#1629 = VERTEX_POINT('',#1630);
#1630 = CARTESIAN_POINT('',(73.592494,111.800042,21.355218));
#1631 = SURFACE_CURVE('',#1632,(#1637,#1644),.PCURVE_S1.);
#1632 = CIRCLE('',#1633,0.275);
#1633 = AXIS2_PLACEMENT_3D('',#1634,#1635,#1636);
#1634 = CARTESIAN_POINT('',(73.317494,111.800042,21.355218));
#1635 = DIRECTION('',(0.,0.,1.));
#1636 = DIRECTION('',(1.,0.,-0.));
#1637 = PCURVE('',#500,#1638);
#1638 = DEFINITIONAL_REPRESENTATION('',(#1639),#1643);
#1639 = CIRCLE('',#1640,0.275);
#1640 = AXIS2_PLACEMENT_2D('',#1641,#1642);
#1641 = CARTESIAN_POINT('',(0.,1.946781));
#1642 = DIRECTION('',(1.,0.));
#1643 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1644 = PCURVE('',#1645,#1650);
#1645 = CYLINDRICAL_SURFACE('',#1646,0.275);
#1646 = AXIS2_PLACEMENT_3D('',#1647,#1648,#1649);
#1647 = CARTESIAN_POINT('',(73.317494,111.800042,12.973447));
#1648 = DIRECTION('',(0.,0.,1.));
#1649 = DIRECTION('',(1.,0.,0.));
#1650 = DEFINITIONAL_REPRESENTATION('',(#1651),#1655);
#1651 = LINE('',#1652,#1653);
#1652 = CARTESIAN_POINT('',(0.,8.381771));
#1653 = VECTOR('',#1654,1.);
#1654 = DIRECTION('',(1.,0.));
#1655 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1656 = ADVANCED_FACE('',(#1657),#649,.F.);
#1657 = FACE_BOUND('',#1658,.F.);
#1658 = EDGE_LOOP('',(#1659,#1660,#1681,#1682));
#1659 = ORIENTED_EDGE('',*,*,#515,.T.);
#1660 = ORIENTED_EDGE('',*,*,#1661,.F.);
#1661 = EDGE_CURVE('',#802,#516,#1662,.T.);
#1662 = SEAM_CURVE('',#1663,(#1667,#1674),.PCURVE_S1.);
#1663 = LINE('',#1664,#1665);
#1664 = CARTESIAN_POINT('',(73.592494,102.80648,26.453618));
#1665 = VECTOR('',#1666,1.);
#1666 = DIRECTION('',(0.,0.,-1.));
#1667 = PCURVE('',#649,#1668);
#1668 = DEFINITIONAL_REPRESENTATION('',(#1669),#1673);
#1669 = LINE('',#1670,#1671);
#1670 = CARTESIAN_POINT('',(6.28318530718,7.));
#1671 = VECTOR('',#1672,1.);
#1672 = DIRECTION('',(-0.,-1.));
#1673 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1674 = PCURVE('',#649,#1675);
#1675 = DEFINITIONAL_REPRESENTATION('',(#1676),#1680);
#1676 = LINE('',#1677,#1678);
#1677 = CARTESIAN_POINT('',(0.,7.));
#1678 = VECTOR('',#1679,1.);
#1679 = DIRECTION('',(-0.,-1.));
#1680 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1681 = ORIENTED_EDGE('',*,*,#801,.F.);
#1682 = ORIENTED_EDGE('',*,*,#1661,.T.);
#1683 = ADVANCED_FACE('',(#1684),#844,.F.);
#1684 = FACE_BOUND('',#1685,.F.);
#1685 = EDGE_LOOP('',(#1686,#1687,#1708,#1709));
#1686 = ORIENTED_EDGE('',*,*,#827,.F.);
#1687 = ORIENTED_EDGE('',*,*,#1688,.F.);
#1688 = EDGE_CURVE('',#860,#828,#1689,.T.);
#1689 = SEAM_CURVE('',#1690,(#1694,#1701),.PCURVE_S1.);
#1690 = LINE('',#1691,#1692);
#1691 = CARTESIAN_POINT('',(73.592494,105.15648,17.720607));
#1692 = VECTOR('',#1693,1.);
#1693 = DIRECTION('',(0.,0.,1.));
#1694 = PCURVE('',#844,#1695);
#1695 = DEFINITIONAL_REPRESENTATION('',(#1696),#1700);
#1696 = LINE('',#1697,#1698);
#1697 = CARTESIAN_POINT('',(6.28318530718,-0.));
#1698 = VECTOR('',#1699,1.);
#1699 = DIRECTION('',(0.,1.));
#1700 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1701 = PCURVE('',#844,#1702);
#1702 = DEFINITIONAL_REPRESENTATION('',(#1703),#1707);
#1703 = LINE('',#1704,#1705);
#1704 = CARTESIAN_POINT('',(0.,-0.));
#1705 = VECTOR('',#1706,1.);
#1706 = DIRECTION('',(0.,1.));
#1707 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1708 = ORIENTED_EDGE('',*,*,#859,.T.);
#1709 = ORIENTED_EDGE('',*,*,#1688,.T.);
#1710 = ADVANCED_FACE('',(#1711),#1184,.T.);
#1711 = FACE_BOUND('',#1712,.T.);
#1712 = EDGE_LOOP('',(#1713,#1738,#1764,#1765,#1787,#1788,#1789,#1813,
    #1860,#1888,#1946));
#1713 = ORIENTED_EDGE('',*,*,#1714,.F.);
#1714 = EDGE_CURVE('',#1715,#1717,#1719,.T.);
#1715 = VERTEX_POINT('',#1716);
#1716 = CARTESIAN_POINT('',(73.592494,111.800042,12.973447));
#1717 = VERTEX_POINT('',#1718);
#1718 = CARTESIAN_POINT('',(77.633945884327,111.800042,19.973447));
#1719 = SEAM_CURVE('',#1720,(#1724,#1731),.PCURVE_S1.);
#1720 = LINE('',#1721,#1722);
#1721 = CARTESIAN_POINT('',(73.592494,111.800042,12.973447));
#1722 = VECTOR('',#1723,1.);
#1723 = DIRECTION('',(0.5,-1.224646799147E-16,0.866025403784));
#1724 = PCURVE('',#1184,#1725);
#1725 = DEFINITIONAL_REPRESENTATION('',(#1726),#1730);
#1726 = LINE('',#1727,#1728);
#1727 = CARTESIAN_POINT('',(0.,-0.));
#1728 = VECTOR('',#1729,1.);
#1729 = DIRECTION('',(0.,1.));
#1730 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1731 = PCURVE('',#1184,#1732);
#1732 = DEFINITIONAL_REPRESENTATION('',(#1733),#1737);
#1733 = LINE('',#1734,#1735);
#1734 = CARTESIAN_POINT('',(6.28318530718,-0.));
#1735 = VECTOR('',#1736,1.);
#1736 = DIRECTION('',(0.,1.));
#1737 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1738 = ORIENTED_EDGE('',*,*,#1739,.T.);
#1739 = EDGE_CURVE('',#1715,#1715,#1740,.T.);
#1740 = SURFACE_CURVE('',#1741,(#1746,#1753),.PCURVE_S1.);
#1741 = CIRCLE('',#1742,0.275);
#1742 = AXIS2_PLACEMENT_3D('',#1743,#1744,#1745);
#1743 = CARTESIAN_POINT('',(73.317494,111.800042,12.973447));
#1744 = DIRECTION('',(0.,0.,1.));
#1745 = DIRECTION('',(1.,0.,0.));
#1746 = PCURVE('',#1184,#1747);
#1747 = DEFINITIONAL_REPRESENTATION('',(#1748),#1752);
#1748 = LINE('',#1749,#1750);
#1749 = CARTESIAN_POINT('',(0.,0.));
#1750 = VECTOR('',#1751,1.);
#1751 = DIRECTION('',(1.,0.));
#1752 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1753 = PCURVE('',#1754,#1759);
#1754 = CYLINDRICAL_SURFACE('',#1755,0.275);
#1755 = AXIS2_PLACEMENT_3D('',#1756,#1757,#1758);
#1756 = CARTESIAN_POINT('',(73.317494,111.800042,12.973447));
#1757 = DIRECTION('',(0.,0.,1.));
#1758 = DIRECTION('',(1.,0.,0.));
#1759 = DEFINITIONAL_REPRESENTATION('',(#1760),#1763);
#1760 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#1761,#1762),.UNSPECIFIED.,.F.,
  .F.,(2,2),(0.,6.28318530718),.PIECEWISE_BEZIER_KNOTS.);
#1761 = CARTESIAN_POINT('',(0.,0.));
#1762 = CARTESIAN_POINT('',(6.28318530718,0.));
#1763 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1764 = ORIENTED_EDGE('',*,*,#1714,.T.);
#1765 = ORIENTED_EDGE('',*,*,#1766,.F.);
#1766 = EDGE_CURVE('',#1164,#1717,#1767,.T.);
#1767 = SURFACE_CURVE('',#1768,(#1773,#1780),.PCURVE_S1.);
#1768 = CIRCLE('',#1769,4.316451884327);
#1769 = AXIS2_PLACEMENT_3D('',#1770,#1771,#1772);
#1770 = CARTESIAN_POINT('',(73.317494,111.800042,19.973447));
#1771 = DIRECTION('',(0.,0.,1.));
#1772 = DIRECTION('',(1.,0.,0.));
#1773 = PCURVE('',#1184,#1774);
#1774 = DEFINITIONAL_REPRESENTATION('',(#1775),#1779);
#1775 = LINE('',#1776,#1777);
#1776 = CARTESIAN_POINT('',(0.,7.));
#1777 = VECTOR('',#1778,1.);
#1778 = DIRECTION('',(1.,0.));
#1779 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1780 = PCURVE('',#1217,#1781);
#1781 = DEFINITIONAL_REPRESENTATION('',(#1782),#1786);
#1782 = CIRCLE('',#1783,4.316451884327);
#1783 = AXIS2_PLACEMENT_2D('',#1784,#1785);
#1784 = CARTESIAN_POINT('',(0.,0.));
#1785 = DIRECTION('',(1.,0.));
#1786 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1787 = ORIENTED_EDGE('',*,*,#1161,.F.);
#1788 = ORIENTED_EDGE('',*,*,#1373,.F.);
#1789 = ORIENTED_EDGE('',*,*,#1790,.F.);
#1790 = EDGE_CURVE('',#1791,#1345,#1793,.T.);
#1791 = VERTEX_POINT('',#1792);
#1792 = CARTESIAN_POINT('',(69.145248888396,112.906445,19.973447));
#1793 = SURFACE_CURVE('',#1794,(#1799,#1806),.PCURVE_S1.);
#1794 = CIRCLE('',#1795,4.316451884327);
#1795 = AXIS2_PLACEMENT_3D('',#1796,#1797,#1798);
#1796 = CARTESIAN_POINT('',(73.317494,111.800042,19.973447));
#1797 = DIRECTION('',(0.,0.,1.));
#1798 = DIRECTION('',(1.,0.,0.));
#1799 = PCURVE('',#1184,#1800);
#1800 = DEFINITIONAL_REPRESENTATION('',(#1801),#1805);
#1801 = LINE('',#1802,#1803);
#1802 = CARTESIAN_POINT('',(0.,7.));
#1803 = VECTOR('',#1804,1.);
#1804 = DIRECTION('',(1.,0.));
#1805 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1806 = PCURVE('',#1361,#1807);
#1807 = DEFINITIONAL_REPRESENTATION('',(#1808),#1812);
#1808 = CIRCLE('',#1809,4.316451884327);
#1809 = AXIS2_PLACEMENT_2D('',#1810,#1811);
#1810 = CARTESIAN_POINT('',(0.,0.));
#1811 = DIRECTION('',(1.,0.));
#1812 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1813 = ORIENTED_EDGE('',*,*,#1814,.F.);
#1814 = EDGE_CURVE('',#1815,#1791,#1817,.T.);
#1815 = VERTEX_POINT('',#1816);
#1816 = CARTESIAN_POINT('',(72.917494,112.906445,14.534872656828));
#1817 = SURFACE_CURVE('',#1818,(#1823,#1853),.PCURVE_S1.);
#1818 = HYPERBOLA('',#1819,1.916346209647,1.106403);
#1819 = AXIS2_PLACEMENT_3D('',#1820,#1821,#1822);
#1820 = CARTESIAN_POINT('',(73.317494,112.906445,12.497133027919));
#1821 = DIRECTION('',(0.,-1.,0.));
#1822 = DIRECTION('',(0.,0.,1.));
#1823 = PCURVE('',#1184,#1824);
#1824 = DEFINITIONAL_REPRESENTATION('',(#1825),#1852);
#1825 = B_SPLINE_CURVE_WITH_KNOTS('',7,(#1826,#1827,#1828,#1829,#1830,
    #1831,#1832,#1833,#1834,#1835,#1836,#1837,#1838,#1839,#1840,#1841,
    #1842,#1843,#1844,#1845,#1846,#1847,#1848,#1849,#1850,#1851),
  .UNSPECIFIED.,.F.,.F.,(8,6,6,6,8),(-1.224991942673,-0.409338631873,
    0.202401351227,1.120011325878,2.037621300528),.UNSPECIFIED.);
#1826 = CARTESIAN_POINT('',(0.571444599553,3.066913127371));
#1827 = CARTESIAN_POINT('',(0.634465201421,2.719645105527));
#1828 = CARTESIAN_POINT('',(0.70469137886,2.42850265209));
#1829 = CARTESIAN_POINT('',(0.782706720734,2.18578479428));
#1830 = CARTESIAN_POINT('',(0.868895553691,1.985656741258));
#1831 = CARTESIAN_POINT('',(0.963299700981,1.823726189775));
#1832 = CARTESIAN_POINT('',(1.06503444736,1.696814108686));
#1833 = CARTESIAN_POINT('',(1.252980718257,1.532352872363));
#1834 = CARTESIAN_POINT('',(1.336714923795,1.480394999953));
#1835 = CARTESIAN_POINT('',(1.422931642782,1.44608343047));
#1836 = CARTESIAN_POINT('',(1.510688045566,1.42888553056));
#1837 = CARTESIAN_POINT('',(1.598823576562,1.428588057134));
#1838 = CARTESIAN_POINT('',(1.686198297303,1.445290721536));
#1839 = CARTESIAN_POINT('',(1.900276868226,1.530612012209));
#1840 = CARTESIAN_POINT('',(2.024800870287,1.621012912345));
#1841 = CARTESIAN_POINT('',(2.142089996233,1.752059120635));
#1842 = CARTESIAN_POINT('',(2.249581319315,1.926836749811));
#1843 = CARTESIAN_POINT('',(2.346651163826,2.150188522973));
#1844 = CARTESIAN_POINT('',(2.433510369369,2.429027946152));
#1845 = CARTESIAN_POINT('',(2.588132322191,3.11699033226));
#1846 = CARTESIAN_POINT('',(2.655894978954,3.52611325124));
#1847 = CARTESIAN_POINT('',(2.71476909542,4.01003312001));
#1848 = CARTESIAN_POINT('',(2.765855325427,4.581144134707));
#1849 = CARTESIAN_POINT('',(2.810209018464,5.255270588862));
#1850 = CARTESIAN_POINT('',(2.848776604062,6.052693477327));
#1851 = CARTESIAN_POINT('',(2.882377164892,7.));
#1852 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1853 = PCURVE('',#1332,#1854);
#1854 = DEFINITIONAL_REPRESENTATION('',(#1855),#1859);
#1855 = ( BOUNDED_CURVE() B_SPLINE_CURVE(2,(#1856,#1857,#1858),
.UNSPECIFIED.,.F.,.F.) B_SPLINE_CURVE_WITH_KNOTS((3,3),(-1.224991942673,
2.037621300528),.PIECEWISE_BEZIER_KNOTS.) CURVE() 
GEOMETRIC_REPRESENTATION_ITEM() RATIONAL_B_SPLINE_CURVE((1.,
2.65311082285,1.)) REPRESENTATION_ITEM('') );
#1856 = CARTESIAN_POINT('',(23.3273271,-31.45963987262));
#1857 = CARTESIAN_POINT('',(21.432518363041,-34.2201175966));
#1858 = CARTESIAN_POINT('',(17.434415888396,-27.526553));
#1859 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1860 = ORIENTED_EDGE('',*,*,#1861,.F.);
#1861 = EDGE_CURVE('',#1862,#1815,#1864,.T.);
#1862 = VERTEX_POINT('',#1863);
#1863 = CARTESIAN_POINT('',(72.917494,112.30648,13.614915808038));
#1864 = SURFACE_CURVE('',#1865,(#1870,#1881),.PCURVE_S1.);
#1865 = HYPERBOLA('',#1866,0.692820323028,0.4);
#1866 = AXIS2_PLACEMENT_3D('',#1867,#1868,#1869);
#1867 = CARTESIAN_POINT('',(72.917494,111.800042,12.497133027919));
#1868 = DIRECTION('',(-1.,0.,-0.));
#1869 = DIRECTION('',(-0.,0.,1.));
#1870 = PCURVE('',#1184,#1871);
#1871 = DEFINITIONAL_REPRESENTATION('',(#1872),#1880);
#1872 = B_SPLINE_CURVE_WITH_KNOTS('',6,(#1873,#1874,#1875,#1876,#1877,
    #1878,#1879),.UNSPECIFIED.,.F.,.F.,(7,7),(0.961136206924,
    1.791542825409),.PIECEWISE_BEZIER_KNOTS.);
#1873 = CARTESIAN_POINT('',(2.301382690323,0.561920124241));
#1874 = CARTESIAN_POINT('',(2.209026739692,0.668940072717));
#1875 = CARTESIAN_POINT('',(2.12809510413,0.799825090339));
#1876 = CARTESIAN_POINT('',(2.058237443732,0.95826230281));
#1877 = CARTESIAN_POINT('',(1.998008447199,1.149322758311));
#1878 = CARTESIAN_POINT('',(1.94605999557,1.379837641433));
#1879 = CARTESIAN_POINT('',(1.901163951684,1.659444296263));
#1880 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1881 = PCURVE('',#1589,#1882);
#1882 = DEFINITIONAL_REPRESENTATION('',(#1883),#1887);
#1883 = ( BOUNDED_CURVE() B_SPLINE_CURVE(2,(#1884,#1885,#1886),
.UNSPECIFIED.,.F.,.F.) B_SPLINE_CURVE_WITH_KNOTS((3,3),(0.961136206924,
1.791542825409),.PIECEWISE_BEZIER_KNOTS.) CURVE() 
GEOMETRIC_REPRESENTATION_ITEM() RATIONAL_B_SPLINE_CURVE((1.,
1.087442349315,1.)) REPRESENTATION_ITEM('') );
#1884 = CARTESIAN_POINT('',(0.6599615,-33.96463287575));
#1885 = CARTESIAN_POINT('',(0.424458430793,-33.66083334583));
#1886 = CARTESIAN_POINT('',(-5.999650000001E-02,-32.86710870373));
#1887 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1888 = ORIENTED_EDGE('',*,*,#1889,.F.);
#1889 = EDGE_CURVE('',#1890,#1862,#1892,.T.);
#1890 = VERTEX_POINT('',#1891);
#1891 = CARTESIAN_POINT('',(77.604133408892,112.30648,19.973447));
#1892 = SURFACE_CURVE('',#1893,(#1898,#1939),.PCURVE_S1.);
#1893 = HYPERBOLA('',#1894,0.877176346884,0.506438);
#1894 = AXIS2_PLACEMENT_3D('',#1895,#1896,#1897);
#1895 = CARTESIAN_POINT('',(73.317494,112.30648,12.497133027919));
#1896 = DIRECTION('',(0.,-1.,0.));
#1897 = DIRECTION('',(0.,0.,1.));
#1898 = PCURVE('',#1184,#1899);
#1899 = DEFINITIONAL_REPRESENTATION('',(#1900),#1938);
#1900 = B_SPLINE_CURVE_WITH_KNOTS('',8,(#1901,#1902,#1903,#1904,#1905,
    #1906,#1907,#1908,#1909,#1910,#1911,#1912,#1913,#1914,#1915,#1916,
    #1917,#1918,#1919,#1920,#1921,#1922,#1923,#1924,#1925,#1926,#1927,
    #1928,#1929,#1930,#1931,#1932,#1933,#1934,#1935,#1936,#1937),
  .UNSPECIFIED.,.F.,.F.,(9,7,7,7,7,9),(-2.832474962845,-1.598977848284,
    -0.673855012363,1.998711457723E-02,1.060750304988,2.101513495399),
  .UNSPECIFIED.);
#1901 = CARTESIAN_POINT('',(0.117598246011,7.));
#1902 = CARTESIAN_POINT('',(0.135688619437,5.855210254458));
#1903 = CARTESIAN_POINT('',(0.156945217624,4.913550652526));
#1904 = CARTESIAN_POINT('',(0.18200227101,4.133553949271));
#1905 = CARTESIAN_POINT('',(0.211652934122,3.484036817935));
#1906 = CARTESIAN_POINT('',(0.246853190726,2.941001246097));
#1907 = CARTESIAN_POINT('',(0.288802140939,2.485693447814));
#1908 = CARTESIAN_POINT('',(0.338956069625,2.10338590184));
#1909 = CARTESIAN_POINT('',(0.443741914712,1.541744794308));
#1910 = CARTESIAN_POINT('',(0.494118755136,1.335562271728));
#1911 = CARTESIAN_POINT('',(0.550607548578,1.158995805451));
#1912 = CARTESIAN_POINT('',(0.613850718783,1.008124813081));
#1913 = CARTESIAN_POINT('',(0.684475045017,0.879804978696));
#1914 = CARTESIAN_POINT('',(0.76292687032,0.771525444212));
#1915 = CARTESIAN_POINT('',(0.849239870699,0.681313321642));
#1916 = CARTESIAN_POINT('',(1.013001599867,0.552433574339));
#1917 = CARTESIAN_POINT('',(1.087272186696,0.506518080065));
#1918 = CARTESIAN_POINT('',(1.165380564208,0.469288235695));
#1919 = CARTESIAN_POINT('',(1.246887785025,0.44026031999));
#1920 = CARTESIAN_POINT('',(1.331119163318,0.419085175713));
#1921 = CARTESIAN_POINT('',(1.417185163373,0.405538605875));
#1922 = CARTESIAN_POINT('',(1.504069165706,0.399516914217));
#1923 = CARTESIAN_POINT('',(1.720851528309,0.403318603479));
#1924 = CARTESIAN_POINT('',(1.850533478064,0.422569915986));
#1925 = CARTESIAN_POINT('',(1.976484912602,0.458850348691));
#1926 = CARTESIAN_POINT('',(2.095387681469,0.512831490655));
#1927 = CARTESIAN_POINT('',(2.205026522403,0.585800751378));
#1928 = CARTESIAN_POINT('',(2.304757276889,0.679720178617));
#1929 = CARTESIAN_POINT('',(2.394592286257,0.797340158014));
#1930 = CARTESIAN_POINT('',(2.555465386916,1.087463543002));
#1931 = CARTESIAN_POINT('',(2.626502634428,1.259966864854));
#1932 = CARTESIAN_POINT('',(2.688627708806,1.463653552759));
#1933 = CARTESIAN_POINT('',(2.74277326196,1.703253959347));
#1934 = CARTESIAN_POINT('',(2.789938507873,1.984696072016));
#1935 = CARTESIAN_POINT('',(2.831044452782,2.315388650847));
#1936 = CARTESIAN_POINT('',(2.86691280234,2.704646152536));
#1937 = CARTESIAN_POINT('',(2.898258032359,3.164323107268));
#1938 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1939 = PCURVE('',#1565,#1940);
#1940 = DEFINITIONAL_REPRESENTATION('',(#1941),#1945);
#1941 = ( BOUNDED_CURVE() B_SPLINE_CURVE(2,(#1942,#1943,#1944),
.UNSPECIFIED.,.F.,.F.) B_SPLINE_CURVE_WITH_KNOTS((3,3),(-2.832474962845,
2.101513495399),.PIECEWISE_BEZIER_KNOTS.) CURVE() 
GEOMETRIC_REPRESENTATION_ITEM() RATIONAL_B_SPLINE_CURVE((1.,
5.935902060898,1.)) REPRESENTATION_ITEM('') );
#1942 = CARTESIAN_POINT('',(4.686639408892,-27.526553));
#1943 = CARTESIAN_POINT('',(0.431880852324,-34.84511229486));
#1944 = CARTESIAN_POINT('',(-1.64,-31.36222989273));
#1945 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1946 = ORIENTED_EDGE('',*,*,#1947,.F.);
#1947 = EDGE_CURVE('',#1717,#1890,#1948,.T.);
#1948 = SURFACE_CURVE('',#1949,(#1954,#1961),.PCURVE_S1.);
#1949 = CIRCLE('',#1950,4.316451884327);
#1950 = AXIS2_PLACEMENT_3D('',#1951,#1952,#1953);
#1951 = CARTESIAN_POINT('',(73.317494,111.800042,19.973447));
#1952 = DIRECTION('',(0.,0.,1.));
#1953 = DIRECTION('',(1.,0.,0.));
#1954 = PCURVE('',#1184,#1955);
#1955 = DEFINITIONAL_REPRESENTATION('',(#1956),#1960);
#1956 = LINE('',#1957,#1958);
#1957 = CARTESIAN_POINT('',(0.,7.));
#1958 = VECTOR('',#1959,1.);
#1959 = DIRECTION('',(1.,0.));
#1960 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1961 = PCURVE('',#1217,#1962);
#1962 = DEFINITIONAL_REPRESENTATION('',(#1963),#1967);
#1963 = CIRCLE('',#1964,4.316451884327);
#1964 = AXIS2_PLACEMENT_2D('',#1965,#1966);
#1965 = CARTESIAN_POINT('',(0.,0.));
#1966 = DIRECTION('',(1.,0.));
#1967 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1968 = ADVANCED_FACE('',(#1969),#1361,.T.);
#1969 = FACE_BOUND('',#1970,.T.);
#1970 = EDGE_LOOP('',(#1971,#1990,#1991));
#1971 = ORIENTED_EDGE('',*,*,#1972,.F.);
#1972 = EDGE_CURVE('',#1791,#1305,#1973,.T.);
#1973 = SURFACE_CURVE('',#1974,(#1978,#1984),.PCURVE_S1.);
#1974 = LINE('',#1975,#1976);
#1975 = CARTESIAN_POINT('',(62.5141635,112.906445,19.973447));
#1976 = VECTOR('',#1977,1.);
#1977 = DIRECTION('',(1.,0.,-0.));
#1978 = PCURVE('',#1361,#1979);
#1979 = DEFINITIONAL_REPRESENTATION('',(#1980),#1983);
#1980 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#1981,#1982),.UNSPECIFIED.,.F.,
  .F.,(2,2),(6.486878615673,10.4033305),.PIECEWISE_BEZIER_KNOTS.);
#1981 = CARTESIAN_POINT('',(-4.316451884327,1.106403));
#1982 = CARTESIAN_POINT('',(-0.4,1.106403));
#1983 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1984 = PCURVE('',#1332,#1985);
#1985 = DEFINITIONAL_REPRESENTATION('',(#1986),#1989);
#1986 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#1987,#1988),.UNSPECIFIED.,.F.,
  .F.,(2,2),(6.486878615673,10.4033305),.PIECEWISE_BEZIER_KNOTS.);
#1987 = CARTESIAN_POINT('',(17.290209115673,-27.526553));
#1988 = CARTESIAN_POINT('',(21.206661,-27.526553));
#1989 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1990 = ORIENTED_EDGE('',*,*,#1790,.T.);
#1991 = ORIENTED_EDGE('',*,*,#1344,.F.);
#1992 = ADVANCED_FACE('',(#1993),#1332,.F.);
#1993 = FACE_BOUND('',#1994,.F.);
#1994 = EDGE_LOOP('',(#1995,#2016,#2017,#2018,#2019));
#1995 = ORIENTED_EDGE('',*,*,#1996,.F.);
#1996 = EDGE_CURVE('',#1503,#1815,#1997,.T.);
#1997 = SURFACE_CURVE('',#1998,(#2002,#2009),.PCURVE_S1.);
#1998 = LINE('',#1999,#2000);
#1999 = CARTESIAN_POINT('',(72.917494,112.906445,21.355218));
#2000 = VECTOR('',#2001,1.);
#2001 = DIRECTION('',(0.,0.,-1.));
#2002 = PCURVE('',#1332,#2003);
#2003 = DEFINITIONAL_REPRESENTATION('',(#2004),#2008);
#2004 = LINE('',#2005,#2006);
#2005 = CARTESIAN_POINT('',(21.206661,-26.144782));
#2006 = VECTOR('',#2007,1.);
#2007 = DIRECTION('',(0.,-1.));
#2008 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2009 = PCURVE('',#1589,#2010);
#2010 = DEFINITIONAL_REPRESENTATION('',(#2011),#2015);
#2011 = LINE('',#2012,#2013);
#2012 = CARTESIAN_POINT('',(0.,-26.144782));
#2013 = VECTOR('',#2014,1.);
#2014 = DIRECTION('',(0.,-1.));
#2015 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2016 = ORIENTED_EDGE('',*,*,#1502,.F.);
#2017 = ORIENTED_EDGE('',*,*,#1304,.F.);
#2018 = ORIENTED_EDGE('',*,*,#1972,.F.);
#2019 = ORIENTED_EDGE('',*,*,#1814,.F.);
#2020 = ADVANCED_FACE('',(#2021),#1245,.T.);
#2021 = FACE_BOUND('',#2022,.T.);
#2022 = EDGE_LOOP('',(#2023,#2047,#2082,#2083));
#2023 = ORIENTED_EDGE('',*,*,#2024,.T.);
#2024 = EDGE_CURVE('',#1201,#2025,#2027,.T.);
#2025 = VERTEX_POINT('',#2026);
#2026 = CARTESIAN_POINT('',(75.839966573532,112.30648,19.973447));
#2027 = SURFACE_CURVE('',#2028,(#2033,#2040),.PCURVE_S1.);
#2028 = CIRCLE('',#2029,3.518686025519);
#2029 = AXIS2_PLACEMENT_3D('',#2030,#2031,#2032);
#2030 = CARTESIAN_POINT('',(73.317494,109.853261,19.973447));
#2031 = DIRECTION('',(0.,0.,1.));
#2032 = DIRECTION('',(1.,0.,-0.));
#2033 = PCURVE('',#1245,#2034);
#2034 = DEFINITIONAL_REPRESENTATION('',(#2035),#2039);
#2035 = LINE('',#2036,#2037);
#2036 = CARTESIAN_POINT('',(0.,5.618229));
#2037 = VECTOR('',#2038,1.);
#2038 = DIRECTION('',(1.,0.));
#2039 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2040 = PCURVE('',#1217,#2041);
#2041 = DEFINITIONAL_REPRESENTATION('',(#2042),#2046);
#2042 = CIRCLE('',#2043,3.518686025519);
#2043 = AXIS2_PLACEMENT_2D('',#2044,#2045);
#2044 = CARTESIAN_POINT('',(0.,-1.946781));
#2045 = DIRECTION('',(1.,0.));
#2046 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2047 = ORIENTED_EDGE('',*,*,#2048,.F.);
#2048 = EDGE_CURVE('',#1527,#2025,#2049,.T.);
#2049 = SURFACE_CURVE('',#2050,(#2055,#2075),.PCURVE_S1.);
#2050 = HYPERBOLA('',#2051,4.249099950093,2.453219);
#2051 = AXIS2_PLACEMENT_3D('',#2052,#2053,#2054);
#2052 = CARTESIAN_POINT('',(73.317494,112.30648,13.878904027919));
#2053 = DIRECTION('',(0.,-1.,0.));
#2054 = DIRECTION('',(0.,0.,1.));
#2055 = PCURVE('',#1245,#2056);
#2056 = DEFINITIONAL_REPRESENTATION('',(#2057),#2074);
#2057 = B_SPLINE_CURVE_WITH_KNOTS('',8,(#2058,#2059,#2060,#2061,#2062,
    #2063,#2064,#2065,#2066,#2067,#2068,#2069,#2070,#2071,#2072,#2073),
  .UNSPECIFIED.,.F.,.F.,(9,7,9),(-1.165402492019,-0.204139441328,
    0.757123609363),.UNSPECIFIED.);
#2058 = CARTESIAN_POINT('',(0.604488826333,7.));
#2059 = CARTESIAN_POINT('',(0.672779544124,6.260853989044));
#2060 = CARTESIAN_POINT('',(0.748785966666,5.64507060412));
#2061 = CARTESIAN_POINT('',(0.833043053089,5.13638893667));
#2062 = CARTESIAN_POINT('',(0.925847458002,4.722344679139));
#2063 = CARTESIAN_POINT('',(1.027109129084,4.393528151474));
#2064 = CARTESIAN_POINT('',(1.135920923013,4.143110934039));
#2065 = CARTESIAN_POINT('',(1.250363172603,3.966581476468));
#2066 = CARTESIAN_POINT('',(1.485757103353,3.756678627338));
#2067 = CARTESIAN_POINT('',(1.606708589145,3.723305344584));
#2068 = CARTESIAN_POINT('',(1.728533072853,3.759201198583));
#2069 = CARTESIAN_POINT('',(1.848385617093,3.864261890412));
#2070 = CARTESIAN_POINT('',(1.963356884617,4.040481012183));
#2071 = CARTESIAN_POINT('',(2.071598944693,4.292013431418));
#2072 = CARTESIAN_POINT('',(2.172099080551,4.625391254823));
#2073 = CARTESIAN_POINT('',(2.264487423665,5.049955147848));
#2074 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2075 = PCURVE('',#1565,#2076);
#2076 = DEFINITIONAL_REPRESENTATION('',(#2077),#2081);
#2077 = ( BOUNDED_CURVE() B_SPLINE_CURVE(2,(#2078,#2079,#2080),
.UNSPECIFIED.,.F.,.F.) B_SPLINE_CURVE_WITH_KNOTS((3,3),(-1.165402492019,
0.757123609363),.PIECEWISE_BEZIER_KNOTS.) CURVE() 
GEOMETRIC_REPRESENTATION_ITEM() RATIONAL_B_SPLINE_CURVE((1.,
1.498703420344,1.)) REPRESENTATION_ITEM('') );
#2078 = CARTESIAN_POINT('',(3.951545214094,-26.144782));
#2079 = CARTESIAN_POINT('',(0.736480383219,-30.72663135346));
#2080 = CARTESIAN_POINT('',(-1.64,-28.09482685215));
#2081 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2082 = ORIENTED_EDGE('',*,*,#1526,.F.);
#2083 = ORIENTED_EDGE('',*,*,#1229,.F.);
#2084 = ADVANCED_FACE('',(#2085),#1217,.T.);
#2085 = FACE_BOUND('',#2086,.T.);
#2086 = EDGE_LOOP('',(#2087,#2088,#2089,#2090,#2109));
#2087 = ORIENTED_EDGE('',*,*,#1200,.F.);
#2088 = ORIENTED_EDGE('',*,*,#1766,.T.);
#2089 = ORIENTED_EDGE('',*,*,#1947,.T.);
#2090 = ORIENTED_EDGE('',*,*,#2091,.F.);
#2091 = EDGE_CURVE('',#2025,#1890,#2092,.T.);
#2092 = SURFACE_CURVE('',#2093,(#2097,#2103),.PCURVE_S1.);
#2093 = LINE('',#2094,#2095);
#2094 = CARTESIAN_POINT('',(73.117494,112.30648,19.973447));
#2095 = VECTOR('',#2096,1.);
#2096 = DIRECTION('',(1.,0.,-0.));
#2097 = PCURVE('',#1217,#2098);
#2098 = DEFINITIONAL_REPRESENTATION('',(#2099),#2102);
#2099 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#2100,#2101),.UNSPECIFIED.,.F.,
  .F.,(2,2),(-0.2,4.516451884327),.PIECEWISE_BEZIER_KNOTS.);
#2100 = CARTESIAN_POINT('',(-0.4,0.506438));
#2101 = CARTESIAN_POINT('',(4.316451884327,0.506438));
#2102 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2103 = PCURVE('',#1565,#2104);
#2104 = DEFINITIONAL_REPRESENTATION('',(#2105),#2108);
#2105 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#2106,#2107),.UNSPECIFIED.,.F.,
  .F.,(2,2),(-0.2,4.516451884327),.PIECEWISE_BEZIER_KNOTS.);
#2106 = CARTESIAN_POINT('',(0.,-27.526553));
#2107 = CARTESIAN_POINT('',(4.716451884327,-27.526553));
#2108 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2109 = ORIENTED_EDGE('',*,*,#2024,.F.);
#2110 = ADVANCED_FACE('',(#2111),#1589,.F.);
#2111 = FACE_BOUND('',#2112,.F.);
#2112 = EDGE_LOOP('',(#2113,#2114,#2115,#2116));
#2113 = ORIENTED_EDGE('',*,*,#1576,.F.);
#2114 = ORIENTED_EDGE('',*,*,#1996,.T.);
#2115 = ORIENTED_EDGE('',*,*,#1861,.F.);
#2116 = ORIENTED_EDGE('',*,*,#2117,.F.);
#2117 = EDGE_CURVE('',#1551,#1862,#2118,.T.);
#2118 = SURFACE_CURVE('',#2119,(#2123,#2130),.PCURVE_S1.);
#2119 = LINE('',#2120,#2121);
#2120 = CARTESIAN_POINT('',(72.917494,112.30648,21.355218));
#2121 = VECTOR('',#2122,1.);
#2122 = DIRECTION('',(0.,0.,-1.));
#2123 = PCURVE('',#1589,#2124);
#2124 = DEFINITIONAL_REPRESENTATION('',(#2125),#2129);
#2125 = LINE('',#2126,#2127);
#2126 = CARTESIAN_POINT('',(0.599965,-26.144782));
#2127 = VECTOR('',#2128,1.);
#2128 = DIRECTION('',(0.,-1.));
#2129 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2130 = PCURVE('',#1565,#2131);
#2131 = DEFINITIONAL_REPRESENTATION('',(#2132),#2136);
#2132 = LINE('',#2133,#2134);
#2133 = CARTESIAN_POINT('',(0.,-26.144782));
#2134 = VECTOR('',#2135,1.);
#2135 = DIRECTION('',(0.,-1.));
#2136 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2137 = ADVANCED_FACE('',(#2138),#1565,.F.);
#2138 = FACE_BOUND('',#2139,.F.);
#2139 = EDGE_LOOP('',(#2140,#2141,#2142,#2143,#2144));
#2140 = ORIENTED_EDGE('',*,*,#1550,.F.);
#2141 = ORIENTED_EDGE('',*,*,#2117,.T.);
#2142 = ORIENTED_EDGE('',*,*,#1889,.F.);
#2143 = ORIENTED_EDGE('',*,*,#2091,.F.);
#2144 = ORIENTED_EDGE('',*,*,#2048,.F.);
#2145 = ADVANCED_FACE('',(#2146),#1487,.F.);
#2146 = FACE_BOUND('',#2147,.F.);
#2147 = EDGE_LOOP('',(#2148,#2149,#2170,#2171));
#2148 = ORIENTED_EDGE('',*,*,#1602,.F.);
#2149 = ORIENTED_EDGE('',*,*,#2150,.F.);
#2150 = EDGE_CURVE('',#1450,#1603,#2151,.T.);
#2151 = SEAM_CURVE('',#2152,(#2156,#2163),.PCURVE_S1.);
#2152 = LINE('',#2153,#2154);
#2153 = CARTESIAN_POINT('',(73.592494,109.853261,14.355218));
#2154 = VECTOR('',#2155,1.);
#2155 = DIRECTION('',(0.,0.,1.));
#2156 = PCURVE('',#1487,#2157);
#2157 = DEFINITIONAL_REPRESENTATION('',(#2158),#2162);
#2158 = LINE('',#2159,#2160);
#2159 = CARTESIAN_POINT('',(0.,-0.));
#2160 = VECTOR('',#2161,1.);
#2161 = DIRECTION('',(0.,1.));
#2162 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2163 = PCURVE('',#1487,#2164);
#2164 = DEFINITIONAL_REPRESENTATION('',(#2165),#2169);
#2165 = LINE('',#2166,#2167);
#2166 = CARTESIAN_POINT('',(6.28318530718,-0.));
#2167 = VECTOR('',#2168,1.);
#2168 = DIRECTION('',(0.,1.));
#2169 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2170 = ORIENTED_EDGE('',*,*,#1472,.T.);
#2171 = ORIENTED_EDGE('',*,*,#2150,.T.);
#2172 = ADVANCED_FACE('',(#2173),#1645,.F.);
#2173 = FACE_BOUND('',#2174,.F.);
#2174 = EDGE_LOOP('',(#2175,#2176,#2199,#2463));
#2175 = ORIENTED_EDGE('',*,*,#1628,.F.);
#2176 = ORIENTED_EDGE('',*,*,#2177,.F.);
#2177 = EDGE_CURVE('',#2178,#1629,#2180,.T.);
#2178 = VERTEX_POINT('',#2179);
#2179 = CARTESIAN_POINT('',(73.592494,111.800042,17.28430323886));
#2180 = SEAM_CURVE('',#2181,(#2185,#2192),.PCURVE_S1.);
#2181 = LINE('',#2182,#2183);
#2182 = CARTESIAN_POINT('',(73.592494,111.800042,12.973447));
#2183 = VECTOR('',#2184,1.);
#2184 = DIRECTION('',(0.,0.,1.));
#2185 = PCURVE('',#1645,#2186);
#2186 = DEFINITIONAL_REPRESENTATION('',(#2187),#2191);
#2187 = LINE('',#2188,#2189);
#2188 = CARTESIAN_POINT('',(6.28318530718,-0.));
#2189 = VECTOR('',#2190,1.);
#2190 = DIRECTION('',(0.,1.));
#2191 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2192 = PCURVE('',#1645,#2193);
#2193 = DEFINITIONAL_REPRESENTATION('',(#2194),#2198);
#2194 = LINE('',#2195,#2196);
#2195 = CARTESIAN_POINT('',(0.,-0.));
#2196 = VECTOR('',#2197,1.);
#2197 = DIRECTION('',(0.,1.));
#2198 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2199 = ORIENTED_EDGE('',*,*,#2200,.T.);
#2200 = EDGE_CURVE('',#2178,#2178,#2201,.T.);
#2201 = SURFACE_CURVE('',#2202,(#2265,#2397),.PCURVE_S1.);
#2202 = B_SPLINE_CURVE_WITH_KNOTS('',7,(#2203,#2204,#2205,#2206,#2207,
    #2208,#2209,#2210,#2211,#2212,#2213,#2214,#2215,#2216,#2217,#2218,
    #2219,#2220,#2221,#2222,#2223,#2224,#2225,#2226,#2227,#2228,#2229,
    #2230,#2231,#2232,#2233,#2234,#2235,#2236,#2237,#2238,#2239,#2240,
    #2241,#2242,#2243,#2244,#2245,#2246,#2247,#2248,#2249,#2250,#2251,
    #2252,#2253,#2254,#2255,#2256,#2257,#2258,#2259,#2260,#2261,#2262,
    #2263,#2264),.UNSPECIFIED.,.T.,.F.,(8,6,6,6,6,6,6,6,6,6,8),(0.,
    0.141123085209,0.245855915845,0.352486603823,0.514755500592,
    0.655524141446,0.745966837792,0.82747842928,0.943679440052,
    0.977466283651,1.),.UNSPECIFIED.);
#2203 = CARTESIAN_POINT('',(73.592494,111.800042,17.284303238861));
#2204 = CARTESIAN_POINT('',(73.592494,111.83117807423,17.337702368281));
#2205 = CARTESIAN_POINT('',(73.588381689493,111.86249895473,
    17.390441041128));
#2206 = CARTESIAN_POINT('',(73.580066262068,111.89366544647,
    17.441983781659));
#2207 = CARTESIAN_POINT('',(73.567347473469,111.9242262721,
    17.491651884555));
#2208 = CARTESIAN_POINT('',(73.549997544515,111.95354546989,
    17.538543718555));
#2209 = CARTESIAN_POINT('',(73.527875556329,111.98076373472,
    17.58148497428));
#2210 = CARTESIAN_POINT('',(73.481184130184,112.02262725867,
    17.646856762037));
#2211 = CARTESIAN_POINT('',(73.458708431506,112.03871271718,
    17.671732947594));
#2212 = CARTESIAN_POINT('',(73.433747788857,112.05254357197,
    17.692940000086));
#2213 = CARTESIAN_POINT('',(73.406562994521,112.06355125671,
    17.709702333437));
#2214 = CARTESIAN_POINT('',(73.377646349801,112.07116490027,
    17.721257384161));
#2215 = CARTESIAN_POINT('',(73.3477880244,112.07499451778,
    17.727069541005));
#2216 = CARTESIAN_POINT('',(73.287527165433,112.07508965106,
    17.727213921661));
#2217 = CARTESIAN_POINT('',(73.257109433825,112.07121687755,
    17.721336257743));
#2218 = CARTESIAN_POINT('',(73.227668431277,112.06341570464,
    17.709497577882));
#2219 = CARTESIAN_POINT('',(73.200038314485,112.05210103297,
    17.692267931867));
#2220 = CARTESIAN_POINT('',(73.174731411202,112.03787949238,
    17.670457040608));
#2221 = CARTESIAN_POINT('',(73.152013690396,112.02135082432,
    17.644883440015));
#2222 = CARTESIAN_POINT('',(73.10149932588,111.97518911105,
    17.572747168017));
#2223 = CARTESIAN_POINT('',(73.077233025067,111.94321380257,
    17.522188286468));
#2224 = CARTESIAN_POINT('',(73.059263368675,111.90874027115,
    17.466789960332));
#2225 = CARTESIAN_POINT('',(73.047317835713,111.87297841556,
    17.4081951657));
#2226 = CARTESIAN_POINT('',(73.041057698808,111.83679424771,
    17.347670264811));
#2227 = CARTESIAN_POINT('',(73.040192568638,111.80077894122,
    17.286126612162));
#2228 = CARTESIAN_POINT('',(73.048580843181,111.73471444115,
    17.170794568144));
#2229 = CARTESIAN_POINT('',(73.0564930473,111.70463096343,
    17.117275456992));
#2230 = CARTESIAN_POINT('',(73.068341048527,111.67542747776,
    17.064295177818));
#2231 = CARTESIAN_POINT('',(73.084191999765,111.6474532994,
    17.012522155359));
#2232 = CARTESIAN_POINT('',(73.104154639604,111.62121378581,
    16.962984092305));
#2233 = CARTESIAN_POINT('',(73.128351259386,111.59734142791,
    16.917104691577));
#2234 = CARTESIAN_POINT('',(73.175256364423,111.56360112158,
    16.851379949576));
#2235 = CARTESIAN_POINT('',(73.195407923113,111.55177496785,
    16.828085701828));
#2236 = CARTESIAN_POINT('',(73.217329901543,111.54162765833,
    16.807892696215));
#2237 = CARTESIAN_POINT('',(73.240879869191,111.53353915346,
    16.791652220274));
#2238 = CARTESIAN_POINT('',(73.265753955398,111.52791702808,
    16.780312308357));
#2239 = CARTESIAN_POINT('',(73.291397951625,111.52508285491,
    16.774596061954));
#2240 = CARTESIAN_POINT('',(73.340187687468,111.52500583099,
    16.774440707446));
#2241 = CARTESIAN_POINT('',(73.36332459521,111.52723851047,
    16.778943863344));
#2242 = CARTESIAN_POINT('',(73.385915262637,111.53174366346,
    16.788031490084));
#2243 = CARTESIAN_POINT('',(73.407528287041,111.53831315741,
    16.801236712279));
#2244 = CARTESIAN_POINT('',(73.427897424504,111.54665880971,
    16.817891791346));
#2245 = CARTESIAN_POINT('',(73.446876506997,111.55649491147,
    16.837352617768));
#2246 = CARTESIAN_POINT('',(73.489396208349,111.58336587847,
    16.890012783258));
#2247 = CARTESIAN_POINT('',(73.51145137295,111.60168968954,
    16.925548231189));
#2248 = CARTESIAN_POINT('',(73.530564369043,111.62193767401,
    16.964277190004));
#2249 = CARTESIAN_POINT('',(73.546792718687,111.64365331567,
    17.005169113269));
#2250 = CARTESIAN_POINT('',(73.560220344624,111.66649465743,
    17.047486519296));
#2251 = CARTESIAN_POINT('',(73.570917635528,111.69021349561,
    17.090724867406));
#2252 = CARTESIAN_POINT('',(73.581221045529,111.72175461584,
    17.147306679094));
#2253 = CARTESIAN_POINT('',(73.583311441195,111.72891706088,
    17.160097210445));
#2254 = CARTESIAN_POINT('',(73.585171495833,111.73613148474,
    17.172922045229));
#2255 = CARTESIAN_POINT('',(73.586801185392,111.74339255139,
    17.185771153737));
#2256 = CARTESIAN_POINT('',(73.588200212526,111.75069498034,
    17.198634866952));
#2257 = CARTESIAN_POINT('',(73.589368006583,111.7580335467,
    17.211503876543));
#2258 = CARTESIAN_POINT('',(73.590927788483,111.77031810068,
    17.232949626062));
#2259 = CARTESIAN_POINT('',(73.591448626564,111.77524687114,
    17.241528347963));
#2260 = CARTESIAN_POINT('',(73.591865988383,111.78018784364,
    17.250102715213));
#2261 = CARTESIAN_POINT('',(73.592179576363,111.78513944714,
    17.258670051262));
#2262 = CARTESIAN_POINT('',(73.592389044828,111.79010008837,
    17.267227688371));
#2263 = CARTESIAN_POINT('',(73.592494,111.79506815189,17.27577296761));
#2264 = CARTESIAN_POINT('',(73.592494,111.800042,17.284303238861));
#2265 = PCURVE('',#1645,#2266);
#2266 = DEFINITIONAL_REPRESENTATION('',(#2267),#2396);
#2267 = B_SPLINE_CURVE_WITH_KNOTS('',10,(#2268,#2269,#2270,#2271,#2272,
    #2273,#2274,#2275,#2276,#2277,#2278,#2279,#2280,#2281,#2282,#2283,
    #2284,#2285,#2286,#2287,#2288,#2289,#2290,#2291,#2292,#2293,#2294,
    #2295,#2296,#2297,#2298,#2299,#2300,#2301,#2302,#2303,#2304,#2305,
    #2306,#2307,#2308,#2309,#2310,#2311,#2312,#2313,#2314,#2315,#2316,
    #2317,#2318,#2319,#2320,#2321,#2322,#2323,#2324,#2325,#2326,#2327,
    #2328,#2329,#2330,#2331,#2332,#2333,#2334,#2335,#2336,#2337,#2338,
    #2339,#2340,#2341,#2342,#2343,#2344,#2345,#2346,#2347,#2348,#2349,
    #2350,#2351,#2352,#2353,#2354,#2355,#2356,#2357,#2358,#2359,#2360,
    #2361,#2362,#2363,#2364,#2365,#2366,#2367,#2368,#2369,#2370,#2371,
    #2372,#2373,#2374,#2375,#2376,#2377,#2378,#2379,#2380,#2381,#2382,
    #2383,#2384,#2385,#2386,#2387,#2388,#2389,#2390,#2391,#2392,#2393,
    #2394,#2395),.UNSPECIFIED.,.F.,.F.,(11,9,9,9,9,9,9,9,9,9,9,9,9,9,11)
  ,(0.,0.125,0.1796875,0.2822265625,0.327087402344,0.369144439697,
    0.448001384735,0.517001211643,0.637750908732,0.683032045141,
    0.762274033856,0.821705525392,0.910852762696,0.955426381348,1.),
  .UNSPECIFIED.);
#2268 = CARTESIAN_POINT('',(-5.062616992291E-14,4.310856238861));
#2269 = CARTESIAN_POINT('',(7.020065282482E-02,4.343965094278));
#2270 = CARTESIAN_POINT('',(0.140647438129,4.37683213931));
#2271 = CARTESIAN_POINT('',(0.211570392588,4.409348840796));
#2272 = CARTESIAN_POINT('',(0.283172013289,4.441391971939));
#2273 = CARTESIAN_POINT('',(0.355634382151,4.472819991404));
#2274 = CARTESIAN_POINT('',(0.429125301629,4.503469904427));
#2275 = CARTESIAN_POINT('',(0.503803441692,4.533154605953));
#2276 = CARTESIAN_POINT('',(0.579822498083,4.561660705781));
#2277 = CARTESIAN_POINT('',(0.657334361847,4.588746835732));
#2278 = CARTESIAN_POINT('',(0.77112246065,4.625253015191));
#2279 = CARTESIAN_POINT('',(0.806069292573,4.636040278079));
#2280 = CARTESIAN_POINT('',(0.841341902121,4.646480349553));
#2281 = CARTESIAN_POINT('',(0.87694700194,4.656549330786));
#2282 = CARTESIAN_POINT('',(0.912941859239,4.666232630472));
#2283 = CARTESIAN_POINT('',(0.949285719103,4.675489027781));
#2284 = CARTESIAN_POINT('',(0.9859439105,4.684280423228));
#2285 = CARTESIAN_POINT('',(1.022966070384,4.692591831296));
#2286 = CARTESIAN_POINT('',(1.060303775677,4.70038221694));
#2287 = CARTESIAN_POINT('',(1.168549187962,4.721206006881));
#2288 = CARTESIAN_POINT('',(1.240235271709,4.732861759173));
#2289 = CARTESIAN_POINT('',(1.312940385245,4.742392468337));
#2290 = CARTESIAN_POINT('',(1.386464097752,4.749623126343));
#2291 = CARTESIAN_POINT('',(1.460694857025,4.754387693385));
#2292 = CARTESIAN_POINT('',(1.535410113711,4.756558464187));
#2293 = CARTESIAN_POINT('',(1.610189854592,4.756083992));
#2294 = CARTESIAN_POINT('',(1.68460413969,4.753008277041));
#2295 = CARTESIAN_POINT('',(1.7584654583,4.747453528167));
#2296 = CARTESIAN_POINT('',(1.863548837416,4.736104293719));
#2297 = CARTESIAN_POINT('',(1.895385472804,4.732202408109));
#2298 = CARTESIAN_POINT('',(1.927060688302,4.727865002352));
#2299 = CARTESIAN_POINT('',(1.958560303205,4.723105669987));
#2300 = CARTESIAN_POINT('',(1.98987199696,4.717939114648));
#2301 = CARTESIAN_POINT('',(2.020985192753,4.712380917269));
#2302 = CARTESIAN_POINT('',(2.05189094108,4.706447303272));
#2303 = CARTESIAN_POINT('',(2.082581803328,4.700154909767));
#2304 = CARTESIAN_POINT('',(2.113051735355,4.693520552747));
#2305 = CARTESIAN_POINT('',(2.171649942044,4.680036408223));
#2306 = CARTESIAN_POINT('',(2.199805625441,4.673225971431));
#2307 = CARTESIAN_POINT('',(2.227758251889,4.66614394885));
#2308 = CARTESIAN_POINT('',(2.255507690911,4.658803217527));
#2309 = CARTESIAN_POINT('',(2.283050505438,4.651217976661));
#2310 = CARTESIAN_POINT('',(2.310382083035,4.643403027438));
#2311 = CARTESIAN_POINT('',(2.337502761653,4.635371630542));
#2312 = CARTESIAN_POINT('',(2.364421867726,4.627133949187));
#2313 = CARTESIAN_POINT('',(2.391153584466,4.618698085561));
#2314 = CARTESIAN_POINT('',(2.467470412521,4.593908780336));
#2315 = CARTESIAN_POINT('',(2.516590067367,4.577084974807));
#2316 = CARTESIAN_POINT('',(2.565099785037,4.559670359589));
#2317 = CARTESIAN_POINT('',(2.613040454934,4.541726424706));
#2318 = CARTESIAN_POINT('',(2.660452084446,4.523310350503));
#2319 = CARTESIAN_POINT('',(2.707374279896,4.504475265586));
#2320 = CARTESIAN_POINT('',(2.753846727479,4.485270504764));
#2321 = CARTESIAN_POINT('',(2.799909674218,4.465741866993));
#2322 = CARTESIAN_POINT('',(2.845604408901,4.445931873316));
#2323 = CARTESIAN_POINT('',(2.930671910396,4.408334657361));
#2324 = CARTESIAN_POINT('',(2.970118536953,4.390605264194));
#2325 = CARTESIAN_POINT('',(3.009366684001,4.372706692171));
#2326 = CARTESIAN_POINT('',(3.048333285599,4.354717312788));
#2327 = CARTESIAN_POINT('',(3.087362715731,4.336510788416));
#2328 = CARTESIAN_POINT('',(3.12590286577,4.318387116422));
#2329 = CARTESIAN_POINT('',(3.164753793729,4.300001675321));
#2330 = CARTESIAN_POINT('',(3.203228552073,4.281718229846));
#2331 = CARTESIAN_POINT('',(3.241860536902,4.263319613905));
#2332 = CARTESIAN_POINT('',(3.34797695941,4.21277509054));
#2333 = CARTESIAN_POINT('',(3.415423123643,4.18064319227));
#2334 = CARTESIAN_POINT('',(3.48302160861,4.148634107331));
#2335 = CARTESIAN_POINT('',(3.550985806537,4.116857622976));
#2336 = CARTESIAN_POINT('',(3.619513906757,4.085446789807));
#2337 = CARTESIAN_POINT('',(3.688796557123,4.054562461695));
#2338 = CARTESIAN_POINT('',(3.75902599294,4.02439888462));
#2339 = CARTESIAN_POINT('',(3.83040663342,3.995190334427));
#2340 = CARTESIAN_POINT('',(3.903167145653,3.967218803493));
#2341 = CARTESIAN_POINT('',(4.005476537518,3.93092421113));
#2342 = CARTESIAN_POINT('',(4.033609414451,3.9212476586));
#2343 = CARTESIAN_POINT('',(4.06199759154,3.91180767249));
#2344 = CARTESIAN_POINT('',(4.090617740732,3.902635310011));
#2345 = CARTESIAN_POINT('',(4.1195496755,3.893730195661));
#2346 = CARTESIAN_POINT('',(4.148783240201,3.885124105148));
#2347 = CARTESIAN_POINT('',(4.178282931407,3.876857214027));
#2348 = CARTESIAN_POINT('',(4.208088400543,3.868944277753));
#2349 = CARTESIAN_POINT('',(4.238178827452,3.861421718445));
#2350 = CARTESIAN_POINT('',(4.321707753845,3.841886296936));
#2351 = CARTESIAN_POINT('',(4.375726516639,3.830737585191));
#2352 = CARTESIAN_POINT('',(4.430615316833,3.82103380654));
#2353 = CARTESIAN_POINT('',(4.486155420532,3.812935938593));
#2354 = CARTESIAN_POINT('',(4.542687382539,3.806610526284));
#2355 = CARTESIAN_POINT('',(4.599340595993,3.802189082373));
#2356 = CARTESIAN_POINT('',(4.656794356754,3.799777936161));
#2357 = CARTESIAN_POINT('',(4.714223826337,3.799447854857));
#2358 = CARTESIAN_POINT('',(4.771468177154,3.80114410046));
#2359 = CARTESIAN_POINT('',(4.870984699561,3.807521613091));
#2360 = CARTESIAN_POINT('',(4.913420812638,3.811350933515));
#2361 = CARTESIAN_POINT('',(4.95556211144,3.816240198089));
#2362 = CARTESIAN_POINT('',(4.997343049792,3.822139948798));
#2363 = CARTESIAN_POINT('',(5.038712118465,3.828990536228));
#2364 = CARTESIAN_POINT('',(5.079630439127,3.836725229605));
#2365 = CARTESIAN_POINT('',(5.120070358279,3.845273326841));
#2366 = CARTESIAN_POINT('',(5.160014041206,3.85456326457));
#2367 = CARTESIAN_POINT('',(5.199452065919,3.864525728191));
#2368 = CARTESIAN_POINT('',(5.29677694387,3.890953312492));
#2369 = CARTESIAN_POINT('',(5.354030463631,3.908178255479));
#2370 = CARTESIAN_POINT('',(5.410085102591,3.926533521939));
#2371 = CARTESIAN_POINT('',(5.465418874509,3.945946909254));
#2372 = CARTESIAN_POINT('',(5.519544242172,3.966113875387));
#2373 = CARTESIAN_POINT('',(5.573187296734,3.987069685361));
#2374 = CARTESIAN_POINT('',(5.62593293643,4.008566128381));
#2375 = CARTESIAN_POINT('',(5.678141063957,4.030575392899));
#2376 = CARTESIAN_POINT('',(5.729796929041,4.05298489573));
#2377 = CARTESIAN_POINT('',(5.806619288276,4.087098898666));
#2378 = CARTESIAN_POINT('',(5.832117662388,4.098554178513));
#2379 = CARTESIAN_POINT('',(5.857505029604,4.110078186481));
#2380 = CARTESIAN_POINT('',(5.882833932423,4.121681853171));
#2381 = CARTESIAN_POINT('',(5.908052140546,4.133327874872));
#2382 = CARTESIAN_POINT('',(5.933206032812,4.14502550051));
#2383 = CARTESIAN_POINT('',(5.95834691401,4.15678683173));
#2384 = CARTESIAN_POINT('',(5.983421442632,4.168575586562));
#2385 = CARTESIAN_POINT('',(6.008455915094,4.180392795138));
#2386 = CARTESIAN_POINT('',(6.058464933825,4.204071855391));
#2387 = CARTESIAN_POINT('',(6.083438747142,4.215933358305));
#2388 = CARTESIAN_POINT('',(6.108390951164,4.227810214396));
#2389 = CARTESIAN_POINT('',(6.133330478469,4.239696020715));
#2390 = CARTESIAN_POINT('',(6.15826611967,4.2515845461));
#2391 = CARTESIAN_POINT('',(6.183206535637,4.2634696863));
#2392 = CARTESIAN_POINT('',(6.208160269697,4.275345419092));
#2393 = CARTESIAN_POINT('',(6.23313575986,4.28720575941));
#2394 = CARTESIAN_POINT('',(6.258141351021,4.299044714464));
#2395 = CARTESIAN_POINT('',(6.283185307179,4.310856238861));
#2396 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2397 = PCURVE('',#1754,#2398);
#2398 = DEFINITIONAL_REPRESENTATION('',(#2399),#2462);
#2399 = B_SPLINE_CURVE_WITH_KNOTS('',7,(#2400,#2401,#2402,#2403,#2404,
    #2405,#2406,#2407,#2408,#2409,#2410,#2411,#2412,#2413,#2414,#2415,
    #2416,#2417,#2418,#2419,#2420,#2421,#2422,#2423,#2424,#2425,#2426,
    #2427,#2428,#2429,#2430,#2431,#2432,#2433,#2434,#2435,#2436,#2437,
    #2438,#2439,#2440,#2441,#2442,#2443,#2444,#2445,#2446,#2447,#2448,
    #2449,#2450,#2451,#2452,#2453,#2454,#2455,#2456,#2457,#2458,#2459,
    #2460,#2461),.UNSPECIFIED.,.F.,.F.,(8,6,6,6,6,6,6,6,6,6,8),(0.,
    0.141123085209,0.245855915845,0.352486603823,0.514755500592,
    0.655524141446,0.745966837792,0.82747842928,0.943679440052,
    0.977466283651,1.),.UNSPECIFIED.);
#2400 = CARTESIAN_POINT('',(0.,4.310856238862));
#2401 = CARTESIAN_POINT('',(0.113222088118,4.364255368282));
#2402 = CARTESIAN_POINT('',(0.227116844032,4.416994041126));
#2403 = CARTESIAN_POINT('',(0.342817253076,4.46853678166));
#2404 = CARTESIAN_POINT('',(0.461193247151,4.518204884557));
#2405 = CARTESIAN_POINT('',(0.582998252801,4.565096718553));
#2406 = CARTESIAN_POINT('',(0.708966767004,4.60803797428));
#2407 = CARTESIAN_POINT('',(0.937006024534,4.673409762037));
#2408 = CARTESIAN_POINT('',(1.036905439739,4.698285947594));
#2409 = CARTESIAN_POINT('',(1.1395584002,4.719493000086));
#2410 = CARTESIAN_POINT('',(1.244752922049,4.736255333437));
#2411 = CARTESIAN_POINT('',(1.352038476906,4.747810384161));
#2412 = CARTESIAN_POINT('',(1.460636102106,4.753622541005));
#2413 = CARTESIAN_POINT('',(1.679766771415,4.753766921661));
#2414 = CARTESIAN_POINT('',(1.79035437746,4.747889257743));
#2415 = CARTESIAN_POINT('',(1.899573936524,4.736050577882));
#2416 = CARTESIAN_POINT('',(2.006611142632,4.718820931867));
#2417 = CARTESIAN_POINT('',(2.111000228333,4.697010040608));
#2418 = CARTESIAN_POINT('',(2.212527739601,4.671436440015));
#2419 = CARTESIAN_POINT('',(2.461362506242,4.599300168017));
#2420 = CARTESIAN_POINT('',(2.604938175937,4.548741286468));
#2421 = CARTESIAN_POINT('',(2.743271818745,4.493342960332));
#2422 = CARTESIAN_POINT('',(2.877468526025,4.4347481657));
#2423 = CARTESIAN_POINT('',(3.008689498294,4.374223264811));
#2424 = CARTESIAN_POINT('',(3.138215551061,4.312679612162));
#2425 = CARTESIAN_POINT('',(3.380378840517,4.197347568144));
#2426 = CARTESIAN_POINT('',(3.492525259255,4.143828456992));
#2427 = CARTESIAN_POINT('',(3.605629854349,4.090848177818));
#2428 = CARTESIAN_POINT('',(3.720705773371,4.039075155359));
#2429 = CARTESIAN_POINT('',(3.838765860656,3.989537092305));
#2430 = CARTESIAN_POINT('',(3.960886179577,3.943657691577));
#2431 = CARTESIAN_POINT('',(4.170994287123,3.877932949576));
#2432 = CARTESIAN_POINT('',(4.255593325054,3.854638701828));
#2433 = CARTESIAN_POINT('',(4.342759887797,3.834445696215));
#2434 = CARTESIAN_POINT('',(4.432415697581,3.818205220274));
#2435 = CARTESIAN_POINT('',(4.524227291973,3.806865308357));
#2436 = CARTESIAN_POINT('',(4.617494142082,3.801149061954));
#2437 = CARTESIAN_POINT('',(4.794911584415,3.800993707446));
#2438 = CARTESIAN_POINT('',(4.879032830034,3.805496863344));
#2439 = CARTESIAN_POINT('',(4.962126506097,3.814584490084));
#2440 = CARTESIAN_POINT('',(5.043598246028,3.827789712279));
#2441 = CARTESIAN_POINT('',(5.123125352862,3.844444791346));
#2442 = CARTESIAN_POINT('',(5.200574459246,3.863905617768));
#2443 = CARTESIAN_POINT('',(5.383479352415,3.916565783258));
#2444 = CARTESIAN_POINT('',(5.486874951827,3.952101231189));
#2445 = CARTESIAN_POINT('',(5.586983440329,3.990830190004));
#2446 = CARTESIAN_POINT('',(5.6844572548,4.031722113269));
#2447 = CARTESIAN_POINT('',(5.779900596866,4.074039519296));
#2448 = CARTESIAN_POINT('',(5.873968089714,4.117277867406));
#2449 = CARTESIAN_POINT('',(5.994627573587,4.173859679094));
#2450 = CARTESIAN_POINT('',(6.021745881044,4.186650210445));
#2451 = CARTESIAN_POINT('',(6.048816349145,4.199475045229));
#2452 = CARTESIAN_POINT('',(6.07585273269,4.212324153737));
#2453 = CARTESIAN_POINT('',(6.102868466557,4.225187866952));
#2454 = CARTESIAN_POINT('',(6.129876665694,4.238056876543));
#2455 = CARTESIAN_POINT('',(6.174906416901,4.259502626062));
#2456 = CARTESIAN_POINT('',(6.192925050399,4.268081347963));
#2457 = CARTESIAN_POINT('',(6.210949831499,4.276655715212));
#2458 = CARTESIAN_POINT('',(6.228984530587,4.285223051262));
#2459 = CARTESIAN_POINT('',(6.247032882551,4.293780688371));
#2460 = CARTESIAN_POINT('',(6.265098586783,4.302325967611));
#2461 = CARTESIAN_POINT('',(6.28318530718,4.310856238862));
#2462 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2463 = ORIENTED_EDGE('',*,*,#2177,.T.);
#2464 = ADVANCED_FACE('',(#2465),#1754,.F.);
#2465 = FACE_BOUND('',#2466,.F.);
#2466 = EDGE_LOOP('',(#2467,#2468,#2489,#2490));
#2467 = ORIENTED_EDGE('',*,*,#2200,.F.);
#2468 = ORIENTED_EDGE('',*,*,#2469,.F.);
#2469 = EDGE_CURVE('',#1715,#2178,#2470,.T.);
#2470 = SEAM_CURVE('',#2471,(#2475,#2482),.PCURVE_S1.);
#2471 = LINE('',#2472,#2473);
#2472 = CARTESIAN_POINT('',(73.592494,111.800042,12.973447));
#2473 = VECTOR('',#2474,1.);
#2474 = DIRECTION('',(0.,0.,1.));
#2475 = PCURVE('',#1754,#2476);
#2476 = DEFINITIONAL_REPRESENTATION('',(#2477),#2481);
#2477 = LINE('',#2478,#2479);
#2478 = CARTESIAN_POINT('',(6.28318530718,-0.));
#2479 = VECTOR('',#2480,1.);
#2480 = DIRECTION('',(0.,1.));
#2481 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2482 = PCURVE('',#1754,#2483);
#2483 = DEFINITIONAL_REPRESENTATION('',(#2484),#2488);
#2484 = LINE('',#2485,#2486);
#2485 = CARTESIAN_POINT('',(0.,-0.));
#2486 = VECTOR('',#2487,1.);
#2487 = DIRECTION('',(0.,1.));
#2488 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2489 = ORIENTED_EDGE('',*,*,#1739,.T.);
#2490 = ORIENTED_EDGE('',*,*,#2469,.T.);
#2491 = ( GEOMETRIC_REPRESENTATION_CONTEXT(3) 
GLOBAL_UNCERTAINTY_ASSIGNED_CONTEXT((#2495)) 
GLOBAL_UNIT_ASSIGNED_CONTEXT((#2492,#2493,#2494)) REPRESENTATION_CONTEXT
('Context #1','3D Context with UNIT and UNCERTAINTY') );
#2492 = ( LENGTH_UNIT() NAMED_UNIT(*) SI_UNIT(.MILLI.,.METRE.) );
#2493 = ( NAMED_UNIT(*) PLANE_ANGLE_UNIT() SI_UNIT($,.RADIAN.) );
#2494 = ( NAMED_UNIT(*) SI_UNIT($,.STERADIAN.) SOLID_ANGLE_UNIT() );
#2495 = UNCERTAINTY_MEASURE_WITH_UNIT(LENGTH_MEASURE(1.E-07),#2492,
  'distance_accuracy_value','confusion accuracy');
#2496 = CONTEXT_DEPENDENT_SHAPE_REPRESENTATION(#2497,#2499);
#2497 = ( REPRESENTATION_RELATIONSHIP('','',#36,#10) 
REPRESENTATION_RELATIONSHIP_WITH_TRANSFORMATION(#2498) 
SHAPE_REPRESENTATION_RELATIONSHIP() );
#2498 = ITEM_DEFINED_TRANSFORMATION('','',#11,#15);
#2499 = PRODUCT_DEFINITION_SHAPE('Placement','Placement of an item',
  #2500);
#2500 = NEXT_ASSEMBLY_USAGE_OCCURRENCE('145','=>[0:1:1:2]','',#5,#31,$);
#2501 = PRODUCT_RELATED_PRODUCT_CATEGORY('part',$,(#33));
#2502 = SHAPE_DEFINITION_REPRESENTATION(#2503,#2509);
#2503 = PRODUCT_DEFINITION_SHAPE('','',#2504);
#2504 = PRODUCT_DEFINITION('design','',#2505,#2508);
#2505 = PRODUCT_DEFINITION_FORMATION('','',#2506);
#2506 = PRODUCT('Group_3_Part3_1','Group_3_Part3_1','',(#2507));
#2507 = PRODUCT_CONTEXT('',#2,'mechanical');
#2508 = PRODUCT_DEFINITION_CONTEXT('part definition',#2,'design');
#2509 = ADVANCED_BREP_SHAPE_REPRESENTATION('',(#11,#2510),#4506);
#2510 = MANIFOLD_SOLID_BREP('',#2511);
#2511 = CLOSED_SHELL('',(#2512,#2634,#2726,#2936,#3286,#3384,#3416,#3443
    ,#3470,#3503,#3621,#3731,#3913,#4015,#4114,#4218,#4244,#4294,#4344,
    #4376,#4434,#4466,#4472,#4500));
#2512 = ADVANCED_FACE('',(#2513),#2528,.T.);
#2513 = FACE_BOUND('',#2514,.T.);
#2514 = EDGE_LOOP('',(#2515,#2551,#2577,#2610));
#2515 = ORIENTED_EDGE('',*,*,#2516,.T.);
#2516 = EDGE_CURVE('',#2517,#2519,#2521,.T.);
#2517 = VERTEX_POINT('',#2518);
#2518 = CARTESIAN_POINT('',(69.164045809675,103.98147999999,26.453618));
#2519 = VERTEX_POINT('',#2520);
#2520 = CARTESIAN_POINT('',(77.470942190325,103.98147999999,26.453618));
#2521 = SURFACE_CURVE('',#2522,(#2527,#2539),.PCURVE_S1.);
#2522 = CIRCLE('',#2523,4.316451884327);
#2523 = AXIS2_PLACEMENT_3D('',#2524,#2525,#2526);
#2524 = CARTESIAN_POINT('',(73.317494,102.80648,26.453618));
#2525 = DIRECTION('',(0.,0.,1.));
#2526 = DIRECTION('',(-0.962236647513,0.272214316639,0.));
#2527 = PCURVE('',#2528,#2533);
#2528 = CYLINDRICAL_SURFACE('',#2529,4.316451884327);
#2529 = AXIS2_PLACEMENT_3D('',#2530,#2531,#2532);
#2530 = CARTESIAN_POINT('',(73.317494,102.80648,26.453618));
#2531 = DIRECTION('',(0.,0.,1.));
#2532 = DIRECTION('',(1.,0.,0.));
#2533 = DEFINITIONAL_REPRESENTATION('',(#2534),#2538);
#2534 = LINE('',#2535,#2536);
#2535 = CARTESIAN_POINT('',(2.865899150304,0.));
#2536 = VECTOR('',#2537,1.);
#2537 = DIRECTION('',(1.,0.));
#2538 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2539 = PCURVE('',#2540,#2545);
#2540 = PLANE('',#2541);
#2541 = AXIS2_PLACEMENT_3D('',#2542,#2543,#2544);
#2542 = CARTESIAN_POINT('',(73.317494,102.80648,26.453618));
#2543 = DIRECTION('',(0.,0.,1.));
#2544 = DIRECTION('',(1.,0.,0.));
#2545 = DEFINITIONAL_REPRESENTATION('',(#2546),#2550);
#2546 = CIRCLE('',#2547,4.316451884327);
#2547 = AXIS2_PLACEMENT_2D('',#2548,#2549);
#2548 = CARTESIAN_POINT('',(0.,0.));
#2549 = DIRECTION('',(-0.962236647513,0.272214316639));
#2550 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2551 = ORIENTED_EDGE('',*,*,#2552,.T.);
#2552 = EDGE_CURVE('',#2519,#2553,#2555,.T.);
#2553 = VERTEX_POINT('',#2554);
#2554 = CARTESIAN_POINT('',(77.470942190325,103.98148,37.5));
#2555 = SURFACE_CURVE('',#2556,(#2560,#2566),.PCURVE_S1.);
#2556 = LINE('',#2557,#2558);
#2557 = CARTESIAN_POINT('',(77.470942190325,103.98148,26.453618));
#2558 = VECTOR('',#2559,1.);
#2559 = DIRECTION('',(0.,0.,1.));
#2560 = PCURVE('',#2528,#2561);
#2561 = DEFINITIONAL_REPRESENTATION('',(#2562),#2565);
#2562 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#2563,#2564),.UNSPECIFIED.,.F.,
  .F.,(2,2),(0.,11.046382),.PIECEWISE_BEZIER_KNOTS.);
#2563 = CARTESIAN_POINT('',(6.558878810465,0.));
#2564 = CARTESIAN_POINT('',(6.558878810465,11.046382));
#2565 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2566 = PCURVE('',#2567,#2572);
#2567 = CYLINDRICAL_SURFACE('',#2568,4.316451884327);
#2568 = AXIS2_PLACEMENT_3D('',#2569,#2570,#2571);
#2569 = CARTESIAN_POINT('',(73.317494,105.15648,24.720607));
#2570 = DIRECTION('',(0.,0.,1.));
#2571 = DIRECTION('',(1.,0.,0.));
#2572 = DEFINITIONAL_REPRESENTATION('',(#2573),#2576);
#2573 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#2574,#2575),.UNSPECIFIED.,.F.,
  .F.,(2,2),(0.,11.046382),.PIECEWISE_BEZIER_KNOTS.);
#2574 = CARTESIAN_POINT('',(6.007491803894,1.733011));
#2575 = CARTESIAN_POINT('',(6.007491803894,12.779393));
#2576 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2577 = ORIENTED_EDGE('',*,*,#2578,.T.);
#2578 = EDGE_CURVE('',#2553,#2579,#2581,.T.);
#2579 = VERTEX_POINT('',#2580);
#2580 = CARTESIAN_POINT('',(69.164045809675,103.98148,37.5));
#2581 = SURFACE_CURVE('',#2582,(#2587,#2594),.PCURVE_S1.);
#2582 = CIRCLE('',#2583,4.316451884327);
#2583 = AXIS2_PLACEMENT_3D('',#2584,#2585,#2586);
#2584 = CARTESIAN_POINT('',(73.317494,102.80648,37.5));
#2585 = DIRECTION('',(0.,0.,-1.));
#2586 = DIRECTION('',(0.962236647513,0.27221431664,0.));
#2587 = PCURVE('',#2528,#2588);
#2588 = DEFINITIONAL_REPRESENTATION('',(#2589),#2593);
#2589 = LINE('',#2590,#2591);
#2590 = CARTESIAN_POINT('',(6.558878810465,11.046382));
#2591 = VECTOR('',#2592,1.);
#2592 = DIRECTION('',(-1.,-0.));
#2593 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2594 = PCURVE('',#2595,#2600);
#2595 = PLANE('',#2596);
#2596 = AXIS2_PLACEMENT_3D('',#2597,#2598,#2599);
#2597 = CARTESIAN_POINT('',(73.317494,102.80648,37.5));
#2598 = DIRECTION('',(0.,0.,1.));
#2599 = DIRECTION('',(1.,0.,0.));
#2600 = DEFINITIONAL_REPRESENTATION('',(#2601),#2609);
#2601 = ( BOUNDED_CURVE() B_SPLINE_CURVE(2,(#2602,#2603,#2604,#2605,
#2606,#2607,#2608),.UNSPECIFIED.,.T.,.F.) B_SPLINE_CURVE_WITH_KNOTS((1,2
    ,2,2,2,1),(-2.094395102393,0.,2.094395102393,4.188790204786,
6.28318530718,8.377580409573),.UNSPECIFIED.) CURVE() 
GEOMETRIC_REPRESENTATION_ITEM() RATIONAL_B_SPLINE_CURVE((1.,0.5,1.,0.5,
1.,0.5,1.)) REPRESENTATION_ITEM('') );
#2602 = CARTESIAN_POINT('',(4.153448190325,1.175));
#2603 = CARTESIAN_POINT('',(6.188607889218,-6.018983292248));
#2604 = CARTESIAN_POINT('',(-1.059144245716,-4.184491646124));
#2605 = CARTESIAN_POINT('',(-8.30689638065,-2.35));
#2606 = CARTESIAN_POINT('',(-3.094303944609,3.009491646124));
#2607 = CARTESIAN_POINT('',(2.118288491432,8.368983292248));
#2608 = CARTESIAN_POINT('',(4.153448190325,1.175));
#2609 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2610 = ORIENTED_EDGE('',*,*,#2611,.F.);
#2611 = EDGE_CURVE('',#2517,#2579,#2612,.T.);
#2612 = SURFACE_CURVE('',#2613,(#2617,#2623),.PCURVE_S1.);
#2613 = LINE('',#2614,#2615);
#2614 = CARTESIAN_POINT('',(69.164045809675,103.98148,26.453618));
#2615 = VECTOR('',#2616,1.);
#2616 = DIRECTION('',(0.,0.,1.));
#2617 = PCURVE('',#2528,#2618);
#2618 = DEFINITIONAL_REPRESENTATION('',(#2619),#2622);
#2619 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#2620,#2621),.UNSPECIFIED.,.F.,
  .F.,(2,2),(0.,11.046382),.PIECEWISE_BEZIER_KNOTS.);
#2620 = CARTESIAN_POINT('',(2.865899150304,0.));
#2621 = CARTESIAN_POINT('',(2.865899150304,11.046382));
#2622 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2623 = PCURVE('',#2624,#2629);
#2624 = CYLINDRICAL_SURFACE('',#2625,4.316451884327);
#2625 = AXIS2_PLACEMENT_3D('',#2626,#2627,#2628);
#2626 = CARTESIAN_POINT('',(73.317494,105.15648,24.720607));
#2627 = DIRECTION('',(0.,0.,1.));
#2628 = DIRECTION('',(1.,0.,0.));
#2629 = DEFINITIONAL_REPRESENTATION('',(#2630),#2633);
#2630 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#2631,#2632),.UNSPECIFIED.,.F.,
  .F.,(2,2),(0.,11.046382),.PIECEWISE_BEZIER_KNOTS.);
#2631 = CARTESIAN_POINT('',(3.417286156876,1.733011));
#2632 = CARTESIAN_POINT('',(3.417286156876,12.779393));
#2633 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2634 = ADVANCED_FACE('',(#2635,#2664,#2695),#2540,.F.);
#2635 = FACE_BOUND('',#2636,.F.);
#2636 = EDGE_LOOP('',(#2637,#2638));
#2637 = ORIENTED_EDGE('',*,*,#2516,.T.);
#2638 = ORIENTED_EDGE('',*,*,#2639,.T.);
#2639 = EDGE_CURVE('',#2519,#2517,#2640,.T.);
#2640 = SURFACE_CURVE('',#2641,(#2646,#2653),.PCURVE_S1.);
#2641 = CIRCLE('',#2642,4.316451884327);
#2642 = AXIS2_PLACEMENT_3D('',#2643,#2644,#2645);
#2643 = CARTESIAN_POINT('',(73.317494,102.80648,26.453618));
#2644 = DIRECTION('',(0.,0.,1.));
#2645 = DIRECTION('',(1.,0.,0.));
#2646 = PCURVE('',#2540,#2647);
#2647 = DEFINITIONAL_REPRESENTATION('',(#2648),#2652);
#2648 = CIRCLE('',#2649,4.316451884327);
#2649 = AXIS2_PLACEMENT_2D('',#2650,#2651);
#2650 = CARTESIAN_POINT('',(0.,0.));
#2651 = DIRECTION('',(1.,0.));
#2652 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2653 = PCURVE('',#2654,#2659);
#2654 = CONICAL_SURFACE('',#2655,0.275,0.523598775598);
#2655 = AXIS2_PLACEMENT_3D('',#2656,#2657,#2658);
#2656 = CARTESIAN_POINT('',(73.317494,102.80648,19.453618));
#2657 = DIRECTION('',(0.,0.,1.));
#2658 = DIRECTION('',(1.,0.,0.));
#2659 = DEFINITIONAL_REPRESENTATION('',(#2660),#2663);
#2660 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#2661,#2662),.UNSPECIFIED.,.F.,
  .F.,(2,2),(0.275693503286,2.865899150304),.PIECEWISE_BEZIER_KNOTS.);
#2661 = CARTESIAN_POINT('',(6.558878810465,7.));
#2662 = CARTESIAN_POINT('',(9.149084457484,7.));
#2663 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2664 = FACE_BOUND('',#2665,.F.);
#2665 = EDGE_LOOP('',(#2666));
#2666 = ORIENTED_EDGE('',*,*,#2667,.F.);
#2667 = EDGE_CURVE('',#2668,#2668,#2670,.T.);
#2668 = VERTEX_POINT('',#2669);
#2669 = CARTESIAN_POINT('',(73.592494,102.80648,26.453618));
#2670 = SURFACE_CURVE('',#2671,(#2676,#2683),.PCURVE_S1.);
#2671 = CIRCLE('',#2672,0.275);
#2672 = AXIS2_PLACEMENT_3D('',#2673,#2674,#2675);
#2673 = CARTESIAN_POINT('',(73.317494,102.80648,26.453618));
#2674 = DIRECTION('',(0.,0.,1.));
#2675 = DIRECTION('',(1.,0.,0.));
#2676 = PCURVE('',#2540,#2677);
#2677 = DEFINITIONAL_REPRESENTATION('',(#2678),#2682);
#2678 = CIRCLE('',#2679,0.275);
#2679 = AXIS2_PLACEMENT_2D('',#2680,#2681);
#2680 = CARTESIAN_POINT('',(0.,0.));
#2681 = DIRECTION('',(1.,0.));
#2682 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2683 = PCURVE('',#2684,#2689);
#2684 = CYLINDRICAL_SURFACE('',#2685,0.275);
#2685 = AXIS2_PLACEMENT_3D('',#2686,#2687,#2688);
#2686 = CARTESIAN_POINT('',(73.317494,102.80648,19.453618));
#2687 = DIRECTION('',(0.,0.,1.));
#2688 = DIRECTION('',(1.,0.,0.));
#2689 = DEFINITIONAL_REPRESENTATION('',(#2690),#2694);
#2690 = LINE('',#2691,#2692);
#2691 = CARTESIAN_POINT('',(0.,7.));
#2692 = VECTOR('',#2693,1.);
#2693 = DIRECTION('',(1.,0.));
#2694 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2695 = FACE_BOUND('',#2696,.F.);
#2696 = EDGE_LOOP('',(#2697));
#2697 = ORIENTED_EDGE('',*,*,#2698,.F.);
#2698 = EDGE_CURVE('',#2699,#2699,#2701,.T.);
#2699 = VERTEX_POINT('',#2700);
#2700 = CARTESIAN_POINT('',(73.592494,105.15648,26.453618));
#2701 = SURFACE_CURVE('',#2702,(#2707,#2714),.PCURVE_S1.);
#2702 = CIRCLE('',#2703,0.275);
#2703 = AXIS2_PLACEMENT_3D('',#2704,#2705,#2706);
#2704 = CARTESIAN_POINT('',(73.317494,105.15648,26.453618));
#2705 = DIRECTION('',(0.,0.,1.));
#2706 = DIRECTION('',(1.,0.,-0.));
#2707 = PCURVE('',#2540,#2708);
#2708 = DEFINITIONAL_REPRESENTATION('',(#2709),#2713);
#2709 = CIRCLE('',#2710,0.275);
#2710 = AXIS2_PLACEMENT_2D('',#2711,#2712);
#2711 = CARTESIAN_POINT('',(0.,2.35));
#2712 = DIRECTION('',(1.,0.));
#2713 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2714 = PCURVE('',#2715,#2720);
#2715 = CYLINDRICAL_SURFACE('',#2716,0.275);
#2716 = AXIS2_PLACEMENT_3D('',#2717,#2718,#2719);
#2717 = CARTESIAN_POINT('',(73.317494,105.15648,17.720607));
#2718 = DIRECTION('',(0.,0.,1.));
#2719 = DIRECTION('',(1.,0.,0.));
#2720 = DEFINITIONAL_REPRESENTATION('',(#2721),#2725);
#2721 = LINE('',#2722,#2723);
#2722 = CARTESIAN_POINT('',(0.,8.733011));
#2723 = VECTOR('',#2724,1.);
#2724 = DIRECTION('',(1.,0.));
#2725 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2726 = ADVANCED_FACE('',(#2727),#2567,.T.);
#2727 = FACE_BOUND('',#2728,.T.);
#2728 = EDGE_LOOP('',(#2729,#2854,#2883,#2909,#2935));
#2729 = ORIENTED_EDGE('',*,*,#2730,.T.);
#2730 = EDGE_CURVE('',#2519,#2731,#2733,.T.);
#2731 = VERTEX_POINT('',#2732);
#2732 = CARTESIAN_POINT('',(76.602741688399,102.35667563262,24.720607));
#2733 = SURFACE_CURVE('',#2734,(#2752,#2803),.PCURVE_S1.);
#2734 = B_SPLINE_CURVE_WITH_KNOTS('',5,(#2735,#2736,#2737,#2738,#2739,
    #2740,#2741,#2742,#2743,#2744,#2745,#2746,#2747,#2748,#2749,#2750,
    #2751),.UNSPECIFIED.,.F.,.F.,(6,4,4,3,6),(0.,0.462648611534,
    0.931085091526,1.,1.437526818379),.UNSPECIFIED.);
#2735 = CARTESIAN_POINT('',(77.470942190325,103.98147999999,26.453618));
#2736 = CARTESIAN_POINT('',(77.437697019316,103.86396332309,
    26.342802352708));
#2737 = CARTESIAN_POINT('',(77.400503405114,103.74835729655,
    26.231735237346));
#2738 = CARTESIAN_POINT('',(77.359492146541,103.63472620293,
    26.120458636407));
#2739 = CARTESIAN_POINT('',(77.314773866286,103.52312746097,
    26.009014196158));
#2740 = CARTESIAN_POINT('',(77.217499472447,103.30272571527,
    25.784476471911));
#2741 = CARTESIAN_POINT('',(77.164852402871,103.19397563591,
    25.671380489328));
#2742 = CARTESIAN_POINT('',(77.10859086802,103.08741388397,
    25.558198234083));
#2743 = CARTESIAN_POINT('',(77.048787143814,102.98308902791,
    25.444975552943));
#2744 = CARTESIAN_POINT('',(76.97617199405,102.86603368925,
    25.315107672072));
#2745 = CARTESIAN_POINT('',(76.966783436847,102.85107082074,
    25.29845244202));
#2746 = CARTESIAN_POINT('',(76.957319299934,102.83615748715,
    25.281797602953));
#2747 = CARTESIAN_POINT('',(76.887214756983,102.72692747291,
    25.159408636001));
#2748 = CARTESIAN_POINT('',(76.813041657728,102.62038841611,
    25.037059377071));
#2749 = CARTESIAN_POINT('',(76.745953025593,102.53039123546,
    24.931431613116));
#2750 = CARTESIAN_POINT('',(76.67586270714,102.44247471937,
    24.825926807176));
#2751 = CARTESIAN_POINT('',(76.602741688398,102.35667563262,24.720607));
#2752 = PCURVE('',#2567,#2753);
#2753 = DEFINITIONAL_REPRESENTATION('',(#2754),#2802);
#2754 = B_SPLINE_CURVE_WITH_KNOTS('',3,(#2755,#2756,#2757,#2758,#2759,
    #2760,#2761,#2762,#2763,#2764,#2765,#2766,#2767,#2768,#2769,#2770,
    #2771,#2772,#2773,#2774,#2775,#2776,#2777,#2778,#2779,#2780,#2781,
    #2782,#2783,#2784,#2785,#2786,#2787,#2788,#2789,#2790,#2791,#2792,
    #2793,#2794,#2795,#2796,#2797,#2798,#2799,#2800,#2801),
  .UNSPECIFIED.,.F.,.F.,(4,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1
    ,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,4),(0.,3.267106405407E-02,
    6.534212810815E-02,9.801319216222E-02,0.130684256216,0.16335532027,
    0.196026384324,0.228697448379,0.261368512433,0.294039576487,
    0.326710640541,0.359381704595,0.392052768649,0.424723832703,
    0.457394896757,0.490065960811,0.522737024865,0.555408088919,
    0.588079152973,0.620750217027,0.653421281081,0.686092345136,
    0.71876340919,0.751434473244,0.784105537298,0.816776601352,
    0.849447665406,0.88211872946,0.914789793514,0.947460857568,
    0.980131921622,1.012802985676,1.04547404973,1.078145113784,
    1.110816177839,1.143487241893,1.176158305947,1.208829370001,
    1.241500434055,1.274171498109,1.306842562163,1.339513626217,
    1.372184690271,1.404855754325,1.437526818379),
  .QUASI_UNIFORM_KNOTS.);
#2755 = CARTESIAN_POINT('',(6.007491803894,1.733011));
#2756 = CARTESIAN_POINT('',(6.004161748546,1.719968469651));
#2757 = CARTESIAN_POINT('',(5.997507519159,1.693875049091));
#2758 = CARTESIAN_POINT('',(5.987543301903,1.654710724125));
#2759 = CARTESIAN_POINT('',(5.977595693813,1.61552309089));
#2760 = CARTESIAN_POINT('',(5.967664179164,1.576313033862));
#2761 = CARTESIAN_POINT('',(5.957748242899,1.537081436616));
#2762 = CARTESIAN_POINT('',(5.947847370372,1.497829181697));
#2763 = CARTESIAN_POINT('',(5.937961046836,1.458557150652));
#2764 = CARTESIAN_POINT('',(5.928088757003,1.419266224024));
#2765 = CARTESIAN_POINT('',(5.918229984594,1.379957281358));
#2766 = CARTESIAN_POINT('',(5.908384211899,1.34063120117));
#2767 = CARTESIAN_POINT('',(5.898550919371,1.301288861069));
#2768 = CARTESIAN_POINT('',(5.888729585097,1.261931137305));
#2769 = CARTESIAN_POINT('',(5.878919684787,1.222558906444));
#2770 = CARTESIAN_POINT('',(5.869120689873,1.183173039132));
#2771 = CARTESIAN_POINT('',(5.85933207269,1.14377442336));
#2772 = CARTESIAN_POINT('',(5.84955330755,1.104363979804));
#2773 = CARTESIAN_POINT('',(5.839783853588,1.064942583507));
#2774 = CARTESIAN_POINT('',(5.830023170097,1.02551113196));
#2775 = CARTESIAN_POINT('',(5.820270711924,0.986070528334));
#2776 = CARTESIAN_POINT('',(5.810525929969,0.94662168646));
#2777 = CARTESIAN_POINT('',(5.800788270299,0.907165529984));
#2778 = CARTESIAN_POINT('',(5.79105717363,0.867702993081));
#2779 = CARTESIAN_POINT('',(5.781332074687,0.828235020751));
#2780 = CARTESIAN_POINT('',(5.7716124016,0.788762569234));
#2781 = CARTESIAN_POINT('',(5.761897575267,0.749286606365));
#2782 = CARTESIAN_POINT('',(5.752187008746,0.709808112058));
#2783 = CARTESIAN_POINT('',(5.742480106537,0.670328078313));
#2784 = CARTESIAN_POINT('',(5.732776264225,0.630847511026));
#2785 = CARTESIAN_POINT('',(5.723074866797,0.591367425046));
#2786 = CARTESIAN_POINT('',(5.713375296093,0.551888884061));
#2787 = CARTESIAN_POINT('',(5.703676922104,0.512412962368));
#2788 = CARTESIAN_POINT('',(5.693979108009,0.472940771603));
#2789 = CARTESIAN_POINT('',(5.684281193483,0.433473403574));
#2790 = CARTESIAN_POINT('',(5.674582510132,0.394011987869));
#2791 = CARTESIAN_POINT('',(5.664882377237,0.354557682138));
#2792 = CARTESIAN_POINT('',(5.655180101517,0.315111676224));
#2793 = CARTESIAN_POINT('',(5.645474975777,0.275675192583));
#2794 = CARTESIAN_POINT('',(5.635766277815,0.236249487713));
#2795 = CARTESIAN_POINT('',(5.62605326924,0.196835853273));
#2796 = CARTESIAN_POINT('',(5.616335194215,0.157435617404));
#2797 = CARTESIAN_POINT('',(5.606611278445,0.118050145555));
#2798 = CARTESIAN_POINT('',(5.596880727127,7.868084311842E-02));
#2799 = CARTESIAN_POINT('',(5.587142726658,3.932915129042E-02));
#2800 = CARTESIAN_POINT('',(5.580645197053,1.310742924761E-02));
#2801 = CARTESIAN_POINT('',(5.577394910616,3.765876499529E-13));
#2802 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2803 = PCURVE('',#2654,#2804);
#2804 = DEFINITIONAL_REPRESENTATION('',(#2805),#2853);
#2805 = B_SPLINE_CURVE_WITH_KNOTS('',3,(#2806,#2807,#2808,#2809,#2810,
    #2811,#2812,#2813,#2814,#2815,#2816,#2817,#2818,#2819,#2820,#2821,
    #2822,#2823,#2824,#2825,#2826,#2827,#2828,#2829,#2830,#2831,#2832,
    #2833,#2834,#2835,#2836,#2837,#2838,#2839,#2840,#2841,#2842,#2843,
    #2844,#2845,#2846,#2847,#2848,#2849,#2850,#2851,#2852),
  .UNSPECIFIED.,.F.,.F.,(4,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1
    ,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,4),(0.,3.267106405407E-02,
    6.534212810815E-02,9.801319216222E-02,0.130684256216,0.16335532027,
    0.196026384324,0.228697448379,0.261368512433,0.294039576487,
    0.326710640541,0.359381704595,0.392052768649,0.424723832703,
    0.457394896757,0.490065960811,0.522737024865,0.555408088919,
    0.588079152973,0.620750217027,0.653421281081,0.686092345136,
    0.71876340919,0.751434473244,0.784105537298,0.816776601352,
    0.849447665406,0.88211872946,0.914789793514,0.947460857568,
    0.980131921622,1.012802985676,1.04547404973,1.078145113784,
    1.110816177839,1.143487241893,1.176158305947,1.208829370001,
    1.241500434055,1.274171498109,1.306842562163,1.339513626217,
    1.372184690271,1.404855754325,1.437526818379),
  .QUASI_UNIFORM_KNOTS.);
#2806 = CARTESIAN_POINT('',(6.558878810465,7.));
#2807 = CARTESIAN_POINT('',(6.556042272276,6.986957469593));
#2808 = CARTESIAN_POINT('',(6.550361948488,6.960864047791));
#2809 = CARTESIAN_POINT('',(6.541818783982,6.921699719386));
#2810 = CARTESIAN_POINT('',(6.533252009642,6.882512083218));
#2811 = CARTESIAN_POINT('',(6.524660674897,6.843302024882));
#2812 = CARTESIAN_POINT('',(6.516043815296,6.804070428469));
#2813 = CARTESIAN_POINT('',(6.507400449888,6.764818176408));
#2814 = CARTESIAN_POINT('',(6.498729580791,6.725546149637));
#2815 = CARTESIAN_POINT('',(6.49003019214,6.686255227729));
#2816 = CARTESIAN_POINT('',(6.481301249151,6.646946289095));
#2817 = CARTESIAN_POINT('',(6.472541697117,6.607620211185));
#2818 = CARTESIAN_POINT('',(6.463750460405,6.568277870877));
#2819 = CARTESIAN_POINT('',(6.454926441288,6.528920144371));
#2820 = CARTESIAN_POINT('',(6.446068519236,6.489547909142));
#2821 = CARTESIAN_POINT('',(6.437175548322,6.450162038458));
#2822 = CARTESIAN_POINT('',(6.428246361491,6.410763423821));
#2823 = CARTESIAN_POINT('',(6.419279773686,6.371352980945));
#2824 = CARTESIAN_POINT('',(6.410274561465,6.331931584925));
#2825 = CARTESIAN_POINT('',(6.401229477789,6.292500132942));
#2826 = CARTESIAN_POINT('',(6.392143246771,6.253059528491));
#2827 = CARTESIAN_POINT('',(6.383014563466,6.213610685786));
#2828 = CARTESIAN_POINT('',(6.373842092237,6.174154528836));
#2829 = CARTESIAN_POINT('',(6.364624465413,6.134691992011));
#2830 = CARTESIAN_POINT('',(6.35536028178,6.095224020289));
#2831 = CARTESIAN_POINT('',(6.346048105025,6.055751569667));
#2832 = CARTESIAN_POINT('',(6.336686462089,6.016275607585));
#2833 = CARTESIAN_POINT('',(6.327273841473,5.976797113537));
#2834 = CARTESIAN_POINT('',(6.317808691346,5.937317079345));
#2835 = CARTESIAN_POINT('',(6.308289418002,5.897836511022));
#2836 = CARTESIAN_POINT('',(6.298714382613,5.858356425085));
#2837 = CARTESIAN_POINT('',(6.289081908617,5.818877883861));
#2838 = CARTESIAN_POINT('',(6.279390270409,5.779401962506));
#2839 = CARTESIAN_POINT('',(6.269637697565,5.739929773796));
#2840 = CARTESIAN_POINT('',(6.259822357707,5.700462406616));
#2841 = CARTESIAN_POINT('',(6.249942368519,5.661000990367));
#2842 = CARTESIAN_POINT('',(6.239995792521,5.621546683106));
#2843 = CARTESIAN_POINT('',(6.229980635202,5.582100675533));
#2844 = CARTESIAN_POINT('',(6.219894842047,5.542664190881));
#2845 = CARTESIAN_POINT('',(6.20973629564,5.503238486083));
#2846 = CARTESIAN_POINT('',(6.199502812584,5.46382485274));
#2847 = CARTESIAN_POINT('',(6.189192139997,5.424424618459));
#2848 = CARTESIAN_POINT('',(6.178801952815,5.385039147832));
#2849 = CARTESIAN_POINT('',(6.168329847132,5.345669845493));
#2850 = CARTESIAN_POINT('',(6.15777334744,5.30631815226));
#2851 = CARTESIAN_POINT('',(6.150677689805,5.280096429422));
#2852 = CARTESIAN_POINT('',(6.147114936652,5.266989));
#2853 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2854 = ORIENTED_EDGE('',*,*,#2855,.T.);
#2855 = EDGE_CURVE('',#2731,#2856,#2858,.T.);
#2856 = VERTEX_POINT('',#2857);
#2857 = CARTESIAN_POINT('',(76.939208915509,107.5048705,24.720607));
#2858 = SURFACE_CURVE('',#2859,(#2864,#2871),.PCURVE_S1.);
#2859 = CIRCLE('',#2860,4.316451884327);
#2860 = AXIS2_PLACEMENT_3D('',#2861,#2862,#2863);
#2861 = CARTESIAN_POINT('',(73.317494,105.15648,24.720607));
#2862 = DIRECTION('',(-0.,0.,1.));
#2863 = DIRECTION('',(0.761099110204,-0.648635602204,0.));
#2864 = PCURVE('',#2567,#2865);
#2865 = DEFINITIONAL_REPRESENTATION('',(#2866),#2870);
#2866 = LINE('',#2867,#2868);
#2867 = CARTESIAN_POINT('',(5.577394910616,0.));
#2868 = VECTOR('',#2869,1.);
#2869 = DIRECTION('',(1.,0.));
#2870 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2871 = PCURVE('',#2872,#2877);
#2872 = PLANE('',#2873);
#2873 = AXIS2_PLACEMENT_3D('',#2874,#2875,#2876);
#2874 = CARTESIAN_POINT('',(73.317494,105.15648,24.720607));
#2875 = DIRECTION('',(0.,0.,1.));
#2876 = DIRECTION('',(1.,0.,0.));
#2877 = DEFINITIONAL_REPRESENTATION('',(#2878),#2882);
#2878 = CIRCLE('',#2879,4.316451884327);
#2879 = AXIS2_PLACEMENT_2D('',#2880,#2881);
#2880 = CARTESIAN_POINT('',(0.,0.));
#2881 = DIRECTION('',(0.761099110204,-0.648635602204));
#2882 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2883 = ORIENTED_EDGE('',*,*,#2884,.T.);
#2884 = EDGE_CURVE('',#2856,#2885,#2887,.T.);
#2885 = VERTEX_POINT('',#2886);
#2886 = CARTESIAN_POINT('',(76.939208915509,107.5048705,37.5));
#2887 = SURFACE_CURVE('',#2888,(#2892,#2898),.PCURVE_S1.);
#2888 = LINE('',#2889,#2890);
#2889 = CARTESIAN_POINT('',(76.939208915509,107.5048705,24.720607));
#2890 = VECTOR('',#2891,1.);
#2891 = DIRECTION('',(0.,0.,1.));
#2892 = PCURVE('',#2567,#2893);
#2893 = DEFINITIONAL_REPRESENTATION('',(#2894),#2897);
#2894 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#2895,#2896),.UNSPECIFIED.,.F.,
  .F.,(2,2),(0.,12.779393),.PIECEWISE_BEZIER_KNOTS.);
#2895 = CARTESIAN_POINT('',(6.858448638668,0.));
#2896 = CARTESIAN_POINT('',(6.858448638668,12.779393));
#2897 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2898 = PCURVE('',#2899,#2904);
#2899 = CYLINDRICAL_SURFACE('',#2900,4.316451884327);
#2900 = AXIS2_PLACEMENT_3D('',#2901,#2902,#2903);
#2901 = CARTESIAN_POINT('',(73.317494,109.853261,21.355218));
#2902 = DIRECTION('',(0.,0.,1.));
#2903 = DIRECTION('',(1.,0.,0.));
#2904 = DEFINITIONAL_REPRESENTATION('',(#2905),#2908);
#2905 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#2906,#2907),.UNSPECIFIED.,.F.,
  .F.,(2,2),(-2.399999999625E-06,12.7793954),.PIECEWISE_BEZIER_KNOTS.);
#2906 = CARTESIAN_POINT('',(5.707921975691,3.3653866));
#2907 = CARTESIAN_POINT('',(5.707921975691,16.1447844));
#2908 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2909 = ORIENTED_EDGE('',*,*,#2910,.T.);
#2910 = EDGE_CURVE('',#2885,#2553,#2911,.T.);
#2911 = SURFACE_CURVE('',#2912,(#2917,#2924),.PCURVE_S1.);
#2912 = CIRCLE('',#2913,4.316451884327);
#2913 = AXIS2_PLACEMENT_3D('',#2914,#2915,#2916);
#2914 = CARTESIAN_POINT('',(73.317494,105.15648,37.5));
#2915 = DIRECTION('',(0.,0.,-1.));
#2916 = DIRECTION('',(0.839049064501,0.544055757583,0.));
#2917 = PCURVE('',#2567,#2918);
#2918 = DEFINITIONAL_REPRESENTATION('',(#2919),#2923);
#2919 = LINE('',#2920,#2921);
#2920 = CARTESIAN_POINT('',(6.858448638668,12.779393));
#2921 = VECTOR('',#2922,1.);
#2922 = DIRECTION('',(-1.,-0.));
#2923 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2924 = PCURVE('',#2595,#2925);
#2925 = DEFINITIONAL_REPRESENTATION('',(#2926),#2934);
#2926 = ( BOUNDED_CURVE() B_SPLINE_CURVE(2,(#2927,#2928,#2929,#2930,
#2931,#2932,#2933),.UNSPECIFIED.,.T.,.F.) B_SPLINE_CURVE_WITH_KNOTS((1,2
    ,2,2,2,1),(-2.094395102393,0.,2.094395102393,4.188790204786,
6.28318530718,8.377580409573),.UNSPECIFIED.) CURVE() 
GEOMETRIC_REPRESENTATION_ITEM() RATIONAL_B_SPLINE_CURVE((1.,0.5,1.,0.5,
1.,0.5,1.)) REPRESENTATION_ITEM('') );
#2927 = CARTESIAN_POINT('',(3.62171491551,4.6983905));
#2928 = CARTESIAN_POINT('',(7.689246577521,-1.574603744193));
#2929 = CARTESIAN_POINT('',(0.222908373251,-1.960692372096));
#2930 = CARTESIAN_POINT('',(-7.243429831019,-2.346781));
#2931 = CARTESIAN_POINT('',(-3.844623288761,4.312301872096));
#2932 = CARTESIAN_POINT('',(-0.445816746502,10.971384744192));
#2933 = CARTESIAN_POINT('',(3.62171491551,4.6983905));
#2934 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2935 = ORIENTED_EDGE('',*,*,#2552,.F.);
#2936 = ADVANCED_FACE('',(#2937,#3172,#3198,#3224,#3255),#2595,.T.);
#2937 = FACE_BOUND('',#2938,.T.);
#2938 = EDGE_LOOP('',(#2939,#2940,#2941,#2969,#3002,#3030,#3057,#3085,
    #3118,#3151));
#2939 = ORIENTED_EDGE('',*,*,#2578,.F.);
#2940 = ORIENTED_EDGE('',*,*,#2910,.F.);
#2941 = ORIENTED_EDGE('',*,*,#2942,.F.);
#2942 = EDGE_CURVE('',#2943,#2885,#2945,.T.);
#2943 = VERTEX_POINT('',#2944);
#2944 = CARTESIAN_POINT('',(77.522760674567,110.8266515,37.5));
#2945 = SURFACE_CURVE('',#2946,(#2951,#2962),.PCURVE_S1.);
#2946 = CIRCLE('',#2947,4.316451884327);
#2947 = AXIS2_PLACEMENT_3D('',#2948,#2949,#2950);
#2948 = CARTESIAN_POINT('',(73.317494,109.853261,37.5));
#2949 = DIRECTION('',(0.,0.,-1.));
#2950 = DIRECTION('',(0.822792726357,0.568341560555,0.));
#2951 = PCURVE('',#2595,#2952);
#2952 = DEFINITIONAL_REPRESENTATION('',(#2953),#2961);
#2953 = ( BOUNDED_CURVE() B_SPLINE_CURVE(2,(#2954,#2955,#2956,#2957,
#2958,#2959,#2960),.UNSPECIFIED.,.T.,.F.) B_SPLINE_CURVE_WITH_KNOTS((1,2
    ,2,2,2,1),(-2.094395102393,0.,2.094395102393,4.188790204786,
6.28318530718,8.377580409573),.UNSPECIFIED.) CURVE() 
GEOMETRIC_REPRESENTATION_ITEM() RATIONAL_B_SPLINE_CURVE((1.,0.5,1.,0.5,
1.,0.5,1.)) REPRESENTATION_ITEM('') );
#2954 = CARTESIAN_POINT('',(3.551545214094,9.5));
#2955 = CARTESIAN_POINT('',(7.800645164187,3.348543243811));
#2956 = CARTESIAN_POINT('',(0.348777368,2.744443121906));
#2957 = CARTESIAN_POINT('',(-7.103090428188,2.140343));
#2958 = CARTESIAN_POINT('',(-3.900322582094,8.895899878094));
#2959 = CARTESIAN_POINT('',(-0.697554736,15.651456756189));
#2960 = CARTESIAN_POINT('',(3.551545214094,9.5));
#2961 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2962 = PCURVE('',#2899,#2963);
#2963 = DEFINITIONAL_REPRESENTATION('',(#2964),#2968);
#2964 = LINE('',#2965,#2966);
#2965 = CARTESIAN_POINT('',(6.887674133512,16.144782));
#2966 = VECTOR('',#2967,1.);
#2967 = DIRECTION('',(-1.,-0.));
#2968 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2969 = ORIENTED_EDGE('',*,*,#2970,.F.);
#2970 = EDGE_CURVE('',#2971,#2943,#2973,.T.);
#2971 = VERTEX_POINT('',#2972);
#2972 = CARTESIAN_POINT('',(77.604133408892,112.30648,37.5));
#2973 = SURFACE_CURVE('',#2974,(#2979,#2990),.PCURVE_S1.);
#2974 = CIRCLE('',#2975,4.316451884327);
#2975 = AXIS2_PLACEMENT_3D('',#2976,#2977,#2978);
#2976 = CARTESIAN_POINT('',(73.317494,111.800042,37.5));
#2977 = DIRECTION('',(0.,0.,-1.));
#2978 = DIRECTION('',(0.993093291381,0.117327382205,0.));
#2979 = PCURVE('',#2595,#2980);
#2980 = DEFINITIONAL_REPRESENTATION('',(#2981),#2989);
#2981 = ( BOUNDED_CURVE() B_SPLINE_CURVE(2,(#2982,#2983,#2984,#2985,
#2986,#2987,#2988),.UNSPECIFIED.,.T.,.F.) B_SPLINE_CURVE_WITH_KNOTS((1,2
    ,2,2,2,1),(-2.094395102393,0.,2.094395102393,4.188790204786,
6.28318530718,8.377580409573),.UNSPECIFIED.) CURVE() 
GEOMETRIC_REPRESENTATION_ITEM() RATIONAL_B_SPLINE_CURVE((1.,0.5,1.,0.5,
1.,0.5,1.)) REPRESENTATION_ITEM('') );
#2982 = CARTESIAN_POINT('',(4.286639408892,9.5));
#2983 = CARTESIAN_POINT('',(5.163815755776,2.075322750071));
#2984 = CARTESIAN_POINT('',(-1.704731531004,5.028004375036));
#2985 = CARTESIAN_POINT('',(-8.573278817785,7.980686));
#2986 = CARTESIAN_POINT('',(-2.581907877888,12.452681624964));
#2987 = CARTESIAN_POINT('',(3.409463062009,16.924677249929));
#2988 = CARTESIAN_POINT('',(4.286639408892,9.5));
#2989 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2990 = PCURVE('',#2991,#2996);
#2991 = CYLINDRICAL_SURFACE('',#2992,4.316451884327);
#2992 = AXIS2_PLACEMENT_3D('',#2993,#2994,#2995);
#2993 = CARTESIAN_POINT('',(73.317494,111.800042,19.973447));
#2994 = DIRECTION('',(0.,0.,1.));
#2995 = DIRECTION('',(1.,0.,0.));
#2996 = DEFINITIONAL_REPRESENTATION('',(#2997),#3001);
#2997 = LINE('',#2998,#2999);
#2998 = CARTESIAN_POINT('',(6.40078355319,17.526553));
#2999 = VECTOR('',#3000,1.);
#3000 = DIRECTION('',(-1.,-0.));
#3001 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3002 = ORIENTED_EDGE('',*,*,#3003,.T.);
#3003 = EDGE_CURVE('',#2971,#3004,#3006,.T.);
#3004 = VERTEX_POINT('',#3005);
#3005 = CARTESIAN_POINT('',(72.917494,112.30648,37.5));
#3006 = SURFACE_CURVE('',#3007,(#3011,#3018),.PCURVE_S1.);
#3007 = LINE('',#3008,#3009);
#3008 = CARTESIAN_POINT('',(77.604133408892,112.30648,37.5));
#3009 = VECTOR('',#3010,1.);
#3010 = DIRECTION('',(-1.,0.,0.));
#3011 = PCURVE('',#2595,#3012);
#3012 = DEFINITIONAL_REPRESENTATION('',(#3013),#3017);
#3013 = LINE('',#3014,#3015);
#3014 = CARTESIAN_POINT('',(4.286639408892,9.5));
#3015 = VECTOR('',#3016,1.);
#3016 = DIRECTION('',(-1.,0.));
#3017 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3018 = PCURVE('',#3019,#3024);
#3019 = PLANE('',#3020);
#3020 = AXIS2_PLACEMENT_3D('',#3021,#3022,#3023);
#3021 = CARTESIAN_POINT('',(72.917494,112.30648,47.5));
#3022 = DIRECTION('',(0.,-1.,0.));
#3023 = DIRECTION('',(1.,0.,0.));
#3024 = DEFINITIONAL_REPRESENTATION('',(#3025),#3029);
#3025 = LINE('',#3026,#3027);
#3026 = CARTESIAN_POINT('',(4.686639408892,-10.));
#3027 = VECTOR('',#3028,1.);
#3028 = DIRECTION('',(-1.,0.));
#3029 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3030 = ORIENTED_EDGE('',*,*,#3031,.F.);
#3031 = EDGE_CURVE('',#3032,#3004,#3034,.T.);
#3032 = VERTEX_POINT('',#3033);
#3033 = CARTESIAN_POINT('',(72.917494,112.906445,37.5));
#3034 = SURFACE_CURVE('',#3035,(#3039,#3046),.PCURVE_S1.);
#3035 = LINE('',#3036,#3037);
#3036 = CARTESIAN_POINT('',(72.917494,112.3532435,37.5));
#3037 = VECTOR('',#3038,1.);
#3038 = DIRECTION('',(0.,-1.,0.));
#3039 = PCURVE('',#2595,#3040);
#3040 = DEFINITIONAL_REPRESENTATION('',(#3041),#3045);
#3041 = LINE('',#3042,#3043);
#3042 = CARTESIAN_POINT('',(-0.4,9.5467635));
#3043 = VECTOR('',#3044,1.);
#3044 = DIRECTION('',(0.,-1.));
#3045 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3046 = PCURVE('',#3047,#3052);
#3047 = PLANE('',#3048);
#3048 = AXIS2_PLACEMENT_3D('',#3049,#3050,#3051);
#3049 = CARTESIAN_POINT('',(72.917494,112.906445,47.5));
#3050 = DIRECTION('',(-1.,-0.,-0.));
#3051 = DIRECTION('',(0.,-1.,0.));
#3052 = DEFINITIONAL_REPRESENTATION('',(#3053),#3056);
#3053 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#3054,#3055),.UNSPECIFIED.,.F.,
  .F.,(2,2),(-0.5532015,4.676350000001E-02),.PIECEWISE_BEZIER_KNOTS.);
#3054 = CARTESIAN_POINT('',(-1.42108547152E-14,-10.));
#3055 = CARTESIAN_POINT('',(0.599965,-10.));
#3056 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3057 = ORIENTED_EDGE('',*,*,#3058,.T.);
#3058 = EDGE_CURVE('',#3032,#3059,#3061,.T.);
#3059 = VERTEX_POINT('',#3060);
#3060 = CARTESIAN_POINT('',(69.145248888396,112.906445,37.5));
#3061 = SURFACE_CURVE('',#3062,(#3066,#3073),.PCURVE_S1.);
#3062 = LINE('',#3063,#3064);
#3063 = CARTESIAN_POINT('',(72.917494,112.906445,37.5));
#3064 = VECTOR('',#3065,1.);
#3065 = DIRECTION('',(-1.,0.,0.));
#3066 = PCURVE('',#2595,#3067);
#3067 = DEFINITIONAL_REPRESENTATION('',(#3068),#3072);
#3068 = LINE('',#3069,#3070);
#3069 = CARTESIAN_POINT('',(-0.4,10.099965));
#3070 = VECTOR('',#3071,1.);
#3071 = DIRECTION('',(-1.,0.));
#3072 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3073 = PCURVE('',#3074,#3079);
#3074 = PLANE('',#3075);
#3075 = AXIS2_PLACEMENT_3D('',#3076,#3077,#3078);
#3076 = CARTESIAN_POINT('',(51.710833,112.906445,47.5));
#3077 = DIRECTION('',(0.,-1.,0.));
#3078 = DIRECTION('',(1.,0.,0.));
#3079 = DEFINITIONAL_REPRESENTATION('',(#3080),#3084);
#3080 = LINE('',#3081,#3082);
#3081 = CARTESIAN_POINT('',(21.206661,-10.));
#3082 = VECTOR('',#3083,1.);
#3083 = DIRECTION('',(-1.,0.));
#3084 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3085 = ORIENTED_EDGE('',*,*,#3086,.F.);
#3086 = EDGE_CURVE('',#3087,#3059,#3089,.T.);
#3087 = VERTEX_POINT('',#3088);
#3088 = CARTESIAN_POINT('',(69.112227325433,110.8266515,37.5));
#3089 = SURFACE_CURVE('',#3090,(#3095,#3106),.PCURVE_S1.);
#3090 = CIRCLE('',#3091,4.316451884327);
#3091 = AXIS2_PLACEMENT_3D('',#3092,#3093,#3094);
#3092 = CARTESIAN_POINT('',(73.317494,111.800042,37.5));
#3093 = DIRECTION('',(0.,0.,-1.));
#3094 = DIRECTION('',(0.993093291381,0.117327382205,0.));
#3095 = PCURVE('',#2595,#3096);
#3096 = DEFINITIONAL_REPRESENTATION('',(#3097),#3105);
#3097 = ( BOUNDED_CURVE() B_SPLINE_CURVE(2,(#3098,#3099,#3100,#3101,
#3102,#3103,#3104),.UNSPECIFIED.,.T.,.F.) B_SPLINE_CURVE_WITH_KNOTS((1,2
    ,2,2,2,1),(-2.094395102393,0.,2.094395102393,4.188790204786,
6.28318530718,8.377580409573),.UNSPECIFIED.) CURVE() 
GEOMETRIC_REPRESENTATION_ITEM() RATIONAL_B_SPLINE_CURVE((1.,0.5,1.,0.5,
1.,0.5,1.)) REPRESENTATION_ITEM('') );
#3098 = CARTESIAN_POINT('',(4.286639408892,9.5));
#3099 = CARTESIAN_POINT('',(5.163815755776,2.075322750071));
#3100 = CARTESIAN_POINT('',(-1.704731531004,5.028004375036));
#3101 = CARTESIAN_POINT('',(-8.573278817785,7.980686));
#3102 = CARTESIAN_POINT('',(-2.581907877888,12.452681624964));
#3103 = CARTESIAN_POINT('',(3.409463062009,16.924677249929));
#3104 = CARTESIAN_POINT('',(4.286639408892,9.5));
#3105 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3106 = PCURVE('',#3107,#3112);
#3107 = CYLINDRICAL_SURFACE('',#3108,4.316451884327);
#3108 = AXIS2_PLACEMENT_3D('',#3109,#3110,#3111);
#3109 = CARTESIAN_POINT('',(73.317494,111.800042,19.973447));
#3110 = DIRECTION('',(0.,0.,1.));
#3111 = DIRECTION('',(1.,0.,0.));
#3112 = DEFINITIONAL_REPRESENTATION('',(#3113),#3117);
#3113 = LINE('',#3114,#3115);
#3114 = CARTESIAN_POINT('',(6.40078355319,17.526553));
#3115 = VECTOR('',#3116,1.);
#3116 = DIRECTION('',(-1.,-0.));
#3117 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3118 = ORIENTED_EDGE('',*,*,#3119,.F.);
#3119 = EDGE_CURVE('',#3120,#3087,#3122,.T.);
#3120 = VERTEX_POINT('',#3121);
#3121 = CARTESIAN_POINT('',(69.695779084491,107.5048705,37.5));
#3122 = SURFACE_CURVE('',#3123,(#3128,#3139),.PCURVE_S1.);
#3123 = CIRCLE('',#3124,4.316451884327);
#3124 = AXIS2_PLACEMENT_3D('',#3125,#3126,#3127);
#3125 = CARTESIAN_POINT('',(73.317494,109.853261,37.5));
#3126 = DIRECTION('',(0.,0.,-1.));
#3127 = DIRECTION('',(0.822792726357,0.568341560555,0.));
#3128 = PCURVE('',#2595,#3129);
#3129 = DEFINITIONAL_REPRESENTATION('',(#3130),#3138);
#3130 = ( BOUNDED_CURVE() B_SPLINE_CURVE(2,(#3131,#3132,#3133,#3134,
#3135,#3136,#3137),.UNSPECIFIED.,.T.,.F.) B_SPLINE_CURVE_WITH_KNOTS((1,2
    ,2,2,2,1),(-2.094395102393,0.,2.094395102393,4.188790204786,
6.28318530718,8.377580409573),.UNSPECIFIED.) CURVE() 
GEOMETRIC_REPRESENTATION_ITEM() RATIONAL_B_SPLINE_CURVE((1.,0.5,1.,0.5,
1.,0.5,1.)) REPRESENTATION_ITEM('') );
#3131 = CARTESIAN_POINT('',(3.551545214094,9.5));
#3132 = CARTESIAN_POINT('',(7.800645164187,3.348543243811));
#3133 = CARTESIAN_POINT('',(0.348777368,2.744443121906));
#3134 = CARTESIAN_POINT('',(-7.103090428188,2.140343));
#3135 = CARTESIAN_POINT('',(-3.900322582094,8.895899878094));
#3136 = CARTESIAN_POINT('',(-0.697554736,15.651456756189));
#3137 = CARTESIAN_POINT('',(3.551545214094,9.5));
#3138 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3139 = PCURVE('',#3140,#3145);
#3140 = CYLINDRICAL_SURFACE('',#3141,4.316451884327);
#3141 = AXIS2_PLACEMENT_3D('',#3142,#3143,#3144);
#3142 = CARTESIAN_POINT('',(73.317494,109.853261,21.355218));
#3143 = DIRECTION('',(0.,0.,1.));
#3144 = DIRECTION('',(1.,0.,0.));
#3145 = DEFINITIONAL_REPRESENTATION('',(#3146),#3150);
#3146 = LINE('',#3147,#3148);
#3147 = CARTESIAN_POINT('',(6.887674133512,16.144782));
#3148 = VECTOR('',#3149,1.);
#3149 = DIRECTION('',(-1.,-0.));
#3150 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3151 = ORIENTED_EDGE('',*,*,#3152,.T.);
#3152 = EDGE_CURVE('',#3120,#2579,#3153,.T.);
#3153 = SURFACE_CURVE('',#3154,(#3159,#3166),.PCURVE_S1.);
#3154 = CIRCLE('',#3155,4.316451884327);
#3155 = AXIS2_PLACEMENT_3D('',#3156,#3157,#3158);
#3156 = CARTESIAN_POINT('',(73.317494,105.15648,37.5));
#3157 = DIRECTION('',(0.,0.,1.));
#3158 = DIRECTION('',(1.,0.,0.));
#3159 = PCURVE('',#2595,#3160);
#3160 = DEFINITIONAL_REPRESENTATION('',(#3161),#3165);
#3161 = CIRCLE('',#3162,4.316451884327);
#3162 = AXIS2_PLACEMENT_2D('',#3163,#3164);
#3163 = CARTESIAN_POINT('',(0.,2.35));
#3164 = DIRECTION('',(1.,0.));
#3165 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3166 = PCURVE('',#2624,#3167);
#3167 = DEFINITIONAL_REPRESENTATION('',(#3168),#3171);
#3168 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#3169,#3170),.UNSPECIFIED.,.F.,
  .F.,(2,2),(2.566329322102,3.417286156876),.PIECEWISE_BEZIER_KNOTS.);
#3169 = CARTESIAN_POINT('',(2.566329322102,12.779393));
#3170 = CARTESIAN_POINT('',(3.417286156876,12.779393));
#3171 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3172 = FACE_BOUND('',#3173,.T.);
#3173 = EDGE_LOOP('',(#3174));
#3174 = ORIENTED_EDGE('',*,*,#3175,.F.);
#3175 = EDGE_CURVE('',#3176,#3176,#3178,.T.);
#3176 = VERTEX_POINT('',#3177);
#3177 = CARTESIAN_POINT('',(73.592494,102.80648,37.5));
#3178 = SURFACE_CURVE('',#3179,(#3184,#3191),.PCURVE_S1.);
#3179 = CIRCLE('',#3180,0.275);
#3180 = AXIS2_PLACEMENT_3D('',#3181,#3182,#3183);
#3181 = CARTESIAN_POINT('',(73.317494,102.80648,37.5));
#3182 = DIRECTION('',(0.,0.,1.));
#3183 = DIRECTION('',(1.,0.,0.));
#3184 = PCURVE('',#2595,#3185);
#3185 = DEFINITIONAL_REPRESENTATION('',(#3186),#3190);
#3186 = CIRCLE('',#3187,0.275);
#3187 = AXIS2_PLACEMENT_2D('',#3188,#3189);
#3188 = CARTESIAN_POINT('',(0.,0.));
#3189 = DIRECTION('',(1.,0.));
#3190 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3191 = PCURVE('',#2684,#3192);
#3192 = DEFINITIONAL_REPRESENTATION('',(#3193),#3197);
#3193 = LINE('',#3194,#3195);
#3194 = CARTESIAN_POINT('',(0.,18.046382));
#3195 = VECTOR('',#3196,1.);
#3196 = DIRECTION('',(1.,0.));
#3197 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3198 = FACE_BOUND('',#3199,.T.);
#3199 = EDGE_LOOP('',(#3200));
#3200 = ORIENTED_EDGE('',*,*,#3201,.F.);
#3201 = EDGE_CURVE('',#3202,#3202,#3204,.T.);
#3202 = VERTEX_POINT('',#3203);
#3203 = CARTESIAN_POINT('',(73.592494,105.15648,37.5));
#3204 = SURFACE_CURVE('',#3205,(#3210,#3217),.PCURVE_S1.);
#3205 = CIRCLE('',#3206,0.275);
#3206 = AXIS2_PLACEMENT_3D('',#3207,#3208,#3209);
#3207 = CARTESIAN_POINT('',(73.317494,105.15648,37.5));
#3208 = DIRECTION('',(0.,0.,1.));
#3209 = DIRECTION('',(1.,0.,0.));
#3210 = PCURVE('',#2595,#3211);
#3211 = DEFINITIONAL_REPRESENTATION('',(#3212),#3216);
#3212 = CIRCLE('',#3213,0.275);
#3213 = AXIS2_PLACEMENT_2D('',#3214,#3215);
#3214 = CARTESIAN_POINT('',(0.,2.35));
#3215 = DIRECTION('',(1.,0.));
#3216 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3217 = PCURVE('',#2715,#3218);
#3218 = DEFINITIONAL_REPRESENTATION('',(#3219),#3223);
#3219 = LINE('',#3220,#3221);
#3220 = CARTESIAN_POINT('',(0.,19.779393));
#3221 = VECTOR('',#3222,1.);
#3222 = DIRECTION('',(1.,0.));
#3223 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3224 = FACE_BOUND('',#3225,.T.);
#3225 = EDGE_LOOP('',(#3226));
#3226 = ORIENTED_EDGE('',*,*,#3227,.F.);
#3227 = EDGE_CURVE('',#3228,#3228,#3230,.T.);
#3228 = VERTEX_POINT('',#3229);
#3229 = CARTESIAN_POINT('',(73.592494,109.853261,37.5));
#3230 = SURFACE_CURVE('',#3231,(#3236,#3243),.PCURVE_S1.);
#3231 = CIRCLE('',#3232,0.275);
#3232 = AXIS2_PLACEMENT_3D('',#3233,#3234,#3235);
#3233 = CARTESIAN_POINT('',(73.317494,109.853261,37.5));
#3234 = DIRECTION('',(0.,0.,1.));
#3235 = DIRECTION('',(1.,0.,0.));
#3236 = PCURVE('',#2595,#3237);
#3237 = DEFINITIONAL_REPRESENTATION('',(#3238),#3242);
#3238 = CIRCLE('',#3239,0.275);
#3239 = AXIS2_PLACEMENT_2D('',#3240,#3241);
#3240 = CARTESIAN_POINT('',(0.,7.046781));
#3241 = DIRECTION('',(1.,0.));
#3242 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3243 = PCURVE('',#3244,#3249);
#3244 = CYLINDRICAL_SURFACE('',#3245,0.275);
#3245 = AXIS2_PLACEMENT_3D('',#3246,#3247,#3248);
#3246 = CARTESIAN_POINT('',(73.317494,109.853261,14.355218));
#3247 = DIRECTION('',(0.,0.,1.));
#3248 = DIRECTION('',(1.,0.,0.));
#3249 = DEFINITIONAL_REPRESENTATION('',(#3250),#3254);
#3250 = LINE('',#3251,#3252);
#3251 = CARTESIAN_POINT('',(0.,23.144782));
#3252 = VECTOR('',#3253,1.);
#3253 = DIRECTION('',(1.,0.));
#3254 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3255 = FACE_BOUND('',#3256,.T.);
#3256 = EDGE_LOOP('',(#3257));
#3257 = ORIENTED_EDGE('',*,*,#3258,.F.);
#3258 = EDGE_CURVE('',#3259,#3259,#3261,.T.);
#3259 = VERTEX_POINT('',#3260);
#3260 = CARTESIAN_POINT('',(73.592494,111.800042,37.5));
#3261 = SURFACE_CURVE('',#3262,(#3267,#3274),.PCURVE_S1.);
#3262 = CIRCLE('',#3263,0.275);
#3263 = AXIS2_PLACEMENT_3D('',#3264,#3265,#3266);
#3264 = CARTESIAN_POINT('',(73.317494,111.800042,37.5));
#3265 = DIRECTION('',(0.,0.,1.));
#3266 = DIRECTION('',(1.,0.,0.));
#3267 = PCURVE('',#2595,#3268);
#3268 = DEFINITIONAL_REPRESENTATION('',(#3269),#3273);
#3269 = CIRCLE('',#3270,0.275);
#3270 = AXIS2_PLACEMENT_2D('',#3271,#3272);
#3271 = CARTESIAN_POINT('',(0.,8.993562));
#3272 = DIRECTION('',(1.,0.));
#3273 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3274 = PCURVE('',#3275,#3280);
#3275 = CYLINDRICAL_SURFACE('',#3276,0.275);
#3276 = AXIS2_PLACEMENT_3D('',#3277,#3278,#3279);
#3277 = CARTESIAN_POINT('',(73.317494,111.800042,12.973447));
#3278 = DIRECTION('',(0.,0.,1.));
#3279 = DIRECTION('',(1.,0.,0.));
#3280 = DEFINITIONAL_REPRESENTATION('',(#3281),#3285);
#3281 = LINE('',#3282,#3283);
#3282 = CARTESIAN_POINT('',(0.,24.526553));
#3283 = VECTOR('',#3284,1.);
#3284 = DIRECTION('',(1.,0.));
#3285 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3286 = ADVANCED_FACE('',(#3287),#2624,.T.);
#3287 = FACE_BOUND('',#3288,.T.);
#3288 = EDGE_LOOP('',(#3289,#3310,#3334,#3382,#3383));
#3289 = ORIENTED_EDGE('',*,*,#3290,.F.);
#3290 = EDGE_CURVE('',#3291,#3120,#3293,.T.);
#3291 = VERTEX_POINT('',#3292);
#3292 = CARTESIAN_POINT('',(69.695779084491,107.5048705,24.720607));
#3293 = SURFACE_CURVE('',#3294,(#3298,#3304),.PCURVE_S1.);
#3294 = LINE('',#3295,#3296);
#3295 = CARTESIAN_POINT('',(69.695779084491,107.5048705,24.720607));
#3296 = VECTOR('',#3297,1.);
#3297 = DIRECTION('',(0.,0.,1.));
#3298 = PCURVE('',#2624,#3299);
#3299 = DEFINITIONAL_REPRESENTATION('',(#3300),#3303);
#3300 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#3301,#3302),.UNSPECIFIED.,.F.,
  .F.,(2,2),(0.,12.779393),.PIECEWISE_BEZIER_KNOTS.);
#3301 = CARTESIAN_POINT('',(2.566329322101,0.));
#3302 = CARTESIAN_POINT('',(2.566329322101,12.779393));
#3303 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3304 = PCURVE('',#3140,#3305);
#3305 = DEFINITIONAL_REPRESENTATION('',(#3306),#3309);
#3306 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#3307,#3308),.UNSPECIFIED.,.F.,
  .F.,(2,2),(-2.399999999625E-06,12.7793954),.PIECEWISE_BEZIER_KNOTS.);
#3307 = CARTESIAN_POINT('',(3.716855985078,3.3653866));
#3308 = CARTESIAN_POINT('',(3.716855985078,16.1447844));
#3309 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3310 = ORIENTED_EDGE('',*,*,#3311,.T.);
#3311 = EDGE_CURVE('',#3291,#3312,#3314,.T.);
#3312 = VERTEX_POINT('',#3313);
#3313 = CARTESIAN_POINT('',(70.032246311601,102.35667563262,24.720607));
#3314 = SURFACE_CURVE('',#3315,(#3320,#3327),.PCURVE_S1.);
#3315 = CIRCLE('',#3316,4.316451884327);
#3316 = AXIS2_PLACEMENT_3D('',#3317,#3318,#3319);
#3317 = CARTESIAN_POINT('',(73.317494,105.15648,24.720607));
#3318 = DIRECTION('',(0.,0.,1.));
#3319 = DIRECTION('',(-0.839049064501,0.544055757583,0.));
#3320 = PCURVE('',#2624,#3321);
#3321 = DEFINITIONAL_REPRESENTATION('',(#3322),#3326);
#3322 = LINE('',#3323,#3324);
#3323 = CARTESIAN_POINT('',(2.566329322101,0.));
#3324 = VECTOR('',#3325,1.);
#3325 = DIRECTION('',(1.,0.));
#3326 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3327 = PCURVE('',#2872,#3328);
#3328 = DEFINITIONAL_REPRESENTATION('',(#3329),#3333);
#3329 = CIRCLE('',#3330,4.316451884327);
#3330 = AXIS2_PLACEMENT_2D('',#3331,#3332);
#3331 = CARTESIAN_POINT('',(0.,0.));
#3332 = DIRECTION('',(-0.839049064501,0.544055757583));
#3333 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3334 = ORIENTED_EDGE('',*,*,#3335,.F.);
#3335 = EDGE_CURVE('',#2517,#3312,#3336,.T.);
#3336 = SURFACE_CURVE('',#3337,(#3350,#3366),.PCURVE_S1.);
#3337 = B_SPLINE_CURVE_WITH_KNOTS('',6,(#3338,#3339,#3340,#3341,#3342,
    #3343,#3344,#3345,#3346,#3347,#3348,#3349),.UNSPECIFIED.,.F.,.F.,(7,
    5,7),(0.,0.647313568007,1.),.UNSPECIFIED.);
#3338 = CARTESIAN_POINT('',(69.164045809675,103.98147999999,26.453618));
#3339 = CARTESIAN_POINT('',(69.219768152736,103.78450990217,
    26.267879520468));
#3340 = CARTESIAN_POINT('',(69.286137713231,103.59269741492,
    26.081467272993));
#3341 = CARTESIAN_POINT('',(69.362624712508,103.40629551133,
    25.894544403707));
#3342 = CARTESIAN_POINT('',(69.448808824168,103.22552841064,
    25.707279260217));
#3343 = CARTESIAN_POINT('',(69.544365104554,103.05059044292,
    25.519845636082));
#3344 = CARTESIAN_POINT('',(69.706207191193,102.7896054863,
    25.230324613684));
#3345 = CARTESIAN_POINT('',(69.766018632357,102.69934198998,
    25.128222937839));
#3346 = CARTESIAN_POINT('',(69.828535029178,102.61088896267,
    25.026161574399));
#3347 = CARTESIAN_POINT('',(69.893740898787,102.52427459189,
    24.924176392568));
#3348 = CARTESIAN_POINT('',(69.96163727462,102.43952717336,
    24.822308656014));
#3349 = CARTESIAN_POINT('',(70.032246728167,102.35667514383,24.7206064)
  );
#3350 = PCURVE('',#2624,#3351);
#3351 = DEFINITIONAL_REPRESENTATION('',(#3352),#3365);
#3352 = B_SPLINE_CURVE_WITH_KNOTS('',6,(#3353,#3354,#3355,#3356,#3357,
    #3358,#3359,#3360,#3361,#3362,#3363,#3364),.UNSPECIFIED.,.F.,.F.,(7,
    5,7),(0.,0.647313568007,1.),.UNSPECIFIED.);
#3353 = CARTESIAN_POINT('',(3.417286156876,1.733011));
#3354 = CARTESIAN_POINT('',(3.464709427566,1.547272520468));
#3355 = CARTESIAN_POINT('',(3.511654429083,1.360860272993));
#3356 = CARTESIAN_POINT('',(3.558223189779,1.173937403707));
#3357 = CARTESIAN_POINT('',(3.604512326169,0.986672260217));
#3358 = CARTESIAN_POINT('',(3.650622366758,0.799238636082));
#3359 = CARTESIAN_POINT('',(3.721767103699,0.509717613684));
#3360 = CARTESIAN_POINT('',(3.746841508372,0.407615937839));
#3361 = CARTESIAN_POINT('',(3.771918175759,0.305554574399));
#3362 = CARTESIAN_POINT('',(3.797017844562,0.203569392568));
#3363 = CARTESIAN_POINT('',(3.822163779309,0.101701656014));
#3364 = CARTESIAN_POINT('',(3.847383198938,-6.E-07));
#3365 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3366 = PCURVE('',#2654,#3367);
#3367 = DEFINITIONAL_REPRESENTATION('',(#3368),#3381);
#3368 = B_SPLINE_CURVE_WITH_KNOTS('',6,(#3369,#3370,#3371,#3372,#3373,
    #3374,#3375,#3376,#3377,#3378,#3379,#3380),.UNSPECIFIED.,.F.,.F.,(7,
    5,7),(0.,0.647313568007,0.999999653217),.UNSPECIFIED.);
#3369 = CARTESIAN_POINT('',(9.149084457484,7.));
#3370 = CARTESIAN_POINT('',(9.189479539307,6.814261520468));
#3371 = CARTESIAN_POINT('',(9.230462009976,6.627849272992));
#3372 = CARTESIAN_POINT('',(9.272209188499,6.440926403706));
#3373 = CARTESIAN_POINT('',(9.314922298462,6.253661260217));
#3374 = CARTESIAN_POINT('',(9.358840395442,6.066227636082));
#3375 = CARTESIAN_POINT('',(9.429051563141,5.776706714085));
#3376 = CARTESIAN_POINT('',(9.454267984724,5.674605138624));
#3377 = CARTESIAN_POINT('',(9.47999104459,5.572543875458));
#3378 = CARTESIAN_POINT('',(9.506279978438,5.470558793681));
#3379 = CARTESIAN_POINT('',(9.533204426792,5.368691156827));
#3380 = CARTESIAN_POINT('',(9.560848331297,5.266989));
#3381 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3382 = ORIENTED_EDGE('',*,*,#2611,.T.);
#3383 = ORIENTED_EDGE('',*,*,#3152,.F.);
#3384 = ADVANCED_FACE('',(#3385),#2654,.F.);
#3385 = FACE_BOUND('',#3386,.F.);
#3386 = EDGE_LOOP('',(#3387,#3413,#3414,#3415));
#3387 = ORIENTED_EDGE('',*,*,#3388,.F.);
#3388 = EDGE_CURVE('',#3312,#2731,#3389,.T.);
#3389 = SURFACE_CURVE('',#3390,(#3395,#3402),.PCURVE_S1.);
#3390 = CIRCLE('',#3391,3.315897516969);
#3391 = AXIS2_PLACEMENT_3D('',#3392,#3393,#3394);
#3392 = CARTESIAN_POINT('',(73.317494,102.80648,24.720607));
#3393 = DIRECTION('',(-0.,0.,-1.));
#3394 = DIRECTION('',(-0.990756702095,-0.135650865286,0.));
#3395 = PCURVE('',#2654,#3396);
#3396 = DEFINITIONAL_REPRESENTATION('',(#3397),#3401);
#3397 = LINE('',#3398,#3399);
#3398 = CARTESIAN_POINT('',(9.560848331297,5.266989));
#3399 = VECTOR('',#3400,1.);
#3400 = DIRECTION('',(-1.,-0.));
#3401 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3402 = PCURVE('',#2872,#3403);
#3403 = DEFINITIONAL_REPRESENTATION('',(#3404),#3412);
#3404 = ( BOUNDED_CURVE() B_SPLINE_CURVE(2,(#3405,#3406,#3407,#3408,
#3409,#3410,#3411),.UNSPECIFIED.,.T.,.F.) B_SPLINE_CURVE_WITH_KNOTS((1,2
    ,2,2,2,1),(-2.094395102393,0.,2.094395102393,4.188790204786,
6.28318530718,8.377580409573),.UNSPECIFIED.) CURVE() 
GEOMETRIC_REPRESENTATION_ITEM() RATIONAL_B_SPLINE_CURVE((1.,0.5,1.,0.5,
1.,0.5,1.)) REPRESENTATION_ITEM('') );
#3405 = CARTESIAN_POINT('',(-3.285247688399,-2.799804367377));
#3406 = CARTESIAN_POINT('',(-4.064331706162,2.890411544377));
#3407 = CARTESIAN_POINT('',(1.253081835317,0.720010139566));
#3408 = CARTESIAN_POINT('',(6.570495376797,-1.450391265245));
#3409 = CARTESIAN_POINT('',(2.032165853081,-4.970205772189));
#3410 = CARTESIAN_POINT('',(-2.506163670635,-8.490020279132));
#3411 = CARTESIAN_POINT('',(-3.285247688399,-2.799804367377));
#3412 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3413 = ORIENTED_EDGE('',*,*,#3335,.F.);
#3414 = ORIENTED_EDGE('',*,*,#2639,.F.);
#3415 = ORIENTED_EDGE('',*,*,#2730,.T.);
#3416 = ADVANCED_FACE('',(#3417),#2684,.F.);
#3417 = FACE_BOUND('',#3418,.F.);
#3418 = EDGE_LOOP('',(#3419,#3440,#3441,#3442));
#3419 = ORIENTED_EDGE('',*,*,#3420,.T.);
#3420 = EDGE_CURVE('',#2668,#3176,#3421,.T.);
#3421 = SEAM_CURVE('',#3422,(#3426,#3433),.PCURVE_S1.);
#3422 = LINE('',#3423,#3424);
#3423 = CARTESIAN_POINT('',(73.592494,102.80648,19.453618));
#3424 = VECTOR('',#3425,1.);
#3425 = DIRECTION('',(0.,0.,1.));
#3426 = PCURVE('',#2684,#3427);
#3427 = DEFINITIONAL_REPRESENTATION('',(#3428),#3432);
#3428 = LINE('',#3429,#3430);
#3429 = CARTESIAN_POINT('',(6.28318530718,-0.));
#3430 = VECTOR('',#3431,1.);
#3431 = DIRECTION('',(0.,1.));
#3432 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3433 = PCURVE('',#2684,#3434);
#3434 = DEFINITIONAL_REPRESENTATION('',(#3435),#3439);
#3435 = LINE('',#3436,#3437);
#3436 = CARTESIAN_POINT('',(0.,-0.));
#3437 = VECTOR('',#3438,1.);
#3438 = DIRECTION('',(0.,1.));
#3439 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3440 = ORIENTED_EDGE('',*,*,#3175,.F.);
#3441 = ORIENTED_EDGE('',*,*,#3420,.F.);
#3442 = ORIENTED_EDGE('',*,*,#2667,.T.);
#3443 = ADVANCED_FACE('',(#3444),#2715,.F.);
#3444 = FACE_BOUND('',#3445,.F.);
#3445 = EDGE_LOOP('',(#3446,#3467,#3468,#3469));
#3446 = ORIENTED_EDGE('',*,*,#3447,.T.);
#3447 = EDGE_CURVE('',#2699,#3202,#3448,.T.);
#3448 = SEAM_CURVE('',#3449,(#3453,#3460),.PCURVE_S1.);
#3449 = LINE('',#3450,#3451);
#3450 = CARTESIAN_POINT('',(73.592494,105.15648,17.720607));
#3451 = VECTOR('',#3452,1.);
#3452 = DIRECTION('',(0.,0.,1.));
#3453 = PCURVE('',#2715,#3454);
#3454 = DEFINITIONAL_REPRESENTATION('',(#3455),#3459);
#3455 = LINE('',#3456,#3457);
#3456 = CARTESIAN_POINT('',(6.28318530718,-0.));
#3457 = VECTOR('',#3458,1.);
#3458 = DIRECTION('',(0.,1.));
#3459 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3460 = PCURVE('',#2715,#3461);
#3461 = DEFINITIONAL_REPRESENTATION('',(#3462),#3466);
#3462 = LINE('',#3463,#3464);
#3463 = CARTESIAN_POINT('',(0.,-0.));
#3464 = VECTOR('',#3465,1.);
#3465 = DIRECTION('',(0.,1.));
#3466 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3467 = ORIENTED_EDGE('',*,*,#3201,.F.);
#3468 = ORIENTED_EDGE('',*,*,#3447,.F.);
#3469 = ORIENTED_EDGE('',*,*,#2698,.T.);
#3470 = ADVANCED_FACE('',(#3471),#2872,.F.);
#3471 = FACE_BOUND('',#3472,.F.);
#3472 = EDGE_LOOP('',(#3473,#3474,#3501,#3502));
#3473 = ORIENTED_EDGE('',*,*,#2855,.T.);
#3474 = ORIENTED_EDGE('',*,*,#3475,.T.);
#3475 = EDGE_CURVE('',#2856,#3291,#3476,.T.);
#3476 = SURFACE_CURVE('',#3477,(#3482,#3489),.PCURVE_S1.);
#3477 = CIRCLE('',#3478,4.316451884327);
#3478 = AXIS2_PLACEMENT_3D('',#3479,#3480,#3481);
#3479 = CARTESIAN_POINT('',(73.317494,105.15648,24.720607));
#3480 = DIRECTION('',(0.,0.,1.));
#3481 = DIRECTION('',(1.,0.,0.));
#3482 = PCURVE('',#2872,#3483);
#3483 = DEFINITIONAL_REPRESENTATION('',(#3484),#3488);
#3484 = CIRCLE('',#3485,4.316451884327);
#3485 = AXIS2_PLACEMENT_2D('',#3486,#3487);
#3486 = CARTESIAN_POINT('',(0.,0.));
#3487 = DIRECTION('',(1.,0.));
#3488 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3489 = PCURVE('',#3490,#3495);
#3490 = CONICAL_SURFACE('',#3491,0.275,0.523598775598);
#3491 = AXIS2_PLACEMENT_3D('',#3492,#3493,#3494);
#3492 = CARTESIAN_POINT('',(73.317494,105.15648,17.720607));
#3493 = DIRECTION('',(0.,0.,1.));
#3494 = DIRECTION('',(1.,0.,0.));
#3495 = DEFINITIONAL_REPRESENTATION('',(#3496),#3500);
#3496 = LINE('',#3497,#3498);
#3497 = CARTESIAN_POINT('',(0.,7.));
#3498 = VECTOR('',#3499,1.);
#3499 = DIRECTION('',(1.,0.));
#3500 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3501 = ORIENTED_EDGE('',*,*,#3311,.T.);
#3502 = ORIENTED_EDGE('',*,*,#3388,.T.);
#3503 = ADVANCED_FACE('',(#3504),#2899,.T.);
#3504 = FACE_BOUND('',#3505,.T.);
#3505 = EDGE_LOOP('',(#3506,#3571,#3600,#3619,#3620));
#3506 = ORIENTED_EDGE('',*,*,#3507,.F.);
#3507 = EDGE_CURVE('',#3508,#2856,#3510,.T.);
#3508 = VERTEX_POINT('',#3509);
#3509 = CARTESIAN_POINT('',(75.486074304206,106.12110125878,21.355218));
#3510 = SURFACE_CURVE('',#3511,(#3529,#3550),.PCURVE_S1.);
#3511 = B_SPLINE_CURVE_WITH_KNOTS('',6,(#3512,#3513,#3514,#3515,#3516,
    #3517,#3518,#3519,#3520,#3521,#3522,#3523,#3524,#3525,#3526,#3527,
    #3528),.UNSPECIFIED.,.F.,.F.,(7,5,5,7),(0.,6.689446601882E-02,
    0.542090605694,1.),.UNSPECIFIED.);
#3512 = CARTESIAN_POINT('',(75.486074002938,106.12110108373,
    21.355217400001));
#3513 = CARTESIAN_POINT('',(75.504985112416,106.13208942688,
    21.392880346328));
#3514 = CARTESIAN_POINT('',(75.523838331808,106.14319794396,
    21.43054112637));
#3515 = CARTESIAN_POINT('',(75.542633099432,106.15442658151,
    21.468199646419));
#3516 = CARTESIAN_POINT('',(75.561368833629,106.16577528499,
    21.505855815562));
#3517 = CARTESIAN_POINT('',(75.580044932761,106.17724399879,
    21.543509545679));
#3518 = CARTESIAN_POINT('',(75.730901556788,106.27115472297,
    21.84862245193));
#3519 = CARTESIAN_POINT('',(75.860101323926,106.35952963752,
    22.115955927074));
#3520 = CARTESIAN_POINT('',(75.986038282642,106.45393764679,
    22.383132593349));
#3521 = CARTESIAN_POINT('',(76.108443976732,106.55435513247,
    22.650125231753));
#3522 = CARTESIAN_POINT('',(76.227025811571,106.66075423564,
    22.916906158187));
#3523 = CARTESIAN_POINT('',(76.45173559575,106.88136475075,
    23.440293388307));
#3524 = CARTESIAN_POINT('',(76.558159145495,106.99515049087,23.696915199
    ));
#3525 = CARTESIAN_POINT('',(76.660444770683,107.11443145049,
    23.953288519978));
#3526 = CARTESIAN_POINT('',(76.758280203075,107.23917331564,
    24.209384136418));
#3527 = CARTESIAN_POINT('',(76.851325391694,107.36933551592,
    24.465169036679));
#3528 = CARTESIAN_POINT('',(76.939208915509,107.5048705,24.720607));
#3529 = PCURVE('',#2899,#3530);
#3530 = DEFINITIONAL_REPRESENTATION('',(#3531),#3549);
#3531 = B_SPLINE_CURVE_WITH_KNOTS('',6,(#3532,#3533,#3534,#3535,#3536,
    #3537,#3538,#3539,#3540,#3541,#3542,#3543,#3544,#3545,#3546,#3547,
    #3548),.UNSPECIFIED.,.F.,.F.,(7,5,5,7),(0.,6.689446601882E-02,
    0.542090605694,1.),.UNSPECIFIED.);
#3532 = CARTESIAN_POINT('',(5.238759808167,-5.999987315874E-07));
#3533 = CARTESIAN_POINT('',(5.243826876541,3.766234632779E-02));
#3534 = CARTESIAN_POINT('',(5.248896336114,7.532312637011E-02));
#3535 = CARTESIAN_POINT('',(5.253968302245,0.112981646419));
#3536 = CARTESIAN_POINT('',(5.259042887412,0.150637815562));
#3537 = CARTESIAN_POINT('',(5.264120201209,0.188291545679));
#3538 = CARTESIAN_POINT('',(5.305288047947,0.49340445193));
#3539 = CARTESIAN_POINT('',(5.341518730034,0.760737927074));
#3540 = CARTESIAN_POINT('',(5.377930930811,1.027914593349));
#3541 = CARTESIAN_POINT('',(5.414556983614,1.294907231753));
#3542 = CARTESIAN_POINT('',(5.451429424868,1.561688158187));
#3543 = CARTESIAN_POINT('',(5.524383404849,2.085075388307));
#3544 = CARTESIAN_POINT('',(5.560444709303,2.341697199));
#3545 = CARTESIAN_POINT('',(5.596796490924,2.598070519978));
#3546 = CARTESIAN_POINT('',(5.63347000323,2.854166136418));
#3547 = CARTESIAN_POINT('',(5.670499100826,3.109951036679));
#3548 = CARTESIAN_POINT('',(5.707921975691,3.365389));
#3549 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3550 = PCURVE('',#3490,#3551);
#3551 = DEFINITIONAL_REPRESENTATION('',(#3552),#3570);
#3552 = B_SPLINE_CURVE_WITH_KNOTS('',6,(#3553,#3554,#3555,#3556,#3557,
    #3558,#3559,#3560,#3561,#3562,#3563,#3564,#3565,#3566,#3567,#3568,
    #3569),.UNSPECIFIED.,.F.,.F.,(7,5,5,7),(0.,6.689446601882E-02,
    0.542090605694,1.),.UNSPECIFIED.);
#3553 = CARTESIAN_POINT('',(0.418535325198,3.634610400001));
#3554 = CARTESIAN_POINT('',(0.419527126218,3.672273346328));
#3555 = CARTESIAN_POINT('',(0.420553292412,3.70993412637));
#3556 = CARTESIAN_POINT('',(0.421612493992,3.747592646419));
#3557 = CARTESIAN_POINT('',(0.422703490666,3.785248815562));
#3558 = CARTESIAN_POINT('',(0.423825131633,3.822902545679));
#3559 = CARTESIAN_POINT('',(0.433154269437,4.12801545193));
#3560 = CARTESIAN_POINT('',(0.442824540317,4.395348927074));
#3561 = CARTESIAN_POINT('',(0.453606555905,4.662525593349));
#3562 = CARTESIAN_POINT('',(0.465307430535,4.929518231753));
#3563 = CARTESIAN_POINT('',(0.477784990314,5.196299158187));
#3564 = CARTESIAN_POINT('',(0.5036271366,5.719686388307));
#3565 = CARTESIAN_POINT('',(0.516942368277,5.976308199));
#3566 = CARTESIAN_POINT('',(0.530807088128,6.232681519978));
#3567 = CARTESIAN_POINT('',(0.545168505431,6.488777136418));
#3568 = CARTESIAN_POINT('',(0.55999454911,6.744562036679));
#3569 = CARTESIAN_POINT('',(0.575263331488,7.));
#3570 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3571 = ORIENTED_EDGE('',*,*,#3572,.T.);
#3572 = EDGE_CURVE('',#3508,#3573,#3575,.T.);
#3573 = VERTEX_POINT('',#3574);
#3574 = CARTESIAN_POINT('',(77.522760674567,110.82665149999,21.355218));
#3575 = SURFACE_CURVE('',#3576,(#3581,#3588),.PCURVE_S1.);
#3576 = CIRCLE('',#3577,4.316451884327);
#3577 = AXIS2_PLACEMENT_3D('',#3578,#3579,#3580);
#3578 = CARTESIAN_POINT('',(73.317494,109.853261,21.355218));
#3579 = DIRECTION('',(-0.,0.,1.));
#3580 = DIRECTION('',(0.502398813266,-0.864636011527,0.));
#3581 = PCURVE('',#2899,#3582);
#3582 = DEFINITIONAL_REPRESENTATION('',(#3583),#3587);
#3583 = LINE('',#3584,#3585);
#3584 = CARTESIAN_POINT('',(5.23875988889,0.));
#3585 = VECTOR('',#3586,1.);
#3586 = DIRECTION('',(1.,0.));
#3587 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3588 = PCURVE('',#3589,#3594);
#3589 = PLANE('',#3590);
#3590 = AXIS2_PLACEMENT_3D('',#3591,#3592,#3593);
#3591 = CARTESIAN_POINT('',(73.317494,109.853261,21.355218));
#3592 = DIRECTION('',(0.,0.,1.));
#3593 = DIRECTION('',(1.,0.,0.));
#3594 = DEFINITIONAL_REPRESENTATION('',(#3595),#3599);
#3595 = CIRCLE('',#3596,4.316451884327);
#3596 = AXIS2_PLACEMENT_2D('',#3597,#3598);
#3597 = CARTESIAN_POINT('',(0.,0.));
#3598 = DIRECTION('',(0.502398813266,-0.864636011527));
#3599 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3600 = ORIENTED_EDGE('',*,*,#3601,.T.);
#3601 = EDGE_CURVE('',#3573,#2943,#3602,.T.);
#3602 = SURFACE_CURVE('',#3603,(#3607,#3613),.PCURVE_S1.);
#3603 = LINE('',#3604,#3605);
#3604 = CARTESIAN_POINT('',(77.522760674567,110.8266515,19.973447));
#3605 = VECTOR('',#3606,1.);
#3606 = DIRECTION('',(0.,0.,1.));
#3607 = PCURVE('',#2899,#3608);
#3608 = DEFINITIONAL_REPRESENTATION('',(#3609),#3612);
#3609 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#3610,#3611),.UNSPECIFIED.,.F.,
  .F.,(2,2),(1.3817686,17.5265554),.PIECEWISE_BEZIER_KNOTS.);
#3610 = CARTESIAN_POINT('',(6.510648808398,-2.399999999625E-06));
#3611 = CARTESIAN_POINT('',(6.510648808398,16.1447844));
#3612 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3613 = PCURVE('',#2991,#3614);
#3614 = DEFINITIONAL_REPRESENTATION('',(#3615),#3618);
#3615 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#3616,#3617),.UNSPECIFIED.,.F.,
  .F.,(2,2),(1.3817686,17.5265554),.PIECEWISE_BEZIER_KNOTS.);
#3616 = CARTESIAN_POINT('',(6.055721805961,1.3817686));
#3617 = CARTESIAN_POINT('',(6.055721805961,17.5265554));
#3618 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3619 = ORIENTED_EDGE('',*,*,#2942,.T.);
#3620 = ORIENTED_EDGE('',*,*,#2884,.F.);
#3621 = ADVANCED_FACE('',(#3622),#3140,.T.);
#3622 = FACE_BOUND('',#3623,.T.);
#3623 = EDGE_LOOP('',(#3624,#3645,#3669,#3729,#3730));
#3624 = ORIENTED_EDGE('',*,*,#3625,.F.);
#3625 = EDGE_CURVE('',#3626,#3087,#3628,.T.);
#3626 = VERTEX_POINT('',#3627);
#3627 = CARTESIAN_POINT('',(69.112227325433,110.82665149999,21.355218));
#3628 = SURFACE_CURVE('',#3629,(#3633,#3639),.PCURVE_S1.);
#3629 = LINE('',#3630,#3631);
#3630 = CARTESIAN_POINT('',(69.112227325433,110.8266515,19.973447));
#3631 = VECTOR('',#3632,1.);
#3632 = DIRECTION('',(0.,0.,1.));
#3633 = PCURVE('',#3140,#3634);
#3634 = DEFINITIONAL_REPRESENTATION('',(#3635),#3638);
#3635 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#3636,#3637),.UNSPECIFIED.,.F.,
  .F.,(2,2),(1.3817686,17.5265554),.PIECEWISE_BEZIER_KNOTS.);
#3636 = CARTESIAN_POINT('',(2.914129152371,-2.399999999625E-06));
#3637 = CARTESIAN_POINT('',(2.914129152371,16.1447844));
#3638 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3639 = PCURVE('',#3107,#3640);
#3640 = DEFINITIONAL_REPRESENTATION('',(#3641),#3644);
#3641 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#3642,#3643),.UNSPECIFIED.,.F.,
  .F.,(2,2),(1.3817686,17.5265554),.PIECEWISE_BEZIER_KNOTS.);
#3642 = CARTESIAN_POINT('',(3.369056154808,1.3817686));
#3643 = CARTESIAN_POINT('',(3.369056154808,17.5265554));
#3644 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3645 = ORIENTED_EDGE('',*,*,#3646,.T.);
#3646 = EDGE_CURVE('',#3626,#3647,#3649,.T.);
#3647 = VERTEX_POINT('',#3648);
#3648 = CARTESIAN_POINT('',(71.148913695794,106.12110125878,21.355218));
#3649 = SURFACE_CURVE('',#3650,(#3655,#3662),.PCURVE_S1.);
#3650 = CIRCLE('',#3651,4.316451884327);
#3651 = AXIS2_PLACEMENT_3D('',#3652,#3653,#3654);
#3652 = CARTESIAN_POINT('',(73.317494,109.853261,21.355218));
#3653 = DIRECTION('',(0.,0.,1.));
#3654 = DIRECTION('',(-0.706876980366,0.707336507349,0.));
#3655 = PCURVE('',#3140,#3656);
#3656 = DEFINITIONAL_REPRESENTATION('',(#3657),#3661);
#3657 = LINE('',#3658,#3659);
#3658 = CARTESIAN_POINT('',(2.355869555541,0.));
#3659 = VECTOR('',#3660,1.);
#3660 = DIRECTION('',(1.,0.));
#3661 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3662 = PCURVE('',#3589,#3663);
#3663 = DEFINITIONAL_REPRESENTATION('',(#3664),#3668);
#3664 = CIRCLE('',#3665,4.316451884327);
#3665 = AXIS2_PLACEMENT_2D('',#3666,#3667);
#3666 = CARTESIAN_POINT('',(0.,0.));
#3667 = DIRECTION('',(-0.706876980366,0.707336507349));
#3668 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3669 = ORIENTED_EDGE('',*,*,#3670,.F.);
#3670 = EDGE_CURVE('',#3291,#3647,#3671,.T.);
#3671 = SURFACE_CURVE('',#3672,(#3689,#3709),.PCURVE_S1.);
#3672 = B_SPLINE_CURVE_WITH_KNOTS('',8,(#3673,#3674,#3675,#3676,#3677,
    #3678,#3679,#3680,#3681,#3682,#3683,#3684,#3685,#3686,#3687,#3688),
  .UNSPECIFIED.,.F.,.F.,(9,7,9),(0.,0.932550193489,1.),.UNSPECIFIED.);
#3673 = CARTESIAN_POINT('',(69.695779084491,107.5048705,24.720607));
#3674 = CARTESIAN_POINT('',(69.830014357841,107.29785131316,
    24.33044530254));
#3675 = CARTESIAN_POINT('',(69.975714769639,107.10277618617,
    23.939524298408));
#3676 = CARTESIAN_POINT('',(70.131761529563,106.91977980249,
    23.547939289272));
#3677 = CARTESIAN_POINT('',(70.29715838774,106.74897859851,
    23.155782730893));
#3678 = CARTESIAN_POINT('',(70.47099571832,106.59046955328,
    22.763140165386));
#3679 = CARTESIAN_POINT('',(70.652454632309,106.44433178032,
    22.37008997804));
#3680 = CARTESIAN_POINT('',(70.840786430069,106.31062859226,
    21.976700967053));
#3681 = CARTESIAN_POINT('',(71.049475113434,106.18064310495,
    21.554565050404));
#3682 = CARTESIAN_POINT('',(71.063583955079,106.17194094129,
    21.526090688068));
#3683 = CARTESIAN_POINT('',(71.077725399302,106.16330413069,
    21.4976149805));
#3684 = CARTESIAN_POINT('',(71.091899222769,106.15473269368,
    21.469137959528));
#3685 = CARTESIAN_POINT('',(71.106105206565,106.14622665055,
    21.440659657604));
#3686 = CARTESIAN_POINT('',(71.120343136193,106.13778602134,
    21.412180107805));
#3687 = CARTESIAN_POINT('',(71.134612801578,106.12941082587,
    21.383699343829));
#3688 = CARTESIAN_POINT('',(71.148913997062,106.12110108373,21.3552174)
  );
#3689 = PCURVE('',#3140,#3690);
#3690 = DEFINITIONAL_REPRESENTATION('',(#3691),#3708);
#3691 = B_SPLINE_CURVE_WITH_KNOTS('',8,(#3692,#3693,#3694,#3695,#3696,
    #3697,#3698,#3699,#3700,#3701,#3702,#3703,#3704,#3705,#3706,#3707),
  .UNSPECIFIED.,.F.,.F.,(9,7,9),(0.,0.932550193489,1.),.UNSPECIFIED.);
#3692 = CARTESIAN_POINT('',(3.716855985078,3.365389));
#3693 = CARTESIAN_POINT('',(3.774016526884,2.97522730254));
#3694 = CARTESIAN_POINT('',(3.830300433286,2.584306298408));
#3695 = CARTESIAN_POINT('',(3.885825057493,2.192721289272));
#3696 = CARTESIAN_POINT('',(3.940690279705,1.800564730893));
#3697 = CARTESIAN_POINT('',(3.994985234839,1.407922165386));
#3698 = CARTESIAN_POINT('',(4.048796213015,1.01487197804));
#3699 = CARTESIAN_POINT('',(4.102207330463,0.621482967053));
#3700 = CARTESIAN_POINT('',(4.159166174475,0.199347050404));
#3701 = CARTESIAN_POINT('',(4.163006491262,0.170872688068));
#3702 = CARTESIAN_POINT('',(4.166845300877,0.1423969805));
#3703 = CARTESIAN_POINT('',(4.170682643003,0.113919959528));
#3704 = CARTESIAN_POINT('',(4.174518557962,8.544165760439E-02));
#3705 = CARTESIAN_POINT('',(4.178353086712,5.69621078052E-02));
#3706 = CARTESIAN_POINT('',(4.182186270849,2.848134382946E-02));
#3707 = CARTESIAN_POINT('',(4.186018152602,-6.E-07));
#3708 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3709 = PCURVE('',#3490,#3710);
#3710 = DEFINITIONAL_REPRESENTATION('',(#3711),#3728);
#3711 = B_SPLINE_CURVE_WITH_KNOTS('',8,(#3712,#3713,#3714,#3715,#3716,
    #3717,#3718,#3719,#3720,#3721,#3722,#3723,#3724,#3725,#3726,#3727),
  .UNSPECIFIED.,.F.,.F.,(9,7,9),(0.,0.932550193489,1.),.UNSPECIFIED.);
#3712 = CARTESIAN_POINT('',(2.566329322101,7.));
#3713 = CARTESIAN_POINT('',(2.589651204149,6.60983830254));
#3714 = CARTESIAN_POINT('',(2.611987941313,6.218917298407));
#3715 = CARTESIAN_POINT('',(2.633279306228,5.827332289272));
#3716 = CARTESIAN_POINT('',(2.653408015044,5.435175730892));
#3717 = CARTESIAN_POINT('',(2.672212847756,5.042533165386));
#3718 = CARTESIAN_POINT('',(2.689384481806,4.64948297804));
#3719 = CARTESIAN_POINT('',(2.704509345944,4.256093967053));
#3720 = CARTESIAN_POINT('',(2.717430389268,3.833958050404));
#3721 = CARTESIAN_POINT('',(2.718285817915,3.805483688068));
#3722 = CARTESIAN_POINT('',(2.719124765522,3.7770079805));
#3723 = CARTESIAN_POINT('',(2.719946824475,3.748530959528));
#3724 = CARTESIAN_POINT('',(2.720751567356,3.720052657604));
#3725 = CARTESIAN_POINT('',(2.721538546948,3.691573107805));
#3726 = CARTESIAN_POINT('',(2.722307296233,3.66309234383));
#3727 = CARTESIAN_POINT('',(2.723057328392,3.6346104));
#3728 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3729 = ORIENTED_EDGE('',*,*,#3290,.T.);
#3730 = ORIENTED_EDGE('',*,*,#3119,.T.);
#3731 = ADVANCED_FACE('',(#3732),#2991,.T.);
#3732 = FACE_BOUND('',#3733,.T.);
#3733 = EDGE_LOOP('',(#3734,#3863,#3892,#3911,#3912));
#3734 = ORIENTED_EDGE('',*,*,#3735,.T.);
#3735 = EDGE_CURVE('',#3573,#3736,#3738,.T.);
#3736 = VERTEX_POINT('',#3737);
#3737 = CARTESIAN_POINT('',(76.778961021497,109.22128204048,19.973447));
#3738 = SURFACE_CURVE('',#3739,(#3756,#3807),.PCURVE_S1.);
#3739 = B_SPLINE_CURVE_WITH_KNOTS('',4,(#3740,#3741,#3742,#3743,#3744,
    #3745,#3746,#3747,#3748,#3749,#3750,#3751,#3752,#3753,#3754,#3755),
  .UNSPECIFIED.,.F.,.F.,(5,3,3,2,3,5),(0.,0.477370237872,0.959849059199,
    1.,1.359211381527,1.719867156647),.UNSPECIFIED.);
#3740 = CARTESIAN_POINT('',(77.522760674567,110.8266515,21.355218));
#3741 = CARTESIAN_POINT('',(77.494497424994,110.70454788224,
    21.259833160369));
#3742 = CARTESIAN_POINT('',(77.461658134434,110.58419179512,
    21.164190868329));
#3743 = CARTESIAN_POINT('',(77.424388604241,110.46569299944,
    21.06835711817));
#3744 = CARTESIAN_POINT('',(77.340751873784,110.23134950642,
    20.875413237024));
#3745 = CARTESIAN_POINT('',(77.294291420121,110.11554717679,
    20.778300794301));
#3746 = CARTESIAN_POINT('',(77.243519192212,110.00183419302,
    20.681129924306));
#3747 = CARTESIAN_POINT('',(77.183913891045,109.88101588833,
    20.575893151606));
#3748 = CARTESIAN_POINT('',(77.179305263829,109.87174911719,
    20.567808486992));
#3749 = CARTESIAN_POINT('',(77.133172741107,109.7797273861,
    20.487395930587));
#3750 = CARTESIAN_POINT('',(77.084131779488,109.6892070709,
    20.407005531045));
#3751 = CARTESIAN_POINT('',(77.037690173016,109.60903805256,
    20.334747784078));
#3752 = CARTESIAN_POINT('',(76.939954329017,109.45095529734,
    20.190119881309));
#3753 = CARTESIAN_POINT('',(76.88864118712,109.3730520621,
    20.117750626958));
#3754 = CARTESIAN_POINT('',(76.834983912989,109.29648151402,
    20.045509945643));
#3755 = CARTESIAN_POINT('',(76.778961021496,109.22128204048,
    19.973447000001));
#3756 = PCURVE('',#2991,#3757);
#3757 = DEFINITIONAL_REPRESENTATION('',(#3758),#3806);
#3758 = B_SPLINE_CURVE_WITH_KNOTS('',3,(#3759,#3760,#3761,#3762,#3763,
    #3764,#3765,#3766,#3767,#3768,#3769,#3770,#3771,#3772,#3773,#3774,
    #3775,#3776,#3777,#3778,#3779,#3780,#3781,#3782,#3783,#3784,#3785,
    #3786,#3787,#3788,#3789,#3790,#3791,#3792,#3793,#3794,#3795,#3796,
    #3797,#3798,#3799,#3800,#3801,#3802,#3803,#3804,#3805),
  .UNSPECIFIED.,.F.,.F.,(4,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1
    ,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,4),(0.,3.90878899238E-02,
    7.81757798476E-02,0.117263669771,0.156351559695,0.195439449619,
    0.234527339543,0.273615229467,0.31270311939,0.351791009314,
    0.390878899238,0.429966789162,0.469054679086,0.508142569009,
    0.547230458933,0.586318348857,0.625406238781,0.664494128705,
    0.703582018628,0.742669908552,0.781757798476,0.8208456884,
    0.859933578324,0.899021468247,0.938109358171,0.977197248095,
    1.016285138019,1.055373027943,1.094460917866,1.13354880779,
    1.172636697714,1.211724587638,1.250812477562,1.289900367485,
    1.328988257409,1.368076147333,1.407164037257,1.446251927181,
    1.485339817104,1.524427707028,1.563515596952,1.602603486876,
    1.6416913768,1.680779266723,1.719867156647),.QUASI_UNIFORM_KNOTS.);
#3759 = CARTESIAN_POINT('',(6.055721805961,1.381771));
#3760 = CARTESIAN_POINT('',(6.052551796097,1.371357302084));
#3761 = CARTESIAN_POINT('',(6.04621594275,1.350523001528));
#3762 = CARTESIAN_POINT('',(6.036724228781,1.31925170729));
#3763 = CARTESIAN_POINT('',(6.027244147825,1.287961440107));
#3764 = CARTESIAN_POINT('',(6.017775266643,1.25665307151));
#3765 = CARTESIAN_POINT('',(6.008317151421,1.225327473734));
#3766 = CARTESIAN_POINT('',(5.998869367435,1.193985519815));
#3767 = CARTESIAN_POINT('',(5.989431478707,1.162628083565));
#3768 = CARTESIAN_POINT('',(5.980003047675,1.131256039571));
#3769 = CARTESIAN_POINT('',(5.97058363487,1.09987026324));
#3770 = CARTESIAN_POINT('',(5.96117279856,1.0684716306));
#3771 = CARTESIAN_POINT('',(5.951770094579,1.037061019069));
#3772 = CARTESIAN_POINT('',(5.942375075468,1.005639304569));
#3773 = CARTESIAN_POINT('',(5.932987292224,0.974207372293));
#3774 = CARTESIAN_POINT('',(5.923606297208,0.942766130251));
#3775 = CARTESIAN_POINT('',(5.914231633229,0.911316458897));
#3776 = CARTESIAN_POINT('',(5.904862841916,0.879859256392));
#3777 = CARTESIAN_POINT('',(5.895499461213,0.848395427553));
#3778 = CARTESIAN_POINT('',(5.886141025579,0.816925886814));
#3779 = CARTESIAN_POINT('',(5.87678706548,0.785451557434));
#3780 = CARTESIAN_POINT('',(5.867437107072,0.753973371711));
#3781 = CARTESIAN_POINT('',(5.858090671848,0.722492270901));
#3782 = CARTESIAN_POINT('',(5.848747276305,0.691009205348));
#3783 = CARTESIAN_POINT('',(5.839406431579,0.659525134032));
#3784 = CARTESIAN_POINT('',(5.830067643271,0.628041026249));
#3785 = CARTESIAN_POINT('',(5.820730410557,0.596557855342));
#3786 = CARTESIAN_POINT('',(5.811394229083,0.565076631821));
#3787 = CARTESIAN_POINT('',(5.802058587311,0.533598376146));
#3788 = CARTESIAN_POINT('',(5.79272296554,0.502124107837));
#3789 = CARTESIAN_POINT('',(5.783386837333,0.470654870604));
#3790 = CARTESIAN_POINT('',(5.774049668729,0.439191727424));
#3791 = CARTESIAN_POINT('',(5.76471091791,0.407735761813));
#3792 = CARTESIAN_POINT('',(5.755370034773,0.376288077648));
#3793 = CARTESIAN_POINT('',(5.746026460494,0.344849798612));
#3794 = CARTESIAN_POINT('',(5.736679627266,0.313422070598));
#3795 = CARTESIAN_POINT('',(5.727328957429,0.282006052637));
#3796 = CARTESIAN_POINT('',(5.71797386512,0.250602954756));
#3797 = CARTESIAN_POINT('',(5.708613752676,0.219213991125));
#3798 = CARTESIAN_POINT('',(5.699248011045,0.18784040482));
#3799 = CARTESIAN_POINT('',(5.689876019674,0.156483474746));
#3800 = CARTESIAN_POINT('',(5.680497145831,0.125144513864));
#3801 = CARTESIAN_POINT('',(5.671110744249,9.38248693739E-02));
#3802 = CARTESIAN_POINT('',(5.661716156131,6.252592374815E-02));
#3803 = CARTESIAN_POINT('',(5.652312710496,3.124909041096E-02));
#3804 = CARTESIAN_POINT('',(5.646037381409,1.041358346458E-02));
#3805 = CARTESIAN_POINT('',(5.642898011383,1.325162202193E-12));
#3806 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3807 = PCURVE('',#3808,#3813);
#3808 = CONICAL_SURFACE('',#3809,0.275,0.523598775598);
#3809 = AXIS2_PLACEMENT_3D('',#3810,#3811,#3812);
#3810 = CARTESIAN_POINT('',(73.317494,109.853261,14.355218));
#3811 = DIRECTION('',(0.,0.,1.));
#3812 = DIRECTION('',(1.,0.,0.));
#3813 = DEFINITIONAL_REPRESENTATION('',(#3814),#3862);
#3814 = B_SPLINE_CURVE_WITH_KNOTS('',3,(#3815,#3816,#3817,#3818,#3819,
    #3820,#3821,#3822,#3823,#3824,#3825,#3826,#3827,#3828,#3829,#3830,
    #3831,#3832,#3833,#3834,#3835,#3836,#3837,#3838,#3839,#3840,#3841,
    #3842,#3843,#3844,#3845,#3846,#3847,#3848,#3849,#3850,#3851,#3852,
    #3853,#3854,#3855,#3856,#3857,#3858,#3859,#3860,#3861),
  .UNSPECIFIED.,.F.,.F.,(4,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1
    ,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,4),(0.,3.90878899238E-02,
    7.81757798476E-02,0.117263669771,0.156351559695,0.195439449619,
    0.234527339543,0.273615229467,0.31270311939,0.351791009314,
    0.390878899238,0.429966789162,0.469054679086,0.508142569009,
    0.547230458933,0.586318348857,0.625406238781,0.664494128705,
    0.703582018628,0.742669908552,0.781757798476,0.8208456884,
    0.859933578324,0.899021468247,0.938109358171,0.977197248095,
    1.016285138019,1.055373027943,1.094460917866,1.13354880779,
    1.172636697714,1.211724587638,1.250812477562,1.289900367485,
    1.328988257409,1.368076147333,1.407164037257,1.446251927181,
    1.485339817104,1.524427707028,1.563515596952,1.602603486876,
    1.6416913768,1.680779266723,1.719867156647),.QUASI_UNIFORM_KNOTS.);
#3815 = CARTESIAN_POINT('',(6.510648808398,7.));
#3816 = CARTESIAN_POINT('',(6.50780120962,6.989586302014));
#3817 = CARTESIAN_POINT('',(6.50209920592,6.968752000565));
#3818 = CARTESIAN_POINT('',(6.493525042943,6.937480704112));
#3819 = CARTESIAN_POINT('',(6.484928987288,6.906190435532));
#3820 = CARTESIAN_POINT('',(6.476310290598,6.87488206697));
#3821 = CARTESIAN_POINT('',(6.467668195225,6.84355647071));
#3822 = CARTESIAN_POINT('',(6.459001932484,6.812214519321));
#3823 = CARTESIAN_POINT('',(6.450310722376,6.780857085841));
#3824 = CARTESIAN_POINT('',(6.441593772903,6.749485043974));
#3825 = CARTESIAN_POINT('',(6.432850279488,6.718099268371));
#3826 = CARTESIAN_POINT('',(6.424079424275,6.686700634776));
#3827 = CARTESIAN_POINT('',(6.415280375696,6.655290020798));
#3828 = CARTESIAN_POINT('',(6.406452287045,6.623868304503));
#3829 = CARTESIAN_POINT('',(6.397594298632,6.592436371322));
#3830 = CARTESIAN_POINT('',(6.388705541232,6.560995123742));
#3831 = CARTESIAN_POINT('',(6.379785121473,6.529545448687));
#3832 = CARTESIAN_POINT('',(6.37083213316,6.498088245481));
#3833 = CARTESIAN_POINT('',(6.361845653577,6.466624419445));
#3834 = CARTESIAN_POINT('',(6.352824743503,6.435154884086));
#3835 = CARTESIAN_POINT('',(6.343768446202,6.403680561033));
#3836 = CARTESIAN_POINT('',(6.334675786673,6.372202380639));
#3837 = CARTESIAN_POINT('',(6.325545770789,6.340721282434));
#3838 = CARTESIAN_POINT('',(6.316377384483,6.309238215729));
#3839 = CARTESIAN_POINT('',(6.307169592765,6.277754140035));
#3840 = CARTESIAN_POINT('',(6.297921339212,6.246270026357));
#3841 = CARTESIAN_POINT('',(6.288631543649,6.214786855407));
#3842 = CARTESIAN_POINT('',(6.279299108716,6.183305631704));
#3843 = CARTESIAN_POINT('',(6.269922912348,6.15182737207));
#3844 = CARTESIAN_POINT('',(6.260501804793,6.120353101949));
#3845 = CARTESIAN_POINT('',(6.251034612613,6.088883865813));
#3846 = CARTESIAN_POINT('',(6.241520136558,6.057420726072));
#3847 = CARTESIAN_POINT('',(6.231957150676,6.025964764387));
#3848 = CARTESIAN_POINT('',(6.222344401087,5.994517082513));
#3849 = CARTESIAN_POINT('',(6.212680604679,5.963078803032));
#3850 = CARTESIAN_POINT('',(6.202964448306,5.931651071337));
#3851 = CARTESIAN_POINT('',(6.193194585878,5.900235053157));
#3852 = CARTESIAN_POINT('',(6.18336964399,5.868831950596));
#3853 = CARTESIAN_POINT('',(6.173488211725,5.837442983113));
#3854 = CARTESIAN_POINT('',(6.16354884338,5.806069397543));
#3855 = CARTESIAN_POINT('',(6.153550058231,5.774712472185));
#3856 = CARTESIAN_POINT('',(6.143490338476,5.743373517472));
#3857 = CARTESIAN_POINT('',(6.133368128105,5.71205387725));
#3858 = CARTESIAN_POINT('',(6.123181829677,5.680754931608));
#3859 = CARTESIAN_POINT('',(6.112929808332,5.649478093457));
#3860 = CARTESIAN_POINT('',(6.106050181951,5.628642583934));
#3861 = CARTESIAN_POINT('',(6.102598852716,5.618229000001));
#3862 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3863 = ORIENTED_EDGE('',*,*,#3864,.T.);
#3864 = EDGE_CURVE('',#3736,#3865,#3867,.T.);
#3865 = VERTEX_POINT('',#3866);
#3866 = CARTESIAN_POINT('',(77.604133408892,112.30648,19.973447));
#3867 = SURFACE_CURVE('',#3868,(#3873,#3880),.PCURVE_S1.);
#3868 = CIRCLE('',#3869,4.316451884327);
#3869 = AXIS2_PLACEMENT_3D('',#3870,#3871,#3872);
#3870 = CARTESIAN_POINT('',(73.317494,111.800042,19.973447));
#3871 = DIRECTION('',(-0.,0.,1.));
#3872 = DIRECTION('',(0.801924153045,-0.597425855453,0.));
#3873 = PCURVE('',#2991,#3874);
#3874 = DEFINITIONAL_REPRESENTATION('',(#3875),#3879);
#3875 = LINE('',#3876,#3877);
#3876 = CARTESIAN_POINT('',(5.642898011383,0.));
#3877 = VECTOR('',#3878,1.);
#3878 = DIRECTION('',(1.,0.));
#3879 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3880 = PCURVE('',#3881,#3886);
#3881 = PLANE('',#3882);
#3882 = AXIS2_PLACEMENT_3D('',#3883,#3884,#3885);
#3883 = CARTESIAN_POINT('',(73.317494,111.800042,19.973447));
#3884 = DIRECTION('',(0.,0.,1.));
#3885 = DIRECTION('',(1.,0.,0.));
#3886 = DEFINITIONAL_REPRESENTATION('',(#3887),#3891);
#3887 = CIRCLE('',#3888,4.316451884327);
#3888 = AXIS2_PLACEMENT_2D('',#3889,#3890);
#3889 = CARTESIAN_POINT('',(0.,0.));
#3890 = DIRECTION('',(0.801924153045,-0.597425855453));
#3891 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3892 = ORIENTED_EDGE('',*,*,#3893,.T.);
#3893 = EDGE_CURVE('',#3865,#2971,#3894,.T.);
#3894 = SURFACE_CURVE('',#3895,(#3899,#3905),.PCURVE_S1.);
#3895 = LINE('',#3896,#3897);
#3896 = CARTESIAN_POINT('',(77.604133408892,112.30648,19.973447));
#3897 = VECTOR('',#3898,1.);
#3898 = DIRECTION('',(0.,0.,1.));
#3899 = PCURVE('',#2991,#3900);
#3900 = DEFINITIONAL_REPRESENTATION('',(#3901),#3904);
#3901 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#3902,#3903),.UNSPECIFIED.,.F.,
  .F.,(2,2),(0.,17.526553),.PIECEWISE_BEZIER_KNOTS.);
#3902 = CARTESIAN_POINT('',(6.40078355319,0.));
#3903 = CARTESIAN_POINT('',(6.40078355319,17.526553));
#3904 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3905 = PCURVE('',#3019,#3906);
#3906 = DEFINITIONAL_REPRESENTATION('',(#3907),#3910);
#3907 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#3908,#3909),.UNSPECIFIED.,.F.,
  .F.,(2,2),(-5.999999999062E-07,17.5265536),.PIECEWISE_BEZIER_KNOTS.);
#3908 = CARTESIAN_POINT('',(4.686639408892,-27.5265536));
#3909 = CARTESIAN_POINT('',(4.686639408892,-9.9999994));
#3910 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3911 = ORIENTED_EDGE('',*,*,#2970,.T.);
#3912 = ORIENTED_EDGE('',*,*,#3601,.F.);
#3913 = ADVANCED_FACE('',(#3914),#3107,.T.);
#3914 = FACE_BOUND('',#3915,.T.);
#3915 = EDGE_LOOP('',(#3916,#3937,#3966,#4013,#4014));
#3916 = ORIENTED_EDGE('',*,*,#3917,.F.);
#3917 = EDGE_CURVE('',#3918,#3059,#3920,.T.);
#3918 = VERTEX_POINT('',#3919);
#3919 = CARTESIAN_POINT('',(69.145248888396,112.906445,19.973447));
#3920 = SURFACE_CURVE('',#3921,(#3925,#3931),.PCURVE_S1.);
#3921 = LINE('',#3922,#3923);
#3922 = CARTESIAN_POINT('',(69.145248888396,112.906445,19.973447));
#3923 = VECTOR('',#3924,1.);
#3924 = DIRECTION('',(0.,0.,1.));
#3925 = PCURVE('',#3107,#3926);
#3926 = DEFINITIONAL_REPRESENTATION('',(#3927),#3930);
#3927 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#3928,#3929),.UNSPECIFIED.,.F.,
  .F.,(2,2),(0.,17.526553),.PIECEWISE_BEZIER_KNOTS.);
#3928 = CARTESIAN_POINT('',(2.882377164892,0.));
#3929 = CARTESIAN_POINT('',(2.882377164892,17.526553));
#3930 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3931 = PCURVE('',#3074,#3932);
#3932 = DEFINITIONAL_REPRESENTATION('',(#3933),#3936);
#3933 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#3934,#3935),.UNSPECIFIED.,.F.,
  .F.,(2,2),(-5.999999999062E-07,17.5265536),.PIECEWISE_BEZIER_KNOTS.);
#3934 = CARTESIAN_POINT('',(17.434415888396,-27.5265536));
#3935 = CARTESIAN_POINT('',(17.434415888396,-9.9999994));
#3936 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3937 = ORIENTED_EDGE('',*,*,#3938,.T.);
#3938 = EDGE_CURVE('',#3918,#3939,#3941,.T.);
#3939 = VERTEX_POINT('',#3940);
#3940 = CARTESIAN_POINT('',(69.856026978503,109.22128204048,19.973447));
#3941 = SURFACE_CURVE('',#3942,(#3947,#3954),.PCURVE_S1.);
#3942 = CIRCLE('',#3943,4.316451884327);
#3943 = AXIS2_PLACEMENT_3D('',#3944,#3945,#3946);
#3944 = CARTESIAN_POINT('',(73.317494,111.800042,19.973447));
#3945 = DIRECTION('',(0.,0.,1.));
#3946 = DIRECTION('',(-0.966591363326,0.256322328998,0.));
#3947 = PCURVE('',#3107,#3948);
#3948 = DEFINITIONAL_REPRESENTATION('',(#3949),#3953);
#3949 = LINE('',#3950,#3951);
#3950 = CARTESIAN_POINT('',(2.882377164892,0.));
#3951 = VECTOR('',#3952,1.);
#3952 = DIRECTION('',(1.,0.));
#3953 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3954 = PCURVE('',#3955,#3960);
#3955 = PLANE('',#3956);
#3956 = AXIS2_PLACEMENT_3D('',#3957,#3958,#3959);
#3957 = CARTESIAN_POINT('',(73.317494,111.800042,19.973447));
#3958 = DIRECTION('',(0.,0.,1.));
#3959 = DIRECTION('',(1.,0.,0.));
#3960 = DEFINITIONAL_REPRESENTATION('',(#3961),#3965);
#3961 = CIRCLE('',#3962,4.316451884327);
#3962 = AXIS2_PLACEMENT_2D('',#3963,#3964);
#3963 = CARTESIAN_POINT('',(0.,0.));
#3964 = DIRECTION('',(-0.966591363326,0.256322328998));
#3965 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3966 = ORIENTED_EDGE('',*,*,#3967,.F.);
#3967 = EDGE_CURVE('',#3626,#3939,#3968,.T.);
#3968 = SURFACE_CURVE('',#3969,(#3980,#3994),.PCURVE_S1.);
#3969 = B_SPLINE_CURVE_WITH_KNOTS('',5,(#3970,#3971,#3972,#3973,#3974,
    #3975,#3976,#3977,#3978,#3979),.UNSPECIFIED.,.F.,.F.,(6,4,6),(0.,
    0.559374659597,1.),.UNSPECIFIED.);
#3970 = CARTESIAN_POINT('',(69.112227325433,110.8266515,21.355218000002)
  );
#3971 = CARTESIAN_POINT('',(69.157794940652,110.62978911669,
    21.201433147386));
#3972 = CARTESIAN_POINT('',(69.214512522258,110.43718850318,
    21.047023358459));
#3973 = CARTESIAN_POINT('',(69.281908296224,110.24920151703,
    20.892199386646));
#3974 = CARTESIAN_POINT('',(69.359629953687,110.06613623505,
    20.737179891635));
#3975 = CARTESIAN_POINT('',(69.516715571341,109.74814953209,
    20.460129610493));
#3976 = CARTESIAN_POINT('',(69.592226540774,109.61126136578,
    20.338084959213));
#3977 = CARTESIAN_POINT('',(69.673948323995,109.47772997477,
    20.216188964164));
#3978 = CARTESIAN_POINT('',(69.761854033055,109.34769024715,
    20.094582803133));
#3979 = CARTESIAN_POINT('',(69.856027444954,109.22128141436,19.9734464)
  );
#3980 = PCURVE('',#3107,#3981);
#3981 = DEFINITIONAL_REPRESENTATION('',(#3982),#3993);
#3982 = B_SPLINE_CURVE_WITH_KNOTS('',5,(#3983,#3984,#3985,#3986,#3987,
    #3988,#3989,#3990,#3991,#3992),.UNSPECIFIED.,.F.,.F.,(6,4,6),(0.,
    0.559374659597,1.),.UNSPECIFIED.);
#3983 = CARTESIAN_POINT('',(3.369056154808,1.381771000002));
#3984 = CARTESIAN_POINT('',(3.415869448361,1.227986147386));
#3985 = CARTESIAN_POINT('',(3.462303330857,1.073576358459));
#3986 = CARTESIAN_POINT('',(3.508467616879,0.918752386646));
#3987 = CARTESIAN_POINT('',(3.554466958531,0.763732891635));
#3988 = CARTESIAN_POINT('',(3.636634185572,0.486682610493));
#3989 = CARTESIAN_POINT('',(3.672815220926,0.364637959213));
#3990 = CARTESIAN_POINT('',(3.709035310312,0.242741964164));
#3991 = CARTESIAN_POINT('',(3.745361264413,0.121135803133));
#3992 = CARTESIAN_POINT('',(3.781880130268,-6.E-07));
#3993 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3994 = PCURVE('',#3995,#4000);
#3995 = CONICAL_SURFACE('',#3996,0.275,0.523598775598);
#3996 = AXIS2_PLACEMENT_3D('',#3997,#3998,#3999);
#3997 = CARTESIAN_POINT('',(73.317494,109.853261,14.355218));
#3998 = DIRECTION('',(0.,0.,1.));
#3999 = DIRECTION('',(1.,0.,0.));
#4000 = DEFINITIONAL_REPRESENTATION('',(#4001),#4012);
#4001 = B_SPLINE_CURVE_WITH_KNOTS('',5,(#4002,#4003,#4004,#4005,#4006,
    #4007,#4008,#4009,#4010,#4011),.UNSPECIFIED.,.F.,.F.,(6,4,6),(0.,
    0.559374659597,1.),.UNSPECIFIED.);
#4002 = CARTESIAN_POINT('',(2.914129152371,7.000000000002));
#4003 = CARTESIAN_POINT('',(2.956181211214,6.846215147385));
#4004 = CARTESIAN_POINT('',(2.998851168522,6.691805358459));
#4005 = CARTESIAN_POINT('',(3.042321388927,6.536981386645));
#4006 = CARTESIAN_POINT('',(3.086795396705,6.381961891634));
#4007 = CARTESIAN_POINT('',(3.168588817466,6.104911610493));
#4008 = CARTESIAN_POINT('',(3.205421869066,5.982866959213));
#4009 = CARTESIAN_POINT('',(3.243183416639,5.860970964163));
#4010 = CARTESIAN_POINT('',(3.282031528666,5.739364803133));
#4011 = CARTESIAN_POINT('',(3.32217930691,5.618228399999));
#4012 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#4013 = ORIENTED_EDGE('',*,*,#3625,.T.);
#4014 = ORIENTED_EDGE('',*,*,#3086,.T.);
#4015 = ADVANCED_FACE('',(#4016),#3019,.F.);
#4016 = FACE_BOUND('',#4017,.F.);
#4017 = EDGE_LOOP('',(#4018,#4019,#4020,#4043,#4065,#4095));
#4018 = ORIENTED_EDGE('',*,*,#3893,.T.);
#4019 = ORIENTED_EDGE('',*,*,#3003,.T.);
#4020 = ORIENTED_EDGE('',*,*,#4021,.F.);
#4021 = EDGE_CURVE('',#4022,#3004,#4024,.T.);
#4022 = VERTEX_POINT('',#4023);
#4023 = CARTESIAN_POINT('',(72.917494,112.30648,21.355218));
#4024 = SURFACE_CURVE('',#4025,(#4029,#4036),.PCURVE_S1.);
#4025 = LINE('',#4026,#4027);
#4026 = CARTESIAN_POINT('',(72.917494,112.30648,19.973447));
#4027 = VECTOR('',#4028,1.);
#4028 = DIRECTION('',(0.,0.,1.));
#4029 = PCURVE('',#3019,#4030);
#4030 = DEFINITIONAL_REPRESENTATION('',(#4031),#4035);
#4031 = LINE('',#4032,#4033);
#4032 = CARTESIAN_POINT('',(0.,-27.526553));
#4033 = VECTOR('',#4034,1.);
#4034 = DIRECTION('',(0.,1.));
#4035 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#4036 = PCURVE('',#3047,#4037);
#4037 = DEFINITIONAL_REPRESENTATION('',(#4038),#4042);
#4038 = LINE('',#4039,#4040);
#4039 = CARTESIAN_POINT('',(0.599965,-27.526553));
#4040 = VECTOR('',#4041,1.);
#4041 = DIRECTION('',(0.,1.));
#4042 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#4043 = ORIENTED_EDGE('',*,*,#4044,.T.);
#4044 = EDGE_CURVE('',#4022,#4045,#4047,.T.);
#4045 = VERTEX_POINT('',#4046);
#4046 = CARTESIAN_POINT('',(76.869039214094,112.30648,21.355218));
#4047 = SURFACE_CURVE('',#4048,(#4052,#4058),.PCURVE_S1.);
#4048 = LINE('',#4049,#4050);
#4049 = CARTESIAN_POINT('',(73.117494,112.30648,21.355218));
#4050 = VECTOR('',#4051,1.);
#4051 = DIRECTION('',(1.,0.,-0.));
#4052 = PCURVE('',#3019,#4053);
#4053 = DEFINITIONAL_REPRESENTATION('',(#4054),#4057);
#4054 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#4055,#4056),.UNSPECIFIED.,.F.,
  .F.,(2,2),(-0.2,4.516451884327),.PIECEWISE_BEZIER_KNOTS.);
#4055 = CARTESIAN_POINT('',(0.,-26.144782));
#4056 = CARTESIAN_POINT('',(4.716451884327,-26.144782));
#4057 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#4058 = PCURVE('',#3589,#4059);
#4059 = DEFINITIONAL_REPRESENTATION('',(#4060),#4064);
#4060 = LINE('',#4061,#4062);
#4061 = CARTESIAN_POINT('',(-0.2,2.453219));
#4062 = VECTOR('',#4063,1.);
#4063 = DIRECTION('',(1.,0.));
#4064 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#4065 = ORIENTED_EDGE('',*,*,#4066,.T.);
#4066 = EDGE_CURVE('',#4045,#4067,#4069,.T.);
#4067 = VERTEX_POINT('',#4068);
#4068 = CARTESIAN_POINT('',(75.839966573532,112.30648,19.973447));
#4069 = SURFACE_CURVE('',#4070,(#4075,#4082),.PCURVE_S1.);
#4070 = HYPERBOLA('',#4071,4.249099950093,2.453219);
#4071 = AXIS2_PLACEMENT_3D('',#4072,#4073,#4074);
#4072 = CARTESIAN_POINT('',(73.317494,112.30648,13.878904027919));
#4073 = DIRECTION('',(0.,-1.,0.));
#4074 = DIRECTION('',(0.,0.,1.));
#4075 = PCURVE('',#3019,#4076);
#4076 = DEFINITIONAL_REPRESENTATION('',(#4077),#4081);
#4077 = ( BOUNDED_CURVE() B_SPLINE_CURVE(2,(#4078,#4079,#4080),
.UNSPECIFIED.,.F.,.F.) B_SPLINE_CURVE_WITH_KNOTS((3,3),(-1.165402492019,
0.757123609363),.PIECEWISE_BEZIER_KNOTS.) CURVE() 
GEOMETRIC_REPRESENTATION_ITEM() RATIONAL_B_SPLINE_CURVE((1.,
1.498703420344,1.)) REPRESENTATION_ITEM('') );
#4078 = CARTESIAN_POINT('',(3.951545214094,-26.144782));
#4079 = CARTESIAN_POINT('',(0.736480383219,-30.72663135346));
#4080 = CARTESIAN_POINT('',(-1.64,-28.09482685215));
#4081 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#4082 = PCURVE('',#3808,#4083);
#4083 = DEFINITIONAL_REPRESENTATION('',(#4084),#4094);
#4084 = B_SPLINE_CURVE_WITH_KNOTS('',8,(#4085,#4086,#4087,#4088,#4089,
    #4090,#4091,#4092,#4093),.UNSPECIFIED.,.F.,.F.,(9,9),(
    -1.165402492019,-0.901194774861),.PIECEWISE_BEZIER_KNOTS.);
#4085 = CARTESIAN_POINT('',(6.887674133512,7.));
#4086 = CARTESIAN_POINT('',(6.906444161797,6.796842206656));
#4087 = CARTESIAN_POINT('',(6.925797074036,6.603003859495));
#4088 = CARTESIAN_POINT('',(6.945743978087,6.418147318001));
#4089 = CARTESIAN_POINT('',(6.966294621789,6.241956609113));
#4090 = CARTESIAN_POINT('',(6.987457160176,6.07413626336));
#4091 = CARTESIAN_POINT('',(7.009237801113,5.914410266795));
#4092 = CARTESIAN_POINT('',(7.031640390624,5.762521121671));
#4093 = CARTESIAN_POINT('',(7.054665999153,5.618229008797));
#4094 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#4095 = ORIENTED_EDGE('',*,*,#4096,.T.);
#4096 = EDGE_CURVE('',#4067,#3865,#4097,.T.);
#4097 = SURFACE_CURVE('',#4098,(#4102,#4108),.PCURVE_S1.);
#4098 = LINE('',#4099,#4100);
#4099 = CARTESIAN_POINT('',(73.117494,112.30648,19.973447));
#4100 = VECTOR('',#4101,1.);
#4101 = DIRECTION('',(1.,0.,-0.));
#4102 = PCURVE('',#3019,#4103);
#4103 = DEFINITIONAL_REPRESENTATION('',(#4104),#4107);
#4104 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#4105,#4106),.UNSPECIFIED.,.F.,
  .F.,(2,2),(-0.2,4.516451884327),.PIECEWISE_BEZIER_KNOTS.);
#4105 = CARTESIAN_POINT('',(0.,-27.526553));
#4106 = CARTESIAN_POINT('',(4.716451884327,-27.526553));
#4107 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#4108 = PCURVE('',#3881,#4109);
#4109 = DEFINITIONAL_REPRESENTATION('',(#4110),#4113);
#4110 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#4111,#4112),.UNSPECIFIED.,.F.,
  .F.,(2,2),(-0.2,4.516451884327),.PIECEWISE_BEZIER_KNOTS.);
#4111 = CARTESIAN_POINT('',(-0.4,0.506438));
#4112 = CARTESIAN_POINT('',(4.316451884327,0.506438));
#4113 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#4114 = ADVANCED_FACE('',(#4115),#3074,.F.);
#4115 = FACE_BOUND('',#4116,.F.);
#4116 = EDGE_LOOP('',(#4117,#4140,#4141,#4142,#4163,#4198));
#4117 = ORIENTED_EDGE('',*,*,#4118,.F.);
#4118 = EDGE_CURVE('',#3032,#4119,#4121,.T.);
#4119 = VERTEX_POINT('',#4120);
#4120 = CARTESIAN_POINT('',(72.917494,112.906445,21.355218));
#4121 = SURFACE_CURVE('',#4122,(#4126,#4133),.PCURVE_S1.);
#4122 = LINE('',#4123,#4124);
#4123 = CARTESIAN_POINT('',(72.917494,112.906445,37.5));
#4124 = VECTOR('',#4125,1.);
#4125 = DIRECTION('',(0.,0.,-1.));
#4126 = PCURVE('',#3074,#4127);
#4127 = DEFINITIONAL_REPRESENTATION('',(#4128),#4132);
#4128 = LINE('',#4129,#4130);
#4129 = CARTESIAN_POINT('',(21.206661,-10.));
#4130 = VECTOR('',#4131,1.);
#4131 = DIRECTION('',(0.,-1.));
#4132 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#4133 = PCURVE('',#3047,#4134);
#4134 = DEFINITIONAL_REPRESENTATION('',(#4135),#4139);
#4135 = LINE('',#4136,#4137);
#4136 = CARTESIAN_POINT('',(0.,-10.));
#4137 = VECTOR('',#4138,1.);
#4138 = DIRECTION('',(0.,-1.));
#4139 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#4140 = ORIENTED_EDGE('',*,*,#3058,.T.);
#4141 = ORIENTED_EDGE('',*,*,#3917,.F.);
#4142 = ORIENTED_EDGE('',*,*,#4143,.T.);
#4143 = EDGE_CURVE('',#3918,#4144,#4146,.T.);
#4144 = VERTEX_POINT('',#4145);
#4145 = CARTESIAN_POINT('',(71.568431734577,112.906445,19.973447));
#4146 = SURFACE_CURVE('',#4147,(#4151,#4157),.PCURVE_S1.);
#4147 = LINE('',#4148,#4149);
#4148 = CARTESIAN_POINT('',(62.5141635,112.906445,19.973447));
#4149 = VECTOR('',#4150,1.);
#4150 = DIRECTION('',(1.,0.,-0.));
#4151 = PCURVE('',#3074,#4152);
#4152 = DEFINITIONAL_REPRESENTATION('',(#4153),#4156);
#4153 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#4154,#4155),.UNSPECIFIED.,.F.,
  .F.,(2,2),(6.486878615673,10.4033305),.PIECEWISE_BEZIER_KNOTS.);
#4154 = CARTESIAN_POINT('',(17.290209115673,-27.526553));
#4155 = CARTESIAN_POINT('',(21.206661,-27.526553));
#4156 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#4157 = PCURVE('',#3955,#4158);
#4158 = DEFINITIONAL_REPRESENTATION('',(#4159),#4162);
#4159 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#4160,#4161),.UNSPECIFIED.,.F.,
  .F.,(2,2),(6.486878615673,10.4033305),.PIECEWISE_BEZIER_KNOTS.);
#4160 = CARTESIAN_POINT('',(-4.316451884327,1.106403));
#4161 = CARTESIAN_POINT('',(-0.4,1.106403));
#4162 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#4163 = ORIENTED_EDGE('',*,*,#4164,.T.);
#4164 = EDGE_CURVE('',#4144,#4165,#4167,.T.);
#4165 = VERTEX_POINT('',#4166);
#4166 = CARTESIAN_POINT('',(70.266293526111,112.906445,21.355218));
#4167 = SURFACE_CURVE('',#4168,(#4173,#4180),.PCURVE_S1.);
#4168 = HYPERBOLA('',#4169,5.288269812856,3.053184);
#4169 = AXIS2_PLACEMENT_3D('',#4170,#4171,#4172);
#4170 = CARTESIAN_POINT('',(73.317494,112.906445,13.878904027919));
#4171 = DIRECTION('',(0.,-1.,0.));
#4172 = DIRECTION('',(0.,0.,1.));
#4173 = PCURVE('',#3074,#4174);
#4174 = DEFINITIONAL_REPRESENTATION('',(#4175),#4179);
#4175 = ( BOUNDED_CURVE() B_SPLINE_CURVE(2,(#4176,#4177,#4178),
.UNSPECIFIED.,.F.,.F.) B_SPLINE_CURVE_WITH_KNOTS((3,3),(-0.537331753152,
0.880914134662),.PIECEWISE_BEZIER_KNOTS.) CURVE() 
GEOMETRIC_REPRESENTATION_ITEM() RATIONAL_B_SPLINE_CURVE((1.,
1.262141851221,1.)) REPRESENTATION_ITEM('') );
#4176 = CARTESIAN_POINT('',(23.3273271,-27.55085107614));
#4177 = CARTESIAN_POINT('',(21.189042462813,-29.36919973444));
#4178 = CARTESIAN_POINT('',(18.555460526111,-26.144782));
#4179 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#4180 = PCURVE('',#3995,#4181);
#4181 = DEFINITIONAL_REPRESENTATION('',(#4182),#4197);
#4182 = B_SPLINE_CURVE_WITH_KNOTS('',7,(#4183,#4184,#4185,#4186,#4187,
    #4188,#4189,#4190,#4191,#4192,#4193,#4194,#4195,#4196),
  .UNSPECIFIED.,.F.,.F.,(8,6,8),(-0.537331753152,0.171791190755,
    0.880914134662),.UNSPECIFIED.);
#4183 = CARTESIAN_POINT('',(1.057598596934,5.593930923854));
#4184 = CARTESIAN_POINT('',(1.145851884008,5.292018679137));
#4185 = CARTESIAN_POINT('',(1.239226607037,5.062783924112));
#4186 = CARTESIAN_POINT('',(1.33695277501,4.90116615996));
#4187 = CARTESIAN_POINT('',(1.437822122059,4.80393167049));
#4188 = CARTESIAN_POINT('',(1.540088773796,4.769463578094));
#4189 = CARTESIAN_POINT('',(1.641922109829,4.797697054788));
#4190 = CARTESIAN_POINT('',(1.841575308708,4.982667378185));
#4191 = CARTESIAN_POINT('',(1.939394507024,5.139404223408));
#4192 = CARTESIAN_POINT('',(2.033632264825,5.36194309185));
#4193 = CARTESIAN_POINT('',(2.122945503615,5.653449211738));
#4194 = CARTESIAN_POINT('',(2.206571989379,6.018769934232));
#4195 = CARTESIAN_POINT('',(2.284214048909,6.46462896889));
#4196 = CARTESIAN_POINT('',(2.355869555541,7.));
#4197 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#4198 = ORIENTED_EDGE('',*,*,#4199,.T.);
#4199 = EDGE_CURVE('',#4165,#4119,#4200,.T.);
#4200 = SURFACE_CURVE('',#4201,(#4205,#4211),.PCURVE_S1.);
#4201 = LINE('',#4202,#4203);
#4202 = CARTESIAN_POINT('',(62.5141635,112.906445,21.355218));
#4203 = VECTOR('',#4204,1.);
#4204 = DIRECTION('',(1.,0.,-0.));
#4205 = PCURVE('',#3074,#4206);
#4206 = DEFINITIONAL_REPRESENTATION('',(#4207),#4210);
#4207 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#4208,#4209),.UNSPECIFIED.,.F.,
  .F.,(2,2),(6.486878615673,10.4033305),.PIECEWISE_BEZIER_KNOTS.);
#4208 = CARTESIAN_POINT('',(17.290209115673,-26.144782));
#4209 = CARTESIAN_POINT('',(21.206661,-26.144782));
#4210 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#4211 = PCURVE('',#3589,#4212);
#4212 = DEFINITIONAL_REPRESENTATION('',(#4213),#4217);
#4213 = LINE('',#4214,#4215);
#4214 = CARTESIAN_POINT('',(-10.8033305,3.053184));
#4215 = VECTOR('',#4216,1.);
#4216 = DIRECTION('',(1.,0.));
#4217 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#4218 = ADVANCED_FACE('',(#4219),#3047,.F.);
#4219 = FACE_BOUND('',#4220,.F.);
#4220 = EDGE_LOOP('',(#4221,#4241,#4242,#4243));
#4221 = ORIENTED_EDGE('',*,*,#4222,.T.);
#4222 = EDGE_CURVE('',#4119,#4022,#4223,.T.);
#4223 = SURFACE_CURVE('',#4224,(#4228,#4234),.PCURVE_S1.);
#4224 = LINE('',#4225,#4226);
#4225 = CARTESIAN_POINT('',(72.917494,111.379853,21.355218));
#4226 = VECTOR('',#4227,1.);
#4227 = DIRECTION('',(0.,-1.,0.));
#4228 = PCURVE('',#3047,#4229);
#4229 = DEFINITIONAL_REPRESENTATION('',(#4230),#4233);
#4230 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#4231,#4232),.UNSPECIFIED.,.F.,
  .F.,(2,2),(-1.526592,-0.926627),.PIECEWISE_BEZIER_KNOTS.);
#4231 = CARTESIAN_POINT('',(-1.42108547152E-14,-26.144782));
#4232 = CARTESIAN_POINT('',(0.599965,-26.144782));
#4233 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#4234 = PCURVE('',#3589,#4235);
#4235 = DEFINITIONAL_REPRESENTATION('',(#4236),#4240);
#4236 = LINE('',#4237,#4238);
#4237 = CARTESIAN_POINT('',(-0.4,1.526592));
#4238 = VECTOR('',#4239,1.);
#4239 = DIRECTION('',(0.,-1.));
#4240 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#4241 = ORIENTED_EDGE('',*,*,#4021,.T.);
#4242 = ORIENTED_EDGE('',*,*,#3031,.F.);
#4243 = ORIENTED_EDGE('',*,*,#4118,.T.);
#4244 = ADVANCED_FACE('',(#4245),#3244,.F.);
#4245 = FACE_BOUND('',#4246,.F.);
#4246 = EDGE_LOOP('',(#4247,#4270,#4271,#4272));
#4247 = ORIENTED_EDGE('',*,*,#4248,.T.);
#4248 = EDGE_CURVE('',#4249,#3228,#4251,.T.);
#4249 = VERTEX_POINT('',#4250);
#4250 = CARTESIAN_POINT('',(73.592494,109.853261,21.355218));
#4251 = SEAM_CURVE('',#4252,(#4256,#4263),.PCURVE_S1.);
#4252 = LINE('',#4253,#4254);
#4253 = CARTESIAN_POINT('',(73.592494,109.853261,14.355218));
#4254 = VECTOR('',#4255,1.);
#4255 = DIRECTION('',(0.,0.,1.));
#4256 = PCURVE('',#3244,#4257);
#4257 = DEFINITIONAL_REPRESENTATION('',(#4258),#4262);
#4258 = LINE('',#4259,#4260);
#4259 = CARTESIAN_POINT('',(6.28318530718,-0.));
#4260 = VECTOR('',#4261,1.);
#4261 = DIRECTION('',(0.,1.));
#4262 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#4263 = PCURVE('',#3244,#4264);
#4264 = DEFINITIONAL_REPRESENTATION('',(#4265),#4269);
#4265 = LINE('',#4266,#4267);
#4266 = CARTESIAN_POINT('',(0.,-0.));
#4267 = VECTOR('',#4268,1.);
#4268 = DIRECTION('',(0.,1.));
#4269 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#4270 = ORIENTED_EDGE('',*,*,#3227,.F.);
#4271 = ORIENTED_EDGE('',*,*,#4248,.F.);
#4272 = ORIENTED_EDGE('',*,*,#4273,.T.);
#4273 = EDGE_CURVE('',#4249,#4249,#4274,.T.);
#4274 = SURFACE_CURVE('',#4275,(#4280,#4287),.PCURVE_S1.);
#4275 = CIRCLE('',#4276,0.275);
#4276 = AXIS2_PLACEMENT_3D('',#4277,#4278,#4279);
#4277 = CARTESIAN_POINT('',(73.317494,109.853261,21.355218));
#4278 = DIRECTION('',(0.,0.,1.));
#4279 = DIRECTION('',(1.,0.,0.));
#4280 = PCURVE('',#3244,#4281);
#4281 = DEFINITIONAL_REPRESENTATION('',(#4282),#4286);
#4282 = LINE('',#4283,#4284);
#4283 = CARTESIAN_POINT('',(0.,7.));
#4284 = VECTOR('',#4285,1.);
#4285 = DIRECTION('',(1.,0.));
#4286 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#4287 = PCURVE('',#3589,#4288);
#4288 = DEFINITIONAL_REPRESENTATION('',(#4289),#4293);
#4289 = CIRCLE('',#4290,0.275);
#4290 = AXIS2_PLACEMENT_2D('',#4291,#4292);
#4291 = CARTESIAN_POINT('',(0.,0.));
#4292 = DIRECTION('',(1.,0.));
#4293 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#4294 = ADVANCED_FACE('',(#4295),#3275,.F.);
#4295 = FACE_BOUND('',#4296,.F.);
#4296 = EDGE_LOOP('',(#4297,#4320,#4321,#4322));
#4297 = ORIENTED_EDGE('',*,*,#4298,.T.);
#4298 = EDGE_CURVE('',#4299,#3259,#4301,.T.);
#4299 = VERTEX_POINT('',#4300);
#4300 = CARTESIAN_POINT('',(73.592494,111.800042,21.355218));
#4301 = SEAM_CURVE('',#4302,(#4306,#4313),.PCURVE_S1.);
#4302 = LINE('',#4303,#4304);
#4303 = CARTESIAN_POINT('',(73.592494,111.800042,12.973447));
#4304 = VECTOR('',#4305,1.);
#4305 = DIRECTION('',(0.,0.,1.));
#4306 = PCURVE('',#3275,#4307);
#4307 = DEFINITIONAL_REPRESENTATION('',(#4308),#4312);
#4308 = LINE('',#4309,#4310);
#4309 = CARTESIAN_POINT('',(6.28318530718,-0.));
#4310 = VECTOR('',#4311,1.);
#4311 = DIRECTION('',(0.,1.));
#4312 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#4313 = PCURVE('',#3275,#4314);
#4314 = DEFINITIONAL_REPRESENTATION('',(#4315),#4319);
#4315 = LINE('',#4316,#4317);
#4316 = CARTESIAN_POINT('',(0.,-0.));
#4317 = VECTOR('',#4318,1.);
#4318 = DIRECTION('',(0.,1.));
#4319 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#4320 = ORIENTED_EDGE('',*,*,#3258,.F.);
#4321 = ORIENTED_EDGE('',*,*,#4298,.F.);
#4322 = ORIENTED_EDGE('',*,*,#4323,.T.);
#4323 = EDGE_CURVE('',#4299,#4299,#4324,.T.);
#4324 = SURFACE_CURVE('',#4325,(#4330,#4337),.PCURVE_S1.);
#4325 = CIRCLE('',#4326,0.275);
#4326 = AXIS2_PLACEMENT_3D('',#4327,#4328,#4329);
#4327 = CARTESIAN_POINT('',(73.317494,111.800042,21.355218));
#4328 = DIRECTION('',(0.,0.,1.));
#4329 = DIRECTION('',(1.,0.,-0.));
#4330 = PCURVE('',#3275,#4331);
#4331 = DEFINITIONAL_REPRESENTATION('',(#4332),#4336);
#4332 = LINE('',#4333,#4334);
#4333 = CARTESIAN_POINT('',(0.,8.381771));
#4334 = VECTOR('',#4335,1.);
#4335 = DIRECTION('',(1.,0.));
#4336 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#4337 = PCURVE('',#3589,#4338);
#4338 = DEFINITIONAL_REPRESENTATION('',(#4339),#4343);
#4339 = CIRCLE('',#4340,0.275);
#4340 = AXIS2_PLACEMENT_2D('',#4341,#4342);
#4341 = CARTESIAN_POINT('',(0.,1.946781));
#4342 = DIRECTION('',(1.,0.));
#4343 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#4344 = ADVANCED_FACE('',(#4345),#3490,.F.);
#4345 = FACE_BOUND('',#4346,.F.);
#4346 = EDGE_LOOP('',(#4347,#4348,#4349,#4375));
#4347 = ORIENTED_EDGE('',*,*,#3475,.F.);
#4348 = ORIENTED_EDGE('',*,*,#3507,.F.);
#4349 = ORIENTED_EDGE('',*,*,#4350,.F.);
#4350 = EDGE_CURVE('',#3647,#3508,#4351,.T.);
#4351 = SURFACE_CURVE('',#4352,(#4357,#4364),.PCURVE_S1.);
#4352 = CIRCLE('',#4353,2.37344363925);
#4353 = AXIS2_PLACEMENT_3D('',#4354,#4355,#4356);
#4354 = CARTESIAN_POINT('',(73.317494,105.15648,21.355218));
#4355 = DIRECTION('',(0.,0.,-1.));
#4356 = DIRECTION('',(-0.913685190726,0.406422652232,0.));
#4357 = PCURVE('',#3490,#4358);
#4358 = DEFINITIONAL_REPRESENTATION('',(#4359),#4363);
#4359 = LINE('',#4360,#4361);
#4360 = CARTESIAN_POINT('',(2.723057312592,3.634611));
#4361 = VECTOR('',#4362,1.);
#4362 = DIRECTION('',(-1.,-0.));
#4363 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#4364 = PCURVE('',#3589,#4365);
#4365 = DEFINITIONAL_REPRESENTATION('',(#4366),#4374);
#4366 = ( BOUNDED_CURVE() B_SPLINE_CURVE(2,(#4367,#4368,#4369,#4370,
#4371,#4372,#4373),.UNSPECIFIED.,.T.,.F.) B_SPLINE_CURVE_WITH_KNOTS((1,2
    ,2,2,2,1),(-2.094395102393,0.,2.094395102393,4.188790204786,
6.28318530718,8.377580409573),.UNSPECIFIED.) CURVE() 
GEOMETRIC_REPRESENTATION_ITEM() RATIONAL_B_SPLINE_CURVE((1.,0.5,1.,0.5,
1.,0.5,1.)) REPRESENTATION_ITEM('') );
#4367 = CARTESIAN_POINT('',(-2.168580304206,-3.732159741212));
#4368 = CARTESIAN_POINT('',(-0.497807273924,2.393152596527E-02));
#4369 = CARTESIAN_POINT('',(1.919676667244,-3.301045995805));
#4370 = CARTESIAN_POINT('',(4.337160608411,-6.626023517576));
#4371 = CARTESIAN_POINT('',(0.248903636962,-7.057137262983));
#4372 = CARTESIAN_POINT('',(-3.839353334487,-7.48825100839));
#4373 = CARTESIAN_POINT('',(-2.168580304206,-3.732159741212));
#4374 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#4375 = ORIENTED_EDGE('',*,*,#3670,.F.);
#4376 = ADVANCED_FACE('',(#4377,#4428,#4431),#3589,.F.);
#4377 = FACE_BOUND('',#4378,.F.);
#4378 = EDGE_LOOP('',(#4379,#4400,#4401,#4402,#4403,#4425,#4426,#4427));
#4379 = ORIENTED_EDGE('',*,*,#4380,.T.);
#4380 = EDGE_CURVE('',#3573,#4045,#4381,.T.);
#4381 = SURFACE_CURVE('',#4382,(#4387,#4394),.PCURVE_S1.);
#4382 = CIRCLE('',#4383,4.316451884327);
#4383 = AXIS2_PLACEMENT_3D('',#4384,#4385,#4386);
#4384 = CARTESIAN_POINT('',(73.317494,109.853261,21.355218));
#4385 = DIRECTION('',(0.,0.,1.));
#4386 = DIRECTION('',(1.,0.,0.));
#4387 = PCURVE('',#3589,#4388);
#4388 = DEFINITIONAL_REPRESENTATION('',(#4389),#4393);
#4389 = CIRCLE('',#4390,4.316451884327);
#4390 = AXIS2_PLACEMENT_2D('',#4391,#4392);
#4391 = CARTESIAN_POINT('',(0.,0.));
#4392 = DIRECTION('',(1.,0.));
#4393 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#4394 = PCURVE('',#3808,#4395);
#4395 = DEFINITIONAL_REPRESENTATION('',(#4396),#4399);
#4396 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#4397,#4398),.UNSPECIFIED.,.F.,
  .F.,(2,2),(0.227463501218,0.604488826333),.PIECEWISE_BEZIER_KNOTS.);
#4397 = CARTESIAN_POINT('',(6.510648808398,7.));
#4398 = CARTESIAN_POINT('',(6.887674133512,7.));
#4399 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#4400 = ORIENTED_EDGE('',*,*,#4044,.F.);
#4401 = ORIENTED_EDGE('',*,*,#4222,.F.);
#4402 = ORIENTED_EDGE('',*,*,#4199,.F.);
#4403 = ORIENTED_EDGE('',*,*,#4404,.T.);
#4404 = EDGE_CURVE('',#4165,#3626,#4405,.T.);
#4405 = SURFACE_CURVE('',#4406,(#4411,#4418),.PCURVE_S1.);
#4406 = CIRCLE('',#4407,4.316451884327);
#4407 = AXIS2_PLACEMENT_3D('',#4408,#4409,#4410);
#4408 = CARTESIAN_POINT('',(73.317494,109.853261,21.355218));
#4409 = DIRECTION('',(0.,0.,1.));
#4410 = DIRECTION('',(1.,0.,0.));
#4411 = PCURVE('',#3589,#4412);
#4412 = DEFINITIONAL_REPRESENTATION('',(#4413),#4417);
#4413 = CIRCLE('',#4414,4.316451884327);
#4414 = AXIS2_PLACEMENT_2D('',#4415,#4416);
#4415 = CARTESIAN_POINT('',(0.,0.));
#4416 = DIRECTION('',(1.,0.));
#4417 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#4418 = PCURVE('',#3995,#4419);
#4419 = DEFINITIONAL_REPRESENTATION('',(#4420),#4424);
#4420 = LINE('',#4421,#4422);
#4421 = CARTESIAN_POINT('',(0.,7.));
#4422 = VECTOR('',#4423,1.);
#4423 = DIRECTION('',(1.,0.));
#4424 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#4425 = ORIENTED_EDGE('',*,*,#3646,.T.);
#4426 = ORIENTED_EDGE('',*,*,#4350,.T.);
#4427 = ORIENTED_EDGE('',*,*,#3572,.T.);
#4428 = FACE_BOUND('',#4429,.F.);
#4429 = EDGE_LOOP('',(#4430));
#4430 = ORIENTED_EDGE('',*,*,#4273,.F.);
#4431 = FACE_BOUND('',#4432,.F.);
#4432 = EDGE_LOOP('',(#4433));
#4433 = ORIENTED_EDGE('',*,*,#4323,.F.);
#4434 = ADVANCED_FACE('',(#4435),#3808,.F.);
#4435 = FACE_BOUND('',#4436,.F.);
#4436 = EDGE_LOOP('',(#4437,#4463,#4464,#4465));
#4437 = ORIENTED_EDGE('',*,*,#4438,.F.);
#4438 = EDGE_CURVE('',#4067,#3736,#4439,.T.);
#4439 = SURFACE_CURVE('',#4440,(#4445,#4452),.PCURVE_S1.);
#4440 = CIRCLE('',#4441,3.518686025519);
#4441 = AXIS2_PLACEMENT_3D('',#4442,#4443,#4444);
#4442 = CARTESIAN_POINT('',(73.317494,109.853261,19.973447));
#4443 = DIRECTION('',(0.,3.330824301149E-14,-1.));
#4444 = DIRECTION('',(0.716879129095,0.697197471502,2.322242280777E-14)
  );
#4445 = PCURVE('',#3808,#4446);
#4446 = DEFINITIONAL_REPRESENTATION('',(#4447),#4451);
#4447 = LINE('',#4448,#4449);
#4448 = CARTESIAN_POINT('',(7.054665987738,5.618229));
#4449 = VECTOR('',#4450,1.);
#4450 = DIRECTION('',(-1.,-0.));
#4451 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#4452 = PCURVE('',#3881,#4453);
#4453 = DEFINITIONAL_REPRESENTATION('',(#4454),#4462);
#4454 = ( BOUNDED_CURVE() B_SPLINE_CURVE(2,(#4455,#4456,#4457,#4458,
#4459,#4460,#4461),.UNSPECIFIED.,.T.,.F.) B_SPLINE_CURVE_WITH_KNOTS((1,2
    ,2,2,2,1),(-2.094395102393,0.,2.094395102393,4.188790204786,
6.28318530718,8.377580409573),.UNSPECIFIED.) CURVE() 
GEOMETRIC_REPRESENTATION_ITEM() RATIONAL_B_SPLINE_CURVE((1.,0.5,1.,0.5,
1.,0.5,1.)) REPRESENTATION_ITEM('') );
#4455 = CARTESIAN_POINT('',(2.522472573532,0.506438));
#4456 = CARTESIAN_POINT('',(6.771572523625,-3.862612658057));
#4457 = CARTESIAN_POINT('',(0.863313688281,-5.357915829028));
#4458 = CARTESIAN_POINT('',(-5.044945147064,-6.853219));
#4459 = CARTESIAN_POINT('',(-3.385786261813,-0.988865170972));
#4460 = CARTESIAN_POINT('',(-1.726627376561,4.875488658057));
#4461 = CARTESIAN_POINT('',(2.522472573532,0.506438));
#4462 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#4463 = ORIENTED_EDGE('',*,*,#4066,.F.);
#4464 = ORIENTED_EDGE('',*,*,#4380,.F.);
#4465 = ORIENTED_EDGE('',*,*,#3735,.T.);
#4466 = ADVANCED_FACE('',(#4467),#3881,.F.);
#4467 = FACE_BOUND('',#4468,.F.);
#4468 = EDGE_LOOP('',(#4469,#4470,#4471));
#4469 = ORIENTED_EDGE('',*,*,#4438,.T.);
#4470 = ORIENTED_EDGE('',*,*,#3864,.T.);
#4471 = ORIENTED_EDGE('',*,*,#4096,.F.);
#4472 = ADVANCED_FACE('',(#4473),#3995,.F.);
#4473 = FACE_BOUND('',#4474,.F.);
#4474 = EDGE_LOOP('',(#4475,#4497,#4498,#4499));
#4475 = ORIENTED_EDGE('',*,*,#4476,.T.);
#4476 = EDGE_CURVE('',#4144,#3939,#4477,.T.);
#4477 = SURFACE_CURVE('',#4478,(#4483,#4490),.PCURVE_S1.);
#4478 = CIRCLE('',#4479,3.518686025519);
#4479 = AXIS2_PLACEMENT_3D('',#4480,#4481,#4482);
#4480 = CARTESIAN_POINT('',(73.317494,109.853261,19.973447));
#4481 = DIRECTION('',(0.,0.,1.));
#4482 = DIRECTION('',(1.,0.,-0.));
#4483 = PCURVE('',#3995,#4484);
#4484 = DEFINITIONAL_REPRESENTATION('',(#4485),#4489);
#4485 = LINE('',#4486,#4487);
#4486 = CARTESIAN_POINT('',(0.,5.618229));
#4487 = VECTOR('',#4488,1.);
#4488 = DIRECTION('',(1.,0.));
#4489 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#4490 = PCURVE('',#3955,#4491);
#4491 = DEFINITIONAL_REPRESENTATION('',(#4492),#4496);
#4492 = CIRCLE('',#4493,3.518686025519);
#4493 = AXIS2_PLACEMENT_2D('',#4494,#4495);
#4494 = CARTESIAN_POINT('',(0.,-1.946781));
#4495 = DIRECTION('',(1.,0.));
#4496 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#4497 = ORIENTED_EDGE('',*,*,#3967,.F.);
#4498 = ORIENTED_EDGE('',*,*,#4404,.F.);
#4499 = ORIENTED_EDGE('',*,*,#4164,.F.);
#4500 = ADVANCED_FACE('',(#4501),#3955,.F.);
#4501 = FACE_BOUND('',#4502,.F.);
#4502 = EDGE_LOOP('',(#4503,#4504,#4505));
#4503 = ORIENTED_EDGE('',*,*,#4143,.F.);
#4504 = ORIENTED_EDGE('',*,*,#3938,.T.);
#4505 = ORIENTED_EDGE('',*,*,#4476,.F.);
#4506 = ( GEOMETRIC_REPRESENTATION_CONTEXT(3) 
GLOBAL_UNCERTAINTY_ASSIGNED_CONTEXT((#4510)) 
GLOBAL_UNIT_ASSIGNED_CONTEXT((#4507,#4508,#4509)) REPRESENTATION_CONTEXT
('Context #1','3D Context with UNIT and UNCERTAINTY') );
#4507 = ( LENGTH_UNIT() NAMED_UNIT(*) SI_UNIT(.MILLI.,.METRE.) );
#4508 = ( NAMED_UNIT(*) PLANE_ANGLE_UNIT() SI_UNIT($,.RADIAN.) );
#4509 = ( NAMED_UNIT(*) SI_UNIT($,.STERADIAN.) SOLID_ANGLE_UNIT() );
#4510 = UNCERTAINTY_MEASURE_WITH_UNIT(LENGTH_MEASURE(1.E-07),#4507,
  'distance_accuracy_value','confusion accuracy');
#4511 = CONTEXT_DEPENDENT_SHAPE_REPRESENTATION(#4512,#4514);
#4512 = ( REPRESENTATION_RELATIONSHIP('','',#2509,#10) 
REPRESENTATION_RELATIONSHIP_WITH_TRANSFORMATION(#4513) 
SHAPE_REPRESENTATION_RELATIONSHIP() );
#4513 = ITEM_DEFINED_TRANSFORMATION('','',#11,#19);
#4514 = PRODUCT_DEFINITION_SHAPE('Placement','Placement of an item',
  #4515);
#4515 = NEXT_ASSEMBLY_USAGE_OCCURRENCE('146','=>[0:1:1:3]','',#5,#2504,$
  );
#4516 = PRODUCT_RELATED_PRODUCT_CATEGORY('part',$,(#2506));
#4517 = MECHANICAL_DESIGN_GEOMETRIC_PRESENTATION_REPRESENTATION('',(
    #4518),#4506);
#4518 = STYLED_ITEM('color',(#4519),#2510);
#4519 = PRESENTATION_STYLE_ASSIGNMENT((#4520));
#4520 = SURFACE_STYLE_USAGE(.BOTH.,#4521);
#4521 = SURFACE_SIDE_STYLE('',(#4522));
#4522 = SURFACE_STYLE_FILL_AREA(#4523);
#4523 = FILL_AREA_STYLE('',(#4524));
#4524 = FILL_AREA_STYLE_COLOUR('',#4525);
#4525 = DRAUGHTING_PRE_DEFINED_COLOUR('green');
#4526 = MECHANICAL_DESIGN_GEOMETRIC_PRESENTATION_REPRESENTATION('',(
    #4527),#2491);
#4527 = STYLED_ITEM('color',(#4528),#37);
#4528 = PRESENTATION_STYLE_ASSIGNMENT((#4529));
#4529 = SURFACE_STYLE_USAGE(.BOTH.,#4530);
#4530 = SURFACE_SIDE_STYLE('',(#4531));
#4531 = SURFACE_STYLE_FILL_AREA(#4532);
#4532 = FILL_AREA_STYLE('',(#4533));
#4533 = FILL_AREA_STYLE_COLOUR('',#4534);
#4534 = COLOUR_RGB('',1.,0.647058735019,0.);
ENDSEC;
END-ISO-10303-21;
