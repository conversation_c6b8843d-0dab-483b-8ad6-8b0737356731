ISO-10303-21;
HEADER;
FILE_DESCRIPTION(('Open CASCADE Model'),'2;1');
FILE_NAME('Pile_PC2-P7','2025-08-25T20:08:32',('Author'),('Open CASCADE'
    ),'Open CASCADE STEP processor 7.8','build123d','Unknown');
FILE_SCHEMA(('AUTOMOTIVE_DESIGN { 1 0 10303 214 1 1 1 1 }'));
ENDSEC;
DATA;
#1 = APPLICATION_PROTOCOL_DEFINITION('international standard',
  'automotive_design',2000,#2);
#2 = APPLICATION_CONTEXT(
  'core data for automotive mechanical design processes');
#3 = SHAPE_DEFINITION_REPRESENTATION(#4,#10);
#4 = PRODUCT_DEFINITION_SHAPE('','',#5);
#5 = PRODUCT_DEFINITION('design','',#6,#9);
#6 = PRODUCT_DEFINITION_FORMATION('','',#7);
#7 = PRODUCT('Pile_PC2-P7','Pile_PC2-P7','',(#8));
#8 = PRODUCT_CONTEXT('',#2,'mechanical');
#9 = PRODUCT_DEFINITION_CONTEXT('part definition',#2,'design');
#10 = SHAPE_REPRESENTATION('',(#11,#15,#19,#23),#27);
#11 = AXIS2_PLACEMENT_3D('',#12,#13,#14);
#12 = CARTESIAN_POINT('',(0.,0.,0.));
#13 = DIRECTION('',(0.,0.,1.));
#14 = DIRECTION('',(1.,0.,-0.));
#15 = AXIS2_PLACEMENT_3D('',#16,#17,#18);
#16 = CARTESIAN_POINT('',(53.397033,120.882851,5.887524));
#17 = DIRECTION('',(0.,0.,1.));
#18 = DIRECTION('',(1.,0.,0.));
#19 = AXIS2_PLACEMENT_3D('',#20,#21,#22);
#20 = CARTESIAN_POINT('',(0.,0.,0.));
#21 = DIRECTION('',(0.,0.,1.));
#22 = DIRECTION('',(1.,0.,-0.));
#23 = AXIS2_PLACEMENT_3D('',#24,#25,#26);
#24 = CARTESIAN_POINT('',(0.,0.,0.));
#25 = DIRECTION('',(0.,0.,1.));
#26 = DIRECTION('',(1.,0.,-0.));
#27 = ( GEOMETRIC_REPRESENTATION_CONTEXT(3) 
GLOBAL_UNCERTAINTY_ASSIGNED_CONTEXT((#31)) GLOBAL_UNIT_ASSIGNED_CONTEXT(
(#28,#29,#30)) REPRESENTATION_CONTEXT('Context #1',
  '3D Context with UNIT and UNCERTAINTY') );
#28 = ( LENGTH_UNIT() NAMED_UNIT(*) SI_UNIT(.MILLI.,.METRE.) );
#29 = ( NAMED_UNIT(*) PLANE_ANGLE_UNIT() SI_UNIT($,.RADIAN.) );
#30 = ( NAMED_UNIT(*) SI_UNIT($,.STERADIAN.) SOLID_ANGLE_UNIT() );
#31 = UNCERTAINTY_MEASURE_WITH_UNIT(LENGTH_MEASURE(1.E-07),#28,
  'distance_accuracy_value','confusion accuracy');
#32 = PRODUCT_RELATED_PRODUCT_CATEGORY('part',$,(#7));
#33 = SHAPE_DEFINITION_REPRESENTATION(#34,#40);
#34 = PRODUCT_DEFINITION_SHAPE('','',#35);
#35 = PRODUCT_DEFINITION('design','',#36,#39);
#36 = PRODUCT_DEFINITION_FORMATION('','',#37);
#37 = PRODUCT('PC2-P7_Part1','PC2-P7_Part1','',(#38));
#38 = PRODUCT_CONTEXT('',#2,'mechanical');
#39 = PRODUCT_DEFINITION_CONTEXT('part definition',#2,'design');
#40 = ADVANCED_BREP_SHAPE_REPRESENTATION('',(#11,#41),#139);
#41 = MANIFOLD_SOLID_BREP('',#42);
#42 = CLOSED_SHELL('',(#43,#131,#135));
#43 = ADVANCED_FACE('',(#44),#57,.T.);
#44 = FACE_BOUND('',#45,.T.);
#45 = EDGE_LOOP('',(#46,#80,#103,#130));
#46 = ORIENTED_EDGE('',*,*,#47,.F.);
#47 = EDGE_CURVE('',#48,#48,#50,.T.);
#48 = VERTEX_POINT('',#49);
#49 = CARTESIAN_POINT('',(0.275,-6.73555739531E-17,26.612476));
#50 = SURFACE_CURVE('',#51,(#56,#68),.PCURVE_S1.);
#51 = CIRCLE('',#52,0.275);
#52 = AXIS2_PLACEMENT_3D('',#53,#54,#55);
#53 = CARTESIAN_POINT('',(0.,0.,26.612476));
#54 = DIRECTION('',(0.,0.,1.));
#55 = DIRECTION('',(1.,0.,-0.));
#56 = PCURVE('',#57,#62);
#57 = CYLINDRICAL_SURFACE('',#58,0.275);
#58 = AXIS2_PLACEMENT_3D('',#59,#60,#61);
#59 = CARTESIAN_POINT('',(0.,0.,0.));
#60 = DIRECTION('',(0.,0.,1.));
#61 = DIRECTION('',(1.,0.,-0.));
#62 = DEFINITIONAL_REPRESENTATION('',(#63),#67);
#63 = LINE('',#64,#65);
#64 = CARTESIAN_POINT('',(0.,26.612476));
#65 = VECTOR('',#66,1.);
#66 = DIRECTION('',(1.,0.));
#67 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#68 = PCURVE('',#69,#74);
#69 = PLANE('',#70);
#70 = AXIS2_PLACEMENT_3D('',#71,#72,#73);
#71 = CARTESIAN_POINT('',(0.,0.,26.612476));
#72 = DIRECTION('',(0.,0.,1.));
#73 = DIRECTION('',(1.,0.,-0.));
#74 = DEFINITIONAL_REPRESENTATION('',(#75),#79);
#75 = CIRCLE('',#76,0.275);
#76 = AXIS2_PLACEMENT_2D('',#77,#78);
#77 = CARTESIAN_POINT('',(0.,0.));
#78 = DIRECTION('',(1.,0.));
#79 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#80 = ORIENTED_EDGE('',*,*,#81,.F.);
#81 = EDGE_CURVE('',#82,#48,#84,.T.);
#82 = VERTEX_POINT('',#83);
#83 = CARTESIAN_POINT('',(0.275,-6.73555739531E-17,0.));
#84 = SEAM_CURVE('',#85,(#89,#96),.PCURVE_S1.);
#85 = LINE('',#86,#87);
#86 = CARTESIAN_POINT('',(0.275,-6.73555739531E-17,0.));
#87 = VECTOR('',#88,1.);
#88 = DIRECTION('',(0.,0.,1.));
#89 = PCURVE('',#57,#90);
#90 = DEFINITIONAL_REPRESENTATION('',(#91),#95);
#91 = LINE('',#92,#93);
#92 = CARTESIAN_POINT('',(6.28318530718,-0.));
#93 = VECTOR('',#94,1.);
#94 = DIRECTION('',(0.,1.));
#95 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#96 = PCURVE('',#57,#97);
#97 = DEFINITIONAL_REPRESENTATION('',(#98),#102);
#98 = LINE('',#99,#100);
#99 = CARTESIAN_POINT('',(0.,-0.));
#100 = VECTOR('',#101,1.);
#101 = DIRECTION('',(0.,1.));
#102 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#103 = ORIENTED_EDGE('',*,*,#104,.T.);
#104 = EDGE_CURVE('',#82,#82,#105,.T.);
#105 = SURFACE_CURVE('',#106,(#111,#118),.PCURVE_S1.);
#106 = CIRCLE('',#107,0.275);
#107 = AXIS2_PLACEMENT_3D('',#108,#109,#110);
#108 = CARTESIAN_POINT('',(0.,0.,0.));
#109 = DIRECTION('',(0.,0.,1.));
#110 = DIRECTION('',(1.,0.,-0.));
#111 = PCURVE('',#57,#112);
#112 = DEFINITIONAL_REPRESENTATION('',(#113),#117);
#113 = LINE('',#114,#115);
#114 = CARTESIAN_POINT('',(0.,0.));
#115 = VECTOR('',#116,1.);
#116 = DIRECTION('',(1.,0.));
#117 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#118 = PCURVE('',#119,#124);
#119 = PLANE('',#120);
#120 = AXIS2_PLACEMENT_3D('',#121,#122,#123);
#121 = CARTESIAN_POINT('',(0.,0.,0.));
#122 = DIRECTION('',(0.,0.,1.));
#123 = DIRECTION('',(1.,0.,-0.));
#124 = DEFINITIONAL_REPRESENTATION('',(#125),#129);
#125 = CIRCLE('',#126,0.275);
#126 = AXIS2_PLACEMENT_2D('',#127,#128);
#127 = CARTESIAN_POINT('',(0.,0.));
#128 = DIRECTION('',(1.,0.));
#129 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#130 = ORIENTED_EDGE('',*,*,#81,.T.);
#131 = ADVANCED_FACE('',(#132),#69,.T.);
#132 = FACE_BOUND('',#133,.T.);
#133 = EDGE_LOOP('',(#134));
#134 = ORIENTED_EDGE('',*,*,#47,.T.);
#135 = ADVANCED_FACE('',(#136),#119,.F.);
#136 = FACE_BOUND('',#137,.T.);
#137 = EDGE_LOOP('',(#138));
#138 = ORIENTED_EDGE('',*,*,#104,.F.);
#139 = ( GEOMETRIC_REPRESENTATION_CONTEXT(3) 
GLOBAL_UNCERTAINTY_ASSIGNED_CONTEXT((#143)) GLOBAL_UNIT_ASSIGNED_CONTEXT
((#140,#141,#142)) REPRESENTATION_CONTEXT('Context #1',
  '3D Context with UNIT and UNCERTAINTY') );
#140 = ( LENGTH_UNIT() NAMED_UNIT(*) SI_UNIT(.MILLI.,.METRE.) );
#141 = ( NAMED_UNIT(*) PLANE_ANGLE_UNIT() SI_UNIT($,.RADIAN.) );
#142 = ( NAMED_UNIT(*) SI_UNIT($,.STERADIAN.) SOLID_ANGLE_UNIT() );
#143 = UNCERTAINTY_MEASURE_WITH_UNIT(LENGTH_MEASURE(1.E-07),#140,
  'distance_accuracy_value','confusion accuracy');
#144 = CONTEXT_DEPENDENT_SHAPE_REPRESENTATION(#145,#147);
#145 = ( REPRESENTATION_RELATIONSHIP('','',#40,#10) 
REPRESENTATION_RELATIONSHIP_WITH_TRANSFORMATION(#146) 
SHAPE_REPRESENTATION_RELATIONSHIP() );
#146 = ITEM_DEFINED_TRANSFORMATION('','',#11,#15);
#147 = PRODUCT_DEFINITION_SHAPE('Placement','Placement of an item',#148
  );
#148 = NEXT_ASSEMBLY_USAGE_OCCURRENCE('123','=>[0:1:1:2]','',#5,#35,$);
#149 = PRODUCT_RELATED_PRODUCT_CATEGORY('part',$,(#37));
#150 = SHAPE_DEFINITION_REPRESENTATION(#151,#157);
#151 = PRODUCT_DEFINITION_SHAPE('','',#152);
#152 = PRODUCT_DEFINITION('design','',#153,#156);
#153 = PRODUCT_DEFINITION_FORMATION('','',#154);
#154 = PRODUCT('PC2-P7_Part2','PC2-P7_Part2','',(#155));
#155 = PRODUCT_CONTEXT('',#2,'mechanical');
#156 = PRODUCT_DEFINITION_CONTEXT('part definition',#2,'design');
#157 = ADVANCED_BREP_SHAPE_REPRESENTATION('',(#11,#158),#501);
#158 = MANIFOLD_SOLID_BREP('',#159);
#159 = CLOSED_SHELL('',(#160,#371,#421,#469,#495));
#160 = ADVANCED_FACE('',(#161),#175,.T.);
#161 = FACE_BOUND('',#162,.T.);
#162 = EDGE_LOOP('',(#163,#193,#222,#264,#322,#344,#345));
#163 = ORIENTED_EDGE('',*,*,#164,.T.);
#164 = EDGE_CURVE('',#165,#167,#169,.T.);
#165 = VERTEX_POINT('',#166);
#166 = CARTESIAN_POINT('',(53.672033,120.882851,5.887524));
#167 = VERTEX_POINT('',#168);
#168 = CARTESIAN_POINT('',(57.713484884327,120.882851,12.887524));
#169 = SEAM_CURVE('',#170,(#174,#186),.PCURVE_S1.);
#170 = LINE('',#171,#172);
#171 = CARTESIAN_POINT('',(53.672033,120.882851,5.887524));
#172 = VECTOR('',#173,1.);
#173 = DIRECTION('',(0.5,-1.224646799147E-16,0.866025403784));
#174 = PCURVE('',#175,#180);
#175 = CONICAL_SURFACE('',#176,0.275,0.523598775598);
#176 = AXIS2_PLACEMENT_3D('',#177,#178,#179);
#177 = CARTESIAN_POINT('',(53.397033,120.882851,5.887524));
#178 = DIRECTION('',(0.,0.,1.));
#179 = DIRECTION('',(1.,0.,0.));
#180 = DEFINITIONAL_REPRESENTATION('',(#181),#185);
#181 = LINE('',#182,#183);
#182 = CARTESIAN_POINT('',(6.28318530718,-0.));
#183 = VECTOR('',#184,1.);
#184 = DIRECTION('',(0.,1.));
#185 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#186 = PCURVE('',#175,#187);
#187 = DEFINITIONAL_REPRESENTATION('',(#188),#192);
#188 = LINE('',#189,#190);
#189 = CARTESIAN_POINT('',(0.,-0.));
#190 = VECTOR('',#191,1.);
#191 = DIRECTION('',(0.,1.));
#192 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#193 = ORIENTED_EDGE('',*,*,#194,.F.);
#194 = EDGE_CURVE('',#195,#167,#197,.T.);
#195 = VERTEX_POINT('',#196);
#196 = CARTESIAN_POINT('',(51.268515728977,117.12769839369,12.887524));
#197 = SURFACE_CURVE('',#198,(#203,#210),.PCURVE_S1.);
#198 = CIRCLE('',#199,4.316451884327);
#199 = AXIS2_PLACEMENT_3D('',#200,#201,#202);
#200 = CARTESIAN_POINT('',(53.397033,120.882851,12.887524));
#201 = DIRECTION('',(0.,0.,1.));
#202 = DIRECTION('',(1.,0.,0.));
#203 = PCURVE('',#175,#204);
#204 = DEFINITIONAL_REPRESENTATION('',(#205),#209);
#205 = LINE('',#206,#207);
#206 = CARTESIAN_POINT('',(0.,7.));
#207 = VECTOR('',#208,1.);
#208 = DIRECTION('',(1.,0.));
#209 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#210 = PCURVE('',#211,#216);
#211 = PLANE('',#212);
#212 = AXIS2_PLACEMENT_3D('',#213,#214,#215);
#213 = CARTESIAN_POINT('',(53.397033,120.882851,12.887524));
#214 = DIRECTION('',(0.,0.,1.));
#215 = DIRECTION('',(1.,0.,0.));
#216 = DEFINITIONAL_REPRESENTATION('',(#217),#221);
#217 = CIRCLE('',#218,4.316451884327);
#218 = AXIS2_PLACEMENT_2D('',#219,#220);
#219 = CARTESIAN_POINT('',(0.,0.));
#220 = DIRECTION('',(1.,0.));
#221 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#222 = ORIENTED_EDGE('',*,*,#223,.F.);
#223 = EDGE_CURVE('',#224,#195,#226,.T.);
#224 = VERTEX_POINT('',#225);
#225 = CARTESIAN_POINT('',(51.008936,121.80569,9.84561160017));
#226 = SURFACE_CURVE('',#227,(#232,#252),.PCURVE_S1.);
#227 = HYPERBOLA('',#228,4.041393460347,2.333299602233);
#228 = AXIS2_PLACEMENT_3D('',#229,#230,#231);
#229 = CARTESIAN_POINT('',(51.067317345541,120.75357608694,
    5.411210027919));
#230 = DIRECTION('',(0.998464000178,5.54043351018E-02,0.));
#231 = DIRECTION('',(-0.,0.,1.));
#232 = PCURVE('',#175,#233);
#233 = DEFINITIONAL_REPRESENTATION('',(#234),#251);
#234 = B_SPLINE_CURVE_WITH_KNOTS('',8,(#235,#236,#237,#238,#239,#240,
    #241,#242,#243,#244,#245,#246,#247,#248,#249,#250),.UNSPECIFIED.,.F.
  ,.F.,(9,7,9),(-0.738053919342,0.243785681061,1.225625281465),
  .UNSPECIFIED.);
#235 = CARTESIAN_POINT('',(2.518086013335,4.716682522382));
#236 = CARTESIAN_POINT('',(2.61359927202,4.316455817964));
#237 = CARTESIAN_POINT('',(2.717525450405,4.005623672517));
#238 = CARTESIAN_POINT('',(2.829401141165,3.774999854541));
#239 = CARTESIAN_POINT('',(2.948049104707,3.618270648657));
#240 = CARTESIAN_POINT('',(3.071355236823,3.531552319577));
#241 = CARTESIAN_POINT('',(3.196092860218,3.513178370119));
#242 = CARTESIAN_POINT('',(3.319260447876,3.563649410722));
#243 = CARTESIAN_POINT('',(3.557602687955,3.807887381635));
#244 = CARTESIAN_POINT('',(3.672777866627,4.001654185841));
#245 = CARTESIAN_POINT('',(3.781534608034,4.269872601594));
#246 = CARTESIAN_POINT('',(3.882041279836,4.617645432195));
#247 = CARTESIAN_POINT('',(3.973568140299,5.0525192775));
#248 = CARTESIAN_POINT('',(4.056205999543,5.58479068867));
#249 = CARTESIAN_POINT('',(4.130376693487,6.228045106729));
#250 = CARTESIAN_POINT('',(4.196719548987,7.));
#251 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#252 = PCURVE('',#253,#258);
#253 = PLANE('',#254);
#254 = AXIS2_PLACEMENT_3D('',#255,#256,#257);
#255 = CARTESIAN_POINT('',(51.468253,113.528152,42.5));
#256 = DIRECTION('',(0.998464000178,5.54043351018E-02,0.));
#257 = DIRECTION('',(-5.54043351018E-02,0.998464000178,0.));
#258 = DEFINITIONAL_REPRESENTATION('',(#259),#263);
#259 = ( BOUNDED_CURVE() B_SPLINE_CURVE(2,(#260,#261,#262),
.UNSPECIFIED.,.F.,.F.) B_SPLINE_CURVE_WITH_KNOTS((3,3),(-0.738053919342,
1.225625281465),.PIECEWISE_BEZIER_KNOTS.) CURVE() 
GEOMETRIC_REPRESENTATION_ITEM() RATIONAL_B_SPLINE_CURVE((1.,
1.521991799485,1.)) REPRESENTATION_ITEM('') );
#260 = CARTESIAN_POINT('',(9.119299041703,-31.89579347761));
#261 = CARTESIAN_POINT('',(6.859089183691,-34.35416132975));
#262 = CARTESIAN_POINT('',(3.605083801768,-29.612476));
#263 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#264 = ORIENTED_EDGE('',*,*,#265,.F.);
#265 = EDGE_CURVE('',#266,#224,#268,.T.);
#266 = VERTEX_POINT('',#267);
#267 = CARTESIAN_POINT('',(57.613606422953,121.80603323474,12.887524));
#268 = SURFACE_CURVE('',#269,(#274,#310),.PCURVE_S1.);
#269 = HYPERBOLA('',#270,1.59861899059,0.922963104549);
#270 = AXIS2_PLACEMENT_3D('',#271,#272,#273);
#271 = CARTESIAN_POINT('',(53.396985035001,121.8058141033,5.411210027919
    ));
#272 = DIRECTION('',(5.196848995186E-05,-0.99999999865,0.));
#273 = DIRECTION('',(0.,0.,1.));
#274 = PCURVE('',#175,#275);
#275 = DEFINITIONAL_REPRESENTATION('',(#276),#309);
#276 = B_SPLINE_CURVE_WITH_KNOTS('',7,(#277,#278,#279,#280,#281,#282,
    #283,#284,#285,#286,#287,#288,#289,#290,#291,#292,#293,#294,#295,
    #296,#297,#298,#299,#300,#301,#302,#303,#304,#305,#306,#307,#308),
  .UNSPECIFIED.,.F.,.F.,(8,6,6,6,6,8),(-2.224115605797,-1.112057802899,
    -0.278014450725,0.347518063406,1.285816834601,2.224115605797),
  .UNSPECIFIED.);
#277 = CARTESIAN_POINT('',(0.215540294447,7.));
#278 = CARTESIAN_POINT('',(0.249509606999,5.839742039029));
#279 = CARTESIAN_POINT('',(0.289629482979,4.899620629208));
#280 = CARTESIAN_POINT('',(0.337170338538,4.131810512094));
#281 = CARTESIAN_POINT('',(0.393688639909,3.502081309047));
#282 = CARTESIAN_POINT('',(0.461001707214,2.984937959084));
#283 = CARTESIAN_POINT('',(0.541232692377,2.561293983405));
#284 = CARTESIAN_POINT('',(0.70625138398,1.958695021825));
#285 = CARTESIAN_POINT('',(0.784885267763,1.745045731294));
#286 = CARTESIAN_POINT('',(0.871917157734,1.570015641798));
#287 = CARTESIAN_POINT('',(0.967515168256,1.429167312824));
#288 = CARTESIAN_POINT('',(1.071292698201,1.319269928367));
#289 = CARTESIAN_POINT('',(1.181659355098,1.238123536976));
#290 = CARTESIAN_POINT('',(1.382363682188,1.144254748537));
#291 = CARTESIAN_POINT('',(1.470810639147,1.119498010794));
#292 = CARTESIAN_POINT('',(1.56073139266,1.109689313322));
#293 = CARTESIAN_POINT('',(1.650952484485,1.114606613366));
#294 = CARTESIAN_POINT('',(1.740192039111,1.134313645903));
#295 = CARTESIAN_POINT('',(1.827350974333,1.169161931785));
#296 = CARTESIAN_POINT('',(2.037912344174,1.295787329721));
#297 = CARTESIAN_POINT('',(2.157647849756,1.407316738191));
#298 = CARTESIAN_POINT('',(2.267895898884,1.556630339762));
#299 = CARTESIAN_POINT('',(2.367055010827,1.7475213524));
#300 = CARTESIAN_POINT('',(2.455337838892,1.985517241482));
#301 = CARTESIAN_POINT('',(2.533419173269,2.27825316999));
#302 = CARTESIAN_POINT('',(2.671109037434,2.994215486939));
#303 = CARTESIAN_POINT('',(2.730716222943,3.417441820616));
#304 = CARTESIAN_POINT('',(2.78204063264,3.916419789155));
#305 = CARTESIAN_POINT('',(2.826272165546,4.504523834615));
#306 = CARTESIAN_POINT('',(2.864454461323,5.198783175417));
#307 = CARTESIAN_POINT('',(2.897494688657,6.02103234543));
#308 = CARTESIAN_POINT('',(2.926156296122,7.));
#309 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#310 = PCURVE('',#311,#316);
#311 = PLANE('',#312);
#312 = AXIS2_PLACEMENT_3D('',#313,#314,#315);
#313 = CARTESIAN_POINT('',(51.008936,121.80569,42.5));
#314 = DIRECTION('',(5.196848995186E-05,-0.99999999865,0.));
#315 = DIRECTION('',(0.99999999865,5.196848995186E-05,0.));
#316 = DEFINITIONAL_REPRESENTATION('',(#317),#321);
#317 = ( BOUNDED_CURVE() B_SPLINE_CURVE(2,(#318,#319,#320),
.UNSPECIFIED.,.F.,.F.) B_SPLINE_CURVE_WITH_KNOTS((3,3),(-2.224115605797,
2.224115605797),.PIECEWISE_BEZIER_KNOTS.) CURVE() 
GEOMETRIC_REPRESENTATION_ITEM() RATIONAL_B_SPLINE_CURVE((1.,
4.67673286511,1.)) REPRESENTATION_ITEM('') );
#318 = CARTESIAN_POINT('',(6.604670431872,-29.612476));
#319 = CARTESIAN_POINT('',(2.388049038226,-36.74696608846));
#320 = CARTESIAN_POINT('',(-1.82857235542,-29.612476));
#321 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#322 = ORIENTED_EDGE('',*,*,#323,.F.);
#323 = EDGE_CURVE('',#167,#266,#324,.T.);
#324 = SURFACE_CURVE('',#325,(#330,#337),.PCURVE_S1.);
#325 = CIRCLE('',#326,4.316451884327);
#326 = AXIS2_PLACEMENT_3D('',#327,#328,#329);
#327 = CARTESIAN_POINT('',(53.397033,120.882851,12.887524));
#328 = DIRECTION('',(0.,0.,1.));
#329 = DIRECTION('',(1.,0.,0.));
#330 = PCURVE('',#175,#331);
#331 = DEFINITIONAL_REPRESENTATION('',(#332),#336);
#332 = LINE('',#333,#334);
#333 = CARTESIAN_POINT('',(0.,7.));
#334 = VECTOR('',#335,1.);
#335 = DIRECTION('',(1.,0.));
#336 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#337 = PCURVE('',#211,#338);
#338 = DEFINITIONAL_REPRESENTATION('',(#339),#343);
#339 = CIRCLE('',#340,4.316451884327);
#340 = AXIS2_PLACEMENT_2D('',#341,#342);
#341 = CARTESIAN_POINT('',(0.,0.));
#342 = DIRECTION('',(1.,0.));
#343 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#344 = ORIENTED_EDGE('',*,*,#164,.F.);
#345 = ORIENTED_EDGE('',*,*,#346,.T.);
#346 = EDGE_CURVE('',#165,#165,#347,.T.);
#347 = SURFACE_CURVE('',#348,(#353,#360),.PCURVE_S1.);
#348 = CIRCLE('',#349,0.275);
#349 = AXIS2_PLACEMENT_3D('',#350,#351,#352);
#350 = CARTESIAN_POINT('',(53.397033,120.882851,5.887524));
#351 = DIRECTION('',(0.,0.,1.));
#352 = DIRECTION('',(1.,0.,0.));
#353 = PCURVE('',#175,#354);
#354 = DEFINITIONAL_REPRESENTATION('',(#355),#359);
#355 = LINE('',#356,#357);
#356 = CARTESIAN_POINT('',(0.,0.));
#357 = VECTOR('',#358,1.);
#358 = DIRECTION('',(1.,0.));
#359 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#360 = PCURVE('',#361,#366);
#361 = CYLINDRICAL_SURFACE('',#362,0.275);
#362 = AXIS2_PLACEMENT_3D('',#363,#364,#365);
#363 = CARTESIAN_POINT('',(53.397033,120.882851,5.887524));
#364 = DIRECTION('',(0.,0.,1.));
#365 = DIRECTION('',(1.,0.,0.));
#366 = DEFINITIONAL_REPRESENTATION('',(#367),#370);
#367 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#368,#369),.UNSPECIFIED.,.F.,.F.,
  (2,2),(0.,6.28318530718),.PIECEWISE_BEZIER_KNOTS.);
#368 = CARTESIAN_POINT('',(0.,0.));
#369 = CARTESIAN_POINT('',(6.28318530718,0.));
#370 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#371 = ADVANCED_FACE('',(#372),#361,.F.);
#372 = FACE_BOUND('',#373,.F.);
#373 = EDGE_LOOP('',(#374,#398,#419,#420));
#374 = ORIENTED_EDGE('',*,*,#375,.F.);
#375 = EDGE_CURVE('',#376,#376,#378,.T.);
#376 = VERTEX_POINT('',#377);
#377 = CARTESIAN_POINT('',(53.672033,120.882851,12.887524));
#378 = SURFACE_CURVE('',#379,(#384,#391),.PCURVE_S1.);
#379 = CIRCLE('',#380,0.275);
#380 = AXIS2_PLACEMENT_3D('',#381,#382,#383);
#381 = CARTESIAN_POINT('',(53.397033,120.882851,12.887524));
#382 = DIRECTION('',(0.,0.,1.));
#383 = DIRECTION('',(1.,0.,0.));
#384 = PCURVE('',#361,#385);
#385 = DEFINITIONAL_REPRESENTATION('',(#386),#390);
#386 = LINE('',#387,#388);
#387 = CARTESIAN_POINT('',(0.,7.));
#388 = VECTOR('',#389,1.);
#389 = DIRECTION('',(1.,0.));
#390 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#391 = PCURVE('',#211,#392);
#392 = DEFINITIONAL_REPRESENTATION('',(#393),#397);
#393 = CIRCLE('',#394,0.275);
#394 = AXIS2_PLACEMENT_2D('',#395,#396);
#395 = CARTESIAN_POINT('',(0.,0.));
#396 = DIRECTION('',(1.,0.));
#397 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#398 = ORIENTED_EDGE('',*,*,#399,.F.);
#399 = EDGE_CURVE('',#165,#376,#400,.T.);
#400 = SEAM_CURVE('',#401,(#405,#412),.PCURVE_S1.);
#401 = LINE('',#402,#403);
#402 = CARTESIAN_POINT('',(53.672033,120.882851,5.887524));
#403 = VECTOR('',#404,1.);
#404 = DIRECTION('',(0.,0.,1.));
#405 = PCURVE('',#361,#406);
#406 = DEFINITIONAL_REPRESENTATION('',(#407),#411);
#407 = LINE('',#408,#409);
#408 = CARTESIAN_POINT('',(6.28318530718,-0.));
#409 = VECTOR('',#410,1.);
#410 = DIRECTION('',(0.,1.));
#411 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#412 = PCURVE('',#361,#413);
#413 = DEFINITIONAL_REPRESENTATION('',(#414),#418);
#414 = LINE('',#415,#416);
#415 = CARTESIAN_POINT('',(0.,-0.));
#416 = VECTOR('',#417,1.);
#417 = DIRECTION('',(0.,1.));
#418 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#419 = ORIENTED_EDGE('',*,*,#346,.T.);
#420 = ORIENTED_EDGE('',*,*,#399,.T.);
#421 = ADVANCED_FACE('',(#422,#466),#211,.T.);
#422 = FACE_BOUND('',#423,.T.);
#423 = EDGE_LOOP('',(#424,#425,#426,#447));
#424 = ORIENTED_EDGE('',*,*,#194,.T.);
#425 = ORIENTED_EDGE('',*,*,#323,.T.);
#426 = ORIENTED_EDGE('',*,*,#427,.F.);
#427 = EDGE_CURVE('',#428,#266,#430,.T.);
#428 = VERTEX_POINT('',#429);
#429 = CARTESIAN_POINT('',(51.008936,121.80569,12.887524));
#430 = SURFACE_CURVE('',#431,(#435,#441),.PCURVE_S1.);
#431 = LINE('',#432,#433);
#432 = CARTESIAN_POINT('',(52.202960517501,121.80575205165,12.887524));
#433 = VECTOR('',#434,1.);
#434 = DIRECTION('',(0.99999999865,5.196848995186E-05,-0.));
#435 = PCURVE('',#211,#436);
#436 = DEFINITIONAL_REPRESENTATION('',(#437),#440);
#437 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#438,#439),.UNSPECIFIED.,.F.,.F.,
  (2,2),(-1.194024519113,5.510524374268),.PIECEWISE_BEZIER_KNOTS.);
#438 = CARTESIAN_POINT('',(-2.388097,0.922839));
#439 = CARTESIAN_POINT('',(4.316451884327,0.923187425282));
#440 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#441 = PCURVE('',#311,#442);
#442 = DEFINITIONAL_REPRESENTATION('',(#443),#446);
#443 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#444,#445),.UNSPECIFIED.,.F.,.F.,
  (2,2),(-1.194024519113,5.510524374268),.PIECEWISE_BEZIER_KNOTS.);
#444 = CARTESIAN_POINT('',(-7.105427357601E-15,-29.612476));
#445 = CARTESIAN_POINT('',(6.704548893381,-29.612476));
#446 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#447 = ORIENTED_EDGE('',*,*,#448,.F.);
#448 = EDGE_CURVE('',#195,#428,#449,.T.);
#449 = SURFACE_CURVE('',#450,(#454,#460),.PCURVE_S1.);
#450 = LINE('',#451,#452);
#451 = CARTESIAN_POINT('',(51.26778517277,117.14086404347,12.887524));
#452 = VECTOR('',#453,1.);
#453 = DIRECTION('',(-5.54043351018E-02,0.998464000178,0.));
#454 = PCURVE('',#211,#455);
#455 = DEFINITIONAL_REPRESENTATION('',(#456),#459);
#456 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#457,#458),.UNSPECIFIED.,.F.,.F.,
  (2,2),(-0.575348663244,4.672002150999),.PIECEWISE_BEZIER_KNOTS.);
#457 = CARTESIAN_POINT('',(-2.097371017091,-4.316451884327));
#458 = CARTESIAN_POINT('',(-2.388097,0.922839));
#459 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#460 = PCURVE('',#253,#461);
#461 = DEFINITIONAL_REPRESENTATION('',(#462),#465);
#462 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#463,#464),.UNSPECIFIED.,.F.,.F.,
  (2,2),(-0.575348663244,4.672002150999),.PIECEWISE_BEZIER_KNOTS.);
#463 = CARTESIAN_POINT('',(3.042921041851,-29.612476));
#464 = CARTESIAN_POINT('',(8.290271856093,-29.612476));
#465 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#466 = FACE_BOUND('',#467,.T.);
#467 = EDGE_LOOP('',(#468));
#468 = ORIENTED_EDGE('',*,*,#375,.F.);
#469 = ADVANCED_FACE('',(#470),#311,.F.);
#470 = FACE_BOUND('',#471,.F.);
#471 = EDGE_LOOP('',(#472,#473,#494));
#472 = ORIENTED_EDGE('',*,*,#427,.F.);
#473 = ORIENTED_EDGE('',*,*,#474,.T.);
#474 = EDGE_CURVE('',#428,#224,#475,.T.);
#475 = SURFACE_CURVE('',#476,(#480,#487),.PCURVE_S1.);
#476 = LINE('',#477,#478);
#477 = CARTESIAN_POINT('',(51.008936,121.80569,42.5));
#478 = VECTOR('',#479,1.);
#479 = DIRECTION('',(0.,0.,-1.));
#480 = PCURVE('',#311,#481);
#481 = DEFINITIONAL_REPRESENTATION('',(#482),#486);
#482 = LINE('',#483,#484);
#483 = CARTESIAN_POINT('',(0.,0.));
#484 = VECTOR('',#485,1.);
#485 = DIRECTION('',(0.,-1.));
#486 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#487 = PCURVE('',#253,#488);
#488 = DEFINITIONAL_REPRESENTATION('',(#489),#493);
#489 = LINE('',#490,#491);
#490 = CARTESIAN_POINT('',(8.290271856093,0.));
#491 = VECTOR('',#492,1.);
#492 = DIRECTION('',(0.,-1.));
#493 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#494 = ORIENTED_EDGE('',*,*,#265,.F.);
#495 = ADVANCED_FACE('',(#496),#253,.F.);
#496 = FACE_BOUND('',#497,.F.);
#497 = EDGE_LOOP('',(#498,#499,#500));
#498 = ORIENTED_EDGE('',*,*,#474,.F.);
#499 = ORIENTED_EDGE('',*,*,#448,.F.);
#500 = ORIENTED_EDGE('',*,*,#223,.F.);
#501 = ( GEOMETRIC_REPRESENTATION_CONTEXT(3) 
GLOBAL_UNCERTAINTY_ASSIGNED_CONTEXT((#505)) GLOBAL_UNIT_ASSIGNED_CONTEXT
((#502,#503,#504)) REPRESENTATION_CONTEXT('Context #1',
  '3D Context with UNIT and UNCERTAINTY') );
#502 = ( LENGTH_UNIT() NAMED_UNIT(*) SI_UNIT(.MILLI.,.METRE.) );
#503 = ( NAMED_UNIT(*) PLANE_ANGLE_UNIT() SI_UNIT($,.RADIAN.) );
#504 = ( NAMED_UNIT(*) SI_UNIT($,.STERADIAN.) SOLID_ANGLE_UNIT() );
#505 = UNCERTAINTY_MEASURE_WITH_UNIT(LENGTH_MEASURE(1.E-07),#502,
  'distance_accuracy_value','confusion accuracy');
#506 = CONTEXT_DEPENDENT_SHAPE_REPRESENTATION(#507,#509);
#507 = ( REPRESENTATION_RELATIONSHIP('','',#157,#10) 
REPRESENTATION_RELATIONSHIP_WITH_TRANSFORMATION(#508) 
SHAPE_REPRESENTATION_RELATIONSHIP() );
#508 = ITEM_DEFINED_TRANSFORMATION('','',#11,#19);
#509 = PRODUCT_DEFINITION_SHAPE('Placement','Placement of an item',#510
  );
#510 = NEXT_ASSEMBLY_USAGE_OCCURRENCE('124','=>[0:1:1:3]','',#5,#152,$);
#511 = PRODUCT_RELATED_PRODUCT_CATEGORY('part',$,(#154));
#512 = SHAPE_DEFINITION_REPRESENTATION(#513,#519);
#513 = PRODUCT_DEFINITION_SHAPE('','',#514);
#514 = PRODUCT_DEFINITION('design','',#515,#518);
#515 = PRODUCT_DEFINITION_FORMATION('','',#516);
#516 = PRODUCT('PC2-P7_Part3','PC2-P7_Part3','',(#517));
#517 = PRODUCT_CONTEXT('',#2,'mechanical');
#518 = PRODUCT_DEFINITION_CONTEXT('part definition',#2,'design');
#519 = ADVANCED_BREP_SHAPE_REPRESENTATION('',(#11,#520),#850);
#520 = MANIFOLD_SOLID_BREP('',#521);
#521 = CLOSED_SHELL('',(#522,#644,#719,#766,#816,#823));
#522 = ADVANCED_FACE('',(#523),#538,.T.);
#523 = FACE_BOUND('',#524,.T.);
#524 = EDGE_LOOP('',(#525,#561,#587,#620));
#525 = ORIENTED_EDGE('',*,*,#526,.T.);
#526 = EDGE_CURVE('',#527,#529,#531,.T.);
#527 = VERTEX_POINT('',#528);
#528 = CARTESIAN_POINT('',(51.268515728977,117.12769839369,12.887524));
#529 = VERTEX_POINT('',#530);
#530 = CARTESIAN_POINT('',(57.613606422953,121.80603323474,12.887524));
#531 = SURFACE_CURVE('',#532,(#537,#549),.PCURVE_S1.);
#532 = CIRCLE('',#533,4.316451884327);
#533 = AXIS2_PLACEMENT_3D('',#534,#535,#536);
#534 = CARTESIAN_POINT('',(53.397033,120.882851,12.887524));
#535 = DIRECTION('',(0.,0.,1.));
#536 = DIRECTION('',(-0.493117339904,-0.869962809025,0.));
#537 = PCURVE('',#538,#543);
#538 = CYLINDRICAL_SURFACE('',#539,4.316451884327);
#539 = AXIS2_PLACEMENT_3D('',#540,#541,#542);
#540 = CARTESIAN_POINT('',(53.397033,120.882851,12.887524));
#541 = DIRECTION('',(0.,0.,1.));
#542 = DIRECTION('',(1.,0.,0.));
#543 = DEFINITIONAL_REPRESENTATION('',(#544),#548);
#544 = LINE('',#545,#546);
#545 = CARTESIAN_POINT('',(4.196719548987,0.));
#546 = VECTOR('',#547,1.);
#547 = DIRECTION('',(1.,0.));
#548 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#549 = PCURVE('',#550,#555);
#550 = PLANE('',#551);
#551 = AXIS2_PLACEMENT_3D('',#552,#553,#554);
#552 = CARTESIAN_POINT('',(53.397033,120.882851,12.887524));
#553 = DIRECTION('',(0.,0.,1.));
#554 = DIRECTION('',(1.,0.,0.));
#555 = DEFINITIONAL_REPRESENTATION('',(#556),#560);
#556 = CIRCLE('',#557,4.316451884327);
#557 = AXIS2_PLACEMENT_2D('',#558,#559);
#558 = CARTESIAN_POINT('',(0.,0.));
#559 = DIRECTION('',(-0.493117339904,-0.869962809025));
#560 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#561 = ORIENTED_EDGE('',*,*,#562,.T.);
#562 = EDGE_CURVE('',#529,#563,#565,.T.);
#563 = VERTEX_POINT('',#564);
#564 = CARTESIAN_POINT('',(57.613606422953,121.80603323474,32.5));
#565 = SURFACE_CURVE('',#566,(#570,#576),.PCURVE_S1.);
#566 = LINE('',#567,#568);
#567 = CARTESIAN_POINT('',(57.613606422953,121.80603323474,12.887524));
#568 = VECTOR('',#569,1.);
#569 = DIRECTION('',(0.,0.,1.));
#570 = PCURVE('',#538,#571);
#571 = DEFINITIONAL_REPRESENTATION('',(#572),#575);
#572 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#573,#574),.UNSPECIFIED.,.F.,.F.,
  (2,2),(0.,19.612476),.PIECEWISE_BEZIER_KNOTS.);
#573 = CARTESIAN_POINT('',(6.498725601627,0.));
#574 = CARTESIAN_POINT('',(6.498725601627,19.612476));
#575 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#576 = PCURVE('',#577,#582);
#577 = PLANE('',#578);
#578 = AXIS2_PLACEMENT_3D('',#579,#580,#581);
#579 = CARTESIAN_POINT('',(51.008936,121.80569,42.5));
#580 = DIRECTION('',(5.196848995186E-05,-0.99999999865,0.));
#581 = DIRECTION('',(0.99999999865,5.196848995186E-05,0.));
#582 = DEFINITIONAL_REPRESENTATION('',(#583),#586);
#583 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#584,#585),.UNSPECIFIED.,.F.,.F.,
  (2,2),(-5.999999999062E-07,19.6124766),.PIECEWISE_BEZIER_KNOTS.);
#584 = CARTESIAN_POINT('',(6.604670431872,-29.6124766));
#585 = CARTESIAN_POINT('',(6.604670431872,-9.9999994));
#586 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#587 = ORIENTED_EDGE('',*,*,#588,.T.);
#588 = EDGE_CURVE('',#563,#589,#591,.T.);
#589 = VERTEX_POINT('',#590);
#590 = CARTESIAN_POINT('',(51.268515728977,117.12769839369,32.5));
#591 = SURFACE_CURVE('',#592,(#597,#604),.PCURVE_S1.);
#592 = CIRCLE('',#593,4.316451884327);
#593 = AXIS2_PLACEMENT_3D('',#594,#595,#596);
#594 = CARTESIAN_POINT('',(53.397033,120.882851,32.5));
#595 = DIRECTION('',(0.,0.,-1.));
#596 = DIRECTION('',(0.976860981183,0.213875252056,0.));
#597 = PCURVE('',#538,#598);
#598 = DEFINITIONAL_REPRESENTATION('',(#599),#603);
#599 = LINE('',#600,#601);
#600 = CARTESIAN_POINT('',(6.498725601627,19.612476));
#601 = VECTOR('',#602,1.);
#602 = DIRECTION('',(-1.,-0.));
#603 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#604 = PCURVE('',#605,#610);
#605 = PLANE('',#606);
#606 = AXIS2_PLACEMENT_3D('',#607,#608,#609);
#607 = CARTESIAN_POINT('',(53.397033,120.882851,32.5));
#608 = DIRECTION('',(0.,0.,1.));
#609 = DIRECTION('',(1.,0.,0.));
#610 = DEFINITIONAL_REPRESENTATION('',(#611),#619);
#611 = ( BOUNDED_CURVE() B_SPLINE_CURVE(2,(#612,#613,#614,#615,#616,#617
,#618),.UNSPECIFIED.,.T.,.F.) B_SPLINE_CURVE_WITH_KNOTS((1,2,2,2,2,1),(
    -2.094395102393,0.,2.094395102393,4.188790204786,6.28318530718,
8.377580409573),.UNSPECIFIED.) CURVE() GEOMETRIC_REPRESENTATION_ITEM() 
RATIONAL_B_SPLINE_CURVE((1.,0.5,1.,0.5,1.,0.5,1.)) REPRESENTATION_ITEM(
  '') );
#612 = CARTESIAN_POINT('',(4.216573422953,0.923182234749));
#613 = CARTESIAN_POINT('',(5.815571958184,-6.380137167651));
#614 = CARTESIAN_POINT('',(-1.308787443862,-4.113250818574));
#615 = CARTESIAN_POINT('',(-8.433146845907,-1.846364469498));
#616 = CARTESIAN_POINT('',(-2.907785979092,3.190068583825));
#617 = CARTESIAN_POINT('',(2.617574887723,8.226501637149));
#618 = CARTESIAN_POINT('',(4.216573422953,0.923182234749));
#619 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#620 = ORIENTED_EDGE('',*,*,#621,.F.);
#621 = EDGE_CURVE('',#527,#589,#622,.T.);
#622 = SURFACE_CURVE('',#623,(#627,#633),.PCURVE_S1.);
#623 = LINE('',#624,#625);
#624 = CARTESIAN_POINT('',(51.268515728977,117.12769839369,12.887524));
#625 = VECTOR('',#626,1.);
#626 = DIRECTION('',(0.,0.,1.));
#627 = PCURVE('',#538,#628);
#628 = DEFINITIONAL_REPRESENTATION('',(#629),#632);
#629 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#630,#631),.UNSPECIFIED.,.F.,.F.,
  (2,2),(0.,19.612476),.PIECEWISE_BEZIER_KNOTS.);
#630 = CARTESIAN_POINT('',(4.196719548987,0.));
#631 = CARTESIAN_POINT('',(4.196719548987,19.612476));
#632 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#633 = PCURVE('',#634,#639);
#634 = PLANE('',#635);
#635 = AXIS2_PLACEMENT_3D('',#636,#637,#638);
#636 = CARTESIAN_POINT('',(51.468253,113.528152,42.5));
#637 = DIRECTION('',(0.998464000178,5.54043351018E-02,0.));
#638 = DIRECTION('',(-5.54043351018E-02,0.998464000178,0.));
#639 = DEFINITIONAL_REPRESENTATION('',(#640),#643);
#640 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#641,#642),.UNSPECIFIED.,.F.,.F.,
  (2,2),(-5.999999999062E-07,19.6124766),.PIECEWISE_BEZIER_KNOTS.);
#641 = CARTESIAN_POINT('',(3.605083801768,-29.6124766));
#642 = CARTESIAN_POINT('',(3.605083801768,-9.9999994));
#643 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#644 = ADVANCED_FACE('',(#645,#688),#550,.F.);
#645 = FACE_BOUND('',#646,.F.);
#646 = EDGE_LOOP('',(#647,#648,#669));
#647 = ORIENTED_EDGE('',*,*,#526,.T.);
#648 = ORIENTED_EDGE('',*,*,#649,.F.);
#649 = EDGE_CURVE('',#650,#529,#652,.T.);
#650 = VERTEX_POINT('',#651);
#651 = CARTESIAN_POINT('',(51.008936,121.80569,12.887524));
#652 = SURFACE_CURVE('',#653,(#657,#663),.PCURVE_S1.);
#653 = LINE('',#654,#655);
#654 = CARTESIAN_POINT('',(52.202960517501,121.80575205165,12.887524));
#655 = VECTOR('',#656,1.);
#656 = DIRECTION('',(0.99999999865,5.196848995186E-05,-0.));
#657 = PCURVE('',#550,#658);
#658 = DEFINITIONAL_REPRESENTATION('',(#659),#662);
#659 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#660,#661),.UNSPECIFIED.,.F.,.F.,
  (2,2),(-1.194024519113,5.510524374268),.PIECEWISE_BEZIER_KNOTS.);
#660 = CARTESIAN_POINT('',(-2.388097,0.922839));
#661 = CARTESIAN_POINT('',(4.316451884327,0.923187425282));
#662 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#663 = PCURVE('',#577,#664);
#664 = DEFINITIONAL_REPRESENTATION('',(#665),#668);
#665 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#666,#667),.UNSPECIFIED.,.F.,.F.,
  (2,2),(-1.194024519113,5.510524374268),.PIECEWISE_BEZIER_KNOTS.);
#666 = CARTESIAN_POINT('',(-7.105427357601E-15,-29.612476));
#667 = CARTESIAN_POINT('',(6.704548893381,-29.612476));
#668 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#669 = ORIENTED_EDGE('',*,*,#670,.F.);
#670 = EDGE_CURVE('',#527,#650,#671,.T.);
#671 = SURFACE_CURVE('',#672,(#676,#682),.PCURVE_S1.);
#672 = LINE('',#673,#674);
#673 = CARTESIAN_POINT('',(51.26778517277,117.14086404347,12.887524));
#674 = VECTOR('',#675,1.);
#675 = DIRECTION('',(-5.54043351018E-02,0.998464000178,0.));
#676 = PCURVE('',#550,#677);
#677 = DEFINITIONAL_REPRESENTATION('',(#678),#681);
#678 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#679,#680),.UNSPECIFIED.,.F.,.F.,
  (2,2),(-0.575348663244,4.672002150999),.PIECEWISE_BEZIER_KNOTS.);
#679 = CARTESIAN_POINT('',(-2.097371017091,-4.316451884327));
#680 = CARTESIAN_POINT('',(-2.388097,0.922839));
#681 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#682 = PCURVE('',#634,#683);
#683 = DEFINITIONAL_REPRESENTATION('',(#684),#687);
#684 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#685,#686),.UNSPECIFIED.,.F.,.F.,
  (2,2),(-0.575348663244,4.672002150999),.PIECEWISE_BEZIER_KNOTS.);
#685 = CARTESIAN_POINT('',(3.042921041851,-29.612476));
#686 = CARTESIAN_POINT('',(8.290271856093,-29.612476));
#687 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#688 = FACE_BOUND('',#689,.F.);
#689 = EDGE_LOOP('',(#690));
#690 = ORIENTED_EDGE('',*,*,#691,.F.);
#691 = EDGE_CURVE('',#692,#692,#694,.T.);
#692 = VERTEX_POINT('',#693);
#693 = CARTESIAN_POINT('',(53.672033,120.882851,12.887524));
#694 = SURFACE_CURVE('',#695,(#700,#707),.PCURVE_S1.);
#695 = CIRCLE('',#696,0.275);
#696 = AXIS2_PLACEMENT_3D('',#697,#698,#699);
#697 = CARTESIAN_POINT('',(53.397033,120.882851,12.887524));
#698 = DIRECTION('',(0.,0.,1.));
#699 = DIRECTION('',(1.,0.,0.));
#700 = PCURVE('',#550,#701);
#701 = DEFINITIONAL_REPRESENTATION('',(#702),#706);
#702 = CIRCLE('',#703,0.275);
#703 = AXIS2_PLACEMENT_2D('',#704,#705);
#704 = CARTESIAN_POINT('',(0.,0.));
#705 = DIRECTION('',(1.,0.));
#706 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#707 = PCURVE('',#708,#713);
#708 = CYLINDRICAL_SURFACE('',#709,0.275);
#709 = AXIS2_PLACEMENT_3D('',#710,#711,#712);
#710 = CARTESIAN_POINT('',(53.397033,120.882851,12.887524));
#711 = DIRECTION('',(0.,0.,1.));
#712 = DIRECTION('',(1.,0.,0.));
#713 = DEFINITIONAL_REPRESENTATION('',(#714),#718);
#714 = LINE('',#715,#716);
#715 = CARTESIAN_POINT('',(0.,0.));
#716 = VECTOR('',#717,1.);
#717 = DIRECTION('',(1.,0.));
#718 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#719 = ADVANCED_FACE('',(#720),#634,.F.);
#720 = FACE_BOUND('',#721,.F.);
#721 = EDGE_LOOP('',(#722,#745,#764,#765));
#722 = ORIENTED_EDGE('',*,*,#723,.F.);
#723 = EDGE_CURVE('',#724,#650,#726,.T.);
#724 = VERTEX_POINT('',#725);
#725 = CARTESIAN_POINT('',(51.008936,121.80569,32.5));
#726 = SURFACE_CURVE('',#727,(#731,#738),.PCURVE_S1.);
#727 = LINE('',#728,#729);
#728 = CARTESIAN_POINT('',(51.008936,121.80569,42.5));
#729 = VECTOR('',#730,1.);
#730 = DIRECTION('',(0.,0.,-1.));
#731 = PCURVE('',#634,#732);
#732 = DEFINITIONAL_REPRESENTATION('',(#733),#737);
#733 = LINE('',#734,#735);
#734 = CARTESIAN_POINT('',(8.290271856093,0.));
#735 = VECTOR('',#736,1.);
#736 = DIRECTION('',(0.,-1.));
#737 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#738 = PCURVE('',#577,#739);
#739 = DEFINITIONAL_REPRESENTATION('',(#740),#744);
#740 = LINE('',#741,#742);
#741 = CARTESIAN_POINT('',(0.,0.));
#742 = VECTOR('',#743,1.);
#743 = DIRECTION('',(0.,-1.));
#744 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#745 = ORIENTED_EDGE('',*,*,#746,.F.);
#746 = EDGE_CURVE('',#589,#724,#747,.T.);
#747 = SURFACE_CURVE('',#748,(#752,#758),.PCURVE_S1.);
#748 = LINE('',#749,#750);
#749 = CARTESIAN_POINT('',(51.26778517277,117.14086404347,32.5));
#750 = VECTOR('',#751,1.);
#751 = DIRECTION('',(-5.54043351018E-02,0.998464000178,0.));
#752 = PCURVE('',#634,#753);
#753 = DEFINITIONAL_REPRESENTATION('',(#754),#757);
#754 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#755,#756),.UNSPECIFIED.,.F.,.F.,
  (2,2),(-0.575348663244,4.672002150999),.PIECEWISE_BEZIER_KNOTS.);
#755 = CARTESIAN_POINT('',(3.042921041851,-10.));
#756 = CARTESIAN_POINT('',(8.290271856093,-10.));
#757 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#758 = PCURVE('',#605,#759);
#759 = DEFINITIONAL_REPRESENTATION('',(#760),#763);
#760 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#761,#762),.UNSPECIFIED.,.F.,.F.,
  (2,2),(-0.575348663244,4.672002150999),.PIECEWISE_BEZIER_KNOTS.);
#761 = CARTESIAN_POINT('',(-2.097371017091,-4.316451884327));
#762 = CARTESIAN_POINT('',(-2.388097,0.922839));
#763 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#764 = ORIENTED_EDGE('',*,*,#621,.F.);
#765 = ORIENTED_EDGE('',*,*,#670,.T.);
#766 = ADVANCED_FACE('',(#767,#790),#605,.T.);
#767 = FACE_BOUND('',#768,.T.);
#768 = EDGE_LOOP('',(#769,#770,#771));
#769 = ORIENTED_EDGE('',*,*,#746,.F.);
#770 = ORIENTED_EDGE('',*,*,#588,.F.);
#771 = ORIENTED_EDGE('',*,*,#772,.F.);
#772 = EDGE_CURVE('',#724,#563,#773,.T.);
#773 = SURFACE_CURVE('',#774,(#778,#784),.PCURVE_S1.);
#774 = LINE('',#775,#776);
#775 = CARTESIAN_POINT('',(52.202960517501,121.80575205165,32.5));
#776 = VECTOR('',#777,1.);
#777 = DIRECTION('',(0.99999999865,5.196848995186E-05,-0.));
#778 = PCURVE('',#605,#779);
#779 = DEFINITIONAL_REPRESENTATION('',(#780),#783);
#780 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#781,#782),.UNSPECIFIED.,.F.,.F.,
  (2,2),(-1.194024519113,5.510524374268),.PIECEWISE_BEZIER_KNOTS.);
#781 = CARTESIAN_POINT('',(-2.388097,0.922839));
#782 = CARTESIAN_POINT('',(4.316451884327,0.923187425282));
#783 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#784 = PCURVE('',#577,#785);
#785 = DEFINITIONAL_REPRESENTATION('',(#786),#789);
#786 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#787,#788),.UNSPECIFIED.,.F.,.F.,
  (2,2),(-1.194024519113,5.510524374268),.PIECEWISE_BEZIER_KNOTS.);
#787 = CARTESIAN_POINT('',(-7.105427357601E-15,-10.));
#788 = CARTESIAN_POINT('',(6.704548893381,-10.));
#789 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#790 = FACE_BOUND('',#791,.T.);
#791 = EDGE_LOOP('',(#792));
#792 = ORIENTED_EDGE('',*,*,#793,.F.);
#793 = EDGE_CURVE('',#794,#794,#796,.T.);
#794 = VERTEX_POINT('',#795);
#795 = CARTESIAN_POINT('',(53.672033,120.882851,32.5));
#796 = SURFACE_CURVE('',#797,(#802,#809),.PCURVE_S1.);
#797 = CIRCLE('',#798,0.275);
#798 = AXIS2_PLACEMENT_3D('',#799,#800,#801);
#799 = CARTESIAN_POINT('',(53.397033,120.882851,32.5));
#800 = DIRECTION('',(0.,0.,1.));
#801 = DIRECTION('',(1.,0.,0.));
#802 = PCURVE('',#605,#803);
#803 = DEFINITIONAL_REPRESENTATION('',(#804),#808);
#804 = CIRCLE('',#805,0.275);
#805 = AXIS2_PLACEMENT_2D('',#806,#807);
#806 = CARTESIAN_POINT('',(0.,0.));
#807 = DIRECTION('',(1.,0.));
#808 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#809 = PCURVE('',#708,#810);
#810 = DEFINITIONAL_REPRESENTATION('',(#811),#815);
#811 = LINE('',#812,#813);
#812 = CARTESIAN_POINT('',(0.,19.612476));
#813 = VECTOR('',#814,1.);
#814 = DIRECTION('',(1.,0.));
#815 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#816 = ADVANCED_FACE('',(#817),#577,.F.);
#817 = FACE_BOUND('',#818,.F.);
#818 = EDGE_LOOP('',(#819,#820,#821,#822));
#819 = ORIENTED_EDGE('',*,*,#772,.F.);
#820 = ORIENTED_EDGE('',*,*,#723,.T.);
#821 = ORIENTED_EDGE('',*,*,#649,.T.);
#822 = ORIENTED_EDGE('',*,*,#562,.T.);
#823 = ADVANCED_FACE('',(#824),#708,.F.);
#824 = FACE_BOUND('',#825,.F.);
#825 = EDGE_LOOP('',(#826,#827,#848,#849));
#826 = ORIENTED_EDGE('',*,*,#793,.F.);
#827 = ORIENTED_EDGE('',*,*,#828,.F.);
#828 = EDGE_CURVE('',#692,#794,#829,.T.);
#829 = SEAM_CURVE('',#830,(#834,#841),.PCURVE_S1.);
#830 = LINE('',#831,#832);
#831 = CARTESIAN_POINT('',(53.672033,120.882851,12.887524));
#832 = VECTOR('',#833,1.);
#833 = DIRECTION('',(0.,0.,1.));
#834 = PCURVE('',#708,#835);
#835 = DEFINITIONAL_REPRESENTATION('',(#836),#840);
#836 = LINE('',#837,#838);
#837 = CARTESIAN_POINT('',(6.28318530718,-0.));
#838 = VECTOR('',#839,1.);
#839 = DIRECTION('',(0.,1.));
#840 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#841 = PCURVE('',#708,#842);
#842 = DEFINITIONAL_REPRESENTATION('',(#843),#847);
#843 = LINE('',#844,#845);
#844 = CARTESIAN_POINT('',(0.,-0.));
#845 = VECTOR('',#846,1.);
#846 = DIRECTION('',(0.,1.));
#847 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#848 = ORIENTED_EDGE('',*,*,#691,.T.);
#849 = ORIENTED_EDGE('',*,*,#828,.T.);
#850 = ( GEOMETRIC_REPRESENTATION_CONTEXT(3) 
GLOBAL_UNCERTAINTY_ASSIGNED_CONTEXT((#854)) GLOBAL_UNIT_ASSIGNED_CONTEXT
((#851,#852,#853)) REPRESENTATION_CONTEXT('Context #1',
  '3D Context with UNIT and UNCERTAINTY') );
#851 = ( LENGTH_UNIT() NAMED_UNIT(*) SI_UNIT(.MILLI.,.METRE.) );
#852 = ( NAMED_UNIT(*) PLANE_ANGLE_UNIT() SI_UNIT($,.RADIAN.) );
#853 = ( NAMED_UNIT(*) SI_UNIT($,.STERADIAN.) SOLID_ANGLE_UNIT() );
#854 = UNCERTAINTY_MEASURE_WITH_UNIT(LENGTH_MEASURE(1.E-07),#851,
  'distance_accuracy_value','confusion accuracy');
#855 = CONTEXT_DEPENDENT_SHAPE_REPRESENTATION(#856,#858);
#856 = ( REPRESENTATION_RELATIONSHIP('','',#519,#10) 
REPRESENTATION_RELATIONSHIP_WITH_TRANSFORMATION(#857) 
SHAPE_REPRESENTATION_RELATIONSHIP() );
#857 = ITEM_DEFINED_TRANSFORMATION('','',#11,#23);
#858 = PRODUCT_DEFINITION_SHAPE('Placement','Placement of an item',#859
  );
#859 = NEXT_ASSEMBLY_USAGE_OCCURRENCE('125','=>[0:1:1:4]','',#5,#514,$);
#860 = PRODUCT_RELATED_PRODUCT_CATEGORY('part',$,(#516));
#861 = MECHANICAL_DESIGN_GEOMETRIC_PRESENTATION_REPRESENTATION('',(#862)
  ,#501);
#862 = STYLED_ITEM('color',(#863),#158);
#863 = PRESENTATION_STYLE_ASSIGNMENT((#864));
#864 = SURFACE_STYLE_USAGE(.BOTH.,#865);
#865 = SURFACE_SIDE_STYLE('',(#866));
#866 = SURFACE_STYLE_FILL_AREA(#867);
#867 = FILL_AREA_STYLE('',(#868));
#868 = FILL_AREA_STYLE_COLOUR('',#869);
#869 = DRAUGHTING_PRE_DEFINED_COLOUR('green');
#870 = MECHANICAL_DESIGN_GEOMETRIC_PRESENTATION_REPRESENTATION('',(#871)
  ,#850);
#871 = STYLED_ITEM('color',(#872),#520);
#872 = PRESENTATION_STYLE_ASSIGNMENT((#873));
#873 = SURFACE_STYLE_USAGE(.BOTH.,#874);
#874 = SURFACE_SIDE_STYLE('',(#875));
#875 = SURFACE_STYLE_FILL_AREA(#876);
#876 = FILL_AREA_STYLE('',(#877));
#877 = FILL_AREA_STYLE_COLOUR('',#878);
#878 = DRAUGHTING_PRE_DEFINED_COLOUR('red');
#879 = MECHANICAL_DESIGN_GEOMETRIC_PRESENTATION_REPRESENTATION('',(#880)
  ,#139);
#880 = STYLED_ITEM('color',(#881),#41);
#881 = PRESENTATION_STYLE_ASSIGNMENT((#882));
#882 = SURFACE_STYLE_USAGE(.BOTH.,#883);
#883 = SURFACE_SIDE_STYLE('',(#884));
#884 = SURFACE_STYLE_FILL_AREA(#885);
#885 = FILL_AREA_STYLE('',(#886));
#886 = FILL_AREA_STYLE_COLOUR('',#887);
#887 = DRAUGHTING_PRE_DEFINED_COLOUR('blue');
ENDSEC;
END-ISO-10303-21;
