ISO-10303-21;
HEADER;
FILE_DESCRIPTION(('Open CASCADE Model'),'2;1');
FILE_NAME('All_Individual_Piles','2025-08-25T20:07:51',('Author'),(
    'Open CASCADE'),'Open CASCADE STEP processor 7.8','build123d',
  'Unknown');
FILE_SCHEMA(('AUTOMOTIVE_DESIGN { 1 0 10303 214 1 1 1 1 }'));
ENDSEC;
DATA;
#1 = APPLICATION_PROTOCOL_DEFINITION('international standard',
  'automotive_design',2000,#2);
#2 = APPLICATION_CONTEXT(
  'core data for automotive mechanical design processes');
#3 = SHAPE_DEFINITION_REPRESENTATION(#4,#10);
#4 = PRODUCT_DEFINITION_SHAPE('','',#5);
#5 = PRODUCT_DEFINITION('design','',#6,#9);
#6 = PRODUCT_DEFINITION_FORMATION('','',#7);
#7 = PRODUCT('All_Individual_Piles','All_Individual_Piles','',(#8));
#8 = PRODUCT_CONTEXT('',#2,'mechanical');
#9 = PRODUCT_DEFINITION_CONTEXT('part definition',#2,'design');
#10 = SHAPE_REPRESENTATION('',(#11,#15,#19,#23,#27,#31,#35,#39,#43,#47,
    #51,#55,#59,#63,#67,#71,#75,#79,#83,#87,#91,#95,#99,#103,#107),#111
  );
#11 = AXIS2_PLACEMENT_3D('',#12,#13,#14);
#12 = CARTESIAN_POINT('',(0.,0.,0.));
#13 = DIRECTION('',(0.,0.,1.));
#14 = DIRECTION('',(1.,0.,-0.));
#15 = AXIS2_PLACEMENT_3D('',#16,#17,#18);
#16 = CARTESIAN_POINT('',(51.523496,132.387429,1.909425));
#17 = DIRECTION('',(0.,0.,1.));
#18 = DIRECTION('',(1.,0.,0.));
#19 = AXIS2_PLACEMENT_3D('',#20,#21,#22);
#20 = CARTESIAN_POINT('',(0.,0.,0.));
#21 = DIRECTION('',(0.,0.,1.));
#22 = DIRECTION('',(1.,0.,-0.));
#23 = AXIS2_PLACEMENT_3D('',#24,#25,#26);
#24 = CARTESIAN_POINT('',(0.,0.,0.));
#25 = DIRECTION('',(0.,0.,1.));
#26 = DIRECTION('',(1.,0.,-0.));
#27 = AXIS2_PLACEMENT_3D('',#28,#29,#30);
#28 = CARTESIAN_POINT('',(51.53192,135.691161,0.97226));
#29 = DIRECTION('',(0.,0.,1.));
#30 = DIRECTION('',(1.,0.,0.));
#31 = AXIS2_PLACEMENT_3D('',#32,#33,#34);
#32 = CARTESIAN_POINT('',(0.,0.,0.));
#33 = DIRECTION('',(0.,0.,1.));
#34 = DIRECTION('',(1.,0.,-0.));
#35 = AXIS2_PLACEMENT_3D('',#36,#37,#38);
#36 = CARTESIAN_POINT('',(0.,0.,0.));
#37 = DIRECTION('',(0.,0.,1.));
#38 = DIRECTION('',(1.,0.,-0.));
#39 = AXIS2_PLACEMENT_3D('',#40,#41,#42);
#40 = CARTESIAN_POINT('',(51.614918,130.739863,2.424596));
#41 = DIRECTION('',(0.,0.,1.));
#42 = DIRECTION('',(1.,0.,0.));
#43 = AXIS2_PLACEMENT_3D('',#44,#45,#46);
#44 = CARTESIAN_POINT('',(0.,0.,0.));
#45 = DIRECTION('',(0.,0.,1.));
#46 = DIRECTION('',(1.,0.,-0.));
#47 = AXIS2_PLACEMENT_3D('',#48,#49,#50);
#48 = CARTESIAN_POINT('',(0.,0.,0.));
#49 = DIRECTION('',(0.,0.,1.));
#50 = DIRECTION('',(1.,0.,-0.));
#51 = AXIS2_PLACEMENT_3D('',#52,#53,#54);
#52 = CARTESIAN_POINT('',(51.706341,129.092298,2.995251));
#53 = DIRECTION('',(0.,0.,1.));
#54 = DIRECTION('',(1.,0.,0.));
#55 = AXIS2_PLACEMENT_3D('',#56,#57,#58);
#56 = CARTESIAN_POINT('',(0.,0.,0.));
#57 = DIRECTION('',(0.,0.,1.));
#58 = DIRECTION('',(1.,0.,-0.));
#59 = AXIS2_PLACEMENT_3D('',#60,#61,#62);
#60 = CARTESIAN_POINT('',(0.,0.,0.));
#61 = DIRECTION('',(0.,0.,1.));
#62 = DIRECTION('',(1.,0.,-0.));
#63 = AXIS2_PLACEMENT_3D('',#64,#65,#66);
#64 = CARTESIAN_POINT('',(51.797763,127.444732,3.620574));
#65 = DIRECTION('',(0.,0.,1.));
#66 = DIRECTION('',(1.,0.,0.));
#67 = AXIS2_PLACEMENT_3D('',#68,#69,#70);
#68 = CARTESIAN_POINT('',(0.,0.,0.));
#69 = DIRECTION('',(0.,0.,1.));
#70 = DIRECTION('',(1.,0.,-0.));
#71 = AXIS2_PLACEMENT_3D('',#72,#73,#74);
#72 = CARTESIAN_POINT('',(0.,0.,0.));
#73 = DIRECTION('',(0.,0.,1.));
#74 = DIRECTION('',(1.,0.,-0.));
#75 = AXIS2_PLACEMENT_3D('',#76,#77,#78);
#76 = CARTESIAN_POINT('',(51.889186,125.797167,4.274863));
#77 = DIRECTION('',(0.,0.,1.));
#78 = DIRECTION('',(1.,0.,0.));
#79 = AXIS2_PLACEMENT_3D('',#80,#81,#82);
#80 = CARTESIAN_POINT('',(0.,0.,0.));
#81 = DIRECTION('',(0.,0.,1.));
#82 = DIRECTION('',(1.,0.,-0.));
#83 = AXIS2_PLACEMENT_3D('',#84,#85,#86);
#84 = CARTESIAN_POINT('',(0.,0.,0.));
#85 = DIRECTION('',(0.,0.,1.));
#86 = DIRECTION('',(1.,0.,-0.));
#87 = AXIS2_PLACEMENT_3D('',#88,#89,#90);
#88 = CARTESIAN_POINT('',(51.980608,124.149601,-7.2622E-02));
#89 = DIRECTION('',(0.,0.,1.));
#90 = DIRECTION('',(1.,0.,0.));
#91 = AXIS2_PLACEMENT_3D('',#92,#93,#94);
#92 = CARTESIAN_POINT('',(0.,0.,0.));
#93 = DIRECTION('',(0.,0.,1.));
#94 = DIRECTION('',(1.,0.,-0.));
#95 = AXIS2_PLACEMENT_3D('',#96,#97,#98);
#96 = CARTESIAN_POINT('',(0.,0.,0.));
#97 = DIRECTION('',(0.,0.,1.));
#98 = DIRECTION('',(1.,0.,-0.));
#99 = AXIS2_PLACEMENT_3D('',#100,#101,#102);
#100 = CARTESIAN_POINT('',(52.072031,122.502036,0.561178));
#101 = DIRECTION('',(0.,0.,1.));
#102 = DIRECTION('',(1.,0.,0.));
#103 = AXIS2_PLACEMENT_3D('',#104,#105,#106);
#104 = CARTESIAN_POINT('',(0.,0.,0.));
#105 = DIRECTION('',(0.,0.,1.));
#106 = DIRECTION('',(1.,0.,-0.));
#107 = AXIS2_PLACEMENT_3D('',#108,#109,#110);
#108 = CARTESIAN_POINT('',(0.,0.,0.));
#109 = DIRECTION('',(0.,0.,1.));
#110 = DIRECTION('',(1.,0.,-0.));
#111 = ( GEOMETRIC_REPRESENTATION_CONTEXT(3) 
GLOBAL_UNCERTAINTY_ASSIGNED_CONTEXT((#115)) GLOBAL_UNIT_ASSIGNED_CONTEXT
((#112,#113,#114)) REPRESENTATION_CONTEXT('Context #1',
  '3D Context with UNIT and UNCERTAINTY') );
#112 = ( LENGTH_UNIT() NAMED_UNIT(*) SI_UNIT(.MILLI.,.METRE.) );
#113 = ( NAMED_UNIT(*) PLANE_ANGLE_UNIT() SI_UNIT($,.RADIAN.) );
#114 = ( NAMED_UNIT(*) SI_UNIT($,.STERADIAN.) SOLID_ANGLE_UNIT() );
#115 = UNCERTAINTY_MEASURE_WITH_UNIT(LENGTH_MEASURE(1.E-07),#112,
  'distance_accuracy_value','confusion accuracy');
#116 = PRODUCT_RELATED_PRODUCT_CATEGORY('part',$,(#7));
#117 = SHAPE_DEFINITION_REPRESENTATION(#118,#124);
#118 = PRODUCT_DEFINITION_SHAPE('','',#119);
#119 = PRODUCT_DEFINITION('design','',#120,#123);
#120 = PRODUCT_DEFINITION_FORMATION('','',#121);
#121 = PRODUCT('PC1-P1_Part1','PC1-P1_Part1','',(#122));
#122 = PRODUCT_CONTEXT('',#2,'mechanical');
#123 = PRODUCT_DEFINITION_CONTEXT('part definition',#2,'design');
#124 = ADVANCED_BREP_SHAPE_REPRESENTATION('',(#11,#125),#223);
#125 = MANIFOLD_SOLID_BREP('',#126);
#126 = CLOSED_SHELL('',(#127,#215,#219));
#127 = ADVANCED_FACE('',(#128),#141,.T.);
#128 = FACE_BOUND('',#129,.T.);
#129 = EDGE_LOOP('',(#130,#164,#187,#214));
#130 = ORIENTED_EDGE('',*,*,#131,.F.);
#131 = EDGE_CURVE('',#132,#132,#134,.T.);
#132 = VERTEX_POINT('',#133);
#133 = CARTESIAN_POINT('',(0.275,-6.73555739531E-17,16.090575));
#134 = SURFACE_CURVE('',#135,(#140,#152),.PCURVE_S1.);
#135 = CIRCLE('',#136,0.275);
#136 = AXIS2_PLACEMENT_3D('',#137,#138,#139);
#137 = CARTESIAN_POINT('',(0.,0.,16.090575));
#138 = DIRECTION('',(0.,0.,1.));
#139 = DIRECTION('',(1.,0.,-0.));
#140 = PCURVE('',#141,#146);
#141 = CYLINDRICAL_SURFACE('',#142,0.275);
#142 = AXIS2_PLACEMENT_3D('',#143,#144,#145);
#143 = CARTESIAN_POINT('',(0.,0.,0.));
#144 = DIRECTION('',(0.,0.,1.));
#145 = DIRECTION('',(1.,0.,-0.));
#146 = DEFINITIONAL_REPRESENTATION('',(#147),#151);
#147 = LINE('',#148,#149);
#148 = CARTESIAN_POINT('',(0.,16.090575));
#149 = VECTOR('',#150,1.);
#150 = DIRECTION('',(1.,0.));
#151 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#152 = PCURVE('',#153,#158);
#153 = PLANE('',#154);
#154 = AXIS2_PLACEMENT_3D('',#155,#156,#157);
#155 = CARTESIAN_POINT('',(0.,0.,16.090575));
#156 = DIRECTION('',(0.,0.,1.));
#157 = DIRECTION('',(1.,0.,-0.));
#158 = DEFINITIONAL_REPRESENTATION('',(#159),#163);
#159 = CIRCLE('',#160,0.275);
#160 = AXIS2_PLACEMENT_2D('',#161,#162);
#161 = CARTESIAN_POINT('',(0.,0.));
#162 = DIRECTION('',(1.,0.));
#163 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#164 = ORIENTED_EDGE('',*,*,#165,.F.);
#165 = EDGE_CURVE('',#166,#132,#168,.T.);
#166 = VERTEX_POINT('',#167);
#167 = CARTESIAN_POINT('',(0.275,-6.73555739531E-17,0.));
#168 = SEAM_CURVE('',#169,(#173,#180),.PCURVE_S1.);
#169 = LINE('',#170,#171);
#170 = CARTESIAN_POINT('',(0.275,-6.73555739531E-17,0.));
#171 = VECTOR('',#172,1.);
#172 = DIRECTION('',(0.,0.,1.));
#173 = PCURVE('',#141,#174);
#174 = DEFINITIONAL_REPRESENTATION('',(#175),#179);
#175 = LINE('',#176,#177);
#176 = CARTESIAN_POINT('',(6.28318530718,-0.));
#177 = VECTOR('',#178,1.);
#178 = DIRECTION('',(0.,1.));
#179 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#180 = PCURVE('',#141,#181);
#181 = DEFINITIONAL_REPRESENTATION('',(#182),#186);
#182 = LINE('',#183,#184);
#183 = CARTESIAN_POINT('',(0.,-0.));
#184 = VECTOR('',#185,1.);
#185 = DIRECTION('',(0.,1.));
#186 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#187 = ORIENTED_EDGE('',*,*,#188,.T.);
#188 = EDGE_CURVE('',#166,#166,#189,.T.);
#189 = SURFACE_CURVE('',#190,(#195,#202),.PCURVE_S1.);
#190 = CIRCLE('',#191,0.275);
#191 = AXIS2_PLACEMENT_3D('',#192,#193,#194);
#192 = CARTESIAN_POINT('',(0.,0.,0.));
#193 = DIRECTION('',(0.,0.,1.));
#194 = DIRECTION('',(1.,0.,-0.));
#195 = PCURVE('',#141,#196);
#196 = DEFINITIONAL_REPRESENTATION('',(#197),#201);
#197 = LINE('',#198,#199);
#198 = CARTESIAN_POINT('',(0.,0.));
#199 = VECTOR('',#200,1.);
#200 = DIRECTION('',(1.,0.));
#201 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#202 = PCURVE('',#203,#208);
#203 = PLANE('',#204);
#204 = AXIS2_PLACEMENT_3D('',#205,#206,#207);
#205 = CARTESIAN_POINT('',(0.,0.,0.));
#206 = DIRECTION('',(0.,0.,1.));
#207 = DIRECTION('',(1.,0.,-0.));
#208 = DEFINITIONAL_REPRESENTATION('',(#209),#213);
#209 = CIRCLE('',#210,0.275);
#210 = AXIS2_PLACEMENT_2D('',#211,#212);
#211 = CARTESIAN_POINT('',(0.,0.));
#212 = DIRECTION('',(1.,0.));
#213 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#214 = ORIENTED_EDGE('',*,*,#165,.T.);
#215 = ADVANCED_FACE('',(#216),#153,.T.);
#216 = FACE_BOUND('',#217,.T.);
#217 = EDGE_LOOP('',(#218));
#218 = ORIENTED_EDGE('',*,*,#131,.T.);
#219 = ADVANCED_FACE('',(#220),#203,.F.);
#220 = FACE_BOUND('',#221,.T.);
#221 = EDGE_LOOP('',(#222));
#222 = ORIENTED_EDGE('',*,*,#188,.F.);
#223 = ( GEOMETRIC_REPRESENTATION_CONTEXT(3) 
GLOBAL_UNCERTAINTY_ASSIGNED_CONTEXT((#227)) GLOBAL_UNIT_ASSIGNED_CONTEXT
((#224,#225,#226)) REPRESENTATION_CONTEXT('Context #1',
  '3D Context with UNIT and UNCERTAINTY') );
#224 = ( LENGTH_UNIT() NAMED_UNIT(*) SI_UNIT(.MILLI.,.METRE.) );
#225 = ( NAMED_UNIT(*) PLANE_ANGLE_UNIT() SI_UNIT($,.RADIAN.) );
#226 = ( NAMED_UNIT(*) SI_UNIT($,.STERADIAN.) SOLID_ANGLE_UNIT() );
#227 = UNCERTAINTY_MEASURE_WITH_UNIT(LENGTH_MEASURE(1.E-07),#224,
  'distance_accuracy_value','confusion accuracy');
#228 = CONTEXT_DEPENDENT_SHAPE_REPRESENTATION(#229,#231);
#229 = ( REPRESENTATION_RELATIONSHIP('','',#124,#10) 
REPRESENTATION_RELATIONSHIP_WITH_TRANSFORMATION(#230) 
SHAPE_REPRESENTATION_RELATIONSHIP() );
#230 = ITEM_DEFINED_TRANSFORMATION('','',#11,#15);
#231 = PRODUCT_DEFINITION_SHAPE('Placement','Placement of an item',#232
  );
#232 = NEXT_ASSEMBLY_USAGE_OCCURRENCE('59','=>[0:1:1:2]','',#5,#119,$);
#233 = PRODUCT_RELATED_PRODUCT_CATEGORY('part',$,(#121));
#234 = SHAPE_DEFINITION_REPRESENTATION(#235,#241);
#235 = PRODUCT_DEFINITION_SHAPE('','',#236);
#236 = PRODUCT_DEFINITION('design','',#237,#240);
#237 = PRODUCT_DEFINITION_FORMATION('','',#238);
#238 = PRODUCT('PC1-P1_Part2','PC1-P1_Part2','',(#239));
#239 = PRODUCT_CONTEXT('',#2,'mechanical');
#240 = PRODUCT_DEFINITION_CONTEXT('part definition',#2,'design');
#241 = ADVANCED_BREP_SHAPE_REPRESENTATION('',(#11,#242),#654);
#242 = MANIFOLD_SOLID_BREP('',#243);
#243 = CLOSED_SHELL('',(#244,#476,#526,#595,#621,#648));
#244 = ADVANCED_FACE('',(#245),#259,.T.);
#245 = FACE_BOUND('',#246,.T.);
#246 = EDGE_LOOP('',(#247,#277,#306,#352,#387,#427,#449,#450));
#247 = ORIENTED_EDGE('',*,*,#248,.T.);
#248 = EDGE_CURVE('',#249,#251,#253,.T.);
#249 = VERTEX_POINT('',#250);
#250 = CARTESIAN_POINT('',(51.798496,132.387429,1.909425));
#251 = VERTEX_POINT('',#252);
#252 = CARTESIAN_POINT('',(55.839947884327,132.387429,8.909425));
#253 = SEAM_CURVE('',#254,(#258,#270),.PCURVE_S1.);
#254 = LINE('',#255,#256);
#255 = CARTESIAN_POINT('',(51.798496,132.387429,1.909425));
#256 = VECTOR('',#257,1.);
#257 = DIRECTION('',(0.5,-1.224646799147E-16,0.866025403784));
#258 = PCURVE('',#259,#264);
#259 = CONICAL_SURFACE('',#260,0.275,0.523598775598);
#260 = AXIS2_PLACEMENT_3D('',#261,#262,#263);
#261 = CARTESIAN_POINT('',(51.523496,132.387429,1.909425));
#262 = DIRECTION('',(0.,0.,1.));
#263 = DIRECTION('',(1.,0.,0.));
#264 = DEFINITIONAL_REPRESENTATION('',(#265),#269);
#265 = LINE('',#266,#267);
#266 = CARTESIAN_POINT('',(6.28318530718,-0.));
#267 = VECTOR('',#268,1.);
#268 = DIRECTION('',(0.,1.));
#269 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#270 = PCURVE('',#259,#271);
#271 = DEFINITIONAL_REPRESENTATION('',(#272),#276);
#272 = LINE('',#273,#274);
#273 = CARTESIAN_POINT('',(0.,-0.));
#274 = VECTOR('',#275,1.);
#275 = DIRECTION('',(0.,1.));
#276 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#277 = ORIENTED_EDGE('',*,*,#278,.F.);
#278 = EDGE_CURVE('',#279,#251,#281,.T.);
#279 = VERTEX_POINT('',#280);
#280 = CARTESIAN_POINT('',(50.656396039216,128.15896656931,8.909425));
#281 = SURFACE_CURVE('',#282,(#287,#294),.PCURVE_S1.);
#282 = CIRCLE('',#283,4.316451884327);
#283 = AXIS2_PLACEMENT_3D('',#284,#285,#286);
#284 = CARTESIAN_POINT('',(51.523496,132.387429,8.909425));
#285 = DIRECTION('',(0.,0.,1.));
#286 = DIRECTION('',(1.,0.,0.));
#287 = PCURVE('',#259,#288);
#288 = DEFINITIONAL_REPRESENTATION('',(#289),#293);
#289 = LINE('',#290,#291);
#290 = CARTESIAN_POINT('',(0.,7.));
#291 = VECTOR('',#292,1.);
#292 = DIRECTION('',(1.,0.));
#293 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#294 = PCURVE('',#295,#300);
#295 = PLANE('',#296);
#296 = AXIS2_PLACEMENT_3D('',#297,#298,#299);
#297 = CARTESIAN_POINT('',(51.523496,132.387429,8.909425));
#298 = DIRECTION('',(0.,0.,1.));
#299 = DIRECTION('',(1.,0.,0.));
#300 = DEFINITIONAL_REPRESENTATION('',(#301),#305);
#301 = CIRCLE('',#302,4.316451884327);
#302 = AXIS2_PLACEMENT_2D('',#303,#304);
#303 = CARTESIAN_POINT('',(0.,0.));
#304 = DIRECTION('',(1.,0.));
#305 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#306 = ORIENTED_EDGE('',*,*,#307,.F.);
#307 = EDGE_CURVE('',#308,#279,#310,.T.);
#308 = VERTEX_POINT('',#309);
#309 = CARTESIAN_POINT('',(50.449147,131.893892,3.480893641506));
#310 = SURFACE_CURVE('',#311,(#316,#340),.PCURVE_S1.);
#311 = HYPERBOLA('',#312,1.905330103751,1.100042848296);
#312 = AXIS2_PLACEMENT_3D('',#313,#314,#315);
#313 = CARTESIAN_POINT('',(50.425142811532,132.32648196176,
    1.433111027919));
#314 = DIRECTION('',(0.998464005442,5.540424023454E-02,0.));
#315 = DIRECTION('',(-0.,0.,1.));
#316 = PCURVE('',#259,#317);
#317 = DEFINITIONAL_REPRESENTATION('',(#318),#339);
#318 = B_SPLINE_CURVE_WITH_KNOTS('',7,(#319,#320,#321,#322,#323,#324,
    #325,#326,#327,#328,#329,#330,#331,#332,#333,#334,#335,#336,#337,
    #338),.UNSPECIFIED.,.F.,.F.,(8,6,6,8),(-0.503133333129,
    0.133546109649,1.088565273816,2.043584437983),.UNSPECIFIED.);
#319 = CARTESIAN_POINT('',(2.713867514344,1.675307233699));
#320 = CARTESIAN_POINT('',(2.794410402888,1.584389825603));
#321 = CARTESIAN_POINT('',(2.878924084936,1.514238670408));
#322 = CARTESIAN_POINT('',(2.966788373961,1.46362530626));
#323 = CARTESIAN_POINT('',(3.057093257368,1.431742081853));
#324 = CARTESIAN_POINT('',(3.148608150206,1.418160979984));
#325 = CARTESIAN_POINT('',(3.24002706695,1.422819665025));
#326 = CARTESIAN_POINT('',(3.46539988072,1.48084991063));
#327 = CARTESIAN_POINT('',(3.59776449686,1.55741318328));
#328 = CARTESIAN_POINT('',(3.723319148411,1.676780601584));
#329 = CARTESIAN_POINT('',(3.838663499098,1.841912563017));
#330 = CARTESIAN_POINT('',(3.942772195366,2.057760448314));
#331 = CARTESIAN_POINT('',(4.035750154341,2.331612026611));
#332 = CARTESIAN_POINT('',(4.200785911388,3.016132387337));
#333 = CARTESIAN_POINT('',(4.272844302094,3.426801109378));
#334 = CARTESIAN_POINT('',(4.335115360032,3.916284766459));
#335 = CARTESIAN_POINT('',(4.388847875973,4.498104636899));
#336 = CARTESIAN_POINT('',(4.43523999271,5.189706233181));
#337 = CARTESIAN_POINT('',(4.475360840532,6.013676289772));
#338 = CARTESIAN_POINT('',(4.510130204342,7.));
#339 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#340 = PCURVE('',#341,#346);
#341 = PLANE('',#342);
#342 = AXIS2_PLACEMENT_3D('',#343,#344,#345);
#343 = CARTESIAN_POINT('',(51.008936,121.80569,28.));
#344 = DIRECTION('',(0.998464005442,5.540424023454E-02,0.));
#345 = DIRECTION('',(-5.540424023454E-02,0.998464005442,0.));
#346 = DEFINITIONAL_REPRESENTATION('',(#347),#351);
#347 = ( BOUNDED_CURVE() B_SPLINE_CURVE(2,(#348,#349,#350),
.UNSPECIFIED.,.F.,.F.) B_SPLINE_CURVE_WITH_KNOTS((3,3),(-0.503133333129,
2.043584437983),.PIECEWISE_BEZIER_KNOTS.) CURVE() 
GEOMETRIC_REPRESENTATION_ITEM() RATIONAL_B_SPLINE_CURVE((1.,
1.926361545493,1.)) REPRESENTATION_ITEM('') );
#348 = CARTESIAN_POINT('',(11.114093386955,-24.4152677663));
#349 = CARTESIAN_POINT('',(10.052344930476,-25.2696273756));
#350 = CARTESIAN_POINT('',(6.36305017976,-19.090575));
#351 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#352 = ORIENTED_EDGE('',*,*,#353,.F.);
#353 = EDGE_CURVE('',#354,#308,#356,.T.);
#354 = VERTEX_POINT('',#355);
#355 = CARTESIAN_POINT('',(48.301331,131.800116,7.106016063399));
#356 = SURFACE_CURVE('',#357,(#362,#375),.PCURVE_S1.);
#357 = HYPERBOLA('',#358,0.772849124257,0.446204649932);
#358 = AXIS2_PLACEMENT_3D('',#359,#360,#361);
#359 = CARTESIAN_POINT('',(51.542959241468,131.94164904099,
    1.433111027919));
#360 = DIRECTION('',(-4.361953975718E-02,0.999048214928,0.));
#361 = DIRECTION('',(0.,0.,1.));
#362 = PCURVE('',#259,#363);
#363 = DEFINITIONAL_REPRESENTATION('',(#364),#374);
#364 = B_SPLINE_CURVE_WITH_KNOTS('',8,(#365,#366,#367,#368,#369,#370,
    #371,#372,#373),.UNSPECIFIED.,.F.,.F.,(9,9),(-2.745440166929,
    -1.43099478095),.PIECEWISE_BEZIER_KNOTS.);
#365 = CARTESIAN_POINT('',(3.313489956121,5.565699335595));
#366 = CARTESIAN_POINT('',(3.33450670819,4.581117154854));
#367 = CARTESIAN_POINT('',(3.359437457884,3.782949025608));
#368 = CARTESIAN_POINT('',(3.389119091478,3.130692278326));
#369 = CARTESIAN_POINT('',(3.424588514988,2.594578163818));
#370 = CARTESIAN_POINT('',(3.467147067719,2.152081677413));
#371 = CARTESIAN_POINT('',(3.518376414741,1.78594375289));
#372 = CARTESIAN_POINT('',(3.580244760661,1.482825996736));
#373 = CARTESIAN_POINT('',(3.654558784415,1.232428056211));
#374 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#375 = PCURVE('',#376,#381);
#376 = PLANE('',#377);
#377 = AXIS2_PLACEMENT_3D('',#378,#379,#380);
#378 = CARTESIAN_POINT('',(50.449147,131.893892,28.));
#379 = DIRECTION('',(-4.361953975718E-02,0.999048214928,0.));
#380 = DIRECTION('',(-0.999048214928,-4.361953975718E-02,0.));
#381 = DEFINITIONAL_REPRESENTATION('',(#382),#386);
#382 = ( BOUNDED_CURVE() B_SPLINE_CURVE(2,(#383,#384,#385),
.UNSPECIFIED.,.F.,.F.) B_SPLINE_CURVE_WITH_KNOTS((3,3),(-2.745440166929,
-1.43099478095),.PIECEWISE_BEZIER_KNOTS.) CURVE() 
GEOMETRIC_REPRESENTATION_ITEM() RATIONAL_B_SPLINE_CURVE((1.,
1.223857530827,1.)) REPRESENTATION_ITEM('') );
#383 = CARTESIAN_POINT('',(2.36484842743,-20.5248756644));
#384 = CARTESIAN_POINT('',(0.35376762307,-23.97955611839));
#385 = CARTESIAN_POINT('',(-0.214986220675,-24.85814694378));
#386 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#387 = ORIENTED_EDGE('',*,*,#388,.F.);
#388 = EDGE_CURVE('',#389,#354,#391,.T.);
#389 = VERTEX_POINT('',#390);
#390 = CARTESIAN_POINT('',(47.95448794827,134.81517247829,8.909425));
#391 = SURFACE_CURVE('',#392,(#397,#415),.PCURVE_S1.);
#392 = HYPERBOLA('',#393,5.660643570614,3.268174089281);
#393 = AXIS2_PLACEMENT_3D('',#394,#395,#396);
#394 = CARTESIAN_POINT('',(48.276734331848,132.01393127837,
    1.433111027919));
#395 = DIRECTION('',(0.993448200572,0.11428330053,0.));
#396 = DIRECTION('',(-0.,0.,1.));
#397 = PCURVE('',#259,#398);
#398 = DEFINITIONAL_REPRESENTATION('',(#399),#414);
#399 = B_SPLINE_CURVE_WITH_KNOTS('',7,(#400,#401,#402,#403,#404,#405,
    #406,#407,#408,#409,#410,#411,#412,#413),.UNSPECIFIED.,.F.,.F.,(8,6,
    8),(-0.780944392204,-0.182155649481,0.416633093242),.UNSPECIFIED.);
#400 = CARTESIAN_POINT('',(2.544259286126,7.));
#401 = CARTESIAN_POINT('',(2.609026298689,6.582225919458));
#402 = CARTESIAN_POINT('',(2.678016167579,6.228275987451));
#403 = CARTESIAN_POINT('',(2.751112394062,5.933157545508));
#404 = CARTESIAN_POINT('',(2.828025030325,5.693020669904));
#405 = CARTESIAN_POINT('',(2.908199262521,5.505013098957));
#406 = CARTESIAN_POINT('',(2.990828163141,5.367193283594));
#407 = CARTESIAN_POINT('',(3.159111062272,5.18980983877));
#408 = CARTESIAN_POINT('',(3.244765336425,5.150246145181));
#409 = CARTESIAN_POINT('',(3.330992615756,5.158750501645));
#410 = CARTESIAN_POINT('',(3.416720508113,5.215143523923));
#411 = CARTESIAN_POINT('',(3.500874720768,5.320095318697));
#412 = CARTESIAN_POINT('',(3.582585389355,5.475144888455));
#413 = CARTESIAN_POINT('',(3.66120393592,5.682773700114));
#414 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#415 = PCURVE('',#416,#421);
#416 = PLANE('',#417);
#417 = AXIS2_PLACEMENT_3D('',#418,#419,#420);
#418 = CARTESIAN_POINT('',(48.301331,131.800116,28.));
#419 = DIRECTION('',(0.993448200572,0.11428330053,0.));
#420 = DIRECTION('',(-0.11428330053,0.993448200572,0.));
#421 = DEFINITIONAL_REPRESENTATION('',(#422),#426);
#422 = ( BOUNDED_CURVE() B_SPLINE_CURVE(2,(#423,#424,#425),
.UNSPECIFIED.,.F.,.F.) B_SPLINE_CURVE_WITH_KNOTS((3,3),(-0.780944392204,
0.416633093242),.PIECEWISE_BEZIER_KNOTS.) CURVE() 
GEOMETRIC_REPRESENTATION_ITEM() RATIONAL_B_SPLINE_CURVE((1.,
1.184694936394,1.)) REPRESENTATION_ITEM('') );
#423 = CARTESIAN_POINT('',(3.034940801702,-19.090575));
#424 = CARTESIAN_POINT('',(0.720514972162,-21.70925401773));
#425 = CARTESIAN_POINT('',(-1.186140051712,-20.40780129988));
#426 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#427 = ORIENTED_EDGE('',*,*,#428,.F.);
#428 = EDGE_CURVE('',#251,#389,#429,.T.);
#429 = SURFACE_CURVE('',#430,(#435,#442),.PCURVE_S1.);
#430 = CIRCLE('',#431,4.316451884327);
#431 = AXIS2_PLACEMENT_3D('',#432,#433,#434);
#432 = CARTESIAN_POINT('',(51.523496,132.387429,8.909425));
#433 = DIRECTION('',(0.,0.,1.));
#434 = DIRECTION('',(1.,0.,0.));
#435 = PCURVE('',#259,#436);
#436 = DEFINITIONAL_REPRESENTATION('',(#437),#441);
#437 = LINE('',#438,#439);
#438 = CARTESIAN_POINT('',(0.,7.));
#439 = VECTOR('',#440,1.);
#440 = DIRECTION('',(1.,0.));
#441 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#442 = PCURVE('',#295,#443);
#443 = DEFINITIONAL_REPRESENTATION('',(#444),#448);
#444 = CIRCLE('',#445,4.316451884327);
#445 = AXIS2_PLACEMENT_2D('',#446,#447);
#446 = CARTESIAN_POINT('',(0.,0.));
#447 = DIRECTION('',(1.,0.));
#448 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#449 = ORIENTED_EDGE('',*,*,#248,.F.);
#450 = ORIENTED_EDGE('',*,*,#451,.T.);
#451 = EDGE_CURVE('',#249,#249,#452,.T.);
#452 = SURFACE_CURVE('',#453,(#458,#465),.PCURVE_S1.);
#453 = CIRCLE('',#454,0.275);
#454 = AXIS2_PLACEMENT_3D('',#455,#456,#457);
#455 = CARTESIAN_POINT('',(51.523496,132.387429,1.909425));
#456 = DIRECTION('',(0.,0.,1.));
#457 = DIRECTION('',(1.,0.,0.));
#458 = PCURVE('',#259,#459);
#459 = DEFINITIONAL_REPRESENTATION('',(#460),#464);
#460 = LINE('',#461,#462);
#461 = CARTESIAN_POINT('',(0.,0.));
#462 = VECTOR('',#463,1.);
#463 = DIRECTION('',(1.,0.));
#464 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#465 = PCURVE('',#466,#471);
#466 = CYLINDRICAL_SURFACE('',#467,0.275);
#467 = AXIS2_PLACEMENT_3D('',#468,#469,#470);
#468 = CARTESIAN_POINT('',(51.523496,132.387429,1.909425));
#469 = DIRECTION('',(0.,0.,1.));
#470 = DIRECTION('',(1.,0.,0.));
#471 = DEFINITIONAL_REPRESENTATION('',(#472),#475);
#472 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#473,#474),.UNSPECIFIED.,.F.,.F.,
  (2,2),(0.,6.28318530718),.PIECEWISE_BEZIER_KNOTS.);
#473 = CARTESIAN_POINT('',(0.,0.));
#474 = CARTESIAN_POINT('',(6.28318530718,0.));
#475 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#476 = ADVANCED_FACE('',(#477),#466,.F.);
#477 = FACE_BOUND('',#478,.F.);
#478 = EDGE_LOOP('',(#479,#503,#524,#525));
#479 = ORIENTED_EDGE('',*,*,#480,.F.);
#480 = EDGE_CURVE('',#481,#481,#483,.T.);
#481 = VERTEX_POINT('',#482);
#482 = CARTESIAN_POINT('',(51.798496,132.387429,8.909425));
#483 = SURFACE_CURVE('',#484,(#489,#496),.PCURVE_S1.);
#484 = CIRCLE('',#485,0.275);
#485 = AXIS2_PLACEMENT_3D('',#486,#487,#488);
#486 = CARTESIAN_POINT('',(51.523496,132.387429,8.909425));
#487 = DIRECTION('',(0.,0.,1.));
#488 = DIRECTION('',(1.,0.,0.));
#489 = PCURVE('',#466,#490);
#490 = DEFINITIONAL_REPRESENTATION('',(#491),#495);
#491 = LINE('',#492,#493);
#492 = CARTESIAN_POINT('',(0.,7.));
#493 = VECTOR('',#494,1.);
#494 = DIRECTION('',(1.,0.));
#495 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#496 = PCURVE('',#295,#497);
#497 = DEFINITIONAL_REPRESENTATION('',(#498),#502);
#498 = CIRCLE('',#499,0.275);
#499 = AXIS2_PLACEMENT_2D('',#500,#501);
#500 = CARTESIAN_POINT('',(0.,0.));
#501 = DIRECTION('',(1.,0.));
#502 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#503 = ORIENTED_EDGE('',*,*,#504,.F.);
#504 = EDGE_CURVE('',#249,#481,#505,.T.);
#505 = SEAM_CURVE('',#506,(#510,#517),.PCURVE_S1.);
#506 = LINE('',#507,#508);
#507 = CARTESIAN_POINT('',(51.798496,132.387429,1.909425));
#508 = VECTOR('',#509,1.);
#509 = DIRECTION('',(0.,0.,1.));
#510 = PCURVE('',#466,#511);
#511 = DEFINITIONAL_REPRESENTATION('',(#512),#516);
#512 = LINE('',#513,#514);
#513 = CARTESIAN_POINT('',(6.28318530718,-0.));
#514 = VECTOR('',#515,1.);
#515 = DIRECTION('',(0.,1.));
#516 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#517 = PCURVE('',#466,#518);
#518 = DEFINITIONAL_REPRESENTATION('',(#519),#523);
#519 = LINE('',#520,#521);
#520 = CARTESIAN_POINT('',(0.,-0.));
#521 = VECTOR('',#522,1.);
#522 = DIRECTION('',(0.,1.));
#523 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#524 = ORIENTED_EDGE('',*,*,#451,.T.);
#525 = ORIENTED_EDGE('',*,*,#504,.T.);
#526 = ADVANCED_FACE('',(#527,#592),#295,.T.);
#527 = FACE_BOUND('',#528,.T.);
#528 = EDGE_LOOP('',(#529,#530,#531,#552,#573));
#529 = ORIENTED_EDGE('',*,*,#278,.T.);
#530 = ORIENTED_EDGE('',*,*,#428,.T.);
#531 = ORIENTED_EDGE('',*,*,#532,.F.);
#532 = EDGE_CURVE('',#533,#389,#535,.T.);
#533 = VERTEX_POINT('',#534);
#534 = CARTESIAN_POINT('',(48.301331,131.800116,8.909425));
#535 = SURFACE_CURVE('',#536,(#540,#546),.PCURVE_S1.);
#536 = LINE('',#537,#538);
#537 = CARTESIAN_POINT('',(48.289032665924,131.90702363918,8.909425));
#538 = VECTOR('',#539,1.);
#539 = DIRECTION('',(-0.11428330053,0.993448200572,0.));
#540 = PCURVE('',#295,#541);
#541 = DEFINITIONAL_REPRESENTATION('',(#542),#545);
#542 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#543,#544),.UNSPECIFIED.,.F.,.F.,
  (2,2),(-0.107612695986,4.828492559933),.PIECEWISE_BEZIER_KNOTS.);
#543 = CARTESIAN_POINT('',(-3.222165,-0.587313));
#544 = CARTESIAN_POINT('',(-3.786279400408,4.316451884327));
#545 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#546 = PCURVE('',#416,#547);
#547 = DEFINITIONAL_REPRESENTATION('',(#548),#551);
#548 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#549,#550),.UNSPECIFIED.,.F.,.F.,
  (2,2),(-0.107612695986,4.828492559933),.PIECEWISE_BEZIER_KNOTS.);
#549 = CARTESIAN_POINT('',(0.,-19.090575));
#550 = CARTESIAN_POINT('',(4.936105255919,-19.090575));
#551 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#552 = ORIENTED_EDGE('',*,*,#553,.F.);
#553 = EDGE_CURVE('',#554,#533,#556,.T.);
#554 = VERTEX_POINT('',#555);
#555 = CARTESIAN_POINT('',(50.449147,131.893892,8.909425));
#556 = SURFACE_CURVE('',#557,(#561,#567),.PCURVE_S1.);
#557 = LINE('',#558,#559);
#558 = CARTESIAN_POINT('',(50.996053120734,131.91777052049,8.909425));
#559 = VECTOR('',#560,1.);
#560 = DIRECTION('',(-0.999048214928,-4.361953975718E-02,0.));
#561 = PCURVE('',#295,#562);
#562 = DEFINITIONAL_REPRESENTATION('',(#563),#566);
#563 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#564,#565),.UNSPECIFIED.,.F.,.F.,
  (2,2),(0.547427153727,2.697289360481),.PIECEWISE_BEZIER_KNOTS.);
#564 = CARTESIAN_POINT('',(-1.074349,-0.493537));
#565 = CARTESIAN_POINT('',(-3.222165,-0.587313));
#566 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#567 = PCURVE('',#376,#568);
#568 = DEFINITIONAL_REPRESENTATION('',(#569),#572);
#569 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#570,#571),.UNSPECIFIED.,.F.,.F.,
  (2,2),(0.547427153727,2.697289360481),.PIECEWISE_BEZIER_KNOTS.);
#570 = CARTESIAN_POINT('',(0.,-19.090575));
#571 = CARTESIAN_POINT('',(2.149862206755,-19.090575));
#572 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#573 = ORIENTED_EDGE('',*,*,#574,.F.);
#574 = EDGE_CURVE('',#279,#554,#575,.T.);
#575 = SURFACE_CURVE('',#576,(#580,#586),.PCURVE_S1.);
#576 = LINE('',#577,#578);
#577 = CARTESIAN_POINT('',(50.717039405766,127.06608598088,8.909425));
#578 = VECTOR('',#579,1.);
#579 = DIRECTION('',(-5.540424023454E-02,0.998464005442,0.));
#580 = PCURVE('',#295,#581);
#581 = DEFINITIONAL_REPRESENTATION('',(#582),#585);
#582 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#583,#584),.UNSPECIFIED.,.F.,.F.,
  (2,2),(1.00643701657,4.835232910553),.PIECEWISE_BEZIER_KNOTS.);
#583 = CARTESIAN_POINT('',(-0.862217472481,-4.316451884327));
#584 = CARTESIAN_POINT('',(-1.074349,-0.493537));
#585 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#586 = PCURVE('',#341,#587);
#587 = DEFINITIONAL_REPRESENTATION('',(#588),#591);
#588 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#589,#590),.UNSPECIFIED.,.F.,.F.,
  (2,2),(1.00643701657,4.835232910553),.PIECEWISE_BEZIER_KNOTS.);
#589 = CARTESIAN_POINT('',(6.274925366886,-19.090575));
#590 = CARTESIAN_POINT('',(10.103721260868,-19.090575));
#591 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#592 = FACE_BOUND('',#593,.T.);
#593 = EDGE_LOOP('',(#594));
#594 = ORIENTED_EDGE('',*,*,#480,.F.);
#595 = ADVANCED_FACE('',(#596),#416,.F.);
#596 = FACE_BOUND('',#597,.F.);
#597 = EDGE_LOOP('',(#598,#599,#620));
#598 = ORIENTED_EDGE('',*,*,#532,.F.);
#599 = ORIENTED_EDGE('',*,*,#600,.T.);
#600 = EDGE_CURVE('',#533,#354,#601,.T.);
#601 = SURFACE_CURVE('',#602,(#606,#613),.PCURVE_S1.);
#602 = LINE('',#603,#604);
#603 = CARTESIAN_POINT('',(48.301331,131.800116,28.));
#604 = VECTOR('',#605,1.);
#605 = DIRECTION('',(0.,0.,-1.));
#606 = PCURVE('',#416,#607);
#607 = DEFINITIONAL_REPRESENTATION('',(#608),#612);
#608 = LINE('',#609,#610);
#609 = CARTESIAN_POINT('',(0.,0.));
#610 = VECTOR('',#611,1.);
#611 = DIRECTION('',(0.,-1.));
#612 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#613 = PCURVE('',#376,#614);
#614 = DEFINITIONAL_REPRESENTATION('',(#615),#619);
#615 = LINE('',#616,#617);
#616 = CARTESIAN_POINT('',(2.149862206755,0.));
#617 = VECTOR('',#618,1.);
#618 = DIRECTION('',(-0.,-1.));
#619 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#620 = ORIENTED_EDGE('',*,*,#388,.F.);
#621 = ADVANCED_FACE('',(#622),#376,.F.);
#622 = FACE_BOUND('',#623,.F.);
#623 = EDGE_LOOP('',(#624,#625,#646,#647));
#624 = ORIENTED_EDGE('',*,*,#553,.F.);
#625 = ORIENTED_EDGE('',*,*,#626,.T.);
#626 = EDGE_CURVE('',#554,#308,#627,.T.);
#627 = SURFACE_CURVE('',#628,(#632,#639),.PCURVE_S1.);
#628 = LINE('',#629,#630);
#629 = CARTESIAN_POINT('',(50.449147,131.893892,28.));
#630 = VECTOR('',#631,1.);
#631 = DIRECTION('',(0.,0.,-1.));
#632 = PCURVE('',#376,#633);
#633 = DEFINITIONAL_REPRESENTATION('',(#634),#638);
#634 = LINE('',#635,#636);
#635 = CARTESIAN_POINT('',(0.,0.));
#636 = VECTOR('',#637,1.);
#637 = DIRECTION('',(-0.,-1.));
#638 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#639 = PCURVE('',#341,#640);
#640 = DEFINITIONAL_REPRESENTATION('',(#641),#645);
#641 = LINE('',#642,#643);
#642 = CARTESIAN_POINT('',(10.103721260868,0.));
#643 = VECTOR('',#644,1.);
#644 = DIRECTION('',(0.,-1.));
#645 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#646 = ORIENTED_EDGE('',*,*,#353,.F.);
#647 = ORIENTED_EDGE('',*,*,#600,.F.);
#648 = ADVANCED_FACE('',(#649),#341,.F.);
#649 = FACE_BOUND('',#650,.F.);
#650 = EDGE_LOOP('',(#651,#652,#653));
#651 = ORIENTED_EDGE('',*,*,#626,.F.);
#652 = ORIENTED_EDGE('',*,*,#574,.F.);
#653 = ORIENTED_EDGE('',*,*,#307,.F.);
#654 = ( GEOMETRIC_REPRESENTATION_CONTEXT(3) 
GLOBAL_UNCERTAINTY_ASSIGNED_CONTEXT((#658)) GLOBAL_UNIT_ASSIGNED_CONTEXT
((#655,#656,#657)) REPRESENTATION_CONTEXT('Context #1',
  '3D Context with UNIT and UNCERTAINTY') );
#655 = ( LENGTH_UNIT() NAMED_UNIT(*) SI_UNIT(.MILLI.,.METRE.) );
#656 = ( NAMED_UNIT(*) PLANE_ANGLE_UNIT() SI_UNIT($,.RADIAN.) );
#657 = ( NAMED_UNIT(*) SI_UNIT($,.STERADIAN.) SOLID_ANGLE_UNIT() );
#658 = UNCERTAINTY_MEASURE_WITH_UNIT(LENGTH_MEASURE(1.E-07),#655,
  'distance_accuracy_value','confusion accuracy');
#659 = CONTEXT_DEPENDENT_SHAPE_REPRESENTATION(#660,#662);
#660 = ( REPRESENTATION_RELATIONSHIP('','',#241,#10) 
REPRESENTATION_RELATIONSHIP_WITH_TRANSFORMATION(#661) 
SHAPE_REPRESENTATION_RELATIONSHIP() );
#661 = ITEM_DEFINED_TRANSFORMATION('','',#11,#19);
#662 = PRODUCT_DEFINITION_SHAPE('Placement','Placement of an item',#663
  );
#663 = NEXT_ASSEMBLY_USAGE_OCCURRENCE('60','=>[0:1:1:3]','',#5,#236,$);
#664 = PRODUCT_RELATED_PRODUCT_CATEGORY('part',$,(#238));
#665 = SHAPE_DEFINITION_REPRESENTATION(#666,#672);
#666 = PRODUCT_DEFINITION_SHAPE('','',#667);
#667 = PRODUCT_DEFINITION('design','',#668,#671);
#668 = PRODUCT_DEFINITION_FORMATION('','',#669);
#669 = PRODUCT('PC1-P1_Part3','PC1-P1_Part3','',(#670));
#670 = PRODUCT_CONTEXT('',#2,'mechanical');
#671 = PRODUCT_DEFINITION_CONTEXT('part definition',#2,'design');
#672 = ADVANCED_BREP_SHAPE_REPRESENTATION('',(#11,#673),#1077);
#673 = MANIFOLD_SOLID_BREP('',#674);
#674 = CLOSED_SHELL('',(#675,#797,#898,#945,#1016,#1043,#1050));
#675 = ADVANCED_FACE('',(#676),#691,.T.);
#676 = FACE_BOUND('',#677,.T.);
#677 = EDGE_LOOP('',(#678,#714,#740,#773));
#678 = ORIENTED_EDGE('',*,*,#679,.T.);
#679 = EDGE_CURVE('',#680,#682,#684,.T.);
#680 = VERTEX_POINT('',#681);
#681 = CARTESIAN_POINT('',(50.656396039216,128.15896656931,8.909425));
#682 = VERTEX_POINT('',#683);
#683 = CARTESIAN_POINT('',(47.95448794827,134.81517247829,8.909425));
#684 = SURFACE_CURVE('',#685,(#690,#702),.PCURVE_S1.);
#685 = CIRCLE('',#686,4.316451884327);
#686 = AXIS2_PLACEMENT_3D('',#687,#688,#689);
#687 = CARTESIAN_POINT('',(51.523496,132.387429,8.909425));
#688 = DIRECTION('',(0.,0.,1.));
#689 = DIRECTION('',(-0.200882573007,-0.979615328515,0.));
#690 = PCURVE('',#691,#696);
#691 = CYLINDRICAL_SURFACE('',#692,4.316451884327);
#692 = AXIS2_PLACEMENT_3D('',#693,#694,#695);
#693 = CARTESIAN_POINT('',(51.523496,132.387429,8.909425));
#694 = DIRECTION('',(0.,0.,1.));
#695 = DIRECTION('',(1.,0.,0.));
#696 = DEFINITIONAL_REPRESENTATION('',(#697),#701);
#697 = LINE('',#698,#699);
#698 = CARTESIAN_POINT('',(4.510130204342,0.));
#699 = VECTOR('',#700,1.);
#700 = DIRECTION('',(1.,0.));
#701 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#702 = PCURVE('',#703,#708);
#703 = PLANE('',#704);
#704 = AXIS2_PLACEMENT_3D('',#705,#706,#707);
#705 = CARTESIAN_POINT('',(51.523496,132.387429,8.909425));
#706 = DIRECTION('',(0.,0.,1.));
#707 = DIRECTION('',(1.,0.,0.));
#708 = DEFINITIONAL_REPRESENTATION('',(#709),#713);
#709 = CIRCLE('',#710,4.316451884327);
#710 = AXIS2_PLACEMENT_2D('',#711,#712);
#711 = CARTESIAN_POINT('',(0.,0.));
#712 = DIRECTION('',(-0.200882573007,-0.979615328515));
#713 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#714 = ORIENTED_EDGE('',*,*,#715,.T.);
#715 = EDGE_CURVE('',#682,#716,#718,.T.);
#716 = VERTEX_POINT('',#717);
#717 = CARTESIAN_POINT('',(47.95448794827,134.81517247829,18.));
#718 = SURFACE_CURVE('',#719,(#723,#729),.PCURVE_S1.);
#719 = LINE('',#720,#721);
#720 = CARTESIAN_POINT('',(47.95448794827,134.81517247829,8.909425));
#721 = VECTOR('',#722,1.);
#722 = DIRECTION('',(0.,0.,1.));
#723 = PCURVE('',#691,#724);
#724 = DEFINITIONAL_REPRESENTATION('',(#725),#728);
#725 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#726,#727),.UNSPECIFIED.,.F.,.F.,
  (2,2),(0.,9.090575),.PIECEWISE_BEZIER_KNOTS.);
#726 = CARTESIAN_POINT('',(8.827444593306,0.));
#727 = CARTESIAN_POINT('',(8.827444593306,9.090575));
#728 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#729 = PCURVE('',#730,#735);
#730 = PLANE('',#731);
#731 = AXIS2_PLACEMENT_3D('',#732,#733,#734);
#732 = CARTESIAN_POINT('',(48.301331,131.800116,28.));
#733 = DIRECTION('',(0.993448200572,0.11428330053,0.));
#734 = DIRECTION('',(-0.11428330053,0.993448200572,0.));
#735 = DEFINITIONAL_REPRESENTATION('',(#736),#739);
#736 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#737,#738),.UNSPECIFIED.,.F.,.F.,
  (2,2),(-5.999999999062E-07,9.0905756),.PIECEWISE_BEZIER_KNOTS.);
#737 = CARTESIAN_POINT('',(3.034940801702,-19.0905756));
#738 = CARTESIAN_POINT('',(3.034940801702,-9.9999994));
#739 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#740 = ORIENTED_EDGE('',*,*,#741,.T.);
#741 = EDGE_CURVE('',#716,#742,#744,.T.);
#742 = VERTEX_POINT('',#743);
#743 = CARTESIAN_POINT('',(50.656396039216,128.15896656931,18.));
#744 = SURFACE_CURVE('',#745,(#750,#757),.PCURVE_S1.);
#745 = CIRCLE('',#746,4.316451884327);
#746 = AXIS2_PLACEMENT_3D('',#747,#748,#749);
#747 = CARTESIAN_POINT('',(51.523496,132.387429,18.));
#748 = DIRECTION('',(0.,0.,-1.));
#749 = DIRECTION('',(-0.826838372666,0.562439601634,0.));
#750 = PCURVE('',#691,#751);
#751 = DEFINITIONAL_REPRESENTATION('',(#752),#756);
#752 = LINE('',#753,#754);
#753 = CARTESIAN_POINT('',(8.827444593306,9.090575));
#754 = VECTOR('',#755,1.);
#755 = DIRECTION('',(-1.,-0.));
#756 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#757 = PCURVE('',#758,#763);
#758 = PLANE('',#759);
#759 = AXIS2_PLACEMENT_3D('',#760,#761,#762);
#760 = CARTESIAN_POINT('',(51.523496,132.387429,18.));
#761 = DIRECTION('',(0.,0.,1.));
#762 = DIRECTION('',(1.,0.,0.));
#763 = DEFINITIONAL_REPRESENTATION('',(#764),#772);
#764 = ( BOUNDED_CURVE() B_SPLINE_CURVE(2,(#765,#766,#767,#768,#769,#770
,#771),.UNSPECIFIED.,.T.,.F.) B_SPLINE_CURVE_WITH_KNOTS((1,2,2,2,2,1),(
    -2.094395102393,0.,2.094395102393,4.188790204786,6.28318530718,
8.377580409573),.UNSPECIFIED.) CURVE() GEOMETRIC_REPRESENTATION_ITEM() 
RATIONAL_B_SPLINE_CURVE((1.,0.5,1.,0.5,1.,0.5,1.)) REPRESENTATION_ITEM(
  '') );
#765 = CARTESIAN_POINT('',(-3.56900805173,2.427743478294));
#766 = CARTESIAN_POINT('',(0.635967000418,8.609446756513));
#767 = CARTESIAN_POINT('',(3.886991551939,1.876979899963));
#768 = CARTESIAN_POINT('',(7.138016103461,-4.855486956587));
#769 = CARTESIAN_POINT('',(-0.317983500209,-4.304723378257));
#770 = CARTESIAN_POINT('',(-7.773983103879,-3.753959799926));
#771 = CARTESIAN_POINT('',(-3.56900805173,2.427743478294));
#772 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#773 = ORIENTED_EDGE('',*,*,#774,.F.);
#774 = EDGE_CURVE('',#680,#742,#775,.T.);
#775 = SURFACE_CURVE('',#776,(#780,#786),.PCURVE_S1.);
#776 = LINE('',#777,#778);
#777 = CARTESIAN_POINT('',(50.656396039216,128.15896656931,8.909425));
#778 = VECTOR('',#779,1.);
#779 = DIRECTION('',(0.,0.,1.));
#780 = PCURVE('',#691,#781);
#781 = DEFINITIONAL_REPRESENTATION('',(#782),#785);
#782 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#783,#784),.UNSPECIFIED.,.F.,.F.,
  (2,2),(0.,9.090575),.PIECEWISE_BEZIER_KNOTS.);
#783 = CARTESIAN_POINT('',(4.510130204342,0.));
#784 = CARTESIAN_POINT('',(4.510130204342,9.090575));
#785 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#786 = PCURVE('',#787,#792);
#787 = PLANE('',#788);
#788 = AXIS2_PLACEMENT_3D('',#789,#790,#791);
#789 = CARTESIAN_POINT('',(51.008936,121.80569,28.));
#790 = DIRECTION('',(0.998464005442,5.540424023454E-02,0.));
#791 = DIRECTION('',(-5.540424023454E-02,0.998464005442,0.));
#792 = DEFINITIONAL_REPRESENTATION('',(#793),#796);
#793 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#794,#795),.UNSPECIFIED.,.F.,.F.,
  (2,2),(-5.999999999062E-07,9.0905756),.PIECEWISE_BEZIER_KNOTS.);
#794 = CARTESIAN_POINT('',(6.36305017976,-19.0905756));
#795 = CARTESIAN_POINT('',(6.36305017976,-9.9999994));
#796 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#797 = ADVANCED_FACE('',(#798,#867),#703,.F.);
#798 = FACE_BOUND('',#799,.F.);
#799 = EDGE_LOOP('',(#800,#801,#822,#848));
#800 = ORIENTED_EDGE('',*,*,#679,.T.);
#801 = ORIENTED_EDGE('',*,*,#802,.F.);
#802 = EDGE_CURVE('',#803,#682,#805,.T.);
#803 = VERTEX_POINT('',#804);
#804 = CARTESIAN_POINT('',(48.301331,131.800116,8.909425));
#805 = SURFACE_CURVE('',#806,(#810,#816),.PCURVE_S1.);
#806 = LINE('',#807,#808);
#807 = CARTESIAN_POINT('',(48.289032665924,131.90702363918,8.909425));
#808 = VECTOR('',#809,1.);
#809 = DIRECTION('',(-0.11428330053,0.993448200572,0.));
#810 = PCURVE('',#703,#811);
#811 = DEFINITIONAL_REPRESENTATION('',(#812),#815);
#812 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#813,#814),.UNSPECIFIED.,.F.,.F.,
  (2,2),(-0.107612695986,4.828492559933),.PIECEWISE_BEZIER_KNOTS.);
#813 = CARTESIAN_POINT('',(-3.222165,-0.587313));
#814 = CARTESIAN_POINT('',(-3.786279400408,4.316451884327));
#815 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#816 = PCURVE('',#730,#817);
#817 = DEFINITIONAL_REPRESENTATION('',(#818),#821);
#818 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#819,#820),.UNSPECIFIED.,.F.,.F.,
  (2,2),(-0.107612695986,4.828492559933),.PIECEWISE_BEZIER_KNOTS.);
#819 = CARTESIAN_POINT('',(0.,-19.090575));
#820 = CARTESIAN_POINT('',(4.936105255919,-19.090575));
#821 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#822 = ORIENTED_EDGE('',*,*,#823,.F.);
#823 = EDGE_CURVE('',#824,#803,#826,.T.);
#824 = VERTEX_POINT('',#825);
#825 = CARTESIAN_POINT('',(50.449147,131.893892,8.909425));
#826 = SURFACE_CURVE('',#827,(#831,#837),.PCURVE_S1.);
#827 = LINE('',#828,#829);
#828 = CARTESIAN_POINT('',(50.996053120734,131.91777052049,8.909425));
#829 = VECTOR('',#830,1.);
#830 = DIRECTION('',(-0.999048214928,-4.361953975718E-02,0.));
#831 = PCURVE('',#703,#832);
#832 = DEFINITIONAL_REPRESENTATION('',(#833),#836);
#833 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#834,#835),.UNSPECIFIED.,.F.,.F.,
  (2,2),(0.547427153727,2.697289360481),.PIECEWISE_BEZIER_KNOTS.);
#834 = CARTESIAN_POINT('',(-1.074349,-0.493537));
#835 = CARTESIAN_POINT('',(-3.222165,-0.587313));
#836 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#837 = PCURVE('',#838,#843);
#838 = PLANE('',#839);
#839 = AXIS2_PLACEMENT_3D('',#840,#841,#842);
#840 = CARTESIAN_POINT('',(50.449147,131.893892,28.));
#841 = DIRECTION('',(-4.361953975718E-02,0.999048214928,0.));
#842 = DIRECTION('',(-0.999048214928,-4.361953975718E-02,0.));
#843 = DEFINITIONAL_REPRESENTATION('',(#844),#847);
#844 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#845,#846),.UNSPECIFIED.,.F.,.F.,
  (2,2),(0.547427153727,2.697289360481),.PIECEWISE_BEZIER_KNOTS.);
#845 = CARTESIAN_POINT('',(0.,-19.090575));
#846 = CARTESIAN_POINT('',(2.149862206755,-19.090575));
#847 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#848 = ORIENTED_EDGE('',*,*,#849,.F.);
#849 = EDGE_CURVE('',#680,#824,#850,.T.);
#850 = SURFACE_CURVE('',#851,(#855,#861),.PCURVE_S1.);
#851 = LINE('',#852,#853);
#852 = CARTESIAN_POINT('',(50.717039405766,127.06608598088,8.909425));
#853 = VECTOR('',#854,1.);
#854 = DIRECTION('',(-5.540424023454E-02,0.998464005442,0.));
#855 = PCURVE('',#703,#856);
#856 = DEFINITIONAL_REPRESENTATION('',(#857),#860);
#857 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#858,#859),.UNSPECIFIED.,.F.,.F.,
  (2,2),(1.00643701657,4.835232910553),.PIECEWISE_BEZIER_KNOTS.);
#858 = CARTESIAN_POINT('',(-0.862217472481,-4.316451884327));
#859 = CARTESIAN_POINT('',(-1.074349,-0.493537));
#860 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#861 = PCURVE('',#787,#862);
#862 = DEFINITIONAL_REPRESENTATION('',(#863),#866);
#863 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#864,#865),.UNSPECIFIED.,.F.,.F.,
  (2,2),(1.00643701657,4.835232910553),.PIECEWISE_BEZIER_KNOTS.);
#864 = CARTESIAN_POINT('',(6.274925366886,-19.090575));
#865 = CARTESIAN_POINT('',(10.103721260868,-19.090575));
#866 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#867 = FACE_BOUND('',#868,.F.);
#868 = EDGE_LOOP('',(#869));
#869 = ORIENTED_EDGE('',*,*,#870,.F.);
#870 = EDGE_CURVE('',#871,#871,#873,.T.);
#871 = VERTEX_POINT('',#872);
#872 = CARTESIAN_POINT('',(51.798496,132.387429,8.909425));
#873 = SURFACE_CURVE('',#874,(#879,#886),.PCURVE_S1.);
#874 = CIRCLE('',#875,0.275);
#875 = AXIS2_PLACEMENT_3D('',#876,#877,#878);
#876 = CARTESIAN_POINT('',(51.523496,132.387429,8.909425));
#877 = DIRECTION('',(0.,0.,1.));
#878 = DIRECTION('',(1.,0.,0.));
#879 = PCURVE('',#703,#880);
#880 = DEFINITIONAL_REPRESENTATION('',(#881),#885);
#881 = CIRCLE('',#882,0.275);
#882 = AXIS2_PLACEMENT_2D('',#883,#884);
#883 = CARTESIAN_POINT('',(0.,0.));
#884 = DIRECTION('',(1.,0.));
#885 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#886 = PCURVE('',#887,#892);
#887 = CYLINDRICAL_SURFACE('',#888,0.275);
#888 = AXIS2_PLACEMENT_3D('',#889,#890,#891);
#889 = CARTESIAN_POINT('',(51.523496,132.387429,8.909425));
#890 = DIRECTION('',(0.,0.,1.));
#891 = DIRECTION('',(1.,0.,0.));
#892 = DEFINITIONAL_REPRESENTATION('',(#893),#897);
#893 = LINE('',#894,#895);
#894 = CARTESIAN_POINT('',(0.,0.));
#895 = VECTOR('',#896,1.);
#896 = DIRECTION('',(1.,0.));
#897 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#898 = ADVANCED_FACE('',(#899),#787,.F.);
#899 = FACE_BOUND('',#900,.F.);
#900 = EDGE_LOOP('',(#901,#924,#943,#944));
#901 = ORIENTED_EDGE('',*,*,#902,.F.);
#902 = EDGE_CURVE('',#903,#824,#905,.T.);
#903 = VERTEX_POINT('',#904);
#904 = CARTESIAN_POINT('',(50.449147,131.893892,18.));
#905 = SURFACE_CURVE('',#906,(#910,#917),.PCURVE_S1.);
#906 = LINE('',#907,#908);
#907 = CARTESIAN_POINT('',(50.449147,131.893892,28.));
#908 = VECTOR('',#909,1.);
#909 = DIRECTION('',(0.,0.,-1.));
#910 = PCURVE('',#787,#911);
#911 = DEFINITIONAL_REPRESENTATION('',(#912),#916);
#912 = LINE('',#913,#914);
#913 = CARTESIAN_POINT('',(10.103721260868,0.));
#914 = VECTOR('',#915,1.);
#915 = DIRECTION('',(0.,-1.));
#916 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#917 = PCURVE('',#838,#918);
#918 = DEFINITIONAL_REPRESENTATION('',(#919),#923);
#919 = LINE('',#920,#921);
#920 = CARTESIAN_POINT('',(0.,0.));
#921 = VECTOR('',#922,1.);
#922 = DIRECTION('',(-0.,-1.));
#923 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#924 = ORIENTED_EDGE('',*,*,#925,.F.);
#925 = EDGE_CURVE('',#742,#903,#926,.T.);
#926 = SURFACE_CURVE('',#927,(#931,#937),.PCURVE_S1.);
#927 = LINE('',#928,#929);
#928 = CARTESIAN_POINT('',(50.717039405766,127.06608598088,18.));
#929 = VECTOR('',#930,1.);
#930 = DIRECTION('',(-5.540424023454E-02,0.998464005442,0.));
#931 = PCURVE('',#787,#932);
#932 = DEFINITIONAL_REPRESENTATION('',(#933),#936);
#933 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#934,#935),.UNSPECIFIED.,.F.,.F.,
  (2,2),(1.00643701657,4.835232910553),.PIECEWISE_BEZIER_KNOTS.);
#934 = CARTESIAN_POINT('',(6.274925366886,-10.));
#935 = CARTESIAN_POINT('',(10.103721260868,-10.));
#936 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#937 = PCURVE('',#758,#938);
#938 = DEFINITIONAL_REPRESENTATION('',(#939),#942);
#939 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#940,#941),.UNSPECIFIED.,.F.,.F.,
  (2,2),(1.00643701657,4.835232910553),.PIECEWISE_BEZIER_KNOTS.);
#940 = CARTESIAN_POINT('',(-0.862217472481,-4.316451884327));
#941 = CARTESIAN_POINT('',(-1.074349,-0.493537));
#942 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#943 = ORIENTED_EDGE('',*,*,#774,.F.);
#944 = ORIENTED_EDGE('',*,*,#849,.T.);
#945 = ADVANCED_FACE('',(#946,#990),#758,.T.);
#946 = FACE_BOUND('',#947,.T.);
#947 = EDGE_LOOP('',(#948,#949,#950,#971));
#948 = ORIENTED_EDGE('',*,*,#925,.F.);
#949 = ORIENTED_EDGE('',*,*,#741,.F.);
#950 = ORIENTED_EDGE('',*,*,#951,.F.);
#951 = EDGE_CURVE('',#952,#716,#954,.T.);
#952 = VERTEX_POINT('',#953);
#953 = CARTESIAN_POINT('',(48.301331,131.800116,18.));
#954 = SURFACE_CURVE('',#955,(#959,#965),.PCURVE_S1.);
#955 = LINE('',#956,#957);
#956 = CARTESIAN_POINT('',(48.289032665924,131.90702363918,18.));
#957 = VECTOR('',#958,1.);
#958 = DIRECTION('',(-0.11428330053,0.993448200572,0.));
#959 = PCURVE('',#758,#960);
#960 = DEFINITIONAL_REPRESENTATION('',(#961),#964);
#961 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#962,#963),.UNSPECIFIED.,.F.,.F.,
  (2,2),(-0.107612695986,4.828492559933),.PIECEWISE_BEZIER_KNOTS.);
#962 = CARTESIAN_POINT('',(-3.222165,-0.587313));
#963 = CARTESIAN_POINT('',(-3.786279400408,4.316451884327));
#964 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#965 = PCURVE('',#730,#966);
#966 = DEFINITIONAL_REPRESENTATION('',(#967),#970);
#967 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#968,#969),.UNSPECIFIED.,.F.,.F.,
  (2,2),(-0.107612695986,4.828492559933),.PIECEWISE_BEZIER_KNOTS.);
#968 = CARTESIAN_POINT('',(0.,-10.));
#969 = CARTESIAN_POINT('',(4.936105255919,-10.));
#970 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#971 = ORIENTED_EDGE('',*,*,#972,.F.);
#972 = EDGE_CURVE('',#903,#952,#973,.T.);
#973 = SURFACE_CURVE('',#974,(#978,#984),.PCURVE_S1.);
#974 = LINE('',#975,#976);
#975 = CARTESIAN_POINT('',(50.996053120734,131.91777052049,18.));
#976 = VECTOR('',#977,1.);
#977 = DIRECTION('',(-0.999048214928,-4.361953975718E-02,0.));
#978 = PCURVE('',#758,#979);
#979 = DEFINITIONAL_REPRESENTATION('',(#980),#983);
#980 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#981,#982),.UNSPECIFIED.,.F.,.F.,
  (2,2),(0.547427153727,2.697289360481),.PIECEWISE_BEZIER_KNOTS.);
#981 = CARTESIAN_POINT('',(-1.074349,-0.493537));
#982 = CARTESIAN_POINT('',(-3.222165,-0.587313));
#983 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#984 = PCURVE('',#838,#985);
#985 = DEFINITIONAL_REPRESENTATION('',(#986),#989);
#986 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#987,#988),.UNSPECIFIED.,.F.,.F.,
  (2,2),(0.547427153727,2.697289360481),.PIECEWISE_BEZIER_KNOTS.);
#987 = CARTESIAN_POINT('',(0.,-10.));
#988 = CARTESIAN_POINT('',(2.149862206755,-10.));
#989 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#990 = FACE_BOUND('',#991,.T.);
#991 = EDGE_LOOP('',(#992));
#992 = ORIENTED_EDGE('',*,*,#993,.F.);
#993 = EDGE_CURVE('',#994,#994,#996,.T.);
#994 = VERTEX_POINT('',#995);
#995 = CARTESIAN_POINT('',(51.798496,132.387429,18.));
#996 = SURFACE_CURVE('',#997,(#1002,#1009),.PCURVE_S1.);
#997 = CIRCLE('',#998,0.275);
#998 = AXIS2_PLACEMENT_3D('',#999,#1000,#1001);
#999 = CARTESIAN_POINT('',(51.523496,132.387429,18.));
#1000 = DIRECTION('',(0.,0.,1.));
#1001 = DIRECTION('',(1.,0.,0.));
#1002 = PCURVE('',#758,#1003);
#1003 = DEFINITIONAL_REPRESENTATION('',(#1004),#1008);
#1004 = CIRCLE('',#1005,0.275);
#1005 = AXIS2_PLACEMENT_2D('',#1006,#1007);
#1006 = CARTESIAN_POINT('',(0.,0.));
#1007 = DIRECTION('',(1.,0.));
#1008 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1009 = PCURVE('',#887,#1010);
#1010 = DEFINITIONAL_REPRESENTATION('',(#1011),#1015);
#1011 = LINE('',#1012,#1013);
#1012 = CARTESIAN_POINT('',(0.,9.090575));
#1013 = VECTOR('',#1014,1.);
#1014 = DIRECTION('',(1.,0.));
#1015 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1016 = ADVANCED_FACE('',(#1017),#730,.F.);
#1017 = FACE_BOUND('',#1018,.F.);
#1018 = EDGE_LOOP('',(#1019,#1020,#1041,#1042));
#1019 = ORIENTED_EDGE('',*,*,#951,.F.);
#1020 = ORIENTED_EDGE('',*,*,#1021,.T.);
#1021 = EDGE_CURVE('',#952,#803,#1022,.T.);
#1022 = SURFACE_CURVE('',#1023,(#1027,#1034),.PCURVE_S1.);
#1023 = LINE('',#1024,#1025);
#1024 = CARTESIAN_POINT('',(48.301331,131.800116,28.));
#1025 = VECTOR('',#1026,1.);
#1026 = DIRECTION('',(0.,0.,-1.));
#1027 = PCURVE('',#730,#1028);
#1028 = DEFINITIONAL_REPRESENTATION('',(#1029),#1033);
#1029 = LINE('',#1030,#1031);
#1030 = CARTESIAN_POINT('',(0.,0.));
#1031 = VECTOR('',#1032,1.);
#1032 = DIRECTION('',(0.,-1.));
#1033 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1034 = PCURVE('',#838,#1035);
#1035 = DEFINITIONAL_REPRESENTATION('',(#1036),#1040);
#1036 = LINE('',#1037,#1038);
#1037 = CARTESIAN_POINT('',(2.149862206755,0.));
#1038 = VECTOR('',#1039,1.);
#1039 = DIRECTION('',(-0.,-1.));
#1040 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1041 = ORIENTED_EDGE('',*,*,#802,.T.);
#1042 = ORIENTED_EDGE('',*,*,#715,.T.);
#1043 = ADVANCED_FACE('',(#1044),#838,.F.);
#1044 = FACE_BOUND('',#1045,.F.);
#1045 = EDGE_LOOP('',(#1046,#1047,#1048,#1049));
#1046 = ORIENTED_EDGE('',*,*,#972,.F.);
#1047 = ORIENTED_EDGE('',*,*,#902,.T.);
#1048 = ORIENTED_EDGE('',*,*,#823,.T.);
#1049 = ORIENTED_EDGE('',*,*,#1021,.F.);
#1050 = ADVANCED_FACE('',(#1051),#887,.F.);
#1051 = FACE_BOUND('',#1052,.F.);
#1052 = EDGE_LOOP('',(#1053,#1054,#1075,#1076));
#1053 = ORIENTED_EDGE('',*,*,#993,.F.);
#1054 = ORIENTED_EDGE('',*,*,#1055,.F.);
#1055 = EDGE_CURVE('',#871,#994,#1056,.T.);
#1056 = SEAM_CURVE('',#1057,(#1061,#1068),.PCURVE_S1.);
#1057 = LINE('',#1058,#1059);
#1058 = CARTESIAN_POINT('',(51.798496,132.387429,8.909425));
#1059 = VECTOR('',#1060,1.);
#1060 = DIRECTION('',(0.,0.,1.));
#1061 = PCURVE('',#887,#1062);
#1062 = DEFINITIONAL_REPRESENTATION('',(#1063),#1067);
#1063 = LINE('',#1064,#1065);
#1064 = CARTESIAN_POINT('',(6.28318530718,-0.));
#1065 = VECTOR('',#1066,1.);
#1066 = DIRECTION('',(0.,1.));
#1067 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1068 = PCURVE('',#887,#1069);
#1069 = DEFINITIONAL_REPRESENTATION('',(#1070),#1074);
#1070 = LINE('',#1071,#1072);
#1071 = CARTESIAN_POINT('',(0.,-0.));
#1072 = VECTOR('',#1073,1.);
#1073 = DIRECTION('',(0.,1.));
#1074 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1075 = ORIENTED_EDGE('',*,*,#870,.T.);
#1076 = ORIENTED_EDGE('',*,*,#1055,.T.);
#1077 = ( GEOMETRIC_REPRESENTATION_CONTEXT(3) 
GLOBAL_UNCERTAINTY_ASSIGNED_CONTEXT((#1081)) 
GLOBAL_UNIT_ASSIGNED_CONTEXT((#1078,#1079,#1080)) REPRESENTATION_CONTEXT
('Context #1','3D Context with UNIT and UNCERTAINTY') );
#1078 = ( LENGTH_UNIT() NAMED_UNIT(*) SI_UNIT(.MILLI.,.METRE.) );
#1079 = ( NAMED_UNIT(*) PLANE_ANGLE_UNIT() SI_UNIT($,.RADIAN.) );
#1080 = ( NAMED_UNIT(*) SI_UNIT($,.STERADIAN.) SOLID_ANGLE_UNIT() );
#1081 = UNCERTAINTY_MEASURE_WITH_UNIT(LENGTH_MEASURE(1.E-07),#1078,
  'distance_accuracy_value','confusion accuracy');
#1082 = CONTEXT_DEPENDENT_SHAPE_REPRESENTATION(#1083,#1085);
#1083 = ( REPRESENTATION_RELATIONSHIP('','',#672,#10) 
REPRESENTATION_RELATIONSHIP_WITH_TRANSFORMATION(#1084) 
SHAPE_REPRESENTATION_RELATIONSHIP() );
#1084 = ITEM_DEFINED_TRANSFORMATION('','',#11,#23);
#1085 = PRODUCT_DEFINITION_SHAPE('Placement','Placement of an item',
  #1086);
#1086 = NEXT_ASSEMBLY_USAGE_OCCURRENCE('61','=>[0:1:1:4]','',#5,#667,$);
#1087 = PRODUCT_RELATED_PRODUCT_CATEGORY('part',$,(#669));
#1088 = SHAPE_DEFINITION_REPRESENTATION(#1089,#1095);
#1089 = PRODUCT_DEFINITION_SHAPE('','',#1090);
#1090 = PRODUCT_DEFINITION('design','',#1091,#1094);
#1091 = PRODUCT_DEFINITION_FORMATION('','',#1092);
#1092 = PRODUCT('PC1-P2_Part1','PC1-P2_Part1','',(#1093));
#1093 = PRODUCT_CONTEXT('',#2,'mechanical');
#1094 = PRODUCT_DEFINITION_CONTEXT('part definition',#2,'design');
#1095 = ADVANCED_BREP_SHAPE_REPRESENTATION('',(#11,#1096),#1194);
#1096 = MANIFOLD_SOLID_BREP('',#1097);
#1097 = CLOSED_SHELL('',(#1098,#1186,#1190));
#1098 = ADVANCED_FACE('',(#1099),#1112,.T.);
#1099 = FACE_BOUND('',#1100,.T.);
#1100 = EDGE_LOOP('',(#1101,#1135,#1158,#1185));
#1101 = ORIENTED_EDGE('',*,*,#1102,.F.);
#1102 = EDGE_CURVE('',#1103,#1103,#1105,.T.);
#1103 = VERTEX_POINT('',#1104);
#1104 = CARTESIAN_POINT('',(0.275,-6.73555739531E-17,17.02774));
#1105 = SURFACE_CURVE('',#1106,(#1111,#1123),.PCURVE_S1.);
#1106 = CIRCLE('',#1107,0.275);
#1107 = AXIS2_PLACEMENT_3D('',#1108,#1109,#1110);
#1108 = CARTESIAN_POINT('',(0.,0.,17.02774));
#1109 = DIRECTION('',(0.,0.,1.));
#1110 = DIRECTION('',(1.,0.,-0.));
#1111 = PCURVE('',#1112,#1117);
#1112 = CYLINDRICAL_SURFACE('',#1113,0.275);
#1113 = AXIS2_PLACEMENT_3D('',#1114,#1115,#1116);
#1114 = CARTESIAN_POINT('',(0.,0.,0.));
#1115 = DIRECTION('',(0.,0.,1.));
#1116 = DIRECTION('',(1.,0.,-0.));
#1117 = DEFINITIONAL_REPRESENTATION('',(#1118),#1122);
#1118 = LINE('',#1119,#1120);
#1119 = CARTESIAN_POINT('',(0.,17.02774));
#1120 = VECTOR('',#1121,1.);
#1121 = DIRECTION('',(1.,0.));
#1122 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1123 = PCURVE('',#1124,#1129);
#1124 = PLANE('',#1125);
#1125 = AXIS2_PLACEMENT_3D('',#1126,#1127,#1128);
#1126 = CARTESIAN_POINT('',(0.,0.,17.02774));
#1127 = DIRECTION('',(0.,0.,1.));
#1128 = DIRECTION('',(1.,0.,-0.));
#1129 = DEFINITIONAL_REPRESENTATION('',(#1130),#1134);
#1130 = CIRCLE('',#1131,0.275);
#1131 = AXIS2_PLACEMENT_2D('',#1132,#1133);
#1132 = CARTESIAN_POINT('',(0.,0.));
#1133 = DIRECTION('',(1.,0.));
#1134 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1135 = ORIENTED_EDGE('',*,*,#1136,.F.);
#1136 = EDGE_CURVE('',#1137,#1103,#1139,.T.);
#1137 = VERTEX_POINT('',#1138);
#1138 = CARTESIAN_POINT('',(0.275,-6.73555739531E-17,0.));
#1139 = SEAM_CURVE('',#1140,(#1144,#1151),.PCURVE_S1.);
#1140 = LINE('',#1141,#1142);
#1141 = CARTESIAN_POINT('',(0.275,-6.73555739531E-17,0.));
#1142 = VECTOR('',#1143,1.);
#1143 = DIRECTION('',(0.,0.,1.));
#1144 = PCURVE('',#1112,#1145);
#1145 = DEFINITIONAL_REPRESENTATION('',(#1146),#1150);
#1146 = LINE('',#1147,#1148);
#1147 = CARTESIAN_POINT('',(6.28318530718,-0.));
#1148 = VECTOR('',#1149,1.);
#1149 = DIRECTION('',(0.,1.));
#1150 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1151 = PCURVE('',#1112,#1152);
#1152 = DEFINITIONAL_REPRESENTATION('',(#1153),#1157);
#1153 = LINE('',#1154,#1155);
#1154 = CARTESIAN_POINT('',(0.,-0.));
#1155 = VECTOR('',#1156,1.);
#1156 = DIRECTION('',(0.,1.));
#1157 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1158 = ORIENTED_EDGE('',*,*,#1159,.T.);
#1159 = EDGE_CURVE('',#1137,#1137,#1160,.T.);
#1160 = SURFACE_CURVE('',#1161,(#1166,#1173),.PCURVE_S1.);
#1161 = CIRCLE('',#1162,0.275);
#1162 = AXIS2_PLACEMENT_3D('',#1163,#1164,#1165);
#1163 = CARTESIAN_POINT('',(0.,0.,0.));
#1164 = DIRECTION('',(0.,0.,1.));
#1165 = DIRECTION('',(1.,0.,-0.));
#1166 = PCURVE('',#1112,#1167);
#1167 = DEFINITIONAL_REPRESENTATION('',(#1168),#1172);
#1168 = LINE('',#1169,#1170);
#1169 = CARTESIAN_POINT('',(0.,0.));
#1170 = VECTOR('',#1171,1.);
#1171 = DIRECTION('',(1.,0.));
#1172 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1173 = PCURVE('',#1174,#1179);
#1174 = PLANE('',#1175);
#1175 = AXIS2_PLACEMENT_3D('',#1176,#1177,#1178);
#1176 = CARTESIAN_POINT('',(0.,0.,0.));
#1177 = DIRECTION('',(0.,0.,1.));
#1178 = DIRECTION('',(1.,0.,-0.));
#1179 = DEFINITIONAL_REPRESENTATION('',(#1180),#1184);
#1180 = CIRCLE('',#1181,0.275);
#1181 = AXIS2_PLACEMENT_2D('',#1182,#1183);
#1182 = CARTESIAN_POINT('',(0.,0.));
#1183 = DIRECTION('',(1.,0.));
#1184 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1185 = ORIENTED_EDGE('',*,*,#1136,.T.);
#1186 = ADVANCED_FACE('',(#1187),#1124,.T.);
#1187 = FACE_BOUND('',#1188,.T.);
#1188 = EDGE_LOOP('',(#1189));
#1189 = ORIENTED_EDGE('',*,*,#1102,.T.);
#1190 = ADVANCED_FACE('',(#1191),#1174,.F.);
#1191 = FACE_BOUND('',#1192,.T.);
#1192 = EDGE_LOOP('',(#1193));
#1193 = ORIENTED_EDGE('',*,*,#1159,.F.);
#1194 = ( GEOMETRIC_REPRESENTATION_CONTEXT(3) 
GLOBAL_UNCERTAINTY_ASSIGNED_CONTEXT((#1198)) 
GLOBAL_UNIT_ASSIGNED_CONTEXT((#1195,#1196,#1197)) REPRESENTATION_CONTEXT
('Context #1','3D Context with UNIT and UNCERTAINTY') );
#1195 = ( LENGTH_UNIT() NAMED_UNIT(*) SI_UNIT(.MILLI.,.METRE.) );
#1196 = ( NAMED_UNIT(*) PLANE_ANGLE_UNIT() SI_UNIT($,.RADIAN.) );
#1197 = ( NAMED_UNIT(*) SI_UNIT($,.STERADIAN.) SOLID_ANGLE_UNIT() );
#1198 = UNCERTAINTY_MEASURE_WITH_UNIT(LENGTH_MEASURE(1.E-07),#1195,
  'distance_accuracy_value','confusion accuracy');
#1199 = CONTEXT_DEPENDENT_SHAPE_REPRESENTATION(#1200,#1202);
#1200 = ( REPRESENTATION_RELATIONSHIP('','',#1095,#10) 
REPRESENTATION_RELATIONSHIP_WITH_TRANSFORMATION(#1201) 
SHAPE_REPRESENTATION_RELATIONSHIP() );
#1201 = ITEM_DEFINED_TRANSFORMATION('','',#11,#27);
#1202 = PRODUCT_DEFINITION_SHAPE('Placement','Placement of an item',
  #1203);
#1203 = NEXT_ASSEMBLY_USAGE_OCCURRENCE('62','=>[0:1:1:5]','',#5,#1090,$
  );
#1204 = PRODUCT_RELATED_PRODUCT_CATEGORY('part',$,(#1092));
#1205 = SHAPE_DEFINITION_REPRESENTATION(#1206,#1212);
#1206 = PRODUCT_DEFINITION_SHAPE('','',#1207);
#1207 = PRODUCT_DEFINITION('design','',#1208,#1211);
#1208 = PRODUCT_DEFINITION_FORMATION('','',#1209);
#1209 = PRODUCT('PC1-P2_Part2','PC1-P2_Part2','',(#1210));
#1210 = PRODUCT_CONTEXT('',#2,'mechanical');
#1211 = PRODUCT_DEFINITION_CONTEXT('part definition',#2,'design');
#1212 = ADVANCED_BREP_SHAPE_REPRESENTATION('',(#11,#1213),#1607);
#1213 = MANIFOLD_SOLID_BREP('',#1214);
#1214 = CLOSED_SHELL('',(#1215,#1452,#1502,#1570,#1575,#1601));
#1215 = ADVANCED_FACE('',(#1216),#1230,.T.);
#1216 = FACE_BOUND('',#1217,.T.);
#1217 = EDGE_LOOP('',(#1218,#1248,#1277,#1309,#1341,#1365,#1403,#1425,
    #1426));
#1218 = ORIENTED_EDGE('',*,*,#1219,.T.);
#1219 = EDGE_CURVE('',#1220,#1222,#1224,.T.);
#1220 = VERTEX_POINT('',#1221);
#1221 = CARTESIAN_POINT('',(51.80692,135.691161,0.97226));
#1222 = VERTEX_POINT('',#1223);
#1223 = CARTESIAN_POINT('',(55.848371884327,135.691161,7.97226));
#1224 = SEAM_CURVE('',#1225,(#1229,#1241),.PCURVE_S1.);
#1225 = LINE('',#1226,#1227);
#1226 = CARTESIAN_POINT('',(51.80692,135.691161,0.97226));
#1227 = VECTOR('',#1228,1.);
#1228 = DIRECTION('',(0.5,-1.224646799147E-16,0.866025403784));
#1229 = PCURVE('',#1230,#1235);
#1230 = CONICAL_SURFACE('',#1231,0.275,0.523598775598);
#1231 = AXIS2_PLACEMENT_3D('',#1232,#1233,#1234);
#1232 = CARTESIAN_POINT('',(51.53192,135.691161,0.97226));
#1233 = DIRECTION('',(0.,0.,1.));
#1234 = DIRECTION('',(1.,0.,0.));
#1235 = DEFINITIONAL_REPRESENTATION('',(#1236),#1240);
#1236 = LINE('',#1237,#1238);
#1237 = CARTESIAN_POINT('',(6.28318530718,-0.));
#1238 = VECTOR('',#1239,1.);
#1239 = DIRECTION('',(0.,1.));
#1240 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1241 = PCURVE('',#1230,#1242);
#1242 = DEFINITIONAL_REPRESENTATION('',(#1243),#1247);
#1243 = LINE('',#1244,#1245);
#1244 = CARTESIAN_POINT('',(0.,-0.));
#1245 = VECTOR('',#1246,1.);
#1246 = DIRECTION('',(0.,1.));
#1247 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1248 = ORIENTED_EDGE('',*,*,#1249,.F.);
#1249 = EDGE_CURVE('',#1250,#1222,#1252,.T.);
#1250 = VERTEX_POINT('',#1251);
#1251 = CARTESIAN_POINT('',(50.470603233643,131.50721984468,7.97226));
#1252 = SURFACE_CURVE('',#1253,(#1258,#1265),.PCURVE_S1.);
#1253 = CIRCLE('',#1254,4.316451884327);
#1254 = AXIS2_PLACEMENT_3D('',#1255,#1256,#1257);
#1255 = CARTESIAN_POINT('',(51.53192,135.691161,7.97226));
#1256 = DIRECTION('',(0.,0.,1.));
#1257 = DIRECTION('',(1.,0.,0.));
#1258 = PCURVE('',#1230,#1259);
#1259 = DEFINITIONAL_REPRESENTATION('',(#1260),#1264);
#1260 = LINE('',#1261,#1262);
#1261 = CARTESIAN_POINT('',(0.,7.));
#1262 = VECTOR('',#1263,1.);
#1263 = DIRECTION('',(1.,0.));
#1264 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1265 = PCURVE('',#1266,#1271);
#1266 = PLANE('',#1267);
#1267 = AXIS2_PLACEMENT_3D('',#1268,#1269,#1270);
#1268 = CARTESIAN_POINT('',(51.53192,135.691161,7.97226));
#1269 = DIRECTION('',(0.,0.,1.));
#1270 = DIRECTION('',(1.,0.,0.));
#1271 = DEFINITIONAL_REPRESENTATION('',(#1272),#1276);
#1272 = CIRCLE('',#1273,4.316451884327);
#1273 = AXIS2_PLACEMENT_2D('',#1274,#1275);
#1274 = CARTESIAN_POINT('',(0.,0.));
#1275 = DIRECTION('',(1.,0.));
#1276 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1277 = ORIENTED_EDGE('',*,*,#1278,.F.);
#1278 = EDGE_CURVE('',#1279,#1250,#1281,.T.);
#1279 = VERTEX_POINT('',#1280);
#1280 = CARTESIAN_POINT('',(50.449147,131.893892,7.335167305496));
#1281 = SURFACE_CURVE('',#1282,(#1287,#1297),.PCURVE_S1.);
#1282 = HYPERBOLA('',#1283,2.236934386969,1.291494670476);
#1283 = AXIS2_PLACEMENT_3D('',#1284,#1285,#1286);
#1284 = CARTESIAN_POINT('',(50.242409058309,135.61960671901,
    0.495946027919));
#1285 = DIRECTION('',(0.998464005442,5.540424023454E-02,0.));
#1286 = DIRECTION('',(-0.,0.,1.));
#1287 = PCURVE('',#1230,#1288);
#1288 = DEFINITIONAL_REPRESENTATION('',(#1289),#1296);
#1289 = B_SPLINE_CURVE_WITH_KNOTS('',5,(#1290,#1291,#1292,#1293,#1294,
    #1295),.UNSPECIFIED.,.F.,.F.,(6,6),(1.490453364319,1.876609079679),
  .PIECEWISE_BEZIER_KNOTS.);
#1290 = CARTESIAN_POINT('',(4.3246778132,4.740639909249));
#1291 = CARTESIAN_POINT('',(4.357793114506,5.104633263599));
#1292 = CARTESIAN_POINT('',(4.388020127502,5.507522627771));
#1293 = CARTESIAN_POINT('',(4.415620175954,5.953835122136));
#1294 = CARTESIAN_POINT('',(4.440856712557,6.449046887062));
#1295 = CARTESIAN_POINT('',(4.463964490043,7.));
#1296 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1297 = PCURVE('',#1298,#1303);
#1298 = PLANE('',#1299);
#1299 = AXIS2_PLACEMENT_3D('',#1300,#1301,#1302);
#1300 = CARTESIAN_POINT('',(51.008936,121.80569,28.));
#1301 = DIRECTION('',(0.998464005442,5.540424023454E-02,0.));
#1302 = DIRECTION('',(-5.540424023454E-02,0.998464005442,0.));
#1303 = DEFINITIONAL_REPRESENTATION('',(#1304),#1308);
#1304 = ( BOUNDED_CURVE() B_SPLINE_CURVE(2,(#1305,#1306,#1307),
.UNSPECIFIED.,.F.,.F.) B_SPLINE_CURVE_WITH_KNOTS((3,3),(1.490453364319,
1.876609079679),.PIECEWISE_BEZIER_KNOTS.) CURVE() 
GEOMETRIC_REPRESENTATION_ITEM() RATIONAL_B_SPLINE_CURVE((1.,
1.01869750691,1.)) REPRESENTATION_ITEM('') );
#1305 = CARTESIAN_POINT('',(11.114093386955,-22.28710009075));
#1306 = CARTESIAN_POINT('',(10.539661418505,-21.38825836883));
#1307 = CARTESIAN_POINT('',(9.716454265553,-20.02774));
#1308 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1309 = ORIENTED_EDGE('',*,*,#1310,.F.);
#1310 = EDGE_CURVE('',#1311,#1279,#1313,.T.);
#1311 = VERTEX_POINT('',#1312);
#1312 = CARTESIAN_POINT('',(49.553535824782,131.85478863473,7.97226));
#1313 = SURFACE_CURVE('',#1314,(#1319,#1329),.PCURVE_S1.);
#1314 = HYPERBOLA('',#1315,6.488998024353,3.746424756131);
#1315 = AXIS2_PLACEMENT_3D('',#1316,#1317,#1318);
#1316 = CARTESIAN_POINT('',(51.695337323597,131.94830203502,
    0.495946027919));
#1317 = DIRECTION('',(-4.361953975718E-02,0.999048214928,0.));
#1318 = DIRECTION('',(0.,0.,1.));
#1319 = PCURVE('',#1230,#1320);
#1320 = DEFINITIONAL_REPRESENTATION('',(#1321),#1328);
#1321 = B_SPLINE_CURVE_WITH_KNOTS('',5,(#1322,#1323,#1324,#1325,#1326,
    #1327),.UNSPECIFIED.,.F.,.F.,(6,6),(-0.544872819069,-0.27219350445),
  .PIECEWISE_BEZIER_KNOTS.);
#1322 = CARTESIAN_POINT('',(4.236267209909,7.));
#1323 = CARTESIAN_POINT('',(4.283601111093,6.797495076471));
#1324 = CARTESIAN_POINT('',(4.332537472006,6.622784760042));
#1325 = CARTESIAN_POINT('',(4.382928739189,6.474614879899));
#1326 = CARTESIAN_POINT('',(4.434552874931,6.352073079516));
#1327 = CARTESIAN_POINT('',(4.487129018099,6.254554544388));
#1328 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1329 = PCURVE('',#1330,#1335);
#1330 = PLANE('',#1331);
#1331 = AXIS2_PLACEMENT_3D('',#1332,#1333,#1334);
#1332 = CARTESIAN_POINT('',(50.449147,131.893892,28.));
#1333 = DIRECTION('',(-4.361953975718E-02,0.999048214928,0.));
#1334 = DIRECTION('',(-0.999048214928,-4.361953975718E-02,0.));
#1335 = DEFINITIONAL_REPRESENTATION('',(#1336),#1340);
#1336 = ( BOUNDED_CURVE() B_SPLINE_CURVE(2,(#1337,#1338,#1339),
.UNSPECIFIED.,.F.,.F.) B_SPLINE_CURVE_WITH_KNOTS((3,3),(-0.544872819069,
-0.27219350445),.PIECEWISE_BEZIER_KNOTS.) CURVE() 
GEOMETRIC_REPRESENTATION_ITEM() RATIONAL_B_SPLINE_CURVE((1.,
1.009308657185,1.)) REPRESENTATION_ITEM('') );
#1337 = CARTESIAN_POINT('',(0.896464416667,-20.02774));
#1338 = CARTESIAN_POINT('',(0.311580402125,-20.53088857264));
#1339 = CARTESIAN_POINT('',(-0.214986220675,-20.77318545561));
#1340 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1341 = ORIENTED_EDGE('',*,*,#1342,.F.);
#1342 = EDGE_CURVE('',#1343,#1311,#1345,.T.);
#1343 = VERTEX_POINT('',#1344);
#1344 = CARTESIAN_POINT('',(48.16434175799,132.99094366547,7.97226));
#1345 = SURFACE_CURVE('',#1346,(#1351,#1358),.PCURVE_S1.);
#1346 = CIRCLE('',#1347,4.316451884327);
#1347 = AXIS2_PLACEMENT_3D('',#1348,#1349,#1350);
#1348 = CARTESIAN_POINT('',(51.53192,135.691161,7.97226));
#1349 = DIRECTION('',(0.,0.,1.));
#1350 = DIRECTION('',(1.,0.,0.));
#1351 = PCURVE('',#1230,#1352);
#1352 = DEFINITIONAL_REPRESENTATION('',(#1353),#1357);
#1353 = LINE('',#1354,#1355);
#1354 = CARTESIAN_POINT('',(0.,7.));
#1355 = VECTOR('',#1356,1.);
#1356 = DIRECTION('',(1.,0.));
#1357 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1358 = PCURVE('',#1266,#1359);
#1359 = DEFINITIONAL_REPRESENTATION('',(#1360),#1364);
#1360 = CIRCLE('',#1361,4.316451884327);
#1361 = AXIS2_PLACEMENT_2D('',#1362,#1363);
#1362 = CARTESIAN_POINT('',(0.,0.));
#1363 = DIRECTION('',(1.,0.));
#1364 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1365 = ORIENTED_EDGE('',*,*,#1366,.F.);
#1366 = EDGE_CURVE('',#1367,#1343,#1369,.T.);
#1367 = VERTEX_POINT('',#1368);
#1368 = CARTESIAN_POINT('',(47.63917157096,137.55617213613,7.97226));
#1369 = SURFACE_CURVE('',#1370,(#1375,#1391),.PCURVE_S1.);
#1370 = HYPERBOLA('',#1371,6.329094293272,3.654104293947);
#1371 = AXIS2_PLACEMENT_3D('',#1372,#1373,#1374);
#1372 = CARTESIAN_POINT('',(47.901756664475,135.2735579008,
    0.495946027919));
#1373 = DIRECTION('',(0.993448200572,0.11428330053,0.));
#1374 = DIRECTION('',(-0.,0.,1.));
#1375 = PCURVE('',#1230,#1376);
#1376 = DEFINITIONAL_REPRESENTATION('',(#1377),#1390);
#1377 = B_SPLINE_CURVE_WITH_KNOTS('',6,(#1378,#1379,#1380,#1381,#1382,
    #1383,#1384,#1385,#1386,#1387,#1388,#1389),.UNSPECIFIED.,.F.,.F.,(7,
    5,7),(-0.593355754776,3.10862446895E-15,0.593355754776),
  .UNSPECIFIED.);
#1378 = CARTESIAN_POINT('',(2.694805373304,7.));
#1379 = CARTESIAN_POINT('',(2.778523199482,6.606439204532));
#1380 = CARTESIAN_POINT('',(2.867529158094,6.300618137545));
#1381 = CARTESIAN_POINT('',(2.961186074241,6.075609202424));
#1382 = CARTESIAN_POINT('',(3.058341549055,5.92705673564));
#1383 = CARTESIAN_POINT('',(3.157233571351,5.852780321191));
#1384 = CARTESIAN_POINT('',(3.355018822943,5.852780321191));
#1385 = CARTESIAN_POINT('',(3.453910845239,5.92705673564));
#1386 = CARTESIAN_POINT('',(3.551066320053,6.075609202423));
#1387 = CARTESIAN_POINT('',(3.6447232362,6.300618137545));
#1388 = CARTESIAN_POINT('',(3.733729194811,6.606439204532));
#1389 = CARTESIAN_POINT('',(3.81744702099,7.));
#1390 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1391 = PCURVE('',#1392,#1397);
#1392 = PLANE('',#1393);
#1393 = AXIS2_PLACEMENT_3D('',#1394,#1395,#1396);
#1394 = CARTESIAN_POINT('',(48.301331,131.800116,28.));
#1395 = DIRECTION('',(0.993448200572,0.11428330053,0.));
#1396 = DIRECTION('',(-0.11428330053,0.993448200572,0.));
#1397 = DEFINITIONAL_REPRESENTATION('',(#1398),#1402);
#1398 = ( BOUNDED_CURVE() B_SPLINE_CURVE(2,(#1399,#1400,#1401),
.UNSPECIFIED.,.F.,.F.) B_SPLINE_CURVE_WITH_KNOTS((3,3),(-0.593355754776,
0.593355754776),.PIECEWISE_BEZIER_KNOTS.) CURVE() 
GEOMETRIC_REPRESENTATION_ITEM() RATIONAL_B_SPLINE_CURVE((1.,
1.181261271463,1.)) REPRESENTATION_ITEM('') );
#1399 = CARTESIAN_POINT('',(5.794017375867,-20.02774));
#1400 = CARTESIAN_POINT('',(3.496349280021,-22.14614167429));
#1401 = CARTESIAN_POINT('',(1.198681184175,-20.02774));
#1402 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1403 = ORIENTED_EDGE('',*,*,#1404,.F.);
#1404 = EDGE_CURVE('',#1222,#1367,#1405,.T.);
#1405 = SURFACE_CURVE('',#1406,(#1411,#1418),.PCURVE_S1.);
#1406 = CIRCLE('',#1407,4.316451884327);
#1407 = AXIS2_PLACEMENT_3D('',#1408,#1409,#1410);
#1408 = CARTESIAN_POINT('',(51.53192,135.691161,7.97226));
#1409 = DIRECTION('',(0.,0.,1.));
#1410 = DIRECTION('',(1.,0.,0.));
#1411 = PCURVE('',#1230,#1412);
#1412 = DEFINITIONAL_REPRESENTATION('',(#1413),#1417);
#1413 = LINE('',#1414,#1415);
#1414 = CARTESIAN_POINT('',(0.,7.));
#1415 = VECTOR('',#1416,1.);
#1416 = DIRECTION('',(1.,0.));
#1417 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1418 = PCURVE('',#1266,#1419);
#1419 = DEFINITIONAL_REPRESENTATION('',(#1420),#1424);
#1420 = CIRCLE('',#1421,4.316451884327);
#1421 = AXIS2_PLACEMENT_2D('',#1422,#1423);
#1422 = CARTESIAN_POINT('',(0.,0.));
#1423 = DIRECTION('',(1.,0.));
#1424 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1425 = ORIENTED_EDGE('',*,*,#1219,.F.);
#1426 = ORIENTED_EDGE('',*,*,#1427,.T.);
#1427 = EDGE_CURVE('',#1220,#1220,#1428,.T.);
#1428 = SURFACE_CURVE('',#1429,(#1434,#1441),.PCURVE_S1.);
#1429 = CIRCLE('',#1430,0.275);
#1430 = AXIS2_PLACEMENT_3D('',#1431,#1432,#1433);
#1431 = CARTESIAN_POINT('',(51.53192,135.691161,0.97226));
#1432 = DIRECTION('',(0.,0.,1.));
#1433 = DIRECTION('',(1.,0.,0.));
#1434 = PCURVE('',#1230,#1435);
#1435 = DEFINITIONAL_REPRESENTATION('',(#1436),#1440);
#1436 = LINE('',#1437,#1438);
#1437 = CARTESIAN_POINT('',(0.,0.));
#1438 = VECTOR('',#1439,1.);
#1439 = DIRECTION('',(1.,0.));
#1440 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1441 = PCURVE('',#1442,#1447);
#1442 = CYLINDRICAL_SURFACE('',#1443,0.275);
#1443 = AXIS2_PLACEMENT_3D('',#1444,#1445,#1446);
#1444 = CARTESIAN_POINT('',(51.53192,135.691161,0.97226));
#1445 = DIRECTION('',(0.,0.,1.));
#1446 = DIRECTION('',(1.,0.,0.));
#1447 = DEFINITIONAL_REPRESENTATION('',(#1448),#1451);
#1448 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#1449,#1450),.UNSPECIFIED.,.F.,
  .F.,(2,2),(0.,6.28318530718),.PIECEWISE_BEZIER_KNOTS.);
#1449 = CARTESIAN_POINT('',(0.,0.));
#1450 = CARTESIAN_POINT('',(6.28318530718,0.));
#1451 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1452 = ADVANCED_FACE('',(#1453),#1442,.F.);
#1453 = FACE_BOUND('',#1454,.F.);
#1454 = EDGE_LOOP('',(#1455,#1479,#1500,#1501));
#1455 = ORIENTED_EDGE('',*,*,#1456,.F.);
#1456 = EDGE_CURVE('',#1457,#1457,#1459,.T.);
#1457 = VERTEX_POINT('',#1458);
#1458 = CARTESIAN_POINT('',(51.80692,135.691161,7.97226));
#1459 = SURFACE_CURVE('',#1460,(#1465,#1472),.PCURVE_S1.);
#1460 = CIRCLE('',#1461,0.275);
#1461 = AXIS2_PLACEMENT_3D('',#1462,#1463,#1464);
#1462 = CARTESIAN_POINT('',(51.53192,135.691161,7.97226));
#1463 = DIRECTION('',(0.,0.,1.));
#1464 = DIRECTION('',(1.,0.,0.));
#1465 = PCURVE('',#1442,#1466);
#1466 = DEFINITIONAL_REPRESENTATION('',(#1467),#1471);
#1467 = LINE('',#1468,#1469);
#1468 = CARTESIAN_POINT('',(0.,7.));
#1469 = VECTOR('',#1470,1.);
#1470 = DIRECTION('',(1.,0.));
#1471 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1472 = PCURVE('',#1266,#1473);
#1473 = DEFINITIONAL_REPRESENTATION('',(#1474),#1478);
#1474 = CIRCLE('',#1475,0.275);
#1475 = AXIS2_PLACEMENT_2D('',#1476,#1477);
#1476 = CARTESIAN_POINT('',(0.,0.));
#1477 = DIRECTION('',(1.,0.));
#1478 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1479 = ORIENTED_EDGE('',*,*,#1480,.F.);
#1480 = EDGE_CURVE('',#1220,#1457,#1481,.T.);
#1481 = SEAM_CURVE('',#1482,(#1486,#1493),.PCURVE_S1.);
#1482 = LINE('',#1483,#1484);
#1483 = CARTESIAN_POINT('',(51.80692,135.691161,0.97226));
#1484 = VECTOR('',#1485,1.);
#1485 = DIRECTION('',(0.,0.,1.));
#1486 = PCURVE('',#1442,#1487);
#1487 = DEFINITIONAL_REPRESENTATION('',(#1488),#1492);
#1488 = LINE('',#1489,#1490);
#1489 = CARTESIAN_POINT('',(6.28318530718,-0.));
#1490 = VECTOR('',#1491,1.);
#1491 = DIRECTION('',(0.,1.));
#1492 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1493 = PCURVE('',#1442,#1494);
#1494 = DEFINITIONAL_REPRESENTATION('',(#1495),#1499);
#1495 = LINE('',#1496,#1497);
#1496 = CARTESIAN_POINT('',(0.,-0.));
#1497 = VECTOR('',#1498,1.);
#1498 = DIRECTION('',(0.,1.));
#1499 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1500 = ORIENTED_EDGE('',*,*,#1427,.T.);
#1501 = ORIENTED_EDGE('',*,*,#1480,.T.);
#1502 = ADVANCED_FACE('',(#1503,#1567),#1266,.T.);
#1503 = FACE_BOUND('',#1504,.T.);
#1504 = EDGE_LOOP('',(#1505,#1506,#1507,#1526,#1527,#1548));
#1505 = ORIENTED_EDGE('',*,*,#1249,.T.);
#1506 = ORIENTED_EDGE('',*,*,#1404,.T.);
#1507 = ORIENTED_EDGE('',*,*,#1508,.F.);
#1508 = EDGE_CURVE('',#1343,#1367,#1509,.T.);
#1509 = SURFACE_CURVE('',#1510,(#1514,#1520),.PCURVE_S1.);
#1510 = LINE('',#1511,#1512);
#1511 = CARTESIAN_POINT('',(48.101543832237,133.5368369504,7.97226));
#1512 = VECTOR('',#1513,1.);
#1513 = DIRECTION('',(-0.11428330053,0.993448200572,0.));
#1514 = PCURVE('',#1266,#1515);
#1515 = DEFINITIONAL_REPRESENTATION('',(#1516),#1519);
#1516 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#1517,#1518),.UNSPECIFIED.,.F.,
  .F.,(2,2),(-1.74817464001,6.513450756865),.PIECEWISE_BEZIER_KNOTS.);
#1517 = CARTESIAN_POINT('',(-3.230589,-3.891045));
#1518 = CARTESIAN_POINT('',(-4.174754818094,4.316451884327));
#1519 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1520 = PCURVE('',#1392,#1521);
#1521 = DEFINITIONAL_REPRESENTATION('',(#1522),#1525);
#1522 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#1523,#1524),.UNSPECIFIED.,.F.,
  .F.,(2,2),(-1.74817464001,6.513450756865),.PIECEWISE_BEZIER_KNOTS.);
#1523 = CARTESIAN_POINT('',(0.,-20.02774));
#1524 = CARTESIAN_POINT('',(8.261625396876,-20.02774));
#1525 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1526 = ORIENTED_EDGE('',*,*,#1342,.T.);
#1527 = ORIENTED_EDGE('',*,*,#1528,.F.);
#1528 = EDGE_CURVE('',#1529,#1311,#1531,.T.);
#1529 = VERTEX_POINT('',#1530);
#1530 = CARTESIAN_POINT('',(50.449147,131.893892,7.97226));
#1531 = SURFACE_CURVE('',#1532,(#1536,#1542),.PCURVE_S1.);
#1532 = LINE('',#1533,#1534);
#1533 = CARTESIAN_POINT('',(51.072242161799,131.92109701751,7.97226));
#1534 = VECTOR('',#1535,1.);
#1535 = DIRECTION('',(-0.999048214928,-4.361953975718E-02,0.));
#1536 = PCURVE('',#1266,#1537);
#1537 = DEFINITIONAL_REPRESENTATION('',(#1538),#1541);
#1538 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#1539,#1540),.UNSPECIFIED.,.F.,
  .F.,(2,2),(0.623688779468,2.773550986223),.PIECEWISE_BEZIER_KNOTS.);
#1539 = CARTESIAN_POINT('',(-1.082773,-3.797269));
#1540 = CARTESIAN_POINT('',(-3.230589,-3.891045));
#1541 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1542 = PCURVE('',#1330,#1543);
#1543 = DEFINITIONAL_REPRESENTATION('',(#1544),#1547);
#1544 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#1545,#1546),.UNSPECIFIED.,.F.,
  .F.,(2,2),(0.623688779468,2.773550986223),.PIECEWISE_BEZIER_KNOTS.);
#1545 = CARTESIAN_POINT('',(0.,-20.02774));
#1546 = CARTESIAN_POINT('',(2.149862206755,-20.02774));
#1547 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1548 = ORIENTED_EDGE('',*,*,#1549,.F.);
#1549 = EDGE_CURVE('',#1250,#1529,#1550,.T.);
#1550 = SURFACE_CURVE('',#1551,(#1555,#1561),.PCURVE_S1.);
#1551 = LINE('',#1552,#1553);
#1552 = CARTESIAN_POINT('',(50.625672529155,128.7126483595,7.97226));
#1553 = VECTOR('',#1554,1.);
#1554 = DIRECTION('',(-5.540424023454E-02,0.998464005442,0.));
#1555 = PCURVE('',#1266,#1556);
#1556 = DEFINITIONAL_REPRESENTATION('',(#1557),#1560);
#1557 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#1558,#1559),.UNSPECIFIED.,.F.,
  .F.,(2,2),(2.666155957205,3.186137530399),.PIECEWISE_BEZIER_KNOTS.);
#1558 = CARTESIAN_POINT('',(-1.053963816001,-4.316451884327));
#1559 = CARTESIAN_POINT('',(-1.082773,-3.797269));
#1560 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1561 = PCURVE('',#1298,#1562);
#1562 = DEFINITIONAL_REPRESENTATION('',(#1563),#1566);
#1563 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#1564,#1565),.UNSPECIFIED.,.F.,
  .F.,(2,2),(2.666155957205,3.186137530399),.PIECEWISE_BEZIER_KNOTS.);
#1564 = CARTESIAN_POINT('',(9.583739687675,-20.02774));
#1565 = CARTESIAN_POINT('',(10.103721260868,-20.02774));
#1566 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1567 = FACE_BOUND('',#1568,.T.);
#1568 = EDGE_LOOP('',(#1569));
#1569 = ORIENTED_EDGE('',*,*,#1456,.F.);
#1570 = ADVANCED_FACE('',(#1571),#1392,.F.);
#1571 = FACE_BOUND('',#1572,.F.);
#1572 = EDGE_LOOP('',(#1573,#1574));
#1573 = ORIENTED_EDGE('',*,*,#1366,.F.);
#1574 = ORIENTED_EDGE('',*,*,#1508,.F.);
#1575 = ADVANCED_FACE('',(#1576),#1330,.F.);
#1576 = FACE_BOUND('',#1577,.F.);
#1577 = EDGE_LOOP('',(#1578,#1579,#1600));
#1578 = ORIENTED_EDGE('',*,*,#1528,.F.);
#1579 = ORIENTED_EDGE('',*,*,#1580,.T.);
#1580 = EDGE_CURVE('',#1529,#1279,#1581,.T.);
#1581 = SURFACE_CURVE('',#1582,(#1586,#1593),.PCURVE_S1.);
#1582 = LINE('',#1583,#1584);
#1583 = CARTESIAN_POINT('',(50.449147,131.893892,28.));
#1584 = VECTOR('',#1585,1.);
#1585 = DIRECTION('',(0.,0.,-1.));
#1586 = PCURVE('',#1330,#1587);
#1587 = DEFINITIONAL_REPRESENTATION('',(#1588),#1592);
#1588 = LINE('',#1589,#1590);
#1589 = CARTESIAN_POINT('',(0.,0.));
#1590 = VECTOR('',#1591,1.);
#1591 = DIRECTION('',(-0.,-1.));
#1592 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1593 = PCURVE('',#1298,#1594);
#1594 = DEFINITIONAL_REPRESENTATION('',(#1595),#1599);
#1595 = LINE('',#1596,#1597);
#1596 = CARTESIAN_POINT('',(10.103721260868,0.));
#1597 = VECTOR('',#1598,1.);
#1598 = DIRECTION('',(0.,-1.));
#1599 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1600 = ORIENTED_EDGE('',*,*,#1310,.F.);
#1601 = ADVANCED_FACE('',(#1602),#1298,.F.);
#1602 = FACE_BOUND('',#1603,.F.);
#1603 = EDGE_LOOP('',(#1604,#1605,#1606));
#1604 = ORIENTED_EDGE('',*,*,#1580,.F.);
#1605 = ORIENTED_EDGE('',*,*,#1549,.F.);
#1606 = ORIENTED_EDGE('',*,*,#1278,.F.);
#1607 = ( GEOMETRIC_REPRESENTATION_CONTEXT(3) 
GLOBAL_UNCERTAINTY_ASSIGNED_CONTEXT((#1611)) 
GLOBAL_UNIT_ASSIGNED_CONTEXT((#1608,#1609,#1610)) REPRESENTATION_CONTEXT
('Context #1','3D Context with UNIT and UNCERTAINTY') );
#1608 = ( LENGTH_UNIT() NAMED_UNIT(*) SI_UNIT(.MILLI.,.METRE.) );
#1609 = ( NAMED_UNIT(*) PLANE_ANGLE_UNIT() SI_UNIT($,.RADIAN.) );
#1610 = ( NAMED_UNIT(*) SI_UNIT($,.STERADIAN.) SOLID_ANGLE_UNIT() );
#1611 = UNCERTAINTY_MEASURE_WITH_UNIT(LENGTH_MEASURE(1.E-07),#1608,
  'distance_accuracy_value','confusion accuracy');
#1612 = CONTEXT_DEPENDENT_SHAPE_REPRESENTATION(#1613,#1615);
#1613 = ( REPRESENTATION_RELATIONSHIP('','',#1212,#10) 
REPRESENTATION_RELATIONSHIP_WITH_TRANSFORMATION(#1614) 
SHAPE_REPRESENTATION_RELATIONSHIP() );
#1614 = ITEM_DEFINED_TRANSFORMATION('','',#11,#31);
#1615 = PRODUCT_DEFINITION_SHAPE('Placement','Placement of an item',
  #1616);
#1616 = NEXT_ASSEMBLY_USAGE_OCCURRENCE('63','=>[0:1:1:6]','',#5,#1207,$
  );
#1617 = PRODUCT_RELATED_PRODUCT_CATEGORY('part',$,(#1209));
#1618 = SHAPE_DEFINITION_REPRESENTATION(#1619,#1625);
#1619 = PRODUCT_DEFINITION_SHAPE('','',#1620);
#1620 = PRODUCT_DEFINITION('design','',#1621,#1624);
#1621 = PRODUCT_DEFINITION_FORMATION('','',#1622);
#1622 = PRODUCT('PC1-P2_Part3','PC1-P2_Part3','',(#1623));
#1623 = PRODUCT_CONTEXT('',#2,'mechanical');
#1624 = PRODUCT_DEFINITION_CONTEXT('part definition',#2,'design');
#1625 = ADVANCED_BREP_SHAPE_REPRESENTATION('',(#11,#1626),#2106);
#1626 = MANIFOLD_SOLID_BREP('',#1627);
#1627 = CLOSED_SHELL('',(#1628,#1750,#1880,#1927,#2022,#2047,#2072,#2079
    ));
#1628 = ADVANCED_FACE('',(#1629),#1644,.T.);
#1629 = FACE_BOUND('',#1630,.T.);
#1630 = EDGE_LOOP('',(#1631,#1667,#1693,#1726));
#1631 = ORIENTED_EDGE('',*,*,#1632,.T.);
#1632 = EDGE_CURVE('',#1633,#1635,#1637,.T.);
#1633 = VERTEX_POINT('',#1634);
#1634 = CARTESIAN_POINT('',(50.470603233643,131.50721984468,7.97226));
#1635 = VERTEX_POINT('',#1636);
#1636 = CARTESIAN_POINT('',(47.63917157096,137.55617213613,7.97226));
#1637 = SURFACE_CURVE('',#1638,(#1643,#1655),.PCURVE_S1.);
#1638 = CIRCLE('',#1639,4.316451884327);
#1639 = AXIS2_PLACEMENT_3D('',#1640,#1641,#1642);
#1640 = CARTESIAN_POINT('',(51.53192,135.691161,7.97226));
#1641 = DIRECTION('',(0.,0.,1.));
#1642 = DIRECTION('',(-0.245877121951,-0.969301006345,0.));
#1643 = PCURVE('',#1644,#1649);
#1644 = CYLINDRICAL_SURFACE('',#1645,4.316451884327);
#1645 = AXIS2_PLACEMENT_3D('',#1646,#1647,#1648);
#1646 = CARTESIAN_POINT('',(51.53192,135.691161,7.97226));
#1647 = DIRECTION('',(0.,0.,1.));
#1648 = DIRECTION('',(1.,0.,0.));
#1649 = DEFINITIONAL_REPRESENTATION('',(#1650),#1654);
#1650 = LINE('',#1651,#1652);
#1651 = CARTESIAN_POINT('',(4.463964490043,0.));
#1652 = VECTOR('',#1653,1.);
#1653 = DIRECTION('',(1.,0.));
#1654 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1655 = PCURVE('',#1656,#1661);
#1656 = PLANE('',#1657);
#1657 = AXIS2_PLACEMENT_3D('',#1658,#1659,#1660);
#1658 = CARTESIAN_POINT('',(51.53192,135.691161,7.97226));
#1659 = DIRECTION('',(0.,0.,1.));
#1660 = DIRECTION('',(1.,0.,0.));
#1661 = DEFINITIONAL_REPRESENTATION('',(#1662),#1666);
#1662 = CIRCLE('',#1663,4.316451884327);
#1663 = AXIS2_PLACEMENT_2D('',#1664,#1665);
#1664 = CARTESIAN_POINT('',(0.,0.));
#1665 = DIRECTION('',(-0.245877121951,-0.969301006345));
#1666 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1667 = ORIENTED_EDGE('',*,*,#1668,.T.);
#1668 = EDGE_CURVE('',#1635,#1669,#1671,.T.);
#1669 = VERTEX_POINT('',#1670);
#1670 = CARTESIAN_POINT('',(47.63917157096,137.55617213613,18.));
#1671 = SURFACE_CURVE('',#1672,(#1676,#1682),.PCURVE_S1.);
#1672 = LINE('',#1673,#1674);
#1673 = CARTESIAN_POINT('',(47.63917157096,137.55617213613,7.97226));
#1674 = VECTOR('',#1675,1.);
#1675 = DIRECTION('',(0.,0.,1.));
#1676 = PCURVE('',#1644,#1677);
#1677 = DEFINITIONAL_REPRESENTATION('',(#1678),#1681);
#1678 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#1679,#1680),.UNSPECIFIED.,.F.,
  .F.,(2,2),(0.,10.02774),.PIECEWISE_BEZIER_KNOTS.);
#1679 = CARTESIAN_POINT('',(8.977990680483,0.));
#1680 = CARTESIAN_POINT('',(8.977990680483,10.02774));
#1681 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1682 = PCURVE('',#1683,#1688);
#1683 = PLANE('',#1684);
#1684 = AXIS2_PLACEMENT_3D('',#1685,#1686,#1687);
#1685 = CARTESIAN_POINT('',(48.301331,131.800116,28.));
#1686 = DIRECTION('',(0.993448200572,0.11428330053,0.));
#1687 = DIRECTION('',(-0.11428330053,0.993448200572,0.));
#1688 = DEFINITIONAL_REPRESENTATION('',(#1689),#1692);
#1689 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#1690,#1691),.UNSPECIFIED.,.F.,
  .F.,(2,2),(-5.999999999062E-07,10.0277406),.PIECEWISE_BEZIER_KNOTS.);
#1690 = CARTESIAN_POINT('',(5.794017375867,-20.0277406));
#1691 = CARTESIAN_POINT('',(5.794017375867,-9.9999994));
#1692 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1693 = ORIENTED_EDGE('',*,*,#1694,.T.);
#1694 = EDGE_CURVE('',#1669,#1695,#1697,.T.);
#1695 = VERTEX_POINT('',#1696);
#1696 = CARTESIAN_POINT('',(50.470603233643,131.50721984468,18.));
#1697 = SURFACE_CURVE('',#1698,(#1703,#1710),.PCURVE_S1.);
#1698 = CIRCLE('',#1699,4.316451884327);
#1699 = AXIS2_PLACEMENT_3D('',#1700,#1701,#1702);
#1700 = CARTESIAN_POINT('',(51.53192,135.691161,18.));
#1701 = DIRECTION('',(0.,0.,-1.));
#1702 = DIRECTION('',(-0.901839875286,0.43207041017,0.));
#1703 = PCURVE('',#1644,#1704);
#1704 = DEFINITIONAL_REPRESENTATION('',(#1705),#1709);
#1705 = LINE('',#1706,#1707);
#1706 = CARTESIAN_POINT('',(8.977990680483,10.02774));
#1707 = VECTOR('',#1708,1.);
#1708 = DIRECTION('',(-1.,-0.));
#1709 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1710 = PCURVE('',#1711,#1716);
#1711 = PLANE('',#1712);
#1712 = AXIS2_PLACEMENT_3D('',#1713,#1714,#1715);
#1713 = CARTESIAN_POINT('',(51.53192,135.691161,18.));
#1714 = DIRECTION('',(0.,0.,1.));
#1715 = DIRECTION('',(1.,0.,0.));
#1716 = DEFINITIONAL_REPRESENTATION('',(#1717),#1725);
#1717 = ( BOUNDED_CURVE() B_SPLINE_CURVE(2,(#1718,#1719,#1720,#1721,
#1722,#1723,#1724),.UNSPECIFIED.,.T.,.F.) B_SPLINE_CURVE_WITH_KNOTS((1,2
    ,2,2,2,1),(-2.094395102393,0.,2.094395102393,4.188790204786,
6.28318530718,8.377580409573),.UNSPECIFIED.) CURVE() 
GEOMETRIC_REPRESENTATION_ITEM() RATIONAL_B_SPLINE_CURVE((1.,0.5,1.,0.5,
1.,0.5,1.)) REPRESENTATION_ITEM('') );
#1718 = CARTESIAN_POINT('',(-3.89274842904,1.865011136139));
#1719 = CARTESIAN_POINT('',(-0.662454384566,8.60744919632));
#1720 = CARTESIAN_POINT('',(3.561521236757,2.438713462021));
#1721 = CARTESIAN_POINT('',(7.78549685808,-3.730022272277));
#1722 = CARTESIAN_POINT('',(0.331227192283,-4.30372459816));
#1723 = CARTESIAN_POINT('',(-7.123042473514,-4.877426924042));
#1724 = CARTESIAN_POINT('',(-3.89274842904,1.865011136139));
#1725 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1726 = ORIENTED_EDGE('',*,*,#1727,.F.);
#1727 = EDGE_CURVE('',#1633,#1695,#1728,.T.);
#1728 = SURFACE_CURVE('',#1729,(#1733,#1739),.PCURVE_S1.);
#1729 = LINE('',#1730,#1731);
#1730 = CARTESIAN_POINT('',(50.470603233643,131.50721984468,7.97226));
#1731 = VECTOR('',#1732,1.);
#1732 = DIRECTION('',(0.,0.,1.));
#1733 = PCURVE('',#1644,#1734);
#1734 = DEFINITIONAL_REPRESENTATION('',(#1735),#1738);
#1735 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#1736,#1737),.UNSPECIFIED.,.F.,
  .F.,(2,2),(0.,10.02774),.PIECEWISE_BEZIER_KNOTS.);
#1736 = CARTESIAN_POINT('',(4.463964490043,0.));
#1737 = CARTESIAN_POINT('',(4.463964490043,10.02774));
#1738 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1739 = PCURVE('',#1740,#1745);
#1740 = PLANE('',#1741);
#1741 = AXIS2_PLACEMENT_3D('',#1742,#1743,#1744);
#1742 = CARTESIAN_POINT('',(51.008936,121.80569,28.));
#1743 = DIRECTION('',(0.998464005442,5.540424023454E-02,0.));
#1744 = DIRECTION('',(-5.540424023454E-02,0.998464005442,0.));
#1745 = DEFINITIONAL_REPRESENTATION('',(#1746),#1749);
#1746 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#1747,#1748),.UNSPECIFIED.,.F.,
  .F.,(2,2),(-5.999999999062E-07,10.0277406),.PIECEWISE_BEZIER_KNOTS.);
#1747 = CARTESIAN_POINT('',(9.716454265553,-20.0277406));
#1748 = CARTESIAN_POINT('',(9.716454265553,-9.9999994));
#1749 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1750 = ADVANCED_FACE('',(#1751,#1849),#1656,.F.);
#1751 = FACE_BOUND('',#1752,.F.);
#1752 = EDGE_LOOP('',(#1753,#1754,#1775,#1804,#1830));
#1753 = ORIENTED_EDGE('',*,*,#1632,.T.);
#1754 = ORIENTED_EDGE('',*,*,#1755,.F.);
#1755 = EDGE_CURVE('',#1756,#1635,#1758,.T.);
#1756 = VERTEX_POINT('',#1757);
#1757 = CARTESIAN_POINT('',(48.16434175799,132.99094366547,7.97226));
#1758 = SURFACE_CURVE('',#1759,(#1763,#1769),.PCURVE_S1.);
#1759 = LINE('',#1760,#1761);
#1760 = CARTESIAN_POINT('',(48.101543832237,133.5368369504,7.97226));
#1761 = VECTOR('',#1762,1.);
#1762 = DIRECTION('',(-0.11428330053,0.993448200572,0.));
#1763 = PCURVE('',#1656,#1764);
#1764 = DEFINITIONAL_REPRESENTATION('',(#1765),#1768);
#1765 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#1766,#1767),.UNSPECIFIED.,.F.,
  .F.,(2,2),(-1.74817464001,6.513450756865),.PIECEWISE_BEZIER_KNOTS.);
#1766 = CARTESIAN_POINT('',(-3.230589,-3.891045));
#1767 = CARTESIAN_POINT('',(-4.174754818094,4.316451884327));
#1768 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1769 = PCURVE('',#1683,#1770);
#1770 = DEFINITIONAL_REPRESENTATION('',(#1771),#1774);
#1771 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#1772,#1773),.UNSPECIFIED.,.F.,
  .F.,(2,2),(-1.74817464001,6.513450756865),.PIECEWISE_BEZIER_KNOTS.);
#1772 = CARTESIAN_POINT('',(0.,-20.02774));
#1773 = CARTESIAN_POINT('',(8.261625396876,-20.02774));
#1774 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1775 = ORIENTED_EDGE('',*,*,#1776,.T.);
#1776 = EDGE_CURVE('',#1756,#1777,#1779,.T.);
#1777 = VERTEX_POINT('',#1778);
#1778 = CARTESIAN_POINT('',(49.553535824782,131.85478863473,7.97226));
#1779 = SURFACE_CURVE('',#1780,(#1785,#1792),.PCURVE_S1.);
#1780 = CIRCLE('',#1781,4.316451884327);
#1781 = AXIS2_PLACEMENT_3D('',#1782,#1783,#1784);
#1782 = CARTESIAN_POINT('',(51.53192,135.691161,7.97226));
#1783 = DIRECTION('',(0.,0.,1.));
#1784 = DIRECTION('',(1.,0.,0.));
#1785 = PCURVE('',#1656,#1786);
#1786 = DEFINITIONAL_REPRESENTATION('',(#1787),#1791);
#1787 = CIRCLE('',#1788,4.316451884327);
#1788 = AXIS2_PLACEMENT_2D('',#1789,#1790);
#1789 = CARTESIAN_POINT('',(0.,0.));
#1790 = DIRECTION('',(1.,0.));
#1791 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1792 = PCURVE('',#1793,#1798);
#1793 = CYLINDRICAL_SURFACE('',#1794,4.316451884327);
#1794 = AXIS2_PLACEMENT_3D('',#1795,#1796,#1797);
#1795 = CARTESIAN_POINT('',(51.53192,135.691161,7.97226));
#1796 = DIRECTION('',(0.,0.,1.));
#1797 = DIRECTION('',(1.,0.,0.));
#1798 = DEFINITIONAL_REPRESENTATION('',(#1799),#1803);
#1799 = LINE('',#1800,#1801);
#1800 = CARTESIAN_POINT('',(0.,0.));
#1801 = VECTOR('',#1802,1.);
#1802 = DIRECTION('',(1.,0.));
#1803 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1804 = ORIENTED_EDGE('',*,*,#1805,.F.);
#1805 = EDGE_CURVE('',#1806,#1777,#1808,.T.);
#1806 = VERTEX_POINT('',#1807);
#1807 = CARTESIAN_POINT('',(50.449147,131.893892,7.97226));
#1808 = SURFACE_CURVE('',#1809,(#1813,#1819),.PCURVE_S1.);
#1809 = LINE('',#1810,#1811);
#1810 = CARTESIAN_POINT('',(51.072242161799,131.92109701751,7.97226));
#1811 = VECTOR('',#1812,1.);
#1812 = DIRECTION('',(-0.999048214928,-4.361953975718E-02,0.));
#1813 = PCURVE('',#1656,#1814);
#1814 = DEFINITIONAL_REPRESENTATION('',(#1815),#1818);
#1815 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#1816,#1817),.UNSPECIFIED.,.F.,
  .F.,(2,2),(0.623688779468,2.773550986223),.PIECEWISE_BEZIER_KNOTS.);
#1816 = CARTESIAN_POINT('',(-1.082773,-3.797269));
#1817 = CARTESIAN_POINT('',(-3.230589,-3.891045));
#1818 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1819 = PCURVE('',#1820,#1825);
#1820 = PLANE('',#1821);
#1821 = AXIS2_PLACEMENT_3D('',#1822,#1823,#1824);
#1822 = CARTESIAN_POINT('',(50.449147,131.893892,28.));
#1823 = DIRECTION('',(-4.361953975718E-02,0.999048214928,0.));
#1824 = DIRECTION('',(-0.999048214928,-4.361953975718E-02,0.));
#1825 = DEFINITIONAL_REPRESENTATION('',(#1826),#1829);
#1826 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#1827,#1828),.UNSPECIFIED.,.F.,
  .F.,(2,2),(0.623688779468,2.773550986223),.PIECEWISE_BEZIER_KNOTS.);
#1827 = CARTESIAN_POINT('',(0.,-20.02774));
#1828 = CARTESIAN_POINT('',(2.149862206755,-20.02774));
#1829 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1830 = ORIENTED_EDGE('',*,*,#1831,.F.);
#1831 = EDGE_CURVE('',#1633,#1806,#1832,.T.);
#1832 = SURFACE_CURVE('',#1833,(#1837,#1843),.PCURVE_S1.);
#1833 = LINE('',#1834,#1835);
#1834 = CARTESIAN_POINT('',(50.625672529155,128.7126483595,7.97226));
#1835 = VECTOR('',#1836,1.);
#1836 = DIRECTION('',(-5.540424023454E-02,0.998464005442,0.));
#1837 = PCURVE('',#1656,#1838);
#1838 = DEFINITIONAL_REPRESENTATION('',(#1839),#1842);
#1839 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#1840,#1841),.UNSPECIFIED.,.F.,
  .F.,(2,2),(2.666155957205,3.186137530399),.PIECEWISE_BEZIER_KNOTS.);
#1840 = CARTESIAN_POINT('',(-1.053963816001,-4.316451884327));
#1841 = CARTESIAN_POINT('',(-1.082773,-3.797269));
#1842 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1843 = PCURVE('',#1740,#1844);
#1844 = DEFINITIONAL_REPRESENTATION('',(#1845),#1848);
#1845 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#1846,#1847),.UNSPECIFIED.,.F.,
  .F.,(2,2),(2.666155957205,3.186137530399),.PIECEWISE_BEZIER_KNOTS.);
#1846 = CARTESIAN_POINT('',(9.583739687675,-20.02774));
#1847 = CARTESIAN_POINT('',(10.103721260868,-20.02774));
#1848 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1849 = FACE_BOUND('',#1850,.F.);
#1850 = EDGE_LOOP('',(#1851));
#1851 = ORIENTED_EDGE('',*,*,#1852,.F.);
#1852 = EDGE_CURVE('',#1853,#1853,#1855,.T.);
#1853 = VERTEX_POINT('',#1854);
#1854 = CARTESIAN_POINT('',(51.80692,135.691161,7.97226));
#1855 = SURFACE_CURVE('',#1856,(#1861,#1868),.PCURVE_S1.);
#1856 = CIRCLE('',#1857,0.275);
#1857 = AXIS2_PLACEMENT_3D('',#1858,#1859,#1860);
#1858 = CARTESIAN_POINT('',(51.53192,135.691161,7.97226));
#1859 = DIRECTION('',(0.,0.,1.));
#1860 = DIRECTION('',(1.,0.,0.));
#1861 = PCURVE('',#1656,#1862);
#1862 = DEFINITIONAL_REPRESENTATION('',(#1863),#1867);
#1863 = CIRCLE('',#1864,0.275);
#1864 = AXIS2_PLACEMENT_2D('',#1865,#1866);
#1865 = CARTESIAN_POINT('',(0.,0.));
#1866 = DIRECTION('',(1.,0.));
#1867 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1868 = PCURVE('',#1869,#1874);
#1869 = CYLINDRICAL_SURFACE('',#1870,0.275);
#1870 = AXIS2_PLACEMENT_3D('',#1871,#1872,#1873);
#1871 = CARTESIAN_POINT('',(51.53192,135.691161,7.97226));
#1872 = DIRECTION('',(0.,0.,1.));
#1873 = DIRECTION('',(1.,0.,0.));
#1874 = DEFINITIONAL_REPRESENTATION('',(#1875),#1879);
#1875 = LINE('',#1876,#1877);
#1876 = CARTESIAN_POINT('',(0.,0.));
#1877 = VECTOR('',#1878,1.);
#1878 = DIRECTION('',(1.,0.));
#1879 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1880 = ADVANCED_FACE('',(#1881),#1740,.F.);
#1881 = FACE_BOUND('',#1882,.F.);
#1882 = EDGE_LOOP('',(#1883,#1906,#1925,#1926));
#1883 = ORIENTED_EDGE('',*,*,#1884,.F.);
#1884 = EDGE_CURVE('',#1885,#1806,#1887,.T.);
#1885 = VERTEX_POINT('',#1886);
#1886 = CARTESIAN_POINT('',(50.449147,131.893892,18.));
#1887 = SURFACE_CURVE('',#1888,(#1892,#1899),.PCURVE_S1.);
#1888 = LINE('',#1889,#1890);
#1889 = CARTESIAN_POINT('',(50.449147,131.893892,28.));
#1890 = VECTOR('',#1891,1.);
#1891 = DIRECTION('',(0.,0.,-1.));
#1892 = PCURVE('',#1740,#1893);
#1893 = DEFINITIONAL_REPRESENTATION('',(#1894),#1898);
#1894 = LINE('',#1895,#1896);
#1895 = CARTESIAN_POINT('',(10.103721260868,0.));
#1896 = VECTOR('',#1897,1.);
#1897 = DIRECTION('',(0.,-1.));
#1898 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1899 = PCURVE('',#1820,#1900);
#1900 = DEFINITIONAL_REPRESENTATION('',(#1901),#1905);
#1901 = LINE('',#1902,#1903);
#1902 = CARTESIAN_POINT('',(0.,0.));
#1903 = VECTOR('',#1904,1.);
#1904 = DIRECTION('',(-0.,-1.));
#1905 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1906 = ORIENTED_EDGE('',*,*,#1907,.F.);
#1907 = EDGE_CURVE('',#1695,#1885,#1908,.T.);
#1908 = SURFACE_CURVE('',#1909,(#1913,#1919),.PCURVE_S1.);
#1909 = LINE('',#1910,#1911);
#1910 = CARTESIAN_POINT('',(50.625672529155,128.7126483595,18.));
#1911 = VECTOR('',#1912,1.);
#1912 = DIRECTION('',(-5.540424023454E-02,0.998464005442,0.));
#1913 = PCURVE('',#1740,#1914);
#1914 = DEFINITIONAL_REPRESENTATION('',(#1915),#1918);
#1915 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#1916,#1917),.UNSPECIFIED.,.F.,
  .F.,(2,2),(2.666155957205,3.186137530399),.PIECEWISE_BEZIER_KNOTS.);
#1916 = CARTESIAN_POINT('',(9.583739687675,-10.));
#1917 = CARTESIAN_POINT('',(10.103721260868,-10.));
#1918 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1919 = PCURVE('',#1711,#1920);
#1920 = DEFINITIONAL_REPRESENTATION('',(#1921),#1924);
#1921 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#1922,#1923),.UNSPECIFIED.,.F.,
  .F.,(2,2),(2.666155957205,3.186137530399),.PIECEWISE_BEZIER_KNOTS.);
#1922 = CARTESIAN_POINT('',(-1.053963816001,-4.316451884327));
#1923 = CARTESIAN_POINT('',(-1.082773,-3.797269));
#1924 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1925 = ORIENTED_EDGE('',*,*,#1727,.F.);
#1926 = ORIENTED_EDGE('',*,*,#1831,.T.);
#1927 = ADVANCED_FACE('',(#1928,#1996),#1711,.T.);
#1928 = FACE_BOUND('',#1929,.T.);
#1929 = EDGE_LOOP('',(#1930,#1931,#1932,#1953,#1977));
#1930 = ORIENTED_EDGE('',*,*,#1907,.F.);
#1931 = ORIENTED_EDGE('',*,*,#1694,.F.);
#1932 = ORIENTED_EDGE('',*,*,#1933,.F.);
#1933 = EDGE_CURVE('',#1934,#1669,#1936,.T.);
#1934 = VERTEX_POINT('',#1935);
#1935 = CARTESIAN_POINT('',(48.16434175799,132.99094366547,18.));
#1936 = SURFACE_CURVE('',#1937,(#1941,#1947),.PCURVE_S1.);
#1937 = LINE('',#1938,#1939);
#1938 = CARTESIAN_POINT('',(48.101543832237,133.5368369504,18.));
#1939 = VECTOR('',#1940,1.);
#1940 = DIRECTION('',(-0.11428330053,0.993448200572,0.));
#1941 = PCURVE('',#1711,#1942);
#1942 = DEFINITIONAL_REPRESENTATION('',(#1943),#1946);
#1943 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#1944,#1945),.UNSPECIFIED.,.F.,
  .F.,(2,2),(-1.74817464001,6.513450756865),.PIECEWISE_BEZIER_KNOTS.);
#1944 = CARTESIAN_POINT('',(-3.230589,-3.891045));
#1945 = CARTESIAN_POINT('',(-4.174754818094,4.316451884327));
#1946 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1947 = PCURVE('',#1683,#1948);
#1948 = DEFINITIONAL_REPRESENTATION('',(#1949),#1952);
#1949 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#1950,#1951),.UNSPECIFIED.,.F.,
  .F.,(2,2),(-1.74817464001,6.513450756865),.PIECEWISE_BEZIER_KNOTS.);
#1950 = CARTESIAN_POINT('',(0.,-10.));
#1951 = CARTESIAN_POINT('',(8.261625396876,-10.));
#1952 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1953 = ORIENTED_EDGE('',*,*,#1954,.T.);
#1954 = EDGE_CURVE('',#1934,#1955,#1957,.T.);
#1955 = VERTEX_POINT('',#1956);
#1956 = CARTESIAN_POINT('',(49.553535824782,131.85478863473,18.));
#1957 = SURFACE_CURVE('',#1958,(#1963,#1970),.PCURVE_S1.);
#1958 = CIRCLE('',#1959,4.316451884327);
#1959 = AXIS2_PLACEMENT_3D('',#1960,#1961,#1962);
#1960 = CARTESIAN_POINT('',(51.53192,135.691161,18.));
#1961 = DIRECTION('',(0.,0.,1.));
#1962 = DIRECTION('',(1.,0.,0.));
#1963 = PCURVE('',#1711,#1964);
#1964 = DEFINITIONAL_REPRESENTATION('',(#1965),#1969);
#1965 = CIRCLE('',#1966,4.316451884327);
#1966 = AXIS2_PLACEMENT_2D('',#1967,#1968);
#1967 = CARTESIAN_POINT('',(0.,0.));
#1968 = DIRECTION('',(1.,0.));
#1969 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1970 = PCURVE('',#1793,#1971);
#1971 = DEFINITIONAL_REPRESENTATION('',(#1972),#1976);
#1972 = LINE('',#1973,#1974);
#1973 = CARTESIAN_POINT('',(0.,10.02774));
#1974 = VECTOR('',#1975,1.);
#1975 = DIRECTION('',(1.,0.));
#1976 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1977 = ORIENTED_EDGE('',*,*,#1978,.F.);
#1978 = EDGE_CURVE('',#1885,#1955,#1979,.T.);
#1979 = SURFACE_CURVE('',#1980,(#1984,#1990),.PCURVE_S1.);
#1980 = LINE('',#1981,#1982);
#1981 = CARTESIAN_POINT('',(51.072242161799,131.92109701751,18.));
#1982 = VECTOR('',#1983,1.);
#1983 = DIRECTION('',(-0.999048214928,-4.361953975718E-02,0.));
#1984 = PCURVE('',#1711,#1985);
#1985 = DEFINITIONAL_REPRESENTATION('',(#1986),#1989);
#1986 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#1987,#1988),.UNSPECIFIED.,.F.,
  .F.,(2,2),(0.623688779468,2.773550986223),.PIECEWISE_BEZIER_KNOTS.);
#1987 = CARTESIAN_POINT('',(-1.082773,-3.797269));
#1988 = CARTESIAN_POINT('',(-3.230589,-3.891045));
#1989 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1990 = PCURVE('',#1820,#1991);
#1991 = DEFINITIONAL_REPRESENTATION('',(#1992),#1995);
#1992 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#1993,#1994),.UNSPECIFIED.,.F.,
  .F.,(2,2),(0.623688779468,2.773550986223),.PIECEWISE_BEZIER_KNOTS.);
#1993 = CARTESIAN_POINT('',(0.,-10.));
#1994 = CARTESIAN_POINT('',(2.149862206755,-10.));
#1995 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1996 = FACE_BOUND('',#1997,.T.);
#1997 = EDGE_LOOP('',(#1998));
#1998 = ORIENTED_EDGE('',*,*,#1999,.F.);
#1999 = EDGE_CURVE('',#2000,#2000,#2002,.T.);
#2000 = VERTEX_POINT('',#2001);
#2001 = CARTESIAN_POINT('',(51.80692,135.691161,18.));
#2002 = SURFACE_CURVE('',#2003,(#2008,#2015),.PCURVE_S1.);
#2003 = CIRCLE('',#2004,0.275);
#2004 = AXIS2_PLACEMENT_3D('',#2005,#2006,#2007);
#2005 = CARTESIAN_POINT('',(51.53192,135.691161,18.));
#2006 = DIRECTION('',(0.,0.,1.));
#2007 = DIRECTION('',(1.,0.,0.));
#2008 = PCURVE('',#1711,#2009);
#2009 = DEFINITIONAL_REPRESENTATION('',(#2010),#2014);
#2010 = CIRCLE('',#2011,0.275);
#2011 = AXIS2_PLACEMENT_2D('',#2012,#2013);
#2012 = CARTESIAN_POINT('',(0.,0.));
#2013 = DIRECTION('',(1.,0.));
#2014 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2015 = PCURVE('',#1869,#2016);
#2016 = DEFINITIONAL_REPRESENTATION('',(#2017),#2021);
#2017 = LINE('',#2018,#2019);
#2018 = CARTESIAN_POINT('',(0.,10.02774));
#2019 = VECTOR('',#2020,1.);
#2020 = DIRECTION('',(1.,0.));
#2021 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2022 = ADVANCED_FACE('',(#2023),#1683,.F.);
#2023 = FACE_BOUND('',#2024,.F.);
#2024 = EDGE_LOOP('',(#2025,#2044,#2045,#2046));
#2025 = ORIENTED_EDGE('',*,*,#2026,.F.);
#2026 = EDGE_CURVE('',#1756,#1934,#2027,.T.);
#2027 = SURFACE_CURVE('',#2028,(#2032,#2038),.PCURVE_S1.);
#2028 = LINE('',#2029,#2030);
#2029 = CARTESIAN_POINT('',(48.16434175799,132.99094366547,7.97226));
#2030 = VECTOR('',#2031,1.);
#2031 = DIRECTION('',(0.,0.,1.));
#2032 = PCURVE('',#1683,#2033);
#2033 = DEFINITIONAL_REPRESENTATION('',(#2034),#2037);
#2034 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#2035,#2036),.UNSPECIFIED.,.F.,
  .F.,(2,2),(-5.999999999062E-07,10.0277406),.PIECEWISE_BEZIER_KNOTS.);
#2035 = CARTESIAN_POINT('',(1.198681184175,-20.0277406));
#2036 = CARTESIAN_POINT('',(1.198681184175,-9.9999994));
#2037 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2038 = PCURVE('',#1793,#2039);
#2039 = DEFINITIONAL_REPRESENTATION('',(#2040),#2043);
#2040 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#2041,#2042),.UNSPECIFIED.,.F.,
  .F.,(2,2),(-5.999999999062E-07,10.0277406),.PIECEWISE_BEZIER_KNOTS.);
#2041 = CARTESIAN_POINT('',(3.81744702099,-5.999999999062E-07));
#2042 = CARTESIAN_POINT('',(3.81744702099,10.0277406));
#2043 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2044 = ORIENTED_EDGE('',*,*,#1755,.T.);
#2045 = ORIENTED_EDGE('',*,*,#1668,.T.);
#2046 = ORIENTED_EDGE('',*,*,#1933,.F.);
#2047 = ADVANCED_FACE('',(#2048),#1820,.F.);
#2048 = FACE_BOUND('',#2049,.F.);
#2049 = EDGE_LOOP('',(#2050,#2051,#2052,#2053));
#2050 = ORIENTED_EDGE('',*,*,#1978,.F.);
#2051 = ORIENTED_EDGE('',*,*,#1884,.T.);
#2052 = ORIENTED_EDGE('',*,*,#1805,.T.);
#2053 = ORIENTED_EDGE('',*,*,#2054,.T.);
#2054 = EDGE_CURVE('',#1777,#1955,#2055,.T.);
#2055 = SURFACE_CURVE('',#2056,(#2060,#2066),.PCURVE_S1.);
#2056 = LINE('',#2057,#2058);
#2057 = CARTESIAN_POINT('',(49.553535824782,131.85478863473,7.97226));
#2058 = VECTOR('',#2059,1.);
#2059 = DIRECTION('',(0.,0.,1.));
#2060 = PCURVE('',#1820,#2061);
#2061 = DEFINITIONAL_REPRESENTATION('',(#2062),#2065);
#2062 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#2063,#2064),.UNSPECIFIED.,.F.,
  .F.,(2,2),(-5.999999999062E-07,10.0277406),.PIECEWISE_BEZIER_KNOTS.);
#2063 = CARTESIAN_POINT('',(0.896464416667,-20.0277406));
#2064 = CARTESIAN_POINT('',(0.896464416667,-9.9999994));
#2065 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2066 = PCURVE('',#1793,#2067);
#2067 = DEFINITIONAL_REPRESENTATION('',(#2068),#2071);
#2068 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#2069,#2070),.UNSPECIFIED.,.F.,
  .F.,(2,2),(-5.999999999062E-07,10.0277406),.PIECEWISE_BEZIER_KNOTS.);
#2069 = CARTESIAN_POINT('',(4.236267209909,-5.999999999062E-07));
#2070 = CARTESIAN_POINT('',(4.236267209909,10.0277406));
#2071 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2072 = ADVANCED_FACE('',(#2073),#1793,.T.);
#2073 = FACE_BOUND('',#2074,.T.);
#2074 = EDGE_LOOP('',(#2075,#2076,#2077,#2078));
#2075 = ORIENTED_EDGE('',*,*,#1954,.F.);
#2076 = ORIENTED_EDGE('',*,*,#2026,.F.);
#2077 = ORIENTED_EDGE('',*,*,#1776,.T.);
#2078 = ORIENTED_EDGE('',*,*,#2054,.T.);
#2079 = ADVANCED_FACE('',(#2080),#1869,.F.);
#2080 = FACE_BOUND('',#2081,.F.);
#2081 = EDGE_LOOP('',(#2082,#2083,#2104,#2105));
#2082 = ORIENTED_EDGE('',*,*,#1999,.F.);
#2083 = ORIENTED_EDGE('',*,*,#2084,.F.);
#2084 = EDGE_CURVE('',#1853,#2000,#2085,.T.);
#2085 = SEAM_CURVE('',#2086,(#2090,#2097),.PCURVE_S1.);
#2086 = LINE('',#2087,#2088);
#2087 = CARTESIAN_POINT('',(51.80692,135.691161,7.97226));
#2088 = VECTOR('',#2089,1.);
#2089 = DIRECTION('',(0.,0.,1.));
#2090 = PCURVE('',#1869,#2091);
#2091 = DEFINITIONAL_REPRESENTATION('',(#2092),#2096);
#2092 = LINE('',#2093,#2094);
#2093 = CARTESIAN_POINT('',(6.28318530718,-0.));
#2094 = VECTOR('',#2095,1.);
#2095 = DIRECTION('',(0.,1.));
#2096 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2097 = PCURVE('',#1869,#2098);
#2098 = DEFINITIONAL_REPRESENTATION('',(#2099),#2103);
#2099 = LINE('',#2100,#2101);
#2100 = CARTESIAN_POINT('',(0.,-0.));
#2101 = VECTOR('',#2102,1.);
#2102 = DIRECTION('',(0.,1.));
#2103 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2104 = ORIENTED_EDGE('',*,*,#1852,.T.);
#2105 = ORIENTED_EDGE('',*,*,#2084,.T.);
#2106 = ( GEOMETRIC_REPRESENTATION_CONTEXT(3) 
GLOBAL_UNCERTAINTY_ASSIGNED_CONTEXT((#2110)) 
GLOBAL_UNIT_ASSIGNED_CONTEXT((#2107,#2108,#2109)) REPRESENTATION_CONTEXT
('Context #1','3D Context with UNIT and UNCERTAINTY') );
#2107 = ( LENGTH_UNIT() NAMED_UNIT(*) SI_UNIT(.MILLI.,.METRE.) );
#2108 = ( NAMED_UNIT(*) PLANE_ANGLE_UNIT() SI_UNIT($,.RADIAN.) );
#2109 = ( NAMED_UNIT(*) SI_UNIT($,.STERADIAN.) SOLID_ANGLE_UNIT() );
#2110 = UNCERTAINTY_MEASURE_WITH_UNIT(LENGTH_MEASURE(1.E-07),#2107,
  'distance_accuracy_value','confusion accuracy');
#2111 = CONTEXT_DEPENDENT_SHAPE_REPRESENTATION(#2112,#2114);
#2112 = ( REPRESENTATION_RELATIONSHIP('','',#1625,#10) 
REPRESENTATION_RELATIONSHIP_WITH_TRANSFORMATION(#2113) 
SHAPE_REPRESENTATION_RELATIONSHIP() );
#2113 = ITEM_DEFINED_TRANSFORMATION('','',#11,#35);
#2114 = PRODUCT_DEFINITION_SHAPE('Placement','Placement of an item',
  #2115);
#2115 = NEXT_ASSEMBLY_USAGE_OCCURRENCE('64','=>[0:1:1:7]','',#5,#1620,$
  );
#2116 = PRODUCT_RELATED_PRODUCT_CATEGORY('part',$,(#1622));
#2117 = SHAPE_DEFINITION_REPRESENTATION(#2118,#2124);
#2118 = PRODUCT_DEFINITION_SHAPE('','',#2119);
#2119 = PRODUCT_DEFINITION('design','',#2120,#2123);
#2120 = PRODUCT_DEFINITION_FORMATION('','',#2121);
#2121 = PRODUCT('PC1-P3_Part1','PC1-P3_Part1','',(#2122));
#2122 = PRODUCT_CONTEXT('',#2,'mechanical');
#2123 = PRODUCT_DEFINITION_CONTEXT('part definition',#2,'design');
#2124 = ADVANCED_BREP_SHAPE_REPRESENTATION('',(#11,#2125),#2223);
#2125 = MANIFOLD_SOLID_BREP('',#2126);
#2126 = CLOSED_SHELL('',(#2127,#2215,#2219));
#2127 = ADVANCED_FACE('',(#2128),#2141,.T.);
#2128 = FACE_BOUND('',#2129,.T.);
#2129 = EDGE_LOOP('',(#2130,#2164,#2187,#2214));
#2130 = ORIENTED_EDGE('',*,*,#2131,.F.);
#2131 = EDGE_CURVE('',#2132,#2132,#2134,.T.);
#2132 = VERTEX_POINT('',#2133);
#2133 = CARTESIAN_POINT('',(0.275,-6.73555739531E-17,15.575404));
#2134 = SURFACE_CURVE('',#2135,(#2140,#2152),.PCURVE_S1.);
#2135 = CIRCLE('',#2136,0.275);
#2136 = AXIS2_PLACEMENT_3D('',#2137,#2138,#2139);
#2137 = CARTESIAN_POINT('',(0.,0.,15.575404));
#2138 = DIRECTION('',(0.,0.,1.));
#2139 = DIRECTION('',(1.,0.,-0.));
#2140 = PCURVE('',#2141,#2146);
#2141 = CYLINDRICAL_SURFACE('',#2142,0.275);
#2142 = AXIS2_PLACEMENT_3D('',#2143,#2144,#2145);
#2143 = CARTESIAN_POINT('',(0.,0.,0.));
#2144 = DIRECTION('',(0.,0.,1.));
#2145 = DIRECTION('',(1.,0.,-0.));
#2146 = DEFINITIONAL_REPRESENTATION('',(#2147),#2151);
#2147 = LINE('',#2148,#2149);
#2148 = CARTESIAN_POINT('',(0.,15.575404));
#2149 = VECTOR('',#2150,1.);
#2150 = DIRECTION('',(1.,0.));
#2151 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2152 = PCURVE('',#2153,#2158);
#2153 = PLANE('',#2154);
#2154 = AXIS2_PLACEMENT_3D('',#2155,#2156,#2157);
#2155 = CARTESIAN_POINT('',(0.,0.,15.575404));
#2156 = DIRECTION('',(0.,0.,1.));
#2157 = DIRECTION('',(1.,0.,-0.));
#2158 = DEFINITIONAL_REPRESENTATION('',(#2159),#2163);
#2159 = CIRCLE('',#2160,0.275);
#2160 = AXIS2_PLACEMENT_2D('',#2161,#2162);
#2161 = CARTESIAN_POINT('',(0.,0.));
#2162 = DIRECTION('',(1.,0.));
#2163 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2164 = ORIENTED_EDGE('',*,*,#2165,.F.);
#2165 = EDGE_CURVE('',#2166,#2132,#2168,.T.);
#2166 = VERTEX_POINT('',#2167);
#2167 = CARTESIAN_POINT('',(0.275,-6.73555739531E-17,0.));
#2168 = SEAM_CURVE('',#2169,(#2173,#2180),.PCURVE_S1.);
#2169 = LINE('',#2170,#2171);
#2170 = CARTESIAN_POINT('',(0.275,-6.73555739531E-17,0.));
#2171 = VECTOR('',#2172,1.);
#2172 = DIRECTION('',(0.,0.,1.));
#2173 = PCURVE('',#2141,#2174);
#2174 = DEFINITIONAL_REPRESENTATION('',(#2175),#2179);
#2175 = LINE('',#2176,#2177);
#2176 = CARTESIAN_POINT('',(6.28318530718,-0.));
#2177 = VECTOR('',#2178,1.);
#2178 = DIRECTION('',(0.,1.));
#2179 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2180 = PCURVE('',#2141,#2181);
#2181 = DEFINITIONAL_REPRESENTATION('',(#2182),#2186);
#2182 = LINE('',#2183,#2184);
#2183 = CARTESIAN_POINT('',(0.,-0.));
#2184 = VECTOR('',#2185,1.);
#2185 = DIRECTION('',(0.,1.));
#2186 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2187 = ORIENTED_EDGE('',*,*,#2188,.T.);
#2188 = EDGE_CURVE('',#2166,#2166,#2189,.T.);
#2189 = SURFACE_CURVE('',#2190,(#2195,#2202),.PCURVE_S1.);
#2190 = CIRCLE('',#2191,0.275);
#2191 = AXIS2_PLACEMENT_3D('',#2192,#2193,#2194);
#2192 = CARTESIAN_POINT('',(0.,0.,0.));
#2193 = DIRECTION('',(0.,0.,1.));
#2194 = DIRECTION('',(1.,0.,-0.));
#2195 = PCURVE('',#2141,#2196);
#2196 = DEFINITIONAL_REPRESENTATION('',(#2197),#2201);
#2197 = LINE('',#2198,#2199);
#2198 = CARTESIAN_POINT('',(0.,0.));
#2199 = VECTOR('',#2200,1.);
#2200 = DIRECTION('',(1.,0.));
#2201 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2202 = PCURVE('',#2203,#2208);
#2203 = PLANE('',#2204);
#2204 = AXIS2_PLACEMENT_3D('',#2205,#2206,#2207);
#2205 = CARTESIAN_POINT('',(0.,0.,0.));
#2206 = DIRECTION('',(0.,0.,1.));
#2207 = DIRECTION('',(1.,0.,-0.));
#2208 = DEFINITIONAL_REPRESENTATION('',(#2209),#2213);
#2209 = CIRCLE('',#2210,0.275);
#2210 = AXIS2_PLACEMENT_2D('',#2211,#2212);
#2211 = CARTESIAN_POINT('',(0.,0.));
#2212 = DIRECTION('',(1.,0.));
#2213 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2214 = ORIENTED_EDGE('',*,*,#2165,.T.);
#2215 = ADVANCED_FACE('',(#2216),#2153,.T.);
#2216 = FACE_BOUND('',#2217,.T.);
#2217 = EDGE_LOOP('',(#2218));
#2218 = ORIENTED_EDGE('',*,*,#2131,.T.);
#2219 = ADVANCED_FACE('',(#2220),#2203,.F.);
#2220 = FACE_BOUND('',#2221,.T.);
#2221 = EDGE_LOOP('',(#2222));
#2222 = ORIENTED_EDGE('',*,*,#2188,.F.);
#2223 = ( GEOMETRIC_REPRESENTATION_CONTEXT(3) 
GLOBAL_UNCERTAINTY_ASSIGNED_CONTEXT((#2227)) 
GLOBAL_UNIT_ASSIGNED_CONTEXT((#2224,#2225,#2226)) REPRESENTATION_CONTEXT
('Context #1','3D Context with UNIT and UNCERTAINTY') );
#2224 = ( LENGTH_UNIT() NAMED_UNIT(*) SI_UNIT(.MILLI.,.METRE.) );
#2225 = ( NAMED_UNIT(*) PLANE_ANGLE_UNIT() SI_UNIT($,.RADIAN.) );
#2226 = ( NAMED_UNIT(*) SI_UNIT($,.STERADIAN.) SOLID_ANGLE_UNIT() );
#2227 = UNCERTAINTY_MEASURE_WITH_UNIT(LENGTH_MEASURE(1.E-07),#2224,
  'distance_accuracy_value','confusion accuracy');
#2228 = CONTEXT_DEPENDENT_SHAPE_REPRESENTATION(#2229,#2231);
#2229 = ( REPRESENTATION_RELATIONSHIP('','',#2124,#10) 
REPRESENTATION_RELATIONSHIP_WITH_TRANSFORMATION(#2230) 
SHAPE_REPRESENTATION_RELATIONSHIP() );
#2230 = ITEM_DEFINED_TRANSFORMATION('','',#11,#39);
#2231 = PRODUCT_DEFINITION_SHAPE('Placement','Placement of an item',
  #2232);
#2232 = NEXT_ASSEMBLY_USAGE_OCCURRENCE('65','=>[0:1:1:8]','',#5,#2119,$
  );
#2233 = PRODUCT_RELATED_PRODUCT_CATEGORY('part',$,(#2121));
#2234 = SHAPE_DEFINITION_REPRESENTATION(#2235,#2241);
#2235 = PRODUCT_DEFINITION_SHAPE('','',#2236);
#2236 = PRODUCT_DEFINITION('design','',#2237,#2240);
#2237 = PRODUCT_DEFINITION_FORMATION('','',#2238);
#2238 = PRODUCT('PC1-P3_Part2','PC1-P3_Part2','',(#2239));
#2239 = PRODUCT_CONTEXT('',#2,'mechanical');
#2240 = PRODUCT_DEFINITION_CONTEXT('part definition',#2,'design');
#2241 = ADVANCED_BREP_SHAPE_REPRESENTATION('',(#11,#2242),#2658);
#2242 = MANIFOLD_SOLID_BREP('',#2243);
#2243 = CLOSED_SHELL('',(#2244,#2480,#2530,#2599,#2625,#2652));
#2244 = ADVANCED_FACE('',(#2245),#2259,.T.);
#2245 = FACE_BOUND('',#2246,.T.);
#2246 = EDGE_LOOP('',(#2247,#2277,#2306,#2362,#2397,#2431,#2453,#2454));
#2247 = ORIENTED_EDGE('',*,*,#2248,.T.);
#2248 = EDGE_CURVE('',#2249,#2251,#2253,.T.);
#2249 = VERTEX_POINT('',#2250);
#2250 = CARTESIAN_POINT('',(51.889918,130.739863,2.424596));
#2251 = VERTEX_POINT('',#2252);
#2252 = CARTESIAN_POINT('',(55.931369884327,130.739863,9.424596));
#2253 = SEAM_CURVE('',#2254,(#2258,#2270),.PCURVE_S1.);
#2254 = LINE('',#2255,#2256);
#2255 = CARTESIAN_POINT('',(51.889918,130.739863,2.424596));
#2256 = VECTOR('',#2257,1.);
#2257 = DIRECTION('',(0.5,-1.224646799147E-16,0.866025403784));
#2258 = PCURVE('',#2259,#2264);
#2259 = CONICAL_SURFACE('',#2260,0.275,0.523598775598);
#2260 = AXIS2_PLACEMENT_3D('',#2261,#2262,#2263);
#2261 = CARTESIAN_POINT('',(51.614918,130.739863,2.424596));
#2262 = DIRECTION('',(0.,0.,1.));
#2263 = DIRECTION('',(1.,0.,0.));
#2264 = DEFINITIONAL_REPRESENTATION('',(#2265),#2269);
#2265 = LINE('',#2266,#2267);
#2266 = CARTESIAN_POINT('',(6.28318530718,-0.));
#2267 = VECTOR('',#2268,1.);
#2268 = DIRECTION('',(0.,1.));
#2269 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2270 = PCURVE('',#2259,#2271);
#2271 = DEFINITIONAL_REPRESENTATION('',(#2272),#2276);
#2272 = LINE('',#2273,#2274);
#2273 = CARTESIAN_POINT('',(0.,-0.));
#2274 = VECTOR('',#2275,1.);
#2275 = DIRECTION('',(0.,1.));
#2276 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2277 = ORIENTED_EDGE('',*,*,#2278,.F.);
#2278 = EDGE_CURVE('',#2279,#2251,#2281,.T.);
#2279 = VERTEX_POINT('',#2280);
#2280 = CARTESIAN_POINT('',(50.747818612774,126.51140045169,9.424596));
#2281 = SURFACE_CURVE('',#2282,(#2287,#2294),.PCURVE_S1.);
#2282 = CIRCLE('',#2283,4.316451884327);
#2283 = AXIS2_PLACEMENT_3D('',#2284,#2285,#2286);
#2284 = CARTESIAN_POINT('',(51.614918,130.739863,9.424596));
#2285 = DIRECTION('',(0.,0.,1.));
#2286 = DIRECTION('',(1.,0.,0.));
#2287 = PCURVE('',#2259,#2288);
#2288 = DEFINITIONAL_REPRESENTATION('',(#2289),#2293);
#2289 = LINE('',#2290,#2291);
#2290 = CARTESIAN_POINT('',(0.,7.));
#2291 = VECTOR('',#2292,1.);
#2292 = DIRECTION('',(1.,0.));
#2293 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2294 = PCURVE('',#2295,#2300);
#2295 = PLANE('',#2296);
#2296 = AXIS2_PLACEMENT_3D('',#2297,#2298,#2299);
#2297 = CARTESIAN_POINT('',(51.614918,130.739863,9.424596));
#2298 = DIRECTION('',(0.,0.,1.));
#2299 = DIRECTION('',(1.,0.,0.));
#2300 = DEFINITIONAL_REPRESENTATION('',(#2301),#2305);
#2301 = CIRCLE('',#2302,4.316451884327);
#2302 = AXIS2_PLACEMENT_2D('',#2303,#2304);
#2303 = CARTESIAN_POINT('',(0.,0.));
#2304 = DIRECTION('',(1.,0.));
#2305 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2306 = ORIENTED_EDGE('',*,*,#2307,.F.);
#2307 = EDGE_CURVE('',#2308,#2279,#2310,.T.);
#2308 = VERTEX_POINT('',#2309);
#2309 = CARTESIAN_POINT('',(50.449147,131.893892,4.78948157597));
#2310 = SURFACE_CURVE('',#2311,(#2316,#2350),.PCURVE_S1.);
#2311 = HYPERBOLA('',#2312,1.905329123132,1.100042282135);
#2312 = AXIS2_PLACEMENT_3D('',#2313,#2314,#2315);
#2313 = CARTESIAN_POINT('',(50.516565376824,130.67891599313,
    1.948282027919));
#2314 = DIRECTION('',(0.998464005442,5.540424023454E-02,0.));
#2315 = DIRECTION('',(-0.,0.,1.));
#2316 = PCURVE('',#2259,#2317);
#2317 = DEFINITIONAL_REPRESENTATION('',(#2318),#2349);
#2318 = B_SPLINE_CURVE_WITH_KNOTS('',8,(#2319,#2320,#2321,#2322,#2323,
    #2324,#2325,#2326,#2327,#2328,#2329,#2330,#2331,#2332,#2333,#2334,
    #2335,#2336,#2337,#2338,#2339,#2340,#2341,#2342,#2343,#2344,#2345,
    #2346,#2347,#2348),.UNSPECIFIED.,.F.,.F.,(9,7,7,7,9),(
    -1.454611933972,-0.580062707922,7.584921161589E-02,1.059717090922,
    2.043584970229),.UNSPECIFIED.);
#2319 = CARTESIAN_POINT('',(2.084991776078,3.826216497337));
#2320 = CARTESIAN_POINT('',(2.133402360321,3.404503028328));
#2321 = CARTESIAN_POINT('',(2.187235206409,3.041552595386));
#2322 = CARTESIAN_POINT('',(2.247029876181,2.729685837248));
#2323 = CARTESIAN_POINT('',(2.313287249843,2.462720711108));
#2324 = CARTESIAN_POINT('',(2.38644216475,2.235681299671));
#2325 = CARTESIAN_POINT('',(2.466692426268,2.04459569829));
#2326 = CARTESIAN_POINT('',(2.553802149193,1.886360591951));
#2327 = CARTESIAN_POINT('',(2.716893817381,1.662867286903));
#2328 = CARTESIAN_POINT('',(2.790213904787,1.584254569486));
#2329 = CARTESIAN_POINT('',(2.866631520057,1.521830856214));
#2330 = CARTESIAN_POINT('',(2.945661026305,1.4748612029));
#2331 = CARTESIAN_POINT('',(3.026628064863,1.442835895372));
#2332 = CARTESIAN_POINT('',(3.108714718804,1.42545793544));
#2333 = CARTESIAN_POINT('',(3.1910481577,1.422638330653));
#2334 = CARTESIAN_POINT('',(3.395432428765,1.4522888951));
#2335 = CARTESIAN_POINT('',(3.516757523913,1.50310885786));
#2336 = CARTESIAN_POINT('',(3.633988858155,1.587368458872));
#2337 = CARTESIAN_POINT('',(3.744517844225,1.706543510819));
#2338 = CARTESIAN_POINT('',(3.846693796466,1.863195282619));
#2339 = CARTESIAN_POINT('',(3.940014657523,2.061076772616));
#2340 = CARTESIAN_POINT('',(4.024517613553,2.30532670751));
#2341 = CARTESIAN_POINT('',(4.176719793761,2.900275543187));
#2342 = CARTESIAN_POINT('',(4.244418554647,3.250974330946));
#2343 = CARTESIAN_POINT('',(4.304124693534,3.661754304637));
#2344 = CARTESIAN_POINT('',(4.356604778668,4.141186672423));
#2345 = CARTESIAN_POINT('',(4.402695914404,4.699897531347));
#2346 = CARTESIAN_POINT('',(4.443186234077,5.351026108887));
#2347 = CARTESIAN_POINT('',(4.478788155006,6.110896668707));
#2348 = CARTESIAN_POINT('',(4.510130339984,7.));
#2349 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2350 = PCURVE('',#2351,#2356);
#2351 = PLANE('',#2352);
#2352 = AXIS2_PLACEMENT_3D('',#2353,#2354,#2355);
#2353 = CARTESIAN_POINT('',(51.008936,121.80569,28.));
#2354 = DIRECTION('',(0.998464005442,5.540424023454E-02,0.));
#2355 = DIRECTION('',(-5.540424023454E-02,0.998464005442,0.));
#2356 = DEFINITIONAL_REPRESENTATION('',(#2357),#2361);
#2357 = ( BOUNDED_CURVE() B_SPLINE_CURVE(2,(#2358,#2359,#2360),
.UNSPECIFIED.,.F.,.F.) B_SPLINE_CURVE_WITH_KNOTS((3,3),(-1.454611933972,
2.043584970229),.PIECEWISE_BEZIER_KNOTS.) CURVE() 
GEOMETRIC_REPRESENTATION_ITEM() RATIONAL_B_SPLINE_CURVE((1.,
2.961673821806,1.)) REPRESENTATION_ITEM('') );
#2358 = CARTESIAN_POINT('',(11.114093386955,-21.74918750266));
#2359 = CARTESIAN_POINT('',(8.775908459009,-25.38029184428));
#2360 = CARTESIAN_POINT('',(4.712949516507,-18.575404));
#2361 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2362 = ORIENTED_EDGE('',*,*,#2363,.F.);
#2363 = EDGE_CURVE('',#2364,#2308,#2366,.T.);
#2364 = VERTEX_POINT('',#2365);
#2365 = CARTESIAN_POINT('',(48.301331,131.800116,7.97422475016));
#2366 = SURFACE_CURVE('',#2367,(#2372,#2385),.PCURVE_S1.);
#2367 = HYPERBOLA('',#2368,2.085009865151,1.203781006908);
#2368 = AXIS2_PLACEMENT_3D('',#2369,#2370,#2371);
#2369 = CARTESIAN_POINT('',(51.56240962651,131.94249826611,
    1.948282027919));
#2370 = DIRECTION('',(-4.361953975718E-02,0.999048214928,0.));
#2371 = DIRECTION('',(0.,0.,1.));
#2372 = PCURVE('',#2259,#2373);
#2373 = DEFINITIONAL_REPRESENTATION('',(#2374),#2384);
#2374 = B_SPLINE_CURVE_WITH_KNOTS('',8,(#2375,#2376,#2377,#2378,#2379,
    #2380,#2381,#2382,#2383),.UNSPECIFIED.,.F.,.F.,(9,9),(
    -1.783141685478,-0.690820382625),.PIECEWISE_BEZIER_KNOTS.);
#2375 = CARTESIAN_POINT('',(2.852122306177,5.900297373404));
#2376 = CARTESIAN_POINT('',(2.80747671448,5.07749241588));
#2377 = CARTESIAN_POINT('',(2.756246999356,4.390550927783));
#2378 = CARTESIAN_POINT('',(2.697438802036,3.816098158881));
#2379 = CARTESIAN_POINT('',(2.629949610366,3.336162529567));
#2380 = CARTESIAN_POINT('',(2.552690686191,2.936782895451));
#2381 = CARTESIAN_POINT('',(2.46462154647,2.607146418428));
#2382 = CARTESIAN_POINT('',(2.36545262595,2.339005117766));
#2383 = CARTESIAN_POINT('',(2.256068082192,2.126316863728));
#2384 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2385 = PCURVE('',#2386,#2391);
#2386 = PLANE('',#2387);
#2387 = AXIS2_PLACEMENT_3D('',#2388,#2389,#2390);
#2388 = CARTESIAN_POINT('',(50.449147,131.893892,28.));
#2389 = DIRECTION('',(-4.361953975718E-02,0.999048214928,0.));
#2390 = DIRECTION('',(-0.999048214928,-4.361953975718E-02,0.));
#2391 = DEFINITIONAL_REPRESENTATION('',(#2392),#2396);
#2392 = ( BOUNDED_CURVE() B_SPLINE_CURVE(2,(#2393,#2394,#2395),
.UNSPECIFIED.,.F.,.F.) B_SPLINE_CURVE_WITH_KNOTS((3,3),(-1.783141685478,
-0.690820382625),.PIECEWISE_BEZIER_KNOTS.) CURVE() 
GEOMETRIC_REPRESENTATION_ITEM() RATIONAL_B_SPLINE_CURVE((1.,
1.152890196588,1.)) REPRESENTATION_ITEM('') );
#2393 = CARTESIAN_POINT('',(2.36484842743,-19.67510662659));
#2394 = CARTESIAN_POINT('',(0.532778130801,-22.67391901695));
#2395 = CARTESIAN_POINT('',(-0.214986220675,-23.44908713627));
#2396 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2397 = ORIENTED_EDGE('',*,*,#2398,.F.);
#2398 = EDGE_CURVE('',#2399,#2364,#2401,.T.);
#2399 = VERTEX_POINT('',#2400);
#2400 = CARTESIAN_POINT('',(48.130261339781,133.28719981128,9.424596));
#2401 = SURFACE_CURVE('',#2402,(#2407,#2419),.PCURVE_S1.);
#2402 = HYPERBOLA('',#2403,5.491827058128,3.170707830353);
#2403 = AXIS2_PLACEMENT_3D('',#2404,#2405,#2406);
#2404 = CARTESIAN_POINT('',(48.464984011396,130.37750404413,
    1.948282027919));
#2405 = DIRECTION('',(0.993448200572,0.11428330053,0.));
#2406 = DIRECTION('',(-0.,0.,1.));
#2407 = PCURVE('',#2259,#2408);
#2408 = DEFINITIONAL_REPRESENTATION('',(#2409),#2418);
#2409 = B_SPLINE_CURVE_WITH_KNOTS('',7,(#2410,#2411,#2412,#2413,#2414,
    #2415,#2416,#2417),.UNSPECIFIED.,.F.,.F.,(8,8),(-0.826403252807,
    -7.74616775129E-02),.PIECEWISE_BEZIER_KNOTS.);
#2410 = CARTESIAN_POINT('',(2.510352974945,7.));
#2411 = CARTESIAN_POINT('',(2.588945134196,6.457233690983));
#2412 = CARTESIAN_POINT('',(2.674194350869,6.014314151979));
#2413 = CARTESIAN_POINT('',(2.765980640173,5.661093407713));
#2414 = CARTESIAN_POINT('',(2.863782837416,5.39022281414));
#2415 = CARTESIAN_POINT('',(2.966428910373,5.196682472771));
#2416 = CARTESIAN_POINT('',(3.072070406774,5.077558099702));
#2417 = CARTESIAN_POINT('',(3.17874186933,5.03199766277));
#2418 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2419 = PCURVE('',#2420,#2425);
#2420 = PLANE('',#2421);
#2421 = AXIS2_PLACEMENT_3D('',#2422,#2423,#2424);
#2422 = CARTESIAN_POINT('',(48.301331,131.800116,28.));
#2423 = DIRECTION('',(0.993448200572,0.11428330053,0.));
#2424 = DIRECTION('',(-0.11428330053,0.993448200572,0.));
#2425 = DEFINITIONAL_REPRESENTATION('',(#2426),#2430);
#2426 = ( BOUNDED_CURVE() B_SPLINE_CURVE(2,(#2427,#2428,#2429),
.UNSPECIFIED.,.F.,.F.) B_SPLINE_CURVE_WITH_KNOTS((3,3),(-0.826403252807,
-7.74616775129E-02),.PIECEWISE_BEZIER_KNOTS.) CURVE() 
GEOMETRIC_REPRESENTATION_ITEM() RATIONAL_B_SPLINE_CURVE((1.,
1.070937357963,1.)) REPRESENTATION_ITEM('') );
#2427 = CARTESIAN_POINT('',(1.496891141807,-18.575404));
#2428 = CARTESIAN_POINT('',(-4.794998083026E-02,-20.39100285228));
#2429 = CARTESIAN_POINT('',(-1.186140051712,-20.54340633723));
#2430 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2431 = ORIENTED_EDGE('',*,*,#2432,.F.);
#2432 = EDGE_CURVE('',#2251,#2399,#2433,.T.);
#2433 = SURFACE_CURVE('',#2434,(#2439,#2446),.PCURVE_S1.);
#2434 = CIRCLE('',#2435,4.316451884327);
#2435 = AXIS2_PLACEMENT_3D('',#2436,#2437,#2438);
#2436 = CARTESIAN_POINT('',(51.614918,130.739863,9.424596));
#2437 = DIRECTION('',(0.,0.,1.));
#2438 = DIRECTION('',(1.,0.,0.));
#2439 = PCURVE('',#2259,#2440);
#2440 = DEFINITIONAL_REPRESENTATION('',(#2441),#2445);
#2441 = LINE('',#2442,#2443);
#2442 = CARTESIAN_POINT('',(0.,7.));
#2443 = VECTOR('',#2444,1.);
#2444 = DIRECTION('',(1.,0.));
#2445 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2446 = PCURVE('',#2295,#2447);
#2447 = DEFINITIONAL_REPRESENTATION('',(#2448),#2452);
#2448 = CIRCLE('',#2449,4.316451884327);
#2449 = AXIS2_PLACEMENT_2D('',#2450,#2451);
#2450 = CARTESIAN_POINT('',(0.,0.));
#2451 = DIRECTION('',(1.,0.));
#2452 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2453 = ORIENTED_EDGE('',*,*,#2248,.F.);
#2454 = ORIENTED_EDGE('',*,*,#2455,.T.);
#2455 = EDGE_CURVE('',#2249,#2249,#2456,.T.);
#2456 = SURFACE_CURVE('',#2457,(#2462,#2469),.PCURVE_S1.);
#2457 = CIRCLE('',#2458,0.275);
#2458 = AXIS2_PLACEMENT_3D('',#2459,#2460,#2461);
#2459 = CARTESIAN_POINT('',(51.614918,130.739863,2.424596));
#2460 = DIRECTION('',(0.,0.,1.));
#2461 = DIRECTION('',(1.,0.,0.));
#2462 = PCURVE('',#2259,#2463);
#2463 = DEFINITIONAL_REPRESENTATION('',(#2464),#2468);
#2464 = LINE('',#2465,#2466);
#2465 = CARTESIAN_POINT('',(0.,0.));
#2466 = VECTOR('',#2467,1.);
#2467 = DIRECTION('',(1.,0.));
#2468 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2469 = PCURVE('',#2470,#2475);
#2470 = CYLINDRICAL_SURFACE('',#2471,0.275);
#2471 = AXIS2_PLACEMENT_3D('',#2472,#2473,#2474);
#2472 = CARTESIAN_POINT('',(51.614918,130.739863,2.424596));
#2473 = DIRECTION('',(0.,0.,1.));
#2474 = DIRECTION('',(1.,0.,0.));
#2475 = DEFINITIONAL_REPRESENTATION('',(#2476),#2479);
#2476 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#2477,#2478),.UNSPECIFIED.,.F.,
  .F.,(2,2),(0.,6.28318530718),.PIECEWISE_BEZIER_KNOTS.);
#2477 = CARTESIAN_POINT('',(0.,0.));
#2478 = CARTESIAN_POINT('',(6.28318530718,0.));
#2479 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2480 = ADVANCED_FACE('',(#2481),#2470,.F.);
#2481 = FACE_BOUND('',#2482,.F.);
#2482 = EDGE_LOOP('',(#2483,#2507,#2528,#2529));
#2483 = ORIENTED_EDGE('',*,*,#2484,.F.);
#2484 = EDGE_CURVE('',#2485,#2485,#2487,.T.);
#2485 = VERTEX_POINT('',#2486);
#2486 = CARTESIAN_POINT('',(51.889918,130.739863,9.424596));
#2487 = SURFACE_CURVE('',#2488,(#2493,#2500),.PCURVE_S1.);
#2488 = CIRCLE('',#2489,0.275);
#2489 = AXIS2_PLACEMENT_3D('',#2490,#2491,#2492);
#2490 = CARTESIAN_POINT('',(51.614918,130.739863,9.424596));
#2491 = DIRECTION('',(0.,0.,1.));
#2492 = DIRECTION('',(1.,0.,0.));
#2493 = PCURVE('',#2470,#2494);
#2494 = DEFINITIONAL_REPRESENTATION('',(#2495),#2499);
#2495 = LINE('',#2496,#2497);
#2496 = CARTESIAN_POINT('',(0.,7.));
#2497 = VECTOR('',#2498,1.);
#2498 = DIRECTION('',(1.,0.));
#2499 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2500 = PCURVE('',#2295,#2501);
#2501 = DEFINITIONAL_REPRESENTATION('',(#2502),#2506);
#2502 = CIRCLE('',#2503,0.275);
#2503 = AXIS2_PLACEMENT_2D('',#2504,#2505);
#2504 = CARTESIAN_POINT('',(0.,0.));
#2505 = DIRECTION('',(1.,0.));
#2506 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2507 = ORIENTED_EDGE('',*,*,#2508,.F.);
#2508 = EDGE_CURVE('',#2249,#2485,#2509,.T.);
#2509 = SEAM_CURVE('',#2510,(#2514,#2521),.PCURVE_S1.);
#2510 = LINE('',#2511,#2512);
#2511 = CARTESIAN_POINT('',(51.889918,130.739863,2.424596));
#2512 = VECTOR('',#2513,1.);
#2513 = DIRECTION('',(0.,0.,1.));
#2514 = PCURVE('',#2470,#2515);
#2515 = DEFINITIONAL_REPRESENTATION('',(#2516),#2520);
#2516 = LINE('',#2517,#2518);
#2517 = CARTESIAN_POINT('',(6.28318530718,-0.));
#2518 = VECTOR('',#2519,1.);
#2519 = DIRECTION('',(0.,1.));
#2520 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2521 = PCURVE('',#2470,#2522);
#2522 = DEFINITIONAL_REPRESENTATION('',(#2523),#2527);
#2523 = LINE('',#2524,#2525);
#2524 = CARTESIAN_POINT('',(0.,-0.));
#2525 = VECTOR('',#2526,1.);
#2526 = DIRECTION('',(0.,1.));
#2527 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2528 = ORIENTED_EDGE('',*,*,#2455,.T.);
#2529 = ORIENTED_EDGE('',*,*,#2508,.T.);
#2530 = ADVANCED_FACE('',(#2531,#2596),#2295,.T.);
#2531 = FACE_BOUND('',#2532,.T.);
#2532 = EDGE_LOOP('',(#2533,#2534,#2535,#2556,#2577));
#2533 = ORIENTED_EDGE('',*,*,#2278,.T.);
#2534 = ORIENTED_EDGE('',*,*,#2432,.T.);
#2535 = ORIENTED_EDGE('',*,*,#2536,.F.);
#2536 = EDGE_CURVE('',#2537,#2399,#2539,.T.);
#2537 = VERTEX_POINT('',#2538);
#2538 = CARTESIAN_POINT('',(48.301331,131.800116,9.424596));
#2539 = SURFACE_CURVE('',#2540,(#2544,#2550),.PCURVE_S1.);
#2540 = LINE('',#2541,#2542);
#2541 = CARTESIAN_POINT('',(48.383157505698,131.08881002206,9.424596));
#2542 = VECTOR('',#2543,1.);
#2543 = DIRECTION('',(-0.11428330053,0.993448200572,0.));
#2544 = PCURVE('',#2295,#2545);
#2545 = DEFINITIONAL_REPRESENTATION('',(#2546),#2549);
#2546 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#2547,#2548),.UNSPECIFIED.,.F.,
  .F.,(2,2),(0.715997046977,3.993670590954),.PIECEWISE_BEZIER_KNOTS.);
#2547 = CARTESIAN_POINT('',(-3.313587,1.060253));
#2548 = CARTESIAN_POINT('',(-3.688170350664,4.316451884327));
#2549 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2550 = PCURVE('',#2420,#2551);
#2551 = DEFINITIONAL_REPRESENTATION('',(#2552),#2555);
#2552 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#2553,#2554),.UNSPECIFIED.,.F.,
  .F.,(2,2),(0.715997046977,3.993670590954),.PIECEWISE_BEZIER_KNOTS.);
#2553 = CARTESIAN_POINT('',(0.,-18.575404));
#2554 = CARTESIAN_POINT('',(3.277673543977,-18.575404));
#2555 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2556 = ORIENTED_EDGE('',*,*,#2557,.F.);
#2557 = EDGE_CURVE('',#2558,#2537,#2560,.T.);
#2558 = VERTEX_POINT('',#2559);
#2559 = CARTESIAN_POINT('',(50.449147,131.893892,9.424596));
#2560 = SURFACE_CURVE('',#2561,(#2565,#2571),.PCURVE_S1.);
#2561 = LINE('',#2562,#2563);
#2562 = CARTESIAN_POINT('',(51.005778313255,131.91819513305,9.424596));
#2563 = VECTOR('',#2564,1.);
#2564 = DIRECTION('',(-0.999048214928,-4.361953975718E-02,0.));
#2565 = PCURVE('',#2295,#2566);
#2566 = DEFINITIONAL_REPRESENTATION('',(#2567),#2570);
#2567 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#2568,#2569),.UNSPECIFIED.,.F.,
  .F.,(2,2),(0.557161611359,2.707023818114),.PIECEWISE_BEZIER_KNOTS.);
#2568 = CARTESIAN_POINT('',(-1.165771,1.154029));
#2569 = CARTESIAN_POINT('',(-3.313587,1.060253));
#2570 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2571 = PCURVE('',#2386,#2572);
#2572 = DEFINITIONAL_REPRESENTATION('',(#2573),#2576);
#2573 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#2574,#2575),.UNSPECIFIED.,.F.,
  .F.,(2,2),(0.557161611359,2.707023818114),.PIECEWISE_BEZIER_KNOTS.);
#2574 = CARTESIAN_POINT('',(0.,-18.575404));
#2575 = CARTESIAN_POINT('',(2.149862206755,-18.575404));
#2576 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2577 = ORIENTED_EDGE('',*,*,#2578,.F.);
#2578 = EDGE_CURVE('',#2279,#2558,#2579,.T.);
#2579 = SURFACE_CURVE('',#2580,(#2584,#2590),.PCURVE_S1.);
#2580 = LINE('',#2581,#2582);
#2581 = CARTESIAN_POINT('',(50.762750688412,126.24230299656,9.424596));
#2582 = VECTOR('',#2583,1.);
#2583 = DIRECTION('',(-5.540424023454E-02,0.998464005442,0.));
#2584 = PCURVE('',#2295,#2585);
#2585 = DEFINITIONAL_REPRESENTATION('',(#2586),#2589);
#2586 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#2587,#2588),.UNSPECIFIED.,.F.,
  .F.,(2,2),(0.181386728134,5.660283167574),.PIECEWISE_BEZIER_KNOTS.);
#2587 = CARTESIAN_POINT('',(-0.862216905449,-4.316451884327));
#2588 = CARTESIAN_POINT('',(-1.165771,1.154029));
#2589 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2590 = PCURVE('',#2351,#2591);
#2591 = DEFINITIONAL_REPRESENTATION('',(#2592),#2595);
#2592 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#2593,#2594),.UNSPECIFIED.,.F.,
  .F.,(2,2),(0.181386728134,5.660283167574),.PIECEWISE_BEZIER_KNOTS.);
#2593 = CARTESIAN_POINT('',(4.624824821428,-18.575404));
#2594 = CARTESIAN_POINT('',(10.103721260868,-18.575404));
#2595 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2596 = FACE_BOUND('',#2597,.T.);
#2597 = EDGE_LOOP('',(#2598));
#2598 = ORIENTED_EDGE('',*,*,#2484,.F.);
#2599 = ADVANCED_FACE('',(#2600),#2420,.F.);
#2600 = FACE_BOUND('',#2601,.F.);
#2601 = EDGE_LOOP('',(#2602,#2603,#2624));
#2602 = ORIENTED_EDGE('',*,*,#2536,.F.);
#2603 = ORIENTED_EDGE('',*,*,#2604,.T.);
#2604 = EDGE_CURVE('',#2537,#2364,#2605,.T.);
#2605 = SURFACE_CURVE('',#2606,(#2610,#2617),.PCURVE_S1.);
#2606 = LINE('',#2607,#2608);
#2607 = CARTESIAN_POINT('',(48.301331,131.800116,28.));
#2608 = VECTOR('',#2609,1.);
#2609 = DIRECTION('',(0.,0.,-1.));
#2610 = PCURVE('',#2420,#2611);
#2611 = DEFINITIONAL_REPRESENTATION('',(#2612),#2616);
#2612 = LINE('',#2613,#2614);
#2613 = CARTESIAN_POINT('',(0.,0.));
#2614 = VECTOR('',#2615,1.);
#2615 = DIRECTION('',(0.,-1.));
#2616 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2617 = PCURVE('',#2386,#2618);
#2618 = DEFINITIONAL_REPRESENTATION('',(#2619),#2623);
#2619 = LINE('',#2620,#2621);
#2620 = CARTESIAN_POINT('',(2.149862206755,0.));
#2621 = VECTOR('',#2622,1.);
#2622 = DIRECTION('',(-0.,-1.));
#2623 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2624 = ORIENTED_EDGE('',*,*,#2398,.F.);
#2625 = ADVANCED_FACE('',(#2626),#2386,.F.);
#2626 = FACE_BOUND('',#2627,.F.);
#2627 = EDGE_LOOP('',(#2628,#2629,#2650,#2651));
#2628 = ORIENTED_EDGE('',*,*,#2557,.F.);
#2629 = ORIENTED_EDGE('',*,*,#2630,.T.);
#2630 = EDGE_CURVE('',#2558,#2308,#2631,.T.);
#2631 = SURFACE_CURVE('',#2632,(#2636,#2643),.PCURVE_S1.);
#2632 = LINE('',#2633,#2634);
#2633 = CARTESIAN_POINT('',(50.449147,131.893892,28.));
#2634 = VECTOR('',#2635,1.);
#2635 = DIRECTION('',(0.,0.,-1.));
#2636 = PCURVE('',#2386,#2637);
#2637 = DEFINITIONAL_REPRESENTATION('',(#2638),#2642);
#2638 = LINE('',#2639,#2640);
#2639 = CARTESIAN_POINT('',(0.,0.));
#2640 = VECTOR('',#2641,1.);
#2641 = DIRECTION('',(-0.,-1.));
#2642 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2643 = PCURVE('',#2351,#2644);
#2644 = DEFINITIONAL_REPRESENTATION('',(#2645),#2649);
#2645 = LINE('',#2646,#2647);
#2646 = CARTESIAN_POINT('',(10.103721260868,0.));
#2647 = VECTOR('',#2648,1.);
#2648 = DIRECTION('',(0.,-1.));
#2649 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2650 = ORIENTED_EDGE('',*,*,#2363,.F.);
#2651 = ORIENTED_EDGE('',*,*,#2604,.F.);
#2652 = ADVANCED_FACE('',(#2653),#2351,.F.);
#2653 = FACE_BOUND('',#2654,.F.);
#2654 = EDGE_LOOP('',(#2655,#2656,#2657));
#2655 = ORIENTED_EDGE('',*,*,#2630,.F.);
#2656 = ORIENTED_EDGE('',*,*,#2578,.F.);
#2657 = ORIENTED_EDGE('',*,*,#2307,.F.);
#2658 = ( GEOMETRIC_REPRESENTATION_CONTEXT(3) 
GLOBAL_UNCERTAINTY_ASSIGNED_CONTEXT((#2662)) 
GLOBAL_UNIT_ASSIGNED_CONTEXT((#2659,#2660,#2661)) REPRESENTATION_CONTEXT
('Context #1','3D Context with UNIT and UNCERTAINTY') );
#2659 = ( LENGTH_UNIT() NAMED_UNIT(*) SI_UNIT(.MILLI.,.METRE.) );
#2660 = ( NAMED_UNIT(*) PLANE_ANGLE_UNIT() SI_UNIT($,.RADIAN.) );
#2661 = ( NAMED_UNIT(*) SI_UNIT($,.STERADIAN.) SOLID_ANGLE_UNIT() );
#2662 = UNCERTAINTY_MEASURE_WITH_UNIT(LENGTH_MEASURE(1.E-07),#2659,
  'distance_accuracy_value','confusion accuracy');
#2663 = CONTEXT_DEPENDENT_SHAPE_REPRESENTATION(#2664,#2666);
#2664 = ( REPRESENTATION_RELATIONSHIP('','',#2241,#10) 
REPRESENTATION_RELATIONSHIP_WITH_TRANSFORMATION(#2665) 
SHAPE_REPRESENTATION_RELATIONSHIP() );
#2665 = ITEM_DEFINED_TRANSFORMATION('','',#11,#43);
#2666 = PRODUCT_DEFINITION_SHAPE('Placement','Placement of an item',
  #2667);
#2667 = NEXT_ASSEMBLY_USAGE_OCCURRENCE('66','=>[0:1:1:9]','',#5,#2236,$
  );
#2668 = PRODUCT_RELATED_PRODUCT_CATEGORY('part',$,(#2238));
#2669 = SHAPE_DEFINITION_REPRESENTATION(#2670,#2676);
#2670 = PRODUCT_DEFINITION_SHAPE('','',#2671);
#2671 = PRODUCT_DEFINITION('design','',#2672,#2675);
#2672 = PRODUCT_DEFINITION_FORMATION('','',#2673);
#2673 = PRODUCT('PC1-P3_Part3','PC1-P3_Part3','',(#2674));
#2674 = PRODUCT_CONTEXT('',#2,'mechanical');
#2675 = PRODUCT_DEFINITION_CONTEXT('part definition',#2,'design');
#2676 = ADVANCED_BREP_SHAPE_REPRESENTATION('',(#11,#2677),#3081);
#2677 = MANIFOLD_SOLID_BREP('',#2678);
#2678 = CLOSED_SHELL('',(#2679,#2801,#2902,#2949,#3020,#3047,#3054));
#2679 = ADVANCED_FACE('',(#2680),#2695,.T.);
#2680 = FACE_BOUND('',#2681,.T.);
#2681 = EDGE_LOOP('',(#2682,#2718,#2744,#2777));
#2682 = ORIENTED_EDGE('',*,*,#2683,.T.);
#2683 = EDGE_CURVE('',#2684,#2686,#2688,.T.);
#2684 = VERTEX_POINT('',#2685);
#2685 = CARTESIAN_POINT('',(50.747818612774,126.51140045169,9.424596));
#2686 = VERTEX_POINT('',#2687);
#2687 = CARTESIAN_POINT('',(48.130261339781,133.28719981128,9.424596));
#2688 = SURFACE_CURVE('',#2689,(#2694,#2706),.PCURVE_S1.);
#2689 = CIRCLE('',#2690,4.316451884327);
#2690 = AXIS2_PLACEMENT_3D('',#2691,#2692,#2693);
#2691 = CARTESIAN_POINT('',(51.614918,130.739863,9.424596));
#2692 = DIRECTION('',(0.,0.,1.));
#2693 = DIRECTION('',(-0.20088244013,-0.979615355763,0.));
#2694 = PCURVE('',#2695,#2700);
#2695 = CYLINDRICAL_SURFACE('',#2696,4.316451884327);
#2696 = AXIS2_PLACEMENT_3D('',#2697,#2698,#2699);
#2697 = CARTESIAN_POINT('',(51.614918,130.739863,9.424596));
#2698 = DIRECTION('',(0.,0.,1.));
#2699 = DIRECTION('',(1.,0.,0.));
#2700 = DEFINITIONAL_REPRESENTATION('',(#2701),#2705);
#2701 = LINE('',#2702,#2703);
#2702 = CARTESIAN_POINT('',(4.510130339984,0.));
#2703 = VECTOR('',#2704,1.);
#2704 = DIRECTION('',(1.,0.));
#2705 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2706 = PCURVE('',#2707,#2712);
#2707 = PLANE('',#2708);
#2708 = AXIS2_PLACEMENT_3D('',#2709,#2710,#2711);
#2709 = CARTESIAN_POINT('',(51.614918,130.739863,9.424596));
#2710 = DIRECTION('',(0.,0.,1.));
#2711 = DIRECTION('',(1.,0.,0.));
#2712 = DEFINITIONAL_REPRESENTATION('',(#2713),#2717);
#2713 = CIRCLE('',#2714,4.316451884327);
#2714 = AXIS2_PLACEMENT_2D('',#2715,#2716);
#2715 = CARTESIAN_POINT('',(0.,0.));
#2716 = DIRECTION('',(-0.20088244013,-0.979615355763));
#2717 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2718 = ORIENTED_EDGE('',*,*,#2719,.T.);
#2719 = EDGE_CURVE('',#2686,#2720,#2722,.T.);
#2720 = VERTEX_POINT('',#2721);
#2721 = CARTESIAN_POINT('',(48.130261339781,133.28719981128,18.));
#2722 = SURFACE_CURVE('',#2723,(#2727,#2733),.PCURVE_S1.);
#2723 = LINE('',#2724,#2725);
#2724 = CARTESIAN_POINT('',(48.130261339781,133.28719981128,9.424596));
#2725 = VECTOR('',#2726,1.);
#2726 = DIRECTION('',(0.,0.,1.));
#2727 = PCURVE('',#2695,#2728);
#2728 = DEFINITIONAL_REPRESENTATION('',(#2729),#2732);
#2729 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#2730,#2731),.UNSPECIFIED.,.F.,
  .F.,(2,2),(0.,8.575404),.PIECEWISE_BEZIER_KNOTS.);
#2730 = CARTESIAN_POINT('',(8.793538282124,0.));
#2731 = CARTESIAN_POINT('',(8.793538282124,8.575404));
#2732 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2733 = PCURVE('',#2734,#2739);
#2734 = PLANE('',#2735);
#2735 = AXIS2_PLACEMENT_3D('',#2736,#2737,#2738);
#2736 = CARTESIAN_POINT('',(48.301331,131.800116,28.));
#2737 = DIRECTION('',(0.993448200572,0.11428330053,0.));
#2738 = DIRECTION('',(-0.11428330053,0.993448200572,0.));
#2739 = DEFINITIONAL_REPRESENTATION('',(#2740),#2743);
#2740 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#2741,#2742),.UNSPECIFIED.,.F.,
  .F.,(2,2),(-5.999999999062E-07,8.5754046),.PIECEWISE_BEZIER_KNOTS.);
#2741 = CARTESIAN_POINT('',(1.496891141807,-18.5754046));
#2742 = CARTESIAN_POINT('',(1.496891141807,-9.9999994));
#2743 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2744 = ORIENTED_EDGE('',*,*,#2745,.T.);
#2745 = EDGE_CURVE('',#2720,#2746,#2748,.T.);
#2746 = VERTEX_POINT('',#2747);
#2747 = CARTESIAN_POINT('',(50.747818612774,126.51140045169,18.));
#2748 = SURFACE_CURVE('',#2749,(#2754,#2761),.PCURVE_S1.);
#2749 = CIRCLE('',#2750,4.316451884327);
#2750 = AXIS2_PLACEMENT_3D('',#2751,#2752,#2753);
#2751 = CARTESIAN_POINT('',(51.614918,130.739863,18.));
#2752 = DIRECTION('',(0.,0.,-1.));
#2753 = DIRECTION('',(-0.807296537434,0.590145999433,0.));
#2754 = PCURVE('',#2695,#2755);
#2755 = DEFINITIONAL_REPRESENTATION('',(#2756),#2760);
#2756 = LINE('',#2757,#2758);
#2757 = CARTESIAN_POINT('',(8.793538282124,8.575404));
#2758 = VECTOR('',#2759,1.);
#2759 = DIRECTION('',(-1.,-0.));
#2760 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2761 = PCURVE('',#2762,#2767);
#2762 = PLANE('',#2763);
#2763 = AXIS2_PLACEMENT_3D('',#2764,#2765,#2766);
#2764 = CARTESIAN_POINT('',(51.614918,130.739863,18.));
#2765 = DIRECTION('',(0.,0.,1.));
#2766 = DIRECTION('',(1.,0.,0.));
#2767 = DEFINITIONAL_REPRESENTATION('',(#2768),#2776);
#2768 = ( BOUNDED_CURVE() B_SPLINE_CURVE(2,(#2769,#2770,#2771,#2772,
#2773,#2774,#2775),.UNSPECIFIED.,.T.,.F.) B_SPLINE_CURVE_WITH_KNOTS((1,2
    ,2,2,2,1),(-2.094395102393,0.,2.094395102393,4.188790204786,
6.28318530718,8.377580409573),.UNSPECIFIED.) CURVE() 
GEOMETRIC_REPRESENTATION_ITEM() RATIONAL_B_SPLINE_CURVE((1.,0.5,1.,0.5,
1.,0.5,1.)) REPRESENTATION_ITEM('') );
#2769 = CARTESIAN_POINT('',(-3.484656660219,2.54733681128));
#2770 = CARTESIAN_POINT('',(0.927460120909,8.582939193713));
#2771 = CARTESIAN_POINT('',(3.948386720674,1.744132785576));
#2772 = CARTESIAN_POINT('',(6.969313320438,-5.09467362256));
#2773 = CARTESIAN_POINT('',(-0.463730060454,-4.291469596857));
#2774 = CARTESIAN_POINT('',(-7.896773441347,-3.488265571153));
#2775 = CARTESIAN_POINT('',(-3.484656660219,2.54733681128));
#2776 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2777 = ORIENTED_EDGE('',*,*,#2778,.F.);
#2778 = EDGE_CURVE('',#2684,#2746,#2779,.T.);
#2779 = SURFACE_CURVE('',#2780,(#2784,#2790),.PCURVE_S1.);
#2780 = LINE('',#2781,#2782);
#2781 = CARTESIAN_POINT('',(50.747818612774,126.51140045169,9.424596));
#2782 = VECTOR('',#2783,1.);
#2783 = DIRECTION('',(0.,0.,1.));
#2784 = PCURVE('',#2695,#2785);
#2785 = DEFINITIONAL_REPRESENTATION('',(#2786),#2789);
#2786 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#2787,#2788),.UNSPECIFIED.,.F.,
  .F.,(2,2),(0.,8.575404),.PIECEWISE_BEZIER_KNOTS.);
#2787 = CARTESIAN_POINT('',(4.510130339984,0.));
#2788 = CARTESIAN_POINT('',(4.510130339984,8.575404));
#2789 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2790 = PCURVE('',#2791,#2796);
#2791 = PLANE('',#2792);
#2792 = AXIS2_PLACEMENT_3D('',#2793,#2794,#2795);
#2793 = CARTESIAN_POINT('',(51.008936,121.80569,28.));
#2794 = DIRECTION('',(0.998464005442,5.540424023454E-02,0.));
#2795 = DIRECTION('',(-5.540424023454E-02,0.998464005442,0.));
#2796 = DEFINITIONAL_REPRESENTATION('',(#2797),#2800);
#2797 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#2798,#2799),.UNSPECIFIED.,.F.,
  .F.,(2,2),(-5.999999999062E-07,8.5754046),.PIECEWISE_BEZIER_KNOTS.);
#2798 = CARTESIAN_POINT('',(4.712949516507,-18.5754046));
#2799 = CARTESIAN_POINT('',(4.712949516507,-9.9999994));
#2800 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2801 = ADVANCED_FACE('',(#2802,#2871),#2707,.F.);
#2802 = FACE_BOUND('',#2803,.F.);
#2803 = EDGE_LOOP('',(#2804,#2805,#2826,#2852));
#2804 = ORIENTED_EDGE('',*,*,#2683,.T.);
#2805 = ORIENTED_EDGE('',*,*,#2806,.F.);
#2806 = EDGE_CURVE('',#2807,#2686,#2809,.T.);
#2807 = VERTEX_POINT('',#2808);
#2808 = CARTESIAN_POINT('',(48.301331,131.800116,9.424596));
#2809 = SURFACE_CURVE('',#2810,(#2814,#2820),.PCURVE_S1.);
#2810 = LINE('',#2811,#2812);
#2811 = CARTESIAN_POINT('',(48.383157505698,131.08881002206,9.424596));
#2812 = VECTOR('',#2813,1.);
#2813 = DIRECTION('',(-0.11428330053,0.993448200572,0.));
#2814 = PCURVE('',#2707,#2815);
#2815 = DEFINITIONAL_REPRESENTATION('',(#2816),#2819);
#2816 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#2817,#2818),.UNSPECIFIED.,.F.,
  .F.,(2,2),(0.715997046977,3.993670590954),.PIECEWISE_BEZIER_KNOTS.);
#2817 = CARTESIAN_POINT('',(-3.313587,1.060253));
#2818 = CARTESIAN_POINT('',(-3.688170350664,4.316451884327));
#2819 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2820 = PCURVE('',#2734,#2821);
#2821 = DEFINITIONAL_REPRESENTATION('',(#2822),#2825);
#2822 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#2823,#2824),.UNSPECIFIED.,.F.,
  .F.,(2,2),(0.715997046977,3.993670590954),.PIECEWISE_BEZIER_KNOTS.);
#2823 = CARTESIAN_POINT('',(0.,-18.575404));
#2824 = CARTESIAN_POINT('',(3.277673543977,-18.575404));
#2825 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2826 = ORIENTED_EDGE('',*,*,#2827,.F.);
#2827 = EDGE_CURVE('',#2828,#2807,#2830,.T.);
#2828 = VERTEX_POINT('',#2829);
#2829 = CARTESIAN_POINT('',(50.449147,131.893892,9.424596));
#2830 = SURFACE_CURVE('',#2831,(#2835,#2841),.PCURVE_S1.);
#2831 = LINE('',#2832,#2833);
#2832 = CARTESIAN_POINT('',(51.005778313255,131.91819513305,9.424596));
#2833 = VECTOR('',#2834,1.);
#2834 = DIRECTION('',(-0.999048214928,-4.361953975718E-02,0.));
#2835 = PCURVE('',#2707,#2836);
#2836 = DEFINITIONAL_REPRESENTATION('',(#2837),#2840);
#2837 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#2838,#2839),.UNSPECIFIED.,.F.,
  .F.,(2,2),(0.557161611359,2.707023818114),.PIECEWISE_BEZIER_KNOTS.);
#2838 = CARTESIAN_POINT('',(-1.165771,1.154029));
#2839 = CARTESIAN_POINT('',(-3.313587,1.060253));
#2840 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2841 = PCURVE('',#2842,#2847);
#2842 = PLANE('',#2843);
#2843 = AXIS2_PLACEMENT_3D('',#2844,#2845,#2846);
#2844 = CARTESIAN_POINT('',(50.449147,131.893892,28.));
#2845 = DIRECTION('',(-4.361953975718E-02,0.999048214928,0.));
#2846 = DIRECTION('',(-0.999048214928,-4.361953975718E-02,0.));
#2847 = DEFINITIONAL_REPRESENTATION('',(#2848),#2851);
#2848 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#2849,#2850),.UNSPECIFIED.,.F.,
  .F.,(2,2),(0.557161611359,2.707023818114),.PIECEWISE_BEZIER_KNOTS.);
#2849 = CARTESIAN_POINT('',(0.,-18.575404));
#2850 = CARTESIAN_POINT('',(2.149862206755,-18.575404));
#2851 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2852 = ORIENTED_EDGE('',*,*,#2853,.F.);
#2853 = EDGE_CURVE('',#2684,#2828,#2854,.T.);
#2854 = SURFACE_CURVE('',#2855,(#2859,#2865),.PCURVE_S1.);
#2855 = LINE('',#2856,#2857);
#2856 = CARTESIAN_POINT('',(50.762750688412,126.24230299656,9.424596));
#2857 = VECTOR('',#2858,1.);
#2858 = DIRECTION('',(-5.540424023454E-02,0.998464005442,0.));
#2859 = PCURVE('',#2707,#2860);
#2860 = DEFINITIONAL_REPRESENTATION('',(#2861),#2864);
#2861 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#2862,#2863),.UNSPECIFIED.,.F.,
  .F.,(2,2),(0.181386728134,5.660283167574),.PIECEWISE_BEZIER_KNOTS.);
#2862 = CARTESIAN_POINT('',(-0.862216905449,-4.316451884327));
#2863 = CARTESIAN_POINT('',(-1.165771,1.154029));
#2864 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2865 = PCURVE('',#2791,#2866);
#2866 = DEFINITIONAL_REPRESENTATION('',(#2867),#2870);
#2867 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#2868,#2869),.UNSPECIFIED.,.F.,
  .F.,(2,2),(0.181386728134,5.660283167574),.PIECEWISE_BEZIER_KNOTS.);
#2868 = CARTESIAN_POINT('',(4.624824821428,-18.575404));
#2869 = CARTESIAN_POINT('',(10.103721260868,-18.575404));
#2870 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2871 = FACE_BOUND('',#2872,.F.);
#2872 = EDGE_LOOP('',(#2873));
#2873 = ORIENTED_EDGE('',*,*,#2874,.F.);
#2874 = EDGE_CURVE('',#2875,#2875,#2877,.T.);
#2875 = VERTEX_POINT('',#2876);
#2876 = CARTESIAN_POINT('',(51.889918,130.739863,9.424596));
#2877 = SURFACE_CURVE('',#2878,(#2883,#2890),.PCURVE_S1.);
#2878 = CIRCLE('',#2879,0.275);
#2879 = AXIS2_PLACEMENT_3D('',#2880,#2881,#2882);
#2880 = CARTESIAN_POINT('',(51.614918,130.739863,9.424596));
#2881 = DIRECTION('',(0.,0.,1.));
#2882 = DIRECTION('',(1.,0.,0.));
#2883 = PCURVE('',#2707,#2884);
#2884 = DEFINITIONAL_REPRESENTATION('',(#2885),#2889);
#2885 = CIRCLE('',#2886,0.275);
#2886 = AXIS2_PLACEMENT_2D('',#2887,#2888);
#2887 = CARTESIAN_POINT('',(0.,0.));
#2888 = DIRECTION('',(1.,0.));
#2889 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2890 = PCURVE('',#2891,#2896);
#2891 = CYLINDRICAL_SURFACE('',#2892,0.275);
#2892 = AXIS2_PLACEMENT_3D('',#2893,#2894,#2895);
#2893 = CARTESIAN_POINT('',(51.614918,130.739863,9.424596));
#2894 = DIRECTION('',(0.,0.,1.));
#2895 = DIRECTION('',(1.,0.,0.));
#2896 = DEFINITIONAL_REPRESENTATION('',(#2897),#2901);
#2897 = LINE('',#2898,#2899);
#2898 = CARTESIAN_POINT('',(0.,0.));
#2899 = VECTOR('',#2900,1.);
#2900 = DIRECTION('',(1.,0.));
#2901 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2902 = ADVANCED_FACE('',(#2903),#2791,.F.);
#2903 = FACE_BOUND('',#2904,.F.);
#2904 = EDGE_LOOP('',(#2905,#2928,#2947,#2948));
#2905 = ORIENTED_EDGE('',*,*,#2906,.F.);
#2906 = EDGE_CURVE('',#2907,#2828,#2909,.T.);
#2907 = VERTEX_POINT('',#2908);
#2908 = CARTESIAN_POINT('',(50.449147,131.893892,18.));
#2909 = SURFACE_CURVE('',#2910,(#2914,#2921),.PCURVE_S1.);
#2910 = LINE('',#2911,#2912);
#2911 = CARTESIAN_POINT('',(50.449147,131.893892,28.));
#2912 = VECTOR('',#2913,1.);
#2913 = DIRECTION('',(0.,0.,-1.));
#2914 = PCURVE('',#2791,#2915);
#2915 = DEFINITIONAL_REPRESENTATION('',(#2916),#2920);
#2916 = LINE('',#2917,#2918);
#2917 = CARTESIAN_POINT('',(10.103721260868,0.));
#2918 = VECTOR('',#2919,1.);
#2919 = DIRECTION('',(0.,-1.));
#2920 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2921 = PCURVE('',#2842,#2922);
#2922 = DEFINITIONAL_REPRESENTATION('',(#2923),#2927);
#2923 = LINE('',#2924,#2925);
#2924 = CARTESIAN_POINT('',(0.,0.));
#2925 = VECTOR('',#2926,1.);
#2926 = DIRECTION('',(-0.,-1.));
#2927 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2928 = ORIENTED_EDGE('',*,*,#2929,.F.);
#2929 = EDGE_CURVE('',#2746,#2907,#2930,.T.);
#2930 = SURFACE_CURVE('',#2931,(#2935,#2941),.PCURVE_S1.);
#2931 = LINE('',#2932,#2933);
#2932 = CARTESIAN_POINT('',(50.762750688412,126.24230299656,18.));
#2933 = VECTOR('',#2934,1.);
#2934 = DIRECTION('',(-5.540424023454E-02,0.998464005442,0.));
#2935 = PCURVE('',#2791,#2936);
#2936 = DEFINITIONAL_REPRESENTATION('',(#2937),#2940);
#2937 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#2938,#2939),.UNSPECIFIED.,.F.,
  .F.,(2,2),(0.181386728134,5.660283167574),.PIECEWISE_BEZIER_KNOTS.);
#2938 = CARTESIAN_POINT('',(4.624824821428,-10.));
#2939 = CARTESIAN_POINT('',(10.103721260868,-10.));
#2940 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2941 = PCURVE('',#2762,#2942);
#2942 = DEFINITIONAL_REPRESENTATION('',(#2943),#2946);
#2943 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#2944,#2945),.UNSPECIFIED.,.F.,
  .F.,(2,2),(0.181386728134,5.660283167574),.PIECEWISE_BEZIER_KNOTS.);
#2944 = CARTESIAN_POINT('',(-0.862216905449,-4.316451884327));
#2945 = CARTESIAN_POINT('',(-1.165771,1.154029));
#2946 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2947 = ORIENTED_EDGE('',*,*,#2778,.F.);
#2948 = ORIENTED_EDGE('',*,*,#2853,.T.);
#2949 = ADVANCED_FACE('',(#2950,#2994),#2762,.T.);
#2950 = FACE_BOUND('',#2951,.T.);
#2951 = EDGE_LOOP('',(#2952,#2953,#2954,#2975));
#2952 = ORIENTED_EDGE('',*,*,#2929,.F.);
#2953 = ORIENTED_EDGE('',*,*,#2745,.F.);
#2954 = ORIENTED_EDGE('',*,*,#2955,.F.);
#2955 = EDGE_CURVE('',#2956,#2720,#2958,.T.);
#2956 = VERTEX_POINT('',#2957);
#2957 = CARTESIAN_POINT('',(48.301331,131.800116,18.));
#2958 = SURFACE_CURVE('',#2959,(#2963,#2969),.PCURVE_S1.);
#2959 = LINE('',#2960,#2961);
#2960 = CARTESIAN_POINT('',(48.383157505698,131.08881002206,18.));
#2961 = VECTOR('',#2962,1.);
#2962 = DIRECTION('',(-0.11428330053,0.993448200572,0.));
#2963 = PCURVE('',#2762,#2964);
#2964 = DEFINITIONAL_REPRESENTATION('',(#2965),#2968);
#2965 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#2966,#2967),.UNSPECIFIED.,.F.,
  .F.,(2,2),(0.715997046977,3.993670590954),.PIECEWISE_BEZIER_KNOTS.);
#2966 = CARTESIAN_POINT('',(-3.313587,1.060253));
#2967 = CARTESIAN_POINT('',(-3.688170350664,4.316451884327));
#2968 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2969 = PCURVE('',#2734,#2970);
#2970 = DEFINITIONAL_REPRESENTATION('',(#2971),#2974);
#2971 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#2972,#2973),.UNSPECIFIED.,.F.,
  .F.,(2,2),(0.715997046977,3.993670590954),.PIECEWISE_BEZIER_KNOTS.);
#2972 = CARTESIAN_POINT('',(0.,-10.));
#2973 = CARTESIAN_POINT('',(3.277673543977,-10.));
#2974 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2975 = ORIENTED_EDGE('',*,*,#2976,.F.);
#2976 = EDGE_CURVE('',#2907,#2956,#2977,.T.);
#2977 = SURFACE_CURVE('',#2978,(#2982,#2988),.PCURVE_S1.);
#2978 = LINE('',#2979,#2980);
#2979 = CARTESIAN_POINT('',(51.005778313255,131.91819513305,18.));
#2980 = VECTOR('',#2981,1.);
#2981 = DIRECTION('',(-0.999048214928,-4.361953975718E-02,0.));
#2982 = PCURVE('',#2762,#2983);
#2983 = DEFINITIONAL_REPRESENTATION('',(#2984),#2987);
#2984 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#2985,#2986),.UNSPECIFIED.,.F.,
  .F.,(2,2),(0.557161611359,2.707023818114),.PIECEWISE_BEZIER_KNOTS.);
#2985 = CARTESIAN_POINT('',(-1.165771,1.154029));
#2986 = CARTESIAN_POINT('',(-3.313587,1.060253));
#2987 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2988 = PCURVE('',#2842,#2989);
#2989 = DEFINITIONAL_REPRESENTATION('',(#2990),#2993);
#2990 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#2991,#2992),.UNSPECIFIED.,.F.,
  .F.,(2,2),(0.557161611359,2.707023818114),.PIECEWISE_BEZIER_KNOTS.);
#2991 = CARTESIAN_POINT('',(0.,-10.));
#2992 = CARTESIAN_POINT('',(2.149862206755,-10.));
#2993 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2994 = FACE_BOUND('',#2995,.T.);
#2995 = EDGE_LOOP('',(#2996));
#2996 = ORIENTED_EDGE('',*,*,#2997,.F.);
#2997 = EDGE_CURVE('',#2998,#2998,#3000,.T.);
#2998 = VERTEX_POINT('',#2999);
#2999 = CARTESIAN_POINT('',(51.889918,130.739863,18.));
#3000 = SURFACE_CURVE('',#3001,(#3006,#3013),.PCURVE_S1.);
#3001 = CIRCLE('',#3002,0.275);
#3002 = AXIS2_PLACEMENT_3D('',#3003,#3004,#3005);
#3003 = CARTESIAN_POINT('',(51.614918,130.739863,18.));
#3004 = DIRECTION('',(0.,0.,1.));
#3005 = DIRECTION('',(1.,0.,0.));
#3006 = PCURVE('',#2762,#3007);
#3007 = DEFINITIONAL_REPRESENTATION('',(#3008),#3012);
#3008 = CIRCLE('',#3009,0.275);
#3009 = AXIS2_PLACEMENT_2D('',#3010,#3011);
#3010 = CARTESIAN_POINT('',(0.,0.));
#3011 = DIRECTION('',(1.,0.));
#3012 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3013 = PCURVE('',#2891,#3014);
#3014 = DEFINITIONAL_REPRESENTATION('',(#3015),#3019);
#3015 = LINE('',#3016,#3017);
#3016 = CARTESIAN_POINT('',(0.,8.575404));
#3017 = VECTOR('',#3018,1.);
#3018 = DIRECTION('',(1.,0.));
#3019 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3020 = ADVANCED_FACE('',(#3021),#2734,.F.);
#3021 = FACE_BOUND('',#3022,.F.);
#3022 = EDGE_LOOP('',(#3023,#3024,#3045,#3046));
#3023 = ORIENTED_EDGE('',*,*,#2955,.F.);
#3024 = ORIENTED_EDGE('',*,*,#3025,.T.);
#3025 = EDGE_CURVE('',#2956,#2807,#3026,.T.);
#3026 = SURFACE_CURVE('',#3027,(#3031,#3038),.PCURVE_S1.);
#3027 = LINE('',#3028,#3029);
#3028 = CARTESIAN_POINT('',(48.301331,131.800116,28.));
#3029 = VECTOR('',#3030,1.);
#3030 = DIRECTION('',(0.,0.,-1.));
#3031 = PCURVE('',#2734,#3032);
#3032 = DEFINITIONAL_REPRESENTATION('',(#3033),#3037);
#3033 = LINE('',#3034,#3035);
#3034 = CARTESIAN_POINT('',(0.,0.));
#3035 = VECTOR('',#3036,1.);
#3036 = DIRECTION('',(0.,-1.));
#3037 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3038 = PCURVE('',#2842,#3039);
#3039 = DEFINITIONAL_REPRESENTATION('',(#3040),#3044);
#3040 = LINE('',#3041,#3042);
#3041 = CARTESIAN_POINT('',(2.149862206755,0.));
#3042 = VECTOR('',#3043,1.);
#3043 = DIRECTION('',(-0.,-1.));
#3044 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3045 = ORIENTED_EDGE('',*,*,#2806,.T.);
#3046 = ORIENTED_EDGE('',*,*,#2719,.T.);
#3047 = ADVANCED_FACE('',(#3048),#2842,.F.);
#3048 = FACE_BOUND('',#3049,.F.);
#3049 = EDGE_LOOP('',(#3050,#3051,#3052,#3053));
#3050 = ORIENTED_EDGE('',*,*,#2976,.F.);
#3051 = ORIENTED_EDGE('',*,*,#2906,.T.);
#3052 = ORIENTED_EDGE('',*,*,#2827,.T.);
#3053 = ORIENTED_EDGE('',*,*,#3025,.F.);
#3054 = ADVANCED_FACE('',(#3055),#2891,.F.);
#3055 = FACE_BOUND('',#3056,.F.);
#3056 = EDGE_LOOP('',(#3057,#3058,#3079,#3080));
#3057 = ORIENTED_EDGE('',*,*,#2997,.F.);
#3058 = ORIENTED_EDGE('',*,*,#3059,.F.);
#3059 = EDGE_CURVE('',#2875,#2998,#3060,.T.);
#3060 = SEAM_CURVE('',#3061,(#3065,#3072),.PCURVE_S1.);
#3061 = LINE('',#3062,#3063);
#3062 = CARTESIAN_POINT('',(51.889918,130.739863,9.424596));
#3063 = VECTOR('',#3064,1.);
#3064 = DIRECTION('',(0.,0.,1.));
#3065 = PCURVE('',#2891,#3066);
#3066 = DEFINITIONAL_REPRESENTATION('',(#3067),#3071);
#3067 = LINE('',#3068,#3069);
#3068 = CARTESIAN_POINT('',(6.28318530718,-0.));
#3069 = VECTOR('',#3070,1.);
#3070 = DIRECTION('',(0.,1.));
#3071 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3072 = PCURVE('',#2891,#3073);
#3073 = DEFINITIONAL_REPRESENTATION('',(#3074),#3078);
#3074 = LINE('',#3075,#3076);
#3075 = CARTESIAN_POINT('',(0.,-0.));
#3076 = VECTOR('',#3077,1.);
#3077 = DIRECTION('',(0.,1.));
#3078 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3079 = ORIENTED_EDGE('',*,*,#2874,.T.);
#3080 = ORIENTED_EDGE('',*,*,#3059,.T.);
#3081 = ( GEOMETRIC_REPRESENTATION_CONTEXT(3) 
GLOBAL_UNCERTAINTY_ASSIGNED_CONTEXT((#3085)) 
GLOBAL_UNIT_ASSIGNED_CONTEXT((#3082,#3083,#3084)) REPRESENTATION_CONTEXT
('Context #1','3D Context with UNIT and UNCERTAINTY') );
#3082 = ( LENGTH_UNIT() NAMED_UNIT(*) SI_UNIT(.MILLI.,.METRE.) );
#3083 = ( NAMED_UNIT(*) PLANE_ANGLE_UNIT() SI_UNIT($,.RADIAN.) );
#3084 = ( NAMED_UNIT(*) SI_UNIT($,.STERADIAN.) SOLID_ANGLE_UNIT() );
#3085 = UNCERTAINTY_MEASURE_WITH_UNIT(LENGTH_MEASURE(1.E-07),#3082,
  'distance_accuracy_value','confusion accuracy');
#3086 = CONTEXT_DEPENDENT_SHAPE_REPRESENTATION(#3087,#3089);
#3087 = ( REPRESENTATION_RELATIONSHIP('','',#2676,#10) 
REPRESENTATION_RELATIONSHIP_WITH_TRANSFORMATION(#3088) 
SHAPE_REPRESENTATION_RELATIONSHIP() );
#3088 = ITEM_DEFINED_TRANSFORMATION('','',#11,#47);
#3089 = PRODUCT_DEFINITION_SHAPE('Placement','Placement of an item',
  #3090);
#3090 = NEXT_ASSEMBLY_USAGE_OCCURRENCE('67','=>[0:1:1:10]','',#5,#2671,$
  );
#3091 = PRODUCT_RELATED_PRODUCT_CATEGORY('part',$,(#2673));
#3092 = SHAPE_DEFINITION_REPRESENTATION(#3093,#3099);
#3093 = PRODUCT_DEFINITION_SHAPE('','',#3094);
#3094 = PRODUCT_DEFINITION('design','',#3095,#3098);
#3095 = PRODUCT_DEFINITION_FORMATION('','',#3096);
#3096 = PRODUCT('PC1-P4_Part1','PC1-P4_Part1','',(#3097));
#3097 = PRODUCT_CONTEXT('',#2,'mechanical');
#3098 = PRODUCT_DEFINITION_CONTEXT('part definition',#2,'design');
#3099 = ADVANCED_BREP_SHAPE_REPRESENTATION('',(#11,#3100),#3198);
#3100 = MANIFOLD_SOLID_BREP('',#3101);
#3101 = CLOSED_SHELL('',(#3102,#3190,#3194));
#3102 = ADVANCED_FACE('',(#3103),#3116,.T.);
#3103 = FACE_BOUND('',#3104,.T.);
#3104 = EDGE_LOOP('',(#3105,#3139,#3162,#3189));
#3105 = ORIENTED_EDGE('',*,*,#3106,.F.);
#3106 = EDGE_CURVE('',#3107,#3107,#3109,.T.);
#3107 = VERTEX_POINT('',#3108);
#3108 = CARTESIAN_POINT('',(0.275,-6.73555739531E-17,15.004749));
#3109 = SURFACE_CURVE('',#3110,(#3115,#3127),.PCURVE_S1.);
#3110 = CIRCLE('',#3111,0.275);
#3111 = AXIS2_PLACEMENT_3D('',#3112,#3113,#3114);
#3112 = CARTESIAN_POINT('',(0.,0.,15.004749));
#3113 = DIRECTION('',(0.,0.,1.));
#3114 = DIRECTION('',(1.,0.,-0.));
#3115 = PCURVE('',#3116,#3121);
#3116 = CYLINDRICAL_SURFACE('',#3117,0.275);
#3117 = AXIS2_PLACEMENT_3D('',#3118,#3119,#3120);
#3118 = CARTESIAN_POINT('',(0.,0.,0.));
#3119 = DIRECTION('',(0.,0.,1.));
#3120 = DIRECTION('',(1.,0.,-0.));
#3121 = DEFINITIONAL_REPRESENTATION('',(#3122),#3126);
#3122 = LINE('',#3123,#3124);
#3123 = CARTESIAN_POINT('',(0.,15.004749));
#3124 = VECTOR('',#3125,1.);
#3125 = DIRECTION('',(1.,0.));
#3126 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3127 = PCURVE('',#3128,#3133);
#3128 = PLANE('',#3129);
#3129 = AXIS2_PLACEMENT_3D('',#3130,#3131,#3132);
#3130 = CARTESIAN_POINT('',(0.,0.,15.004749));
#3131 = DIRECTION('',(0.,0.,1.));
#3132 = DIRECTION('',(1.,0.,-0.));
#3133 = DEFINITIONAL_REPRESENTATION('',(#3134),#3138);
#3134 = CIRCLE('',#3135,0.275);
#3135 = AXIS2_PLACEMENT_2D('',#3136,#3137);
#3136 = CARTESIAN_POINT('',(0.,0.));
#3137 = DIRECTION('',(1.,0.));
#3138 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3139 = ORIENTED_EDGE('',*,*,#3140,.F.);
#3140 = EDGE_CURVE('',#3141,#3107,#3143,.T.);
#3141 = VERTEX_POINT('',#3142);
#3142 = CARTESIAN_POINT('',(0.275,-6.73555739531E-17,0.));
#3143 = SEAM_CURVE('',#3144,(#3148,#3155),.PCURVE_S1.);
#3144 = LINE('',#3145,#3146);
#3145 = CARTESIAN_POINT('',(0.275,-6.73555739531E-17,0.));
#3146 = VECTOR('',#3147,1.);
#3147 = DIRECTION('',(0.,0.,1.));
#3148 = PCURVE('',#3116,#3149);
#3149 = DEFINITIONAL_REPRESENTATION('',(#3150),#3154);
#3150 = LINE('',#3151,#3152);
#3151 = CARTESIAN_POINT('',(6.28318530718,-0.));
#3152 = VECTOR('',#3153,1.);
#3153 = DIRECTION('',(0.,1.));
#3154 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3155 = PCURVE('',#3116,#3156);
#3156 = DEFINITIONAL_REPRESENTATION('',(#3157),#3161);
#3157 = LINE('',#3158,#3159);
#3158 = CARTESIAN_POINT('',(0.,-0.));
#3159 = VECTOR('',#3160,1.);
#3160 = DIRECTION('',(0.,1.));
#3161 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3162 = ORIENTED_EDGE('',*,*,#3163,.T.);
#3163 = EDGE_CURVE('',#3141,#3141,#3164,.T.);
#3164 = SURFACE_CURVE('',#3165,(#3170,#3177),.PCURVE_S1.);
#3165 = CIRCLE('',#3166,0.275);
#3166 = AXIS2_PLACEMENT_3D('',#3167,#3168,#3169);
#3167 = CARTESIAN_POINT('',(0.,0.,0.));
#3168 = DIRECTION('',(0.,0.,1.));
#3169 = DIRECTION('',(1.,0.,-0.));
#3170 = PCURVE('',#3116,#3171);
#3171 = DEFINITIONAL_REPRESENTATION('',(#3172),#3176);
#3172 = LINE('',#3173,#3174);
#3173 = CARTESIAN_POINT('',(0.,0.));
#3174 = VECTOR('',#3175,1.);
#3175 = DIRECTION('',(1.,0.));
#3176 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3177 = PCURVE('',#3178,#3183);
#3178 = PLANE('',#3179);
#3179 = AXIS2_PLACEMENT_3D('',#3180,#3181,#3182);
#3180 = CARTESIAN_POINT('',(0.,0.,0.));
#3181 = DIRECTION('',(0.,0.,1.));
#3182 = DIRECTION('',(1.,0.,-0.));
#3183 = DEFINITIONAL_REPRESENTATION('',(#3184),#3188);
#3184 = CIRCLE('',#3185,0.275);
#3185 = AXIS2_PLACEMENT_2D('',#3186,#3187);
#3186 = CARTESIAN_POINT('',(0.,0.));
#3187 = DIRECTION('',(1.,0.));
#3188 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3189 = ORIENTED_EDGE('',*,*,#3140,.T.);
#3190 = ADVANCED_FACE('',(#3191),#3128,.T.);
#3191 = FACE_BOUND('',#3192,.T.);
#3192 = EDGE_LOOP('',(#3193));
#3193 = ORIENTED_EDGE('',*,*,#3106,.T.);
#3194 = ADVANCED_FACE('',(#3195),#3178,.F.);
#3195 = FACE_BOUND('',#3196,.T.);
#3196 = EDGE_LOOP('',(#3197));
#3197 = ORIENTED_EDGE('',*,*,#3163,.F.);
#3198 = ( GEOMETRIC_REPRESENTATION_CONTEXT(3) 
GLOBAL_UNCERTAINTY_ASSIGNED_CONTEXT((#3202)) 
GLOBAL_UNIT_ASSIGNED_CONTEXT((#3199,#3200,#3201)) REPRESENTATION_CONTEXT
('Context #1','3D Context with UNIT and UNCERTAINTY') );
#3199 = ( LENGTH_UNIT() NAMED_UNIT(*) SI_UNIT(.MILLI.,.METRE.) );
#3200 = ( NAMED_UNIT(*) PLANE_ANGLE_UNIT() SI_UNIT($,.RADIAN.) );
#3201 = ( NAMED_UNIT(*) SI_UNIT($,.STERADIAN.) SOLID_ANGLE_UNIT() );
#3202 = UNCERTAINTY_MEASURE_WITH_UNIT(LENGTH_MEASURE(1.E-07),#3199,
  'distance_accuracy_value','confusion accuracy');
#3203 = CONTEXT_DEPENDENT_SHAPE_REPRESENTATION(#3204,#3206);
#3204 = ( REPRESENTATION_RELATIONSHIP('','',#3099,#10) 
REPRESENTATION_RELATIONSHIP_WITH_TRANSFORMATION(#3205) 
SHAPE_REPRESENTATION_RELATIONSHIP() );
#3205 = ITEM_DEFINED_TRANSFORMATION('','',#11,#51);
#3206 = PRODUCT_DEFINITION_SHAPE('Placement','Placement of an item',
  #3207);
#3207 = NEXT_ASSEMBLY_USAGE_OCCURRENCE('68','=>[0:1:1:11]','',#5,#3094,$
  );
#3208 = PRODUCT_RELATED_PRODUCT_CATEGORY('part',$,(#3096));
#3209 = SHAPE_DEFINITION_REPRESENTATION(#3210,#3216);
#3210 = PRODUCT_DEFINITION_SHAPE('','',#3211);
#3211 = PRODUCT_DEFINITION('design','',#3212,#3215);
#3212 = PRODUCT_DEFINITION_FORMATION('','',#3213);
#3213 = PRODUCT('PC1-P4_Part2','PC1-P4_Part2','',(#3214));
#3214 = PRODUCT_CONTEXT('',#2,'mechanical');
#3215 = PRODUCT_DEFINITION_CONTEXT('part definition',#2,'design');
#3216 = ADVANCED_BREP_SHAPE_REPRESENTATION('',(#11,#3217),#3550);
#3217 = MANIFOLD_SOLID_BREP('',#3218);
#3218 = CLOSED_SHELL('',(#3219,#3420,#3470,#3518,#3544));
#3219 = ADVANCED_FACE('',(#3220),#3234,.T.);
#3220 = FACE_BOUND('',#3221,.T.);
#3221 = EDGE_LOOP('',(#3222,#3252,#3281,#3337,#3371,#3393,#3394));
#3222 = ORIENTED_EDGE('',*,*,#3223,.T.);
#3223 = EDGE_CURVE('',#3224,#3226,#3228,.T.);
#3224 = VERTEX_POINT('',#3225);
#3225 = CARTESIAN_POINT('',(51.981341,129.092298,2.995251));
#3226 = VERTEX_POINT('',#3227);
#3227 = CARTESIAN_POINT('',(56.022792884327,129.092298,9.995251));
#3228 = SEAM_CURVE('',#3229,(#3233,#3245),.PCURVE_S1.);
#3229 = LINE('',#3230,#3231);
#3230 = CARTESIAN_POINT('',(51.981341,129.092298,2.995251));
#3231 = VECTOR('',#3232,1.);
#3232 = DIRECTION('',(0.5,-1.224646799147E-16,0.866025403784));
#3233 = PCURVE('',#3234,#3239);
#3234 = CONICAL_SURFACE('',#3235,0.275,0.523598775598);
#3235 = AXIS2_PLACEMENT_3D('',#3236,#3237,#3238);
#3236 = CARTESIAN_POINT('',(51.706341,129.092298,2.995251));
#3237 = DIRECTION('',(0.,0.,1.));
#3238 = DIRECTION('',(1.,0.,0.));
#3239 = DEFINITIONAL_REPRESENTATION('',(#3240),#3244);
#3240 = LINE('',#3241,#3242);
#3241 = CARTESIAN_POINT('',(6.28318530718,-0.));
#3242 = VECTOR('',#3243,1.);
#3243 = DIRECTION('',(0.,1.));
#3244 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3245 = PCURVE('',#3234,#3246);
#3246 = DEFINITIONAL_REPRESENTATION('',(#3247),#3251);
#3247 = LINE('',#3248,#3249);
#3248 = CARTESIAN_POINT('',(0.,-0.));
#3249 = VECTOR('',#3250,1.);
#3250 = DIRECTION('',(0.,1.));
#3251 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3252 = ORIENTED_EDGE('',*,*,#3253,.F.);
#3253 = EDGE_CURVE('',#3254,#3226,#3256,.T.);
#3254 = VERTEX_POINT('',#3255);
#3255 = CARTESIAN_POINT('',(50.839241118694,124.86383555301,9.995251));
#3256 = SURFACE_CURVE('',#3257,(#3262,#3269),.PCURVE_S1.);
#3257 = CIRCLE('',#3258,4.316451884327);
#3258 = AXIS2_PLACEMENT_3D('',#3259,#3260,#3261);
#3259 = CARTESIAN_POINT('',(51.706341,129.092298,9.995251));
#3260 = DIRECTION('',(0.,0.,1.));
#3261 = DIRECTION('',(1.,0.,0.));
#3262 = PCURVE('',#3234,#3263);
#3263 = DEFINITIONAL_REPRESENTATION('',(#3264),#3268);
#3264 = LINE('',#3265,#3266);
#3265 = CARTESIAN_POINT('',(0.,7.));
#3266 = VECTOR('',#3267,1.);
#3267 = DIRECTION('',(1.,0.));
#3268 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3269 = PCURVE('',#3270,#3275);
#3270 = PLANE('',#3271);
#3271 = AXIS2_PLACEMENT_3D('',#3272,#3273,#3274);
#3272 = CARTESIAN_POINT('',(51.706341,129.092298,9.995251));
#3273 = DIRECTION('',(0.,0.,1.));
#3274 = DIRECTION('',(1.,0.,0.));
#3275 = DEFINITIONAL_REPRESENTATION('',(#3276),#3280);
#3276 = CIRCLE('',#3277,4.316451884327);
#3277 = AXIS2_PLACEMENT_2D('',#3278,#3279);
#3278 = CARTESIAN_POINT('',(0.,0.));
#3279 = DIRECTION('',(1.,0.));
#3280 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3281 = ORIENTED_EDGE('',*,*,#3282,.F.);
#3282 = EDGE_CURVE('',#3283,#3254,#3285,.T.);
#3283 = VERTEX_POINT('',#3284);
#3284 = CARTESIAN_POINT('',(50.449147,131.893892,7.837620801505));
#3285 = SURFACE_CURVE('',#3286,(#3291,#3325),.PCURVE_S1.);
#3286 = HYPERBOLA('',#3287,1.905329967866,1.100042769842);
#3287 = AXIS2_PLACEMENT_3D('',#3288,#3289,#3290);
#3288 = CARTESIAN_POINT('',(50.607987889865,129.03135096611,
    2.518937027919));
#3289 = DIRECTION('',(0.998464005442,5.540424023454E-02,0.));
#3290 = DIRECTION('',(-0.,0.,1.));
#3291 = PCURVE('',#3234,#3292);
#3292 = DEFINITIONAL_REPRESENTATION('',(#3293),#3324);
#3293 = B_SPLINE_CURVE_WITH_KNOTS('',8,(#3294,#3295,#3296,#3297,#3298,
    #3299,#3300,#3301,#3302,#3303,#3304,#3305,#3306,#3307,#3308,#3309,
    #3310,#3311,#3312,#3313,#3314,#3315,#3316,#3317,#3318,#3319,#3320,
    #3321,#3322,#3323),.UNSPECIFIED.,.F.,.F.,(9,7,7,7,9),(
    -1.972483051607,-0.968466160771,-0.215453492644,0.914065509546,
    2.043584511737),.UNSPECIFIED.);
#3294 = CARTESIAN_POINT('',(1.902676814881,6.504448043814));
#3295 = CARTESIAN_POINT('',(1.936931375236,5.661612304121));
#3296 = CARTESIAN_POINT('',(1.975912383322,4.944436369548));
#3297 = CARTESIAN_POINT('',(2.020320500835,4.332692100164));
#3298 = CARTESIAN_POINT('',(2.070940989991,3.810370052149));
#3299 = CARTESIAN_POINT('',(2.128635694936,3.364671148761));
#3300 = CARTESIAN_POINT('',(2.194290539112,2.985323742375));
#3301 = CARTESIAN_POINT('',(2.26864591414,2.664124948525));
#3302 = CARTESIAN_POINT('',(2.41440590209,2.192465271092));
#3303 = CARTESIAN_POINT('',(2.481901272502,2.019394898011));
#3304 = CARTESIAN_POINT('',(2.554523418434,1.872665036393));
#3305 = CARTESIAN_POINT('',(2.632217489442,1.750095966695));
#3306 = CARTESIAN_POINT('',(2.714697151366,1.649980407088));
#3307 = CARTESIAN_POINT('',(2.801352576169,1.571031252215));
#3308 = CARTESIAN_POINT('',(2.891236228932,1.512349780094));
#3309 = CARTESIAN_POINT('',(3.121194650158,1.415000921457));
#3310 = CARTESIAN_POINT('',(3.263893413341,1.401010962491));
#3311 = CARTESIAN_POINT('',(3.407502284408,1.429666071387));
#3312 = CARTESIAN_POINT('',(3.547267331516,1.501081075033));
#3313 = CARTESIAN_POINT('',(3.678404153148,1.617146393677));
#3314 = CARTESIAN_POINT('',(3.798836353984,1.781616454804));
#3315 = CARTESIAN_POINT('',(3.907743736194,2.000372872439));
#3316 = CARTESIAN_POINT('',(4.102802220054,2.56356621898));
#3317 = CARTESIAN_POINT('',(4.188953067548,2.908002828435));
#3318 = CARTESIAN_POINT('',(4.263853993802,3.323835456944));
#3319 = CARTESIAN_POINT('',(4.328566364437,3.822284233872));
#3320 = CARTESIAN_POINT('',(4.384388712654,4.417803282879));
#3321 = CARTESIAN_POINT('',(4.432552139025,5.128876775506));
#3322 = CARTESIAN_POINT('',(4.47414814674,5.979274455643));
#3323 = CARTESIAN_POINT('',(4.510130223138,7.));
#3324 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3325 = PCURVE('',#3326,#3331);
#3326 = PLANE('',#3327);
#3327 = AXIS2_PLACEMENT_3D('',#3328,#3329,#3330);
#3328 = CARTESIAN_POINT('',(51.008936,121.80569,28.));
#3329 = DIRECTION('',(0.998464005442,5.540424023454E-02,0.));
#3330 = DIRECTION('',(-5.540424023454E-02,0.998464005442,0.));
#3331 = DEFINITIONAL_REPRESENTATION('',(#3332),#3336);
#3332 = ( BOUNDED_CURVE() B_SPLINE_CURVE(2,(#3333,#3334,#3335),
.UNSPECIFIED.,.F.,.F.) B_SPLINE_CURVE_WITH_KNOTS((3,3),(-1.972483051607,
2.043584511737),.PIECEWISE_BEZIER_KNOTS.) CURVE() 
GEOMETRIC_REPRESENTATION_ITEM() RATIONAL_B_SPLINE_CURVE((1.,
3.791454818993,1.)) REPRESENTATION_ITEM('') );
#3333 = CARTESIAN_POINT('',(11.114093386955,-18.50030095618));
#3334 = CARTESIAN_POINT('',(7.22645984708,-24.97821269018));
#3335 = CARTESIAN_POINT('',(3.062850074061,-18.004749));
#3336 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3337 = ORIENTED_EDGE('',*,*,#3338,.F.);
#3338 = EDGE_CURVE('',#3339,#3283,#3341,.T.);
#3339 = VERTEX_POINT('',#3340);
#3340 = CARTESIAN_POINT('',(48.346459465914,131.80208635827,9.995251));
#3341 = SURFACE_CURVE('',#3342,(#3347,#3359),.PCURVE_S1.);
#3342 = HYPERBOLA('',#3343,4.942867199707,2.853765708319);
#3343 = AXIS2_PLACEMENT_3D('',#3344,#3345,#3346);
#3344 = CARTESIAN_POINT('',(51.581861053228,131.94334753672,
    2.518937027919));
#3345 = DIRECTION('',(-4.361953975718E-02,0.999048214928,0.));
#3346 = DIRECTION('',(0.,0.,1.));
#3347 = PCURVE('',#3234,#3348);
#3348 = DEFINITIONAL_REPRESENTATION('',(#3349),#3358);
#3349 = B_SPLINE_CURVE_WITH_KNOTS('',7,(#3350,#3351,#3352,#3353,#3354,
    #3355,#3356,#3357),.UNSPECIFIED.,.F.,.F.,(8,8),(-0.973561676338,
    -0.316645052248),.PIECEWISE_BEZIER_KNOTS.);
#3350 = CARTESIAN_POINT('',(2.46289292773,7.));
#3351 = CARTESIAN_POINT('',(2.400848379989,6.47360156943));
#3352 = CARTESIAN_POINT('',(2.333707271595,6.02402037909));
#3353 = CARTESIAN_POINT('',(2.261357258779,5.643684438515));
#3354 = CARTESIAN_POINT('',(2.183865085272,5.326678914029));
#3355 = CARTESIAN_POINT('',(2.101573725469,5.068475137725));
#3356 = CARTESIAN_POINT('',(2.015241719518,4.865773829448));
#3357 = CARTESIAN_POINT('',(1.925912296825,4.71642661869));
#3358 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3359 = PCURVE('',#3360,#3365);
#3360 = PLANE('',#3361);
#3361 = AXIS2_PLACEMENT_3D('',#3362,#3363,#3364);
#3362 = CARTESIAN_POINT('',(50.449147,131.893892,28.));
#3363 = DIRECTION('',(-4.361953975718E-02,0.999048214928,0.));
#3364 = DIRECTION('',(-0.999048214928,-4.361953975718E-02,0.));
#3365 = DEFINITIONAL_REPRESENTATION('',(#3366),#3370);
#3366 = ( BOUNDED_CURVE() B_SPLINE_CURVE(2,(#3367,#3368,#3369),
.UNSPECIFIED.,.F.,.F.) B_SPLINE_CURVE_WITH_KNOTS((3,3),(-0.973561676338,
-0.316645052248),.PIECEWISE_BEZIER_KNOTS.) CURVE() 
GEOMETRIC_REPRESENTATION_ITEM() RATIONAL_B_SPLINE_CURVE((1.,
1.054429143067,1.)) REPRESENTATION_ITEM('') );
#3367 = CARTESIAN_POINT('',(2.10469074732,-18.004749));
#3368 = CARTESIAN_POINT('',(0.735793546625,-19.78362757574));
#3369 = CARTESIAN_POINT('',(-0.214986220675,-20.28832238131));
#3370 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3371 = ORIENTED_EDGE('',*,*,#3372,.F.);
#3372 = EDGE_CURVE('',#3226,#3339,#3373,.T.);
#3373 = SURFACE_CURVE('',#3374,(#3379,#3386),.PCURVE_S1.);
#3374 = CIRCLE('',#3375,4.316451884327);
#3375 = AXIS2_PLACEMENT_3D('',#3376,#3377,#3378);
#3376 = CARTESIAN_POINT('',(51.706341,129.092298,9.995251));
#3377 = DIRECTION('',(0.,0.,1.));
#3378 = DIRECTION('',(1.,0.,0.));
#3379 = PCURVE('',#3234,#3380);
#3380 = DEFINITIONAL_REPRESENTATION('',(#3381),#3385);
#3381 = LINE('',#3382,#3383);
#3382 = CARTESIAN_POINT('',(0.,7.));
#3383 = VECTOR('',#3384,1.);
#3384 = DIRECTION('',(1.,0.));
#3385 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3386 = PCURVE('',#3270,#3387);
#3387 = DEFINITIONAL_REPRESENTATION('',(#3388),#3392);
#3388 = CIRCLE('',#3389,4.316451884327);
#3389 = AXIS2_PLACEMENT_2D('',#3390,#3391);
#3390 = CARTESIAN_POINT('',(0.,0.));
#3391 = DIRECTION('',(1.,0.));
#3392 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3393 = ORIENTED_EDGE('',*,*,#3223,.F.);
#3394 = ORIENTED_EDGE('',*,*,#3395,.T.);
#3395 = EDGE_CURVE('',#3224,#3224,#3396,.T.);
#3396 = SURFACE_CURVE('',#3397,(#3402,#3409),.PCURVE_S1.);
#3397 = CIRCLE('',#3398,0.275);
#3398 = AXIS2_PLACEMENT_3D('',#3399,#3400,#3401);
#3399 = CARTESIAN_POINT('',(51.706341,129.092298,2.995251));
#3400 = DIRECTION('',(0.,0.,1.));
#3401 = DIRECTION('',(1.,0.,0.));
#3402 = PCURVE('',#3234,#3403);
#3403 = DEFINITIONAL_REPRESENTATION('',(#3404),#3408);
#3404 = LINE('',#3405,#3406);
#3405 = CARTESIAN_POINT('',(0.,0.));
#3406 = VECTOR('',#3407,1.);
#3407 = DIRECTION('',(1.,0.));
#3408 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3409 = PCURVE('',#3410,#3415);
#3410 = CYLINDRICAL_SURFACE('',#3411,0.275);
#3411 = AXIS2_PLACEMENT_3D('',#3412,#3413,#3414);
#3412 = CARTESIAN_POINT('',(51.706341,129.092298,2.995251));
#3413 = DIRECTION('',(0.,0.,1.));
#3414 = DIRECTION('',(1.,0.,0.));
#3415 = DEFINITIONAL_REPRESENTATION('',(#3416),#3419);
#3416 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#3417,#3418),.UNSPECIFIED.,.F.,
  .F.,(2,2),(0.,6.28318530718),.PIECEWISE_BEZIER_KNOTS.);
#3417 = CARTESIAN_POINT('',(0.,0.));
#3418 = CARTESIAN_POINT('',(6.28318530718,0.));
#3419 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3420 = ADVANCED_FACE('',(#3421),#3410,.F.);
#3421 = FACE_BOUND('',#3422,.F.);
#3422 = EDGE_LOOP('',(#3423,#3447,#3468,#3469));
#3423 = ORIENTED_EDGE('',*,*,#3424,.F.);
#3424 = EDGE_CURVE('',#3425,#3425,#3427,.T.);
#3425 = VERTEX_POINT('',#3426);
#3426 = CARTESIAN_POINT('',(51.981341,129.092298,9.995251));
#3427 = SURFACE_CURVE('',#3428,(#3433,#3440),.PCURVE_S1.);
#3428 = CIRCLE('',#3429,0.275);
#3429 = AXIS2_PLACEMENT_3D('',#3430,#3431,#3432);
#3430 = CARTESIAN_POINT('',(51.706341,129.092298,9.995251));
#3431 = DIRECTION('',(0.,0.,1.));
#3432 = DIRECTION('',(1.,0.,0.));
#3433 = PCURVE('',#3410,#3434);
#3434 = DEFINITIONAL_REPRESENTATION('',(#3435),#3439);
#3435 = LINE('',#3436,#3437);
#3436 = CARTESIAN_POINT('',(0.,7.));
#3437 = VECTOR('',#3438,1.);
#3438 = DIRECTION('',(1.,0.));
#3439 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3440 = PCURVE('',#3270,#3441);
#3441 = DEFINITIONAL_REPRESENTATION('',(#3442),#3446);
#3442 = CIRCLE('',#3443,0.275);
#3443 = AXIS2_PLACEMENT_2D('',#3444,#3445);
#3444 = CARTESIAN_POINT('',(0.,0.));
#3445 = DIRECTION('',(1.,0.));
#3446 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3447 = ORIENTED_EDGE('',*,*,#3448,.F.);
#3448 = EDGE_CURVE('',#3224,#3425,#3449,.T.);
#3449 = SEAM_CURVE('',#3450,(#3454,#3461),.PCURVE_S1.);
#3450 = LINE('',#3451,#3452);
#3451 = CARTESIAN_POINT('',(51.981341,129.092298,2.995251));
#3452 = VECTOR('',#3453,1.);
#3453 = DIRECTION('',(0.,0.,1.));
#3454 = PCURVE('',#3410,#3455);
#3455 = DEFINITIONAL_REPRESENTATION('',(#3456),#3460);
#3456 = LINE('',#3457,#3458);
#3457 = CARTESIAN_POINT('',(6.28318530718,-0.));
#3458 = VECTOR('',#3459,1.);
#3459 = DIRECTION('',(0.,1.));
#3460 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3461 = PCURVE('',#3410,#3462);
#3462 = DEFINITIONAL_REPRESENTATION('',(#3463),#3467);
#3463 = LINE('',#3464,#3465);
#3464 = CARTESIAN_POINT('',(0.,-0.));
#3465 = VECTOR('',#3466,1.);
#3466 = DIRECTION('',(0.,1.));
#3467 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3468 = ORIENTED_EDGE('',*,*,#3395,.T.);
#3469 = ORIENTED_EDGE('',*,*,#3448,.T.);
#3470 = ADVANCED_FACE('',(#3471,#3515),#3270,.T.);
#3471 = FACE_BOUND('',#3472,.T.);
#3472 = EDGE_LOOP('',(#3473,#3474,#3475,#3496));
#3473 = ORIENTED_EDGE('',*,*,#3253,.T.);
#3474 = ORIENTED_EDGE('',*,*,#3372,.T.);
#3475 = ORIENTED_EDGE('',*,*,#3476,.F.);
#3476 = EDGE_CURVE('',#3477,#3339,#3479,.T.);
#3477 = VERTEX_POINT('',#3478);
#3478 = CARTESIAN_POINT('',(50.449147,131.893892,9.995251));
#3479 = SURFACE_CURVE('',#3480,(#3484,#3490),.PCURVE_S1.);
#3480 = LINE('',#3481,#3482);
#3481 = CARTESIAN_POINT('',(51.015504026614,131.91861976836,9.995251));
#3482 = VECTOR('',#3483,1.);
#3483 = DIRECTION('',(-0.999048214928,-4.361953975718E-02,0.));
#3484 = PCURVE('',#3270,#3485);
#3485 = DEFINITIONAL_REPRESENTATION('',(#3486),#3489);
#3486 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#3487,#3488),.UNSPECIFIED.,.F.,
  .F.,(2,2),(0.566896590326,2.716758797081),.PIECEWISE_BEZIER_KNOTS.);
#3487 = CARTESIAN_POINT('',(-1.257194,2.801594));
#3488 = CARTESIAN_POINT('',(-3.40501,2.707818));
#3489 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3490 = PCURVE('',#3360,#3491);
#3491 = DEFINITIONAL_REPRESENTATION('',(#3492),#3495);
#3492 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#3493,#3494),.UNSPECIFIED.,.F.,
  .F.,(2,2),(0.566896590326,2.716758797081),.PIECEWISE_BEZIER_KNOTS.);
#3493 = CARTESIAN_POINT('',(0.,-18.004749));
#3494 = CARTESIAN_POINT('',(2.149862206755,-18.004749));
#3495 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3496 = ORIENTED_EDGE('',*,*,#3497,.F.);
#3497 = EDGE_CURVE('',#3254,#3477,#3498,.T.);
#3498 = SURFACE_CURVE('',#3499,(#3503,#3509),.PCURVE_S1.);
#3499 = LINE('',#3500,#3501);
#3500 = CARTESIAN_POINT('',(50.808461944933,125.41852048305,9.995251));
#3501 = VECTOR('',#3502,1.);
#3502 = DIRECTION('',(-5.540424023454E-02,0.998464005442,0.));
#3503 = PCURVE('',#3270,#3504);
#3504 = DEFINITIONAL_REPRESENTATION('',(#3505),#3508);
#3505 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#3506,#3507),.UNSPECIFIED.,.F.,
  .F.,(2,2),(-0.643663030295,6.485332953065),.PIECEWISE_BEZIER_KNOTS.);
#3506 = CARTESIAN_POINT('',(-0.862217393907,-4.316451884327));
#3507 = CARTESIAN_POINT('',(-1.257194,2.801594));
#3508 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3509 = PCURVE('',#3326,#3510);
#3510 = DEFINITIONAL_REPRESENTATION('',(#3511),#3514);
#3511 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#3512,#3513),.UNSPECIFIED.,.F.,
  .F.,(2,2),(-0.643663030295,6.485332953065),.PIECEWISE_BEZIER_KNOTS.);
#3512 = CARTESIAN_POINT('',(2.974725277509,-18.004749));
#3513 = CARTESIAN_POINT('',(10.103721260868,-18.004749));
#3514 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3515 = FACE_BOUND('',#3516,.T.);
#3516 = EDGE_LOOP('',(#3517));
#3517 = ORIENTED_EDGE('',*,*,#3424,.F.);
#3518 = ADVANCED_FACE('',(#3519),#3360,.F.);
#3519 = FACE_BOUND('',#3520,.F.);
#3520 = EDGE_LOOP('',(#3521,#3522,#3543));
#3521 = ORIENTED_EDGE('',*,*,#3476,.F.);
#3522 = ORIENTED_EDGE('',*,*,#3523,.T.);
#3523 = EDGE_CURVE('',#3477,#3283,#3524,.T.);
#3524 = SURFACE_CURVE('',#3525,(#3529,#3536),.PCURVE_S1.);
#3525 = LINE('',#3526,#3527);
#3526 = CARTESIAN_POINT('',(50.449147,131.893892,28.));
#3527 = VECTOR('',#3528,1.);
#3528 = DIRECTION('',(0.,0.,-1.));
#3529 = PCURVE('',#3360,#3530);
#3530 = DEFINITIONAL_REPRESENTATION('',(#3531),#3535);
#3531 = LINE('',#3532,#3533);
#3532 = CARTESIAN_POINT('',(0.,0.));
#3533 = VECTOR('',#3534,1.);
#3534 = DIRECTION('',(-0.,-1.));
#3535 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3536 = PCURVE('',#3326,#3537);
#3537 = DEFINITIONAL_REPRESENTATION('',(#3538),#3542);
#3538 = LINE('',#3539,#3540);
#3539 = CARTESIAN_POINT('',(10.103721260868,0.));
#3540 = VECTOR('',#3541,1.);
#3541 = DIRECTION('',(0.,-1.));
#3542 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3543 = ORIENTED_EDGE('',*,*,#3338,.F.);
#3544 = ADVANCED_FACE('',(#3545),#3326,.F.);
#3545 = FACE_BOUND('',#3546,.F.);
#3546 = EDGE_LOOP('',(#3547,#3548,#3549));
#3547 = ORIENTED_EDGE('',*,*,#3523,.F.);
#3548 = ORIENTED_EDGE('',*,*,#3497,.F.);
#3549 = ORIENTED_EDGE('',*,*,#3282,.F.);
#3550 = ( GEOMETRIC_REPRESENTATION_CONTEXT(3) 
GLOBAL_UNCERTAINTY_ASSIGNED_CONTEXT((#3554)) 
GLOBAL_UNIT_ASSIGNED_CONTEXT((#3551,#3552,#3553)) REPRESENTATION_CONTEXT
('Context #1','3D Context with UNIT and UNCERTAINTY') );
#3551 = ( LENGTH_UNIT() NAMED_UNIT(*) SI_UNIT(.MILLI.,.METRE.) );
#3552 = ( NAMED_UNIT(*) PLANE_ANGLE_UNIT() SI_UNIT($,.RADIAN.) );
#3553 = ( NAMED_UNIT(*) SI_UNIT($,.STERADIAN.) SOLID_ANGLE_UNIT() );
#3554 = UNCERTAINTY_MEASURE_WITH_UNIT(LENGTH_MEASURE(1.E-07),#3551,
  'distance_accuracy_value','confusion accuracy');
#3555 = CONTEXT_DEPENDENT_SHAPE_REPRESENTATION(#3556,#3558);
#3556 = ( REPRESENTATION_RELATIONSHIP('','',#3216,#10) 
REPRESENTATION_RELATIONSHIP_WITH_TRANSFORMATION(#3557) 
SHAPE_REPRESENTATION_RELATIONSHIP() );
#3557 = ITEM_DEFINED_TRANSFORMATION('','',#11,#55);
#3558 = PRODUCT_DEFINITION_SHAPE('Placement','Placement of an item',
  #3559);
#3559 = NEXT_ASSEMBLY_USAGE_OCCURRENCE('69','=>[0:1:1:12]','',#5,#3211,$
  );
#3560 = PRODUCT_RELATED_PRODUCT_CATEGORY('part',$,(#3213));
#3561 = SHAPE_DEFINITION_REPRESENTATION(#3562,#3568);
#3562 = PRODUCT_DEFINITION_SHAPE('','',#3563);
#3563 = PRODUCT_DEFINITION('design','',#3564,#3567);
#3564 = PRODUCT_DEFINITION_FORMATION('','',#3565);
#3565 = PRODUCT('PC1-P4_Part3','PC1-P4_Part3','',(#3566));
#3566 = PRODUCT_CONTEXT('',#2,'mechanical');
#3567 = PRODUCT_DEFINITION_CONTEXT('part definition',#2,'design');
#3568 = ADVANCED_BREP_SHAPE_REPRESENTATION('',(#11,#3569),#3899);
#3569 = MANIFOLD_SOLID_BREP('',#3570);
#3570 = CLOSED_SHELL('',(#3571,#3693,#3768,#3815,#3865,#3872));
#3571 = ADVANCED_FACE('',(#3572),#3587,.T.);
#3572 = FACE_BOUND('',#3573,.T.);
#3573 = EDGE_LOOP('',(#3574,#3610,#3636,#3669));
#3574 = ORIENTED_EDGE('',*,*,#3575,.T.);
#3575 = EDGE_CURVE('',#3576,#3578,#3580,.T.);
#3576 = VERTEX_POINT('',#3577);
#3577 = CARTESIAN_POINT('',(50.839241118694,124.86383555301,9.995251));
#3578 = VERTEX_POINT('',#3579);
#3579 = CARTESIAN_POINT('',(48.346459465914,131.80208635827,9.995251));
#3580 = SURFACE_CURVE('',#3581,(#3586,#3598),.PCURVE_S1.);
#3581 = CIRCLE('',#3582,4.316451884327);
#3582 = AXIS2_PLACEMENT_3D('',#3583,#3584,#3585);
#3583 = CARTESIAN_POINT('',(51.706341,129.092298,9.995251));
#3584 = DIRECTION('',(0.,0.,1.));
#3585 = DIRECTION('',(-0.200882554594,-0.979615332291,0.));
#3586 = PCURVE('',#3587,#3592);
#3587 = CYLINDRICAL_SURFACE('',#3588,4.316451884327);
#3588 = AXIS2_PLACEMENT_3D('',#3589,#3590,#3591);
#3589 = CARTESIAN_POINT('',(51.706341,129.092298,9.995251));
#3590 = DIRECTION('',(0.,0.,1.));
#3591 = DIRECTION('',(1.,0.,0.));
#3592 = DEFINITIONAL_REPRESENTATION('',(#3593),#3597);
#3593 = LINE('',#3594,#3595);
#3594 = CARTESIAN_POINT('',(4.510130223138,0.));
#3595 = VECTOR('',#3596,1.);
#3596 = DIRECTION('',(1.,0.));
#3597 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3598 = PCURVE('',#3599,#3604);
#3599 = PLANE('',#3600);
#3600 = AXIS2_PLACEMENT_3D('',#3601,#3602,#3603);
#3601 = CARTESIAN_POINT('',(51.706341,129.092298,9.995251));
#3602 = DIRECTION('',(0.,0.,1.));
#3603 = DIRECTION('',(1.,0.,0.));
#3604 = DEFINITIONAL_REPRESENTATION('',(#3605),#3609);
#3605 = CIRCLE('',#3606,4.316451884327);
#3606 = AXIS2_PLACEMENT_2D('',#3607,#3608);
#3607 = CARTESIAN_POINT('',(0.,0.));
#3608 = DIRECTION('',(-0.200882554594,-0.979615332291));
#3609 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3610 = ORIENTED_EDGE('',*,*,#3611,.T.);
#3611 = EDGE_CURVE('',#3578,#3612,#3614,.T.);
#3612 = VERTEX_POINT('',#3613);
#3613 = CARTESIAN_POINT('',(48.346459465914,131.80208635827,18.));
#3614 = SURFACE_CURVE('',#3615,(#3619,#3625),.PCURVE_S1.);
#3615 = LINE('',#3616,#3617);
#3616 = CARTESIAN_POINT('',(48.346459465914,131.80208635827,9.995251));
#3617 = VECTOR('',#3618,1.);
#3618 = DIRECTION('',(0.,0.,1.));
#3619 = PCURVE('',#3587,#3620);
#3620 = DEFINITIONAL_REPRESENTATION('',(#3621),#3624);
#3621 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#3622,#3623),.UNSPECIFIED.,.F.,
  .F.,(2,2),(0.,8.004749),.PIECEWISE_BEZIER_KNOTS.);
#3622 = CARTESIAN_POINT('',(8.74607823491,0.));
#3623 = CARTESIAN_POINT('',(8.74607823491,8.004749));
#3624 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3625 = PCURVE('',#3626,#3631);
#3626 = PLANE('',#3627);
#3627 = AXIS2_PLACEMENT_3D('',#3628,#3629,#3630);
#3628 = CARTESIAN_POINT('',(50.449147,131.893892,28.));
#3629 = DIRECTION('',(-4.361953975718E-02,0.999048214928,0.));
#3630 = DIRECTION('',(-0.999048214928,-4.361953975718E-02,0.));
#3631 = DEFINITIONAL_REPRESENTATION('',(#3632),#3635);
#3632 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#3633,#3634),.UNSPECIFIED.,.F.,
  .F.,(2,2),(-5.999999999062E-07,8.0047496),.PIECEWISE_BEZIER_KNOTS.);
#3633 = CARTESIAN_POINT('',(2.10469074732,-18.0047496));
#3634 = CARTESIAN_POINT('',(2.10469074732,-9.9999994));
#3635 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3636 = ORIENTED_EDGE('',*,*,#3637,.T.);
#3637 = EDGE_CURVE('',#3612,#3638,#3640,.T.);
#3638 = VERTEX_POINT('',#3639);
#3639 = CARTESIAN_POINT('',(50.839241118694,124.86383555301,18.));
#3640 = SURFACE_CURVE('',#3641,(#3646,#3653),.PCURVE_S1.);
#3641 = CIRCLE('',#3642,4.316451884327);
#3642 = AXIS2_PLACEMENT_3D('',#3643,#3644,#3645);
#3643 = CARTESIAN_POINT('',(51.706341,129.092298,18.));
#3644 = DIRECTION('',(0.,0.,-1.));
#3645 = DIRECTION('',(-0.778389664503,0.627781435051,0.));
#3646 = PCURVE('',#3587,#3647);
#3647 = DEFINITIONAL_REPRESENTATION('',(#3648),#3652);
#3648 = LINE('',#3649,#3650);
#3649 = CARTESIAN_POINT('',(8.74607823491,8.004749));
#3650 = VECTOR('',#3651,1.);
#3651 = DIRECTION('',(-1.,-0.));
#3652 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3653 = PCURVE('',#3654,#3659);
#3654 = PLANE('',#3655);
#3655 = AXIS2_PLACEMENT_3D('',#3656,#3657,#3658);
#3656 = CARTESIAN_POINT('',(51.706341,129.092298,18.));
#3657 = DIRECTION('',(0.,0.,1.));
#3658 = DIRECTION('',(1.,0.,0.));
#3659 = DEFINITIONAL_REPRESENTATION('',(#3660),#3668);
#3660 = ( BOUNDED_CURVE() B_SPLINE_CURVE(2,(#3661,#3662,#3663,#3664,
#3665,#3666,#3667),.UNSPECIFIED.,.T.,.F.) B_SPLINE_CURVE_WITH_KNOTS((1,2
    ,2,2,2,1),(-2.094395102393,0.,2.094395102393,4.188790204786,
6.28318530718,8.377580409573),.UNSPECIFIED.) CURVE() 
GEOMETRIC_REPRESENTATION_ITEM() RATIONAL_B_SPLINE_CURVE((1.,0.5,1.,0.5,
1.,0.5,1.)) REPRESENTATION_ITEM('') );
#3661 = CARTESIAN_POINT('',(-3.359881534086,2.709788358271));
#3662 = CARTESIAN_POINT('',(1.333609580197,8.529273882721));
#3663 = CARTESIAN_POINT('',(4.026686324185,1.55484858309));
#3664 = CARTESIAN_POINT('',(6.719763068173,-5.419576716541));
#3665 = CARTESIAN_POINT('',(-0.666804790099,-4.26463694136));
#3666 = CARTESIAN_POINT('',(-8.05337264837,-3.109697166179));
#3667 = CARTESIAN_POINT('',(-3.359881534086,2.709788358271));
#3668 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3669 = ORIENTED_EDGE('',*,*,#3670,.F.);
#3670 = EDGE_CURVE('',#3576,#3638,#3671,.T.);
#3671 = SURFACE_CURVE('',#3672,(#3676,#3682),.PCURVE_S1.);
#3672 = LINE('',#3673,#3674);
#3673 = CARTESIAN_POINT('',(50.839241118694,124.86383555301,9.995251));
#3674 = VECTOR('',#3675,1.);
#3675 = DIRECTION('',(0.,0.,1.));
#3676 = PCURVE('',#3587,#3677);
#3677 = DEFINITIONAL_REPRESENTATION('',(#3678),#3681);
#3678 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#3679,#3680),.UNSPECIFIED.,.F.,
  .F.,(2,2),(0.,8.004749),.PIECEWISE_BEZIER_KNOTS.);
#3679 = CARTESIAN_POINT('',(4.510130223138,0.));
#3680 = CARTESIAN_POINT('',(4.510130223138,8.004749));
#3681 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3682 = PCURVE('',#3683,#3688);
#3683 = PLANE('',#3684);
#3684 = AXIS2_PLACEMENT_3D('',#3685,#3686,#3687);
#3685 = CARTESIAN_POINT('',(51.008936,121.80569,28.));
#3686 = DIRECTION('',(0.998464005442,5.540424023454E-02,0.));
#3687 = DIRECTION('',(-5.540424023454E-02,0.998464005442,0.));
#3688 = DEFINITIONAL_REPRESENTATION('',(#3689),#3692);
#3689 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#3690,#3691),.UNSPECIFIED.,.F.,
  .F.,(2,2),(-5.999999999062E-07,8.0047496),.PIECEWISE_BEZIER_KNOTS.);
#3690 = CARTESIAN_POINT('',(3.062850074061,-18.0047496));
#3691 = CARTESIAN_POINT('',(3.062850074061,-9.9999994));
#3692 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3693 = ADVANCED_FACE('',(#3694,#3737),#3599,.F.);
#3694 = FACE_BOUND('',#3695,.F.);
#3695 = EDGE_LOOP('',(#3696,#3697,#3718));
#3696 = ORIENTED_EDGE('',*,*,#3575,.T.);
#3697 = ORIENTED_EDGE('',*,*,#3698,.F.);
#3698 = EDGE_CURVE('',#3699,#3578,#3701,.T.);
#3699 = VERTEX_POINT('',#3700);
#3700 = CARTESIAN_POINT('',(50.449147,131.893892,9.995251));
#3701 = SURFACE_CURVE('',#3702,(#3706,#3712),.PCURVE_S1.);
#3702 = LINE('',#3703,#3704);
#3703 = CARTESIAN_POINT('',(51.015504026614,131.91861976836,9.995251));
#3704 = VECTOR('',#3705,1.);
#3705 = DIRECTION('',(-0.999048214928,-4.361953975718E-02,0.));
#3706 = PCURVE('',#3599,#3707);
#3707 = DEFINITIONAL_REPRESENTATION('',(#3708),#3711);
#3708 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#3709,#3710),.UNSPECIFIED.,.F.,
  .F.,(2,2),(0.566896590326,2.716758797081),.PIECEWISE_BEZIER_KNOTS.);
#3709 = CARTESIAN_POINT('',(-1.257194,2.801594));
#3710 = CARTESIAN_POINT('',(-3.40501,2.707818));
#3711 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3712 = PCURVE('',#3626,#3713);
#3713 = DEFINITIONAL_REPRESENTATION('',(#3714),#3717);
#3714 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#3715,#3716),.UNSPECIFIED.,.F.,
  .F.,(2,2),(0.566896590326,2.716758797081),.PIECEWISE_BEZIER_KNOTS.);
#3715 = CARTESIAN_POINT('',(0.,-18.004749));
#3716 = CARTESIAN_POINT('',(2.149862206755,-18.004749));
#3717 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3718 = ORIENTED_EDGE('',*,*,#3719,.F.);
#3719 = EDGE_CURVE('',#3576,#3699,#3720,.T.);
#3720 = SURFACE_CURVE('',#3721,(#3725,#3731),.PCURVE_S1.);
#3721 = LINE('',#3722,#3723);
#3722 = CARTESIAN_POINT('',(50.808461944933,125.41852048305,9.995251));
#3723 = VECTOR('',#3724,1.);
#3724 = DIRECTION('',(-5.540424023454E-02,0.998464005442,0.));
#3725 = PCURVE('',#3599,#3726);
#3726 = DEFINITIONAL_REPRESENTATION('',(#3727),#3730);
#3727 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#3728,#3729),.UNSPECIFIED.,.F.,
  .F.,(2,2),(-0.643663030295,6.485332953065),.PIECEWISE_BEZIER_KNOTS.);
#3728 = CARTESIAN_POINT('',(-0.862217393907,-4.316451884327));
#3729 = CARTESIAN_POINT('',(-1.257194,2.801594));
#3730 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3731 = PCURVE('',#3683,#3732);
#3732 = DEFINITIONAL_REPRESENTATION('',(#3733),#3736);
#3733 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#3734,#3735),.UNSPECIFIED.,.F.,
  .F.,(2,2),(-0.643663030295,6.485332953065),.PIECEWISE_BEZIER_KNOTS.);
#3734 = CARTESIAN_POINT('',(2.974725277509,-18.004749));
#3735 = CARTESIAN_POINT('',(10.103721260868,-18.004749));
#3736 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3737 = FACE_BOUND('',#3738,.F.);
#3738 = EDGE_LOOP('',(#3739));
#3739 = ORIENTED_EDGE('',*,*,#3740,.F.);
#3740 = EDGE_CURVE('',#3741,#3741,#3743,.T.);
#3741 = VERTEX_POINT('',#3742);
#3742 = CARTESIAN_POINT('',(51.981341,129.092298,9.995251));
#3743 = SURFACE_CURVE('',#3744,(#3749,#3756),.PCURVE_S1.);
#3744 = CIRCLE('',#3745,0.275);
#3745 = AXIS2_PLACEMENT_3D('',#3746,#3747,#3748);
#3746 = CARTESIAN_POINT('',(51.706341,129.092298,9.995251));
#3747 = DIRECTION('',(0.,0.,1.));
#3748 = DIRECTION('',(1.,0.,0.));
#3749 = PCURVE('',#3599,#3750);
#3750 = DEFINITIONAL_REPRESENTATION('',(#3751),#3755);
#3751 = CIRCLE('',#3752,0.275);
#3752 = AXIS2_PLACEMENT_2D('',#3753,#3754);
#3753 = CARTESIAN_POINT('',(0.,0.));
#3754 = DIRECTION('',(1.,0.));
#3755 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3756 = PCURVE('',#3757,#3762);
#3757 = CYLINDRICAL_SURFACE('',#3758,0.275);
#3758 = AXIS2_PLACEMENT_3D('',#3759,#3760,#3761);
#3759 = CARTESIAN_POINT('',(51.706341,129.092298,9.995251));
#3760 = DIRECTION('',(0.,0.,1.));
#3761 = DIRECTION('',(1.,0.,0.));
#3762 = DEFINITIONAL_REPRESENTATION('',(#3763),#3767);
#3763 = LINE('',#3764,#3765);
#3764 = CARTESIAN_POINT('',(0.,0.));
#3765 = VECTOR('',#3766,1.);
#3766 = DIRECTION('',(1.,0.));
#3767 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3768 = ADVANCED_FACE('',(#3769),#3683,.F.);
#3769 = FACE_BOUND('',#3770,.F.);
#3770 = EDGE_LOOP('',(#3771,#3794,#3813,#3814));
#3771 = ORIENTED_EDGE('',*,*,#3772,.F.);
#3772 = EDGE_CURVE('',#3773,#3699,#3775,.T.);
#3773 = VERTEX_POINT('',#3774);
#3774 = CARTESIAN_POINT('',(50.449147,131.893892,18.));
#3775 = SURFACE_CURVE('',#3776,(#3780,#3787),.PCURVE_S1.);
#3776 = LINE('',#3777,#3778);
#3777 = CARTESIAN_POINT('',(50.449147,131.893892,28.));
#3778 = VECTOR('',#3779,1.);
#3779 = DIRECTION('',(0.,0.,-1.));
#3780 = PCURVE('',#3683,#3781);
#3781 = DEFINITIONAL_REPRESENTATION('',(#3782),#3786);
#3782 = LINE('',#3783,#3784);
#3783 = CARTESIAN_POINT('',(10.103721260868,0.));
#3784 = VECTOR('',#3785,1.);
#3785 = DIRECTION('',(0.,-1.));
#3786 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3787 = PCURVE('',#3626,#3788);
#3788 = DEFINITIONAL_REPRESENTATION('',(#3789),#3793);
#3789 = LINE('',#3790,#3791);
#3790 = CARTESIAN_POINT('',(0.,0.));
#3791 = VECTOR('',#3792,1.);
#3792 = DIRECTION('',(-0.,-1.));
#3793 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3794 = ORIENTED_EDGE('',*,*,#3795,.F.);
#3795 = EDGE_CURVE('',#3638,#3773,#3796,.T.);
#3796 = SURFACE_CURVE('',#3797,(#3801,#3807),.PCURVE_S1.);
#3797 = LINE('',#3798,#3799);
#3798 = CARTESIAN_POINT('',(50.808461944933,125.41852048305,18.));
#3799 = VECTOR('',#3800,1.);
#3800 = DIRECTION('',(-5.540424023454E-02,0.998464005442,0.));
#3801 = PCURVE('',#3683,#3802);
#3802 = DEFINITIONAL_REPRESENTATION('',(#3803),#3806);
#3803 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#3804,#3805),.UNSPECIFIED.,.F.,
  .F.,(2,2),(-0.643663030295,6.485332953065),.PIECEWISE_BEZIER_KNOTS.);
#3804 = CARTESIAN_POINT('',(2.974725277509,-10.));
#3805 = CARTESIAN_POINT('',(10.103721260868,-10.));
#3806 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3807 = PCURVE('',#3654,#3808);
#3808 = DEFINITIONAL_REPRESENTATION('',(#3809),#3812);
#3809 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#3810,#3811),.UNSPECIFIED.,.F.,
  .F.,(2,2),(-0.643663030295,6.485332953065),.PIECEWISE_BEZIER_KNOTS.);
#3810 = CARTESIAN_POINT('',(-0.862217393907,-4.316451884327));
#3811 = CARTESIAN_POINT('',(-1.257194,2.801594));
#3812 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3813 = ORIENTED_EDGE('',*,*,#3670,.F.);
#3814 = ORIENTED_EDGE('',*,*,#3719,.T.);
#3815 = ADVANCED_FACE('',(#3816,#3839),#3654,.T.);
#3816 = FACE_BOUND('',#3817,.T.);
#3817 = EDGE_LOOP('',(#3818,#3819,#3820));
#3818 = ORIENTED_EDGE('',*,*,#3795,.F.);
#3819 = ORIENTED_EDGE('',*,*,#3637,.F.);
#3820 = ORIENTED_EDGE('',*,*,#3821,.F.);
#3821 = EDGE_CURVE('',#3773,#3612,#3822,.T.);
#3822 = SURFACE_CURVE('',#3823,(#3827,#3833),.PCURVE_S1.);
#3823 = LINE('',#3824,#3825);
#3824 = CARTESIAN_POINT('',(51.015504026614,131.91861976836,18.));
#3825 = VECTOR('',#3826,1.);
#3826 = DIRECTION('',(-0.999048214928,-4.361953975718E-02,0.));
#3827 = PCURVE('',#3654,#3828);
#3828 = DEFINITIONAL_REPRESENTATION('',(#3829),#3832);
#3829 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#3830,#3831),.UNSPECIFIED.,.F.,
  .F.,(2,2),(0.566896590326,2.716758797081),.PIECEWISE_BEZIER_KNOTS.);
#3830 = CARTESIAN_POINT('',(-1.257194,2.801594));
#3831 = CARTESIAN_POINT('',(-3.40501,2.707818));
#3832 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3833 = PCURVE('',#3626,#3834);
#3834 = DEFINITIONAL_REPRESENTATION('',(#3835),#3838);
#3835 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#3836,#3837),.UNSPECIFIED.,.F.,
  .F.,(2,2),(0.566896590326,2.716758797081),.PIECEWISE_BEZIER_KNOTS.);
#3836 = CARTESIAN_POINT('',(0.,-10.));
#3837 = CARTESIAN_POINT('',(2.149862206755,-10.));
#3838 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3839 = FACE_BOUND('',#3840,.T.);
#3840 = EDGE_LOOP('',(#3841));
#3841 = ORIENTED_EDGE('',*,*,#3842,.F.);
#3842 = EDGE_CURVE('',#3843,#3843,#3845,.T.);
#3843 = VERTEX_POINT('',#3844);
#3844 = CARTESIAN_POINT('',(51.981341,129.092298,18.));
#3845 = SURFACE_CURVE('',#3846,(#3851,#3858),.PCURVE_S1.);
#3846 = CIRCLE('',#3847,0.275);
#3847 = AXIS2_PLACEMENT_3D('',#3848,#3849,#3850);
#3848 = CARTESIAN_POINT('',(51.706341,129.092298,18.));
#3849 = DIRECTION('',(0.,0.,1.));
#3850 = DIRECTION('',(1.,0.,0.));
#3851 = PCURVE('',#3654,#3852);
#3852 = DEFINITIONAL_REPRESENTATION('',(#3853),#3857);
#3853 = CIRCLE('',#3854,0.275);
#3854 = AXIS2_PLACEMENT_2D('',#3855,#3856);
#3855 = CARTESIAN_POINT('',(0.,0.));
#3856 = DIRECTION('',(1.,0.));
#3857 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3858 = PCURVE('',#3757,#3859);
#3859 = DEFINITIONAL_REPRESENTATION('',(#3860),#3864);
#3860 = LINE('',#3861,#3862);
#3861 = CARTESIAN_POINT('',(0.,8.004749));
#3862 = VECTOR('',#3863,1.);
#3863 = DIRECTION('',(1.,0.));
#3864 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3865 = ADVANCED_FACE('',(#3866),#3626,.F.);
#3866 = FACE_BOUND('',#3867,.F.);
#3867 = EDGE_LOOP('',(#3868,#3869,#3870,#3871));
#3868 = ORIENTED_EDGE('',*,*,#3821,.F.);
#3869 = ORIENTED_EDGE('',*,*,#3772,.T.);
#3870 = ORIENTED_EDGE('',*,*,#3698,.T.);
#3871 = ORIENTED_EDGE('',*,*,#3611,.T.);
#3872 = ADVANCED_FACE('',(#3873),#3757,.F.);
#3873 = FACE_BOUND('',#3874,.F.);
#3874 = EDGE_LOOP('',(#3875,#3876,#3897,#3898));
#3875 = ORIENTED_EDGE('',*,*,#3842,.F.);
#3876 = ORIENTED_EDGE('',*,*,#3877,.F.);
#3877 = EDGE_CURVE('',#3741,#3843,#3878,.T.);
#3878 = SEAM_CURVE('',#3879,(#3883,#3890),.PCURVE_S1.);
#3879 = LINE('',#3880,#3881);
#3880 = CARTESIAN_POINT('',(51.981341,129.092298,9.995251));
#3881 = VECTOR('',#3882,1.);
#3882 = DIRECTION('',(0.,0.,1.));
#3883 = PCURVE('',#3757,#3884);
#3884 = DEFINITIONAL_REPRESENTATION('',(#3885),#3889);
#3885 = LINE('',#3886,#3887);
#3886 = CARTESIAN_POINT('',(6.28318530718,-0.));
#3887 = VECTOR('',#3888,1.);
#3888 = DIRECTION('',(0.,1.));
#3889 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3890 = PCURVE('',#3757,#3891);
#3891 = DEFINITIONAL_REPRESENTATION('',(#3892),#3896);
#3892 = LINE('',#3893,#3894);
#3893 = CARTESIAN_POINT('',(0.,-0.));
#3894 = VECTOR('',#3895,1.);
#3895 = DIRECTION('',(0.,1.));
#3896 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3897 = ORIENTED_EDGE('',*,*,#3740,.T.);
#3898 = ORIENTED_EDGE('',*,*,#3877,.T.);
#3899 = ( GEOMETRIC_REPRESENTATION_CONTEXT(3) 
GLOBAL_UNCERTAINTY_ASSIGNED_CONTEXT((#3903)) 
GLOBAL_UNIT_ASSIGNED_CONTEXT((#3900,#3901,#3902)) REPRESENTATION_CONTEXT
('Context #1','3D Context with UNIT and UNCERTAINTY') );
#3900 = ( LENGTH_UNIT() NAMED_UNIT(*) SI_UNIT(.MILLI.,.METRE.) );
#3901 = ( NAMED_UNIT(*) PLANE_ANGLE_UNIT() SI_UNIT($,.RADIAN.) );
#3902 = ( NAMED_UNIT(*) SI_UNIT($,.STERADIAN.) SOLID_ANGLE_UNIT() );
#3903 = UNCERTAINTY_MEASURE_WITH_UNIT(LENGTH_MEASURE(1.E-07),#3900,
  'distance_accuracy_value','confusion accuracy');
#3904 = CONTEXT_DEPENDENT_SHAPE_REPRESENTATION(#3905,#3907);
#3905 = ( REPRESENTATION_RELATIONSHIP('','',#3568,#10) 
REPRESENTATION_RELATIONSHIP_WITH_TRANSFORMATION(#3906) 
SHAPE_REPRESENTATION_RELATIONSHIP() );
#3906 = ITEM_DEFINED_TRANSFORMATION('','',#11,#59);
#3907 = PRODUCT_DEFINITION_SHAPE('Placement','Placement of an item',
  #3908);
#3908 = NEXT_ASSEMBLY_USAGE_OCCURRENCE('70','=>[0:1:1:13]','',#5,#3563,$
  );
#3909 = PRODUCT_RELATED_PRODUCT_CATEGORY('part',$,(#3565));
#3910 = SHAPE_DEFINITION_REPRESENTATION(#3911,#3917);
#3911 = PRODUCT_DEFINITION_SHAPE('','',#3912);
#3912 = PRODUCT_DEFINITION('design','',#3913,#3916);
#3913 = PRODUCT_DEFINITION_FORMATION('','',#3914);
#3914 = PRODUCT('PC1-P5_Part1','PC1-P5_Part1','',(#3915));
#3915 = PRODUCT_CONTEXT('',#2,'mechanical');
#3916 = PRODUCT_DEFINITION_CONTEXT('part definition',#2,'design');
#3917 = ADVANCED_BREP_SHAPE_REPRESENTATION('',(#11,#3918),#4016);
#3918 = MANIFOLD_SOLID_BREP('',#3919);
#3919 = CLOSED_SHELL('',(#3920,#4008,#4012));
#3920 = ADVANCED_FACE('',(#3921),#3934,.T.);
#3921 = FACE_BOUND('',#3922,.T.);
#3922 = EDGE_LOOP('',(#3923,#3957,#3980,#4007));
#3923 = ORIENTED_EDGE('',*,*,#3924,.F.);
#3924 = EDGE_CURVE('',#3925,#3925,#3927,.T.);
#3925 = VERTEX_POINT('',#3926);
#3926 = CARTESIAN_POINT('',(0.275,-6.73555739531E-17,14.379426));
#3927 = SURFACE_CURVE('',#3928,(#3933,#3945),.PCURVE_S1.);
#3928 = CIRCLE('',#3929,0.275);
#3929 = AXIS2_PLACEMENT_3D('',#3930,#3931,#3932);
#3930 = CARTESIAN_POINT('',(0.,0.,14.379426));
#3931 = DIRECTION('',(0.,0.,1.));
#3932 = DIRECTION('',(1.,0.,-0.));
#3933 = PCURVE('',#3934,#3939);
#3934 = CYLINDRICAL_SURFACE('',#3935,0.275);
#3935 = AXIS2_PLACEMENT_3D('',#3936,#3937,#3938);
#3936 = CARTESIAN_POINT('',(0.,0.,0.));
#3937 = DIRECTION('',(0.,0.,1.));
#3938 = DIRECTION('',(1.,0.,-0.));
#3939 = DEFINITIONAL_REPRESENTATION('',(#3940),#3944);
#3940 = LINE('',#3941,#3942);
#3941 = CARTESIAN_POINT('',(0.,14.379426));
#3942 = VECTOR('',#3943,1.);
#3943 = DIRECTION('',(1.,0.));
#3944 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3945 = PCURVE('',#3946,#3951);
#3946 = PLANE('',#3947);
#3947 = AXIS2_PLACEMENT_3D('',#3948,#3949,#3950);
#3948 = CARTESIAN_POINT('',(0.,0.,14.379426));
#3949 = DIRECTION('',(0.,0.,1.));
#3950 = DIRECTION('',(1.,0.,-0.));
#3951 = DEFINITIONAL_REPRESENTATION('',(#3952),#3956);
#3952 = CIRCLE('',#3953,0.275);
#3953 = AXIS2_PLACEMENT_2D('',#3954,#3955);
#3954 = CARTESIAN_POINT('',(0.,0.));
#3955 = DIRECTION('',(1.,0.));
#3956 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3957 = ORIENTED_EDGE('',*,*,#3958,.F.);
#3958 = EDGE_CURVE('',#3959,#3925,#3961,.T.);
#3959 = VERTEX_POINT('',#3960);
#3960 = CARTESIAN_POINT('',(0.275,-6.73555739531E-17,0.));
#3961 = SEAM_CURVE('',#3962,(#3966,#3973),.PCURVE_S1.);
#3962 = LINE('',#3963,#3964);
#3963 = CARTESIAN_POINT('',(0.275,-6.73555739531E-17,0.));
#3964 = VECTOR('',#3965,1.);
#3965 = DIRECTION('',(0.,0.,1.));
#3966 = PCURVE('',#3934,#3967);
#3967 = DEFINITIONAL_REPRESENTATION('',(#3968),#3972);
#3968 = LINE('',#3969,#3970);
#3969 = CARTESIAN_POINT('',(6.28318530718,-0.));
#3970 = VECTOR('',#3971,1.);
#3971 = DIRECTION('',(0.,1.));
#3972 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3973 = PCURVE('',#3934,#3974);
#3974 = DEFINITIONAL_REPRESENTATION('',(#3975),#3979);
#3975 = LINE('',#3976,#3977);
#3976 = CARTESIAN_POINT('',(0.,-0.));
#3977 = VECTOR('',#3978,1.);
#3978 = DIRECTION('',(0.,1.));
#3979 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3980 = ORIENTED_EDGE('',*,*,#3981,.T.);
#3981 = EDGE_CURVE('',#3959,#3959,#3982,.T.);
#3982 = SURFACE_CURVE('',#3983,(#3988,#3995),.PCURVE_S1.);
#3983 = CIRCLE('',#3984,0.275);
#3984 = AXIS2_PLACEMENT_3D('',#3985,#3986,#3987);
#3985 = CARTESIAN_POINT('',(0.,0.,0.));
#3986 = DIRECTION('',(0.,0.,1.));
#3987 = DIRECTION('',(1.,0.,-0.));
#3988 = PCURVE('',#3934,#3989);
#3989 = DEFINITIONAL_REPRESENTATION('',(#3990),#3994);
#3990 = LINE('',#3991,#3992);
#3991 = CARTESIAN_POINT('',(0.,0.));
#3992 = VECTOR('',#3993,1.);
#3993 = DIRECTION('',(1.,0.));
#3994 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3995 = PCURVE('',#3996,#4001);
#3996 = PLANE('',#3997);
#3997 = AXIS2_PLACEMENT_3D('',#3998,#3999,#4000);
#3998 = CARTESIAN_POINT('',(0.,0.,0.));
#3999 = DIRECTION('',(0.,0.,1.));
#4000 = DIRECTION('',(1.,0.,-0.));
#4001 = DEFINITIONAL_REPRESENTATION('',(#4002),#4006);
#4002 = CIRCLE('',#4003,0.275);
#4003 = AXIS2_PLACEMENT_2D('',#4004,#4005);
#4004 = CARTESIAN_POINT('',(0.,0.));
#4005 = DIRECTION('',(1.,0.));
#4006 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#4007 = ORIENTED_EDGE('',*,*,#3958,.T.);
#4008 = ADVANCED_FACE('',(#4009),#3946,.T.);
#4009 = FACE_BOUND('',#4010,.T.);
#4010 = EDGE_LOOP('',(#4011));
#4011 = ORIENTED_EDGE('',*,*,#3924,.T.);
#4012 = ADVANCED_FACE('',(#4013),#3996,.F.);
#4013 = FACE_BOUND('',#4014,.T.);
#4014 = EDGE_LOOP('',(#4015));
#4015 = ORIENTED_EDGE('',*,*,#3981,.F.);
#4016 = ( GEOMETRIC_REPRESENTATION_CONTEXT(3) 
GLOBAL_UNCERTAINTY_ASSIGNED_CONTEXT((#4020)) 
GLOBAL_UNIT_ASSIGNED_CONTEXT((#4017,#4018,#4019)) REPRESENTATION_CONTEXT
('Context #1','3D Context with UNIT and UNCERTAINTY') );
#4017 = ( LENGTH_UNIT() NAMED_UNIT(*) SI_UNIT(.MILLI.,.METRE.) );
#4018 = ( NAMED_UNIT(*) PLANE_ANGLE_UNIT() SI_UNIT($,.RADIAN.) );
#4019 = ( NAMED_UNIT(*) SI_UNIT($,.STERADIAN.) SOLID_ANGLE_UNIT() );
#4020 = UNCERTAINTY_MEASURE_WITH_UNIT(LENGTH_MEASURE(1.E-07),#4017,
  'distance_accuracy_value','confusion accuracy');
#4021 = CONTEXT_DEPENDENT_SHAPE_REPRESENTATION(#4022,#4024);
#4022 = ( REPRESENTATION_RELATIONSHIP('','',#3917,#10) 
REPRESENTATION_RELATIONSHIP_WITH_TRANSFORMATION(#4023) 
SHAPE_REPRESENTATION_RELATIONSHIP() );
#4023 = ITEM_DEFINED_TRANSFORMATION('','',#11,#63);
#4024 = PRODUCT_DEFINITION_SHAPE('Placement','Placement of an item',
  #4025);
#4025 = NEXT_ASSEMBLY_USAGE_OCCURRENCE('71','=>[0:1:1:14]','',#5,#3912,$
  );
#4026 = PRODUCT_RELATED_PRODUCT_CATEGORY('part',$,(#3914));
#4027 = SHAPE_DEFINITION_REPRESENTATION(#4028,#4034);
#4028 = PRODUCT_DEFINITION_SHAPE('','',#4029);
#4029 = PRODUCT_DEFINITION('design','',#4030,#4033);
#4030 = PRODUCT_DEFINITION_FORMATION('','',#4031);
#4031 = PRODUCT('PC1-P5_Part2','PC1-P5_Part2','',(#4032));
#4032 = PRODUCT_CONTEXT('',#2,'mechanical');
#4033 = PRODUCT_DEFINITION_CONTEXT('part definition',#2,'design');
#4034 = ADVANCED_BREP_SHAPE_REPRESENTATION('',(#11,#4035),#4286);
#4035 = MANIFOLD_SOLID_BREP('',#4036);
#4036 = CLOSED_SHELL('',(#4037,#4204,#4254,#4281));
#4037 = ADVANCED_FACE('',(#4038),#4052,.T.);
#4038 = FACE_BOUND('',#4039,.T.);
#4039 = EDGE_LOOP('',(#4040,#4070,#4099,#4155,#4177,#4178));
#4040 = ORIENTED_EDGE('',*,*,#4041,.T.);
#4041 = EDGE_CURVE('',#4042,#4044,#4046,.T.);
#4042 = VERTEX_POINT('',#4043);
#4043 = CARTESIAN_POINT('',(52.072763,127.444732,3.620574));
#4044 = VERTEX_POINT('',#4045);
#4045 = CARTESIAN_POINT('',(56.114214884327,127.444732,10.620574));
#4046 = SEAM_CURVE('',#4047,(#4051,#4063),.PCURVE_S1.);
#4047 = LINE('',#4048,#4049);
#4048 = CARTESIAN_POINT('',(52.072763,127.444732,3.620574));
#4049 = VECTOR('',#4050,1.);
#4050 = DIRECTION('',(0.5,-1.224646799147E-16,0.866025403784));
#4051 = PCURVE('',#4052,#4057);
#4052 = CONICAL_SURFACE('',#4053,0.275,0.523598775598);
#4053 = AXIS2_PLACEMENT_3D('',#4054,#4055,#4056);
#4054 = CARTESIAN_POINT('',(51.797763,127.444732,3.620574));
#4055 = DIRECTION('',(0.,0.,1.));
#4056 = DIRECTION('',(1.,0.,0.));
#4057 = DEFINITIONAL_REPRESENTATION('',(#4058),#4062);
#4058 = LINE('',#4059,#4060);
#4059 = CARTESIAN_POINT('',(6.28318530718,-0.));
#4060 = VECTOR('',#4061,1.);
#4061 = DIRECTION('',(0.,1.));
#4062 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#4063 = PCURVE('',#4052,#4064);
#4064 = DEFINITIONAL_REPRESENTATION('',(#4065),#4069);
#4065 = LINE('',#4066,#4067);
#4066 = CARTESIAN_POINT('',(0.,-0.));
#4067 = VECTOR('',#4068,1.);
#4068 = DIRECTION('',(0.,1.));
#4069 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#4070 = ORIENTED_EDGE('',*,*,#4071,.F.);
#4071 = EDGE_CURVE('',#4072,#4044,#4074,.T.);
#4072 = VERTEX_POINT('',#4073);
#4073 = CARTESIAN_POINT('',(50.930663692252,123.2162694354,10.620574));
#4074 = SURFACE_CURVE('',#4075,(#4080,#4087),.PCURVE_S1.);
#4075 = CIRCLE('',#4076,4.316451884327);
#4076 = AXIS2_PLACEMENT_3D('',#4077,#4078,#4079);
#4077 = CARTESIAN_POINT('',(51.797763,127.444732,10.620574));
#4078 = DIRECTION('',(0.,0.,1.));
#4079 = DIRECTION('',(1.,0.,0.));
#4080 = PCURVE('',#4052,#4081);
#4081 = DEFINITIONAL_REPRESENTATION('',(#4082),#4086);
#4082 = LINE('',#4083,#4084);
#4083 = CARTESIAN_POINT('',(0.,7.));
#4084 = VECTOR('',#4085,1.);
#4085 = DIRECTION('',(1.,0.));
#4086 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#4087 = PCURVE('',#4088,#4093);
#4088 = PLANE('',#4089);
#4089 = AXIS2_PLACEMENT_3D('',#4090,#4091,#4092);
#4090 = CARTESIAN_POINT('',(51.797763,127.444732,10.620574));
#4091 = DIRECTION('',(0.,0.,1.));
#4092 = DIRECTION('',(1.,0.,0.));
#4093 = DEFINITIONAL_REPRESENTATION('',(#4094),#4098);
#4094 = CIRCLE('',#4095,4.316451884327);
#4095 = AXIS2_PLACEMENT_2D('',#4096,#4097);
#4096 = CARTESIAN_POINT('',(0.,0.));
#4097 = DIRECTION('',(1.,0.));
#4098 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#4099 = ORIENTED_EDGE('',*,*,#4100,.F.);
#4100 = EDGE_CURVE('',#4101,#4072,#4103,.T.);
#4101 = VERTEX_POINT('',#4102);
#4102 = CARTESIAN_POINT('',(50.46815721806,131.55130055955,10.620574));
#4103 = SURFACE_CURVE('',#4104,(#4109,#4143),.PCURVE_S1.);
#4104 = HYPERBOLA('',#4105,1.905328987247,1.100042203682);
#4105 = AXIS2_PLACEMENT_3D('',#4106,#4107,#4108);
#4106 = CARTESIAN_POINT('',(50.699410455156,127.38378499747,
    3.144260027919));
#4107 = DIRECTION('',(0.998464005442,5.540424023454E-02,0.));
#4108 = DIRECTION('',(-0.,0.,1.));
#4109 = PCURVE('',#4052,#4110);
#4110 = DEFINITIONAL_REPRESENTATION('',(#4111),#4142);
#4111 = B_SPLINE_CURVE_WITH_KNOTS('',8,(#4112,#4113,#4114,#4115,#4116,
    #4117,#4118,#4119,#4120,#4121,#4122,#4123,#4124,#4125,#4126,#4127,
    #4128,#4129,#4130,#4131,#4132,#4133,#4134,#4135,#4136,#4137,#4138,
    #4139,#4140,#4141),.UNSPECIFIED.,.F.,.F.,(9,7,7,7,9),(
    -2.043585043983,-1.021792521991,-0.255448130498,0.894068456742,
    2.043585043983),.UNSPECIFIED.);
#4112 = CARTESIAN_POINT('',(1.88392019749,7.));
#4113 = CARTESIAN_POINT('',(1.916470511026,6.076624861121));
#4114 = CARTESIAN_POINT('',(1.953615242995,5.292637417378));
#4115 = CARTESIAN_POINT('',(1.9960588345,4.625085004212));
#4116 = CARTESIAN_POINT('',(2.04460238642,4.055861434679));
#4117 = CARTESIAN_POINT('',(2.100135705889,3.570522499041));
#4118 = CARTESIAN_POINT('',(2.163602910691,3.157487695876));
#4119 = CARTESIAN_POINT('',(2.235848408752,2.807505683108));
#4120 = CARTESIAN_POINT('',(2.378301315839,2.292577378061));
#4121 = CARTESIAN_POINT('',(2.44450303977,2.103246108879));
#4122 = CARTESIAN_POINT('',(2.516015493445,1.942181378034));
#4123 = CARTESIAN_POINT('',(2.592853173862,1.806910973652));
#4124 = CARTESIAN_POINT('',(2.67481033825,1.695486265018));
#4125 = CARTESIAN_POINT('',(2.761349011278,1.606420616361));
#4126 = CARTESIAN_POINT('',(2.85156042425,1.538651135636));
#4127 = CARTESIAN_POINT('',(3.08343642873,1.420820098332));
#4128 = CARTESIAN_POINT('',(3.228274784649,1.39655489347));
#4129 = CARTESIAN_POINT('',(3.374991349167,1.416498793948));
#4130 = CARTESIAN_POINT('',(3.518625850975,1.480472799166));
#4131 = CARTESIAN_POINT('',(3.653831981199,1.590195911812));
#4132 = CARTESIAN_POINT('',(3.778180771564,1.749364150964));
#4133 = CARTESIAN_POINT('',(3.890667920948,1.963920532406));
#4134 = CARTESIAN_POINT('',(4.092051641345,2.521347075926));
#4135 = CARTESIAN_POINT('',(4.180948405499,2.864216873609));
#4136 = CARTESIAN_POINT('',(4.258098117641,3.280014433319));
#4137 = CARTESIAN_POINT('',(4.324599077022,3.78032732669));
#4138 = CARTESIAN_POINT('',(4.381824308015,4.380166479684));
#4139 = CARTESIAN_POINT('',(4.431077790375,5.098818298292));
#4140 = CARTESIAN_POINT('',(4.473511256052,5.961202968761));
#4141 = CARTESIAN_POINT('',(4.51013035878,7.));
#4142 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#4143 = PCURVE('',#4144,#4149);
#4144 = PLANE('',#4145);
#4145 = AXIS2_PLACEMENT_3D('',#4146,#4147,#4148);
#4146 = CARTESIAN_POINT('',(51.008936,121.80569,28.));
#4147 = DIRECTION('',(0.998464005442,5.540424023454E-02,0.));
#4148 = DIRECTION('',(-5.540424023454E-02,0.998464005442,0.));
#4149 = DEFINITIONAL_REPRESENTATION('',(#4150),#4154);
#4150 = ( BOUNDED_CURVE() B_SPLINE_CURVE(2,(#4151,#4152,#4153),
.UNSPECIFIED.,.F.,.F.) B_SPLINE_CURVE_WITH_KNOTS((3,3),(-2.043585043983,
2.043585043983),.PIECEWISE_BEZIER_KNOTS.) CURVE() 
GEOMETRIC_REPRESENTATION_ITEM() RATIONAL_B_SPLINE_CURVE((1.,
3.923896619494,1.)) REPRESENTATION_ITEM('') );
#4151 = CARTESIAN_POINT('',(9.760602792326,-17.379426));
#4152 = CARTESIAN_POINT('',(5.586676101566,-24.3701693335));
#4153 = CARTESIAN_POINT('',(1.412749410807,-17.379426));
#4154 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#4155 = ORIENTED_EDGE('',*,*,#4156,.F.);
#4156 = EDGE_CURVE('',#4044,#4101,#4157,.T.);
#4157 = SURFACE_CURVE('',#4158,(#4163,#4170),.PCURVE_S1.);
#4158 = CIRCLE('',#4159,4.316451884327);
#4159 = AXIS2_PLACEMENT_3D('',#4160,#4161,#4162);
#4160 = CARTESIAN_POINT('',(51.797763,127.444732,10.620574));
#4161 = DIRECTION('',(0.,0.,1.));
#4162 = DIRECTION('',(1.,0.,0.));
#4163 = PCURVE('',#4052,#4164);
#4164 = DEFINITIONAL_REPRESENTATION('',(#4165),#4169);
#4165 = LINE('',#4166,#4167);
#4166 = CARTESIAN_POINT('',(0.,7.));
#4167 = VECTOR('',#4168,1.);
#4168 = DIRECTION('',(1.,0.));
#4169 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#4170 = PCURVE('',#4088,#4171);
#4171 = DEFINITIONAL_REPRESENTATION('',(#4172),#4176);
#4172 = CIRCLE('',#4173,4.316451884327);
#4173 = AXIS2_PLACEMENT_2D('',#4174,#4175);
#4174 = CARTESIAN_POINT('',(0.,0.));
#4175 = DIRECTION('',(1.,0.));
#4176 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#4177 = ORIENTED_EDGE('',*,*,#4041,.F.);
#4178 = ORIENTED_EDGE('',*,*,#4179,.T.);
#4179 = EDGE_CURVE('',#4042,#4042,#4180,.T.);
#4180 = SURFACE_CURVE('',#4181,(#4186,#4193),.PCURVE_S1.);
#4181 = CIRCLE('',#4182,0.275);
#4182 = AXIS2_PLACEMENT_3D('',#4183,#4184,#4185);
#4183 = CARTESIAN_POINT('',(51.797763,127.444732,3.620574));
#4184 = DIRECTION('',(0.,0.,1.));
#4185 = DIRECTION('',(1.,0.,0.));
#4186 = PCURVE('',#4052,#4187);
#4187 = DEFINITIONAL_REPRESENTATION('',(#4188),#4192);
#4188 = LINE('',#4189,#4190);
#4189 = CARTESIAN_POINT('',(0.,0.));
#4190 = VECTOR('',#4191,1.);
#4191 = DIRECTION('',(1.,0.));
#4192 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#4193 = PCURVE('',#4194,#4199);
#4194 = CYLINDRICAL_SURFACE('',#4195,0.275);
#4195 = AXIS2_PLACEMENT_3D('',#4196,#4197,#4198);
#4196 = CARTESIAN_POINT('',(51.797763,127.444732,3.620574));
#4197 = DIRECTION('',(0.,0.,1.));
#4198 = DIRECTION('',(1.,0.,0.));
#4199 = DEFINITIONAL_REPRESENTATION('',(#4200),#4203);
#4200 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#4201,#4202),.UNSPECIFIED.,.F.,
  .F.,(2,2),(0.,6.28318530718),.PIECEWISE_BEZIER_KNOTS.);
#4201 = CARTESIAN_POINT('',(0.,0.));
#4202 = CARTESIAN_POINT('',(6.28318530718,0.));
#4203 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#4204 = ADVANCED_FACE('',(#4205),#4194,.F.);
#4205 = FACE_BOUND('',#4206,.F.);
#4206 = EDGE_LOOP('',(#4207,#4231,#4252,#4253));
#4207 = ORIENTED_EDGE('',*,*,#4208,.F.);
#4208 = EDGE_CURVE('',#4209,#4209,#4211,.T.);
#4209 = VERTEX_POINT('',#4210);
#4210 = CARTESIAN_POINT('',(52.072763,127.444732,10.620574));
#4211 = SURFACE_CURVE('',#4212,(#4217,#4224),.PCURVE_S1.);
#4212 = CIRCLE('',#4213,0.275);
#4213 = AXIS2_PLACEMENT_3D('',#4214,#4215,#4216);
#4214 = CARTESIAN_POINT('',(51.797763,127.444732,10.620574));
#4215 = DIRECTION('',(0.,0.,1.));
#4216 = DIRECTION('',(1.,0.,0.));
#4217 = PCURVE('',#4194,#4218);
#4218 = DEFINITIONAL_REPRESENTATION('',(#4219),#4223);
#4219 = LINE('',#4220,#4221);
#4220 = CARTESIAN_POINT('',(0.,7.));
#4221 = VECTOR('',#4222,1.);
#4222 = DIRECTION('',(1.,0.));
#4223 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#4224 = PCURVE('',#4088,#4225);
#4225 = DEFINITIONAL_REPRESENTATION('',(#4226),#4230);
#4226 = CIRCLE('',#4227,0.275);
#4227 = AXIS2_PLACEMENT_2D('',#4228,#4229);
#4228 = CARTESIAN_POINT('',(0.,0.));
#4229 = DIRECTION('',(1.,0.));
#4230 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#4231 = ORIENTED_EDGE('',*,*,#4232,.F.);
#4232 = EDGE_CURVE('',#4042,#4209,#4233,.T.);
#4233 = SEAM_CURVE('',#4234,(#4238,#4245),.PCURVE_S1.);
#4234 = LINE('',#4235,#4236);
#4235 = CARTESIAN_POINT('',(52.072763,127.444732,3.620574));
#4236 = VECTOR('',#4237,1.);
#4237 = DIRECTION('',(0.,0.,1.));
#4238 = PCURVE('',#4194,#4239);
#4239 = DEFINITIONAL_REPRESENTATION('',(#4240),#4244);
#4240 = LINE('',#4241,#4242);
#4241 = CARTESIAN_POINT('',(6.28318530718,-0.));
#4242 = VECTOR('',#4243,1.);
#4243 = DIRECTION('',(0.,1.));
#4244 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#4245 = PCURVE('',#4194,#4246);
#4246 = DEFINITIONAL_REPRESENTATION('',(#4247),#4251);
#4247 = LINE('',#4248,#4249);
#4248 = CARTESIAN_POINT('',(0.,-0.));
#4249 = VECTOR('',#4250,1.);
#4250 = DIRECTION('',(0.,1.));
#4251 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#4252 = ORIENTED_EDGE('',*,*,#4179,.T.);
#4253 = ORIENTED_EDGE('',*,*,#4232,.T.);
#4254 = ADVANCED_FACE('',(#4255,#4278),#4088,.T.);
#4255 = FACE_BOUND('',#4256,.T.);
#4256 = EDGE_LOOP('',(#4257,#4258,#4259));
#4257 = ORIENTED_EDGE('',*,*,#4071,.T.);
#4258 = ORIENTED_EDGE('',*,*,#4156,.T.);
#4259 = ORIENTED_EDGE('',*,*,#4260,.F.);
#4260 = EDGE_CURVE('',#4072,#4101,#4261,.T.);
#4261 = SURFACE_CURVE('',#4262,(#4266,#4272),.PCURVE_S1.);
#4262 = LINE('',#4263,#4264);
#4263 = CARTESIAN_POINT('',(50.854173227578,124.59473749874,10.620574));
#4264 = VECTOR('',#4265,1.);
#4265 = DIRECTION('',(-5.540424023454E-02,0.998464005442,0.));
#4266 = PCURVE('',#4088,#4267);
#4267 = DEFINITIONAL_REPRESENTATION('',(#4268),#4271);
#4268 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#4269,#4270),.UNSPECIFIED.,.F.,
  .F.,(2,2),(-1.468713318731,7.177470941892),.PIECEWISE_BEZIER_KNOTS.);
#4269 = CARTESIAN_POINT('',(-0.862216826875,-4.316451884327));
#4270 = CARTESIAN_POINT('',(-1.341252096763,4.316451884327));
#4271 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#4272 = PCURVE('',#4144,#4273);
#4273 = DEFINITIONAL_REPRESENTATION('',(#4274),#4277);
#4274 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#4275,#4276),.UNSPECIFIED.,.F.,
  .F.,(2,2),(-1.468713318731,7.177470941892),.PIECEWISE_BEZIER_KNOTS.);
#4275 = CARTESIAN_POINT('',(1.324624732052,-17.379426));
#4276 = CARTESIAN_POINT('',(9.970808992675,-17.379426));
#4277 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#4278 = FACE_BOUND('',#4279,.T.);
#4279 = EDGE_LOOP('',(#4280));
#4280 = ORIENTED_EDGE('',*,*,#4208,.F.);
#4281 = ADVANCED_FACE('',(#4282),#4144,.F.);
#4282 = FACE_BOUND('',#4283,.F.);
#4283 = EDGE_LOOP('',(#4284,#4285));
#4284 = ORIENTED_EDGE('',*,*,#4100,.F.);
#4285 = ORIENTED_EDGE('',*,*,#4260,.F.);
#4286 = ( GEOMETRIC_REPRESENTATION_CONTEXT(3) 
GLOBAL_UNCERTAINTY_ASSIGNED_CONTEXT((#4290)) 
GLOBAL_UNIT_ASSIGNED_CONTEXT((#4287,#4288,#4289)) REPRESENTATION_CONTEXT
('Context #1','3D Context with UNIT and UNCERTAINTY') );
#4287 = ( LENGTH_UNIT() NAMED_UNIT(*) SI_UNIT(.MILLI.,.METRE.) );
#4288 = ( NAMED_UNIT(*) PLANE_ANGLE_UNIT() SI_UNIT($,.RADIAN.) );
#4289 = ( NAMED_UNIT(*) SI_UNIT($,.STERADIAN.) SOLID_ANGLE_UNIT() );
#4290 = UNCERTAINTY_MEASURE_WITH_UNIT(LENGTH_MEASURE(1.E-07),#4287,
  'distance_accuracy_value','confusion accuracy');
#4291 = CONTEXT_DEPENDENT_SHAPE_REPRESENTATION(#4292,#4294);
#4292 = ( REPRESENTATION_RELATIONSHIP('','',#4034,#10) 
REPRESENTATION_RELATIONSHIP_WITH_TRANSFORMATION(#4293) 
SHAPE_REPRESENTATION_RELATIONSHIP() );
#4293 = ITEM_DEFINED_TRANSFORMATION('','',#11,#67);
#4294 = PRODUCT_DEFINITION_SHAPE('Placement','Placement of an item',
  #4295);
#4295 = NEXT_ASSEMBLY_USAGE_OCCURRENCE('72','=>[0:1:1:15]','',#5,#4029,$
  );
#4296 = PRODUCT_RELATED_PRODUCT_CATEGORY('part',$,(#4031));
#4297 = SHAPE_DEFINITION_REPRESENTATION(#4298,#4304);
#4298 = PRODUCT_DEFINITION_SHAPE('','',#4299);
#4299 = PRODUCT_DEFINITION('design','',#4300,#4303);
#4300 = PRODUCT_DEFINITION_FORMATION('','',#4301);
#4301 = PRODUCT('PC1-P5_Part3','PC1-P5_Part3','',(#4302));
#4302 = PRODUCT_CONTEXT('',#2,'mechanical');
#4303 = PRODUCT_DEFINITION_CONTEXT('part definition',#2,'design');
#4304 = ADVANCED_BREP_SHAPE_REPRESENTATION('',(#11,#4305),#4561);
#4305 = MANIFOLD_SOLID_BREP('',#4306);
#4306 = CLOSED_SHELL('',(#4307,#4424,#4478,#4503,#4534));
#4307 = ADVANCED_FACE('',(#4308),#4323,.T.);
#4308 = FACE_BOUND('',#4309,.T.);
#4309 = EDGE_LOOP('',(#4310,#4346,#4372,#4405));
#4310 = ORIENTED_EDGE('',*,*,#4311,.T.);
#4311 = EDGE_CURVE('',#4312,#4314,#4316,.T.);
#4312 = VERTEX_POINT('',#4313);
#4313 = CARTESIAN_POINT('',(50.930663692252,123.2162694354,10.620574));
#4314 = VERTEX_POINT('',#4315);
#4315 = CARTESIAN_POINT('',(50.46815721806,131.55130055955,10.620574));
#4316 = SURFACE_CURVE('',#4317,(#4322,#4334),.PCURVE_S1.);
#4317 = CIRCLE('',#4318,4.316451884327);
#4318 = AXIS2_PLACEMENT_3D('',#4319,#4320,#4321);
#4319 = CARTESIAN_POINT('',(51.797763,127.444732,10.620574));
#4320 = DIRECTION('',(0.,0.,1.));
#4321 = DIRECTION('',(-0.200882421717,-0.979615359539,0.));
#4322 = PCURVE('',#4323,#4328);
#4323 = CYLINDRICAL_SURFACE('',#4324,4.316451884327);
#4324 = AXIS2_PLACEMENT_3D('',#4325,#4326,#4327);
#4325 = CARTESIAN_POINT('',(51.797763,127.444732,10.620574));
#4326 = DIRECTION('',(0.,0.,1.));
#4327 = DIRECTION('',(1.,0.,0.));
#4328 = DEFINITIONAL_REPRESENTATION('',(#4329),#4333);
#4329 = LINE('',#4330,#4331);
#4330 = CARTESIAN_POINT('',(4.51013035878,0.));
#4331 = VECTOR('',#4332,1.);
#4332 = DIRECTION('',(1.,0.));
#4333 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#4334 = PCURVE('',#4335,#4340);
#4335 = PLANE('',#4336);
#4336 = AXIS2_PLACEMENT_3D('',#4337,#4338,#4339);
#4337 = CARTESIAN_POINT('',(51.797763,127.444732,10.620574));
#4338 = DIRECTION('',(0.,0.,1.));
#4339 = DIRECTION('',(1.,0.,0.));
#4340 = DEFINITIONAL_REPRESENTATION('',(#4341),#4345);
#4341 = CIRCLE('',#4342,4.316451884327);
#4342 = AXIS2_PLACEMENT_2D('',#4343,#4344);
#4343 = CARTESIAN_POINT('',(0.,0.));
#4344 = DIRECTION('',(-0.200882421717,-0.979615359539));
#4345 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#4346 = ORIENTED_EDGE('',*,*,#4347,.T.);
#4347 = EDGE_CURVE('',#4314,#4348,#4350,.T.);
#4348 = VERTEX_POINT('',#4349);
#4349 = CARTESIAN_POINT('',(50.46815721806,131.55130055955,18.));
#4350 = SURFACE_CURVE('',#4351,(#4355,#4361),.PCURVE_S1.);
#4351 = LINE('',#4352,#4353);
#4352 = CARTESIAN_POINT('',(50.46815721806,131.55130055955,10.620574));
#4353 = VECTOR('',#4354,1.);
#4354 = DIRECTION('',(0.,0.,1.));
#4355 = PCURVE('',#4323,#4356);
#4356 = DEFINITIONAL_REPRESENTATION('',(#4357),#4360);
#4357 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#4358,#4359),.UNSPECIFIED.,.F.,
  .F.,(2,2),(0.,7.379426),.PIECEWISE_BEZIER_KNOTS.);
#4358 = CARTESIAN_POINT('',(8.167105504669,0.));
#4359 = CARTESIAN_POINT('',(8.167105504669,7.379426));
#4360 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#4361 = PCURVE('',#4362,#4367);
#4362 = PLANE('',#4363);
#4363 = AXIS2_PLACEMENT_3D('',#4364,#4365,#4366);
#4364 = CARTESIAN_POINT('',(51.008936,121.80569,28.));
#4365 = DIRECTION('',(0.998464005442,5.540424023454E-02,0.));
#4366 = DIRECTION('',(-5.540424023454E-02,0.998464005442,0.));
#4367 = DEFINITIONAL_REPRESENTATION('',(#4368),#4371);
#4368 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#4369,#4370),.UNSPECIFIED.,.F.,
  .F.,(2,2),(-5.999999999062E-07,7.3794266),.PIECEWISE_BEZIER_KNOTS.);
#4369 = CARTESIAN_POINT('',(9.760602792326,-17.3794266));
#4370 = CARTESIAN_POINT('',(9.760602792326,-9.9999994));
#4371 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#4372 = ORIENTED_EDGE('',*,*,#4373,.T.);
#4373 = EDGE_CURVE('',#4348,#4374,#4376,.T.);
#4374 = VERTEX_POINT('',#4375);
#4375 = CARTESIAN_POINT('',(50.930663692252,123.2162694354,18.));
#4376 = SURFACE_CURVE('',#4377,(#4382,#4389),.PCURVE_S1.);
#4377 = CIRCLE('',#4378,4.316451884327);
#4378 = AXIS2_PLACEMENT_3D('',#4379,#4380,#4381);
#4379 = CARTESIAN_POINT('',(51.797763,127.444732,18.));
#4380 = DIRECTION('',(0.,0.,-1.));
#4381 = DIRECTION('',(-0.308032110069,0.951375960999,0.));
#4382 = PCURVE('',#4323,#4383);
#4383 = DEFINITIONAL_REPRESENTATION('',(#4384),#4388);
#4384 = LINE('',#4385,#4386);
#4385 = CARTESIAN_POINT('',(8.167105504669,7.379426));
#4386 = VECTOR('',#4387,1.);
#4387 = DIRECTION('',(-1.,-0.));
#4388 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#4389 = PCURVE('',#4390,#4395);
#4390 = PLANE('',#4391);
#4391 = AXIS2_PLACEMENT_3D('',#4392,#4393,#4394);
#4392 = CARTESIAN_POINT('',(51.797763,127.444732,18.));
#4393 = DIRECTION('',(0.,0.,1.));
#4394 = DIRECTION('',(1.,0.,0.));
#4395 = DEFINITIONAL_REPRESENTATION('',(#4396),#4404);
#4396 = ( BOUNDED_CURVE() B_SPLINE_CURVE(2,(#4397,#4398,#4399,#4400,
#4401,#4402,#4403),.UNSPECIFIED.,.T.,.F.) B_SPLINE_CURVE_WITH_KNOTS((1,2
    ,2,2,2,1),(-2.094395102393,0.,2.094395102393,4.188790204786,
6.28318530718,8.377580409573),.UNSPECIFIED.) CURVE() 
GEOMETRIC_REPRESENTATION_ITEM() RATIONAL_B_SPLINE_CURVE((1.,0.5,1.,0.5,
1.,0.5,1.)) REPRESENTATION_ITEM('') );
#4397 = CARTESIAN_POINT('',(-1.32960578194,4.106568559557));
#4398 = CARTESIAN_POINT('',(5.783179607978,6.409513327915));
#4399 = CARTESIAN_POINT('',(4.221195585929,-0.9018118956));
#4400 = CARTESIAN_POINT('',(2.65921156388,-8.213137119115));
#4401 = CARTESIAN_POINT('',(-2.891589803989,-3.204756663957));
#4402 = CARTESIAN_POINT('',(-8.442391171858,1.8036237912));
#4403 = CARTESIAN_POINT('',(-1.32960578194,4.106568559557));
#4404 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#4405 = ORIENTED_EDGE('',*,*,#4406,.F.);
#4406 = EDGE_CURVE('',#4312,#4374,#4407,.T.);
#4407 = SURFACE_CURVE('',#4408,(#4412,#4418),.PCURVE_S1.);
#4408 = LINE('',#4409,#4410);
#4409 = CARTESIAN_POINT('',(50.930663692252,123.2162694354,10.620574));
#4410 = VECTOR('',#4411,1.);
#4411 = DIRECTION('',(0.,0.,1.));
#4412 = PCURVE('',#4323,#4413);
#4413 = DEFINITIONAL_REPRESENTATION('',(#4414),#4417);
#4414 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#4415,#4416),.UNSPECIFIED.,.F.,
  .F.,(2,2),(0.,7.379426),.PIECEWISE_BEZIER_KNOTS.);
#4415 = CARTESIAN_POINT('',(4.51013035878,0.));
#4416 = CARTESIAN_POINT('',(4.51013035878,7.379426));
#4417 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#4418 = PCURVE('',#4362,#4419);
#4419 = DEFINITIONAL_REPRESENTATION('',(#4420),#4423);
#4420 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#4421,#4422),.UNSPECIFIED.,.F.,
  .F.,(2,2),(-5.999999999062E-07,7.3794266),.PIECEWISE_BEZIER_KNOTS.);
#4421 = CARTESIAN_POINT('',(1.412749410807,-17.3794266));
#4422 = CARTESIAN_POINT('',(1.412749410807,-9.9999994));
#4423 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#4424 = ADVANCED_FACE('',(#4425,#4447),#4335,.F.);
#4425 = FACE_BOUND('',#4426,.F.);
#4426 = EDGE_LOOP('',(#4427,#4428));
#4427 = ORIENTED_EDGE('',*,*,#4311,.T.);
#4428 = ORIENTED_EDGE('',*,*,#4429,.F.);
#4429 = EDGE_CURVE('',#4312,#4314,#4430,.T.);
#4430 = SURFACE_CURVE('',#4431,(#4435,#4441),.PCURVE_S1.);
#4431 = LINE('',#4432,#4433);
#4432 = CARTESIAN_POINT('',(50.854173227578,124.59473749874,10.620574));
#4433 = VECTOR('',#4434,1.);
#4434 = DIRECTION('',(-5.540424023454E-02,0.998464005442,0.));
#4435 = PCURVE('',#4335,#4436);
#4436 = DEFINITIONAL_REPRESENTATION('',(#4437),#4440);
#4437 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#4438,#4439),.UNSPECIFIED.,.F.,
  .F.,(2,2),(-1.468713318731,7.177470941892),.PIECEWISE_BEZIER_KNOTS.);
#4438 = CARTESIAN_POINT('',(-0.862216826875,-4.316451884327));
#4439 = CARTESIAN_POINT('',(-1.341252096763,4.316451884327));
#4440 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#4441 = PCURVE('',#4362,#4442);
#4442 = DEFINITIONAL_REPRESENTATION('',(#4443),#4446);
#4443 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#4444,#4445),.UNSPECIFIED.,.F.,
  .F.,(2,2),(-1.468713318731,7.177470941892),.PIECEWISE_BEZIER_KNOTS.);
#4444 = CARTESIAN_POINT('',(1.324624732052,-17.379426));
#4445 = CARTESIAN_POINT('',(9.970808992675,-17.379426));
#4446 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#4447 = FACE_BOUND('',#4448,.F.);
#4448 = EDGE_LOOP('',(#4449));
#4449 = ORIENTED_EDGE('',*,*,#4450,.F.);
#4450 = EDGE_CURVE('',#4451,#4451,#4453,.T.);
#4451 = VERTEX_POINT('',#4452);
#4452 = CARTESIAN_POINT('',(52.072763,127.444732,10.620574));
#4453 = SURFACE_CURVE('',#4454,(#4459,#4466),.PCURVE_S1.);
#4454 = CIRCLE('',#4455,0.275);
#4455 = AXIS2_PLACEMENT_3D('',#4456,#4457,#4458);
#4456 = CARTESIAN_POINT('',(51.797763,127.444732,10.620574));
#4457 = DIRECTION('',(0.,0.,1.));
#4458 = DIRECTION('',(1.,0.,0.));
#4459 = PCURVE('',#4335,#4460);
#4460 = DEFINITIONAL_REPRESENTATION('',(#4461),#4465);
#4461 = CIRCLE('',#4462,0.275);
#4462 = AXIS2_PLACEMENT_2D('',#4463,#4464);
#4463 = CARTESIAN_POINT('',(0.,0.));
#4464 = DIRECTION('',(1.,0.));
#4465 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#4466 = PCURVE('',#4467,#4472);
#4467 = CYLINDRICAL_SURFACE('',#4468,0.275);
#4468 = AXIS2_PLACEMENT_3D('',#4469,#4470,#4471);
#4469 = CARTESIAN_POINT('',(51.797763,127.444732,10.620574));
#4470 = DIRECTION('',(0.,0.,1.));
#4471 = DIRECTION('',(1.,0.,0.));
#4472 = DEFINITIONAL_REPRESENTATION('',(#4473),#4477);
#4473 = LINE('',#4474,#4475);
#4474 = CARTESIAN_POINT('',(0.,0.));
#4475 = VECTOR('',#4476,1.);
#4476 = DIRECTION('',(1.,0.));
#4477 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#4478 = ADVANCED_FACE('',(#4479),#4362,.F.);
#4479 = FACE_BOUND('',#4480,.F.);
#4480 = EDGE_LOOP('',(#4481,#4482,#4483,#4484));
#4481 = ORIENTED_EDGE('',*,*,#4406,.F.);
#4482 = ORIENTED_EDGE('',*,*,#4429,.T.);
#4483 = ORIENTED_EDGE('',*,*,#4347,.T.);
#4484 = ORIENTED_EDGE('',*,*,#4485,.F.);
#4485 = EDGE_CURVE('',#4374,#4348,#4486,.T.);
#4486 = SURFACE_CURVE('',#4487,(#4491,#4497),.PCURVE_S1.);
#4487 = LINE('',#4488,#4489);
#4488 = CARTESIAN_POINT('',(50.854173227578,124.59473749874,18.));
#4489 = VECTOR('',#4490,1.);
#4490 = DIRECTION('',(-5.540424023454E-02,0.998464005442,0.));
#4491 = PCURVE('',#4362,#4492);
#4492 = DEFINITIONAL_REPRESENTATION('',(#4493),#4496);
#4493 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#4494,#4495),.UNSPECIFIED.,.F.,
  .F.,(2,2),(-1.468713318731,7.177470941892),.PIECEWISE_BEZIER_KNOTS.);
#4494 = CARTESIAN_POINT('',(1.324624732052,-10.));
#4495 = CARTESIAN_POINT('',(9.970808992675,-10.));
#4496 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#4497 = PCURVE('',#4390,#4498);
#4498 = DEFINITIONAL_REPRESENTATION('',(#4499),#4502);
#4499 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#4500,#4501),.UNSPECIFIED.,.F.,
  .F.,(2,2),(-1.468713318731,7.177470941892),.PIECEWISE_BEZIER_KNOTS.);
#4500 = CARTESIAN_POINT('',(-0.862216826875,-4.316451884327));
#4501 = CARTESIAN_POINT('',(-1.341252096763,4.316451884327));
#4502 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#4503 = ADVANCED_FACE('',(#4504,#4508),#4390,.T.);
#4504 = FACE_BOUND('',#4505,.T.);
#4505 = EDGE_LOOP('',(#4506,#4507));
#4506 = ORIENTED_EDGE('',*,*,#4485,.F.);
#4507 = ORIENTED_EDGE('',*,*,#4373,.F.);
#4508 = FACE_BOUND('',#4509,.T.);
#4509 = EDGE_LOOP('',(#4510));
#4510 = ORIENTED_EDGE('',*,*,#4511,.F.);
#4511 = EDGE_CURVE('',#4512,#4512,#4514,.T.);
#4512 = VERTEX_POINT('',#4513);
#4513 = CARTESIAN_POINT('',(52.072763,127.444732,18.));
#4514 = SURFACE_CURVE('',#4515,(#4520,#4527),.PCURVE_S1.);
#4515 = CIRCLE('',#4516,0.275);
#4516 = AXIS2_PLACEMENT_3D('',#4517,#4518,#4519);
#4517 = CARTESIAN_POINT('',(51.797763,127.444732,18.));
#4518 = DIRECTION('',(0.,0.,1.));
#4519 = DIRECTION('',(1.,0.,0.));
#4520 = PCURVE('',#4390,#4521);
#4521 = DEFINITIONAL_REPRESENTATION('',(#4522),#4526);
#4522 = CIRCLE('',#4523,0.275);
#4523 = AXIS2_PLACEMENT_2D('',#4524,#4525);
#4524 = CARTESIAN_POINT('',(0.,0.));
#4525 = DIRECTION('',(1.,0.));
#4526 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#4527 = PCURVE('',#4467,#4528);
#4528 = DEFINITIONAL_REPRESENTATION('',(#4529),#4533);
#4529 = LINE('',#4530,#4531);
#4530 = CARTESIAN_POINT('',(0.,7.379426));
#4531 = VECTOR('',#4532,1.);
#4532 = DIRECTION('',(1.,0.));
#4533 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#4534 = ADVANCED_FACE('',(#4535),#4467,.F.);
#4535 = FACE_BOUND('',#4536,.F.);
#4536 = EDGE_LOOP('',(#4537,#4538,#4559,#4560));
#4537 = ORIENTED_EDGE('',*,*,#4511,.F.);
#4538 = ORIENTED_EDGE('',*,*,#4539,.F.);
#4539 = EDGE_CURVE('',#4451,#4512,#4540,.T.);
#4540 = SEAM_CURVE('',#4541,(#4545,#4552),.PCURVE_S1.);
#4541 = LINE('',#4542,#4543);
#4542 = CARTESIAN_POINT('',(52.072763,127.444732,10.620574));
#4543 = VECTOR('',#4544,1.);
#4544 = DIRECTION('',(0.,0.,1.));
#4545 = PCURVE('',#4467,#4546);
#4546 = DEFINITIONAL_REPRESENTATION('',(#4547),#4551);
#4547 = LINE('',#4548,#4549);
#4548 = CARTESIAN_POINT('',(6.28318530718,-0.));
#4549 = VECTOR('',#4550,1.);
#4550 = DIRECTION('',(0.,1.));
#4551 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#4552 = PCURVE('',#4467,#4553);
#4553 = DEFINITIONAL_REPRESENTATION('',(#4554),#4558);
#4554 = LINE('',#4555,#4556);
#4555 = CARTESIAN_POINT('',(0.,-0.));
#4556 = VECTOR('',#4557,1.);
#4557 = DIRECTION('',(0.,1.));
#4558 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#4559 = ORIENTED_EDGE('',*,*,#4450,.T.);
#4560 = ORIENTED_EDGE('',*,*,#4539,.T.);
#4561 = ( GEOMETRIC_REPRESENTATION_CONTEXT(3) 
GLOBAL_UNCERTAINTY_ASSIGNED_CONTEXT((#4565)) 
GLOBAL_UNIT_ASSIGNED_CONTEXT((#4562,#4563,#4564)) REPRESENTATION_CONTEXT
('Context #1','3D Context with UNIT and UNCERTAINTY') );
#4562 = ( LENGTH_UNIT() NAMED_UNIT(*) SI_UNIT(.MILLI.,.METRE.) );
#4563 = ( NAMED_UNIT(*) PLANE_ANGLE_UNIT() SI_UNIT($,.RADIAN.) );
#4564 = ( NAMED_UNIT(*) SI_UNIT($,.STERADIAN.) SOLID_ANGLE_UNIT() );
#4565 = UNCERTAINTY_MEASURE_WITH_UNIT(LENGTH_MEASURE(1.E-07),#4562,
  'distance_accuracy_value','confusion accuracy');
#4566 = CONTEXT_DEPENDENT_SHAPE_REPRESENTATION(#4567,#4569);
#4567 = ( REPRESENTATION_RELATIONSHIP('','',#4304,#10) 
REPRESENTATION_RELATIONSHIP_WITH_TRANSFORMATION(#4568) 
SHAPE_REPRESENTATION_RELATIONSHIP() );
#4568 = ITEM_DEFINED_TRANSFORMATION('','',#11,#71);
#4569 = PRODUCT_DEFINITION_SHAPE('Placement','Placement of an item',
  #4570);
#4570 = NEXT_ASSEMBLY_USAGE_OCCURRENCE('73','=>[0:1:1:16]','',#5,#4299,$
  );
#4571 = PRODUCT_RELATED_PRODUCT_CATEGORY('part',$,(#4301));
#4572 = SHAPE_DEFINITION_REPRESENTATION(#4573,#4579);
#4573 = PRODUCT_DEFINITION_SHAPE('','',#4574);
#4574 = PRODUCT_DEFINITION('design','',#4575,#4578);
#4575 = PRODUCT_DEFINITION_FORMATION('','',#4576);
#4576 = PRODUCT('PC1-P6_Part1','PC1-P6_Part1','',(#4577));
#4577 = PRODUCT_CONTEXT('',#2,'mechanical');
#4578 = PRODUCT_DEFINITION_CONTEXT('part definition',#2,'design');
#4579 = ADVANCED_BREP_SHAPE_REPRESENTATION('',(#11,#4580),#4678);
#4580 = MANIFOLD_SOLID_BREP('',#4581);
#4581 = CLOSED_SHELL('',(#4582,#4670,#4674));
#4582 = ADVANCED_FACE('',(#4583),#4596,.T.);
#4583 = FACE_BOUND('',#4584,.T.);
#4584 = EDGE_LOOP('',(#4585,#4619,#4642,#4669));
#4585 = ORIENTED_EDGE('',*,*,#4586,.F.);
#4586 = EDGE_CURVE('',#4587,#4587,#4589,.T.);
#4587 = VERTEX_POINT('',#4588);
#4588 = CARTESIAN_POINT('',(0.275,-6.73555739531E-17,13.725137));
#4589 = SURFACE_CURVE('',#4590,(#4595,#4607),.PCURVE_S1.);
#4590 = CIRCLE('',#4591,0.275);
#4591 = AXIS2_PLACEMENT_3D('',#4592,#4593,#4594);
#4592 = CARTESIAN_POINT('',(0.,0.,13.725137));
#4593 = DIRECTION('',(0.,0.,1.));
#4594 = DIRECTION('',(1.,0.,-0.));
#4595 = PCURVE('',#4596,#4601);
#4596 = CYLINDRICAL_SURFACE('',#4597,0.275);
#4597 = AXIS2_PLACEMENT_3D('',#4598,#4599,#4600);
#4598 = CARTESIAN_POINT('',(0.,0.,0.));
#4599 = DIRECTION('',(0.,0.,1.));
#4600 = DIRECTION('',(1.,0.,-0.));
#4601 = DEFINITIONAL_REPRESENTATION('',(#4602),#4606);
#4602 = LINE('',#4603,#4604);
#4603 = CARTESIAN_POINT('',(0.,13.725137));
#4604 = VECTOR('',#4605,1.);
#4605 = DIRECTION('',(1.,0.));
#4606 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#4607 = PCURVE('',#4608,#4613);
#4608 = PLANE('',#4609);
#4609 = AXIS2_PLACEMENT_3D('',#4610,#4611,#4612);
#4610 = CARTESIAN_POINT('',(0.,0.,13.725137));
#4611 = DIRECTION('',(0.,0.,1.));
#4612 = DIRECTION('',(1.,0.,-0.));
#4613 = DEFINITIONAL_REPRESENTATION('',(#4614),#4618);
#4614 = CIRCLE('',#4615,0.275);
#4615 = AXIS2_PLACEMENT_2D('',#4616,#4617);
#4616 = CARTESIAN_POINT('',(0.,0.));
#4617 = DIRECTION('',(1.,0.));
#4618 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#4619 = ORIENTED_EDGE('',*,*,#4620,.F.);
#4620 = EDGE_CURVE('',#4621,#4587,#4623,.T.);
#4621 = VERTEX_POINT('',#4622);
#4622 = CARTESIAN_POINT('',(0.275,-6.73555739531E-17,0.));
#4623 = SEAM_CURVE('',#4624,(#4628,#4635),.PCURVE_S1.);
#4624 = LINE('',#4625,#4626);
#4625 = CARTESIAN_POINT('',(0.275,-6.73555739531E-17,0.));
#4626 = VECTOR('',#4627,1.);
#4627 = DIRECTION('',(0.,0.,1.));
#4628 = PCURVE('',#4596,#4629);
#4629 = DEFINITIONAL_REPRESENTATION('',(#4630),#4634);
#4630 = LINE('',#4631,#4632);
#4631 = CARTESIAN_POINT('',(6.28318530718,-0.));
#4632 = VECTOR('',#4633,1.);
#4633 = DIRECTION('',(0.,1.));
#4634 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#4635 = PCURVE('',#4596,#4636);
#4636 = DEFINITIONAL_REPRESENTATION('',(#4637),#4641);
#4637 = LINE('',#4638,#4639);
#4638 = CARTESIAN_POINT('',(0.,-0.));
#4639 = VECTOR('',#4640,1.);
#4640 = DIRECTION('',(0.,1.));
#4641 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#4642 = ORIENTED_EDGE('',*,*,#4643,.T.);
#4643 = EDGE_CURVE('',#4621,#4621,#4644,.T.);
#4644 = SURFACE_CURVE('',#4645,(#4650,#4657),.PCURVE_S1.);
#4645 = CIRCLE('',#4646,0.275);
#4646 = AXIS2_PLACEMENT_3D('',#4647,#4648,#4649);
#4647 = CARTESIAN_POINT('',(0.,0.,0.));
#4648 = DIRECTION('',(0.,0.,1.));
#4649 = DIRECTION('',(1.,0.,-0.));
#4650 = PCURVE('',#4596,#4651);
#4651 = DEFINITIONAL_REPRESENTATION('',(#4652),#4656);
#4652 = LINE('',#4653,#4654);
#4653 = CARTESIAN_POINT('',(0.,0.));
#4654 = VECTOR('',#4655,1.);
#4655 = DIRECTION('',(1.,0.));
#4656 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#4657 = PCURVE('',#4658,#4663);
#4658 = PLANE('',#4659);
#4659 = AXIS2_PLACEMENT_3D('',#4660,#4661,#4662);
#4660 = CARTESIAN_POINT('',(0.,0.,0.));
#4661 = DIRECTION('',(0.,0.,1.));
#4662 = DIRECTION('',(1.,0.,-0.));
#4663 = DEFINITIONAL_REPRESENTATION('',(#4664),#4668);
#4664 = CIRCLE('',#4665,0.275);
#4665 = AXIS2_PLACEMENT_2D('',#4666,#4667);
#4666 = CARTESIAN_POINT('',(0.,0.));
#4667 = DIRECTION('',(1.,0.));
#4668 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#4669 = ORIENTED_EDGE('',*,*,#4620,.T.);
#4670 = ADVANCED_FACE('',(#4671),#4608,.T.);
#4671 = FACE_BOUND('',#4672,.T.);
#4672 = EDGE_LOOP('',(#4673));
#4673 = ORIENTED_EDGE('',*,*,#4586,.T.);
#4674 = ADVANCED_FACE('',(#4675),#4658,.F.);
#4675 = FACE_BOUND('',#4676,.T.);
#4676 = EDGE_LOOP('',(#4677));
#4677 = ORIENTED_EDGE('',*,*,#4643,.F.);
#4678 = ( GEOMETRIC_REPRESENTATION_CONTEXT(3) 
GLOBAL_UNCERTAINTY_ASSIGNED_CONTEXT((#4682)) 
GLOBAL_UNIT_ASSIGNED_CONTEXT((#4679,#4680,#4681)) REPRESENTATION_CONTEXT
('Context #1','3D Context with UNIT and UNCERTAINTY') );
#4679 = ( LENGTH_UNIT() NAMED_UNIT(*) SI_UNIT(.MILLI.,.METRE.) );
#4680 = ( NAMED_UNIT(*) PLANE_ANGLE_UNIT() SI_UNIT($,.RADIAN.) );
#4681 = ( NAMED_UNIT(*) SI_UNIT($,.STERADIAN.) SOLID_ANGLE_UNIT() );
#4682 = UNCERTAINTY_MEASURE_WITH_UNIT(LENGTH_MEASURE(1.E-07),#4679,
  'distance_accuracy_value','confusion accuracy');
#4683 = CONTEXT_DEPENDENT_SHAPE_REPRESENTATION(#4684,#4686);
#4684 = ( REPRESENTATION_RELATIONSHIP('','',#4579,#10) 
REPRESENTATION_RELATIONSHIP_WITH_TRANSFORMATION(#4685) 
SHAPE_REPRESENTATION_RELATIONSHIP() );
#4685 = ITEM_DEFINED_TRANSFORMATION('','',#11,#75);
#4686 = PRODUCT_DEFINITION_SHAPE('Placement','Placement of an item',
  #4687);
#4687 = NEXT_ASSEMBLY_USAGE_OCCURRENCE('74','=>[0:1:1:17]','',#5,#4574,$
  );
#4688 = PRODUCT_RELATED_PRODUCT_CATEGORY('part',$,(#4576));
#4689 = SHAPE_DEFINITION_REPRESENTATION(#4690,#4696);
#4690 = PRODUCT_DEFINITION_SHAPE('','',#4691);
#4691 = PRODUCT_DEFINITION('design','',#4692,#4695);
#4692 = PRODUCT_DEFINITION_FORMATION('','',#4693);
#4693 = PRODUCT('PC1-P6_Part2','PC1-P6_Part2','',(#4694));
#4694 = PRODUCT_CONTEXT('',#2,'mechanical');
#4695 = PRODUCT_DEFINITION_CONTEXT('part definition',#2,'design');
#4696 = ADVANCED_BREP_SHAPE_REPRESENTATION('',(#11,#4697),#5030);
#4697 = MANIFOLD_SOLID_BREP('',#4698);
#4698 = CLOSED_SHELL('',(#4699,#4900,#4950,#4998,#5024));
#4699 = ADVANCED_FACE('',(#4700),#4714,.T.);
#4700 = FACE_BOUND('',#4701,.T.);
#4701 = EDGE_LOOP('',(#4702,#4732,#4761,#4795,#4851,#4873,#4874));
#4702 = ORIENTED_EDGE('',*,*,#4703,.T.);
#4703 = EDGE_CURVE('',#4704,#4706,#4708,.T.);
#4704 = VERTEX_POINT('',#4705);
#4705 = CARTESIAN_POINT('',(52.164186,125.797167,4.274863));
#4706 = VERTEX_POINT('',#4707);
#4707 = CARTESIAN_POINT('',(56.205637884327,125.797167,11.274863));
#4708 = SEAM_CURVE('',#4709,(#4713,#4725),.PCURVE_S1.);
#4709 = LINE('',#4710,#4711);
#4710 = CARTESIAN_POINT('',(52.164186,125.797167,4.274863));
#4711 = VECTOR('',#4712,1.);
#4712 = DIRECTION('',(0.5,-1.224646799147E-16,0.866025403784));
#4713 = PCURVE('',#4714,#4719);
#4714 = CONICAL_SURFACE('',#4715,0.275,0.523598775598);
#4715 = AXIS2_PLACEMENT_3D('',#4716,#4717,#4718);
#4716 = CARTESIAN_POINT('',(51.889186,125.797167,4.274863));
#4717 = DIRECTION('',(0.,0.,1.));
#4718 = DIRECTION('',(1.,0.,0.));
#4719 = DEFINITIONAL_REPRESENTATION('',(#4720),#4724);
#4720 = LINE('',#4721,#4722);
#4721 = CARTESIAN_POINT('',(6.28318530718,-0.));
#4722 = VECTOR('',#4723,1.);
#4723 = DIRECTION('',(0.,1.));
#4724 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#4725 = PCURVE('',#4714,#4726);
#4726 = DEFINITIONAL_REPRESENTATION('',(#4727),#4731);
#4727 = LINE('',#4728,#4729);
#4728 = CARTESIAN_POINT('',(0.,-0.));
#4729 = VECTOR('',#4730,1.);
#4730 = DIRECTION('',(0.,1.));
#4731 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#4732 = ORIENTED_EDGE('',*,*,#4733,.F.);
#4733 = EDGE_CURVE('',#4734,#4706,#4736,.T.);
#4734 = VERTEX_POINT('',#4735);
#4735 = CARTESIAN_POINT('',(53.53263213443,121.80582115267,11.274863));
#4736 = SURFACE_CURVE('',#4737,(#4742,#4749),.PCURVE_S1.);
#4737 = CIRCLE('',#4738,4.316451884327);
#4738 = AXIS2_PLACEMENT_3D('',#4739,#4740,#4741);
#4739 = CARTESIAN_POINT('',(51.889186,125.797167,11.274863));
#4740 = DIRECTION('',(0.,0.,1.));
#4741 = DIRECTION('',(1.,0.,0.));
#4742 = PCURVE('',#4714,#4743);
#4743 = DEFINITIONAL_REPRESENTATION('',(#4744),#4748);
#4744 = LINE('',#4745,#4746);
#4745 = CARTESIAN_POINT('',(0.,7.));
#4746 = VECTOR('',#4747,1.);
#4747 = DIRECTION('',(1.,0.));
#4748 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#4749 = PCURVE('',#4750,#4755);
#4750 = PLANE('',#4751);
#4751 = AXIS2_PLACEMENT_3D('',#4752,#4753,#4754);
#4752 = CARTESIAN_POINT('',(51.889186,125.797167,11.274863));
#4753 = DIRECTION('',(0.,0.,1.));
#4754 = DIRECTION('',(1.,0.,0.));
#4755 = DEFINITIONAL_REPRESENTATION('',(#4756),#4760);
#4756 = CIRCLE('',#4757,4.316451884327);
#4757 = AXIS2_PLACEMENT_2D('',#4758,#4759);
#4758 = CARTESIAN_POINT('',(0.,0.));
#4759 = DIRECTION('',(1.,0.));
#4760 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#4761 = ORIENTED_EDGE('',*,*,#4762,.F.);
#4762 = EDGE_CURVE('',#4763,#4734,#4765,.T.);
#4763 = VERTEX_POINT('',#4764);
#4764 = CARTESIAN_POINT('',(51.008936,121.80569,10.878110180426));
#4765 = SURFACE_CURVE('',#4766,(#4771,#4783),.PCURVE_S1.);
#4766 = HYPERBOLA('',#4767,6.913361718787,3.991431249347);
#4767 = AXIS2_PLACEMENT_3D('',#4768,#4769,#4770);
#4768 = CARTESIAN_POINT('',(51.889393428655,121.80573575604,
    3.798549027919));
#4769 = DIRECTION('',(-5.196848995186E-05,0.99999999865,0.));
#4770 = DIRECTION('',(0.,0.,1.));
#4771 = PCURVE('',#4714,#4772);
#4772 = DEFINITIONAL_REPRESENTATION('',(#4773),#4782);
#4773 = B_SPLINE_CURVE_WITH_KNOTS('',7,(#4774,#4775,#4776,#4777,#4778,
    #4779,#4780,#4781),.UNSPECIFIED.,.F.,.F.,(8,8),(-0.400868689999,
    0.400868689999),.PIECEWISE_BEZIER_KNOTS.);
#4774 = CARTESIAN_POINT('',(4.321896416479,7.));
#4775 = CARTESIAN_POINT('',(4.427806141743,6.674016681587));
#4776 = CARTESIAN_POINT('',(4.539103632585,6.462453466464));
#4777 = CARTESIAN_POINT('',(4.654174955952,6.358326172249));
#4778 = CARTESIAN_POINT('',(4.770706941798,6.358326172245));
#4779 = CARTESIAN_POINT('',(4.885778265164,6.462453466466));
#4780 = CARTESIAN_POINT('',(4.997075756006,6.674016681587));
#4781 = CARTESIAN_POINT('',(5.102985481271,7.));
#4782 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#4783 = PCURVE('',#4784,#4789);
#4784 = PLANE('',#4785);
#4785 = AXIS2_PLACEMENT_3D('',#4786,#4787,#4788);
#4786 = CARTESIAN_POINT('',(77.717428,121.807078,28.));
#4787 = DIRECTION('',(-5.196848995186E-05,0.99999999865,0.));
#4788 = DIRECTION('',(-0.99999999865,-5.196848995186E-05,0.));
#4789 = DEFINITIONAL_REPRESENTATION('',(#4790),#4794);
#4790 = ( BOUNDED_CURVE() B_SPLINE_CURVE(2,(#4791,#4792,#4793),
.UNSPECIFIED.,.F.,.F.) B_SPLINE_CURVE_WITH_KNOTS((3,3),(-0.400868689999,
0.400868689999),.PIECEWISE_BEZIER_KNOTS.) CURVE() 
GEOMETRIC_REPRESENTATION_ITEM() RATIONAL_B_SPLINE_CURVE((1.,
1.081429596222,1.)) REPRESENTATION_ITEM('') );
#4791 = CARTESIAN_POINT('',(27.471273314217,-16.725137));
#4792 = CARTESIAN_POINT('',(25.828034606222,-17.8086522703));
#4793 = CARTESIAN_POINT('',(24.184795898228,-16.725137));
#4794 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#4795 = ORIENTED_EDGE('',*,*,#4796,.F.);
#4796 = EDGE_CURVE('',#4797,#4763,#4799,.T.);
#4797 = VERTEX_POINT('',#4798);
#4798 = CARTESIAN_POINT('',(50.559579738223,129.90373540419,11.274863));
#4799 = SURFACE_CURVE('',#4800,(#4805,#4839),.PCURVE_S1.);
#4800 = HYPERBOLA('',#4801,1.905329831981,1.100042691389);
#4801 = AXIS2_PLACEMENT_3D('',#4802,#4803,#4804);
#4802 = CARTESIAN_POINT('',(50.790832968198,125.73621997045,
    3.798549027919));
#4803 = DIRECTION('',(0.998464005442,5.540424023454E-02,0.));
#4804 = DIRECTION('',(-0.,0.,1.));
#4805 = PCURVE('',#4714,#4806);
#4806 = DEFINITIONAL_REPRESENTATION('',(#4807),#4838);
#4807 = B_SPLINE_CURVE_WITH_KNOTS('',8,(#4808,#4809,#4810,#4811,#4812,
    #4813,#4814,#4815,#4816,#4817,#4818,#4819,#4820,#4821,#4822,#4823,
    #4824,#4825,#4826,#4827,#4828,#4829,#4830,#4831,#4832,#4833,#4834,
    #4835,#4836,#4837),.UNSPECIFIED.,.F.,.F.,(9,7,7,7,9),(-2.04358458549
    ,-1.021792292745,-0.255448073186,0.894068256152,2.04358458549),
  .UNSPECIFIED.);
#4808 = CARTESIAN_POINT('',(1.883920314336,7.));
#4809 = CARTESIAN_POINT('',(1.916470635001,6.076625096722));
#4810 = CARTESIAN_POINT('',(1.953615373932,5.292637826035));
#4811 = CARTESIAN_POINT('',(1.99605897198,4.625085539533));
#4812 = CARTESIAN_POINT('',(2.044602529658,4.055862062085));
#4813 = CARTESIAN_POINT('',(2.100135853619,3.57052319277));
#4814 = CARTESIAN_POINT('',(2.163603060963,3.157488436848));
#4815 = CARTESIAN_POINT('',(2.235848558739,2.807506457323));
#4816 = CARTESIAN_POINT('',(2.378301459031,2.292578192696));
#4817 = CARTESIAN_POINT('',(2.444503178026,2.10324693513));
#4818 = CARTESIAN_POINT('',(2.516015624417,1.942182211821));
#4819 = CARTESIAN_POINT('',(2.592853294915,1.806911812135));
#4820 = CARTESIAN_POINT('',(2.674810446558,1.695487106298));
#4821 = CARTESIAN_POINT('',(2.761349104081,1.606421459238));
#4822 = CARTESIAN_POINT('',(2.851560499222,1.538651979402));
#4823 = CARTESIAN_POINT('',(3.083436455001,1.420820943301));
#4824 = CARTESIAN_POINT('',(3.228274777996,1.396555738247));
#4825 = CARTESIAN_POINT('',(3.374991308132,1.416499638643));
#4826 = CARTESIAN_POINT('',(3.518625777724,1.480473643941));
#4827 = CARTESIAN_POINT('',(3.653831881804,1.590196756081));
#4828 = CARTESIAN_POINT('',(3.778180652595,1.749364992471));
#4829 = CARTESIAN_POINT('',(3.890667788564,1.963921366108));
#4830 = CARTESIAN_POINT('',(4.092051492598,2.521347874967));
#4831 = CARTESIAN_POINT('',(4.180948253803,2.864217645796));
#4832 = CARTESIAN_POINT('',(4.258097967311,3.280015164643));
#4833 = CARTESIAN_POINT('',(4.32459893098,3.780327996989));
#4834 = CARTESIAN_POINT('',(4.381824168099,4.380167060364));
#4835 = CARTESIAN_POINT('',(4.431077657701,5.098818749236));
#4836 = CARTESIAN_POINT('',(4.473511131186,5.961203233812));
#4837 = CARTESIAN_POINT('',(4.510130241934,7.));
#4838 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#4839 = PCURVE('',#4840,#4845);
#4840 = PLANE('',#4841);
#4841 = AXIS2_PLACEMENT_3D('',#4842,#4843,#4844);
#4842 = CARTESIAN_POINT('',(51.008936,121.80569,28.));
#4843 = DIRECTION('',(0.998464005442,5.540424023454E-02,0.));
#4844 = DIRECTION('',(-5.540424023454E-02,0.998464005442,0.));
#4845 = DEFINITIONAL_REPRESENTATION('',(#4846),#4850);
#4846 = ( BOUNDED_CURVE() B_SPLINE_CURVE(2,(#4847,#4848,#4849),
.UNSPECIFIED.,.F.,.F.) B_SPLINE_CURVE_WITH_KNOTS((3,3),(-2.04358458549,
2.04358458549),.PIECEWISE_BEZIER_KNOTS.) CURVE() 
GEOMETRIC_REPRESENTATION_ITEM() RATIONAL_B_SPLINE_CURVE((1.,
3.923894879821,1.)) REPRESENTATION_ITEM('') );
#4847 = CARTESIAN_POINT('',(8.110503092808,-16.725137));
#4848 = CARTESIAN_POINT('',(3.936576530585,-23.71587990294));
#4849 = CARTESIAN_POINT('',(-0.237350031639,-16.725137));
#4850 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#4851 = ORIENTED_EDGE('',*,*,#4852,.F.);
#4852 = EDGE_CURVE('',#4706,#4797,#4853,.T.);
#4853 = SURFACE_CURVE('',#4854,(#4859,#4866),.PCURVE_S1.);
#4854 = CIRCLE('',#4855,4.316451884327);
#4855 = AXIS2_PLACEMENT_3D('',#4856,#4857,#4858);
#4856 = CARTESIAN_POINT('',(51.889186,125.797167,11.274863));
#4857 = DIRECTION('',(0.,0.,1.));
#4858 = DIRECTION('',(1.,0.,0.));
#4859 = PCURVE('',#4714,#4860);
#4860 = DEFINITIONAL_REPRESENTATION('',(#4861),#4865);
#4861 = LINE('',#4862,#4863);
#4862 = CARTESIAN_POINT('',(0.,7.));
#4863 = VECTOR('',#4864,1.);
#4864 = DIRECTION('',(1.,0.));
#4865 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#4866 = PCURVE('',#4750,#4867);
#4867 = DEFINITIONAL_REPRESENTATION('',(#4868),#4872);
#4868 = CIRCLE('',#4869,4.316451884327);
#4869 = AXIS2_PLACEMENT_2D('',#4870,#4871);
#4870 = CARTESIAN_POINT('',(0.,0.));
#4871 = DIRECTION('',(1.,0.));
#4872 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#4873 = ORIENTED_EDGE('',*,*,#4703,.F.);
#4874 = ORIENTED_EDGE('',*,*,#4875,.T.);
#4875 = EDGE_CURVE('',#4704,#4704,#4876,.T.);
#4876 = SURFACE_CURVE('',#4877,(#4882,#4889),.PCURVE_S1.);
#4877 = CIRCLE('',#4878,0.275);
#4878 = AXIS2_PLACEMENT_3D('',#4879,#4880,#4881);
#4879 = CARTESIAN_POINT('',(51.889186,125.797167,4.274863));
#4880 = DIRECTION('',(0.,0.,1.));
#4881 = DIRECTION('',(1.,0.,0.));
#4882 = PCURVE('',#4714,#4883);
#4883 = DEFINITIONAL_REPRESENTATION('',(#4884),#4888);
#4884 = LINE('',#4885,#4886);
#4885 = CARTESIAN_POINT('',(0.,0.));
#4886 = VECTOR('',#4887,1.);
#4887 = DIRECTION('',(1.,0.));
#4888 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#4889 = PCURVE('',#4890,#4895);
#4890 = CYLINDRICAL_SURFACE('',#4891,0.275);
#4891 = AXIS2_PLACEMENT_3D('',#4892,#4893,#4894);
#4892 = CARTESIAN_POINT('',(51.889186,125.797167,4.274863));
#4893 = DIRECTION('',(0.,0.,1.));
#4894 = DIRECTION('',(1.,0.,0.));
#4895 = DEFINITIONAL_REPRESENTATION('',(#4896),#4899);
#4896 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#4897,#4898),.UNSPECIFIED.,.F.,
  .F.,(2,2),(0.,6.28318530718),.PIECEWISE_BEZIER_KNOTS.);
#4897 = CARTESIAN_POINT('',(0.,0.));
#4898 = CARTESIAN_POINT('',(6.28318530718,0.));
#4899 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#4900 = ADVANCED_FACE('',(#4901),#4890,.F.);
#4901 = FACE_BOUND('',#4902,.F.);
#4902 = EDGE_LOOP('',(#4903,#4927,#4948,#4949));
#4903 = ORIENTED_EDGE('',*,*,#4904,.F.);
#4904 = EDGE_CURVE('',#4905,#4905,#4907,.T.);
#4905 = VERTEX_POINT('',#4906);
#4906 = CARTESIAN_POINT('',(52.164186,125.797167,11.274863));
#4907 = SURFACE_CURVE('',#4908,(#4913,#4920),.PCURVE_S1.);
#4908 = CIRCLE('',#4909,0.275);
#4909 = AXIS2_PLACEMENT_3D('',#4910,#4911,#4912);
#4910 = CARTESIAN_POINT('',(51.889186,125.797167,11.274863));
#4911 = DIRECTION('',(0.,0.,1.));
#4912 = DIRECTION('',(1.,0.,0.));
#4913 = PCURVE('',#4890,#4914);
#4914 = DEFINITIONAL_REPRESENTATION('',(#4915),#4919);
#4915 = LINE('',#4916,#4917);
#4916 = CARTESIAN_POINT('',(0.,7.));
#4917 = VECTOR('',#4918,1.);
#4918 = DIRECTION('',(1.,0.));
#4919 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#4920 = PCURVE('',#4750,#4921);
#4921 = DEFINITIONAL_REPRESENTATION('',(#4922),#4926);
#4922 = CIRCLE('',#4923,0.275);
#4923 = AXIS2_PLACEMENT_2D('',#4924,#4925);
#4924 = CARTESIAN_POINT('',(0.,0.));
#4925 = DIRECTION('',(1.,0.));
#4926 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#4927 = ORIENTED_EDGE('',*,*,#4928,.F.);
#4928 = EDGE_CURVE('',#4704,#4905,#4929,.T.);
#4929 = SEAM_CURVE('',#4930,(#4934,#4941),.PCURVE_S1.);
#4930 = LINE('',#4931,#4932);
#4931 = CARTESIAN_POINT('',(52.164186,125.797167,4.274863));
#4932 = VECTOR('',#4933,1.);
#4933 = DIRECTION('',(0.,0.,1.));
#4934 = PCURVE('',#4890,#4935);
#4935 = DEFINITIONAL_REPRESENTATION('',(#4936),#4940);
#4936 = LINE('',#4937,#4938);
#4937 = CARTESIAN_POINT('',(6.28318530718,-0.));
#4938 = VECTOR('',#4939,1.);
#4939 = DIRECTION('',(0.,1.));
#4940 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#4941 = PCURVE('',#4890,#4942);
#4942 = DEFINITIONAL_REPRESENTATION('',(#4943),#4947);
#4943 = LINE('',#4944,#4945);
#4944 = CARTESIAN_POINT('',(0.,-0.));
#4945 = VECTOR('',#4946,1.);
#4946 = DIRECTION('',(0.,1.));
#4947 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#4948 = ORIENTED_EDGE('',*,*,#4875,.T.);
#4949 = ORIENTED_EDGE('',*,*,#4928,.T.);
#4950 = ADVANCED_FACE('',(#4951,#4995),#4750,.T.);
#4951 = FACE_BOUND('',#4952,.T.);
#4952 = EDGE_LOOP('',(#4953,#4954,#4955,#4976));
#4953 = ORIENTED_EDGE('',*,*,#4733,.T.);
#4954 = ORIENTED_EDGE('',*,*,#4852,.T.);
#4955 = ORIENTED_EDGE('',*,*,#4956,.F.);
#4956 = EDGE_CURVE('',#4957,#4797,#4959,.T.);
#4957 = VERTEX_POINT('',#4958);
#4958 = CARTESIAN_POINT('',(51.008936,121.80569,11.274863));
#4959 = SURFACE_CURVE('',#4960,(#4964,#4970),.PCURVE_S1.);
#4960 = LINE('',#4961,#4962);
#4961 = CARTESIAN_POINT('',(50.899884484099,123.77095498522,11.274863));
#4962 = VECTOR('',#4963,1.);
#4963 = DIRECTION('',(-5.540424023454E-02,0.998464005442,0.));
#4964 = PCURVE('',#4750,#4965);
#4965 = DEFINITIONAL_REPRESENTATION('',(#4966),#4969);
#4966 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#4967,#4968),.UNSPECIFIED.,.F.,
  .F.,(2,2),(-1.968288265292,6.352421183464),.PIECEWISE_BEZIER_KNOTS.);
#4967 = CARTESIAN_POINT('',(-0.88025,-3.991477));
#4968 = CARTESIAN_POINT('',(-1.341252585221,4.316451884327));
#4969 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#4970 = PCURVE('',#4840,#4971);
#4971 = DEFINITIONAL_REPRESENTATION('',(#4972),#4975);
#4972 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#4973,#4974),.UNSPECIFIED.,.F.,
  .F.,(2,2),(-1.968288265292,6.352421183464),.PIECEWISE_BEZIER_KNOTS.);
#4973 = CARTESIAN_POINT('',(0.,-16.725137));
#4974 = CARTESIAN_POINT('',(8.320709448756,-16.725137));
#4975 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#4976 = ORIENTED_EDGE('',*,*,#4977,.F.);
#4977 = EDGE_CURVE('',#4734,#4957,#4978,.T.);
#4978 = SURFACE_CURVE('',#4979,(#4983,#4989),.PCURVE_S1.);
#4979 = LINE('',#4980,#4981);
#4980 = CARTESIAN_POINT('',(64.803410714327,121.80640687802,11.274863));
#4981 = VECTOR('',#4982,1.);
#4982 = DIRECTION('',(-0.99999999865,-5.196848995186E-05,0.));
#4983 = PCURVE('',#4750,#4984);
#4984 = DEFINITIONAL_REPRESENTATION('',(#4985),#4988);
#4985 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#4986,#4987),.UNSPECIFIED.,.F.,
  .F.,(2,2),(8.59777284161,13.794474732955),.PIECEWISE_BEZIER_KNOTS.);
#4986 = CARTESIAN_POINT('',(4.316451884327,-3.99120693525));
#4987 = CARTESIAN_POINT('',(-0.88025,-3.991477));
#4988 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#4989 = PCURVE('',#4784,#4990);
#4990 = DEFINITIONAL_REPRESENTATION('',(#4991),#4994);
#4991 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#4992,#4993),.UNSPECIFIED.,.F.,
  .F.,(2,2),(8.59777284161,13.794474732955),.PIECEWISE_BEZIER_KNOTS.);
#4992 = CARTESIAN_POINT('',(21.511790144721,-16.725137));
#4993 = CARTESIAN_POINT('',(26.708492036066,-16.725137));
#4994 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#4995 = FACE_BOUND('',#4996,.T.);
#4996 = EDGE_LOOP('',(#4997));
#4997 = ORIENTED_EDGE('',*,*,#4904,.F.);
#4998 = ADVANCED_FACE('',(#4999),#4840,.F.);
#4999 = FACE_BOUND('',#5000,.F.);
#5000 = EDGE_LOOP('',(#5001,#5002,#5023));
#5001 = ORIENTED_EDGE('',*,*,#4956,.F.);
#5002 = ORIENTED_EDGE('',*,*,#5003,.T.);
#5003 = EDGE_CURVE('',#4957,#4763,#5004,.T.);
#5004 = SURFACE_CURVE('',#5005,(#5009,#5016),.PCURVE_S1.);
#5005 = LINE('',#5006,#5007);
#5006 = CARTESIAN_POINT('',(51.008936,121.80569,28.));
#5007 = VECTOR('',#5008,1.);
#5008 = DIRECTION('',(0.,0.,-1.));
#5009 = PCURVE('',#4840,#5010);
#5010 = DEFINITIONAL_REPRESENTATION('',(#5011),#5015);
#5011 = LINE('',#5012,#5013);
#5012 = CARTESIAN_POINT('',(0.,0.));
#5013 = VECTOR('',#5014,1.);
#5014 = DIRECTION('',(0.,-1.));
#5015 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#5016 = PCURVE('',#4784,#5017);
#5017 = DEFINITIONAL_REPRESENTATION('',(#5018),#5022);
#5018 = LINE('',#5019,#5020);
#5019 = CARTESIAN_POINT('',(26.708492036066,0.));
#5020 = VECTOR('',#5021,1.);
#5021 = DIRECTION('',(-0.,-1.));
#5022 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#5023 = ORIENTED_EDGE('',*,*,#4796,.F.);
#5024 = ADVANCED_FACE('',(#5025),#4784,.F.);
#5025 = FACE_BOUND('',#5026,.F.);
#5026 = EDGE_LOOP('',(#5027,#5028,#5029));
#5027 = ORIENTED_EDGE('',*,*,#5003,.F.);
#5028 = ORIENTED_EDGE('',*,*,#4977,.F.);
#5029 = ORIENTED_EDGE('',*,*,#4762,.F.);
#5030 = ( GEOMETRIC_REPRESENTATION_CONTEXT(3) 
GLOBAL_UNCERTAINTY_ASSIGNED_CONTEXT((#5034)) 
GLOBAL_UNIT_ASSIGNED_CONTEXT((#5031,#5032,#5033)) REPRESENTATION_CONTEXT
('Context #1','3D Context with UNIT and UNCERTAINTY') );
#5031 = ( LENGTH_UNIT() NAMED_UNIT(*) SI_UNIT(.MILLI.,.METRE.) );
#5032 = ( NAMED_UNIT(*) PLANE_ANGLE_UNIT() SI_UNIT($,.RADIAN.) );
#5033 = ( NAMED_UNIT(*) SI_UNIT($,.STERADIAN.) SOLID_ANGLE_UNIT() );
#5034 = UNCERTAINTY_MEASURE_WITH_UNIT(LENGTH_MEASURE(1.E-07),#5031,
  'distance_accuracy_value','confusion accuracy');
#5035 = CONTEXT_DEPENDENT_SHAPE_REPRESENTATION(#5036,#5038);
#5036 = ( REPRESENTATION_RELATIONSHIP('','',#4696,#10) 
REPRESENTATION_RELATIONSHIP_WITH_TRANSFORMATION(#5037) 
SHAPE_REPRESENTATION_RELATIONSHIP() );
#5037 = ITEM_DEFINED_TRANSFORMATION('','',#11,#79);
#5038 = PRODUCT_DEFINITION_SHAPE('Placement','Placement of an item',
  #5039);
#5039 = NEXT_ASSEMBLY_USAGE_OCCURRENCE('75','=>[0:1:1:18]','',#5,#4691,$
  );
#5040 = PRODUCT_RELATED_PRODUCT_CATEGORY('part',$,(#4693));
#5041 = SHAPE_DEFINITION_REPRESENTATION(#5042,#5048);
#5042 = PRODUCT_DEFINITION_SHAPE('','',#5043);
#5043 = PRODUCT_DEFINITION('design','',#5044,#5047);
#5044 = PRODUCT_DEFINITION_FORMATION('','',#5045);
#5045 = PRODUCT('PC1-P6_Part3','PC1-P6_Part3','',(#5046));
#5046 = PRODUCT_CONTEXT('',#2,'mechanical');
#5047 = PRODUCT_DEFINITION_CONTEXT('part definition',#2,'design');
#5048 = ADVANCED_BREP_SHAPE_REPRESENTATION('',(#11,#5049),#5379);
#5049 = MANIFOLD_SOLID_BREP('',#5050);
#5050 = CLOSED_SHELL('',(#5051,#5173,#5248,#5295,#5345,#5352));
#5051 = ADVANCED_FACE('',(#5052),#5067,.T.);
#5052 = FACE_BOUND('',#5053,.T.);
#5053 = EDGE_LOOP('',(#5054,#5090,#5116,#5149));
#5054 = ORIENTED_EDGE('',*,*,#5055,.T.);
#5055 = EDGE_CURVE('',#5056,#5058,#5060,.T.);
#5056 = VERTEX_POINT('',#5057);
#5057 = CARTESIAN_POINT('',(53.53263213443,121.80582115267,11.274863));
#5058 = VERTEX_POINT('',#5059);
#5059 = CARTESIAN_POINT('',(50.559579738223,129.90373540419,11.274863));
#5060 = SURFACE_CURVE('',#5061,(#5066,#5078),.PCURVE_S1.);
#5061 = CIRCLE('',#5062,4.316451884327);
#5062 = AXIS2_PLACEMENT_3D('',#5063,#5064,#5065);
#5063 = CARTESIAN_POINT('',(51.889186,125.797167,11.274863));
#5064 = DIRECTION('',(-0.,0.,1.));
#5065 = DIRECTION('',(0.380740056526,-0.924682112597,0.));
#5066 = PCURVE('',#5067,#5072);
#5067 = CYLINDRICAL_SURFACE('',#5068,4.316451884327);
#5068 = AXIS2_PLACEMENT_3D('',#5069,#5070,#5071);
#5069 = CARTESIAN_POINT('',(51.889186,125.797167,11.274863));
#5070 = DIRECTION('',(0.,0.,1.));
#5071 = DIRECTION('',(1.,0.,0.));
#5072 = DEFINITIONAL_REPRESENTATION('',(#5073),#5077);
#5073 = LINE('',#5074,#5075);
#5074 = CARTESIAN_POINT('',(5.102985481271,0.));
#5075 = VECTOR('',#5076,1.);
#5076 = DIRECTION('',(1.,0.));
#5077 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#5078 = PCURVE('',#5079,#5084);
#5079 = PLANE('',#5080);
#5080 = AXIS2_PLACEMENT_3D('',#5081,#5082,#5083);
#5081 = CARTESIAN_POINT('',(51.889186,125.797167,11.274863));
#5082 = DIRECTION('',(0.,0.,1.));
#5083 = DIRECTION('',(1.,0.,0.));
#5084 = DEFINITIONAL_REPRESENTATION('',(#5085),#5089);
#5085 = CIRCLE('',#5086,4.316451884327);
#5086 = AXIS2_PLACEMENT_2D('',#5087,#5088);
#5087 = CARTESIAN_POINT('',(0.,0.));
#5088 = DIRECTION('',(0.380740056526,-0.924682112597));
#5089 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#5090 = ORIENTED_EDGE('',*,*,#5091,.T.);
#5091 = EDGE_CURVE('',#5058,#5092,#5094,.T.);
#5092 = VERTEX_POINT('',#5093);
#5093 = CARTESIAN_POINT('',(50.559579738223,129.90373540419,18.));
#5094 = SURFACE_CURVE('',#5095,(#5099,#5105),.PCURVE_S1.);
#5095 = LINE('',#5096,#5097);
#5096 = CARTESIAN_POINT('',(50.559579738223,129.90373540419,11.274863));
#5097 = VECTOR('',#5098,1.);
#5098 = DIRECTION('',(0.,0.,1.));
#5099 = PCURVE('',#5067,#5100);
#5100 = DEFINITIONAL_REPRESENTATION('',(#5101),#5104);
#5101 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#5102,#5103),.UNSPECIFIED.,.F.,
  .F.,(2,2),(0.,6.725137),.PIECEWISE_BEZIER_KNOTS.);
#5102 = CARTESIAN_POINT('',(8.167105621515,0.));
#5103 = CARTESIAN_POINT('',(8.167105621515,6.725137));
#5104 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#5105 = PCURVE('',#5106,#5111);
#5106 = PLANE('',#5107);
#5107 = AXIS2_PLACEMENT_3D('',#5108,#5109,#5110);
#5108 = CARTESIAN_POINT('',(51.008936,121.80569,28.));
#5109 = DIRECTION('',(0.998464005442,5.540424023454E-02,0.));
#5110 = DIRECTION('',(-5.540424023454E-02,0.998464005442,0.));
#5111 = DEFINITIONAL_REPRESENTATION('',(#5112),#5115);
#5112 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#5113,#5114),.UNSPECIFIED.,.F.,
  .F.,(2,2),(-5.999999999062E-07,6.7251376),.PIECEWISE_BEZIER_KNOTS.);
#5113 = CARTESIAN_POINT('',(8.110503092808,-16.7251376));
#5114 = CARTESIAN_POINT('',(8.110503092808,-9.9999994));
#5115 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#5116 = ORIENTED_EDGE('',*,*,#5117,.T.);
#5117 = EDGE_CURVE('',#5092,#5118,#5120,.T.);
#5118 = VERTEX_POINT('',#5119);
#5119 = CARTESIAN_POINT('',(53.53263213443,121.80582115267,18.));
#5120 = SURFACE_CURVE('',#5121,(#5126,#5133),.PCURVE_S1.);
#5121 = CIRCLE('',#5122,4.316451884327);
#5122 = AXIS2_PLACEMENT_3D('',#5123,#5124,#5125);
#5123 = CARTESIAN_POINT('',(51.889186,125.797167,18.));
#5124 = DIRECTION('',(0.,0.,-1.));
#5125 = DIRECTION('',(-0.308032221234,0.951375925006,0.));
#5126 = PCURVE('',#5067,#5127);
#5127 = DEFINITIONAL_REPRESENTATION('',(#5128),#5132);
#5128 = LINE('',#5129,#5130);
#5129 = CARTESIAN_POINT('',(8.167105621515,6.725137));
#5130 = VECTOR('',#5131,1.);
#5131 = DIRECTION('',(-1.,-0.));
#5132 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#5133 = PCURVE('',#5134,#5139);
#5134 = PLANE('',#5135);
#5135 = AXIS2_PLACEMENT_3D('',#5136,#5137,#5138);
#5136 = CARTESIAN_POINT('',(51.889186,125.797167,18.));
#5137 = DIRECTION('',(0.,0.,1.));
#5138 = DIRECTION('',(1.,0.,0.));
#5139 = DEFINITIONAL_REPRESENTATION('',(#5140),#5148);
#5140 = ( BOUNDED_CURVE() B_SPLINE_CURVE(2,(#5141,#5142,#5143,#5144,
#5145,#5146,#5147),.UNSPECIFIED.,.T.,.F.) B_SPLINE_CURVE_WITH_KNOTS((1,2
    ,2,2,2,1),(-2.094395102393,0.,2.094395102393,4.188790204786,
6.28318530718,8.377580409573),.UNSPECIFIED.) CURVE() 
GEOMETRIC_REPRESENTATION_ITEM() RATIONAL_B_SPLINE_CURVE((1.,0.5,1.,0.5,
1.,0.5,1.)) REPRESENTATION_ITEM('') );
#5141 = CARTESIAN_POINT('',(-1.329606261777,4.106568404198));
#5142 = CARTESIAN_POINT('',(5.783178859051,6.409514003657));
#5143 = CARTESIAN_POINT('',(4.221195691302,-0.901811402369));
#5144 = CARTESIAN_POINT('',(2.659212523554,-8.213136808396));
#5145 = CARTESIAN_POINT('',(-2.891589429525,-3.204757001829));
#5146 = CARTESIAN_POINT('',(-8.442391382605,1.803622804739));
#5147 = CARTESIAN_POINT('',(-1.329606261777,4.106568404198));
#5148 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#5149 = ORIENTED_EDGE('',*,*,#5150,.F.);
#5150 = EDGE_CURVE('',#5056,#5118,#5151,.T.);
#5151 = SURFACE_CURVE('',#5152,(#5156,#5162),.PCURVE_S1.);
#5152 = LINE('',#5153,#5154);
#5153 = CARTESIAN_POINT('',(53.53263213443,121.80582115267,11.274863));
#5154 = VECTOR('',#5155,1.);
#5155 = DIRECTION('',(0.,0.,1.));
#5156 = PCURVE('',#5067,#5157);
#5157 = DEFINITIONAL_REPRESENTATION('',(#5158),#5161);
#5158 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#5159,#5160),.UNSPECIFIED.,.F.,
  .F.,(2,2),(0.,6.725137),.PIECEWISE_BEZIER_KNOTS.);
#5159 = CARTESIAN_POINT('',(5.102985481271,0.));
#5160 = CARTESIAN_POINT('',(5.102985481271,6.725137));
#5161 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#5162 = PCURVE('',#5163,#5168);
#5163 = PLANE('',#5164);
#5164 = AXIS2_PLACEMENT_3D('',#5165,#5166,#5167);
#5165 = CARTESIAN_POINT('',(77.717428,121.807078,28.));
#5166 = DIRECTION('',(-5.196848995186E-05,0.99999999865,0.));
#5167 = DIRECTION('',(-0.99999999865,-5.196848995186E-05,0.));
#5168 = DEFINITIONAL_REPRESENTATION('',(#5169),#5172);
#5169 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#5170,#5171),.UNSPECIFIED.,.F.,
  .F.,(2,2),(-5.999999999062E-07,6.7251376),.PIECEWISE_BEZIER_KNOTS.);
#5170 = CARTESIAN_POINT('',(24.184795898228,-16.7251376));
#5171 = CARTESIAN_POINT('',(24.184795898228,-9.9999994));
#5172 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#5173 = ADVANCED_FACE('',(#5174,#5217),#5079,.F.);
#5174 = FACE_BOUND('',#5175,.F.);
#5175 = EDGE_LOOP('',(#5176,#5177,#5198));
#5176 = ORIENTED_EDGE('',*,*,#5055,.T.);
#5177 = ORIENTED_EDGE('',*,*,#5178,.F.);
#5178 = EDGE_CURVE('',#5179,#5058,#5181,.T.);
#5179 = VERTEX_POINT('',#5180);
#5180 = CARTESIAN_POINT('',(51.008936,121.80569,11.274863));
#5181 = SURFACE_CURVE('',#5182,(#5186,#5192),.PCURVE_S1.);
#5182 = LINE('',#5183,#5184);
#5183 = CARTESIAN_POINT('',(50.899884484099,123.77095498522,11.274863));
#5184 = VECTOR('',#5185,1.);
#5185 = DIRECTION('',(-5.540424023454E-02,0.998464005442,0.));
#5186 = PCURVE('',#5079,#5187);
#5187 = DEFINITIONAL_REPRESENTATION('',(#5188),#5191);
#5188 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#5189,#5190),.UNSPECIFIED.,.F.,
  .F.,(2,2),(-1.968288265292,6.352421183464),.PIECEWISE_BEZIER_KNOTS.);
#5189 = CARTESIAN_POINT('',(-0.88025,-3.991477));
#5190 = CARTESIAN_POINT('',(-1.341252585221,4.316451884327));
#5191 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#5192 = PCURVE('',#5106,#5193);
#5193 = DEFINITIONAL_REPRESENTATION('',(#5194),#5197);
#5194 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#5195,#5196),.UNSPECIFIED.,.F.,
  .F.,(2,2),(-1.968288265292,6.352421183464),.PIECEWISE_BEZIER_KNOTS.);
#5195 = CARTESIAN_POINT('',(0.,-16.725137));
#5196 = CARTESIAN_POINT('',(8.320709448756,-16.725137));
#5197 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#5198 = ORIENTED_EDGE('',*,*,#5199,.F.);
#5199 = EDGE_CURVE('',#5056,#5179,#5200,.T.);
#5200 = SURFACE_CURVE('',#5201,(#5205,#5211),.PCURVE_S1.);
#5201 = LINE('',#5202,#5203);
#5202 = CARTESIAN_POINT('',(64.803410714327,121.80640687802,11.274863));
#5203 = VECTOR('',#5204,1.);
#5204 = DIRECTION('',(-0.99999999865,-5.196848995186E-05,0.));
#5205 = PCURVE('',#5079,#5206);
#5206 = DEFINITIONAL_REPRESENTATION('',(#5207),#5210);
#5207 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#5208,#5209),.UNSPECIFIED.,.F.,
  .F.,(2,2),(8.59777284161,13.794474732955),.PIECEWISE_BEZIER_KNOTS.);
#5208 = CARTESIAN_POINT('',(4.316451884327,-3.99120693525));
#5209 = CARTESIAN_POINT('',(-0.88025,-3.991477));
#5210 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#5211 = PCURVE('',#5163,#5212);
#5212 = DEFINITIONAL_REPRESENTATION('',(#5213),#5216);
#5213 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#5214,#5215),.UNSPECIFIED.,.F.,
  .F.,(2,2),(8.59777284161,13.794474732955),.PIECEWISE_BEZIER_KNOTS.);
#5214 = CARTESIAN_POINT('',(21.511790144721,-16.725137));
#5215 = CARTESIAN_POINT('',(26.708492036066,-16.725137));
#5216 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#5217 = FACE_BOUND('',#5218,.F.);
#5218 = EDGE_LOOP('',(#5219));
#5219 = ORIENTED_EDGE('',*,*,#5220,.F.);
#5220 = EDGE_CURVE('',#5221,#5221,#5223,.T.);
#5221 = VERTEX_POINT('',#5222);
#5222 = CARTESIAN_POINT('',(52.164186,125.797167,11.274863));
#5223 = SURFACE_CURVE('',#5224,(#5229,#5236),.PCURVE_S1.);
#5224 = CIRCLE('',#5225,0.275);
#5225 = AXIS2_PLACEMENT_3D('',#5226,#5227,#5228);
#5226 = CARTESIAN_POINT('',(51.889186,125.797167,11.274863));
#5227 = DIRECTION('',(0.,0.,1.));
#5228 = DIRECTION('',(1.,0.,0.));
#5229 = PCURVE('',#5079,#5230);
#5230 = DEFINITIONAL_REPRESENTATION('',(#5231),#5235);
#5231 = CIRCLE('',#5232,0.275);
#5232 = AXIS2_PLACEMENT_2D('',#5233,#5234);
#5233 = CARTESIAN_POINT('',(0.,0.));
#5234 = DIRECTION('',(1.,0.));
#5235 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#5236 = PCURVE('',#5237,#5242);
#5237 = CYLINDRICAL_SURFACE('',#5238,0.275);
#5238 = AXIS2_PLACEMENT_3D('',#5239,#5240,#5241);
#5239 = CARTESIAN_POINT('',(51.889186,125.797167,11.274863));
#5240 = DIRECTION('',(0.,0.,1.));
#5241 = DIRECTION('',(1.,0.,0.));
#5242 = DEFINITIONAL_REPRESENTATION('',(#5243),#5247);
#5243 = LINE('',#5244,#5245);
#5244 = CARTESIAN_POINT('',(0.,0.));
#5245 = VECTOR('',#5246,1.);
#5246 = DIRECTION('',(1.,0.));
#5247 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#5248 = ADVANCED_FACE('',(#5249),#5163,.F.);
#5249 = FACE_BOUND('',#5250,.F.);
#5250 = EDGE_LOOP('',(#5251,#5274,#5293,#5294));
#5251 = ORIENTED_EDGE('',*,*,#5252,.F.);
#5252 = EDGE_CURVE('',#5253,#5179,#5255,.T.);
#5253 = VERTEX_POINT('',#5254);
#5254 = CARTESIAN_POINT('',(51.008936,121.80569,18.));
#5255 = SURFACE_CURVE('',#5256,(#5260,#5267),.PCURVE_S1.);
#5256 = LINE('',#5257,#5258);
#5257 = CARTESIAN_POINT('',(51.008936,121.80569,28.));
#5258 = VECTOR('',#5259,1.);
#5259 = DIRECTION('',(0.,0.,-1.));
#5260 = PCURVE('',#5163,#5261);
#5261 = DEFINITIONAL_REPRESENTATION('',(#5262),#5266);
#5262 = LINE('',#5263,#5264);
#5263 = CARTESIAN_POINT('',(26.708492036066,0.));
#5264 = VECTOR('',#5265,1.);
#5265 = DIRECTION('',(-0.,-1.));
#5266 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#5267 = PCURVE('',#5106,#5268);
#5268 = DEFINITIONAL_REPRESENTATION('',(#5269),#5273);
#5269 = LINE('',#5270,#5271);
#5270 = CARTESIAN_POINT('',(0.,0.));
#5271 = VECTOR('',#5272,1.);
#5272 = DIRECTION('',(0.,-1.));
#5273 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#5274 = ORIENTED_EDGE('',*,*,#5275,.F.);
#5275 = EDGE_CURVE('',#5118,#5253,#5276,.T.);
#5276 = SURFACE_CURVE('',#5277,(#5281,#5287),.PCURVE_S1.);
#5277 = LINE('',#5278,#5279);
#5278 = CARTESIAN_POINT('',(64.803410714327,121.80640687802,18.));
#5279 = VECTOR('',#5280,1.);
#5280 = DIRECTION('',(-0.99999999865,-5.196848995186E-05,0.));
#5281 = PCURVE('',#5163,#5282);
#5282 = DEFINITIONAL_REPRESENTATION('',(#5283),#5286);
#5283 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#5284,#5285),.UNSPECIFIED.,.F.,
  .F.,(2,2),(8.59777284161,13.794474732955),.PIECEWISE_BEZIER_KNOTS.);
#5284 = CARTESIAN_POINT('',(21.511790144721,-10.));
#5285 = CARTESIAN_POINT('',(26.708492036066,-10.));
#5286 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#5287 = PCURVE('',#5134,#5288);
#5288 = DEFINITIONAL_REPRESENTATION('',(#5289),#5292);
#5289 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#5290,#5291),.UNSPECIFIED.,.F.,
  .F.,(2,2),(8.59777284161,13.794474732955),.PIECEWISE_BEZIER_KNOTS.);
#5290 = CARTESIAN_POINT('',(4.316451884327,-3.99120693525));
#5291 = CARTESIAN_POINT('',(-0.88025,-3.991477));
#5292 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#5293 = ORIENTED_EDGE('',*,*,#5150,.F.);
#5294 = ORIENTED_EDGE('',*,*,#5199,.T.);
#5295 = ADVANCED_FACE('',(#5296,#5319),#5134,.T.);
#5296 = FACE_BOUND('',#5297,.T.);
#5297 = EDGE_LOOP('',(#5298,#5299,#5300));
#5298 = ORIENTED_EDGE('',*,*,#5275,.F.);
#5299 = ORIENTED_EDGE('',*,*,#5117,.F.);
#5300 = ORIENTED_EDGE('',*,*,#5301,.F.);
#5301 = EDGE_CURVE('',#5253,#5092,#5302,.T.);
#5302 = SURFACE_CURVE('',#5303,(#5307,#5313),.PCURVE_S1.);
#5303 = LINE('',#5304,#5305);
#5304 = CARTESIAN_POINT('',(50.899884484099,123.77095498522,18.));
#5305 = VECTOR('',#5306,1.);
#5306 = DIRECTION('',(-5.540424023454E-02,0.998464005442,0.));
#5307 = PCURVE('',#5134,#5308);
#5308 = DEFINITIONAL_REPRESENTATION('',(#5309),#5312);
#5309 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#5310,#5311),.UNSPECIFIED.,.F.,
  .F.,(2,2),(-1.968288265292,6.352421183464),.PIECEWISE_BEZIER_KNOTS.);
#5310 = CARTESIAN_POINT('',(-0.88025,-3.991477));
#5311 = CARTESIAN_POINT('',(-1.341252585221,4.316451884327));
#5312 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#5313 = PCURVE('',#5106,#5314);
#5314 = DEFINITIONAL_REPRESENTATION('',(#5315),#5318);
#5315 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#5316,#5317),.UNSPECIFIED.,.F.,
  .F.,(2,2),(-1.968288265292,6.352421183464),.PIECEWISE_BEZIER_KNOTS.);
#5316 = CARTESIAN_POINT('',(0.,-10.));
#5317 = CARTESIAN_POINT('',(8.320709448756,-10.));
#5318 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#5319 = FACE_BOUND('',#5320,.T.);
#5320 = EDGE_LOOP('',(#5321));
#5321 = ORIENTED_EDGE('',*,*,#5322,.F.);
#5322 = EDGE_CURVE('',#5323,#5323,#5325,.T.);
#5323 = VERTEX_POINT('',#5324);
#5324 = CARTESIAN_POINT('',(52.164186,125.797167,18.));
#5325 = SURFACE_CURVE('',#5326,(#5331,#5338),.PCURVE_S1.);
#5326 = CIRCLE('',#5327,0.275);
#5327 = AXIS2_PLACEMENT_3D('',#5328,#5329,#5330);
#5328 = CARTESIAN_POINT('',(51.889186,125.797167,18.));
#5329 = DIRECTION('',(0.,0.,1.));
#5330 = DIRECTION('',(1.,0.,0.));
#5331 = PCURVE('',#5134,#5332);
#5332 = DEFINITIONAL_REPRESENTATION('',(#5333),#5337);
#5333 = CIRCLE('',#5334,0.275);
#5334 = AXIS2_PLACEMENT_2D('',#5335,#5336);
#5335 = CARTESIAN_POINT('',(0.,0.));
#5336 = DIRECTION('',(1.,0.));
#5337 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#5338 = PCURVE('',#5237,#5339);
#5339 = DEFINITIONAL_REPRESENTATION('',(#5340),#5344);
#5340 = LINE('',#5341,#5342);
#5341 = CARTESIAN_POINT('',(0.,6.725137));
#5342 = VECTOR('',#5343,1.);
#5343 = DIRECTION('',(1.,0.));
#5344 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#5345 = ADVANCED_FACE('',(#5346),#5106,.F.);
#5346 = FACE_BOUND('',#5347,.F.);
#5347 = EDGE_LOOP('',(#5348,#5349,#5350,#5351));
#5348 = ORIENTED_EDGE('',*,*,#5301,.F.);
#5349 = ORIENTED_EDGE('',*,*,#5252,.T.);
#5350 = ORIENTED_EDGE('',*,*,#5178,.T.);
#5351 = ORIENTED_EDGE('',*,*,#5091,.T.);
#5352 = ADVANCED_FACE('',(#5353),#5237,.F.);
#5353 = FACE_BOUND('',#5354,.F.);
#5354 = EDGE_LOOP('',(#5355,#5356,#5377,#5378));
#5355 = ORIENTED_EDGE('',*,*,#5322,.F.);
#5356 = ORIENTED_EDGE('',*,*,#5357,.F.);
#5357 = EDGE_CURVE('',#5221,#5323,#5358,.T.);
#5358 = SEAM_CURVE('',#5359,(#5363,#5370),.PCURVE_S1.);
#5359 = LINE('',#5360,#5361);
#5360 = CARTESIAN_POINT('',(52.164186,125.797167,11.274863));
#5361 = VECTOR('',#5362,1.);
#5362 = DIRECTION('',(0.,0.,1.));
#5363 = PCURVE('',#5237,#5364);
#5364 = DEFINITIONAL_REPRESENTATION('',(#5365),#5369);
#5365 = LINE('',#5366,#5367);
#5366 = CARTESIAN_POINT('',(6.28318530718,-0.));
#5367 = VECTOR('',#5368,1.);
#5368 = DIRECTION('',(0.,1.));
#5369 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#5370 = PCURVE('',#5237,#5371);
#5371 = DEFINITIONAL_REPRESENTATION('',(#5372),#5376);
#5372 = LINE('',#5373,#5374);
#5373 = CARTESIAN_POINT('',(0.,-0.));
#5374 = VECTOR('',#5375,1.);
#5375 = DIRECTION('',(0.,1.));
#5376 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#5377 = ORIENTED_EDGE('',*,*,#5220,.T.);
#5378 = ORIENTED_EDGE('',*,*,#5357,.T.);
#5379 = ( GEOMETRIC_REPRESENTATION_CONTEXT(3) 
GLOBAL_UNCERTAINTY_ASSIGNED_CONTEXT((#5383)) 
GLOBAL_UNIT_ASSIGNED_CONTEXT((#5380,#5381,#5382)) REPRESENTATION_CONTEXT
('Context #1','3D Context with UNIT and UNCERTAINTY') );
#5380 = ( LENGTH_UNIT() NAMED_UNIT(*) SI_UNIT(.MILLI.,.METRE.) );
#5381 = ( NAMED_UNIT(*) PLANE_ANGLE_UNIT() SI_UNIT($,.RADIAN.) );
#5382 = ( NAMED_UNIT(*) SI_UNIT($,.STERADIAN.) SOLID_ANGLE_UNIT() );
#5383 = UNCERTAINTY_MEASURE_WITH_UNIT(LENGTH_MEASURE(1.E-07),#5380,
  'distance_accuracy_value','confusion accuracy');
#5384 = CONTEXT_DEPENDENT_SHAPE_REPRESENTATION(#5385,#5387);
#5385 = ( REPRESENTATION_RELATIONSHIP('','',#5048,#10) 
REPRESENTATION_RELATIONSHIP_WITH_TRANSFORMATION(#5386) 
SHAPE_REPRESENTATION_RELATIONSHIP() );
#5386 = ITEM_DEFINED_TRANSFORMATION('','',#11,#83);
#5387 = PRODUCT_DEFINITION_SHAPE('Placement','Placement of an item',
  #5388);
#5388 = NEXT_ASSEMBLY_USAGE_OCCURRENCE('76','=>[0:1:1:19]','',#5,#5043,$
  );
#5389 = PRODUCT_RELATED_PRODUCT_CATEGORY('part',$,(#5045));
#5390 = SHAPE_DEFINITION_REPRESENTATION(#5391,#5397);
#5391 = PRODUCT_DEFINITION_SHAPE('','',#5392);
#5392 = PRODUCT_DEFINITION('design','',#5393,#5396);
#5393 = PRODUCT_DEFINITION_FORMATION('','',#5394);
#5394 = PRODUCT('PC1-P7_Part1','PC1-P7_Part1','',(#5395));
#5395 = PRODUCT_CONTEXT('',#2,'mechanical');
#5396 = PRODUCT_DEFINITION_CONTEXT('part definition',#2,'design');
#5397 = ADVANCED_BREP_SHAPE_REPRESENTATION('',(#11,#5398),#5496);
#5398 = MANIFOLD_SOLID_BREP('',#5399);
#5399 = CLOSED_SHELL('',(#5400,#5488,#5492));
#5400 = ADVANCED_FACE('',(#5401),#5414,.T.);
#5401 = FACE_BOUND('',#5402,.T.);
#5402 = EDGE_LOOP('',(#5403,#5437,#5460,#5487));
#5403 = ORIENTED_EDGE('',*,*,#5404,.F.);
#5404 = EDGE_CURVE('',#5405,#5405,#5407,.T.);
#5405 = VERTEX_POINT('',#5406);
#5406 = CARTESIAN_POINT('',(0.275,-6.73555739531E-17,18.072622));
#5407 = SURFACE_CURVE('',#5408,(#5413,#5425),.PCURVE_S1.);
#5408 = CIRCLE('',#5409,0.275);
#5409 = AXIS2_PLACEMENT_3D('',#5410,#5411,#5412);
#5410 = CARTESIAN_POINT('',(0.,0.,18.072622));
#5411 = DIRECTION('',(0.,0.,1.));
#5412 = DIRECTION('',(1.,0.,-0.));
#5413 = PCURVE('',#5414,#5419);
#5414 = CYLINDRICAL_SURFACE('',#5415,0.275);
#5415 = AXIS2_PLACEMENT_3D('',#5416,#5417,#5418);
#5416 = CARTESIAN_POINT('',(0.,0.,0.));
#5417 = DIRECTION('',(0.,0.,1.));
#5418 = DIRECTION('',(1.,0.,-0.));
#5419 = DEFINITIONAL_REPRESENTATION('',(#5420),#5424);
#5420 = LINE('',#5421,#5422);
#5421 = CARTESIAN_POINT('',(0.,18.072622));
#5422 = VECTOR('',#5423,1.);
#5423 = DIRECTION('',(1.,0.));
#5424 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#5425 = PCURVE('',#5426,#5431);
#5426 = PLANE('',#5427);
#5427 = AXIS2_PLACEMENT_3D('',#5428,#5429,#5430);
#5428 = CARTESIAN_POINT('',(0.,0.,18.072622));
#5429 = DIRECTION('',(0.,0.,1.));
#5430 = DIRECTION('',(1.,0.,-0.));
#5431 = DEFINITIONAL_REPRESENTATION('',(#5432),#5436);
#5432 = CIRCLE('',#5433,0.275);
#5433 = AXIS2_PLACEMENT_2D('',#5434,#5435);
#5434 = CARTESIAN_POINT('',(0.,0.));
#5435 = DIRECTION('',(1.,0.));
#5436 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#5437 = ORIENTED_EDGE('',*,*,#5438,.F.);
#5438 = EDGE_CURVE('',#5439,#5405,#5441,.T.);
#5439 = VERTEX_POINT('',#5440);
#5440 = CARTESIAN_POINT('',(0.275,-6.73555739531E-17,0.));
#5441 = SEAM_CURVE('',#5442,(#5446,#5453),.PCURVE_S1.);
#5442 = LINE('',#5443,#5444);
#5443 = CARTESIAN_POINT('',(0.275,-6.73555739531E-17,0.));
#5444 = VECTOR('',#5445,1.);
#5445 = DIRECTION('',(0.,0.,1.));
#5446 = PCURVE('',#5414,#5447);
#5447 = DEFINITIONAL_REPRESENTATION('',(#5448),#5452);
#5448 = LINE('',#5449,#5450);
#5449 = CARTESIAN_POINT('',(6.28318530718,-0.));
#5450 = VECTOR('',#5451,1.);
#5451 = DIRECTION('',(0.,1.));
#5452 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#5453 = PCURVE('',#5414,#5454);
#5454 = DEFINITIONAL_REPRESENTATION('',(#5455),#5459);
#5455 = LINE('',#5456,#5457);
#5456 = CARTESIAN_POINT('',(0.,-0.));
#5457 = VECTOR('',#5458,1.);
#5458 = DIRECTION('',(0.,1.));
#5459 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#5460 = ORIENTED_EDGE('',*,*,#5461,.T.);
#5461 = EDGE_CURVE('',#5439,#5439,#5462,.T.);
#5462 = SURFACE_CURVE('',#5463,(#5468,#5475),.PCURVE_S1.);
#5463 = CIRCLE('',#5464,0.275);
#5464 = AXIS2_PLACEMENT_3D('',#5465,#5466,#5467);
#5465 = CARTESIAN_POINT('',(0.,0.,0.));
#5466 = DIRECTION('',(0.,0.,1.));
#5467 = DIRECTION('',(1.,0.,-0.));
#5468 = PCURVE('',#5414,#5469);
#5469 = DEFINITIONAL_REPRESENTATION('',(#5470),#5474);
#5470 = LINE('',#5471,#5472);
#5471 = CARTESIAN_POINT('',(0.,0.));
#5472 = VECTOR('',#5473,1.);
#5473 = DIRECTION('',(1.,0.));
#5474 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#5475 = PCURVE('',#5476,#5481);
#5476 = PLANE('',#5477);
#5477 = AXIS2_PLACEMENT_3D('',#5478,#5479,#5480);
#5478 = CARTESIAN_POINT('',(0.,0.,0.));
#5479 = DIRECTION('',(0.,0.,1.));
#5480 = DIRECTION('',(1.,0.,-0.));
#5481 = DEFINITIONAL_REPRESENTATION('',(#5482),#5486);
#5482 = CIRCLE('',#5483,0.275);
#5483 = AXIS2_PLACEMENT_2D('',#5484,#5485);
#5484 = CARTESIAN_POINT('',(0.,0.));
#5485 = DIRECTION('',(1.,0.));
#5486 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#5487 = ORIENTED_EDGE('',*,*,#5438,.T.);
#5488 = ADVANCED_FACE('',(#5489),#5426,.T.);
#5489 = FACE_BOUND('',#5490,.T.);
#5490 = EDGE_LOOP('',(#5491));
#5491 = ORIENTED_EDGE('',*,*,#5404,.T.);
#5492 = ADVANCED_FACE('',(#5493),#5476,.F.);
#5493 = FACE_BOUND('',#5494,.T.);
#5494 = EDGE_LOOP('',(#5495));
#5495 = ORIENTED_EDGE('',*,*,#5461,.F.);
#5496 = ( GEOMETRIC_REPRESENTATION_CONTEXT(3) 
GLOBAL_UNCERTAINTY_ASSIGNED_CONTEXT((#5500)) 
GLOBAL_UNIT_ASSIGNED_CONTEXT((#5497,#5498,#5499)) REPRESENTATION_CONTEXT
('Context #1','3D Context with UNIT and UNCERTAINTY') );
#5497 = ( LENGTH_UNIT() NAMED_UNIT(*) SI_UNIT(.MILLI.,.METRE.) );
#5498 = ( NAMED_UNIT(*) PLANE_ANGLE_UNIT() SI_UNIT($,.RADIAN.) );
#5499 = ( NAMED_UNIT(*) SI_UNIT($,.STERADIAN.) SOLID_ANGLE_UNIT() );
#5500 = UNCERTAINTY_MEASURE_WITH_UNIT(LENGTH_MEASURE(1.E-07),#5497,
  'distance_accuracy_value','confusion accuracy');
#5501 = CONTEXT_DEPENDENT_SHAPE_REPRESENTATION(#5502,#5504);
#5502 = ( REPRESENTATION_RELATIONSHIP('','',#5397,#10) 
REPRESENTATION_RELATIONSHIP_WITH_TRANSFORMATION(#5503) 
SHAPE_REPRESENTATION_RELATIONSHIP() );
#5503 = ITEM_DEFINED_TRANSFORMATION('','',#11,#87);
#5504 = PRODUCT_DEFINITION_SHAPE('Placement','Placement of an item',
  #5505);
#5505 = NEXT_ASSEMBLY_USAGE_OCCURRENCE('77','=>[0:1:1:20]','',#5,#5392,$
  );
#5506 = PRODUCT_RELATED_PRODUCT_CATEGORY('part',$,(#5394));
#5507 = SHAPE_DEFINITION_REPRESENTATION(#5508,#5514);
#5508 = PRODUCT_DEFINITION_SHAPE('','',#5509);
#5509 = PRODUCT_DEFINITION('design','',#5510,#5513);
#5510 = PRODUCT_DEFINITION_FORMATION('','',#5511);
#5511 = PRODUCT('PC1-P7_Part2','PC1-P7_Part2','',(#5512));
#5512 = PRODUCT_CONTEXT('',#2,'mechanical');
#5513 = PRODUCT_DEFINITION_CONTEXT('part definition',#2,'design');
#5514 = ADVANCED_BREP_SHAPE_REPRESENTATION('',(#11,#5515),#5873);
#5515 = MANIFOLD_SOLID_BREP('',#5516);
#5516 = CLOSED_SHELL('',(#5517,#5743,#5793,#5841,#5867));
#5517 = ADVANCED_FACE('',(#5518),#5532,.T.);
#5518 = FACE_BOUND('',#5519,.T.);
#5519 = EDGE_LOOP('',(#5520,#5550,#5579,#5631,#5694,#5716,#5717));
#5520 = ORIENTED_EDGE('',*,*,#5521,.T.);
#5521 = EDGE_CURVE('',#5522,#5524,#5526,.T.);
#5522 = VERTEX_POINT('',#5523);
#5523 = CARTESIAN_POINT('',(52.255608,124.149601,-7.2622E-02));
#5524 = VERTEX_POINT('',#5525);
#5525 = CARTESIAN_POINT('',(59.183811230276,124.149601,11.927378));
#5526 = SEAM_CURVE('',#5527,(#5531,#5543),.PCURVE_S1.);
#5527 = LINE('',#5528,#5529);
#5528 = CARTESIAN_POINT('',(52.255608,124.149601,-7.2622E-02));
#5529 = VECTOR('',#5530,1.);
#5530 = DIRECTION('',(0.5,-1.224646799147E-16,0.866025403784));
#5531 = PCURVE('',#5532,#5537);
#5532 = CONICAL_SURFACE('',#5533,0.275,0.523598775598);
#5533 = AXIS2_PLACEMENT_3D('',#5534,#5535,#5536);
#5534 = CARTESIAN_POINT('',(51.980608,124.149601,-7.2622E-02));
#5535 = DIRECTION('',(0.,0.,1.));
#5536 = DIRECTION('',(1.,0.,0.));
#5537 = DEFINITIONAL_REPRESENTATION('',(#5538),#5542);
#5538 = LINE('',#5539,#5540);
#5539 = CARTESIAN_POINT('',(6.28318530718,-0.));
#5540 = VECTOR('',#5541,1.);
#5541 = DIRECTION('',(0.,1.));
#5542 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#5543 = PCURVE('',#5532,#5544);
#5544 = DEFINITIONAL_REPRESENTATION('',(#5545),#5549);
#5545 = LINE('',#5546,#5547);
#5546 = CARTESIAN_POINT('',(0.,-0.));
#5547 = VECTOR('',#5548,1.);
#5548 = DIRECTION('',(0.,1.));
#5549 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#5550 = ORIENTED_EDGE('',*,*,#5551,.F.);
#5551 = EDGE_CURVE('',#5552,#5524,#5554,.T.);
#5552 = VERTEX_POINT('',#5553);
#5553 = CARTESIAN_POINT('',(58.791930479718,121.80609447047,11.927378));
#5554 = SURFACE_CURVE('',#5555,(#5560,#5567),.PCURVE_S1.);
#5555 = CIRCLE('',#5556,7.203203230276);
#5556 = AXIS2_PLACEMENT_3D('',#5557,#5558,#5559);
#5557 = CARTESIAN_POINT('',(51.980608,124.149601,11.927378));
#5558 = DIRECTION('',(0.,0.,1.));
#5559 = DIRECTION('',(1.,0.,0.));
#5560 = PCURVE('',#5532,#5561);
#5561 = DEFINITIONAL_REPRESENTATION('',(#5562),#5566);
#5562 = LINE('',#5563,#5564);
#5563 = CARTESIAN_POINT('',(0.,12.));
#5564 = VECTOR('',#5565,1.);
#5565 = DIRECTION('',(1.,0.));
#5566 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#5567 = PCURVE('',#5568,#5573);
#5568 = PLANE('',#5569);
#5569 = AXIS2_PLACEMENT_3D('',#5570,#5571,#5572);
#5570 = CARTESIAN_POINT('',(51.980608,124.149601,11.927378));
#5571 = DIRECTION('',(0.,0.,1.));
#5572 = DIRECTION('',(1.,0.,0.));
#5573 = DEFINITIONAL_REPRESENTATION('',(#5574),#5578);
#5574 = CIRCLE('',#5575,7.203203230276);
#5575 = AXIS2_PLACEMENT_2D('',#5576,#5577);
#5576 = CARTESIAN_POINT('',(0.,0.));
#5577 = DIRECTION('',(1.,0.));
#5578 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#5579 = ORIENTED_EDGE('',*,*,#5580,.F.);
#5580 = EDGE_CURVE('',#5581,#5552,#5583,.T.);
#5581 = VERTEX_POINT('',#5582);
#5582 = CARTESIAN_POINT('',(51.008936,121.80569,3.845855917703));
#5583 = SURFACE_CURVE('',#5584,(#5589,#5619),.PCURVE_S1.);
#5584 = HYPERBOLA('',#5585,4.059685472734,2.343860500508);
#5585 = AXIS2_PLACEMENT_3D('',#5586,#5587,#5588);
#5586 = CARTESIAN_POINT('',(51.980729806891,121.80574050265,
    -0.548935972081));
#5587 = DIRECTION('',(-5.196848995186E-05,0.99999999865,0.));
#5588 = DIRECTION('',(0.,0.,1.));
#5589 = PCURVE('',#5532,#5590);
#5590 = DEFINITIONAL_REPRESENTATION('',(#5591),#5618);
#5591 = B_SPLINE_CURVE_WITH_KNOTS('',7,(#5592,#5593,#5594,#5595,#5596,
    #5597,#5598,#5599,#5600,#5601,#5602,#5603,#5604,#5605,#5606,#5607,
    #5608,#5609,#5610,#5611,#5612,#5613,#5614,#5615,#5616,#5617),
  .UNSPECIFIED.,.F.,.F.,(8,6,6,6,8),(-1.22441406126,-0.471238972175,
    9.364234463917E-02,0.94096431986,1.788286295081),.UNSPECIFIED.);
#5592 = CARTESIAN_POINT('',(3.71340184343,7.026191640845));
#5593 = CARTESIAN_POINT('',(3.771623420619,6.347339571477));
#5594 = CARTESIAN_POINT('',(3.835990844919,5.769820020681));
#5595 = CARTESIAN_POINT('',(3.906962406363,5.280796698447));
#5596 = CARTESIAN_POINT('',(3.984854207059,4.870306552375));
#5597 = CARTESIAN_POINT('',(4.069735200621,4.530656668208));
#5598 = CARTESIAN_POINT('',(4.161069631221,4.25607866044));
#5599 = CARTESIAN_POINT('',(4.330230759128,3.882376250535));
#5600 = CARTESIAN_POINT('',(4.405725631994,3.756549571396));
#5601 = CARTESIAN_POINT('',(4.483746740301,3.663350969701));
#5602 = CARTESIAN_POINT('',(4.563631262479,3.601624468143));
#5603 = CARTESIAN_POINT('',(4.644551482922,3.570717122737));
#5604 = CARTESIAN_POINT('',(4.725601930072,3.570461348077));
#5605 = CARTESIAN_POINT('',(4.926463945447,3.64726791864));
#5606 = CARTESIAN_POINT('',(5.045390819341,3.763053198796));
#5607 = CARTESIAN_POINT('',(5.159904082211,3.949643062945));
#5608 = CARTESIAN_POINT('',(5.267406685944,4.210641048973));
#5609 = CARTESIAN_POINT('',(5.366590139749,4.552224170603));
#5610 = CARTESIAN_POINT('',(5.457098902746,4.983488964544));
#5611 = CARTESIAN_POINT('',(5.621078840878,6.050926847065));
#5612 = CARTESIAN_POINT('',(5.694551014176,6.687099899795));
#5613 = CARTESIAN_POINT('',(5.759665242227,7.438500601502));
#5614 = CARTESIAN_POINT('',(5.817127229927,8.321577088372));
#5615 = CARTESIAN_POINT('',(5.867777530952,9.35722904496));
#5616 = CARTESIAN_POINT('',(5.912424305561,10.571978541202));
#5617 = CARTESIAN_POINT('',(5.951811633779,12.));
#5618 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#5619 = PCURVE('',#5620,#5625);
#5620 = PLANE('',#5621);
#5621 = AXIS2_PLACEMENT_3D('',#5622,#5623,#5624);
#5622 = CARTESIAN_POINT('',(77.717428,121.807078,28.));
#5623 = DIRECTION('',(-5.196848995186E-05,0.99999999865,0.));
#5624 = DIRECTION('',(-0.99999999865,-5.196848995186E-05,0.));
#5625 = DEFINITIONAL_REPRESENTATION('',(#5626),#5630);
#5626 = ( BOUNDED_CURVE() B_SPLINE_CURVE(2,(#5627,#5628,#5629),
.UNSPECIFIED.,.F.,.F.) B_SPLINE_CURVE_WITH_KNOTS((3,3),(-1.22441406126,
1.788286295081),.PIECEWISE_BEZIER_KNOTS.) CURVE() 
GEOMETRIC_REPRESENTATION_ITEM() RATIONAL_B_SPLINE_CURVE((1.,
2.365978440371,1.)) REPRESENTATION_ITEM('') );
#5627 = CARTESIAN_POINT('',(29.379341239673,-21.04643035915));
#5628 = CARTESIAN_POINT('',(25.453682836486,-26.76442893998));
#5629 = CARTESIAN_POINT('',(18.925497545839,-16.072622));
#5630 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#5631 = ORIENTED_EDGE('',*,*,#5632,.F.);
#5632 = EDGE_CURVE('',#5633,#5581,#5635,.T.);
#5633 = VERTEX_POINT('',#5634);
#5634 = CARTESIAN_POINT('',(50.487848767654,131.19643054603,11.927378));
#5635 = SURFACE_CURVE('',#5636,(#5641,#5682),.PCURVE_S1.);
#5636 = HYPERBOLA('',#5637,1.905328851362,1.100042125229);
#5637 = AXIS2_PLACEMENT_3D('',#5638,#5639,#5640);
#5638 = CARTESIAN_POINT('',(50.882255533489,124.08865400182,
    -0.548935972081));
#5639 = DIRECTION('',(0.998464005442,5.540424023454E-02,0.));
#5640 = DIRECTION('',(-0.,0.,1.));
#5641 = PCURVE('',#5532,#5642);
#5642 = DEFINITIONAL_REPRESENTATION('',(#5643),#5681);
#5643 = B_SPLINE_CURVE_WITH_KNOTS('',8,(#5644,#5645,#5646,#5647,#5648,
    #5649,#5650,#5651,#5652,#5653,#5654,#5655,#5656,#5657,#5658,#5659,
    #5660,#5661,#5662,#5663,#5664,#5665,#5666,#5667,#5668,#5669,#5670,
    #5671,#5672,#5673,#5674,#5675,#5676,#5677,#5678,#5679,#5680),
  .UNSPECIFIED.,.F.,.F.,(9,7,7,7,7,9),(-2.566442342364,-1.470455844753,
    -0.648465971545,-3.19735666382E-02,0.892765040721,1.817503648081),
  .UNSPECIFIED.);
#5644 = CARTESIAN_POINT('',(1.779544563379,12.));
#5645 = CARTESIAN_POINT('',(1.800466354656,10.31081507552));
#5646 = CARTESIAN_POINT('',(1.824625684143,8.889243602521));
#5647 = CARTESIAN_POINT('',(1.852591346686,7.686978568295));
#5648 = CARTESIAN_POINT('',(1.885049960736,6.666415517483));
#5649 = CARTESIAN_POINT('',(1.922807502611,5.797789684068));
#5650 = CARTESIAN_POINT('',(1.966828326301,5.057284591118));
#5651 = CARTESIAN_POINT('',(2.018214104732,4.425776462387));
#5652 = CARTESIAN_POINT('',(2.12288339625,3.484455535542));
#5653 = CARTESIAN_POINT('',(2.172480396695,3.133688379633));
#5654 = CARTESIAN_POINT('',(2.227264788847,2.829087452864));
#5655 = CARTESIAN_POINT('',(2.28766192563,2.565348305828));
#5656 = CARTESIAN_POINT('',(2.354048814255,2.3381342147));
#5657 = CARTESIAN_POINT('',(2.426643231786,2.143921251907));
#5658 = CARTESIAN_POINT('',(2.505367161566,1.979890817838));
#5659 = CARTESIAN_POINT('',(2.65302788216,1.741826963532));
#5660 = CARTESIAN_POINT('',(2.719491422035,1.655546187008));
#5661 = CARTESIAN_POINT('',(2.788935379906,1.584088818192));
#5662 = CARTESIAN_POINT('',(2.861038562872,1.526730945962));
#5663 = CARTESIAN_POINT('',(2.93533594264,1.482930854509));
#5664 = CARTESIAN_POINT('',(3.011229852672,1.452317463162));
#5665 = CARTESIAN_POINT('',(3.088034980887,1.434684281481));
#5666 = CARTESIAN_POINT('',(3.280590423458,1.422945776867));
#5667 = CARTESIAN_POINT('',(3.396611269632,1.445012650683));
#5668 = CARTESIAN_POINT('',(3.510774300832,1.496046096107));
#5669 = CARTESIAN_POINT('',(3.620654733939,1.57673249932));
#5670 = CARTESIAN_POINT('',(3.724281501156,1.688581829224));
#5671 = CARTESIAN_POINT('',(3.820656572739,1.833980821268));
#5672 = CARTESIAN_POINT('',(3.909368211583,2.016303858141));
#5673 = CARTESIAN_POINT('',(4.071523738589,2.463911533675));
#5674 = CARTESIAN_POINT('',(4.144967640816,2.729196114371));
#5675 = CARTESIAN_POINT('',(4.210801642974,3.040518591943));
#5676 = CARTESIAN_POINT('',(4.269485307736,3.403616990149));
#5677 = CARTESIAN_POINT('',(4.321663444645,3.82560949599));
#5678 = CARTESIAN_POINT('',(4.368015824662,4.315267270133));
#5679 = CARTESIAN_POINT('',(4.409186563586,4.883410631228));
#5680 = CARTESIAN_POINT('',(4.4457727741,5.543478471062));
#5681 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#5682 = PCURVE('',#5683,#5688);
#5683 = PLANE('',#5684);
#5684 = AXIS2_PLACEMENT_3D('',#5685,#5686,#5687);
#5685 = CARTESIAN_POINT('',(51.008936,121.80569,28.));
#5686 = DIRECTION('',(0.998464005442,5.540424023454E-02,0.));
#5687 = DIRECTION('',(-5.540424023454E-02,0.998464005442,0.));
#5688 = DEFINITIONAL_REPRESENTATION('',(#5689),#5693);
#5689 = ( BOUNDED_CURVE() B_SPLINE_CURVE(2,(#5690,#5691,#5692),
.UNSPECIFIED.,.F.,.F.) B_SPLINE_CURVE_WITH_KNOTS((3,3),(-2.566442342364,
1.817503648081),.PIECEWISE_BEZIER_KNOTS.) CURVE() 
GEOMETRIC_REPRESENTATION_ITEM() RATIONAL_B_SPLINE_CURVE((1.,
4.53227790256,1.)) REPRESENTATION_ITEM('') );
#5690 = CARTESIAN_POINT('',(9.40518686187,-16.072622));
#5691 = CARTESIAN_POINT('',(2.379503660701,-28.09872377459));
#5692 = CARTESIAN_POINT('',(-1.010372126087,-22.52914352893));
#5693 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#5694 = ORIENTED_EDGE('',*,*,#5695,.F.);
#5695 = EDGE_CURVE('',#5524,#5633,#5696,.T.);
#5696 = SURFACE_CURVE('',#5697,(#5702,#5709),.PCURVE_S1.);
#5697 = CIRCLE('',#5698,7.203203230276);
#5698 = AXIS2_PLACEMENT_3D('',#5699,#5700,#5701);
#5699 = CARTESIAN_POINT('',(51.980608,124.149601,11.927378));
#5700 = DIRECTION('',(0.,0.,1.));
#5701 = DIRECTION('',(1.,0.,0.));
#5702 = PCURVE('',#5532,#5703);
#5703 = DEFINITIONAL_REPRESENTATION('',(#5704),#5708);
#5704 = LINE('',#5705,#5706);
#5705 = CARTESIAN_POINT('',(0.,12.));
#5706 = VECTOR('',#5707,1.);
#5707 = DIRECTION('',(1.,0.));
#5708 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#5709 = PCURVE('',#5568,#5710);
#5710 = DEFINITIONAL_REPRESENTATION('',(#5711),#5715);
#5711 = CIRCLE('',#5712,7.203203230276);
#5712 = AXIS2_PLACEMENT_2D('',#5713,#5714);
#5713 = CARTESIAN_POINT('',(0.,0.));
#5714 = DIRECTION('',(1.,0.));
#5715 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#5716 = ORIENTED_EDGE('',*,*,#5521,.F.);
#5717 = ORIENTED_EDGE('',*,*,#5718,.T.);
#5718 = EDGE_CURVE('',#5522,#5522,#5719,.T.);
#5719 = SURFACE_CURVE('',#5720,(#5725,#5732),.PCURVE_S1.);
#5720 = CIRCLE('',#5721,0.275);
#5721 = AXIS2_PLACEMENT_3D('',#5722,#5723,#5724);
#5722 = CARTESIAN_POINT('',(51.980608,124.149601,-7.2622E-02));
#5723 = DIRECTION('',(0.,0.,1.));
#5724 = DIRECTION('',(1.,0.,0.));
#5725 = PCURVE('',#5532,#5726);
#5726 = DEFINITIONAL_REPRESENTATION('',(#5727),#5731);
#5727 = LINE('',#5728,#5729);
#5728 = CARTESIAN_POINT('',(0.,0.));
#5729 = VECTOR('',#5730,1.);
#5730 = DIRECTION('',(1.,0.));
#5731 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#5732 = PCURVE('',#5733,#5738);
#5733 = CYLINDRICAL_SURFACE('',#5734,0.275);
#5734 = AXIS2_PLACEMENT_3D('',#5735,#5736,#5737);
#5735 = CARTESIAN_POINT('',(51.980608,124.149601,-7.2622E-02));
#5736 = DIRECTION('',(0.,0.,1.));
#5737 = DIRECTION('',(1.,0.,0.));
#5738 = DEFINITIONAL_REPRESENTATION('',(#5739),#5742);
#5739 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#5740,#5741),.UNSPECIFIED.,.F.,
  .F.,(2,2),(0.,6.28318530718),.PIECEWISE_BEZIER_KNOTS.);
#5740 = CARTESIAN_POINT('',(0.,0.));
#5741 = CARTESIAN_POINT('',(6.28318530718,0.));
#5742 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#5743 = ADVANCED_FACE('',(#5744),#5733,.F.);
#5744 = FACE_BOUND('',#5745,.F.);
#5745 = EDGE_LOOP('',(#5746,#5770,#5791,#5792));
#5746 = ORIENTED_EDGE('',*,*,#5747,.F.);
#5747 = EDGE_CURVE('',#5748,#5748,#5750,.T.);
#5748 = VERTEX_POINT('',#5749);
#5749 = CARTESIAN_POINT('',(52.255608,124.149601,11.927378));
#5750 = SURFACE_CURVE('',#5751,(#5756,#5763),.PCURVE_S1.);
#5751 = CIRCLE('',#5752,0.275);
#5752 = AXIS2_PLACEMENT_3D('',#5753,#5754,#5755);
#5753 = CARTESIAN_POINT('',(51.980608,124.149601,11.927378));
#5754 = DIRECTION('',(0.,0.,1.));
#5755 = DIRECTION('',(1.,0.,0.));
#5756 = PCURVE('',#5733,#5757);
#5757 = DEFINITIONAL_REPRESENTATION('',(#5758),#5762);
#5758 = LINE('',#5759,#5760);
#5759 = CARTESIAN_POINT('',(0.,12.));
#5760 = VECTOR('',#5761,1.);
#5761 = DIRECTION('',(1.,0.));
#5762 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#5763 = PCURVE('',#5568,#5764);
#5764 = DEFINITIONAL_REPRESENTATION('',(#5765),#5769);
#5765 = CIRCLE('',#5766,0.275);
#5766 = AXIS2_PLACEMENT_2D('',#5767,#5768);
#5767 = CARTESIAN_POINT('',(0.,0.));
#5768 = DIRECTION('',(1.,0.));
#5769 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#5770 = ORIENTED_EDGE('',*,*,#5771,.F.);
#5771 = EDGE_CURVE('',#5522,#5748,#5772,.T.);
#5772 = SEAM_CURVE('',#5773,(#5777,#5784),.PCURVE_S1.);
#5773 = LINE('',#5774,#5775);
#5774 = CARTESIAN_POINT('',(52.255608,124.149601,-7.2622E-02));
#5775 = VECTOR('',#5776,1.);
#5776 = DIRECTION('',(0.,0.,1.));
#5777 = PCURVE('',#5733,#5778);
#5778 = DEFINITIONAL_REPRESENTATION('',(#5779),#5783);
#5779 = LINE('',#5780,#5781);
#5780 = CARTESIAN_POINT('',(6.28318530718,-0.));
#5781 = VECTOR('',#5782,1.);
#5782 = DIRECTION('',(0.,1.));
#5783 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#5784 = PCURVE('',#5733,#5785);
#5785 = DEFINITIONAL_REPRESENTATION('',(#5786),#5790);
#5786 = LINE('',#5787,#5788);
#5787 = CARTESIAN_POINT('',(0.,-0.));
#5788 = VECTOR('',#5789,1.);
#5789 = DIRECTION('',(0.,1.));
#5790 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#5791 = ORIENTED_EDGE('',*,*,#5718,.T.);
#5792 = ORIENTED_EDGE('',*,*,#5771,.T.);
#5793 = ADVANCED_FACE('',(#5794,#5838),#5568,.T.);
#5794 = FACE_BOUND('',#5795,.T.);
#5795 = EDGE_LOOP('',(#5796,#5797,#5798,#5819));
#5796 = ORIENTED_EDGE('',*,*,#5551,.T.);
#5797 = ORIENTED_EDGE('',*,*,#5695,.T.);
#5798 = ORIENTED_EDGE('',*,*,#5799,.F.);
#5799 = EDGE_CURVE('',#5800,#5633,#5802,.T.);
#5800 = VERTEX_POINT('',#5801);
#5801 = CARTESIAN_POINT('',(51.008936,121.80569,11.927378));
#5802 = SURFACE_CURVE('',#5803,(#5807,#5813),.PCURVE_S1.);
#5803 = LINE('',#5804,#5805);
#5804 = CARTESIAN_POINT('',(50.945595766744,122.94717200091,11.927378));
#5805 = VECTOR('',#5806,1.);
#5806 = DIRECTION('',(-5.540424023454E-02,0.998464005442,0.));
#5807 = PCURVE('',#5568,#5808);
#5808 = DEFINITIONAL_REPRESENTATION('',(#5809),#5812);
#5809 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#5810,#5811),.UNSPECIFIED.,.F.,
  .F.,(2,2),(-1.143238008272,8.418563096462),.PIECEWISE_BEZIER_KNOTS.);
#5810 = CARTESIAN_POINT('',(-0.971672,-2.343911));
#5811 = CARTESIAN_POINT('',(-1.501436325482,7.203203230276));
#5812 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#5813 = PCURVE('',#5683,#5814);
#5814 = DEFINITIONAL_REPRESENTATION('',(#5815),#5818);
#5815 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#5816,#5817),.UNSPECIFIED.,.F.,
  .F.,(2,2),(-1.143238008272,8.418563096462),.PIECEWISE_BEZIER_KNOTS.);
#5816 = CARTESIAN_POINT('',(0.,-16.072622));
#5817 = CARTESIAN_POINT('',(9.561801104733,-16.072622));
#5818 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#5819 = ORIENTED_EDGE('',*,*,#5820,.F.);
#5820 = EDGE_CURVE('',#5552,#5800,#5821,.T.);
#5821 = SURFACE_CURVE('',#5822,(#5826,#5832),.PCURVE_S1.);
#5822 = LINE('',#5823,#5824);
#5823 = CARTESIAN_POINT('',(64.849078903445,121.80640925132,11.927378));
#5824 = VECTOR('',#5825,1.);
#5825 = DIRECTION('',(-0.99999999865,-5.196848995186E-05,0.));
#5826 = PCURVE('',#5568,#5827);
#5827 = DEFINITIONAL_REPRESENTATION('',(#5828),#5831);
#5828 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#5829,#5830),.UNSPECIFIED.,.F.,
  .F.,(2,2),(5.66526768082,13.840142922135),.PIECEWISE_BEZIER_KNOTS.);
#5829 = CARTESIAN_POINT('',(7.203203230276,-2.343486164078));
#5830 = CARTESIAN_POINT('',(-0.971672,-2.343911));
#5831 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#5832 = PCURVE('',#5620,#5833);
#5833 = DEFINITIONAL_REPRESENTATION('',(#5834),#5837);
#5834 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#5835,#5836),.UNSPECIFIED.,.F.,
  .F.,(2,2),(5.66526768082,13.840142922135),.PIECEWISE_BEZIER_KNOTS.);
#5835 = CARTESIAN_POINT('',(18.533616794752,-16.072622));
#5836 = CARTESIAN_POINT('',(26.708492036066,-16.072622));
#5837 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#5838 = FACE_BOUND('',#5839,.T.);
#5839 = EDGE_LOOP('',(#5840));
#5840 = ORIENTED_EDGE('',*,*,#5747,.F.);
#5841 = ADVANCED_FACE('',(#5842),#5683,.F.);
#5842 = FACE_BOUND('',#5843,.F.);
#5843 = EDGE_LOOP('',(#5844,#5845,#5866));
#5844 = ORIENTED_EDGE('',*,*,#5799,.F.);
#5845 = ORIENTED_EDGE('',*,*,#5846,.T.);
#5846 = EDGE_CURVE('',#5800,#5581,#5847,.T.);
#5847 = SURFACE_CURVE('',#5848,(#5852,#5859),.PCURVE_S1.);
#5848 = LINE('',#5849,#5850);
#5849 = CARTESIAN_POINT('',(51.008936,121.80569,28.));
#5850 = VECTOR('',#5851,1.);
#5851 = DIRECTION('',(0.,0.,-1.));
#5852 = PCURVE('',#5683,#5853);
#5853 = DEFINITIONAL_REPRESENTATION('',(#5854),#5858);
#5854 = LINE('',#5855,#5856);
#5855 = CARTESIAN_POINT('',(0.,0.));
#5856 = VECTOR('',#5857,1.);
#5857 = DIRECTION('',(0.,-1.));
#5858 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#5859 = PCURVE('',#5620,#5860);
#5860 = DEFINITIONAL_REPRESENTATION('',(#5861),#5865);
#5861 = LINE('',#5862,#5863);
#5862 = CARTESIAN_POINT('',(26.708492036066,0.));
#5863 = VECTOR('',#5864,1.);
#5864 = DIRECTION('',(-0.,-1.));
#5865 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#5866 = ORIENTED_EDGE('',*,*,#5632,.F.);
#5867 = ADVANCED_FACE('',(#5868),#5620,.F.);
#5868 = FACE_BOUND('',#5869,.F.);
#5869 = EDGE_LOOP('',(#5870,#5871,#5872));
#5870 = ORIENTED_EDGE('',*,*,#5846,.F.);
#5871 = ORIENTED_EDGE('',*,*,#5820,.F.);
#5872 = ORIENTED_EDGE('',*,*,#5580,.F.);
#5873 = ( GEOMETRIC_REPRESENTATION_CONTEXT(3) 
GLOBAL_UNCERTAINTY_ASSIGNED_CONTEXT((#5877)) 
GLOBAL_UNIT_ASSIGNED_CONTEXT((#5874,#5875,#5876)) REPRESENTATION_CONTEXT
('Context #1','3D Context with UNIT and UNCERTAINTY') );
#5874 = ( LENGTH_UNIT() NAMED_UNIT(*) SI_UNIT(.MILLI.,.METRE.) );
#5875 = ( NAMED_UNIT(*) PLANE_ANGLE_UNIT() SI_UNIT($,.RADIAN.) );
#5876 = ( NAMED_UNIT(*) SI_UNIT($,.STERADIAN.) SOLID_ANGLE_UNIT() );
#5877 = UNCERTAINTY_MEASURE_WITH_UNIT(LENGTH_MEASURE(1.E-07),#5874,
  'distance_accuracy_value','confusion accuracy');
#5878 = CONTEXT_DEPENDENT_SHAPE_REPRESENTATION(#5879,#5881);
#5879 = ( REPRESENTATION_RELATIONSHIP('','',#5514,#10) 
REPRESENTATION_RELATIONSHIP_WITH_TRANSFORMATION(#5880) 
SHAPE_REPRESENTATION_RELATIONSHIP() );
#5880 = ITEM_DEFINED_TRANSFORMATION('','',#11,#91);
#5881 = PRODUCT_DEFINITION_SHAPE('Placement','Placement of an item',
  #5882);
#5882 = NEXT_ASSEMBLY_USAGE_OCCURRENCE('78','=>[0:1:1:21]','',#5,#5509,$
  );
#5883 = PRODUCT_RELATED_PRODUCT_CATEGORY('part',$,(#5511));
#5884 = SHAPE_DEFINITION_REPRESENTATION(#5885,#5891);
#5885 = PRODUCT_DEFINITION_SHAPE('','',#5886);
#5886 = PRODUCT_DEFINITION('design','',#5887,#5890);
#5887 = PRODUCT_DEFINITION_FORMATION('','',#5888);
#5888 = PRODUCT('PC1-P7_Part3','PC1-P7_Part3','',(#5889));
#5889 = PRODUCT_CONTEXT('',#2,'mechanical');
#5890 = PRODUCT_DEFINITION_CONTEXT('part definition',#2,'design');
#5891 = ADVANCED_BREP_SHAPE_REPRESENTATION('',(#11,#5892),#6222);
#5892 = MANIFOLD_SOLID_BREP('',#5893);
#5893 = CLOSED_SHELL('',(#5894,#6016,#6091,#6138,#6188,#6195));
#5894 = ADVANCED_FACE('',(#5895),#5910,.T.);
#5895 = FACE_BOUND('',#5896,.T.);
#5896 = EDGE_LOOP('',(#5897,#5933,#5959,#5992));
#5897 = ORIENTED_EDGE('',*,*,#5898,.T.);
#5898 = EDGE_CURVE('',#5899,#5901,#5903,.T.);
#5899 = VERTEX_POINT('',#5900);
#5900 = CARTESIAN_POINT('',(58.791930479718,121.80609447047,11.927378));
#5901 = VERTEX_POINT('',#5902);
#5902 = CARTESIAN_POINT('',(50.487848767654,131.19643054603,11.927378));
#5903 = SURFACE_CURVE('',#5904,(#5909,#5921),.PCURVE_S1.);
#5904 = CIRCLE('',#5905,7.203203230276);
#5905 = AXIS2_PLACEMENT_3D('',#5906,#5907,#5908);
#5906 = CARTESIAN_POINT('',(51.980608,124.149601,11.927378));
#5907 = DIRECTION('',(-0.,0.,1.));
#5908 = DIRECTION('',(0.945596321799,-0.325342275459,0.));
#5909 = PCURVE('',#5910,#5915);
#5910 = CYLINDRICAL_SURFACE('',#5911,7.203203230276);
#5911 = AXIS2_PLACEMENT_3D('',#5912,#5913,#5914);
#5912 = CARTESIAN_POINT('',(51.980608,124.149601,11.927378));
#5913 = DIRECTION('',(0.,0.,1.));
#5914 = DIRECTION('',(1.,0.,0.));
#5915 = DEFINITIONAL_REPRESENTATION('',(#5916),#5920);
#5916 = LINE('',#5917,#5918);
#5917 = CARTESIAN_POINT('',(5.951811633779,0.));
#5918 = VECTOR('',#5919,1.);
#5919 = DIRECTION('',(1.,0.));
#5920 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#5921 = PCURVE('',#5922,#5927);
#5922 = PLANE('',#5923);
#5923 = AXIS2_PLACEMENT_3D('',#5924,#5925,#5926);
#5924 = CARTESIAN_POINT('',(51.980608,124.149601,11.927378));
#5925 = DIRECTION('',(0.,0.,1.));
#5926 = DIRECTION('',(1.,0.,0.));
#5927 = DEFINITIONAL_REPRESENTATION('',(#5928),#5932);
#5928 = CIRCLE('',#5929,7.203203230276);
#5929 = AXIS2_PLACEMENT_2D('',#5930,#5931);
#5930 = CARTESIAN_POINT('',(0.,0.));
#5931 = DIRECTION('',(0.945596321799,-0.325342275459));
#5932 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#5933 = ORIENTED_EDGE('',*,*,#5934,.T.);
#5934 = EDGE_CURVE('',#5901,#5935,#5937,.T.);
#5935 = VERTEX_POINT('',#5936);
#5936 = CARTESIAN_POINT('',(50.487848767654,131.19643054603,18.));
#5937 = SURFACE_CURVE('',#5938,(#5942,#5948),.PCURVE_S1.);
#5938 = LINE('',#5939,#5940);
#5939 = CARTESIAN_POINT('',(50.487848767654,131.19643054603,11.927378));
#5940 = VECTOR('',#5941,1.);
#5941 = DIRECTION('',(0.,0.,1.));
#5942 = PCURVE('',#5910,#5943);
#5943 = DEFINITIONAL_REPRESENTATION('',(#5944),#5947);
#5944 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#5945,#5946),.UNSPECIFIED.,.F.,
  .F.,(2,2),(0.,6.072622),.PIECEWISE_BEZIER_KNOTS.);
#5945 = CARTESIAN_POINT('',(8.062729870559,0.));
#5946 = CARTESIAN_POINT('',(8.062729870559,6.072622));
#5947 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#5948 = PCURVE('',#5949,#5954);
#5949 = PLANE('',#5950);
#5950 = AXIS2_PLACEMENT_3D('',#5951,#5952,#5953);
#5951 = CARTESIAN_POINT('',(51.008936,121.80569,28.));
#5952 = DIRECTION('',(0.998464005442,5.540424023454E-02,0.));
#5953 = DIRECTION('',(-5.540424023454E-02,0.998464005442,0.));
#5954 = DEFINITIONAL_REPRESENTATION('',(#5955),#5958);
#5955 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#5956,#5957),.UNSPECIFIED.,.F.,
  .F.,(2,2),(-5.999999999062E-07,6.0726226),.PIECEWISE_BEZIER_KNOTS.);
#5956 = CARTESIAN_POINT('',(9.40518686187,-16.0726226));
#5957 = CARTESIAN_POINT('',(9.40518686187,-9.9999994));
#5958 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#5959 = ORIENTED_EDGE('',*,*,#5960,.T.);
#5960 = EDGE_CURVE('',#5935,#5961,#5963,.T.);
#5961 = VERTEX_POINT('',#5962);
#5962 = CARTESIAN_POINT('',(58.791930479718,121.80609447047,18.));
#5963 = SURFACE_CURVE('',#5964,(#5969,#5976),.PCURVE_S1.);
#5964 = CIRCLE('',#5965,7.203203230276);
#5965 = AXIS2_PLACEMENT_3D('',#5966,#5967,#5968);
#5966 = CARTESIAN_POINT('',(51.980608,124.149601,18.));
#5967 = DIRECTION('',(0.,0.,-1.));
#5968 = DIRECTION('',(-0.207235473528,0.978291090888,0.));
#5969 = PCURVE('',#5910,#5970);
#5970 = DEFINITIONAL_REPRESENTATION('',(#5971),#5975);
#5971 = LINE('',#5972,#5973);
#5972 = CARTESIAN_POINT('',(8.062729870559,6.072622));
#5973 = VECTOR('',#5974,1.);
#5974 = DIRECTION('',(-1.,-0.));
#5975 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#5976 = PCURVE('',#5977,#5982);
#5977 = PLANE('',#5978);
#5978 = AXIS2_PLACEMENT_3D('',#5979,#5980,#5981);
#5979 = CARTESIAN_POINT('',(51.980608,124.149601,18.));
#5980 = DIRECTION('',(0.,0.,1.));
#5981 = DIRECTION('',(1.,0.,0.));
#5982 = DEFINITIONAL_REPRESENTATION('',(#5983),#5991);
#5983 = ( BOUNDED_CURVE() B_SPLINE_CURVE(2,(#5984,#5985,#5986,#5987,
#5988,#5989,#5990),.UNSPECIFIED.,.T.,.F.) B_SPLINE_CURVE_WITH_KNOTS((1,2
    ,2,2,2,1),(-2.094395102393,0.,2.094395102393,4.188790204786,
6.28318530718,8.377580409573),.UNSPECIFIED.) CURVE() 
GEOMETRIC_REPRESENTATION_ITEM() RATIONAL_B_SPLINE_CURVE((1.,0.5,1.,0.5,
1.,0.5,1.)) REPRESENTATION_ITEM('') );
#5984 = CARTESIAN_POINT('',(-1.492759232346,7.046829546037));
#5985 = CARTESIAN_POINT('',(10.712707573668,9.632364379927));
#5986 = CARTESIAN_POINT('',(6.84911301918,-2.230647356073));
#5987 = CARTESIAN_POINT('',(2.985518464692,-14.09365909207));
#5988 = CARTESIAN_POINT('',(-5.356353786834,-4.816182189964));
#5989 = CARTESIAN_POINT('',(-13.69822603835,4.461294712146));
#5990 = CARTESIAN_POINT('',(-1.492759232346,7.046829546037));
#5991 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#5992 = ORIENTED_EDGE('',*,*,#5993,.F.);
#5993 = EDGE_CURVE('',#5899,#5961,#5994,.T.);
#5994 = SURFACE_CURVE('',#5995,(#5999,#6005),.PCURVE_S1.);
#5995 = LINE('',#5996,#5997);
#5996 = CARTESIAN_POINT('',(58.791930479718,121.80609447047,11.927378));
#5997 = VECTOR('',#5998,1.);
#5998 = DIRECTION('',(0.,0.,1.));
#5999 = PCURVE('',#5910,#6000);
#6000 = DEFINITIONAL_REPRESENTATION('',(#6001),#6004);
#6001 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#6002,#6003),.UNSPECIFIED.,.F.,
  .F.,(2,2),(0.,6.072622),.PIECEWISE_BEZIER_KNOTS.);
#6002 = CARTESIAN_POINT('',(5.951811633779,0.));
#6003 = CARTESIAN_POINT('',(5.951811633779,6.072622));
#6004 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#6005 = PCURVE('',#6006,#6011);
#6006 = PLANE('',#6007);
#6007 = AXIS2_PLACEMENT_3D('',#6008,#6009,#6010);
#6008 = CARTESIAN_POINT('',(77.717428,121.807078,28.));
#6009 = DIRECTION('',(-5.196848995186E-05,0.99999999865,0.));
#6010 = DIRECTION('',(-0.99999999865,-5.196848995186E-05,0.));
#6011 = DEFINITIONAL_REPRESENTATION('',(#6012),#6015);
#6012 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#6013,#6014),.UNSPECIFIED.,.F.,
  .F.,(2,2),(-5.999999999062E-07,6.0726226),.PIECEWISE_BEZIER_KNOTS.);
#6013 = CARTESIAN_POINT('',(18.925497545839,-16.0726226));
#6014 = CARTESIAN_POINT('',(18.925497545839,-9.9999994));
#6015 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#6016 = ADVANCED_FACE('',(#6017,#6060),#5922,.F.);
#6017 = FACE_BOUND('',#6018,.F.);
#6018 = EDGE_LOOP('',(#6019,#6020,#6041));
#6019 = ORIENTED_EDGE('',*,*,#5898,.T.);
#6020 = ORIENTED_EDGE('',*,*,#6021,.F.);
#6021 = EDGE_CURVE('',#6022,#5901,#6024,.T.);
#6022 = VERTEX_POINT('',#6023);
#6023 = CARTESIAN_POINT('',(51.008936,121.80569,11.927378));
#6024 = SURFACE_CURVE('',#6025,(#6029,#6035),.PCURVE_S1.);
#6025 = LINE('',#6026,#6027);
#6026 = CARTESIAN_POINT('',(50.945595766744,122.94717200091,11.927378));
#6027 = VECTOR('',#6028,1.);
#6028 = DIRECTION('',(-5.540424023454E-02,0.998464005442,0.));
#6029 = PCURVE('',#5922,#6030);
#6030 = DEFINITIONAL_REPRESENTATION('',(#6031),#6034);
#6031 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#6032,#6033),.UNSPECIFIED.,.F.,
  .F.,(2,2),(-1.143238008272,8.418563096462),.PIECEWISE_BEZIER_KNOTS.);
#6032 = CARTESIAN_POINT('',(-0.971672,-2.343911));
#6033 = CARTESIAN_POINT('',(-1.501436325482,7.203203230276));
#6034 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#6035 = PCURVE('',#5949,#6036);
#6036 = DEFINITIONAL_REPRESENTATION('',(#6037),#6040);
#6037 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#6038,#6039),.UNSPECIFIED.,.F.,
  .F.,(2,2),(-1.143238008272,8.418563096462),.PIECEWISE_BEZIER_KNOTS.);
#6038 = CARTESIAN_POINT('',(0.,-16.072622));
#6039 = CARTESIAN_POINT('',(9.561801104733,-16.072622));
#6040 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#6041 = ORIENTED_EDGE('',*,*,#6042,.F.);
#6042 = EDGE_CURVE('',#5899,#6022,#6043,.T.);
#6043 = SURFACE_CURVE('',#6044,(#6048,#6054),.PCURVE_S1.);
#6044 = LINE('',#6045,#6046);
#6045 = CARTESIAN_POINT('',(64.849078903445,121.80640925132,11.927378));
#6046 = VECTOR('',#6047,1.);
#6047 = DIRECTION('',(-0.99999999865,-5.196848995186E-05,0.));
#6048 = PCURVE('',#5922,#6049);
#6049 = DEFINITIONAL_REPRESENTATION('',(#6050),#6053);
#6050 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#6051,#6052),.UNSPECIFIED.,.F.,
  .F.,(2,2),(5.66526768082,13.840142922135),.PIECEWISE_BEZIER_KNOTS.);
#6051 = CARTESIAN_POINT('',(7.203203230276,-2.343486164078));
#6052 = CARTESIAN_POINT('',(-0.971672,-2.343911));
#6053 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#6054 = PCURVE('',#6006,#6055);
#6055 = DEFINITIONAL_REPRESENTATION('',(#6056),#6059);
#6056 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#6057,#6058),.UNSPECIFIED.,.F.,
  .F.,(2,2),(5.66526768082,13.840142922135),.PIECEWISE_BEZIER_KNOTS.);
#6057 = CARTESIAN_POINT('',(18.533616794752,-16.072622));
#6058 = CARTESIAN_POINT('',(26.708492036066,-16.072622));
#6059 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#6060 = FACE_BOUND('',#6061,.F.);
#6061 = EDGE_LOOP('',(#6062));
#6062 = ORIENTED_EDGE('',*,*,#6063,.F.);
#6063 = EDGE_CURVE('',#6064,#6064,#6066,.T.);
#6064 = VERTEX_POINT('',#6065);
#6065 = CARTESIAN_POINT('',(52.255608,124.149601,11.927378));
#6066 = SURFACE_CURVE('',#6067,(#6072,#6079),.PCURVE_S1.);
#6067 = CIRCLE('',#6068,0.275);
#6068 = AXIS2_PLACEMENT_3D('',#6069,#6070,#6071);
#6069 = CARTESIAN_POINT('',(51.980608,124.149601,11.927378));
#6070 = DIRECTION('',(0.,0.,1.));
#6071 = DIRECTION('',(1.,0.,0.));
#6072 = PCURVE('',#5922,#6073);
#6073 = DEFINITIONAL_REPRESENTATION('',(#6074),#6078);
#6074 = CIRCLE('',#6075,0.275);
#6075 = AXIS2_PLACEMENT_2D('',#6076,#6077);
#6076 = CARTESIAN_POINT('',(0.,0.));
#6077 = DIRECTION('',(1.,0.));
#6078 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#6079 = PCURVE('',#6080,#6085);
#6080 = CYLINDRICAL_SURFACE('',#6081,0.275);
#6081 = AXIS2_PLACEMENT_3D('',#6082,#6083,#6084);
#6082 = CARTESIAN_POINT('',(51.980608,124.149601,11.927378));
#6083 = DIRECTION('',(0.,0.,1.));
#6084 = DIRECTION('',(1.,0.,0.));
#6085 = DEFINITIONAL_REPRESENTATION('',(#6086),#6090);
#6086 = LINE('',#6087,#6088);
#6087 = CARTESIAN_POINT('',(0.,0.));
#6088 = VECTOR('',#6089,1.);
#6089 = DIRECTION('',(1.,0.));
#6090 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#6091 = ADVANCED_FACE('',(#6092),#6006,.F.);
#6092 = FACE_BOUND('',#6093,.F.);
#6093 = EDGE_LOOP('',(#6094,#6117,#6136,#6137));
#6094 = ORIENTED_EDGE('',*,*,#6095,.F.);
#6095 = EDGE_CURVE('',#6096,#6022,#6098,.T.);
#6096 = VERTEX_POINT('',#6097);
#6097 = CARTESIAN_POINT('',(51.008936,121.80569,18.));
#6098 = SURFACE_CURVE('',#6099,(#6103,#6110),.PCURVE_S1.);
#6099 = LINE('',#6100,#6101);
#6100 = CARTESIAN_POINT('',(51.008936,121.80569,28.));
#6101 = VECTOR('',#6102,1.);
#6102 = DIRECTION('',(0.,0.,-1.));
#6103 = PCURVE('',#6006,#6104);
#6104 = DEFINITIONAL_REPRESENTATION('',(#6105),#6109);
#6105 = LINE('',#6106,#6107);
#6106 = CARTESIAN_POINT('',(26.708492036066,0.));
#6107 = VECTOR('',#6108,1.);
#6108 = DIRECTION('',(-0.,-1.));
#6109 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#6110 = PCURVE('',#5949,#6111);
#6111 = DEFINITIONAL_REPRESENTATION('',(#6112),#6116);
#6112 = LINE('',#6113,#6114);
#6113 = CARTESIAN_POINT('',(0.,0.));
#6114 = VECTOR('',#6115,1.);
#6115 = DIRECTION('',(0.,-1.));
#6116 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#6117 = ORIENTED_EDGE('',*,*,#6118,.F.);
#6118 = EDGE_CURVE('',#5961,#6096,#6119,.T.);
#6119 = SURFACE_CURVE('',#6120,(#6124,#6130),.PCURVE_S1.);
#6120 = LINE('',#6121,#6122);
#6121 = CARTESIAN_POINT('',(64.849078903445,121.80640925132,18.));
#6122 = VECTOR('',#6123,1.);
#6123 = DIRECTION('',(-0.99999999865,-5.196848995186E-05,0.));
#6124 = PCURVE('',#6006,#6125);
#6125 = DEFINITIONAL_REPRESENTATION('',(#6126),#6129);
#6126 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#6127,#6128),.UNSPECIFIED.,.F.,
  .F.,(2,2),(5.66526768082,13.840142922135),.PIECEWISE_BEZIER_KNOTS.);
#6127 = CARTESIAN_POINT('',(18.533616794752,-10.));
#6128 = CARTESIAN_POINT('',(26.708492036066,-10.));
#6129 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#6130 = PCURVE('',#5977,#6131);
#6131 = DEFINITIONAL_REPRESENTATION('',(#6132),#6135);
#6132 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#6133,#6134),.UNSPECIFIED.,.F.,
  .F.,(2,2),(5.66526768082,13.840142922135),.PIECEWISE_BEZIER_KNOTS.);
#6133 = CARTESIAN_POINT('',(7.203203230276,-2.343486164078));
#6134 = CARTESIAN_POINT('',(-0.971672,-2.343911));
#6135 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#6136 = ORIENTED_EDGE('',*,*,#5993,.F.);
#6137 = ORIENTED_EDGE('',*,*,#6042,.T.);
#6138 = ADVANCED_FACE('',(#6139,#6162),#5977,.T.);
#6139 = FACE_BOUND('',#6140,.T.);
#6140 = EDGE_LOOP('',(#6141,#6142,#6143));
#6141 = ORIENTED_EDGE('',*,*,#6118,.F.);
#6142 = ORIENTED_EDGE('',*,*,#5960,.F.);
#6143 = ORIENTED_EDGE('',*,*,#6144,.F.);
#6144 = EDGE_CURVE('',#6096,#5935,#6145,.T.);
#6145 = SURFACE_CURVE('',#6146,(#6150,#6156),.PCURVE_S1.);
#6146 = LINE('',#6147,#6148);
#6147 = CARTESIAN_POINT('',(50.945595766744,122.94717200091,18.));
#6148 = VECTOR('',#6149,1.);
#6149 = DIRECTION('',(-5.540424023454E-02,0.998464005442,0.));
#6150 = PCURVE('',#5977,#6151);
#6151 = DEFINITIONAL_REPRESENTATION('',(#6152),#6155);
#6152 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#6153,#6154),.UNSPECIFIED.,.F.,
  .F.,(2,2),(-1.143238008272,8.418563096462),.PIECEWISE_BEZIER_KNOTS.);
#6153 = CARTESIAN_POINT('',(-0.971672,-2.343911));
#6154 = CARTESIAN_POINT('',(-1.501436325482,7.203203230276));
#6155 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#6156 = PCURVE('',#5949,#6157);
#6157 = DEFINITIONAL_REPRESENTATION('',(#6158),#6161);
#6158 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#6159,#6160),.UNSPECIFIED.,.F.,
  .F.,(2,2),(-1.143238008272,8.418563096462),.PIECEWISE_BEZIER_KNOTS.);
#6159 = CARTESIAN_POINT('',(0.,-10.));
#6160 = CARTESIAN_POINT('',(9.561801104733,-10.));
#6161 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#6162 = FACE_BOUND('',#6163,.T.);
#6163 = EDGE_LOOP('',(#6164));
#6164 = ORIENTED_EDGE('',*,*,#6165,.F.);
#6165 = EDGE_CURVE('',#6166,#6166,#6168,.T.);
#6166 = VERTEX_POINT('',#6167);
#6167 = CARTESIAN_POINT('',(52.255608,124.149601,18.));
#6168 = SURFACE_CURVE('',#6169,(#6174,#6181),.PCURVE_S1.);
#6169 = CIRCLE('',#6170,0.275);
#6170 = AXIS2_PLACEMENT_3D('',#6171,#6172,#6173);
#6171 = CARTESIAN_POINT('',(51.980608,124.149601,18.));
#6172 = DIRECTION('',(0.,0.,1.));
#6173 = DIRECTION('',(1.,0.,0.));
#6174 = PCURVE('',#5977,#6175);
#6175 = DEFINITIONAL_REPRESENTATION('',(#6176),#6180);
#6176 = CIRCLE('',#6177,0.275);
#6177 = AXIS2_PLACEMENT_2D('',#6178,#6179);
#6178 = CARTESIAN_POINT('',(0.,0.));
#6179 = DIRECTION('',(1.,0.));
#6180 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#6181 = PCURVE('',#6080,#6182);
#6182 = DEFINITIONAL_REPRESENTATION('',(#6183),#6187);
#6183 = LINE('',#6184,#6185);
#6184 = CARTESIAN_POINT('',(0.,6.072622));
#6185 = VECTOR('',#6186,1.);
#6186 = DIRECTION('',(1.,0.));
#6187 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#6188 = ADVANCED_FACE('',(#6189),#5949,.F.);
#6189 = FACE_BOUND('',#6190,.F.);
#6190 = EDGE_LOOP('',(#6191,#6192,#6193,#6194));
#6191 = ORIENTED_EDGE('',*,*,#6144,.F.);
#6192 = ORIENTED_EDGE('',*,*,#6095,.T.);
#6193 = ORIENTED_EDGE('',*,*,#6021,.T.);
#6194 = ORIENTED_EDGE('',*,*,#5934,.T.);
#6195 = ADVANCED_FACE('',(#6196),#6080,.F.);
#6196 = FACE_BOUND('',#6197,.F.);
#6197 = EDGE_LOOP('',(#6198,#6199,#6220,#6221));
#6198 = ORIENTED_EDGE('',*,*,#6165,.F.);
#6199 = ORIENTED_EDGE('',*,*,#6200,.F.);
#6200 = EDGE_CURVE('',#6064,#6166,#6201,.T.);
#6201 = SEAM_CURVE('',#6202,(#6206,#6213),.PCURVE_S1.);
#6202 = LINE('',#6203,#6204);
#6203 = CARTESIAN_POINT('',(52.255608,124.149601,11.927378));
#6204 = VECTOR('',#6205,1.);
#6205 = DIRECTION('',(0.,0.,1.));
#6206 = PCURVE('',#6080,#6207);
#6207 = DEFINITIONAL_REPRESENTATION('',(#6208),#6212);
#6208 = LINE('',#6209,#6210);
#6209 = CARTESIAN_POINT('',(6.28318530718,-0.));
#6210 = VECTOR('',#6211,1.);
#6211 = DIRECTION('',(0.,1.));
#6212 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#6213 = PCURVE('',#6080,#6214);
#6214 = DEFINITIONAL_REPRESENTATION('',(#6215),#6219);
#6215 = LINE('',#6216,#6217);
#6216 = CARTESIAN_POINT('',(0.,-0.));
#6217 = VECTOR('',#6218,1.);
#6218 = DIRECTION('',(0.,1.));
#6219 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#6220 = ORIENTED_EDGE('',*,*,#6063,.T.);
#6221 = ORIENTED_EDGE('',*,*,#6200,.T.);
#6222 = ( GEOMETRIC_REPRESENTATION_CONTEXT(3) 
GLOBAL_UNCERTAINTY_ASSIGNED_CONTEXT((#6226)) 
GLOBAL_UNIT_ASSIGNED_CONTEXT((#6223,#6224,#6225)) REPRESENTATION_CONTEXT
('Context #1','3D Context with UNIT and UNCERTAINTY') );
#6223 = ( LENGTH_UNIT() NAMED_UNIT(*) SI_UNIT(.MILLI.,.METRE.) );
#6224 = ( NAMED_UNIT(*) PLANE_ANGLE_UNIT() SI_UNIT($,.RADIAN.) );
#6225 = ( NAMED_UNIT(*) SI_UNIT($,.STERADIAN.) SOLID_ANGLE_UNIT() );
#6226 = UNCERTAINTY_MEASURE_WITH_UNIT(LENGTH_MEASURE(1.E-07),#6223,
  'distance_accuracy_value','confusion accuracy');
#6227 = CONTEXT_DEPENDENT_SHAPE_REPRESENTATION(#6228,#6230);
#6228 = ( REPRESENTATION_RELATIONSHIP('','',#5891,#10) 
REPRESENTATION_RELATIONSHIP_WITH_TRANSFORMATION(#6229) 
SHAPE_REPRESENTATION_RELATIONSHIP() );
#6229 = ITEM_DEFINED_TRANSFORMATION('','',#11,#95);
#6230 = PRODUCT_DEFINITION_SHAPE('Placement','Placement of an item',
  #6231);
#6231 = NEXT_ASSEMBLY_USAGE_OCCURRENCE('79','=>[0:1:1:22]','',#5,#5886,$
  );
#6232 = PRODUCT_RELATED_PRODUCT_CATEGORY('part',$,(#5888));
#6233 = SHAPE_DEFINITION_REPRESENTATION(#6234,#6240);
#6234 = PRODUCT_DEFINITION_SHAPE('','',#6235);
#6235 = PRODUCT_DEFINITION('design','',#6236,#6239);
#6236 = PRODUCT_DEFINITION_FORMATION('','',#6237);
#6237 = PRODUCT('PC1-P8_Part1','PC1-P8_Part1','',(#6238));
#6238 = PRODUCT_CONTEXT('',#2,'mechanical');
#6239 = PRODUCT_DEFINITION_CONTEXT('part definition',#2,'design');
#6240 = ADVANCED_BREP_SHAPE_REPRESENTATION('',(#11,#6241),#6339);
#6241 = MANIFOLD_SOLID_BREP('',#6242);
#6242 = CLOSED_SHELL('',(#6243,#6331,#6335));
#6243 = ADVANCED_FACE('',(#6244),#6257,.T.);
#6244 = FACE_BOUND('',#6245,.T.);
#6245 = EDGE_LOOP('',(#6246,#6280,#6303,#6330));
#6246 = ORIENTED_EDGE('',*,*,#6247,.F.);
#6247 = EDGE_CURVE('',#6248,#6248,#6250,.T.);
#6248 = VERTEX_POINT('',#6249);
#6249 = CARTESIAN_POINT('',(0.275,-6.73555739531E-17,17.438822));
#6250 = SURFACE_CURVE('',#6251,(#6256,#6268),.PCURVE_S1.);
#6251 = CIRCLE('',#6252,0.275);
#6252 = AXIS2_PLACEMENT_3D('',#6253,#6254,#6255);
#6253 = CARTESIAN_POINT('',(0.,0.,17.438822));
#6254 = DIRECTION('',(0.,0.,1.));
#6255 = DIRECTION('',(1.,0.,-0.));
#6256 = PCURVE('',#6257,#6262);
#6257 = CYLINDRICAL_SURFACE('',#6258,0.275);
#6258 = AXIS2_PLACEMENT_3D('',#6259,#6260,#6261);
#6259 = CARTESIAN_POINT('',(0.,0.,0.));
#6260 = DIRECTION('',(0.,0.,1.));
#6261 = DIRECTION('',(1.,0.,-0.));
#6262 = DEFINITIONAL_REPRESENTATION('',(#6263),#6267);
#6263 = LINE('',#6264,#6265);
#6264 = CARTESIAN_POINT('',(0.,17.438822));
#6265 = VECTOR('',#6266,1.);
#6266 = DIRECTION('',(1.,0.));
#6267 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#6268 = PCURVE('',#6269,#6274);
#6269 = PLANE('',#6270);
#6270 = AXIS2_PLACEMENT_3D('',#6271,#6272,#6273);
#6271 = CARTESIAN_POINT('',(0.,0.,17.438822));
#6272 = DIRECTION('',(0.,0.,1.));
#6273 = DIRECTION('',(1.,0.,-0.));
#6274 = DEFINITIONAL_REPRESENTATION('',(#6275),#6279);
#6275 = CIRCLE('',#6276,0.275);
#6276 = AXIS2_PLACEMENT_2D('',#6277,#6278);
#6277 = CARTESIAN_POINT('',(0.,0.));
#6278 = DIRECTION('',(1.,0.));
#6279 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#6280 = ORIENTED_EDGE('',*,*,#6281,.F.);
#6281 = EDGE_CURVE('',#6282,#6248,#6284,.T.);
#6282 = VERTEX_POINT('',#6283);
#6283 = CARTESIAN_POINT('',(0.275,-6.73555739531E-17,0.));
#6284 = SEAM_CURVE('',#6285,(#6289,#6296),.PCURVE_S1.);
#6285 = LINE('',#6286,#6287);
#6286 = CARTESIAN_POINT('',(0.275,-6.73555739531E-17,0.));
#6287 = VECTOR('',#6288,1.);
#6288 = DIRECTION('',(0.,0.,1.));
#6289 = PCURVE('',#6257,#6290);
#6290 = DEFINITIONAL_REPRESENTATION('',(#6291),#6295);
#6291 = LINE('',#6292,#6293);
#6292 = CARTESIAN_POINT('',(6.28318530718,-0.));
#6293 = VECTOR('',#6294,1.);
#6294 = DIRECTION('',(0.,1.));
#6295 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#6296 = PCURVE('',#6257,#6297);
#6297 = DEFINITIONAL_REPRESENTATION('',(#6298),#6302);
#6298 = LINE('',#6299,#6300);
#6299 = CARTESIAN_POINT('',(0.,-0.));
#6300 = VECTOR('',#6301,1.);
#6301 = DIRECTION('',(0.,1.));
#6302 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#6303 = ORIENTED_EDGE('',*,*,#6304,.T.);
#6304 = EDGE_CURVE('',#6282,#6282,#6305,.T.);
#6305 = SURFACE_CURVE('',#6306,(#6311,#6318),.PCURVE_S1.);
#6306 = CIRCLE('',#6307,0.275);
#6307 = AXIS2_PLACEMENT_3D('',#6308,#6309,#6310);
#6308 = CARTESIAN_POINT('',(0.,0.,0.));
#6309 = DIRECTION('',(0.,0.,1.));
#6310 = DIRECTION('',(1.,0.,-0.));
#6311 = PCURVE('',#6257,#6312);
#6312 = DEFINITIONAL_REPRESENTATION('',(#6313),#6317);
#6313 = LINE('',#6314,#6315);
#6314 = CARTESIAN_POINT('',(0.,0.));
#6315 = VECTOR('',#6316,1.);
#6316 = DIRECTION('',(1.,0.));
#6317 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#6318 = PCURVE('',#6319,#6324);
#6319 = PLANE('',#6320);
#6320 = AXIS2_PLACEMENT_3D('',#6321,#6322,#6323);
#6321 = CARTESIAN_POINT('',(0.,0.,0.));
#6322 = DIRECTION('',(0.,0.,1.));
#6323 = DIRECTION('',(1.,0.,-0.));
#6324 = DEFINITIONAL_REPRESENTATION('',(#6325),#6329);
#6325 = CIRCLE('',#6326,0.275);
#6326 = AXIS2_PLACEMENT_2D('',#6327,#6328);
#6327 = CARTESIAN_POINT('',(0.,0.));
#6328 = DIRECTION('',(1.,0.));
#6329 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#6330 = ORIENTED_EDGE('',*,*,#6281,.T.);
#6331 = ADVANCED_FACE('',(#6332),#6269,.T.);
#6332 = FACE_BOUND('',#6333,.T.);
#6333 = EDGE_LOOP('',(#6334));
#6334 = ORIENTED_EDGE('',*,*,#6247,.T.);
#6335 = ADVANCED_FACE('',(#6336),#6319,.F.);
#6336 = FACE_BOUND('',#6337,.T.);
#6337 = EDGE_LOOP('',(#6338));
#6338 = ORIENTED_EDGE('',*,*,#6304,.F.);
#6339 = ( GEOMETRIC_REPRESENTATION_CONTEXT(3) 
GLOBAL_UNCERTAINTY_ASSIGNED_CONTEXT((#6343)) 
GLOBAL_UNIT_ASSIGNED_CONTEXT((#6340,#6341,#6342)) REPRESENTATION_CONTEXT
('Context #1','3D Context with UNIT and UNCERTAINTY') );
#6340 = ( LENGTH_UNIT() NAMED_UNIT(*) SI_UNIT(.MILLI.,.METRE.) );
#6341 = ( NAMED_UNIT(*) PLANE_ANGLE_UNIT() SI_UNIT($,.RADIAN.) );
#6342 = ( NAMED_UNIT(*) SI_UNIT($,.STERADIAN.) SOLID_ANGLE_UNIT() );
#6343 = UNCERTAINTY_MEASURE_WITH_UNIT(LENGTH_MEASURE(1.E-07),#6340,
  'distance_accuracy_value','confusion accuracy');
#6344 = CONTEXT_DEPENDENT_SHAPE_REPRESENTATION(#6345,#6347);
#6345 = ( REPRESENTATION_RELATIONSHIP('','',#6240,#10) 
REPRESENTATION_RELATIONSHIP_WITH_TRANSFORMATION(#6346) 
SHAPE_REPRESENTATION_RELATIONSHIP() );
#6346 = ITEM_DEFINED_TRANSFORMATION('','',#11,#99);
#6347 = PRODUCT_DEFINITION_SHAPE('Placement','Placement of an item',
  #6348);
#6348 = NEXT_ASSEMBLY_USAGE_OCCURRENCE('80','=>[0:1:1:23]','',#5,#6235,$
  );
#6349 = PRODUCT_RELATED_PRODUCT_CATEGORY('part',$,(#6237));
#6350 = SHAPE_DEFINITION_REPRESENTATION(#6351,#6357);
#6351 = PRODUCT_DEFINITION_SHAPE('','',#6352);
#6352 = PRODUCT_DEFINITION('design','',#6353,#6356);
#6353 = PRODUCT_DEFINITION_FORMATION('','',#6354);
#6354 = PRODUCT('PC1-P8_Part2','PC1-P8_Part2','',(#6355));
#6355 = PRODUCT_CONTEXT('',#2,'mechanical');
#6356 = PRODUCT_DEFINITION_CONTEXT('part definition',#2,'design');
#6357 = ADVANCED_BREP_SHAPE_REPRESENTATION('',(#11,#6358),#6720);
#6358 = MANIFOLD_SOLID_BREP('',#6359);
#6359 = CLOSED_SHELL('',(#6360,#6590,#6640,#6688,#6714));
#6360 = ADVANCED_FACE('',(#6361),#6375,.T.);
#6361 = FACE_BOUND('',#6362,.T.);
#6362 = EDGE_LOOP('',(#6363,#6393,#6422,#6485,#6541,#6563,#6564));
#6363 = ORIENTED_EDGE('',*,*,#6364,.T.);
#6364 = EDGE_CURVE('',#6365,#6367,#6369,.T.);
#6365 = VERTEX_POINT('',#6366);
#6366 = CARTESIAN_POINT('',(52.347031,122.502036,0.561178));
#6367 = VERTEX_POINT('',#6368);
#6368 = CARTESIAN_POINT('',(59.275234230276,122.502036,12.561178));
#6369 = SEAM_CURVE('',#6370,(#6374,#6386),.PCURVE_S1.);
#6370 = LINE('',#6371,#6372);
#6371 = CARTESIAN_POINT('',(52.347031,122.502036,0.561178));
#6372 = VECTOR('',#6373,1.);
#6373 = DIRECTION('',(0.5,-1.224646799147E-16,0.866025403784));
#6374 = PCURVE('',#6375,#6380);
#6375 = CONICAL_SURFACE('',#6376,0.275,0.523598775598);
#6376 = AXIS2_PLACEMENT_3D('',#6377,#6378,#6379);
#6377 = CARTESIAN_POINT('',(52.072031,122.502036,0.561178));
#6378 = DIRECTION('',(0.,0.,1.));
#6379 = DIRECTION('',(1.,0.,0.));
#6380 = DEFINITIONAL_REPRESENTATION('',(#6381),#6385);
#6381 = LINE('',#6382,#6383);
#6382 = CARTESIAN_POINT('',(6.28318530718,-0.));
#6383 = VECTOR('',#6384,1.);
#6384 = DIRECTION('',(0.,1.));
#6385 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#6386 = PCURVE('',#6375,#6387);
#6387 = DEFINITIONAL_REPRESENTATION('',(#6388),#6392);
#6388 = LINE('',#6389,#6390);
#6389 = CARTESIAN_POINT('',(0.,-0.));
#6390 = VECTOR('',#6391,1.);
#6391 = DIRECTION('',(0.,1.));
#6392 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#6393 = ORIENTED_EDGE('',*,*,#6394,.F.);
#6394 = EDGE_CURVE('',#6395,#6367,#6397,.T.);
#6395 = VERTEX_POINT('',#6396);
#6396 = CARTESIAN_POINT('',(59.241538283317,121.80611783591,12.561178));
#6397 = SURFACE_CURVE('',#6398,(#6403,#6410),.PCURVE_S1.);
#6398 = CIRCLE('',#6399,7.203203230276);
#6399 = AXIS2_PLACEMENT_3D('',#6400,#6401,#6402);
#6400 = CARTESIAN_POINT('',(52.072031,122.502036,12.561178));
#6401 = DIRECTION('',(0.,0.,1.));
#6402 = DIRECTION('',(1.,0.,0.));
#6403 = PCURVE('',#6375,#6404);
#6404 = DEFINITIONAL_REPRESENTATION('',(#6405),#6409);
#6405 = LINE('',#6406,#6407);
#6406 = CARTESIAN_POINT('',(0.,12.));
#6407 = VECTOR('',#6408,1.);
#6408 = DIRECTION('',(1.,0.));
#6409 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#6410 = PCURVE('',#6411,#6416);
#6411 = PLANE('',#6412);
#6412 = AXIS2_PLACEMENT_3D('',#6413,#6414,#6415);
#6413 = CARTESIAN_POINT('',(52.072031,122.502036,12.561178));
#6414 = DIRECTION('',(0.,0.,1.));
#6415 = DIRECTION('',(1.,0.,0.));
#6416 = DEFINITIONAL_REPRESENTATION('',(#6417),#6421);
#6417 = CIRCLE('',#6418,7.203203230276);
#6418 = AXIS2_PLACEMENT_2D('',#6419,#6420);
#6419 = CARTESIAN_POINT('',(0.,0.));
#6420 = DIRECTION('',(1.,0.));
#6421 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#6422 = ORIENTED_EDGE('',*,*,#6423,.F.);
#6423 = EDGE_CURVE('',#6424,#6395,#6426,.T.);
#6424 = VERTEX_POINT('',#6425);
#6425 = CARTESIAN_POINT('',(51.008936,121.80569,2.286046935408));
#6426 = SURFACE_CURVE('',#6427,(#6432,#6473),.PCURVE_S1.);
#6427 = HYPERBOLA('',#6428,1.206010958642,0.696290751618);
#6428 = AXIS2_PLACEMENT_3D('',#6429,#6430,#6431);
#6429 = CARTESIAN_POINT('',(52.072067185179,121.80574524932,
    8.486402791856E-02));
#6430 = DIRECTION('',(-5.196848995186E-05,0.99999999865,0.));
#6431 = DIRECTION('',(0.,0.,1.));
#6432 = PCURVE('',#6375,#6433);
#6433 = DEFINITIONAL_REPRESENTATION('',(#6434),#6472);
#6434 = B_SPLINE_CURVE_WITH_KNOTS('',8,(#6435,#6436,#6437,#6438,#6439,
    #6440,#6441,#6442,#6443,#6444,#6445,#6446,#6447,#6448,#6449,#6450,
    #6451,#6452,#6453,#6454,#6455,#6456,#6457,#6458,#6459,#6460,#6461,
    #6462,#6463,#6464,#6465,#6466,#6467,#6468,#6469,#6470,#6471),
  .UNSPECIFIED.,.F.,.F.,(9,7,7,7,7,9),(-2.381191868529,-1.029064714466,
    -1.496934891958E-02,0.745602175241,1.886459461481,3.027316747721),
  .UNSPECIFIED.);
#6435 = CARTESIAN_POINT('',(3.326001360872,6.102613835399));
#6436 = CARTESIAN_POINT('',(3.356984376133,5.009513046375));
#6437 = CARTESIAN_POINT('',(3.393851003693,4.131196317832));
#6438 = CARTESIAN_POINT('',(3.437856652736,3.420081809582));
#6439 = CARTESIAN_POINT('',(3.490555458728,2.841674147631));
#6440 = CARTESIAN_POINT('',(3.553800176292,2.37022676901));
#6441 = CARTESIAN_POINT('',(3.629818436355,1.986353043227));
#6442 = CARTESIAN_POINT('',(3.720929123746,1.675435125053));
#6443 = CARTESIAN_POINT('',(3.9083801112,1.240050051284));
#6444 = CARTESIAN_POINT('',(3.997718093616,1.088402545061));
#6445 = CARTESIAN_POINT('',(4.096452090605,0.96713231323));
#6446 = CARTESIAN_POINT('',(4.204503531437,0.872868428661));
#6447 = CARTESIAN_POINT('',(4.32115112168,0.803203123433));
#6448 = CARTESIAN_POINT('',(4.444252344136,0.756558982635));
#6449 = CARTESIAN_POINT('',(4.570724439458,0.732120655028));
#6450 = CARTESIAN_POINT('',(4.792532948626,0.72811570368));
#6451 = CARTESIAN_POINT('',(4.887747625269,0.738858586078));
#6452 = CARTESIAN_POINT('',(4.981812242505,0.762036945516));
#6453 = CARTESIAN_POINT('',(5.073389639769,0.797867658611));
#6454 = CARTESIAN_POINT('',(5.161351585635,0.846806824315));
#6455 = CARTESIAN_POINT('',(5.244905184558,0.909560738401));
#6456 = CARTESIAN_POINT('',(5.323603547058,0.987109309782));
#6457 = CARTESIAN_POINT('',(5.507696487782,1.221190832047));
#6458 = CARTESIAN_POINT('',(5.606766093943,1.397828938759));
#6459 = CARTESIAN_POINT('',(5.693761338451,1.61500860323));
#6460 = CARTESIAN_POINT('',(5.769300442109,1.878652603735));
#6461 = CARTESIAN_POINT('',(5.834690496216,2.196536008787));
#6462 = CARTESIAN_POINT('',(5.891195549737,2.57874317537));
#6463 = CARTESIAN_POINT('',(5.940044493573,3.038344971187));
#6464 = CARTESIAN_POINT('',(6.024582728899,4.1466844663));
#6465 = CARTESIAN_POINT('',(6.060272230999,4.795421613605));
#6466 = CARTESIAN_POINT('',(6.090463130971,5.555903307208));
#6467 = CARTESIAN_POINT('',(6.116096170252,6.449395042153));
#6468 = CARTESIAN_POINT('',(6.137939972223,7.502410212094));
#6469 = CARTESIAN_POINT('',(6.156615790827,8.74821506082));
#6470 = CARTESIAN_POINT('',(6.172637073537,10.229120217757));
#6471 = CARTESIAN_POINT('',(6.186422058354,12.));
#6472 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#6473 = PCURVE('',#6474,#6479);
#6474 = PLANE('',#6475);
#6475 = AXIS2_PLACEMENT_3D('',#6476,#6477,#6478);
#6476 = CARTESIAN_POINT('',(77.717428,121.807078,28.));
#6477 = DIRECTION('',(-5.196848995186E-05,0.99999999865,0.));
#6478 = DIRECTION('',(-0.99999999865,-5.196848995186E-05,0.));
#6479 = DEFINITIONAL_REPRESENTATION('',(#6480),#6484);
#6480 = ( BOUNDED_CURVE() B_SPLINE_CURVE(2,(#6481,#6482,#6483),
.UNSPECIFIED.,.F.,.F.) B_SPLINE_CURVE_WITH_KNOTS((3,3),(-2.381191868529,
3.027316747721),.PIECEWISE_BEZIER_KNOTS.) CURVE() 
GEOMETRIC_REPRESENTATION_ITEM() RATIONAL_B_SPLINE_CURVE((1.,
7.50504487093,1.)) REPRESENTATION_ITEM('') );
#6481 = CARTESIAN_POINT('',(29.379341239673,-21.3362081646));
#6482 = CARTESIAN_POINT('',(25.614864195098,-27.74598368207));
#6483 = CARTESIAN_POINT('',(18.475889741632,-15.438822));
#6484 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#6485 = ORIENTED_EDGE('',*,*,#6486,.F.);
#6486 = EDGE_CURVE('',#6487,#6424,#6489,.T.);
#6487 = VERTEX_POINT('',#6488);
#6488 = CARTESIAN_POINT('',(50.579271284871,129.54886544376,12.561178));
#6489 = SURFACE_CURVE('',#6490,(#6495,#6529),.PCURVE_S1.);
#6490 = HYPERBOLA('',#6491,1.905329696096,1.100042612936);
#6491 = AXIS2_PLACEMENT_3D('',#6492,#6493,#6494);
#6492 = CARTESIAN_POINT('',(50.97367804653,122.4410889748,
    8.486402791856E-02));
#6493 = DIRECTION('',(0.998464005442,5.540424023454E-02,0.));
#6494 = DIRECTION('',(-0.,0.,1.));
#6495 = PCURVE('',#6375,#6496);
#6496 = DEFINITIONAL_REPRESENTATION('',(#6497),#6528);
#6497 = B_SPLINE_CURVE_WITH_KNOTS('',8,(#6498,#6499,#6500,#6501,#6502,
    #6503,#6504,#6505,#6506,#6507,#6508,#6509,#6510,#6511,#6512,#6513,
    #6514,#6515,#6516,#6517,#6518,#6519,#6520,#6521,#6522,#6523,#6524,
    #6525,#6526,#6527),.UNSPECIFIED.,.F.,.F.,(9,7,7,7,9),(
    -2.566441893749,-1.6265588507,-0.921646568414,0.135721855015,
    1.193090278444),.UNSPECIFIED.);
#6498 = CARTESIAN_POINT('',(1.77954463189,12.));
#6499 = CARTESIAN_POINT('',(1.797486500345,10.551408932734));
#6500 = CARTESIAN_POINT('',(1.8178092146,9.299627150538));
#6501 = CARTESIAN_POINT('',(1.840872097726,8.21418761677));
#6502 = CARTESIAN_POINT('',(1.867096467721,7.270414629039));
#6503 = CARTESIAN_POINT('',(1.896969899295,6.448090044373));
#6504 = CARTESIAN_POINT('',(1.931059965875,5.730515006948));
#6505 = CARTESIAN_POINT('',(1.970009686555,5.103843445926));
#6506 = CARTESIAN_POINT('',(2.047844814811,4.146103734674));
#6507 = CARTESIAN_POINT('',(2.084311862798,3.780301604302));
#6508 = CARTESIAN_POINT('',(2.124169837934,3.454301939611));
#6509 = CARTESIAN_POINT('',(2.16770677464,3.16398669886));
#6510 = CARTESIAN_POINT('',(2.215206124119,2.905859485833));
#6511 = CARTESIAN_POINT('',(2.266919432554,2.676952433579));
#6512 = CARTESIAN_POINT('',(2.323022523861,2.47475556483));
#6513 = CARTESIAN_POINT('',(2.474354181756,2.030788225536));
#6514 = CARTESIAN_POINT('',(2.57511894045,1.819779805388));
#6515 = CARTESIAN_POINT('',(2.685993552261,1.65705241284));
#6516 = CARTESIAN_POINT('',(2.806395156938,1.537578594487));
#6517 = CARTESIAN_POINT('',(2.934794881709,1.457998343316));
#6518 = CARTESIAN_POINT('',(3.067738324985,1.416413985197));
#6519 = CARTESIAN_POINT('',(3.201369362791,1.412307381016));
#6520 = CARTESIAN_POINT('',(3.463295369486,1.480874952114));
#6521 = CARTESIAN_POINT('',(3.591588299541,1.553549023551));
#6522 = CARTESIAN_POINT('',(3.713864932211,1.665526697464));
#6523 = CARTESIAN_POINT('',(3.827199573075,1.819149306673));
#6524 = CARTESIAN_POINT('',(3.930222169995,2.018245151741));
#6525 = CARTESIAN_POINT('',(4.022834258334,2.268302401994));
#6526 = CARTESIAN_POINT('',(4.105472974782,2.5768063646));
#6527 = CARTESIAN_POINT('',(4.178890375852,2.953791540218));
#6528 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#6529 = PCURVE('',#6530,#6535);
#6530 = PLANE('',#6531);
#6531 = AXIS2_PLACEMENT_3D('',#6532,#6533,#6534);
#6532 = CARTESIAN_POINT('',(51.008936,121.80569,28.));
#6533 = DIRECTION('',(0.998464005442,5.540424023454E-02,0.));
#6534 = DIRECTION('',(-5.540424023454E-02,0.998464005442,0.));
#6535 = DEFINITIONAL_REPRESENTATION('',(#6536),#6540);
#6536 = ( BOUNDED_CURVE() B_SPLINE_CURVE(2,(#6537,#6538,#6539),
.UNSPECIFIED.,.F.,.F.) B_SPLINE_CURVE_WITH_KNOTS((3,3),(-2.566441893749,
1.193090278444),.PIECEWISE_BEZIER_KNOTS.) CURVE() 
GEOMETRIC_REPRESENTATION_ITEM() RATIONAL_B_SPLINE_CURVE((1.,
3.352298944221,1.)) REPRESENTATION_ITEM('') );
#6537 = CARTESIAN_POINT('',(7.755087215524,-15.438822));
#6538 = CARTESIAN_POINT('',(0.879836442536,-27.20742332729));
#6539 = CARTESIAN_POINT('',(-1.010372126087,-24.48503045978));
#6540 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#6541 = ORIENTED_EDGE('',*,*,#6542,.F.);
#6542 = EDGE_CURVE('',#6367,#6487,#6543,.T.);
#6543 = SURFACE_CURVE('',#6544,(#6549,#6556),.PCURVE_S1.);
#6544 = CIRCLE('',#6545,7.203203230276);
#6545 = AXIS2_PLACEMENT_3D('',#6546,#6547,#6548);
#6546 = CARTESIAN_POINT('',(52.072031,122.502036,12.561178));
#6547 = DIRECTION('',(0.,0.,1.));
#6548 = DIRECTION('',(1.,0.,0.));
#6549 = PCURVE('',#6375,#6550);
#6550 = DEFINITIONAL_REPRESENTATION('',(#6551),#6555);
#6551 = LINE('',#6552,#6553);
#6552 = CARTESIAN_POINT('',(0.,12.));
#6553 = VECTOR('',#6554,1.);
#6554 = DIRECTION('',(1.,0.));
#6555 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#6556 = PCURVE('',#6411,#6557);
#6557 = DEFINITIONAL_REPRESENTATION('',(#6558),#6562);
#6558 = CIRCLE('',#6559,7.203203230276);
#6559 = AXIS2_PLACEMENT_2D('',#6560,#6561);
#6560 = CARTESIAN_POINT('',(0.,0.));
#6561 = DIRECTION('',(1.,0.));
#6562 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#6563 = ORIENTED_EDGE('',*,*,#6364,.F.);
#6564 = ORIENTED_EDGE('',*,*,#6565,.T.);
#6565 = EDGE_CURVE('',#6365,#6365,#6566,.T.);
#6566 = SURFACE_CURVE('',#6567,(#6572,#6579),.PCURVE_S1.);
#6567 = CIRCLE('',#6568,0.275);
#6568 = AXIS2_PLACEMENT_3D('',#6569,#6570,#6571);
#6569 = CARTESIAN_POINT('',(52.072031,122.502036,0.561178));
#6570 = DIRECTION('',(0.,0.,1.));
#6571 = DIRECTION('',(1.,0.,0.));
#6572 = PCURVE('',#6375,#6573);
#6573 = DEFINITIONAL_REPRESENTATION('',(#6574),#6578);
#6574 = LINE('',#6575,#6576);
#6575 = CARTESIAN_POINT('',(0.,0.));
#6576 = VECTOR('',#6577,1.);
#6577 = DIRECTION('',(1.,0.));
#6578 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#6579 = PCURVE('',#6580,#6585);
#6580 = CYLINDRICAL_SURFACE('',#6581,0.275);
#6581 = AXIS2_PLACEMENT_3D('',#6582,#6583,#6584);
#6582 = CARTESIAN_POINT('',(52.072031,122.502036,0.561178));
#6583 = DIRECTION('',(0.,0.,1.));
#6584 = DIRECTION('',(1.,0.,0.));
#6585 = DEFINITIONAL_REPRESENTATION('',(#6586),#6589);
#6586 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#6587,#6588),.UNSPECIFIED.,.F.,
  .F.,(2,2),(0.,6.28318530718),.PIECEWISE_BEZIER_KNOTS.);
#6587 = CARTESIAN_POINT('',(0.,0.));
#6588 = CARTESIAN_POINT('',(6.28318530718,0.));
#6589 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#6590 = ADVANCED_FACE('',(#6591),#6580,.F.);
#6591 = FACE_BOUND('',#6592,.F.);
#6592 = EDGE_LOOP('',(#6593,#6617,#6638,#6639));
#6593 = ORIENTED_EDGE('',*,*,#6594,.F.);
#6594 = EDGE_CURVE('',#6595,#6595,#6597,.T.);
#6595 = VERTEX_POINT('',#6596);
#6596 = CARTESIAN_POINT('',(52.347031,122.502036,12.561178));
#6597 = SURFACE_CURVE('',#6598,(#6603,#6610),.PCURVE_S1.);
#6598 = CIRCLE('',#6599,0.275);
#6599 = AXIS2_PLACEMENT_3D('',#6600,#6601,#6602);
#6600 = CARTESIAN_POINT('',(52.072031,122.502036,12.561178));
#6601 = DIRECTION('',(0.,0.,1.));
#6602 = DIRECTION('',(1.,0.,0.));
#6603 = PCURVE('',#6580,#6604);
#6604 = DEFINITIONAL_REPRESENTATION('',(#6605),#6609);
#6605 = LINE('',#6606,#6607);
#6606 = CARTESIAN_POINT('',(0.,12.));
#6607 = VECTOR('',#6608,1.);
#6608 = DIRECTION('',(1.,0.));
#6609 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#6610 = PCURVE('',#6411,#6611);
#6611 = DEFINITIONAL_REPRESENTATION('',(#6612),#6616);
#6612 = CIRCLE('',#6613,0.275);
#6613 = AXIS2_PLACEMENT_2D('',#6614,#6615);
#6614 = CARTESIAN_POINT('',(0.,0.));
#6615 = DIRECTION('',(1.,0.));
#6616 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#6617 = ORIENTED_EDGE('',*,*,#6618,.F.);
#6618 = EDGE_CURVE('',#6365,#6595,#6619,.T.);
#6619 = SEAM_CURVE('',#6620,(#6624,#6631),.PCURVE_S1.);
#6620 = LINE('',#6621,#6622);
#6621 = CARTESIAN_POINT('',(52.347031,122.502036,0.561178));
#6622 = VECTOR('',#6623,1.);
#6623 = DIRECTION('',(0.,0.,1.));
#6624 = PCURVE('',#6580,#6625);
#6625 = DEFINITIONAL_REPRESENTATION('',(#6626),#6630);
#6626 = LINE('',#6627,#6628);
#6627 = CARTESIAN_POINT('',(6.28318530718,-0.));
#6628 = VECTOR('',#6629,1.);
#6629 = DIRECTION('',(0.,1.));
#6630 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#6631 = PCURVE('',#6580,#6632);
#6632 = DEFINITIONAL_REPRESENTATION('',(#6633),#6637);
#6633 = LINE('',#6634,#6635);
#6634 = CARTESIAN_POINT('',(0.,-0.));
#6635 = VECTOR('',#6636,1.);
#6636 = DIRECTION('',(0.,1.));
#6637 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#6638 = ORIENTED_EDGE('',*,*,#6565,.T.);
#6639 = ORIENTED_EDGE('',*,*,#6618,.T.);
#6640 = ADVANCED_FACE('',(#6641,#6685),#6411,.T.);
#6641 = FACE_BOUND('',#6642,.T.);
#6642 = EDGE_LOOP('',(#6643,#6644,#6645,#6666));
#6643 = ORIENTED_EDGE('',*,*,#6394,.T.);
#6644 = ORIENTED_EDGE('',*,*,#6542,.T.);
#6645 = ORIENTED_EDGE('',*,*,#6646,.F.);
#6646 = EDGE_CURVE('',#6647,#6487,#6649,.T.);
#6647 = VERTEX_POINT('',#6648);
#6648 = CARTESIAN_POINT('',(51.008936,121.80569,12.561178));
#6649 = SURFACE_CURVE('',#6650,(#6654,#6660),.PCURVE_S1.);
#6650 = LINE('',#6651,#6652);
#6651 = CARTESIAN_POINT('',(50.991307023265,122.1233894874,12.561178));
#6652 = VECTOR('',#6653,1.);
#6653 = DIRECTION('',(-5.540424023454E-02,0.998464005442,0.));
#6654 = PCURVE('',#6411,#6655);
#6655 = DEFINITIONAL_REPRESENTATION('',(#6656),#6659);
#6656 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#6657,#6658),.UNSPECIFIED.,.F.,
  .F.,(2,2),(-0.318188222781,7.593513338034),.PIECEWISE_BEZIER_KNOTS.);
#6657 = CARTESIAN_POINT('',(-1.063095,-0.696346));
#6658 = CARTESIAN_POINT('',(-1.501436813939,7.203203230276));
#6659 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#6660 = PCURVE('',#6530,#6661);
#6661 = DEFINITIONAL_REPRESENTATION('',(#6662),#6665);
#6662 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#6663,#6664),.UNSPECIFIED.,.F.,
  .F.,(2,2),(-0.318188222781,7.593513338034),.PIECEWISE_BEZIER_KNOTS.);
#6663 = CARTESIAN_POINT('',(0.,-15.438822));
#6664 = CARTESIAN_POINT('',(7.911701560814,-15.438822));
#6665 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#6666 = ORIENTED_EDGE('',*,*,#6667,.F.);
#6667 = EDGE_CURVE('',#6395,#6647,#6668,.T.);
#6668 = SURFACE_CURVE('',#6669,(#6673,#6679),.PCURVE_S1.);
#6669 = LINE('',#6670,#6671);
#6670 = CARTESIAN_POINT('',(64.894747592589,121.80641162466,12.561178));
#6671 = VECTOR('',#6672,1.);
#6672 = DIRECTION('',(-0.99999999865,-5.196848995186E-05,0.));
#6673 = PCURVE('',#6411,#6674);
#6674 = DEFINITIONAL_REPRESENTATION('',(#6675),#6678);
#6675 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#6676,#6677),.UNSPECIFIED.,.F.,
  .F.,(2,2),(5.619513369902,13.88581161134),.PIECEWISE_BEZIER_KNOTS.);
#6676 = CARTESIAN_POINT('',(7.203203230276,-0.695916412963));
#6677 = CARTESIAN_POINT('',(-1.063095,-0.696346));
#6678 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#6679 = PCURVE('',#6474,#6680);
#6680 = DEFINITIONAL_REPRESENTATION('',(#6681),#6684);
#6681 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#6682,#6683),.UNSPECIFIED.,.F.,
  .F.,(2,2),(5.619513369902,13.88581161134),.PIECEWISE_BEZIER_KNOTS.);
#6682 = CARTESIAN_POINT('',(18.442193794628,-15.438822));
#6683 = CARTESIAN_POINT('',(26.708492036066,-15.438822));
#6684 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#6685 = FACE_BOUND('',#6686,.T.);
#6686 = EDGE_LOOP('',(#6687));
#6687 = ORIENTED_EDGE('',*,*,#6594,.F.);
#6688 = ADVANCED_FACE('',(#6689),#6530,.F.);
#6689 = FACE_BOUND('',#6690,.F.);
#6690 = EDGE_LOOP('',(#6691,#6692,#6713));
#6691 = ORIENTED_EDGE('',*,*,#6646,.F.);
#6692 = ORIENTED_EDGE('',*,*,#6693,.T.);
#6693 = EDGE_CURVE('',#6647,#6424,#6694,.T.);
#6694 = SURFACE_CURVE('',#6695,(#6699,#6706),.PCURVE_S1.);
#6695 = LINE('',#6696,#6697);
#6696 = CARTESIAN_POINT('',(51.008936,121.80569,28.));
#6697 = VECTOR('',#6698,1.);
#6698 = DIRECTION('',(0.,0.,-1.));
#6699 = PCURVE('',#6530,#6700);
#6700 = DEFINITIONAL_REPRESENTATION('',(#6701),#6705);
#6701 = LINE('',#6702,#6703);
#6702 = CARTESIAN_POINT('',(0.,0.));
#6703 = VECTOR('',#6704,1.);
#6704 = DIRECTION('',(0.,-1.));
#6705 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#6706 = PCURVE('',#6474,#6707);
#6707 = DEFINITIONAL_REPRESENTATION('',(#6708),#6712);
#6708 = LINE('',#6709,#6710);
#6709 = CARTESIAN_POINT('',(26.708492036066,0.));
#6710 = VECTOR('',#6711,1.);
#6711 = DIRECTION('',(-0.,-1.));
#6712 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#6713 = ORIENTED_EDGE('',*,*,#6486,.F.);
#6714 = ADVANCED_FACE('',(#6715),#6474,.F.);
#6715 = FACE_BOUND('',#6716,.F.);
#6716 = EDGE_LOOP('',(#6717,#6718,#6719));
#6717 = ORIENTED_EDGE('',*,*,#6693,.F.);
#6718 = ORIENTED_EDGE('',*,*,#6667,.F.);
#6719 = ORIENTED_EDGE('',*,*,#6423,.F.);
#6720 = ( GEOMETRIC_REPRESENTATION_CONTEXT(3) 
GLOBAL_UNCERTAINTY_ASSIGNED_CONTEXT((#6724)) 
GLOBAL_UNIT_ASSIGNED_CONTEXT((#6721,#6722,#6723)) REPRESENTATION_CONTEXT
('Context #1','3D Context with UNIT and UNCERTAINTY') );
#6721 = ( LENGTH_UNIT() NAMED_UNIT(*) SI_UNIT(.MILLI.,.METRE.) );
#6722 = ( NAMED_UNIT(*) PLANE_ANGLE_UNIT() SI_UNIT($,.RADIAN.) );
#6723 = ( NAMED_UNIT(*) SI_UNIT($,.STERADIAN.) SOLID_ANGLE_UNIT() );
#6724 = UNCERTAINTY_MEASURE_WITH_UNIT(LENGTH_MEASURE(1.E-07),#6721,
  'distance_accuracy_value','confusion accuracy');
#6725 = CONTEXT_DEPENDENT_SHAPE_REPRESENTATION(#6726,#6728);
#6726 = ( REPRESENTATION_RELATIONSHIP('','',#6357,#10) 
REPRESENTATION_RELATIONSHIP_WITH_TRANSFORMATION(#6727) 
SHAPE_REPRESENTATION_RELATIONSHIP() );
#6727 = ITEM_DEFINED_TRANSFORMATION('','',#11,#103);
#6728 = PRODUCT_DEFINITION_SHAPE('Placement','Placement of an item',
  #6729);
#6729 = NEXT_ASSEMBLY_USAGE_OCCURRENCE('81','=>[0:1:1:24]','',#5,#6352,$
  );
#6730 = PRODUCT_RELATED_PRODUCT_CATEGORY('part',$,(#6354));
#6731 = SHAPE_DEFINITION_REPRESENTATION(#6732,#6738);
#6732 = PRODUCT_DEFINITION_SHAPE('','',#6733);
#6733 = PRODUCT_DEFINITION('design','',#6734,#6737);
#6734 = PRODUCT_DEFINITION_FORMATION('','',#6735);
#6735 = PRODUCT('PC1-P8_Part3','PC1-P8_Part3','',(#6736));
#6736 = PRODUCT_CONTEXT('',#2,'mechanical');
#6737 = PRODUCT_DEFINITION_CONTEXT('part definition',#2,'design');
#6738 = ADVANCED_BREP_SHAPE_REPRESENTATION('',(#11,#6739),#7069);
#6739 = MANIFOLD_SOLID_BREP('',#6740);
#6740 = CLOSED_SHELL('',(#6741,#6863,#6938,#6985,#7035,#7042));
#6741 = ADVANCED_FACE('',(#6742),#6757,.T.);
#6742 = FACE_BOUND('',#6743,.T.);
#6743 = EDGE_LOOP('',(#6744,#6780,#6806,#6839));
#6744 = ORIENTED_EDGE('',*,*,#6745,.T.);
#6745 = EDGE_CURVE('',#6746,#6748,#6750,.T.);
#6746 = VERTEX_POINT('',#6747);
#6747 = CARTESIAN_POINT('',(59.241538283317,121.80611783591,12.561178));
#6748 = VERTEX_POINT('',#6749);
#6749 = CARTESIAN_POINT('',(50.579271284871,129.54886544376,12.561178));
#6750 = SURFACE_CURVE('',#6751,(#6756,#6768),.PCURVE_S1.);
#6751 = CIRCLE('',#6752,7.203203230276);
#6752 = AXIS2_PLACEMENT_3D('',#6753,#6754,#6755);
#6753 = CARTESIAN_POINT('',(52.072031,122.502036,12.561178));
#6754 = DIRECTION('',(-0.,0.,1.));
#6755 = DIRECTION('',(0.995322088537,-9.661231841487E-02,0.));
#6756 = PCURVE('',#6757,#6762);
#6757 = CYLINDRICAL_SURFACE('',#6758,7.203203230276);
#6758 = AXIS2_PLACEMENT_3D('',#6759,#6760,#6761);
#6759 = CARTESIAN_POINT('',(52.072031,122.502036,12.561178));
#6760 = DIRECTION('',(0.,0.,1.));
#6761 = DIRECTION('',(1.,0.,0.));
#6762 = DEFINITIONAL_REPRESENTATION('',(#6763),#6767);
#6763 = LINE('',#6764,#6765);
#6764 = CARTESIAN_POINT('',(6.186422058354,0.));
#6765 = VECTOR('',#6766,1.);
#6766 = DIRECTION('',(1.,0.));
#6767 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#6768 = PCURVE('',#6769,#6774);
#6769 = PLANE('',#6770);
#6770 = AXIS2_PLACEMENT_3D('',#6771,#6772,#6773);
#6771 = CARTESIAN_POINT('',(52.072031,122.502036,12.561178));
#6772 = DIRECTION('',(0.,0.,1.));
#6773 = DIRECTION('',(1.,0.,0.));
#6774 = DEFINITIONAL_REPRESENTATION('',(#6775),#6779);
#6775 = CIRCLE('',#6776,7.203203230276);
#6776 = AXIS2_PLACEMENT_2D('',#6777,#6778);
#6777 = CARTESIAN_POINT('',(0.,0.));
#6778 = DIRECTION('',(0.995322088537,-9.661231841487E-02));
#6779 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#6780 = ORIENTED_EDGE('',*,*,#6781,.T.);
#6781 = EDGE_CURVE('',#6748,#6782,#6784,.T.);
#6782 = VERTEX_POINT('',#6783);
#6783 = CARTESIAN_POINT('',(50.579271284871,129.54886544376,18.));
#6784 = SURFACE_CURVE('',#6785,(#6789,#6795),.PCURVE_S1.);
#6785 = LINE('',#6786,#6787);
#6786 = CARTESIAN_POINT('',(50.579271284871,129.54886544376,12.561178));
#6787 = VECTOR('',#6788,1.);
#6788 = DIRECTION('',(0.,0.,1.));
#6789 = PCURVE('',#6757,#6790);
#6790 = DEFINITIONAL_REPRESENTATION('',(#6791),#6794);
#6791 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#6792,#6793),.UNSPECIFIED.,.F.,
  .F.,(2,2),(0.,5.438822),.PIECEWISE_BEZIER_KNOTS.);
#6792 = CARTESIAN_POINT('',(8.062729939069,0.));
#6793 = CARTESIAN_POINT('',(8.062729939069,5.438822));
#6794 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#6795 = PCURVE('',#6796,#6801);
#6796 = PLANE('',#6797);
#6797 = AXIS2_PLACEMENT_3D('',#6798,#6799,#6800);
#6798 = CARTESIAN_POINT('',(51.008936,121.80569,28.));
#6799 = DIRECTION('',(0.998464005442,5.540424023454E-02,0.));
#6800 = DIRECTION('',(-5.540424023454E-02,0.998464005442,0.));
#6801 = DEFINITIONAL_REPRESENTATION('',(#6802),#6805);
#6802 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#6803,#6804),.UNSPECIFIED.,.F.,
  .F.,(2,2),(-5.999999999062E-07,5.4388226),.PIECEWISE_BEZIER_KNOTS.);
#6803 = CARTESIAN_POINT('',(7.755087215524,-15.4388226));
#6804 = CARTESIAN_POINT('',(7.755087215524,-9.9999994));
#6805 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#6806 = ORIENTED_EDGE('',*,*,#6807,.T.);
#6807 = EDGE_CURVE('',#6782,#6808,#6810,.T.);
#6808 = VERTEX_POINT('',#6809);
#6809 = CARTESIAN_POINT('',(59.241538283317,121.80611783591,18.));
#6810 = SURFACE_CURVE('',#6811,(#6816,#6823),.PCURVE_S1.);
#6811 = CIRCLE('',#6812,7.203203230276);
#6812 = AXIS2_PLACEMENT_3D('',#6813,#6814,#6815);
#6813 = CARTESIAN_POINT('',(52.072031,122.502036,18.));
#6814 = DIRECTION('',(0.,0.,-1.));
#6815 = DIRECTION('',(-0.207235540552,0.978291076691,0.));
#6816 = PCURVE('',#6757,#6817);
#6817 = DEFINITIONAL_REPRESENTATION('',(#6818),#6822);
#6818 = LINE('',#6819,#6820);
#6819 = CARTESIAN_POINT('',(8.062729939069,5.438822));
#6820 = VECTOR('',#6821,1.);
#6821 = DIRECTION('',(-1.,-0.));
#6822 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#6823 = PCURVE('',#6824,#6829);
#6824 = PLANE('',#6825);
#6825 = AXIS2_PLACEMENT_3D('',#6826,#6827,#6828);
#6826 = CARTESIAN_POINT('',(52.072031,122.502036,18.));
#6827 = DIRECTION('',(0.,0.,1.));
#6828 = DIRECTION('',(1.,0.,0.));
#6829 = DEFINITIONAL_REPRESENTATION('',(#6830),#6838);
#6830 = ( BOUNDED_CURVE() B_SPLINE_CURVE(2,(#6831,#6832,#6833,#6834,
#6835,#6836,#6837),.UNSPECIFIED.,.T.,.F.) B_SPLINE_CURVE_WITH_KNOTS((1,2
    ,2,2,2,1),(-2.094395102393,0.,2.094395102393,4.188790204786,
6.28318530718,8.377580409573),.UNSPECIFIED.) CURVE() 
GEOMETRIC_REPRESENTATION_ITEM() RATIONAL_B_SPLINE_CURVE((1.,0.5,1.,0.5,
1.,0.5,1.)) REPRESENTATION_ITEM('') );
#6831 = CARTESIAN_POINT('',(-1.492759715129,7.046829443767));
#6832 = CARTESIAN_POINT('',(10.712706913748,9.632365113862));
#6833 = CARTESIAN_POINT('',(6.849113172003,-2.230646886836));
#6834 = CARTESIAN_POINT('',(2.985519430257,-14.09365888753));
#6835 = CARTESIAN_POINT('',(-5.356353456874,-4.816182556931));
#6836 = CARTESIAN_POINT('',(-13.698226344,4.461293773672));
#6837 = CARTESIAN_POINT('',(-1.492759715129,7.046829443767));
#6838 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#6839 = ORIENTED_EDGE('',*,*,#6840,.F.);
#6840 = EDGE_CURVE('',#6746,#6808,#6841,.T.);
#6841 = SURFACE_CURVE('',#6842,(#6846,#6852),.PCURVE_S1.);
#6842 = LINE('',#6843,#6844);
#6843 = CARTESIAN_POINT('',(59.241538283317,121.80611783591,12.561178));
#6844 = VECTOR('',#6845,1.);
#6845 = DIRECTION('',(0.,0.,1.));
#6846 = PCURVE('',#6757,#6847);
#6847 = DEFINITIONAL_REPRESENTATION('',(#6848),#6851);
#6848 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#6849,#6850),.UNSPECIFIED.,.F.,
  .F.,(2,2),(0.,5.438822),.PIECEWISE_BEZIER_KNOTS.);
#6849 = CARTESIAN_POINT('',(6.186422058354,0.));
#6850 = CARTESIAN_POINT('',(6.186422058354,5.438822));
#6851 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#6852 = PCURVE('',#6853,#6858);
#6853 = PLANE('',#6854);
#6854 = AXIS2_PLACEMENT_3D('',#6855,#6856,#6857);
#6855 = CARTESIAN_POINT('',(77.717428,121.807078,28.));
#6856 = DIRECTION('',(-5.196848995186E-05,0.99999999865,0.));
#6857 = DIRECTION('',(-0.99999999865,-5.196848995186E-05,0.));
#6858 = DEFINITIONAL_REPRESENTATION('',(#6859),#6862);
#6859 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#6860,#6861),.UNSPECIFIED.,.F.,
  .F.,(2,2),(-5.999999999062E-07,5.4388226),.PIECEWISE_BEZIER_KNOTS.);
#6860 = CARTESIAN_POINT('',(18.475889741632,-15.4388226));
#6861 = CARTESIAN_POINT('',(18.475889741632,-9.9999994));
#6862 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#6863 = ADVANCED_FACE('',(#6864,#6907),#6769,.F.);
#6864 = FACE_BOUND('',#6865,.F.);
#6865 = EDGE_LOOP('',(#6866,#6867,#6888));
#6866 = ORIENTED_EDGE('',*,*,#6745,.T.);
#6867 = ORIENTED_EDGE('',*,*,#6868,.F.);
#6868 = EDGE_CURVE('',#6869,#6748,#6871,.T.);
#6869 = VERTEX_POINT('',#6870);
#6870 = CARTESIAN_POINT('',(51.008936,121.80569,12.561178));
#6871 = SURFACE_CURVE('',#6872,(#6876,#6882),.PCURVE_S1.);
#6872 = LINE('',#6873,#6874);
#6873 = CARTESIAN_POINT('',(50.991307023265,122.1233894874,12.561178));
#6874 = VECTOR('',#6875,1.);
#6875 = DIRECTION('',(-5.540424023454E-02,0.998464005442,0.));
#6876 = PCURVE('',#6769,#6877);
#6877 = DEFINITIONAL_REPRESENTATION('',(#6878),#6881);
#6878 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#6879,#6880),.UNSPECIFIED.,.F.,
  .F.,(2,2),(-0.318188222781,7.593513338034),.PIECEWISE_BEZIER_KNOTS.);
#6879 = CARTESIAN_POINT('',(-1.063095,-0.696346));
#6880 = CARTESIAN_POINT('',(-1.501436813939,7.203203230276));
#6881 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#6882 = PCURVE('',#6796,#6883);
#6883 = DEFINITIONAL_REPRESENTATION('',(#6884),#6887);
#6884 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#6885,#6886),.UNSPECIFIED.,.F.,
  .F.,(2,2),(-0.318188222781,7.593513338034),.PIECEWISE_BEZIER_KNOTS.);
#6885 = CARTESIAN_POINT('',(0.,-15.438822));
#6886 = CARTESIAN_POINT('',(7.911701560814,-15.438822));
#6887 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#6888 = ORIENTED_EDGE('',*,*,#6889,.F.);
#6889 = EDGE_CURVE('',#6746,#6869,#6890,.T.);
#6890 = SURFACE_CURVE('',#6891,(#6895,#6901),.PCURVE_S1.);
#6891 = LINE('',#6892,#6893);
#6892 = CARTESIAN_POINT('',(64.894747592589,121.80641162466,12.561178));
#6893 = VECTOR('',#6894,1.);
#6894 = DIRECTION('',(-0.99999999865,-5.196848995186E-05,0.));
#6895 = PCURVE('',#6769,#6896);
#6896 = DEFINITIONAL_REPRESENTATION('',(#6897),#6900);
#6897 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#6898,#6899),.UNSPECIFIED.,.F.,
  .F.,(2,2),(5.619513369902,13.88581161134),.PIECEWISE_BEZIER_KNOTS.);
#6898 = CARTESIAN_POINT('',(7.203203230276,-0.695916412963));
#6899 = CARTESIAN_POINT('',(-1.063095,-0.696346));
#6900 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#6901 = PCURVE('',#6853,#6902);
#6902 = DEFINITIONAL_REPRESENTATION('',(#6903),#6906);
#6903 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#6904,#6905),.UNSPECIFIED.,.F.,
  .F.,(2,2),(5.619513369902,13.88581161134),.PIECEWISE_BEZIER_KNOTS.);
#6904 = CARTESIAN_POINT('',(18.442193794628,-15.438822));
#6905 = CARTESIAN_POINT('',(26.708492036066,-15.438822));
#6906 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#6907 = FACE_BOUND('',#6908,.F.);
#6908 = EDGE_LOOP('',(#6909));
#6909 = ORIENTED_EDGE('',*,*,#6910,.F.);
#6910 = EDGE_CURVE('',#6911,#6911,#6913,.T.);
#6911 = VERTEX_POINT('',#6912);
#6912 = CARTESIAN_POINT('',(52.347031,122.502036,12.561178));
#6913 = SURFACE_CURVE('',#6914,(#6919,#6926),.PCURVE_S1.);
#6914 = CIRCLE('',#6915,0.275);
#6915 = AXIS2_PLACEMENT_3D('',#6916,#6917,#6918);
#6916 = CARTESIAN_POINT('',(52.072031,122.502036,12.561178));
#6917 = DIRECTION('',(0.,0.,1.));
#6918 = DIRECTION('',(1.,0.,0.));
#6919 = PCURVE('',#6769,#6920);
#6920 = DEFINITIONAL_REPRESENTATION('',(#6921),#6925);
#6921 = CIRCLE('',#6922,0.275);
#6922 = AXIS2_PLACEMENT_2D('',#6923,#6924);
#6923 = CARTESIAN_POINT('',(0.,0.));
#6924 = DIRECTION('',(1.,0.));
#6925 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#6926 = PCURVE('',#6927,#6932);
#6927 = CYLINDRICAL_SURFACE('',#6928,0.275);
#6928 = AXIS2_PLACEMENT_3D('',#6929,#6930,#6931);
#6929 = CARTESIAN_POINT('',(52.072031,122.502036,12.561178));
#6930 = DIRECTION('',(0.,0.,1.));
#6931 = DIRECTION('',(1.,0.,0.));
#6932 = DEFINITIONAL_REPRESENTATION('',(#6933),#6937);
#6933 = LINE('',#6934,#6935);
#6934 = CARTESIAN_POINT('',(0.,0.));
#6935 = VECTOR('',#6936,1.);
#6936 = DIRECTION('',(1.,0.));
#6937 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#6938 = ADVANCED_FACE('',(#6939),#6853,.F.);
#6939 = FACE_BOUND('',#6940,.F.);
#6940 = EDGE_LOOP('',(#6941,#6964,#6983,#6984));
#6941 = ORIENTED_EDGE('',*,*,#6942,.F.);
#6942 = EDGE_CURVE('',#6943,#6869,#6945,.T.);
#6943 = VERTEX_POINT('',#6944);
#6944 = CARTESIAN_POINT('',(51.008936,121.80569,18.));
#6945 = SURFACE_CURVE('',#6946,(#6950,#6957),.PCURVE_S1.);
#6946 = LINE('',#6947,#6948);
#6947 = CARTESIAN_POINT('',(51.008936,121.80569,28.));
#6948 = VECTOR('',#6949,1.);
#6949 = DIRECTION('',(0.,0.,-1.));
#6950 = PCURVE('',#6853,#6951);
#6951 = DEFINITIONAL_REPRESENTATION('',(#6952),#6956);
#6952 = LINE('',#6953,#6954);
#6953 = CARTESIAN_POINT('',(26.708492036066,0.));
#6954 = VECTOR('',#6955,1.);
#6955 = DIRECTION('',(-0.,-1.));
#6956 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#6957 = PCURVE('',#6796,#6958);
#6958 = DEFINITIONAL_REPRESENTATION('',(#6959),#6963);
#6959 = LINE('',#6960,#6961);
#6960 = CARTESIAN_POINT('',(0.,0.));
#6961 = VECTOR('',#6962,1.);
#6962 = DIRECTION('',(0.,-1.));
#6963 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#6964 = ORIENTED_EDGE('',*,*,#6965,.F.);
#6965 = EDGE_CURVE('',#6808,#6943,#6966,.T.);
#6966 = SURFACE_CURVE('',#6967,(#6971,#6977),.PCURVE_S1.);
#6967 = LINE('',#6968,#6969);
#6968 = CARTESIAN_POINT('',(64.894747592589,121.80641162466,18.));
#6969 = VECTOR('',#6970,1.);
#6970 = DIRECTION('',(-0.99999999865,-5.196848995186E-05,0.));
#6971 = PCURVE('',#6853,#6972);
#6972 = DEFINITIONAL_REPRESENTATION('',(#6973),#6976);
#6973 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#6974,#6975),.UNSPECIFIED.,.F.,
  .F.,(2,2),(5.619513369902,13.88581161134),.PIECEWISE_BEZIER_KNOTS.);
#6974 = CARTESIAN_POINT('',(18.442193794628,-10.));
#6975 = CARTESIAN_POINT('',(26.708492036066,-10.));
#6976 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#6977 = PCURVE('',#6824,#6978);
#6978 = DEFINITIONAL_REPRESENTATION('',(#6979),#6982);
#6979 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#6980,#6981),.UNSPECIFIED.,.F.,
  .F.,(2,2),(5.619513369902,13.88581161134),.PIECEWISE_BEZIER_KNOTS.);
#6980 = CARTESIAN_POINT('',(7.203203230276,-0.695916412963));
#6981 = CARTESIAN_POINT('',(-1.063095,-0.696346));
#6982 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#6983 = ORIENTED_EDGE('',*,*,#6840,.F.);
#6984 = ORIENTED_EDGE('',*,*,#6889,.T.);
#6985 = ADVANCED_FACE('',(#6986,#7009),#6824,.T.);
#6986 = FACE_BOUND('',#6987,.T.);
#6987 = EDGE_LOOP('',(#6988,#6989,#6990));
#6988 = ORIENTED_EDGE('',*,*,#6965,.F.);
#6989 = ORIENTED_EDGE('',*,*,#6807,.F.);
#6990 = ORIENTED_EDGE('',*,*,#6991,.F.);
#6991 = EDGE_CURVE('',#6943,#6782,#6992,.T.);
#6992 = SURFACE_CURVE('',#6993,(#6997,#7003),.PCURVE_S1.);
#6993 = LINE('',#6994,#6995);
#6994 = CARTESIAN_POINT('',(50.991307023265,122.1233894874,18.));
#6995 = VECTOR('',#6996,1.);
#6996 = DIRECTION('',(-5.540424023454E-02,0.998464005442,0.));
#6997 = PCURVE('',#6824,#6998);
#6998 = DEFINITIONAL_REPRESENTATION('',(#6999),#7002);
#6999 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#7000,#7001),.UNSPECIFIED.,.F.,
  .F.,(2,2),(-0.318188222781,7.593513338034),.PIECEWISE_BEZIER_KNOTS.);
#7000 = CARTESIAN_POINT('',(-1.063095,-0.696346));
#7001 = CARTESIAN_POINT('',(-1.501436813939,7.203203230276));
#7002 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#7003 = PCURVE('',#6796,#7004);
#7004 = DEFINITIONAL_REPRESENTATION('',(#7005),#7008);
#7005 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#7006,#7007),.UNSPECIFIED.,.F.,
  .F.,(2,2),(-0.318188222781,7.593513338034),.PIECEWISE_BEZIER_KNOTS.);
#7006 = CARTESIAN_POINT('',(0.,-10.));
#7007 = CARTESIAN_POINT('',(7.911701560814,-10.));
#7008 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#7009 = FACE_BOUND('',#7010,.T.);
#7010 = EDGE_LOOP('',(#7011));
#7011 = ORIENTED_EDGE('',*,*,#7012,.F.);
#7012 = EDGE_CURVE('',#7013,#7013,#7015,.T.);
#7013 = VERTEX_POINT('',#7014);
#7014 = CARTESIAN_POINT('',(52.347031,122.502036,18.));
#7015 = SURFACE_CURVE('',#7016,(#7021,#7028),.PCURVE_S1.);
#7016 = CIRCLE('',#7017,0.275);
#7017 = AXIS2_PLACEMENT_3D('',#7018,#7019,#7020);
#7018 = CARTESIAN_POINT('',(52.072031,122.502036,18.));
#7019 = DIRECTION('',(0.,0.,1.));
#7020 = DIRECTION('',(1.,0.,0.));
#7021 = PCURVE('',#6824,#7022);
#7022 = DEFINITIONAL_REPRESENTATION('',(#7023),#7027);
#7023 = CIRCLE('',#7024,0.275);
#7024 = AXIS2_PLACEMENT_2D('',#7025,#7026);
#7025 = CARTESIAN_POINT('',(0.,0.));
#7026 = DIRECTION('',(1.,0.));
#7027 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#7028 = PCURVE('',#6927,#7029);
#7029 = DEFINITIONAL_REPRESENTATION('',(#7030),#7034);
#7030 = LINE('',#7031,#7032);
#7031 = CARTESIAN_POINT('',(0.,5.438822));
#7032 = VECTOR('',#7033,1.);
#7033 = DIRECTION('',(1.,0.));
#7034 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#7035 = ADVANCED_FACE('',(#7036),#6796,.F.);
#7036 = FACE_BOUND('',#7037,.F.);
#7037 = EDGE_LOOP('',(#7038,#7039,#7040,#7041));
#7038 = ORIENTED_EDGE('',*,*,#6991,.F.);
#7039 = ORIENTED_EDGE('',*,*,#6942,.T.);
#7040 = ORIENTED_EDGE('',*,*,#6868,.T.);
#7041 = ORIENTED_EDGE('',*,*,#6781,.T.);
#7042 = ADVANCED_FACE('',(#7043),#6927,.F.);
#7043 = FACE_BOUND('',#7044,.F.);
#7044 = EDGE_LOOP('',(#7045,#7046,#7067,#7068));
#7045 = ORIENTED_EDGE('',*,*,#7012,.F.);
#7046 = ORIENTED_EDGE('',*,*,#7047,.F.);
#7047 = EDGE_CURVE('',#6911,#7013,#7048,.T.);
#7048 = SEAM_CURVE('',#7049,(#7053,#7060),.PCURVE_S1.);
#7049 = LINE('',#7050,#7051);
#7050 = CARTESIAN_POINT('',(52.347031,122.502036,12.561178));
#7051 = VECTOR('',#7052,1.);
#7052 = DIRECTION('',(0.,0.,1.));
#7053 = PCURVE('',#6927,#7054);
#7054 = DEFINITIONAL_REPRESENTATION('',(#7055),#7059);
#7055 = LINE('',#7056,#7057);
#7056 = CARTESIAN_POINT('',(6.28318530718,-0.));
#7057 = VECTOR('',#7058,1.);
#7058 = DIRECTION('',(0.,1.));
#7059 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#7060 = PCURVE('',#6927,#7061);
#7061 = DEFINITIONAL_REPRESENTATION('',(#7062),#7066);
#7062 = LINE('',#7063,#7064);
#7063 = CARTESIAN_POINT('',(0.,-0.));
#7064 = VECTOR('',#7065,1.);
#7065 = DIRECTION('',(0.,1.));
#7066 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#7067 = ORIENTED_EDGE('',*,*,#6910,.T.);
#7068 = ORIENTED_EDGE('',*,*,#7047,.T.);
#7069 = ( GEOMETRIC_REPRESENTATION_CONTEXT(3) 
GLOBAL_UNCERTAINTY_ASSIGNED_CONTEXT((#7073)) 
GLOBAL_UNIT_ASSIGNED_CONTEXT((#7070,#7071,#7072)) REPRESENTATION_CONTEXT
('Context #1','3D Context with UNIT and UNCERTAINTY') );
#7070 = ( LENGTH_UNIT() NAMED_UNIT(*) SI_UNIT(.MILLI.,.METRE.) );
#7071 = ( NAMED_UNIT(*) PLANE_ANGLE_UNIT() SI_UNIT($,.RADIAN.) );
#7072 = ( NAMED_UNIT(*) SI_UNIT($,.STERADIAN.) SOLID_ANGLE_UNIT() );
#7073 = UNCERTAINTY_MEASURE_WITH_UNIT(LENGTH_MEASURE(1.E-07),#7070,
  'distance_accuracy_value','confusion accuracy');
#7074 = CONTEXT_DEPENDENT_SHAPE_REPRESENTATION(#7075,#7077);
#7075 = ( REPRESENTATION_RELATIONSHIP('','',#6738,#10) 
REPRESENTATION_RELATIONSHIP_WITH_TRANSFORMATION(#7076) 
SHAPE_REPRESENTATION_RELATIONSHIP() );
#7076 = ITEM_DEFINED_TRANSFORMATION('','',#11,#107);
#7077 = PRODUCT_DEFINITION_SHAPE('Placement','Placement of an item',
  #7078);
#7078 = NEXT_ASSEMBLY_USAGE_OCCURRENCE('82','=>[0:1:1:25]','',#5,#6733,$
  );
#7079 = PRODUCT_RELATED_PRODUCT_CATEGORY('part',$,(#6735));
#7080 = MECHANICAL_DESIGN_GEOMETRIC_PRESENTATION_REPRESENTATION('',(
    #7081),#223);
#7081 = STYLED_ITEM('color',(#7082),#125);
#7082 = PRESENTATION_STYLE_ASSIGNMENT((#7083));
#7083 = SURFACE_STYLE_USAGE(.BOTH.,#7084);
#7084 = SURFACE_SIDE_STYLE('',(#7085));
#7085 = SURFACE_STYLE_FILL_AREA(#7086);
#7086 = FILL_AREA_STYLE('',(#7087));
#7087 = FILL_AREA_STYLE_COLOUR('',#7088);
#7088 = DRAUGHTING_PRE_DEFINED_COLOUR('blue');
#7089 = MECHANICAL_DESIGN_GEOMETRIC_PRESENTATION_REPRESENTATION('',(
    #7090),#1077);
#7090 = STYLED_ITEM('color',(#7091),#673);
#7091 = PRESENTATION_STYLE_ASSIGNMENT((#7092));
#7092 = SURFACE_STYLE_USAGE(.BOTH.,#7093);
#7093 = SURFACE_SIDE_STYLE('',(#7094));
#7094 = SURFACE_STYLE_FILL_AREA(#7095);
#7095 = FILL_AREA_STYLE('',(#7096));
#7096 = FILL_AREA_STYLE_COLOUR('',#7097);
#7097 = DRAUGHTING_PRE_DEFINED_COLOUR('green');
#7098 = MECHANICAL_DESIGN_GEOMETRIC_PRESENTATION_REPRESENTATION('',(
    #7099),#1607);
#7099 = STYLED_ITEM('color',(#7100),#1213);
#7100 = PRESENTATION_STYLE_ASSIGNMENT((#7101));
#7101 = SURFACE_STYLE_USAGE(.BOTH.,#7102);
#7102 = SURFACE_SIDE_STYLE('',(#7103));
#7103 = SURFACE_STYLE_FILL_AREA(#7104);
#7104 = FILL_AREA_STYLE('',(#7105));
#7105 = FILL_AREA_STYLE_COLOUR('',#7106);
#7106 = COLOUR_RGB('',0.627451300878,0.125491010875,0.941175948901);
#7107 = MECHANICAL_DESIGN_GEOMETRIC_PRESENTATION_REPRESENTATION('',(
    #7108),#2223);
#7108 = STYLED_ITEM('color',(#7109),#2125);
#7109 = PRESENTATION_STYLE_ASSIGNMENT((#7110));
#7110 = SURFACE_STYLE_USAGE(.BOTH.,#7111);
#7111 = SURFACE_SIDE_STYLE('',(#7112));
#7112 = SURFACE_STYLE_FILL_AREA(#7113);
#7113 = FILL_AREA_STYLE('',(#7114));
#7114 = FILL_AREA_STYLE_COLOUR('',#7088);
#7115 = MECHANICAL_DESIGN_GEOMETRIC_PRESENTATION_REPRESENTATION('',(
    #7116),#5030);
#7116 = STYLED_ITEM('color',(#7117),#4697);
#7117 = PRESENTATION_STYLE_ASSIGNMENT((#7118));
#7118 = SURFACE_STYLE_USAGE(.BOTH.,#7119);
#7119 = SURFACE_SIDE_STYLE('',(#7120));
#7120 = SURFACE_STYLE_FILL_AREA(#7121);
#7121 = FILL_AREA_STYLE('',(#7122));
#7122 = FILL_AREA_STYLE_COLOUR('',#7106);
#7123 = MECHANICAL_DESIGN_GEOMETRIC_PRESENTATION_REPRESENTATION('',(
    #7124),#6720);
#7124 = STYLED_ITEM('color',(#7125),#6358);
#7125 = PRESENTATION_STYLE_ASSIGNMENT((#7126));
#7126 = SURFACE_STYLE_USAGE(.BOTH.,#7127);
#7127 = SURFACE_SIDE_STYLE('',(#7128));
#7128 = SURFACE_STYLE_FILL_AREA(#7129);
#7129 = FILL_AREA_STYLE('',(#7130));
#7130 = FILL_AREA_STYLE_COLOUR('',#7106);
#7131 = MECHANICAL_DESIGN_GEOMETRIC_PRESENTATION_REPRESENTATION('',(
    #7132),#2658);
#7132 = STYLED_ITEM('color',(#7133),#2242);
#7133 = PRESENTATION_STYLE_ASSIGNMENT((#7134));
#7134 = SURFACE_STYLE_USAGE(.BOTH.,#7135);
#7135 = SURFACE_SIDE_STYLE('',(#7136));
#7136 = SURFACE_STYLE_FILL_AREA(#7137);
#7137 = FILL_AREA_STYLE('',(#7138));
#7138 = FILL_AREA_STYLE_COLOUR('',#7139);
#7139 = COLOUR_RGB('',1.,0.647058735019,0.);
#7140 = MECHANICAL_DESIGN_GEOMETRIC_PRESENTATION_REPRESENTATION('',(
    #7141),#6222);
#7141 = STYLED_ITEM('color',(#7142),#5892);
#7142 = PRESENTATION_STYLE_ASSIGNMENT((#7143));
#7143 = SURFACE_STYLE_USAGE(.BOTH.,#7144);
#7144 = SURFACE_SIDE_STYLE('',(#7145));
#7145 = SURFACE_STYLE_FILL_AREA(#7146);
#7146 = FILL_AREA_STYLE('',(#7147));
#7147 = FILL_AREA_STYLE_COLOUR('',#7097);
#7148 = MECHANICAL_DESIGN_GEOMETRIC_PRESENTATION_REPRESENTATION('',(
    #7149),#2106);
#7149 = STYLED_ITEM('color',(#7150),#1626);
#7150 = PRESENTATION_STYLE_ASSIGNMENT((#7151));
#7151 = SURFACE_STYLE_USAGE(.BOTH.,#7152);
#7152 = SURFACE_SIDE_STYLE('',(#7153));
#7153 = SURFACE_STYLE_FILL_AREA(#7154);
#7154 = FILL_AREA_STYLE('',(#7155));
#7155 = FILL_AREA_STYLE_COLOUR('',#7156);
#7156 = DRAUGHTING_PRE_DEFINED_COLOUR('yellow');
#7157 = MECHANICAL_DESIGN_GEOMETRIC_PRESENTATION_REPRESENTATION('',(
    #7158),#654);
#7158 = STYLED_ITEM('color',(#7159),#242);
#7159 = PRESENTATION_STYLE_ASSIGNMENT((#7160));
#7160 = SURFACE_STYLE_USAGE(.BOTH.,#7161);
#7161 = SURFACE_SIDE_STYLE('',(#7162));
#7162 = SURFACE_STYLE_FILL_AREA(#7163);
#7163 = FILL_AREA_STYLE('',(#7164));
#7164 = FILL_AREA_STYLE_COLOUR('',#7139);
#7165 = MECHANICAL_DESIGN_GEOMETRIC_PRESENTATION_REPRESENTATION('',(
    #7166),#5873);
#7166 = STYLED_ITEM('color',(#7167),#5515);
#7167 = PRESENTATION_STYLE_ASSIGNMENT((#7168));
#7168 = SURFACE_STYLE_USAGE(.BOTH.,#7169);
#7169 = SURFACE_SIDE_STYLE('',(#7170));
#7170 = SURFACE_STYLE_FILL_AREA(#7171);
#7171 = FILL_AREA_STYLE('',(#7172));
#7172 = FILL_AREA_STYLE_COLOUR('',#7139);
#7173 = MECHANICAL_DESIGN_GEOMETRIC_PRESENTATION_REPRESENTATION('',(
    #7174),#4561);
#7174 = STYLED_ITEM('color',(#7175),#4305);
#7175 = PRESENTATION_STYLE_ASSIGNMENT((#7176));
#7176 = SURFACE_STYLE_USAGE(.BOTH.,#7177);
#7177 = SURFACE_SIDE_STYLE('',(#7178));
#7178 = SURFACE_STYLE_FILL_AREA(#7179);
#7179 = FILL_AREA_STYLE('',(#7180));
#7180 = FILL_AREA_STYLE_COLOUR('',#7097);
#7181 = MECHANICAL_DESIGN_GEOMETRIC_PRESENTATION_REPRESENTATION('',(
    #7182),#3899);
#7182 = STYLED_ITEM('color',(#7183),#3569);
#7183 = PRESENTATION_STYLE_ASSIGNMENT((#7184));
#7184 = SURFACE_STYLE_USAGE(.BOTH.,#7185);
#7185 = SURFACE_SIDE_STYLE('',(#7186));
#7186 = SURFACE_STYLE_FILL_AREA(#7187);
#7187 = FILL_AREA_STYLE('',(#7188));
#7188 = FILL_AREA_STYLE_COLOUR('',#7156);
#7189 = MECHANICAL_DESIGN_GEOMETRIC_PRESENTATION_REPRESENTATION('',(
    #7190),#1194);
#7190 = STYLED_ITEM('color',(#7191),#1096);
#7191 = PRESENTATION_STYLE_ASSIGNMENT((#7192));
#7192 = SURFACE_STYLE_USAGE(.BOTH.,#7193);
#7193 = SURFACE_SIDE_STYLE('',(#7194));
#7194 = SURFACE_STYLE_FILL_AREA(#7195);
#7195 = FILL_AREA_STYLE('',(#7196));
#7196 = FILL_AREA_STYLE_COLOUR('',#7197);
#7197 = DRAUGHTING_PRE_DEFINED_COLOUR('red');
#7198 = MECHANICAL_DESIGN_GEOMETRIC_PRESENTATION_REPRESENTATION('',(
    #7199),#3550);
#7199 = STYLED_ITEM('color',(#7200),#3217);
#7200 = PRESENTATION_STYLE_ASSIGNMENT((#7201));
#7201 = SURFACE_STYLE_USAGE(.BOTH.,#7202);
#7202 = SURFACE_SIDE_STYLE('',(#7203));
#7203 = SURFACE_STYLE_FILL_AREA(#7204);
#7204 = FILL_AREA_STYLE('',(#7205));
#7205 = FILL_AREA_STYLE_COLOUR('',#7106);
#7206 = MECHANICAL_DESIGN_GEOMETRIC_PRESENTATION_REPRESENTATION('',(
    #7207),#4286);
#7207 = STYLED_ITEM('color',(#7208),#4035);
#7208 = PRESENTATION_STYLE_ASSIGNMENT((#7209));
#7209 = SURFACE_STYLE_USAGE(.BOTH.,#7210);
#7210 = SURFACE_SIDE_STYLE('',(#7211));
#7211 = SURFACE_STYLE_FILL_AREA(#7212);
#7212 = FILL_AREA_STYLE('',(#7213));
#7213 = FILL_AREA_STYLE_COLOUR('',#7139);
#7214 = MECHANICAL_DESIGN_GEOMETRIC_PRESENTATION_REPRESENTATION('',(
    #7215),#4016);
#7215 = STYLED_ITEM('color',(#7216),#3918);
#7216 = PRESENTATION_STYLE_ASSIGNMENT((#7217));
#7217 = SURFACE_STYLE_USAGE(.BOTH.,#7218);
#7218 = SURFACE_SIDE_STYLE('',(#7219));
#7219 = SURFACE_STYLE_FILL_AREA(#7220);
#7220 = FILL_AREA_STYLE('',(#7221));
#7221 = FILL_AREA_STYLE_COLOUR('',#7088);
#7222 = MECHANICAL_DESIGN_GEOMETRIC_PRESENTATION_REPRESENTATION('',(
    #7223),#7069);
#7223 = STYLED_ITEM('color',(#7224),#6739);
#7224 = PRESENTATION_STYLE_ASSIGNMENT((#7225));
#7225 = SURFACE_STYLE_USAGE(.BOTH.,#7226);
#7226 = SURFACE_SIDE_STYLE('',(#7227));
#7227 = SURFACE_STYLE_FILL_AREA(#7228);
#7228 = FILL_AREA_STYLE('',(#7229));
#7229 = FILL_AREA_STYLE_COLOUR('',#7156);
#7230 = MECHANICAL_DESIGN_GEOMETRIC_PRESENTATION_REPRESENTATION('',(
    #7231),#4678);
#7231 = STYLED_ITEM('color',(#7232),#4580);
#7232 = PRESENTATION_STYLE_ASSIGNMENT((#7233));
#7233 = SURFACE_STYLE_USAGE(.BOTH.,#7234);
#7234 = SURFACE_SIDE_STYLE('',(#7235));
#7235 = SURFACE_STYLE_FILL_AREA(#7236);
#7236 = FILL_AREA_STYLE('',(#7237));
#7237 = FILL_AREA_STYLE_COLOUR('',#7197);
#7238 = MECHANICAL_DESIGN_GEOMETRIC_PRESENTATION_REPRESENTATION('',(
    #7239),#6339);
#7239 = STYLED_ITEM('color',(#7240),#6241);
#7240 = PRESENTATION_STYLE_ASSIGNMENT((#7241));
#7241 = SURFACE_STYLE_USAGE(.BOTH.,#7242);
#7242 = SURFACE_SIDE_STYLE('',(#7243));
#7243 = SURFACE_STYLE_FILL_AREA(#7244);
#7244 = FILL_AREA_STYLE('',(#7245));
#7245 = FILL_AREA_STYLE_COLOUR('',#7197);
#7246 = MECHANICAL_DESIGN_GEOMETRIC_PRESENTATION_REPRESENTATION('',(
    #7247),#3081);
#7247 = STYLED_ITEM('color',(#7248),#2677);
#7248 = PRESENTATION_STYLE_ASSIGNMENT((#7249));
#7249 = SURFACE_STYLE_USAGE(.BOTH.,#7250);
#7250 = SURFACE_SIDE_STYLE('',(#7251));
#7251 = SURFACE_STYLE_FILL_AREA(#7252);
#7252 = FILL_AREA_STYLE('',(#7253));
#7253 = FILL_AREA_STYLE_COLOUR('',#7097);
#7254 = MECHANICAL_DESIGN_GEOMETRIC_PRESENTATION_REPRESENTATION('',(
    #7255),#5496);
#7255 = STYLED_ITEM('color',(#7256),#5398);
#7256 = PRESENTATION_STYLE_ASSIGNMENT((#7257));
#7257 = SURFACE_STYLE_USAGE(.BOTH.,#7258);
#7258 = SURFACE_SIDE_STYLE('',(#7259));
#7259 = SURFACE_STYLE_FILL_AREA(#7260);
#7260 = FILL_AREA_STYLE('',(#7261));
#7261 = FILL_AREA_STYLE_COLOUR('',#7088);
#7262 = MECHANICAL_DESIGN_GEOMETRIC_PRESENTATION_REPRESENTATION('',(
    #7263),#3198);
#7263 = STYLED_ITEM('color',(#7264),#3100);
#7264 = PRESENTATION_STYLE_ASSIGNMENT((#7265));
#7265 = SURFACE_STYLE_USAGE(.BOTH.,#7266);
#7266 = SURFACE_SIDE_STYLE('',(#7267));
#7267 = SURFACE_STYLE_FILL_AREA(#7268);
#7268 = FILL_AREA_STYLE('',(#7269));
#7269 = FILL_AREA_STYLE_COLOUR('',#7197);
#7270 = MECHANICAL_DESIGN_GEOMETRIC_PRESENTATION_REPRESENTATION('',(
    #7271),#5379);
#7271 = STYLED_ITEM('color',(#7272),#5049);
#7272 = PRESENTATION_STYLE_ASSIGNMENT((#7273));
#7273 = SURFACE_STYLE_USAGE(.BOTH.,#7274);
#7274 = SURFACE_SIDE_STYLE('',(#7275));
#7275 = SURFACE_STYLE_FILL_AREA(#7276);
#7276 = FILL_AREA_STYLE('',(#7277));
#7277 = FILL_AREA_STYLE_COLOUR('',#7156);
ENDSEC;
END-ISO-10303-21;
