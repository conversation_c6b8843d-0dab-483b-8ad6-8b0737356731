ISO-10303-21;
HEADER;
FILE_DESCRIPTION(('Open CASCADE Model'),'2;1');
FILE_NAME('Pile_PC1-P3_original','2025-08-25T20:07:48',('Author'),(
    'Open CASCADE'),'Open CASCADE STEP processor 7.8','build123d',
  'Unknown');
FILE_SCHEMA(('AUTOMOTIVE_DESIGN { 1 0 10303 214 1 1 1 1 }'));
ENDSEC;
DATA;
#1 = APPLICATION_PROTOCOL_DEFINITION('international standard',
  'automotive_design',2000,#2);
#2 = APPLICATION_CONTEXT(
  'core data for automotive mechanical design processes');
#3 = SHAPE_DEFINITION_REPRESENTATION(#4,#10);
#4 = PRODUCT_DEFINITION_SHAPE('','',#5);
#5 = PRODUCT_DEFINITION('design','',#6,#9);
#6 = PRODUCT_DEFINITION_FORMATION('','',#7);
#7 = PRODUCT('Pile_PC1-P3_original','Pile_PC1-P3_original','',(#8));
#8 = PRODUCT_CONTEXT('',#2,'mechanical');
#9 = PRODUCT_DEFINITION_CONTEXT('part definition',#2,'design');
#10 = SHAPE_REPRESENTATION('',(#11,#15,#19,#23),#27);
#11 = AXIS2_PLACEMENT_3D('',#12,#13,#14);
#12 = CARTESIAN_POINT('',(0.,0.,0.));
#13 = DIRECTION('',(0.,0.,1.));
#14 = DIRECTION('',(1.,0.,-0.));
#15 = AXIS2_PLACEMENT_3D('',#16,#17,#18);
#16 = CARTESIAN_POINT('',(51.614918,130.739863,10.212298));
#17 = DIRECTION('',(0.,0.,1.));
#18 = DIRECTION('',(1.,0.,-0.));
#19 = AXIS2_PLACEMENT_3D('',#20,#21,#22);
#20 = CARTESIAN_POINT('',(0.,0.,0.));
#21 = DIRECTION('',(0.,0.,1.));
#22 = DIRECTION('',(1.,0.,-0.));
#23 = AXIS2_PLACEMENT_3D('',#24,#25,#26);
#24 = CARTESIAN_POINT('',(0.,0.,0.));
#25 = DIRECTION('',(0.,0.,1.));
#26 = DIRECTION('',(1.,0.,-0.));
#27 = ( GEOMETRIC_REPRESENTATION_CONTEXT(3) 
GLOBAL_UNCERTAINTY_ASSIGNED_CONTEXT((#31)) GLOBAL_UNIT_ASSIGNED_CONTEXT(
(#28,#29,#30)) REPRESENTATION_CONTEXT('Context #1',
  '3D Context with UNIT and UNCERTAINTY') );
#28 = ( LENGTH_UNIT() NAMED_UNIT(*) SI_UNIT(.MILLI.,.METRE.) );
#29 = ( NAMED_UNIT(*) PLANE_ANGLE_UNIT() SI_UNIT($,.RADIAN.) );
#30 = ( NAMED_UNIT(*) SI_UNIT($,.STERADIAN.) SOLID_ANGLE_UNIT() );
#31 = UNCERTAINTY_MEASURE_WITH_UNIT(LENGTH_MEASURE(1.E-07),#28,
  'distance_accuracy_value','confusion accuracy');
#32 = PRODUCT_RELATED_PRODUCT_CATEGORY('part',$,(#7));
#33 = SHAPE_DEFINITION_REPRESENTATION(#34,#40);
#34 = PRODUCT_DEFINITION_SHAPE('','',#35);
#35 = PRODUCT_DEFINITION('design','',#36,#39);
#36 = PRODUCT_DEFINITION_FORMATION('','',#37);
#37 = PRODUCT('PC1-P3_original_Part1','PC1-P3_original_Part1','',(#38));
#38 = PRODUCT_CONTEXT('',#2,'mechanical');
#39 = PRODUCT_DEFINITION_CONTEXT('part definition',#2,'design');
#40 = SHAPE_REPRESENTATION('',(#11,#41),#45);
#41 = AXIS2_PLACEMENT_3D('',#42,#43,#44);
#42 = CARTESIAN_POINT('',(0.,0.,-7.787702));
#43 = DIRECTION('',(0.,0.,1.));
#44 = DIRECTION('',(1.,0.,0.));
#45 = ( GEOMETRIC_REPRESENTATION_CONTEXT(3) 
GLOBAL_UNCERTAINTY_ASSIGNED_CONTEXT((#49)) GLOBAL_UNIT_ASSIGNED_CONTEXT(
(#46,#47,#48)) REPRESENTATION_CONTEXT('Context #1',
  '3D Context with UNIT and UNCERTAINTY') );
#46 = ( LENGTH_UNIT() NAMED_UNIT(*) SI_UNIT(.MILLI.,.METRE.) );
#47 = ( NAMED_UNIT(*) PLANE_ANGLE_UNIT() SI_UNIT($,.RADIAN.) );
#48 = ( NAMED_UNIT(*) SI_UNIT($,.STERADIAN.) SOLID_ANGLE_UNIT() );
#49 = UNCERTAINTY_MEASURE_WITH_UNIT(LENGTH_MEASURE(1.E-07),#46,
  'distance_accuracy_value','confusion accuracy');
#50 = CONTEXT_DEPENDENT_SHAPE_REPRESENTATION(#51,#53);
#51 = ( REPRESENTATION_RELATIONSHIP('','',#40,#10) 
REPRESENTATION_RELATIONSHIP_WITH_TRANSFORMATION(#52) 
SHAPE_REPRESENTATION_RELATIONSHIP() );
#52 = ITEM_DEFINED_TRANSFORMATION('','',#11,#15);
#53 = PRODUCT_DEFINITION_SHAPE('Placement','Placement of an item',#54);
#54 = NEXT_ASSEMBLY_USAGE_OCCURRENCE('10','=>[0:1:1:2]','',#5,#35,$);
#55 = PRODUCT_RELATED_PRODUCT_CATEGORY('part',$,(#37));
#56 = SHAPE_DEFINITION_REPRESENTATION(#57,#63);
#57 = PRODUCT_DEFINITION_SHAPE('','',#58);
#58 = PRODUCT_DEFINITION('design','',#59,#62);
#59 = PRODUCT_DEFINITION_FORMATION('','',#60);
#60 = PRODUCT('SOLID','SOLID','',(#61));
#61 = PRODUCT_CONTEXT('',#2,'mechanical');
#62 = PRODUCT_DEFINITION_CONTEXT('part definition',#2,'design');
#63 = ADVANCED_BREP_SHAPE_REPRESENTATION('',(#11,#64),#162);
#64 = MANIFOLD_SOLID_BREP('',#65);
#65 = CLOSED_SHELL('',(#66,#154,#158));
#66 = ADVANCED_FACE('',(#67),#80,.T.);
#67 = FACE_BOUND('',#68,.T.);
#68 = EDGE_LOOP('',(#69,#103,#126,#153));
#69 = ORIENTED_EDGE('',*,*,#70,.F.);
#70 = EDGE_CURVE('',#71,#71,#73,.T.);
#71 = VERTEX_POINT('',#72);
#72 = CARTESIAN_POINT('',(0.275,-6.73555739531E-17,15.575404));
#73 = SURFACE_CURVE('',#74,(#79,#91),.PCURVE_S1.);
#74 = CIRCLE('',#75,0.275);
#75 = AXIS2_PLACEMENT_3D('',#76,#77,#78);
#76 = CARTESIAN_POINT('',(0.,0.,15.575404));
#77 = DIRECTION('',(0.,0.,1.));
#78 = DIRECTION('',(1.,0.,-0.));
#79 = PCURVE('',#80,#85);
#80 = CYLINDRICAL_SURFACE('',#81,0.275);
#81 = AXIS2_PLACEMENT_3D('',#82,#83,#84);
#82 = CARTESIAN_POINT('',(0.,0.,0.));
#83 = DIRECTION('',(0.,0.,1.));
#84 = DIRECTION('',(1.,0.,-0.));
#85 = DEFINITIONAL_REPRESENTATION('',(#86),#90);
#86 = LINE('',#87,#88);
#87 = CARTESIAN_POINT('',(0.,15.575404));
#88 = VECTOR('',#89,1.);
#89 = DIRECTION('',(1.,0.));
#90 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#91 = PCURVE('',#92,#97);
#92 = PLANE('',#93);
#93 = AXIS2_PLACEMENT_3D('',#94,#95,#96);
#94 = CARTESIAN_POINT('',(0.,0.,15.575404));
#95 = DIRECTION('',(0.,0.,1.));
#96 = DIRECTION('',(1.,0.,-0.));
#97 = DEFINITIONAL_REPRESENTATION('',(#98),#102);
#98 = CIRCLE('',#99,0.275);
#99 = AXIS2_PLACEMENT_2D('',#100,#101);
#100 = CARTESIAN_POINT('',(0.,0.));
#101 = DIRECTION('',(1.,0.));
#102 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#103 = ORIENTED_EDGE('',*,*,#104,.F.);
#104 = EDGE_CURVE('',#105,#71,#107,.T.);
#105 = VERTEX_POINT('',#106);
#106 = CARTESIAN_POINT('',(0.275,-6.73555739531E-17,0.));
#107 = SEAM_CURVE('',#108,(#112,#119),.PCURVE_S1.);
#108 = LINE('',#109,#110);
#109 = CARTESIAN_POINT('',(0.275,-6.73555739531E-17,0.));
#110 = VECTOR('',#111,1.);
#111 = DIRECTION('',(0.,0.,1.));
#112 = PCURVE('',#80,#113);
#113 = DEFINITIONAL_REPRESENTATION('',(#114),#118);
#114 = LINE('',#115,#116);
#115 = CARTESIAN_POINT('',(6.28318530718,-0.));
#116 = VECTOR('',#117,1.);
#117 = DIRECTION('',(0.,1.));
#118 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#119 = PCURVE('',#80,#120);
#120 = DEFINITIONAL_REPRESENTATION('',(#121),#125);
#121 = LINE('',#122,#123);
#122 = CARTESIAN_POINT('',(0.,-0.));
#123 = VECTOR('',#124,1.);
#124 = DIRECTION('',(0.,1.));
#125 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#126 = ORIENTED_EDGE('',*,*,#127,.T.);
#127 = EDGE_CURVE('',#105,#105,#128,.T.);
#128 = SURFACE_CURVE('',#129,(#134,#141),.PCURVE_S1.);
#129 = CIRCLE('',#130,0.275);
#130 = AXIS2_PLACEMENT_3D('',#131,#132,#133);
#131 = CARTESIAN_POINT('',(0.,0.,0.));
#132 = DIRECTION('',(0.,0.,1.));
#133 = DIRECTION('',(1.,0.,-0.));
#134 = PCURVE('',#80,#135);
#135 = DEFINITIONAL_REPRESENTATION('',(#136),#140);
#136 = LINE('',#137,#138);
#137 = CARTESIAN_POINT('',(0.,0.));
#138 = VECTOR('',#139,1.);
#139 = DIRECTION('',(1.,0.));
#140 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#141 = PCURVE('',#142,#147);
#142 = PLANE('',#143);
#143 = AXIS2_PLACEMENT_3D('',#144,#145,#146);
#144 = CARTESIAN_POINT('',(0.,0.,0.));
#145 = DIRECTION('',(0.,0.,1.));
#146 = DIRECTION('',(1.,0.,-0.));
#147 = DEFINITIONAL_REPRESENTATION('',(#148),#152);
#148 = CIRCLE('',#149,0.275);
#149 = AXIS2_PLACEMENT_2D('',#150,#151);
#150 = CARTESIAN_POINT('',(0.,0.));
#151 = DIRECTION('',(1.,0.));
#152 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#153 = ORIENTED_EDGE('',*,*,#104,.T.);
#154 = ADVANCED_FACE('',(#155),#92,.T.);
#155 = FACE_BOUND('',#156,.T.);
#156 = EDGE_LOOP('',(#157));
#157 = ORIENTED_EDGE('',*,*,#70,.T.);
#158 = ADVANCED_FACE('',(#159),#142,.F.);
#159 = FACE_BOUND('',#160,.T.);
#160 = EDGE_LOOP('',(#161));
#161 = ORIENTED_EDGE('',*,*,#127,.F.);
#162 = ( GEOMETRIC_REPRESENTATION_CONTEXT(3) 
GLOBAL_UNCERTAINTY_ASSIGNED_CONTEXT((#166)) GLOBAL_UNIT_ASSIGNED_CONTEXT
((#163,#164,#165)) REPRESENTATION_CONTEXT('Context #1',
  '3D Context with UNIT and UNCERTAINTY') );
#163 = ( LENGTH_UNIT() NAMED_UNIT(*) SI_UNIT(.MILLI.,.METRE.) );
#164 = ( NAMED_UNIT(*) PLANE_ANGLE_UNIT() SI_UNIT($,.RADIAN.) );
#165 = ( NAMED_UNIT(*) SI_UNIT($,.STERADIAN.) SOLID_ANGLE_UNIT() );
#166 = UNCERTAINTY_MEASURE_WITH_UNIT(LENGTH_MEASURE(1.E-07),#163,
  'distance_accuracy_value','confusion accuracy');
#167 = CONTEXT_DEPENDENT_SHAPE_REPRESENTATION(#168,#170);
#168 = ( REPRESENTATION_RELATIONSHIP('','',#63,#40) 
REPRESENTATION_RELATIONSHIP_WITH_TRANSFORMATION(#169) 
SHAPE_REPRESENTATION_RELATIONSHIP() );
#169 = ITEM_DEFINED_TRANSFORMATION('','',#11,#41);
#170 = PRODUCT_DEFINITION_SHAPE('Placement','Placement of an item',#171
  );
#171 = NEXT_ASSEMBLY_USAGE_OCCURRENCE('9','=>[0:1:1:3]','',#35,#58,$);
#172 = PRODUCT_RELATED_PRODUCT_CATEGORY('part',$,(#60));
#173 = SHAPE_DEFINITION_REPRESENTATION(#174,#180);
#174 = PRODUCT_DEFINITION_SHAPE('','',#175);
#175 = PRODUCT_DEFINITION('design','',#176,#179);
#176 = PRODUCT_DEFINITION_FORMATION('','',#177);
#177 = PRODUCT('PC1-P3_original_Part2','PC1-P3_original_Part2','',(#178)
  );
#178 = PRODUCT_CONTEXT('',#2,'mechanical');
#179 = PRODUCT_DEFINITION_CONTEXT('part definition',#2,'design');
#180 = ADVANCED_BREP_SHAPE_REPRESENTATION('',(#11,#181),#327);
#181 = MANIFOLD_SOLID_BREP('',#182);
#182 = CLOSED_SHELL('',(#183,#270,#300));
#183 = ADVANCED_FACE('',(#184),#197,.T.);
#184 = FACE_BOUND('',#185,.T.);
#185 = EDGE_LOOP('',(#186,#220,#243,#269));
#186 = ORIENTED_EDGE('',*,*,#187,.F.);
#187 = EDGE_CURVE('',#188,#188,#190,.T.);
#188 = VERTEX_POINT('',#189);
#189 = CARTESIAN_POINT('',(55.931369884327,130.739863,9.424596));
#190 = SURFACE_CURVE('',#191,(#196,#208),.PCURVE_S1.);
#191 = CIRCLE('',#192,4.316451884327);
#192 = AXIS2_PLACEMENT_3D('',#193,#194,#195);
#193 = CARTESIAN_POINT('',(51.614918,130.739863,9.424596));
#194 = DIRECTION('',(0.,0.,1.));
#195 = DIRECTION('',(1.,0.,0.));
#196 = PCURVE('',#197,#202);
#197 = CONICAL_SURFACE('',#198,0.275,0.523598775598);
#198 = AXIS2_PLACEMENT_3D('',#199,#200,#201);
#199 = CARTESIAN_POINT('',(51.614918,130.739863,2.424596));
#200 = DIRECTION('',(0.,0.,1.));
#201 = DIRECTION('',(1.,0.,0.));
#202 = DEFINITIONAL_REPRESENTATION('',(#203),#207);
#203 = LINE('',#204,#205);
#204 = CARTESIAN_POINT('',(0.,7.));
#205 = VECTOR('',#206,1.);
#206 = DIRECTION('',(1.,0.));
#207 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#208 = PCURVE('',#209,#214);
#209 = PLANE('',#210);
#210 = AXIS2_PLACEMENT_3D('',#211,#212,#213);
#211 = CARTESIAN_POINT('',(51.614918,130.739863,9.424596));
#212 = DIRECTION('',(0.,0.,1.));
#213 = DIRECTION('',(1.,0.,0.));
#214 = DEFINITIONAL_REPRESENTATION('',(#215),#219);
#215 = CIRCLE('',#216,4.316451884327);
#216 = AXIS2_PLACEMENT_2D('',#217,#218);
#217 = CARTESIAN_POINT('',(0.,0.));
#218 = DIRECTION('',(1.,0.));
#219 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#220 = ORIENTED_EDGE('',*,*,#221,.F.);
#221 = EDGE_CURVE('',#222,#188,#224,.T.);
#222 = VERTEX_POINT('',#223);
#223 = CARTESIAN_POINT('',(51.889918,130.739863,2.424596));
#224 = SEAM_CURVE('',#225,(#229,#236),.PCURVE_S1.);
#225 = LINE('',#226,#227);
#226 = CARTESIAN_POINT('',(51.889918,130.739863,2.424596));
#227 = VECTOR('',#228,1.);
#228 = DIRECTION('',(0.5,-1.224646799147E-16,0.866025403784));
#229 = PCURVE('',#197,#230);
#230 = DEFINITIONAL_REPRESENTATION('',(#231),#235);
#231 = LINE('',#232,#233);
#232 = CARTESIAN_POINT('',(6.28318530718,-0.));
#233 = VECTOR('',#234,1.);
#234 = DIRECTION('',(0.,1.));
#235 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#236 = PCURVE('',#197,#237);
#237 = DEFINITIONAL_REPRESENTATION('',(#238),#242);
#238 = LINE('',#239,#240);
#239 = CARTESIAN_POINT('',(0.,-0.));
#240 = VECTOR('',#241,1.);
#241 = DIRECTION('',(0.,1.));
#242 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#243 = ORIENTED_EDGE('',*,*,#244,.T.);
#244 = EDGE_CURVE('',#222,#222,#245,.T.);
#245 = SURFACE_CURVE('',#246,(#251,#258),.PCURVE_S1.);
#246 = CIRCLE('',#247,0.275);
#247 = AXIS2_PLACEMENT_3D('',#248,#249,#250);
#248 = CARTESIAN_POINT('',(51.614918,130.739863,2.424596));
#249 = DIRECTION('',(0.,0.,1.));
#250 = DIRECTION('',(1.,0.,0.));
#251 = PCURVE('',#197,#252);
#252 = DEFINITIONAL_REPRESENTATION('',(#253),#257);
#253 = LINE('',#254,#255);
#254 = CARTESIAN_POINT('',(0.,0.));
#255 = VECTOR('',#256,1.);
#256 = DIRECTION('',(1.,0.));
#257 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#258 = PCURVE('',#259,#264);
#259 = CYLINDRICAL_SURFACE('',#260,0.275);
#260 = AXIS2_PLACEMENT_3D('',#261,#262,#263);
#261 = CARTESIAN_POINT('',(51.614918,130.739863,2.424596));
#262 = DIRECTION('',(0.,0.,1.));
#263 = DIRECTION('',(1.,0.,0.));
#264 = DEFINITIONAL_REPRESENTATION('',(#265),#268);
#265 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#266,#267),.UNSPECIFIED.,.F.,.F.,
  (2,2),(0.,6.28318530718),.PIECEWISE_BEZIER_KNOTS.);
#266 = CARTESIAN_POINT('',(0.,0.));
#267 = CARTESIAN_POINT('',(6.28318530718,0.));
#268 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#269 = ORIENTED_EDGE('',*,*,#221,.T.);
#270 = ADVANCED_FACE('',(#271,#274),#209,.T.);
#271 = FACE_BOUND('',#272,.T.);
#272 = EDGE_LOOP('',(#273));
#273 = ORIENTED_EDGE('',*,*,#187,.T.);
#274 = FACE_BOUND('',#275,.T.);
#275 = EDGE_LOOP('',(#276));
#276 = ORIENTED_EDGE('',*,*,#277,.F.);
#277 = EDGE_CURVE('',#278,#278,#280,.T.);
#278 = VERTEX_POINT('',#279);
#279 = CARTESIAN_POINT('',(51.889918,130.739863,9.424596));
#280 = SURFACE_CURVE('',#281,(#286,#293),.PCURVE_S1.);
#281 = CIRCLE('',#282,0.275);
#282 = AXIS2_PLACEMENT_3D('',#283,#284,#285);
#283 = CARTESIAN_POINT('',(51.614918,130.739863,9.424596));
#284 = DIRECTION('',(0.,0.,1.));
#285 = DIRECTION('',(1.,0.,0.));
#286 = PCURVE('',#209,#287);
#287 = DEFINITIONAL_REPRESENTATION('',(#288),#292);
#288 = CIRCLE('',#289,0.275);
#289 = AXIS2_PLACEMENT_2D('',#290,#291);
#290 = CARTESIAN_POINT('',(0.,0.));
#291 = DIRECTION('',(1.,0.));
#292 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#293 = PCURVE('',#259,#294);
#294 = DEFINITIONAL_REPRESENTATION('',(#295),#299);
#295 = LINE('',#296,#297);
#296 = CARTESIAN_POINT('',(0.,7.));
#297 = VECTOR('',#298,1.);
#298 = DIRECTION('',(1.,0.));
#299 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#300 = ADVANCED_FACE('',(#301),#259,.F.);
#301 = FACE_BOUND('',#302,.F.);
#302 = EDGE_LOOP('',(#303,#304,#325,#326));
#303 = ORIENTED_EDGE('',*,*,#277,.F.);
#304 = ORIENTED_EDGE('',*,*,#305,.F.);
#305 = EDGE_CURVE('',#222,#278,#306,.T.);
#306 = SEAM_CURVE('',#307,(#311,#318),.PCURVE_S1.);
#307 = LINE('',#308,#309);
#308 = CARTESIAN_POINT('',(51.889918,130.739863,2.424596));
#309 = VECTOR('',#310,1.);
#310 = DIRECTION('',(0.,0.,1.));
#311 = PCURVE('',#259,#312);
#312 = DEFINITIONAL_REPRESENTATION('',(#313),#317);
#313 = LINE('',#314,#315);
#314 = CARTESIAN_POINT('',(6.28318530718,-0.));
#315 = VECTOR('',#316,1.);
#316 = DIRECTION('',(0.,1.));
#317 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#318 = PCURVE('',#259,#319);
#319 = DEFINITIONAL_REPRESENTATION('',(#320),#324);
#320 = LINE('',#321,#322);
#321 = CARTESIAN_POINT('',(0.,-0.));
#322 = VECTOR('',#323,1.);
#323 = DIRECTION('',(0.,1.));
#324 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#325 = ORIENTED_EDGE('',*,*,#244,.T.);
#326 = ORIENTED_EDGE('',*,*,#305,.T.);
#327 = ( GEOMETRIC_REPRESENTATION_CONTEXT(3) 
GLOBAL_UNCERTAINTY_ASSIGNED_CONTEXT((#331)) GLOBAL_UNIT_ASSIGNED_CONTEXT
((#328,#329,#330)) REPRESENTATION_CONTEXT('Context #1',
  '3D Context with UNIT and UNCERTAINTY') );
#328 = ( LENGTH_UNIT() NAMED_UNIT(*) SI_UNIT(.MILLI.,.METRE.) );
#329 = ( NAMED_UNIT(*) PLANE_ANGLE_UNIT() SI_UNIT($,.RADIAN.) );
#330 = ( NAMED_UNIT(*) SI_UNIT($,.STERADIAN.) SOLID_ANGLE_UNIT() );
#331 = UNCERTAINTY_MEASURE_WITH_UNIT(LENGTH_MEASURE(1.E-07),#328,
  'distance_accuracy_value','confusion accuracy');
#332 = CONTEXT_DEPENDENT_SHAPE_REPRESENTATION(#333,#335);
#333 = ( REPRESENTATION_RELATIONSHIP('','',#180,#10) 
REPRESENTATION_RELATIONSHIP_WITH_TRANSFORMATION(#334) 
SHAPE_REPRESENTATION_RELATIONSHIP() );
#334 = ITEM_DEFINED_TRANSFORMATION('','',#11,#19);
#335 = PRODUCT_DEFINITION_SHAPE('Placement','Placement of an item',#336
  );
#336 = NEXT_ASSEMBLY_USAGE_OCCURRENCE('11','=>[0:1:1:4]','',#5,#175,$);
#337 = PRODUCT_RELATED_PRODUCT_CATEGORY('part',$,(#177));
#338 = SHAPE_DEFINITION_REPRESENTATION(#339,#345);
#339 = PRODUCT_DEFINITION_SHAPE('','',#340);
#340 = PRODUCT_DEFINITION('design','',#341,#344);
#341 = PRODUCT_DEFINITION_FORMATION('','',#342);
#342 = PRODUCT('PC1-P3_original_Part3','PC1-P3_original_Part3','',(#343)
  );
#343 = PRODUCT_CONTEXT('',#2,'mechanical');
#344 = PRODUCT_DEFINITION_CONTEXT('part definition',#2,'design');
#345 = ADVANCED_BREP_SHAPE_REPRESENTATION('',(#11,#346),#528);
#346 = MANIFOLD_SOLID_BREP('',#347);
#347 = CLOSED_SHELL('',(#348,#436,#471,#501));
#348 = ADVANCED_FACE('',(#349),#362,.T.);
#349 = FACE_BOUND('',#350,.T.);
#350 = EDGE_LOOP('',(#351,#385,#408,#435));
#351 = ORIENTED_EDGE('',*,*,#352,.F.);
#352 = EDGE_CURVE('',#353,#353,#355,.T.);
#353 = VERTEX_POINT('',#354);
#354 = CARTESIAN_POINT('',(55.931369884327,130.739863,18.));
#355 = SURFACE_CURVE('',#356,(#361,#373),.PCURVE_S1.);
#356 = CIRCLE('',#357,4.316451884327);
#357 = AXIS2_PLACEMENT_3D('',#358,#359,#360);
#358 = CARTESIAN_POINT('',(51.614918,130.739863,18.));
#359 = DIRECTION('',(0.,0.,1.));
#360 = DIRECTION('',(1.,0.,0.));
#361 = PCURVE('',#362,#367);
#362 = CYLINDRICAL_SURFACE('',#363,4.316451884327);
#363 = AXIS2_PLACEMENT_3D('',#364,#365,#366);
#364 = CARTESIAN_POINT('',(51.614918,130.739863,9.424596));
#365 = DIRECTION('',(0.,0.,1.));
#366 = DIRECTION('',(1.,0.,0.));
#367 = DEFINITIONAL_REPRESENTATION('',(#368),#372);
#368 = LINE('',#369,#370);
#369 = CARTESIAN_POINT('',(0.,8.575404));
#370 = VECTOR('',#371,1.);
#371 = DIRECTION('',(1.,0.));
#372 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#373 = PCURVE('',#374,#379);
#374 = PLANE('',#375);
#375 = AXIS2_PLACEMENT_3D('',#376,#377,#378);
#376 = CARTESIAN_POINT('',(51.614918,130.739863,18.));
#377 = DIRECTION('',(0.,0.,1.));
#378 = DIRECTION('',(1.,0.,0.));
#379 = DEFINITIONAL_REPRESENTATION('',(#380),#384);
#380 = CIRCLE('',#381,4.316451884327);
#381 = AXIS2_PLACEMENT_2D('',#382,#383);
#382 = CARTESIAN_POINT('',(0.,0.));
#383 = DIRECTION('',(1.,0.));
#384 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#385 = ORIENTED_EDGE('',*,*,#386,.F.);
#386 = EDGE_CURVE('',#387,#353,#389,.T.);
#387 = VERTEX_POINT('',#388);
#388 = CARTESIAN_POINT('',(55.931369884327,130.739863,9.424596));
#389 = SEAM_CURVE('',#390,(#394,#401),.PCURVE_S1.);
#390 = LINE('',#391,#392);
#391 = CARTESIAN_POINT('',(55.931369884327,130.739863,9.424596));
#392 = VECTOR('',#393,1.);
#393 = DIRECTION('',(0.,0.,1.));
#394 = PCURVE('',#362,#395);
#395 = DEFINITIONAL_REPRESENTATION('',(#396),#400);
#396 = LINE('',#397,#398);
#397 = CARTESIAN_POINT('',(6.28318530718,-0.));
#398 = VECTOR('',#399,1.);
#399 = DIRECTION('',(0.,1.));
#400 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#401 = PCURVE('',#362,#402);
#402 = DEFINITIONAL_REPRESENTATION('',(#403),#407);
#403 = LINE('',#404,#405);
#404 = CARTESIAN_POINT('',(0.,-0.));
#405 = VECTOR('',#406,1.);
#406 = DIRECTION('',(0.,1.));
#407 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#408 = ORIENTED_EDGE('',*,*,#409,.T.);
#409 = EDGE_CURVE('',#387,#387,#410,.T.);
#410 = SURFACE_CURVE('',#411,(#416,#423),.PCURVE_S1.);
#411 = CIRCLE('',#412,4.316451884327);
#412 = AXIS2_PLACEMENT_3D('',#413,#414,#415);
#413 = CARTESIAN_POINT('',(51.614918,130.739863,9.424596));
#414 = DIRECTION('',(0.,0.,1.));
#415 = DIRECTION('',(1.,0.,0.));
#416 = PCURVE('',#362,#417);
#417 = DEFINITIONAL_REPRESENTATION('',(#418),#422);
#418 = LINE('',#419,#420);
#419 = CARTESIAN_POINT('',(0.,0.));
#420 = VECTOR('',#421,1.);
#421 = DIRECTION('',(1.,0.));
#422 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#423 = PCURVE('',#424,#429);
#424 = PLANE('',#425);
#425 = AXIS2_PLACEMENT_3D('',#426,#427,#428);
#426 = CARTESIAN_POINT('',(51.614918,130.739863,9.424596));
#427 = DIRECTION('',(0.,0.,1.));
#428 = DIRECTION('',(1.,0.,0.));
#429 = DEFINITIONAL_REPRESENTATION('',(#430),#434);
#430 = CIRCLE('',#431,4.316451884327);
#431 = AXIS2_PLACEMENT_2D('',#432,#433);
#432 = CARTESIAN_POINT('',(0.,0.));
#433 = DIRECTION('',(1.,0.));
#434 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#435 = ORIENTED_EDGE('',*,*,#386,.T.);
#436 = ADVANCED_FACE('',(#437,#440),#374,.T.);
#437 = FACE_BOUND('',#438,.T.);
#438 = EDGE_LOOP('',(#439));
#439 = ORIENTED_EDGE('',*,*,#352,.T.);
#440 = FACE_BOUND('',#441,.T.);
#441 = EDGE_LOOP('',(#442));
#442 = ORIENTED_EDGE('',*,*,#443,.F.);
#443 = EDGE_CURVE('',#444,#444,#446,.T.);
#444 = VERTEX_POINT('',#445);
#445 = CARTESIAN_POINT('',(51.889918,130.739863,18.));
#446 = SURFACE_CURVE('',#447,(#452,#459),.PCURVE_S1.);
#447 = CIRCLE('',#448,0.275);
#448 = AXIS2_PLACEMENT_3D('',#449,#450,#451);
#449 = CARTESIAN_POINT('',(51.614918,130.739863,18.));
#450 = DIRECTION('',(0.,0.,1.));
#451 = DIRECTION('',(1.,0.,0.));
#452 = PCURVE('',#374,#453);
#453 = DEFINITIONAL_REPRESENTATION('',(#454),#458);
#454 = CIRCLE('',#455,0.275);
#455 = AXIS2_PLACEMENT_2D('',#456,#457);
#456 = CARTESIAN_POINT('',(0.,0.));
#457 = DIRECTION('',(1.,0.));
#458 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#459 = PCURVE('',#460,#465);
#460 = CYLINDRICAL_SURFACE('',#461,0.275);
#461 = AXIS2_PLACEMENT_3D('',#462,#463,#464);
#462 = CARTESIAN_POINT('',(51.614918,130.739863,9.424596));
#463 = DIRECTION('',(0.,0.,1.));
#464 = DIRECTION('',(1.,0.,0.));
#465 = DEFINITIONAL_REPRESENTATION('',(#466),#470);
#466 = LINE('',#467,#468);
#467 = CARTESIAN_POINT('',(0.,8.575404));
#468 = VECTOR('',#469,1.);
#469 = DIRECTION('',(1.,0.));
#470 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#471 = ADVANCED_FACE('',(#472,#475),#424,.F.);
#472 = FACE_BOUND('',#473,.F.);
#473 = EDGE_LOOP('',(#474));
#474 = ORIENTED_EDGE('',*,*,#409,.T.);
#475 = FACE_BOUND('',#476,.F.);
#476 = EDGE_LOOP('',(#477));
#477 = ORIENTED_EDGE('',*,*,#478,.F.);
#478 = EDGE_CURVE('',#479,#479,#481,.T.);
#479 = VERTEX_POINT('',#480);
#480 = CARTESIAN_POINT('',(51.889918,130.739863,9.424596));
#481 = SURFACE_CURVE('',#482,(#487,#494),.PCURVE_S1.);
#482 = CIRCLE('',#483,0.275);
#483 = AXIS2_PLACEMENT_3D('',#484,#485,#486);
#484 = CARTESIAN_POINT('',(51.614918,130.739863,9.424596));
#485 = DIRECTION('',(0.,0.,1.));
#486 = DIRECTION('',(1.,0.,0.));
#487 = PCURVE('',#424,#488);
#488 = DEFINITIONAL_REPRESENTATION('',(#489),#493);
#489 = CIRCLE('',#490,0.275);
#490 = AXIS2_PLACEMENT_2D('',#491,#492);
#491 = CARTESIAN_POINT('',(0.,0.));
#492 = DIRECTION('',(1.,0.));
#493 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#494 = PCURVE('',#460,#495);
#495 = DEFINITIONAL_REPRESENTATION('',(#496),#500);
#496 = LINE('',#497,#498);
#497 = CARTESIAN_POINT('',(0.,0.));
#498 = VECTOR('',#499,1.);
#499 = DIRECTION('',(1.,0.));
#500 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#501 = ADVANCED_FACE('',(#502),#460,.F.);
#502 = FACE_BOUND('',#503,.F.);
#503 = EDGE_LOOP('',(#504,#505,#526,#527));
#504 = ORIENTED_EDGE('',*,*,#443,.F.);
#505 = ORIENTED_EDGE('',*,*,#506,.F.);
#506 = EDGE_CURVE('',#479,#444,#507,.T.);
#507 = SEAM_CURVE('',#508,(#512,#519),.PCURVE_S1.);
#508 = LINE('',#509,#510);
#509 = CARTESIAN_POINT('',(51.889918,130.739863,9.424596));
#510 = VECTOR('',#511,1.);
#511 = DIRECTION('',(0.,0.,1.));
#512 = PCURVE('',#460,#513);
#513 = DEFINITIONAL_REPRESENTATION('',(#514),#518);
#514 = LINE('',#515,#516);
#515 = CARTESIAN_POINT('',(6.28318530718,-0.));
#516 = VECTOR('',#517,1.);
#517 = DIRECTION('',(0.,1.));
#518 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#519 = PCURVE('',#460,#520);
#520 = DEFINITIONAL_REPRESENTATION('',(#521),#525);
#521 = LINE('',#522,#523);
#522 = CARTESIAN_POINT('',(0.,-0.));
#523 = VECTOR('',#524,1.);
#524 = DIRECTION('',(0.,1.));
#525 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#526 = ORIENTED_EDGE('',*,*,#478,.T.);
#527 = ORIENTED_EDGE('',*,*,#506,.T.);
#528 = ( GEOMETRIC_REPRESENTATION_CONTEXT(3) 
GLOBAL_UNCERTAINTY_ASSIGNED_CONTEXT((#532)) GLOBAL_UNIT_ASSIGNED_CONTEXT
((#529,#530,#531)) REPRESENTATION_CONTEXT('Context #1',
  '3D Context with UNIT and UNCERTAINTY') );
#529 = ( LENGTH_UNIT() NAMED_UNIT(*) SI_UNIT(.MILLI.,.METRE.) );
#530 = ( NAMED_UNIT(*) PLANE_ANGLE_UNIT() SI_UNIT($,.RADIAN.) );
#531 = ( NAMED_UNIT(*) SI_UNIT($,.STERADIAN.) SOLID_ANGLE_UNIT() );
#532 = UNCERTAINTY_MEASURE_WITH_UNIT(LENGTH_MEASURE(1.E-07),#529,
  'distance_accuracy_value','confusion accuracy');
#533 = CONTEXT_DEPENDENT_SHAPE_REPRESENTATION(#534,#536);
#534 = ( REPRESENTATION_RELATIONSHIP('','',#345,#10) 
REPRESENTATION_RELATIONSHIP_WITH_TRANSFORMATION(#535) 
SHAPE_REPRESENTATION_RELATIONSHIP() );
#535 = ITEM_DEFINED_TRANSFORMATION('','',#11,#23);
#536 = PRODUCT_DEFINITION_SHAPE('Placement','Placement of an item',#537
  );
#537 = NEXT_ASSEMBLY_USAGE_OCCURRENCE('12','=>[0:1:1:5]','',#5,#340,$);
#538 = PRODUCT_RELATED_PRODUCT_CATEGORY('part',$,(#342));
#539 = MECHANICAL_DESIGN_GEOMETRIC_PRESENTATION_REPRESENTATION('',(#540)
  ,#162);
#540 = STYLED_ITEM('color',(#541),#64);
#541 = PRESENTATION_STYLE_ASSIGNMENT((#542));
#542 = SURFACE_STYLE_USAGE(.BOTH.,#543);
#543 = SURFACE_SIDE_STYLE('',(#544));
#544 = SURFACE_STYLE_FILL_AREA(#545);
#545 = FILL_AREA_STYLE('',(#546));
#546 = FILL_AREA_STYLE_COLOUR('',#547);
#547 = DRAUGHTING_PRE_DEFINED_COLOUR('blue');
#548 = MECHANICAL_DESIGN_GEOMETRIC_PRESENTATION_REPRESENTATION('',(#549)
  ,#528);
#549 = STYLED_ITEM('color',(#550),#346);
#550 = PRESENTATION_STYLE_ASSIGNMENT((#551));
#551 = SURFACE_STYLE_USAGE(.BOTH.,#552);
#552 = SURFACE_SIDE_STYLE('',(#553));
#553 = SURFACE_STYLE_FILL_AREA(#554);
#554 = FILL_AREA_STYLE('',(#555));
#555 = FILL_AREA_STYLE_COLOUR('',#556);
#556 = DRAUGHTING_PRE_DEFINED_COLOUR('red');
#557 = MECHANICAL_DESIGN_GEOMETRIC_PRESENTATION_REPRESENTATION('',(#558)
  ,#327);
#558 = STYLED_ITEM('color',(#559),#181);
#559 = PRESENTATION_STYLE_ASSIGNMENT((#560));
#560 = SURFACE_STYLE_USAGE(.BOTH.,#561);
#561 = SURFACE_SIDE_STYLE('',(#562));
#562 = SURFACE_STYLE_FILL_AREA(#563);
#563 = FILL_AREA_STYLE('',(#564));
#564 = FILL_AREA_STYLE_COLOUR('',#565);
#565 = DRAUGHTING_PRE_DEFINED_COLOUR('green');
ENDSEC;
END-ISO-10303-21;
