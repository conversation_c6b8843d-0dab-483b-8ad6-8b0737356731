#!/usr/bin/env python3
"""
Component Processing Module

Handles high-level group component processing with Part 1, 2, and 3 coordination.
Implements sophisticated trimming strategies and result assembly.
"""

from typing import Any, List, Optional, Dict
import logging
from .base_types import GeometryResult, GeometryError


class ComponentProcessor:
    """High-level component processing with multi-part coordination."""
    
    def __init__(self, logger: Optional[logging.Logger] = None):
        """Initialize component processor."""
        self.logger = logger or logging.getLogger(__name__)
        
        # Will be injected by main class
        self.union_components = None
        self.difference_operation = None
        self._extract_valid_components = None
        self._extract_solids_safely = None
        self._is_valid_geometry = None
        self._process_part2_components = None
        self._process_part3_components_simple = None
    
    def process_group_components(self, 
                                group_components: Dict[str, List[Any]],
                                group_members: List[str]) -> GeometryResult:
        """
        Process group components with optimized boolean operations.
        
        This method has been refactored to reduce deep nesting by breaking down
        the complex final trimming logic into focused helper methods:
        
        - _prepare_component_lists(): Extract and validate component lists
        - _process_initial_parts(): Process Part 2 and Part 3 initially  
        - _perform_final_group_trimming(): Handle complex final trimming
        - _calculate_result_volumes(): Calculate final volumes
        - _create_final_geometry_result(): Create and return final result
        
        Args:
            group_components: Components organized by type
            group_members: List of group member IDs
            
        Returns:
            GeometryResult with processed components
            
        Raises:
            GeometryError: If processing fails
        """
        from datetime import datetime
        
        union_start = datetime.now()
        self.logger.info(f"🔧 Starting complex union calculation for {len(group_members)} piles...")
        self.logger.info(f"📊 Group members: {', '.join(group_members[:5])}{'...' if len(group_members) > 5 else ''}")
        
        # Log component counts
        part1_count = len(group_components.get('part1', []))
        part2_count = len(group_components.get('part2', []))
        part3_count = len(group_components.get('part3', []))
        self.logger.info(f"📊 Components: Part1={part1_count}, Part2={part2_count}, Part3={part3_count}")
        
        try:
            # Step 1: Prepare and validate component lists
            self.logger.info("⏱️  Step 1: Preparing and validating component lists...")
            step1_start = datetime.now()
            component_lists = self._prepare_component_lists(group_components)
            step1_duration = datetime.now() - step1_start
            self.logger.info(f"✅ Component preparation completed in {step1_duration.total_seconds():.2f}s")

            # 🔍 PART2 DEBUG: Log Part2 components after preparation
            part2_list = component_lists.get('part2_list', [])
            part2_prep_volume = sum(getattr(comp, 'volume', 0.0) for comp in part2_list)
            self.logger.info(f"🔍 PART2 DEBUG: After preparation - {len(part2_list)} Part2 components, {part2_prep_volume:.3f} m³ total")
            
            # Step 2: Process initial parts (Part 2 and Part 3)
            self.logger.info("⏱️  Step 2: Processing initial parts (Part 2 and Part 3)...")
            step2_start = datetime.now()
            part_results = self._process_initial_parts(
                component_lists, group_members
            )
            step2_duration = datetime.now() - step2_start
            self.logger.info(f"✅ Initial parts processing completed in {step2_duration.total_seconds():.2f}s")

            # 🔍 PART2 DEBUG: Log Part2 results after initial processing
            part2_result_parts = part_results.get('part2_result', {}).get('parts', [])
            part2_result_volume = sum(getattr(part, 'volume', 0.0) for part in part2_result_parts)
            self.logger.info(f"🔍 PART2 DEBUG: After initial processing - {len(part2_result_parts)} Part2 parts, {part2_result_volume:.3f} m³ total")

            if part2_prep_volume > 0.1 and part2_result_volume < 0.001:
                self.logger.error(f"🔍 PART2 DEBUG: CRITICAL - Part2 ELIMINATED during initial processing! Input: {part2_prep_volume:.3f} m³ → Output: {part2_result_volume:.3f} m³")
            
            # Step 3: Perform final group trimming (Part 2 only, no Part 1 double-trimming)
            self.logger.info("⏱️  Step 3: Performing final group trimming (Part 2 only, simplified)...")
            step3_start = datetime.now()
            final_part_results = self._perform_final_group_trimming(
                part_results, component_lists['union_part1']
            )
            step3_duration = datetime.now() - step3_start
            self.logger.info(f"✅ Final group trimming completed in {step3_duration.total_seconds():.2f}s")
            
            # Step 4: Calculate final volumes and create result
            self.logger.info("⏱️  Step 4: Calculating final volumes and creating result...")
            step4_start = datetime.now()
            result = self._create_final_geometry_result(
                final_part_results, component_lists['union_part1'], group_members
            )
            step4_duration = datetime.now() - step4_start
            self.logger.info(f"✅ Final result creation completed in {step4_duration.total_seconds():.2f}s")
            
            total_duration = datetime.now() - union_start
            self.logger.info(f"🎉 Complex union calculation completed in {total_duration.total_seconds():.2f}s")
            self.logger.info(f"📊 Result: {len(result.parts)} parts, total volume: {sum(result.volumes.values()):.2f} m³")
            
            return result
            
        except Exception as e:
            error_duration = datetime.now() - union_start
            error_msg = f"🚨 GEOMETRY PROCESSING FAILED for group {group_members} after {error_duration.total_seconds():.2f}s: {type(e).__name__}: {str(e)}"
            self.logger.error(error_msg)
            print(f"❌ GEOMETRY ERROR: {error_msg}")
            raise GeometryError(f"Group processing failed: {e}") from e
    
    def _prepare_component_lists(self, group_components: Dict[str, List[Any]]) -> Dict[str, Any]:
        """
        Extract and validate component lists from group components.
        
        Args:
            group_components: Components organized by type
            
        Returns:
            Dictionary containing validated component lists and union_part1
        """
        from datetime import datetime
        
        self.logger.info("🔧 Extracting and validating components...")
        
        part1_list = self._extract_valid_components(group_components.get('part1', []))
        part2_list = self._extract_valid_components(group_components.get('part2', []))
        part3_list = self._extract_valid_components(group_components.get('part3', []))
        
        self.logger.info(f"📊 Valid components: Part1={len(part1_list)}, Part2={len(part2_list)}, Part3={len(part3_list)}")
        
        # Union Part 1 components for volume calculation (no longer used for cutting)
        if part1_list:
            self.logger.info("⏱️  Creating Part1 union for volume calculation...")
            union_start = datetime.now()
            union_part1 = self.union_components(part1_list)
            union_duration = datetime.now() - union_start
            self.logger.info(f"✅ Part1 union created in {union_duration.total_seconds():.2f}s (for volume calculation only)")
        else:
            union_part1 = None
            self.logger.info("ℹ️  No Part1 components - skipping union creation")
        
        return {
            'part1_list': part1_list,
            'part2_list': part2_list, 
            'part3_list': part3_list,
            'union_part1': union_part1
        }
    
    def _process_initial_parts(self, component_lists: Dict[str, Any], 
                              group_members: List[str]) -> Dict[str, Any]:
        """
        Process Part 2 and Part 3 components initially.
        
        Args:
            component_lists: Validated component lists
            group_members: List of group member IDs
            
        Returns:
            Dictionary containing part2_result and part3_result
        """
        from datetime import datetime
        
        # Process Part 2 FIRST
        if component_lists['part2_list']:
            self.logger.info(f"⏱️  Processing {len(component_lists['part2_list'])} Part2 components...")
            part2_start = datetime.now()
            part2_result = self._process_part2_components(
                component_lists['part2_list'], 
                component_lists['union_part1'], 
                group_members
            )
            part2_duration = datetime.now() - part2_start
            self.logger.info(f"✅ Part2 processing completed in {part2_duration.total_seconds():.2f}s")
        else:
            self.logger.info("ℹ️  No Part2 components to process")
            part2_result = {'parts': [], 'sub_objects': []}
        
        # Process Part 3 - simplified approach (no intermediate trimming)
        if component_lists['part3_list']:
            self.logger.info(f"⏱️  Processing {len(component_lists['part3_list'])} Part3 components...")
            part3_start = datetime.now()
            part3_result = self._process_part3_components_simple(
                component_lists['part3_list'], 
                group_members
            )
            part3_duration = datetime.now() - part3_start
            self.logger.info(f"✅ Part3 processing completed in {part3_duration.total_seconds():.2f}s")
        else:
            self.logger.info("ℹ️  No Part3 components to process")
            part3_result = {'parts': [], 'sub_objects': []}
        
        return {
            'part2_result': part2_result,
            'part3_result': part3_result
        }
    
    def _perform_final_group_trimming(self, part_results: Dict[str, Any], 
                                     union_part1: Any) -> Dict[str, Any]:
        """
        Perform final group trimming with reduced nesting.
        
        Args:
            part_results: Results from initial part processing
            union_part1: Unioned Part 1 components for cutting
            
        Returns:
            Dictionary containing final part results after trimming
        """
        from datetime import datetime
        
        part2_result = part_results['part2_result']
        part3_result = part_results['part3_result']
        
        final_part2_parts = part2_result['parts']
        final_part3_parts = part3_result['parts']
        
        if not final_part3_parts:
            self.logger.info("ℹ️  No Part3 parts to trim - skipping final group trimming")
            return part_results
        
        self.logger.info("🔧 Starting final group trimming (complex boolean operations)...")
        self.logger.info(f"📊 Trimming {len(final_part3_parts)} Part3 parts using {len(final_part2_parts)} Part2 parts")
        
        try:
            # Create unions for trimming
            self.logger.info("⏱️  Creating part unions for trimming operations...")
            union_start = datetime.now()
            trimming_unions = self._create_part_unions_for_trimming(
                final_part2_parts, final_part3_parts
            )
            union_duration = datetime.now() - union_start
            self.logger.info(f"✅ Part unions created in {union_duration.total_seconds():.2f}s")
            
            # Perform the actual trimming operations (most time-consuming)
            self.logger.info("⏱️  Performing complex trimming operations...")
            trim_start = datetime.now()
            trimmed_part3_parts = self._trim_part3_by_parts(
                trimming_unions, union_part1
            )
            trim_duration = datetime.now() - trim_start
            self.logger.info(f"✅ Trimming operations completed in {trim_duration.total_seconds():.2f}s")
            
            # Update Part 3 sub-objects with new geometry
            self.logger.info("⏱️  Updating Part3 sub-objects with trimmed geometry...")
            
            # DEBUG: Check original sub_objects before trimming
            original_sub_objects = part_results['part3_result']['sub_objects']
            self.logger.info(f"🔍 DEBUG: Original Part3 sub_objects before trimming: {len(original_sub_objects)}")
            for i, obj in enumerate(original_sub_objects):
                self.logger.info(f"   📊 Original Part3 #{i+1}: {obj.get('label', 'unknown')} - {obj.get('volume', 0):.3f} m³")
            
            update_start = datetime.now()
            updated_part3_sub_objects = self._update_trimmed_part3_sub_objects(
                trimmed_part3_parts, part_results['part3_result']['sub_objects']
            )
            update_duration = datetime.now() - update_start
            self.logger.info(f"✅ Sub-objects updated in {update_duration.total_seconds():.2f}s")
            
            # Update part3_result with trimmed geometry
            part_results['part3_result'] = {
                'parts': trimmed_part3_parts,
                'sub_objects': updated_part3_sub_objects
            }
            
            final_volume = sum(getattr(p, 'volume', 0.0) for p in trimmed_part3_parts)
            self.logger.info(f"🎉 Final group trimming completed: Part3 volume after trimming = {final_volume:.3f} m³")
            
        except Exception as e:
            # Log detailed error information
            self.logger.error(f"❌ Final group trimming failed: {type(e).__name__}: {str(e)}")
            self.logger.warning(f"Failed to perform final union trimming: {e}")
            # Falling back to original Part 3 geometry without trimming
        
        return part_results
    
    def _create_part_unions_for_trimming(self, final_part2_parts: List[Any], 
                                        final_part3_parts: List[Any]) -> Dict[str, Any]:
        """
        Create unions of Part 2 and Part 3 components for trimming operations.
        
        Args:
            final_part2_parts: Final Part 2 components
            final_part3_parts: Final Part 3 components
            
        Returns:
            Dictionary containing union_part2 and union_part3
        """
        # Step 1: Create union of all Part 2 components
        if final_part2_parts:
            # Removed verbose debug print
            if len(final_part2_parts) == 1:
                union_part2 = final_part2_parts[0]
            else:
                union_part2 = self.union_components(final_part2_parts)
            
            part2_volume = getattr(union_part2, 'volume', 0.0)
            # Removed verbose debug print
        else:
            union_part2 = None
            print("🔧 No Part 2 components for trimming")
        
        # Step 2: Create union of all Part 3 components
        # Removed verbose debug print
        if len(final_part3_parts) == 1:
            union_part3 = final_part3_parts[0]
        else:
            union_part3 = self.union_components(final_part3_parts)
        
        original_part3_volume = getattr(union_part3, 'volume', 0.0)
        # Removed verbose debug print
        
        return {
            'union_part2': union_part2,
            'union_part3': union_part3,
            'original_part3_volume': original_part3_volume
        }
    
    def _trim_part3_by_parts(self, trimming_unions: Dict[str, Any], 
                            union_part1: Any) -> List[Any]:
        """
        Trim Part 3 union by Part 2 only (Part 1 trimming skipped).
        
        Part 1 trimming is skipped because Part 3 components already have
        their own pile cylinders subtracted during individual processing.
        
        Args:
            trimming_unions: Unions created for trimming
            union_part1: Part 1 union (used for logging only)
            
        Returns:
            List of Part 3 components trimmed by Part 2 only
        """
        union_part2 = trimming_unions['union_part2']
        union_part3 = trimming_unions['union_part3']
        original_part3_volume = trimming_unions['original_part3_volume']
        
        trimmed_union_part3 = union_part3
        
        # Step 3: Trim by Part 2
        if union_part2:
            trimmed_union_part3 = self._trim_by_part2(
                union_part2, union_part3, original_part3_volume
            )
        
        # Step 4: Skip Part 1 trimming (already done during individual processing)
        if trimmed_union_part3 and union_part1:
            trimmed_union_part3 = self._trim_by_part1(
                union_part1, trimmed_union_part3
            )
        
        # Step 5: Extract final trimmed geometry
        return self._extract_final_trimmed_geometry(trimmed_union_part3)
    
    def _trim_by_part2(self, union_part2: Any, union_part3: Any, 
                      original_part3_volume: float) -> Any:
        """
        Trim Part 3 union by Part 2 union with Part3 protection.
        
        Args:
            union_part2: Part 2 union geometry
            union_part3: Part 3 union geometry  
            original_part3_volume: Original Part 3 volume for tracking
            
        Returns:
            Trimmed Part 3 geometry (or original if protection activated)
        """
        self.logger.info("🔍 DIFFERENCE OPERATION INVESTIGATION - Part3 - Part2")
        
        # Extract original Part3 components for protection analysis
        original_part3_solids = self._extract_solids_safely(union_part3)
        original_part3_count = len(original_part3_solids)
        original_part3_total_volume = sum(getattr(s, 'volume', 0.0) for s in original_part3_solids)
        
        # Log input geometry details
        part2_volume = getattr(union_part2, 'volume', 0.0)
        part3_volume = getattr(union_part3, 'volume', 0.0)
        self.logger.info(f"📊 INPUT GEOMETRIES:")
        self.logger.info(f"   📦 Part2 volume: {part2_volume:.3f} m³")
        self.logger.info(f"   📦 Part3 volume: {part3_volume:.3f} m³")
        self.logger.info(f"   📦 Part3 components: {original_part3_count}")
        
        # Test if Part2 and Part3 have any intersection first
        intersection_volume = 0.0
        try:
            intersection = union_part2 & union_part3
            intersection_volume = getattr(intersection, 'volume', 0.0) if intersection else 0.0
            self.logger.info(f"🔍 INTERSECTION TEST: Part2 & Part3 = {intersection_volume:.3f} m³")
            
            if intersection_volume > 1e-6:
                self.logger.info("✅ Part2 and Part3 DO intersect - difference should remove overlap")
            else:
                self.logger.warning("⚠️  Part2 and Part3 do NOT intersect - difference may be problematic")
        except Exception as e:
            self.logger.warning(f"⚠️  Intersection test failed: {e}")
        
        # Add detailed spatial debugging
        self._log_spatial_debugging(union_part2, union_part3)
        
        # Perform difference operation with detailed logging
        self.logger.info("🔧 PERFORMING DIFFERENCE: Part3 - Part2")
        self.logger.info("🔍 PRE-DIFFERENCE ANALYSIS:")
        self.logger.info(f"   📦 Part3 input type: {type(union_part3).__name__}")
        self.logger.info(f"   📦 Part2 input type: {type(union_part2).__name__}")
        
        # Check if inputs are valid geometries
        part3_valid = self._is_valid_geometry(union_part3)
        part2_valid = self._is_valid_geometry(union_part2)
        self.logger.info(f"   ✅ Part3 geometry valid: {part3_valid}")
        self.logger.info(f"   ✅ Part2 geometry valid: {part2_valid}")
        
        # Log detailed geometry properties
        try:
            if hasattr(union_part3, 'solids'):
                part3_solids = list(union_part3.solids())
                self.logger.info(f"   📊 Part3 contains {len(part3_solids)} solids")
            if hasattr(union_part2, 'solids'):
                part2_solids = list(union_part2.solids())
                self.logger.info(f"   📊 Part2 contains {len(part2_solids)} solids")
        except Exception as e:
            self.logger.warning(f"   ⚠️  Could not analyze solid composition: {e}")
        
        # Perform the difference operation
        self.logger.info("🔧 Executing difference operation...")
        try:
            trimmed_union_part3 = self.difference_operation(union_part3, [union_part2])
            self.logger.info("✅ Difference operation completed successfully")
        except Exception as diff_error:
            self.logger.error(f"❌ Difference operation failed: {diff_error}")
            self.logger.error("🔧 Falling back to original Part3 geometry")
            trimmed_union_part3 = union_part3
        
        # 🛡️ PART3 PROTECTION LOGIC - Check for excessive elimination
        if trimmed_union_part3:
            result_solids = self._extract_solids_safely(trimmed_union_part3)
            result_count = len(result_solids)
            result_volume = sum(getattr(s, 'volume', 0.0) for s in result_solids)
            
            # Calculate elimination rates
            component_loss_rate = (original_part3_count - result_count) / original_part3_count if original_part3_count > 0 else 0
            volume_loss_rate = (original_part3_total_volume - result_volume) / original_part3_total_volume if original_part3_total_volume > 0 else 0
            
            # Protection thresholds: >50% component loss OR >95% volume loss
            if component_loss_rate > 0.5 or volume_loss_rate > 0.95:
                self.logger.warning("🛡️ PART3 PROTECTION ACTIVATED - Excessive elimination detected!")
                self.logger.warning(f"   Component reduction: {original_part3_count} → {result_count} ({component_loss_rate:.1%})")
                self.logger.warning(f"   Volume reduction: {original_part3_total_volume:.2f} → {result_volume:.2f} m³ ({volume_loss_rate:.1%})")
                self.logger.warning(f"   Preserving original Part3 geometry instead of difference result")
                
                # Return original Part3 geometry to prevent inappropriate elimination
                trimmed_union_part3 = union_part3
            else:
                self.logger.info(f"✅ Part3 protection check passed:")
                self.logger.info(f"   Component reduction: {original_part3_count} → {result_count} ({component_loss_rate:.1%})")
                self.logger.info(f"   Volume reduction: {original_part3_total_volume:.2f} → {result_volume:.2f} m³ ({volume_loss_rate:.1%})")
        
        # Analyze difference result
        result_volume = getattr(trimmed_union_part3, 'volume', 0.0) if trimmed_union_part3 else 0.0
        self.logger.info(f"📊 DIFFERENCE RESULT:")
        self.logger.info(f"   📦 Result volume: {result_volume:.3f} m³")
        self.logger.info(f"   📦 Expected volume: {part3_volume - intersection_volume:.3f} m³")
        
        # Check if result is compound or single solid
        if trimmed_union_part3:
            result_type = type(trimmed_union_part3).__name__
            self.logger.info(f"   📦 Result type: {result_type}")
            
            # If it's a compound, analyze the pieces
            if hasattr(trimmed_union_part3, 'children') and trimmed_union_part3.children:
                num_pieces = len(trimmed_union_part3.children)
                self.logger.info(f"   📦 Number of pieces: {num_pieces}")
                
                for i, piece in enumerate(trimmed_union_part3.children):
                    piece_volume = getattr(piece, 'volume', 0.0)
                    self.logger.info(f"      🧩 Piece {i+1}: {piece_volume:.3f} m³")
                    
                    # Enhanced spatial debugging for pieces
                    try:
                        piece_bbox = piece.bounding_box()
                        piece_center = piece_bbox.center()
                        self.logger.info(f"         📍 Piece {i+1} center: ({piece_center.X:.3f}, {piece_center.Y:.3f}, {piece_center.Z:.3f})")
                        self.logger.info(f"         📦 Piece {i+1} bbox: ({piece_bbox.min.X:.3f}, {piece_bbox.min.Y:.3f}, {piece_bbox.min.Z:.3f}) to ({piece_bbox.max.X:.3f}, {piece_bbox.max.Y:.3f}, {piece_bbox.max.Z:.3f})")
                    except:
                        pass
                    
                    # CRITICAL: Test if any piece is still inside Part2
                    try:
                        # Get Part2 bounding box for reference
                        try:
                            part2_bbox = union_part2.bounding_box()
                            part2_center = part2_bbox.center()
                            self.logger.info(f"🔍 Part2 reference - Center: ({part2_center.X:.3f}, {part2_center.Y:.3f}, {part2_center.Z:.3f})")
                            self.logger.info(f"🔍 Part2 reference - BBox: ({part2_bbox.min.X:.3f}, {part2_bbox.min.Y:.3f}, {part2_bbox.min.Z:.3f}) to ({part2_bbox.max.X:.3f}, {part2_bbox.max.Y:.3f}, {part2_bbox.max.Z:.3f})")
                        except Exception as bbox_err:
                            self.logger.warning(f"Could not get Part2 bounding box: {bbox_err}")
                        
                        piece_part2_intersection = piece & union_part2
                        piece_intersection_volume = getattr(piece_part2_intersection, 'volume', 0.0) if piece_part2_intersection else 0.0
                        piece_volume = getattr(piece, 'volume', 0.0)
                        
                        # Enhanced containment analysis
                        containment_ratio = piece_intersection_volume / piece_volume if piece_volume > 0 else 0.0
                        
                        self.logger.info(f"🔍 DETAILED PIECE {i+1} ANALYSIS:")
                        self.logger.info(f"   📊 Piece volume: {piece_volume:.3f} m³")
                        self.logger.info(f"   📊 Intersection volume: {piece_intersection_volume:.3f} m³")
                        self.logger.info(f"   📊 Containment ratio: {containment_ratio:.3f} ({containment_ratio*100:.1f}%)")
                        
                        if piece_intersection_volume > 1e-6:
                            if containment_ratio > 0.95:  # 95% or more inside Part2
                                self.logger.error(f"🚨 CRITICAL BUG: Piece {i+1} is {containment_ratio*100:.1f}% inside Part2!")
                                self.logger.error(f"🚨 This piece should have been REMOVED by difference operation!")
                                self.logger.error(f"🚨 Piece {i+1} volume: {piece_volume:.3f} m³, intersection: {piece_intersection_volume:.3f} m³")
                                self.logger.error(f"🚨 This will incorrectly create Part3_Union_{i+1}")
                            elif containment_ratio > 0.5:  # More than 50% inside
                                self.logger.warning(f"⚠️  Piece {i+1} is {containment_ratio*100:.1f}% inside Part2 - partially contained")
                            else:
                                self.logger.warning(f"⚠️  Piece {i+1} has {containment_ratio*100:.1f}% intersection with Part2")
                        else:
                            self.logger.info(f"✅ Piece {i+1} correctly separated from Part2 (no intersection)")
                    except Exception as e:
                        self.logger.warning(f"⚠️  Could not test piece {i+1} intersection: {e}")
        
        self.logger.info("🔍 DIFFERENCE OPERATION INVESTIGATION COMPLETE")
        
        after_part2_volume = getattr(trimmed_union_part3, 'volume', 0.0) if trimmed_union_part3 else 0.0
        part2_reduction = original_part3_volume - after_part2_volume
        
        return trimmed_union_part3
    
    def _trim_by_part1(self, union_part1: Any, trimmed_union_part3: Any) -> Any:
        """
        Skip Part1 trimming - already done during individual pile processing.
        
        During individual pile creation, Part 3 components already have their own 
        pile cylinder subtracted. Group-level Part 1 trimming would be double-trimming,
        which can cause geometry failures and is mathematically incorrect.
        
        Args:
            union_part1: Part 1 union geometry (ignored)
            trimmed_union_part3: Part 3 geometry (returned unchanged)
            
        Returns:
            Part 3 geometry without additional Part 1 trimming
        """
        # Skip Part 1 trimming to avoid double-trimming
        # Individual processing already subtracted own pile cylinder
        self.logger.info("🔄 Skipping Part 1 trimming - already done during individual processing")
        return trimmed_union_part3
    
    def _log_spatial_debugging(self, union_part2: Any, union_part3: Any) -> None:
        """
        Log spatial debugging information for Part 2 and Part 3 bounding boxes.
        
        Args:
            union_part2: Part 2 union geometry
            union_part3: Part 3 union geometry
        """
        try:
            part2_bounds = union_part2.bounding_box()
            part3_bounds = union_part3.bounding_box()
            # Spatial debugging removed for cleaner output
            
            # Check if bounding boxes overlap
            x_overlap = part2_bounds.max.X > part3_bounds.min.X and part2_bounds.min.X < part3_bounds.max.X
            y_overlap = part2_bounds.max.Y > part3_bounds.min.Y and part2_bounds.min.Y < part3_bounds.max.Y  
            z_overlap = part2_bounds.max.Z > part3_bounds.min.Z and part2_bounds.min.Z < part3_bounds.max.Z
            bounding_box_overlap = x_overlap and y_overlap and z_overlap
            # Bounding box overlap details (logging removed for cleaner output)
            
        except Exception as e:
            pass  # Removed verbose debug print
    
    def _extract_final_trimmed_geometry(self, trimmed_union_part3: Any) -> List[Any]:
        """
        Extract final trimmed geometry from the trimming result.
        
        Args:
            trimmed_union_part3: Result from trimming operations
            
        Returns:
            List of extracted solid geometries
        """
        self.logger.info("🔍 EXTRACTING FINAL TRIMMED GEOMETRY - ENHANCED DEBUG")
        self.logger.info(f"📊 Input object type: {type(trimmed_union_part3).__name__}")
        self.logger.info(f"📊 Input object volume: {getattr(trimmed_union_part3, 'volume', 0.0):.3f} m³")
        
        if self._is_valid_geometry(trimmed_union_part3):
            # Extract solids from trimmed result
            self.logger.info("🔍 Object is valid geometry - extracting solids...")
            final_solids = self._extract_solids_safely(trimmed_union_part3)
            
            self.logger.info(f"🔍 EXTRACTION RESULT: {len(final_solids)} solids extracted")
            for i, solid in enumerate(final_solids):
                solid_volume = getattr(solid, 'volume', 0.0)
                solid_type = type(solid).__name__
                self.logger.info(f"   📊 Solid {i+1}: {solid_type}, volume: {solid_volume:.3f} m³")
            
            final_volume = sum(getattr(solid, 'volume', 0.0) for solid in final_solids)
            self.logger.info(f"🔍 TOTAL EXTRACTED VOLUME: {final_volume:.3f} m³")
            
            if len(final_solids) > 1:
                self.logger.warning(f"⚠️  MULTIPLE PART3 SOLIDS DETECTED: {len(final_solids)} separate components")
                self.logger.warning("⚠️  This will create Part3_Union_1, Part3_Union_2, etc.")
                self.logger.warning("⚠️  Root cause: Trimming operation split geometry into separate pieces")
                self.logger.warning("⚠️  Each piece becomes an orphaned component without pile attribution")
            
            # Enhanced spatial analysis for containment investigation
            if len(final_solids) > 1:
                self.logger.info("🔧 POST-TRIMMING VALIDATION: Checking for improperly retained pieces...")
                self.logger.info("📋 VALIDATION LOGIC:")
                self.logger.info("   🐛 BUG CASE: Part3_Union_2 completely contained within Part3_Union_1 bounds")
                self.logger.info("   ✅ LEGITIMATE CASE: Part3_Union_1 and Part3_Union_2 vertically separated by Part2 intersection")
                
                # Check each piece for spatial containment within Part2 bounds
                valid_pieces = []
                removed_pieces = []
                
                for i, solid in enumerate(final_solids):
                    piece_num = i + 1
                    try:
                        # Get piece bounding box
                        piece_bbox = solid.bounding_box()
                        piece_volume = getattr(solid, 'volume', 0.0)
                        
                        # Test if piece is completely within Part2 Z-bounds (indicating improper retention)
                        piece_z_min = piece_bbox.min.Z
                        piece_z_max = piece_bbox.max.Z
                        
                        self.logger.info(f"🔍 Validating Part3_Union_{piece_num} (volume: {piece_volume:.3f} m³)")
                        self.logger.info(f"   📍 Z-range: {piece_z_min:.3f} to {piece_z_max:.3f}")
                        
                        # Enhanced validation: Check for improper containment vs legitimate separation
                        is_improperly_retained = False
                        
                        # Test 1: Check if piece is completely contained within another piece (BUG CASE)
                        for j, other_solid in enumerate(final_solids):
                            if i == j:
                                continue
                            try:
                                other_bbox = other_solid.bounding_box()
                                other_z_min = other_bbox.min.Z
                                other_z_max = other_bbox.max.Z
                                
                                # Check if this piece is completely contained within another (with tolerance)
                                tolerance = 0.1  # 10cm tolerance
                                if (piece_z_min >= (other_z_min - tolerance) and piece_z_max <= (other_z_max + tolerance) and
                                    piece_bbox.min.X >= (other_bbox.min.X - tolerance) and piece_bbox.max.X <= (other_bbox.max.X + tolerance) and
                                    piece_bbox.min.Y >= (other_bbox.min.Y - tolerance) and piece_bbox.max.Y <= (other_bbox.max.Y + tolerance)):
                                    
                                    self.logger.error(f"🚨 BUG CASE DETECTED: Part3_Union_{piece_num} is completely contained within Part3_Union_{j+1}!")
                                    self.logger.error(f"🚨 Piece {piece_num} bounds: ({piece_bbox.min.X:.3f}, {piece_bbox.min.Y:.3f}, {piece_z_min:.3f}) to ({piece_bbox.max.X:.3f}, {piece_bbox.max.Y:.3f}, {piece_z_max:.3f})")
                                    self.logger.error(f"🚨 Other piece bounds: ({other_bbox.min.X:.3f}, {other_bbox.min.Y:.3f}, {other_z_min:.3f}) to ({other_bbox.max.X:.3f}, {other_bbox.max.Y:.3f}, {other_z_max:.3f})")
                                    self.logger.error(f"🚨 This indicates failed difference operation - piece should have been removed!")
                                    is_improperly_retained = True
                                    break
                                    
                                # Test 2: Check for legitimate vertical separation (VALID CASE)
                                elif (piece_z_max < other_z_min - tolerance or piece_z_min > other_z_max + tolerance):
                                    # Pieces are vertically separated - this is legitimate Part2 intersection case
                                    gap = min(abs(piece_z_max - other_z_min), abs(piece_z_min - other_z_max))
                                    self.logger.info(f"✅ LEGITIMATE CASE: Part3_Union_{piece_num} and Part3_Union_{j+1} are vertically separated by {gap:.3f}")
                                    self.logger.info(f"✅ This indicates proper Part2 intersection creating separate soil volumes")
                                    
                            except:
                                pass
                        
                        if is_improperly_retained:
                            self.logger.error(f"🚨 REMOVING improperly retained Part3_Union_{piece_num}")
                            removed_pieces.append(piece_num)
                        else:
                            self.logger.info(f"✅ Part3_Union_{piece_num} appears valid (legitimate separate volume)")
                            valid_pieces.append(solid)
                    except Exception as e:
                        self.logger.warning(f"Could not validate piece {piece_num}: {e}")
                        valid_pieces.append(solid)  # Keep if validation fails
                
                if removed_pieces:
                    self.logger.info(f"🔧 CORRECTED: Removed {len(removed_pieces)} improperly retained pieces: {removed_pieces}")
                    self.logger.info(f"✅ Final result: {len(valid_pieces)} valid Part3 component(s)")
                    final_solids = valid_pieces
                else:
                    self.logger.info(f"✅ All {len(final_solids)} pieces passed validation - legitimate separate volumes")
                
                self._analyze_part3_spatial_relationships(final_solids)
            
            return final_solids
        else:
            self.logger.info("🔧 FINAL RESULT: Part 3 completely removed after trimming")
            return []

    
    def _analyze_part3_spatial_relationships(self, final_solids: List[Any]) -> None:
        """
        Analyze spatial relationships between Part3 solids and previously processed unions.
        This helps investigate containment issues where Part3_2 ends up inside Part2.
        """
        try:
            self.logger.info("🔍 SPATIAL ANALYSIS: Starting Part3 containment investigation...")
            
            # Analyze each Part3 solid for spatial properties
            for i, solid in enumerate(final_solids):
                solid_num = i + 1
                self.logger.info(f"📐 Part3_{solid_num} ANALYSIS:")
                
                # Get bounding box information
                try:
                    bbox = solid.bounding_box()
                    min_pt = bbox.min
                    max_pt = bbox.max
                    center = bbox.center()
                    size = bbox.size
                    
                    self.logger.info(f"   📦 Bounding Box - Min: ({min_pt.X:.3f}, {min_pt.Y:.3f}, {min_pt.Z:.3f})")
                    self.logger.info(f"   📦 Bounding Box - Max: ({max_pt.X:.3f}, {max_pt.Y:.3f}, {max_pt.Z:.3f})")
                    self.logger.info(f"   📦 Center: ({center.X:.3f}, {center.Y:.3f}, {center.Z:.3f})")
                    self.logger.info(f"   📦 Size: {size.X:.3f} x {size.Y:.3f} x {size.Z:.3f}")
                    
                    # Volume and basic properties
                    volume = getattr(solid, 'volume', 0.0)
                    self.logger.info(f"   📊 Volume: {volume:.6f} m³")
                    
                    # Check for extremely small or suspicious volumes
                    if volume < 0.001:
                        self.logger.warning(f"   ⚠️  SUSPICIOUS: Part3_{solid_num} has very small volume ({volume:.6f} m³)")
                    
                    # Check for potential containment by analyzing size relative to typical pile dimensions
                    if size.X < 2.0 and size.Y < 2.0:  # Typical pile cap might be larger
                        self.logger.warning(f"   ⚠️  POTENTIAL CONTAINMENT: Part3_{solid_num} has small footprint ({size.X:.3f} x {size.Y:.3f})")
                        self.logger.warning(f"   ⚠️  This could indicate it's contained within another union part")
                    
                except Exception as bbox_error:
                    self.logger.error(f"   ❌ Failed to get bounding box for Part3_{solid_num}: {bbox_error}")
                
                # Log separation distance between solids
                if i > 0:
                    try:
                        prev_center = final_solids[i-1].bounding_box().center()
                        distance = ((center.X - prev_center.X)**2 + 
                                  (center.Y - prev_center.Y)**2 + 
                                  (center.Z - prev_center.Z)**2)**0.5
                        self.logger.info(f"   📏 Distance from Part3_{i}: {distance:.3f} m")
                        
                        if distance < 1.0:  # Very close
                            self.logger.warning(f"   ⚠️  Part3_{solid_num} and Part3_{i} are very close ({distance:.3f} m)")
                    except:
                        pass
            
            # Enhanced vertical layering analysis
            if len(final_solids) >= 2:
                self.logger.info("🔍 VERTICAL LAYERING ANALYSIS:")
                
                # Collect Z-coordinates for all pieces
                piece_z_data = []
                for i, solid in enumerate(final_solids):
                    try:
                        bbox = solid.bounding_box()
                        z_min = bbox.min.Z
                        z_max = bbox.max.Z
                        z_center = bbox.center().Z
                        volume = getattr(solid, 'volume', 0.0)
                        
                        piece_z_data.append({
                            'index': i + 1,
                            'z_min': z_min,
                            'z_max': z_max,
                            'z_center': z_center,
                            'z_height': z_max - z_min,
                            'volume': volume
                        })
                    except:
                        pass
                
                # Sort by Z-center to identify layering
                if piece_z_data:
                    sorted_pieces = sorted(piece_z_data, key=lambda x: x['z_center'])
                    
                    for i, piece in enumerate(sorted_pieces):
                        layer_position = "bottom" if i == 0 else "top" if i == len(sorted_pieces)-1 else "middle"
                        self.logger.info(f"   📊 Part3_Union_{piece['index']}: {layer_position} layer (Z: {piece['z_min']:.3f} to {piece['z_max']:.3f}, center: {piece['z_center']:.3f})")
                    
                    # Check for vertical separation indicating either legitimate or bug cases
                    if len(sorted_pieces) >= 2:
                        top_piece = sorted_pieces[-1]
                        bottom_piece = sorted_pieces[0]
                        
                        if top_piece['z_min'] > bottom_piece['z_max']:
                            gap = top_piece['z_min'] - bottom_piece['z_max']
                            self.logger.info(f"✅ LEGITIMATE SEPARATION: Vertical gap of {gap:.3f} between Part3_Union_{bottom_piece['index']} and Part3_Union_{top_piece['index']}")
                            self.logger.info(f"✅ This indicates proper Part2 intersection creating separate soil volumes")
                            self.logger.info(f"   � Part3_Union_{bottom_piece['index']} (bottom): Z {bottom_piece['z_min']:.3f} to {bottom_piece['z_max']:.3f}")
                            self.logger.info(f"   📊 Part3_Union_{top_piece['index']} (top): Z {top_piece['z_min']:.3f} to {top_piece['z_max']:.3f}")
                        elif top_piece['z_center'] > bottom_piece['z_center']:
                            overlap = min(top_piece['z_max'], bottom_piece['z_max']) - max(top_piece['z_min'], bottom_piece['z_min'])
                            if overlap <= 0:
                                self.logger.info(f"✅ ADJACENT PIECES: Part3 pieces are vertically adjacent with no overlap")
                            elif overlap > 0:
                                overlap_ratio_top = overlap / top_piece['z_height']
                                overlap_ratio_bottom = overlap / bottom_piece['z_height']
                                
                                if overlap_ratio_top > 0.8 or overlap_ratio_bottom > 0.8:
                                    # Significant overlap suggests one piece is mostly contained within another
                                    contained_piece = top_piece if overlap_ratio_top > overlap_ratio_bottom else bottom_piece
                                    self.logger.warning(f"⚠️  POTENTIAL BUG: Part3_Union_{contained_piece['index']} has {max(overlap_ratio_top, overlap_ratio_bottom)*100:.1f}% Z-overlap")
                                    self.logger.warning(f"⚠️  This might indicate improper geometry retention")
                                else:
                                    self.logger.info(f"✅ NORMAL OVERLAP: Part3 pieces overlap vertically by {overlap:.3f} ({overlap/min(top_piece['z_height'], bottom_piece['z_height'])*100:.1f}%)")
                        else:
                            self.logger.warning(f"⚠️  Unexpected vertical arrangement detected")
            
            self.logger.info("🔍 SPATIAL ANALYSIS: Complete")
            
        except Exception as e:
            self.logger.error(f"❌ Spatial analysis failed: {e}")
    
    def _update_trimmed_part3_sub_objects(self, final_part3_parts: List[Any], 
                                         original_sub_objects: List[Dict]) -> List[Dict]:
        """
        Update Part 3 sub-objects after trimming with new geometry and volumes.
        
        Args:
            final_part3_parts: Final trimmed Part 3 components
            original_sub_objects: Original sub-objects list
            
        Returns:
            Updated list of sub-objects
        """
        self.logger.info(f"🔍 DEBUG: _update_trimmed_part3_sub_objects called with {len(final_part3_parts)} parts")
        for i, part in enumerate(final_part3_parts):
            part_volume = getattr(part, 'volume', 0.0)
            part_type = type(part).__name__
            self.logger.info(f"   📊 Part {i+1}: {part_type}, volume: {part_volume:.3f} m³")
        
        updated_part3_sub_objects = []
        
        # Extract contributing piles from original sub-objects if available
        contributing_piles = []
        if original_sub_objects:
            # Get contributing piles from first sub-object as they should be consistent
            contributing_piles = original_sub_objects[0].get('contributing_piles', [])
        
        for i, part in enumerate(final_part3_parts):
            # Use simple sequential format: Part3_Union_1, Part3_Union_2, etc.
            if len(final_part3_parts) == 1:
                label = 'Part3_Union_1'
            else:
                label = f'Part3_Union_{i+1}'
            
            part_volume = getattr(part, 'volume', 0.0)

            updated_part3_sub_objects.append({
                'volume': part_volume,
                'material': 'Soil',
                'label': label,
                'contributing_piles': contributing_piles.copy(),
                'part_type': 'Part3',
                'material_type': 'Soil'
            })
        
        return updated_part3_sub_objects
    
    def _create_final_geometry_result(self, final_part_results: Dict[str, Any], 
                                     union_part1: Any, group_members: List[str]) -> GeometryResult:
        """
        Create the final GeometryResult with calculated volumes and metadata.
        
        Args:
            final_part_results: Final results after trimming
            union_part1: Part 1 union for volume calculation
            group_members: List of group member IDs
            
        Returns:
            Complete GeometryResult object
        """
        part2_result = final_part_results['part2_result']
        part3_result = final_part_results['part3_result']
        
        # Calculate volumes and metadata
        volumes = {
            'part1_volume': getattr(union_part1, 'volume', 0.0) if union_part1 else 0.0,
            'part2_volume': sum(getattr(p, 'volume', 0.0) for p in part2_result['parts']),
            'part3_volume': sum(getattr(p, 'volume', 0.0) for p in part3_result['parts'])
        }
        
        # DEBUG: Log sub_objects combination for investigation
        self.logger.info(f"🔍 DEBUG: Final sub_objects combination:")
        self.logger.info(f"   📊 Part2 sub_objects: {len(part2_result['sub_objects'])}")
        for i, obj in enumerate(part2_result['sub_objects']):
            self.logger.info(f"      - Part2 #{i+1}: {obj.get('label', 'unknown')} ({obj.get('part_type', 'unknown')}) - {obj.get('volume', 0):.3f} m³")
        self.logger.info(f"   📊 Part3 sub_objects: {len(part3_result['sub_objects'])}")
        for i, obj in enumerate(part3_result['sub_objects']):
            self.logger.info(f"      - Part3 #{i+1}: {obj.get('label', 'unknown')} ({obj.get('part_type', 'unknown')}) - {obj.get('volume', 0):.3f} m³")

        return GeometryResult(
            parts=part2_result['parts'] + part3_result['parts'],
            volumes=volumes,
            sub_objects=part2_result['sub_objects'] + part3_result['sub_objects'],
            metadata={
                'part2_count': len(part2_result['parts']),
                'part3_count': len(part3_result['parts']),
                'group_members': group_members
            }
        )
