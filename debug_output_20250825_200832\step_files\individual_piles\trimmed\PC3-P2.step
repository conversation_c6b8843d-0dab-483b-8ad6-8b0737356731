ISO-10303-21;
HEADER;
FILE_DESCRIPTION(('Open CASCADE Model'),'2;1');
FILE_NAME('Pile_PC3-P2','2025-08-25T20:08:32',('Author'),('Open CASCADE'
    ),'Open CASCADE STEP processor 7.8','build123d','Unknown');
FILE_SCHEMA(('AUTOMOTIVE_DESIGN { 1 0 10303 214 1 1 1 1 }'));
ENDSEC;
DATA;
#1 = APPLICATION_PROTOCOL_DEFINITION('international standard',
  'automotive_design',2000,#2);
#2 = APPLICATION_CONTEXT(
  'core data for automotive mechanical design processes');
#3 = SHAPE_DEFINITION_REPRESENTATION(#4,#10);
#4 = PRODUCT_DEFINITION_SHAPE('','',#5);
#5 = PRODUCT_DEFINITION('design','',#6,#9);
#6 = PRODUCT_DEFINITION_FORMATION('','',#7);
#7 = PRODUCT('Pile_PC3-P2','Pile_PC3-P2','',(#8));
#8 = PRODUCT_CONTEXT('',#2,'mechanical');
#9 = PRODUCT_DEFINITION_CONTEXT('part definition',#2,'design');
#10 = SHAPE_REPRESENTATION('',(#11,#15,#19,#23),#27);
#11 = AXIS2_PLACEMENT_3D('',#12,#13,#14);
#12 = CARTESIAN_POINT('',(0.,0.,0.));
#13 = DIRECTION('',(0.,0.,1.));
#14 = DIRECTION('',(1.,0.,-0.));
#15 = AXIS2_PLACEMENT_3D('',#16,#17,#18);
#16 = CARTESIAN_POINT('',(73.317494,105.15648,17.720607));
#17 = DIRECTION('',(0.,0.,1.));
#18 = DIRECTION('',(1.,0.,0.));
#19 = AXIS2_PLACEMENT_3D('',#20,#21,#22);
#20 = CARTESIAN_POINT('',(0.,0.,0.));
#21 = DIRECTION('',(0.,0.,1.));
#22 = DIRECTION('',(1.,0.,-0.));
#23 = AXIS2_PLACEMENT_3D('',#24,#25,#26);
#24 = CARTESIAN_POINT('',(0.,0.,0.));
#25 = DIRECTION('',(0.,0.,1.));
#26 = DIRECTION('',(1.,0.,-0.));
#27 = ( GEOMETRIC_REPRESENTATION_CONTEXT(3) 
GLOBAL_UNCERTAINTY_ASSIGNED_CONTEXT((#31)) GLOBAL_UNIT_ASSIGNED_CONTEXT(
(#28,#29,#30)) REPRESENTATION_CONTEXT('Context #1',
  '3D Context with UNIT and UNCERTAINTY') );
#28 = ( LENGTH_UNIT() NAMED_UNIT(*) SI_UNIT(.MILLI.,.METRE.) );
#29 = ( NAMED_UNIT(*) PLANE_ANGLE_UNIT() SI_UNIT($,.RADIAN.) );
#30 = ( NAMED_UNIT(*) SI_UNIT($,.STERADIAN.) SOLID_ANGLE_UNIT() );
#31 = UNCERTAINTY_MEASURE_WITH_UNIT(LENGTH_MEASURE(1.E-07),#28,
  'distance_accuracy_value','confusion accuracy');
#32 = PRODUCT_RELATED_PRODUCT_CATEGORY('part',$,(#7));
#33 = SHAPE_DEFINITION_REPRESENTATION(#34,#40);
#34 = PRODUCT_DEFINITION_SHAPE('','',#35);
#35 = PRODUCT_DEFINITION('design','',#36,#39);
#36 = PRODUCT_DEFINITION_FORMATION('','',#37);
#37 = PRODUCT('PC3-P2_Part1','PC3-P2_Part1','',(#38));
#38 = PRODUCT_CONTEXT('',#2,'mechanical');
#39 = PRODUCT_DEFINITION_CONTEXT('part definition',#2,'design');
#40 = ADVANCED_BREP_SHAPE_REPRESENTATION('',(#11,#41),#139);
#41 = MANIFOLD_SOLID_BREP('',#42);
#42 = CLOSED_SHELL('',(#43,#131,#135));
#43 = ADVANCED_FACE('',(#44),#57,.T.);
#44 = FACE_BOUND('',#45,.T.);
#45 = EDGE_LOOP('',(#46,#80,#103,#130));
#46 = ORIENTED_EDGE('',*,*,#47,.F.);
#47 = EDGE_CURVE('',#48,#48,#50,.T.);
#48 = VERTEX_POINT('',#49);
#49 = CARTESIAN_POINT('',(0.275,-6.73555739531E-17,19.779393));
#50 = SURFACE_CURVE('',#51,(#56,#68),.PCURVE_S1.);
#51 = CIRCLE('',#52,0.275);
#52 = AXIS2_PLACEMENT_3D('',#53,#54,#55);
#53 = CARTESIAN_POINT('',(0.,0.,19.779393));
#54 = DIRECTION('',(0.,0.,1.));
#55 = DIRECTION('',(1.,0.,-0.));
#56 = PCURVE('',#57,#62);
#57 = CYLINDRICAL_SURFACE('',#58,0.275);
#58 = AXIS2_PLACEMENT_3D('',#59,#60,#61);
#59 = CARTESIAN_POINT('',(0.,0.,0.));
#60 = DIRECTION('',(0.,0.,1.));
#61 = DIRECTION('',(1.,0.,-0.));
#62 = DEFINITIONAL_REPRESENTATION('',(#63),#67);
#63 = LINE('',#64,#65);
#64 = CARTESIAN_POINT('',(0.,19.779393));
#65 = VECTOR('',#66,1.);
#66 = DIRECTION('',(1.,0.));
#67 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#68 = PCURVE('',#69,#74);
#69 = PLANE('',#70);
#70 = AXIS2_PLACEMENT_3D('',#71,#72,#73);
#71 = CARTESIAN_POINT('',(0.,0.,19.779393));
#72 = DIRECTION('',(0.,0.,1.));
#73 = DIRECTION('',(1.,0.,-0.));
#74 = DEFINITIONAL_REPRESENTATION('',(#75),#79);
#75 = CIRCLE('',#76,0.275);
#76 = AXIS2_PLACEMENT_2D('',#77,#78);
#77 = CARTESIAN_POINT('',(0.,0.));
#78 = DIRECTION('',(1.,0.));
#79 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#80 = ORIENTED_EDGE('',*,*,#81,.F.);
#81 = EDGE_CURVE('',#82,#48,#84,.T.);
#82 = VERTEX_POINT('',#83);
#83 = CARTESIAN_POINT('',(0.275,-6.73555739531E-17,0.));
#84 = SEAM_CURVE('',#85,(#89,#96),.PCURVE_S1.);
#85 = LINE('',#86,#87);
#86 = CARTESIAN_POINT('',(0.275,-6.73555739531E-17,0.));
#87 = VECTOR('',#88,1.);
#88 = DIRECTION('',(0.,0.,1.));
#89 = PCURVE('',#57,#90);
#90 = DEFINITIONAL_REPRESENTATION('',(#91),#95);
#91 = LINE('',#92,#93);
#92 = CARTESIAN_POINT('',(6.28318530718,-0.));
#93 = VECTOR('',#94,1.);
#94 = DIRECTION('',(0.,1.));
#95 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#96 = PCURVE('',#57,#97);
#97 = DEFINITIONAL_REPRESENTATION('',(#98),#102);
#98 = LINE('',#99,#100);
#99 = CARTESIAN_POINT('',(0.,-0.));
#100 = VECTOR('',#101,1.);
#101 = DIRECTION('',(0.,1.));
#102 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#103 = ORIENTED_EDGE('',*,*,#104,.T.);
#104 = EDGE_CURVE('',#82,#82,#105,.T.);
#105 = SURFACE_CURVE('',#106,(#111,#118),.PCURVE_S1.);
#106 = CIRCLE('',#107,0.275);
#107 = AXIS2_PLACEMENT_3D('',#108,#109,#110);
#108 = CARTESIAN_POINT('',(0.,0.,0.));
#109 = DIRECTION('',(0.,0.,1.));
#110 = DIRECTION('',(1.,0.,-0.));
#111 = PCURVE('',#57,#112);
#112 = DEFINITIONAL_REPRESENTATION('',(#113),#117);
#113 = LINE('',#114,#115);
#114 = CARTESIAN_POINT('',(0.,0.));
#115 = VECTOR('',#116,1.);
#116 = DIRECTION('',(1.,0.));
#117 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#118 = PCURVE('',#119,#124);
#119 = PLANE('',#120);
#120 = AXIS2_PLACEMENT_3D('',#121,#122,#123);
#121 = CARTESIAN_POINT('',(0.,0.,0.));
#122 = DIRECTION('',(0.,0.,1.));
#123 = DIRECTION('',(1.,0.,-0.));
#124 = DEFINITIONAL_REPRESENTATION('',(#125),#129);
#125 = CIRCLE('',#126,0.275);
#126 = AXIS2_PLACEMENT_2D('',#127,#128);
#127 = CARTESIAN_POINT('',(0.,0.));
#128 = DIRECTION('',(1.,0.));
#129 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#130 = ORIENTED_EDGE('',*,*,#81,.T.);
#131 = ADVANCED_FACE('',(#132),#69,.T.);
#132 = FACE_BOUND('',#133,.T.);
#133 = EDGE_LOOP('',(#134));
#134 = ORIENTED_EDGE('',*,*,#47,.T.);
#135 = ADVANCED_FACE('',(#136),#119,.F.);
#136 = FACE_BOUND('',#137,.T.);
#137 = EDGE_LOOP('',(#138));
#138 = ORIENTED_EDGE('',*,*,#104,.F.);
#139 = ( GEOMETRIC_REPRESENTATION_CONTEXT(3) 
GLOBAL_UNCERTAINTY_ASSIGNED_CONTEXT((#143)) GLOBAL_UNIT_ASSIGNED_CONTEXT
((#140,#141,#142)) REPRESENTATION_CONTEXT('Context #1',
  '3D Context with UNIT and UNCERTAINTY') );
#140 = ( LENGTH_UNIT() NAMED_UNIT(*) SI_UNIT(.MILLI.,.METRE.) );
#141 = ( NAMED_UNIT(*) PLANE_ANGLE_UNIT() SI_UNIT($,.RADIAN.) );
#142 = ( NAMED_UNIT(*) SI_UNIT($,.STERADIAN.) SOLID_ANGLE_UNIT() );
#143 = UNCERTAINTY_MEASURE_WITH_UNIT(LENGTH_MEASURE(1.E-07),#140,
  'distance_accuracy_value','confusion accuracy');
#144 = CONTEXT_DEPENDENT_SHAPE_REPRESENTATION(#145,#147);
#145 = ( REPRESENTATION_RELATIONSHIP('','',#40,#10) 
REPRESENTATION_RELATIONSHIP_WITH_TRANSFORMATION(#146) 
SHAPE_REPRESENTATION_RELATIONSHIP() );
#146 = ITEM_DEFINED_TRANSFORMATION('','',#11,#15);
#147 = PRODUCT_DEFINITION_SHAPE('Placement','Placement of an item',#148
  );
#148 = NEXT_ASSEMBLY_USAGE_OCCURRENCE('132','=>[0:1:1:2]','',#5,#35,$);
#149 = PRODUCT_RELATED_PRODUCT_CATEGORY('part',$,(#37));
#150 = SHAPE_DEFINITION_REPRESENTATION(#151,#157);
#151 = PRODUCT_DEFINITION_SHAPE('','',#152);
#152 = PRODUCT_DEFINITION('design','',#153,#156);
#153 = PRODUCT_DEFINITION_FORMATION('','',#154);
#154 = PRODUCT('PC3-P2_Part2','PC3-P2_Part2','',(#155));
#155 = PRODUCT_CONTEXT('',#2,'mechanical');
#156 = PRODUCT_DEFINITION_CONTEXT('part definition',#2,'design');
#157 = ADVANCED_BREP_SHAPE_REPRESENTATION('',(#11,#158),#304);
#158 = MANIFOLD_SOLID_BREP('',#159);
#159 = CLOSED_SHELL('',(#160,#247,#277));
#160 = ADVANCED_FACE('',(#161),#174,.T.);
#161 = FACE_BOUND('',#162,.T.);
#162 = EDGE_LOOP('',(#163,#197,#220,#246));
#163 = ORIENTED_EDGE('',*,*,#164,.F.);
#164 = EDGE_CURVE('',#165,#165,#167,.T.);
#165 = VERTEX_POINT('',#166);
#166 = CARTESIAN_POINT('',(77.633945884327,105.15648,24.720607));
#167 = SURFACE_CURVE('',#168,(#173,#185),.PCURVE_S1.);
#168 = CIRCLE('',#169,4.316451884327);
#169 = AXIS2_PLACEMENT_3D('',#170,#171,#172);
#170 = CARTESIAN_POINT('',(73.317494,105.15648,24.720607));
#171 = DIRECTION('',(0.,0.,1.));
#172 = DIRECTION('',(1.,0.,0.));
#173 = PCURVE('',#174,#179);
#174 = CONICAL_SURFACE('',#175,0.275,0.523598775598);
#175 = AXIS2_PLACEMENT_3D('',#176,#177,#178);
#176 = CARTESIAN_POINT('',(73.317494,105.15648,17.720607));
#177 = DIRECTION('',(0.,0.,1.));
#178 = DIRECTION('',(1.,0.,0.));
#179 = DEFINITIONAL_REPRESENTATION('',(#180),#184);
#180 = LINE('',#181,#182);
#181 = CARTESIAN_POINT('',(0.,7.));
#182 = VECTOR('',#183,1.);
#183 = DIRECTION('',(1.,0.));
#184 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#185 = PCURVE('',#186,#191);
#186 = PLANE('',#187);
#187 = AXIS2_PLACEMENT_3D('',#188,#189,#190);
#188 = CARTESIAN_POINT('',(73.317494,105.15648,24.720607));
#189 = DIRECTION('',(0.,0.,1.));
#190 = DIRECTION('',(1.,0.,0.));
#191 = DEFINITIONAL_REPRESENTATION('',(#192),#196);
#192 = CIRCLE('',#193,4.316451884327);
#193 = AXIS2_PLACEMENT_2D('',#194,#195);
#194 = CARTESIAN_POINT('',(0.,0.));
#195 = DIRECTION('',(1.,0.));
#196 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#197 = ORIENTED_EDGE('',*,*,#198,.F.);
#198 = EDGE_CURVE('',#199,#165,#201,.T.);
#199 = VERTEX_POINT('',#200);
#200 = CARTESIAN_POINT('',(73.592494,105.15648,17.720607));
#201 = SEAM_CURVE('',#202,(#206,#213),.PCURVE_S1.);
#202 = LINE('',#203,#204);
#203 = CARTESIAN_POINT('',(73.592494,105.15648,17.720607));
#204 = VECTOR('',#205,1.);
#205 = DIRECTION('',(0.5,-1.224646799147E-16,0.866025403784));
#206 = PCURVE('',#174,#207);
#207 = DEFINITIONAL_REPRESENTATION('',(#208),#212);
#208 = LINE('',#209,#210);
#209 = CARTESIAN_POINT('',(6.28318530718,-0.));
#210 = VECTOR('',#211,1.);
#211 = DIRECTION('',(0.,1.));
#212 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#213 = PCURVE('',#174,#214);
#214 = DEFINITIONAL_REPRESENTATION('',(#215),#219);
#215 = LINE('',#216,#217);
#216 = CARTESIAN_POINT('',(0.,-0.));
#217 = VECTOR('',#218,1.);
#218 = DIRECTION('',(0.,1.));
#219 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#220 = ORIENTED_EDGE('',*,*,#221,.T.);
#221 = EDGE_CURVE('',#199,#199,#222,.T.);
#222 = SURFACE_CURVE('',#223,(#228,#235),.PCURVE_S1.);
#223 = CIRCLE('',#224,0.275);
#224 = AXIS2_PLACEMENT_3D('',#225,#226,#227);
#225 = CARTESIAN_POINT('',(73.317494,105.15648,17.720607));
#226 = DIRECTION('',(0.,0.,1.));
#227 = DIRECTION('',(1.,0.,0.));
#228 = PCURVE('',#174,#229);
#229 = DEFINITIONAL_REPRESENTATION('',(#230),#234);
#230 = LINE('',#231,#232);
#231 = CARTESIAN_POINT('',(0.,0.));
#232 = VECTOR('',#233,1.);
#233 = DIRECTION('',(1.,0.));
#234 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#235 = PCURVE('',#236,#241);
#236 = CYLINDRICAL_SURFACE('',#237,0.275);
#237 = AXIS2_PLACEMENT_3D('',#238,#239,#240);
#238 = CARTESIAN_POINT('',(73.317494,105.15648,17.720607));
#239 = DIRECTION('',(0.,0.,1.));
#240 = DIRECTION('',(1.,0.,0.));
#241 = DEFINITIONAL_REPRESENTATION('',(#242),#245);
#242 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#243,#244),.UNSPECIFIED.,.F.,.F.,
  (2,2),(0.,6.28318530718),.PIECEWISE_BEZIER_KNOTS.);
#243 = CARTESIAN_POINT('',(0.,0.));
#244 = CARTESIAN_POINT('',(6.28318530718,0.));
#245 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#246 = ORIENTED_EDGE('',*,*,#198,.T.);
#247 = ADVANCED_FACE('',(#248,#251),#186,.T.);
#248 = FACE_BOUND('',#249,.T.);
#249 = EDGE_LOOP('',(#250));
#250 = ORIENTED_EDGE('',*,*,#164,.T.);
#251 = FACE_BOUND('',#252,.T.);
#252 = EDGE_LOOP('',(#253));
#253 = ORIENTED_EDGE('',*,*,#254,.F.);
#254 = EDGE_CURVE('',#255,#255,#257,.T.);
#255 = VERTEX_POINT('',#256);
#256 = CARTESIAN_POINT('',(73.592494,105.15648,24.720607));
#257 = SURFACE_CURVE('',#258,(#263,#270),.PCURVE_S1.);
#258 = CIRCLE('',#259,0.275);
#259 = AXIS2_PLACEMENT_3D('',#260,#261,#262);
#260 = CARTESIAN_POINT('',(73.317494,105.15648,24.720607));
#261 = DIRECTION('',(0.,0.,1.));
#262 = DIRECTION('',(1.,0.,0.));
#263 = PCURVE('',#186,#264);
#264 = DEFINITIONAL_REPRESENTATION('',(#265),#269);
#265 = CIRCLE('',#266,0.275);
#266 = AXIS2_PLACEMENT_2D('',#267,#268);
#267 = CARTESIAN_POINT('',(0.,0.));
#268 = DIRECTION('',(1.,0.));
#269 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#270 = PCURVE('',#236,#271);
#271 = DEFINITIONAL_REPRESENTATION('',(#272),#276);
#272 = LINE('',#273,#274);
#273 = CARTESIAN_POINT('',(0.,7.));
#274 = VECTOR('',#275,1.);
#275 = DIRECTION('',(1.,0.));
#276 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#277 = ADVANCED_FACE('',(#278),#236,.F.);
#278 = FACE_BOUND('',#279,.F.);
#279 = EDGE_LOOP('',(#280,#281,#302,#303));
#280 = ORIENTED_EDGE('',*,*,#254,.F.);
#281 = ORIENTED_EDGE('',*,*,#282,.F.);
#282 = EDGE_CURVE('',#199,#255,#283,.T.);
#283 = SEAM_CURVE('',#284,(#288,#295),.PCURVE_S1.);
#284 = LINE('',#285,#286);
#285 = CARTESIAN_POINT('',(73.592494,105.15648,17.720607));
#286 = VECTOR('',#287,1.);
#287 = DIRECTION('',(0.,0.,1.));
#288 = PCURVE('',#236,#289);
#289 = DEFINITIONAL_REPRESENTATION('',(#290),#294);
#290 = LINE('',#291,#292);
#291 = CARTESIAN_POINT('',(6.28318530718,-0.));
#292 = VECTOR('',#293,1.);
#293 = DIRECTION('',(0.,1.));
#294 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#295 = PCURVE('',#236,#296);
#296 = DEFINITIONAL_REPRESENTATION('',(#297),#301);
#297 = LINE('',#298,#299);
#298 = CARTESIAN_POINT('',(0.,-0.));
#299 = VECTOR('',#300,1.);
#300 = DIRECTION('',(0.,1.));
#301 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#302 = ORIENTED_EDGE('',*,*,#221,.T.);
#303 = ORIENTED_EDGE('',*,*,#282,.T.);
#304 = ( GEOMETRIC_REPRESENTATION_CONTEXT(3) 
GLOBAL_UNCERTAINTY_ASSIGNED_CONTEXT((#308)) GLOBAL_UNIT_ASSIGNED_CONTEXT
((#305,#306,#307)) REPRESENTATION_CONTEXT('Context #1',
  '3D Context with UNIT and UNCERTAINTY') );
#305 = ( LENGTH_UNIT() NAMED_UNIT(*) SI_UNIT(.MILLI.,.METRE.) );
#306 = ( NAMED_UNIT(*) PLANE_ANGLE_UNIT() SI_UNIT($,.RADIAN.) );
#307 = ( NAMED_UNIT(*) SI_UNIT($,.STERADIAN.) SOLID_ANGLE_UNIT() );
#308 = UNCERTAINTY_MEASURE_WITH_UNIT(LENGTH_MEASURE(1.E-07),#305,
  'distance_accuracy_value','confusion accuracy');
#309 = CONTEXT_DEPENDENT_SHAPE_REPRESENTATION(#310,#312);
#310 = ( REPRESENTATION_RELATIONSHIP('','',#157,#10) 
REPRESENTATION_RELATIONSHIP_WITH_TRANSFORMATION(#311) 
SHAPE_REPRESENTATION_RELATIONSHIP() );
#311 = ITEM_DEFINED_TRANSFORMATION('','',#11,#19);
#312 = PRODUCT_DEFINITION_SHAPE('Placement','Placement of an item',#313
  );
#313 = NEXT_ASSEMBLY_USAGE_OCCURRENCE('133','=>[0:1:1:3]','',#5,#152,$);
#314 = PRODUCT_RELATED_PRODUCT_CATEGORY('part',$,(#154));
#315 = SHAPE_DEFINITION_REPRESENTATION(#316,#322);
#316 = PRODUCT_DEFINITION_SHAPE('','',#317);
#317 = PRODUCT_DEFINITION('design','',#318,#321);
#318 = PRODUCT_DEFINITION_FORMATION('','',#319);
#319 = PRODUCT('PC3-P2_Part3','PC3-P2_Part3','',(#320));
#320 = PRODUCT_CONTEXT('',#2,'mechanical');
#321 = PRODUCT_DEFINITION_CONTEXT('part definition',#2,'design');
#322 = ADVANCED_BREP_SHAPE_REPRESENTATION('',(#11,#323),#505);
#323 = MANIFOLD_SOLID_BREP('',#324);
#324 = CLOSED_SHELL('',(#325,#413,#448,#478));
#325 = ADVANCED_FACE('',(#326),#339,.T.);
#326 = FACE_BOUND('',#327,.T.);
#327 = EDGE_LOOP('',(#328,#362,#385,#412));
#328 = ORIENTED_EDGE('',*,*,#329,.F.);
#329 = EDGE_CURVE('',#330,#330,#332,.T.);
#330 = VERTEX_POINT('',#331);
#331 = CARTESIAN_POINT('',(77.633945884327,105.15648,37.5));
#332 = SURFACE_CURVE('',#333,(#338,#350),.PCURVE_S1.);
#333 = CIRCLE('',#334,4.316451884327);
#334 = AXIS2_PLACEMENT_3D('',#335,#336,#337);
#335 = CARTESIAN_POINT('',(73.317494,105.15648,37.5));
#336 = DIRECTION('',(0.,0.,1.));
#337 = DIRECTION('',(1.,0.,0.));
#338 = PCURVE('',#339,#344);
#339 = CYLINDRICAL_SURFACE('',#340,4.316451884327);
#340 = AXIS2_PLACEMENT_3D('',#341,#342,#343);
#341 = CARTESIAN_POINT('',(73.317494,105.15648,24.720607));
#342 = DIRECTION('',(0.,0.,1.));
#343 = DIRECTION('',(1.,0.,0.));
#344 = DEFINITIONAL_REPRESENTATION('',(#345),#349);
#345 = LINE('',#346,#347);
#346 = CARTESIAN_POINT('',(0.,12.779393));
#347 = VECTOR('',#348,1.);
#348 = DIRECTION('',(1.,0.));
#349 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#350 = PCURVE('',#351,#356);
#351 = PLANE('',#352);
#352 = AXIS2_PLACEMENT_3D('',#353,#354,#355);
#353 = CARTESIAN_POINT('',(73.317494,105.15648,37.5));
#354 = DIRECTION('',(0.,0.,1.));
#355 = DIRECTION('',(1.,0.,0.));
#356 = DEFINITIONAL_REPRESENTATION('',(#357),#361);
#357 = CIRCLE('',#358,4.316451884327);
#358 = AXIS2_PLACEMENT_2D('',#359,#360);
#359 = CARTESIAN_POINT('',(0.,0.));
#360 = DIRECTION('',(1.,0.));
#361 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#362 = ORIENTED_EDGE('',*,*,#363,.F.);
#363 = EDGE_CURVE('',#364,#330,#366,.T.);
#364 = VERTEX_POINT('',#365);
#365 = CARTESIAN_POINT('',(77.633945884327,105.15648,24.720607));
#366 = SEAM_CURVE('',#367,(#371,#378),.PCURVE_S1.);
#367 = LINE('',#368,#369);
#368 = CARTESIAN_POINT('',(77.633945884327,105.15648,24.720607));
#369 = VECTOR('',#370,1.);
#370 = DIRECTION('',(0.,0.,1.));
#371 = PCURVE('',#339,#372);
#372 = DEFINITIONAL_REPRESENTATION('',(#373),#377);
#373 = LINE('',#374,#375);
#374 = CARTESIAN_POINT('',(6.28318530718,-0.));
#375 = VECTOR('',#376,1.);
#376 = DIRECTION('',(0.,1.));
#377 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#378 = PCURVE('',#339,#379);
#379 = DEFINITIONAL_REPRESENTATION('',(#380),#384);
#380 = LINE('',#381,#382);
#381 = CARTESIAN_POINT('',(0.,-0.));
#382 = VECTOR('',#383,1.);
#383 = DIRECTION('',(0.,1.));
#384 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#385 = ORIENTED_EDGE('',*,*,#386,.T.);
#386 = EDGE_CURVE('',#364,#364,#387,.T.);
#387 = SURFACE_CURVE('',#388,(#393,#400),.PCURVE_S1.);
#388 = CIRCLE('',#389,4.316451884327);
#389 = AXIS2_PLACEMENT_3D('',#390,#391,#392);
#390 = CARTESIAN_POINT('',(73.317494,105.15648,24.720607));
#391 = DIRECTION('',(0.,0.,1.));
#392 = DIRECTION('',(1.,0.,0.));
#393 = PCURVE('',#339,#394);
#394 = DEFINITIONAL_REPRESENTATION('',(#395),#399);
#395 = LINE('',#396,#397);
#396 = CARTESIAN_POINT('',(0.,0.));
#397 = VECTOR('',#398,1.);
#398 = DIRECTION('',(1.,0.));
#399 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#400 = PCURVE('',#401,#406);
#401 = PLANE('',#402);
#402 = AXIS2_PLACEMENT_3D('',#403,#404,#405);
#403 = CARTESIAN_POINT('',(73.317494,105.15648,24.720607));
#404 = DIRECTION('',(0.,0.,1.));
#405 = DIRECTION('',(1.,0.,0.));
#406 = DEFINITIONAL_REPRESENTATION('',(#407),#411);
#407 = CIRCLE('',#408,4.316451884327);
#408 = AXIS2_PLACEMENT_2D('',#409,#410);
#409 = CARTESIAN_POINT('',(0.,0.));
#410 = DIRECTION('',(1.,0.));
#411 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#412 = ORIENTED_EDGE('',*,*,#363,.T.);
#413 = ADVANCED_FACE('',(#414,#417),#351,.T.);
#414 = FACE_BOUND('',#415,.T.);
#415 = EDGE_LOOP('',(#416));
#416 = ORIENTED_EDGE('',*,*,#329,.T.);
#417 = FACE_BOUND('',#418,.T.);
#418 = EDGE_LOOP('',(#419));
#419 = ORIENTED_EDGE('',*,*,#420,.F.);
#420 = EDGE_CURVE('',#421,#421,#423,.T.);
#421 = VERTEX_POINT('',#422);
#422 = CARTESIAN_POINT('',(73.592494,105.15648,37.5));
#423 = SURFACE_CURVE('',#424,(#429,#436),.PCURVE_S1.);
#424 = CIRCLE('',#425,0.275);
#425 = AXIS2_PLACEMENT_3D('',#426,#427,#428);
#426 = CARTESIAN_POINT('',(73.317494,105.15648,37.5));
#427 = DIRECTION('',(0.,0.,1.));
#428 = DIRECTION('',(1.,0.,0.));
#429 = PCURVE('',#351,#430);
#430 = DEFINITIONAL_REPRESENTATION('',(#431),#435);
#431 = CIRCLE('',#432,0.275);
#432 = AXIS2_PLACEMENT_2D('',#433,#434);
#433 = CARTESIAN_POINT('',(0.,0.));
#434 = DIRECTION('',(1.,0.));
#435 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#436 = PCURVE('',#437,#442);
#437 = CYLINDRICAL_SURFACE('',#438,0.275);
#438 = AXIS2_PLACEMENT_3D('',#439,#440,#441);
#439 = CARTESIAN_POINT('',(73.317494,105.15648,24.720607));
#440 = DIRECTION('',(0.,0.,1.));
#441 = DIRECTION('',(1.,0.,0.));
#442 = DEFINITIONAL_REPRESENTATION('',(#443),#447);
#443 = LINE('',#444,#445);
#444 = CARTESIAN_POINT('',(0.,12.779393));
#445 = VECTOR('',#446,1.);
#446 = DIRECTION('',(1.,0.));
#447 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#448 = ADVANCED_FACE('',(#449,#452),#401,.F.);
#449 = FACE_BOUND('',#450,.F.);
#450 = EDGE_LOOP('',(#451));
#451 = ORIENTED_EDGE('',*,*,#386,.T.);
#452 = FACE_BOUND('',#453,.F.);
#453 = EDGE_LOOP('',(#454));
#454 = ORIENTED_EDGE('',*,*,#455,.F.);
#455 = EDGE_CURVE('',#456,#456,#458,.T.);
#456 = VERTEX_POINT('',#457);
#457 = CARTESIAN_POINT('',(73.592494,105.15648,24.720607));
#458 = SURFACE_CURVE('',#459,(#464,#471),.PCURVE_S1.);
#459 = CIRCLE('',#460,0.275);
#460 = AXIS2_PLACEMENT_3D('',#461,#462,#463);
#461 = CARTESIAN_POINT('',(73.317494,105.15648,24.720607));
#462 = DIRECTION('',(0.,0.,1.));
#463 = DIRECTION('',(1.,0.,0.));
#464 = PCURVE('',#401,#465);
#465 = DEFINITIONAL_REPRESENTATION('',(#466),#470);
#466 = CIRCLE('',#467,0.275);
#467 = AXIS2_PLACEMENT_2D('',#468,#469);
#468 = CARTESIAN_POINT('',(0.,0.));
#469 = DIRECTION('',(1.,0.));
#470 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#471 = PCURVE('',#437,#472);
#472 = DEFINITIONAL_REPRESENTATION('',(#473),#477);
#473 = LINE('',#474,#475);
#474 = CARTESIAN_POINT('',(0.,0.));
#475 = VECTOR('',#476,1.);
#476 = DIRECTION('',(1.,0.));
#477 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#478 = ADVANCED_FACE('',(#479),#437,.F.);
#479 = FACE_BOUND('',#480,.F.);
#480 = EDGE_LOOP('',(#481,#482,#503,#504));
#481 = ORIENTED_EDGE('',*,*,#420,.F.);
#482 = ORIENTED_EDGE('',*,*,#483,.F.);
#483 = EDGE_CURVE('',#456,#421,#484,.T.);
#484 = SEAM_CURVE('',#485,(#489,#496),.PCURVE_S1.);
#485 = LINE('',#486,#487);
#486 = CARTESIAN_POINT('',(73.592494,105.15648,24.720607));
#487 = VECTOR('',#488,1.);
#488 = DIRECTION('',(0.,0.,1.));
#489 = PCURVE('',#437,#490);
#490 = DEFINITIONAL_REPRESENTATION('',(#491),#495);
#491 = LINE('',#492,#493);
#492 = CARTESIAN_POINT('',(6.28318530718,-0.));
#493 = VECTOR('',#494,1.);
#494 = DIRECTION('',(0.,1.));
#495 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#496 = PCURVE('',#437,#497);
#497 = DEFINITIONAL_REPRESENTATION('',(#498),#502);
#498 = LINE('',#499,#500);
#499 = CARTESIAN_POINT('',(0.,-0.));
#500 = VECTOR('',#501,1.);
#501 = DIRECTION('',(0.,1.));
#502 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#503 = ORIENTED_EDGE('',*,*,#455,.T.);
#504 = ORIENTED_EDGE('',*,*,#483,.T.);
#505 = ( GEOMETRIC_REPRESENTATION_CONTEXT(3) 
GLOBAL_UNCERTAINTY_ASSIGNED_CONTEXT((#509)) GLOBAL_UNIT_ASSIGNED_CONTEXT
((#506,#507,#508)) REPRESENTATION_CONTEXT('Context #1',
  '3D Context with UNIT and UNCERTAINTY') );
#506 = ( LENGTH_UNIT() NAMED_UNIT(*) SI_UNIT(.MILLI.,.METRE.) );
#507 = ( NAMED_UNIT(*) PLANE_ANGLE_UNIT() SI_UNIT($,.RADIAN.) );
#508 = ( NAMED_UNIT(*) SI_UNIT($,.STERADIAN.) SOLID_ANGLE_UNIT() );
#509 = UNCERTAINTY_MEASURE_WITH_UNIT(LENGTH_MEASURE(1.E-07),#506,
  'distance_accuracy_value','confusion accuracy');
#510 = CONTEXT_DEPENDENT_SHAPE_REPRESENTATION(#511,#513);
#511 = ( REPRESENTATION_RELATIONSHIP('','',#322,#10) 
REPRESENTATION_RELATIONSHIP_WITH_TRANSFORMATION(#512) 
SHAPE_REPRESENTATION_RELATIONSHIP() );
#512 = ITEM_DEFINED_TRANSFORMATION('','',#11,#23);
#513 = PRODUCT_DEFINITION_SHAPE('Placement','Placement of an item',#514
  );
#514 = NEXT_ASSEMBLY_USAGE_OCCURRENCE('134','=>[0:1:1:4]','',#5,#317,$);
#515 = PRODUCT_RELATED_PRODUCT_CATEGORY('part',$,(#319));
#516 = MECHANICAL_DESIGN_GEOMETRIC_PRESENTATION_REPRESENTATION('',(#517)
  ,#304);
#517 = STYLED_ITEM('color',(#518),#158);
#518 = PRESENTATION_STYLE_ASSIGNMENT((#519));
#519 = SURFACE_STYLE_USAGE(.BOTH.,#520);
#520 = SURFACE_SIDE_STYLE('',(#521));
#521 = SURFACE_STYLE_FILL_AREA(#522);
#522 = FILL_AREA_STYLE('',(#523));
#523 = FILL_AREA_STYLE_COLOUR('',#524);
#524 = DRAUGHTING_PRE_DEFINED_COLOUR('green');
#525 = MECHANICAL_DESIGN_GEOMETRIC_PRESENTATION_REPRESENTATION('',(#526)
  ,#505);
#526 = STYLED_ITEM('color',(#527),#323);
#527 = PRESENTATION_STYLE_ASSIGNMENT((#528));
#528 = SURFACE_STYLE_USAGE(.BOTH.,#529);
#529 = SURFACE_SIDE_STYLE('',(#530));
#530 = SURFACE_STYLE_FILL_AREA(#531);
#531 = FILL_AREA_STYLE('',(#532));
#532 = FILL_AREA_STYLE_COLOUR('',#533);
#533 = DRAUGHTING_PRE_DEFINED_COLOUR('red');
#534 = MECHANICAL_DESIGN_GEOMETRIC_PRESENTATION_REPRESENTATION('',(#535)
  ,#139);
#535 = STYLED_ITEM('color',(#536),#41);
#536 = PRESENTATION_STYLE_ASSIGNMENT((#537));
#537 = SURFACE_STYLE_USAGE(.BOTH.,#538);
#538 = SURFACE_SIDE_STYLE('',(#539));
#539 = SURFACE_STYLE_FILL_AREA(#540);
#540 = FILL_AREA_STYLE('',(#541));
#541 = FILL_AREA_STYLE_COLOUR('',#542);
#542 = DRAUGHTING_PRE_DEFINED_COLOUR('blue');
ENDSEC;
END-ISO-10303-21;
