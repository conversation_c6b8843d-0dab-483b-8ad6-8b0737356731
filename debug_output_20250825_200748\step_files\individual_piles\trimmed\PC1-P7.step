ISO-10303-21;
HEADER;
FILE_DESCRIPTION(('Open CASCADE Model'),'2;1');
FILE_NAME('Pile_PC1-P7','2025-08-25T20:07:48',('Author'),('Open CASCADE'
    ),'Open CASCADE STEP processor 7.8','build123d','Unknown');
FILE_SCHEMA(('AUTOMOTIVE_DESIGN { 1 0 10303 214 1 1 1 1 }'));
ENDSEC;
DATA;
#1 = APPLICATION_PROTOCOL_DEFINITION('international standard',
  'automotive_design',2000,#2);
#2 = APPLICATION_CONTEXT(
  'core data for automotive mechanical design processes');
#3 = SHAPE_DEFINITION_REPRESENTATION(#4,#10);
#4 = PRODUCT_DEFINITION_SHAPE('','',#5);
#5 = PRODUCT_DEFINITION('design','',#6,#9);
#6 = PRODUCT_DEFINITION_FORMATION('','',#7);
#7 = PRODUCT('Pile_PC1-P7','Pile_PC1-P7','',(#8));
#8 = PRODUCT_CONTEXT('',#2,'mechanical');
#9 = PRODUCT_DEFINITION_CONTEXT('part definition',#2,'design');
#10 = SHAPE_REPRESENTATION('',(#11,#15,#19,#23),#27);
#11 = AXIS2_PLACEMENT_3D('',#12,#13,#14);
#12 = CARTESIAN_POINT('',(0.,0.,0.));
#13 = DIRECTION('',(0.,0.,1.));
#14 = DIRECTION('',(1.,0.,-0.));
#15 = AXIS2_PLACEMENT_3D('',#16,#17,#18);
#16 = CARTESIAN_POINT('',(51.980608,124.149601,-7.2622E-02));
#17 = DIRECTION('',(0.,0.,1.));
#18 = DIRECTION('',(1.,0.,0.));
#19 = AXIS2_PLACEMENT_3D('',#20,#21,#22);
#20 = CARTESIAN_POINT('',(0.,0.,0.));
#21 = DIRECTION('',(0.,0.,1.));
#22 = DIRECTION('',(1.,0.,-0.));
#23 = AXIS2_PLACEMENT_3D('',#24,#25,#26);
#24 = CARTESIAN_POINT('',(0.,0.,0.));
#25 = DIRECTION('',(0.,0.,1.));
#26 = DIRECTION('',(1.,0.,-0.));
#27 = ( GEOMETRIC_REPRESENTATION_CONTEXT(3) 
GLOBAL_UNCERTAINTY_ASSIGNED_CONTEXT((#31)) GLOBAL_UNIT_ASSIGNED_CONTEXT(
(#28,#29,#30)) REPRESENTATION_CONTEXT('Context #1',
  '3D Context with UNIT and UNCERTAINTY') );
#28 = ( LENGTH_UNIT() NAMED_UNIT(*) SI_UNIT(.MILLI.,.METRE.) );
#29 = ( NAMED_UNIT(*) PLANE_ANGLE_UNIT() SI_UNIT($,.RADIAN.) );
#30 = ( NAMED_UNIT(*) SI_UNIT($,.STERADIAN.) SOLID_ANGLE_UNIT() );
#31 = UNCERTAINTY_MEASURE_WITH_UNIT(LENGTH_MEASURE(1.E-07),#28,
  'distance_accuracy_value','confusion accuracy');
#32 = PRODUCT_RELATED_PRODUCT_CATEGORY('part',$,(#7));
#33 = SHAPE_DEFINITION_REPRESENTATION(#34,#40);
#34 = PRODUCT_DEFINITION_SHAPE('','',#35);
#35 = PRODUCT_DEFINITION('design','',#36,#39);
#36 = PRODUCT_DEFINITION_FORMATION('','',#37);
#37 = PRODUCT('PC1-P7_Part1','PC1-P7_Part1','',(#38));
#38 = PRODUCT_CONTEXT('',#2,'mechanical');
#39 = PRODUCT_DEFINITION_CONTEXT('part definition',#2,'design');
#40 = ADVANCED_BREP_SHAPE_REPRESENTATION('',(#11,#41),#139);
#41 = MANIFOLD_SOLID_BREP('',#42);
#42 = CLOSED_SHELL('',(#43,#131,#135));
#43 = ADVANCED_FACE('',(#44),#57,.T.);
#44 = FACE_BOUND('',#45,.T.);
#45 = EDGE_LOOP('',(#46,#80,#103,#130));
#46 = ORIENTED_EDGE('',*,*,#47,.F.);
#47 = EDGE_CURVE('',#48,#48,#50,.T.);
#48 = VERTEX_POINT('',#49);
#49 = CARTESIAN_POINT('',(0.275,-6.73555739531E-17,18.072622));
#50 = SURFACE_CURVE('',#51,(#56,#68),.PCURVE_S1.);
#51 = CIRCLE('',#52,0.275);
#52 = AXIS2_PLACEMENT_3D('',#53,#54,#55);
#53 = CARTESIAN_POINT('',(0.,0.,18.072622));
#54 = DIRECTION('',(0.,0.,1.));
#55 = DIRECTION('',(1.,0.,-0.));
#56 = PCURVE('',#57,#62);
#57 = CYLINDRICAL_SURFACE('',#58,0.275);
#58 = AXIS2_PLACEMENT_3D('',#59,#60,#61);
#59 = CARTESIAN_POINT('',(0.,0.,0.));
#60 = DIRECTION('',(0.,0.,1.));
#61 = DIRECTION('',(1.,0.,-0.));
#62 = DEFINITIONAL_REPRESENTATION('',(#63),#67);
#63 = LINE('',#64,#65);
#64 = CARTESIAN_POINT('',(0.,18.072622));
#65 = VECTOR('',#66,1.);
#66 = DIRECTION('',(1.,0.));
#67 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#68 = PCURVE('',#69,#74);
#69 = PLANE('',#70);
#70 = AXIS2_PLACEMENT_3D('',#71,#72,#73);
#71 = CARTESIAN_POINT('',(0.,0.,18.072622));
#72 = DIRECTION('',(0.,0.,1.));
#73 = DIRECTION('',(1.,0.,-0.));
#74 = DEFINITIONAL_REPRESENTATION('',(#75),#79);
#75 = CIRCLE('',#76,0.275);
#76 = AXIS2_PLACEMENT_2D('',#77,#78);
#77 = CARTESIAN_POINT('',(0.,0.));
#78 = DIRECTION('',(1.,0.));
#79 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#80 = ORIENTED_EDGE('',*,*,#81,.F.);
#81 = EDGE_CURVE('',#82,#48,#84,.T.);
#82 = VERTEX_POINT('',#83);
#83 = CARTESIAN_POINT('',(0.275,-6.73555739531E-17,0.));
#84 = SEAM_CURVE('',#85,(#89,#96),.PCURVE_S1.);
#85 = LINE('',#86,#87);
#86 = CARTESIAN_POINT('',(0.275,-6.73555739531E-17,0.));
#87 = VECTOR('',#88,1.);
#88 = DIRECTION('',(0.,0.,1.));
#89 = PCURVE('',#57,#90);
#90 = DEFINITIONAL_REPRESENTATION('',(#91),#95);
#91 = LINE('',#92,#93);
#92 = CARTESIAN_POINT('',(6.28318530718,-0.));
#93 = VECTOR('',#94,1.);
#94 = DIRECTION('',(0.,1.));
#95 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#96 = PCURVE('',#57,#97);
#97 = DEFINITIONAL_REPRESENTATION('',(#98),#102);
#98 = LINE('',#99,#100);
#99 = CARTESIAN_POINT('',(0.,-0.));
#100 = VECTOR('',#101,1.);
#101 = DIRECTION('',(0.,1.));
#102 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#103 = ORIENTED_EDGE('',*,*,#104,.T.);
#104 = EDGE_CURVE('',#82,#82,#105,.T.);
#105 = SURFACE_CURVE('',#106,(#111,#118),.PCURVE_S1.);
#106 = CIRCLE('',#107,0.275);
#107 = AXIS2_PLACEMENT_3D('',#108,#109,#110);
#108 = CARTESIAN_POINT('',(0.,0.,0.));
#109 = DIRECTION('',(0.,0.,1.));
#110 = DIRECTION('',(1.,0.,-0.));
#111 = PCURVE('',#57,#112);
#112 = DEFINITIONAL_REPRESENTATION('',(#113),#117);
#113 = LINE('',#114,#115);
#114 = CARTESIAN_POINT('',(0.,0.));
#115 = VECTOR('',#116,1.);
#116 = DIRECTION('',(1.,0.));
#117 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#118 = PCURVE('',#119,#124);
#119 = PLANE('',#120);
#120 = AXIS2_PLACEMENT_3D('',#121,#122,#123);
#121 = CARTESIAN_POINT('',(0.,0.,0.));
#122 = DIRECTION('',(0.,0.,1.));
#123 = DIRECTION('',(1.,0.,-0.));
#124 = DEFINITIONAL_REPRESENTATION('',(#125),#129);
#125 = CIRCLE('',#126,0.275);
#126 = AXIS2_PLACEMENT_2D('',#127,#128);
#127 = CARTESIAN_POINT('',(0.,0.));
#128 = DIRECTION('',(1.,0.));
#129 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#130 = ORIENTED_EDGE('',*,*,#81,.T.);
#131 = ADVANCED_FACE('',(#132),#69,.T.);
#132 = FACE_BOUND('',#133,.T.);
#133 = EDGE_LOOP('',(#134));
#134 = ORIENTED_EDGE('',*,*,#47,.T.);
#135 = ADVANCED_FACE('',(#136),#119,.F.);
#136 = FACE_BOUND('',#137,.T.);
#137 = EDGE_LOOP('',(#138));
#138 = ORIENTED_EDGE('',*,*,#104,.F.);
#139 = ( GEOMETRIC_REPRESENTATION_CONTEXT(3) 
GLOBAL_UNCERTAINTY_ASSIGNED_CONTEXT((#143)) GLOBAL_UNIT_ASSIGNED_CONTEXT
((#140,#141,#142)) REPRESENTATION_CONTEXT('Context #1',
  '3D Context with UNIT and UNCERTAINTY') );
#140 = ( LENGTH_UNIT() NAMED_UNIT(*) SI_UNIT(.MILLI.,.METRE.) );
#141 = ( NAMED_UNIT(*) PLANE_ANGLE_UNIT() SI_UNIT($,.RADIAN.) );
#142 = ( NAMED_UNIT(*) SI_UNIT($,.STERADIAN.) SOLID_ANGLE_UNIT() );
#143 = UNCERTAINTY_MEASURE_WITH_UNIT(LENGTH_MEASURE(1.E-07),#140,
  'distance_accuracy_value','confusion accuracy');
#144 = CONTEXT_DEPENDENT_SHAPE_REPRESENTATION(#145,#147);
#145 = ( REPRESENTATION_RELATIONSHIP('','',#40,#10) 
REPRESENTATION_RELATIONSHIP_WITH_TRANSFORMATION(#146) 
SHAPE_REPRESENTATION_RELATIONSHIP() );
#146 = ITEM_DEFINED_TRANSFORMATION('','',#11,#15);
#147 = PRODUCT_DEFINITION_SHAPE('Placement','Placement of an item',#148
  );
#148 = NEXT_ASSEMBLY_USAGE_OCCURRENCE('51','=>[0:1:1:2]','',#5,#35,$);
#149 = PRODUCT_RELATED_PRODUCT_CATEGORY('part',$,(#37));
#150 = SHAPE_DEFINITION_REPRESENTATION(#151,#157);
#151 = PRODUCT_DEFINITION_SHAPE('','',#152);
#152 = PRODUCT_DEFINITION('design','',#153,#156);
#153 = PRODUCT_DEFINITION_FORMATION('','',#154);
#154 = PRODUCT('PC1-P7_Part2','PC1-P7_Part2','',(#155));
#155 = PRODUCT_CONTEXT('',#2,'mechanical');
#156 = PRODUCT_DEFINITION_CONTEXT('part definition',#2,'design');
#157 = ADVANCED_BREP_SHAPE_REPRESENTATION('',(#11,#158),#516);
#158 = MANIFOLD_SOLID_BREP('',#159);
#159 = CLOSED_SHELL('',(#160,#386,#436,#484,#510));
#160 = ADVANCED_FACE('',(#161),#175,.T.);
#161 = FACE_BOUND('',#162,.T.);
#162 = EDGE_LOOP('',(#163,#193,#222,#274,#337,#359,#360));
#163 = ORIENTED_EDGE('',*,*,#164,.T.);
#164 = EDGE_CURVE('',#165,#167,#169,.T.);
#165 = VERTEX_POINT('',#166);
#166 = CARTESIAN_POINT('',(52.255608,124.149601,-7.2622E-02));
#167 = VERTEX_POINT('',#168);
#168 = CARTESIAN_POINT('',(59.183811230276,124.149601,11.927378));
#169 = SEAM_CURVE('',#170,(#174,#186),.PCURVE_S1.);
#170 = LINE('',#171,#172);
#171 = CARTESIAN_POINT('',(52.255608,124.149601,-7.2622E-02));
#172 = VECTOR('',#173,1.);
#173 = DIRECTION('',(0.5,-1.224646799147E-16,0.866025403784));
#174 = PCURVE('',#175,#180);
#175 = CONICAL_SURFACE('',#176,0.275,0.523598775598);
#176 = AXIS2_PLACEMENT_3D('',#177,#178,#179);
#177 = CARTESIAN_POINT('',(51.980608,124.149601,-7.2622E-02));
#178 = DIRECTION('',(0.,0.,1.));
#179 = DIRECTION('',(1.,0.,0.));
#180 = DEFINITIONAL_REPRESENTATION('',(#181),#185);
#181 = LINE('',#182,#183);
#182 = CARTESIAN_POINT('',(6.28318530718,-0.));
#183 = VECTOR('',#184,1.);
#184 = DIRECTION('',(0.,1.));
#185 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#186 = PCURVE('',#175,#187);
#187 = DEFINITIONAL_REPRESENTATION('',(#188),#192);
#188 = LINE('',#189,#190);
#189 = CARTESIAN_POINT('',(0.,-0.));
#190 = VECTOR('',#191,1.);
#191 = DIRECTION('',(0.,1.));
#192 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#193 = ORIENTED_EDGE('',*,*,#194,.F.);
#194 = EDGE_CURVE('',#195,#167,#197,.T.);
#195 = VERTEX_POINT('',#196);
#196 = CARTESIAN_POINT('',(58.791930479718,121.80609447047,11.927378));
#197 = SURFACE_CURVE('',#198,(#203,#210),.PCURVE_S1.);
#198 = CIRCLE('',#199,7.203203230276);
#199 = AXIS2_PLACEMENT_3D('',#200,#201,#202);
#200 = CARTESIAN_POINT('',(51.980608,124.149601,11.927378));
#201 = DIRECTION('',(0.,0.,1.));
#202 = DIRECTION('',(1.,0.,0.));
#203 = PCURVE('',#175,#204);
#204 = DEFINITIONAL_REPRESENTATION('',(#205),#209);
#205 = LINE('',#206,#207);
#206 = CARTESIAN_POINT('',(0.,12.));
#207 = VECTOR('',#208,1.);
#208 = DIRECTION('',(1.,0.));
#209 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#210 = PCURVE('',#211,#216);
#211 = PLANE('',#212);
#212 = AXIS2_PLACEMENT_3D('',#213,#214,#215);
#213 = CARTESIAN_POINT('',(51.980608,124.149601,11.927378));
#214 = DIRECTION('',(0.,0.,1.));
#215 = DIRECTION('',(1.,0.,0.));
#216 = DEFINITIONAL_REPRESENTATION('',(#217),#221);
#217 = CIRCLE('',#218,7.203203230276);
#218 = AXIS2_PLACEMENT_2D('',#219,#220);
#219 = CARTESIAN_POINT('',(0.,0.));
#220 = DIRECTION('',(1.,0.));
#221 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#222 = ORIENTED_EDGE('',*,*,#223,.F.);
#223 = EDGE_CURVE('',#224,#195,#226,.T.);
#224 = VERTEX_POINT('',#225);
#225 = CARTESIAN_POINT('',(51.008936,121.80569,3.845855917703));
#226 = SURFACE_CURVE('',#227,(#232,#262),.PCURVE_S1.);
#227 = HYPERBOLA('',#228,4.059685472734,2.343860500508);
#228 = AXIS2_PLACEMENT_3D('',#229,#230,#231);
#229 = CARTESIAN_POINT('',(51.980729806891,121.80574050265,
    -0.548935972081));
#230 = DIRECTION('',(-5.196848995186E-05,0.99999999865,0.));
#231 = DIRECTION('',(0.,0.,1.));
#232 = PCURVE('',#175,#233);
#233 = DEFINITIONAL_REPRESENTATION('',(#234),#261);
#234 = B_SPLINE_CURVE_WITH_KNOTS('',7,(#235,#236,#237,#238,#239,#240,
    #241,#242,#243,#244,#245,#246,#247,#248,#249,#250,#251,#252,#253,
    #254,#255,#256,#257,#258,#259,#260),.UNSPECIFIED.,.F.,.F.,(8,6,6,6,8
    ),(-1.22441406126,-0.471238972175,9.364234463917E-02,0.94096431986,
    1.788286295081),.UNSPECIFIED.);
#235 = CARTESIAN_POINT('',(3.71340184343,7.026191640845));
#236 = CARTESIAN_POINT('',(3.771623420619,6.347339571477));
#237 = CARTESIAN_POINT('',(3.835990844919,5.769820020681));
#238 = CARTESIAN_POINT('',(3.906962406363,5.280796698447));
#239 = CARTESIAN_POINT('',(3.984854207059,4.870306552375));
#240 = CARTESIAN_POINT('',(4.069735200621,4.530656668208));
#241 = CARTESIAN_POINT('',(4.161069631221,4.25607866044));
#242 = CARTESIAN_POINT('',(4.330230759128,3.882376250535));
#243 = CARTESIAN_POINT('',(4.405725631994,3.756549571396));
#244 = CARTESIAN_POINT('',(4.483746740301,3.663350969701));
#245 = CARTESIAN_POINT('',(4.563631262479,3.601624468143));
#246 = CARTESIAN_POINT('',(4.644551482922,3.570717122737));
#247 = CARTESIAN_POINT('',(4.725601930072,3.570461348077));
#248 = CARTESIAN_POINT('',(4.926463945447,3.64726791864));
#249 = CARTESIAN_POINT('',(5.045390819341,3.763053198796));
#250 = CARTESIAN_POINT('',(5.159904082211,3.949643062945));
#251 = CARTESIAN_POINT('',(5.267406685944,4.210641048973));
#252 = CARTESIAN_POINT('',(5.366590139749,4.552224170603));
#253 = CARTESIAN_POINT('',(5.457098902746,4.983488964544));
#254 = CARTESIAN_POINT('',(5.621078840878,6.050926847065));
#255 = CARTESIAN_POINT('',(5.694551014176,6.687099899795));
#256 = CARTESIAN_POINT('',(5.759665242227,7.438500601502));
#257 = CARTESIAN_POINT('',(5.817127229927,8.321577088372));
#258 = CARTESIAN_POINT('',(5.867777530952,9.35722904496));
#259 = CARTESIAN_POINT('',(5.912424305561,10.571978541202));
#260 = CARTESIAN_POINT('',(5.951811633779,12.));
#261 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#262 = PCURVE('',#263,#268);
#263 = PLANE('',#264);
#264 = AXIS2_PLACEMENT_3D('',#265,#266,#267);
#265 = CARTESIAN_POINT('',(77.717428,121.807078,28.));
#266 = DIRECTION('',(-5.196848995186E-05,0.99999999865,0.));
#267 = DIRECTION('',(-0.99999999865,-5.196848995186E-05,0.));
#268 = DEFINITIONAL_REPRESENTATION('',(#269),#273);
#269 = ( BOUNDED_CURVE() B_SPLINE_CURVE(2,(#270,#271,#272),
.UNSPECIFIED.,.F.,.F.) B_SPLINE_CURVE_WITH_KNOTS((3,3),(-1.22441406126,
1.788286295081),.PIECEWISE_BEZIER_KNOTS.) CURVE() 
GEOMETRIC_REPRESENTATION_ITEM() RATIONAL_B_SPLINE_CURVE((1.,
2.365978440371,1.)) REPRESENTATION_ITEM('') );
#270 = CARTESIAN_POINT('',(29.379341239673,-21.04643035915));
#271 = CARTESIAN_POINT('',(25.453682836486,-26.76442893998));
#272 = CARTESIAN_POINT('',(18.925497545839,-16.072622));
#273 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#274 = ORIENTED_EDGE('',*,*,#275,.F.);
#275 = EDGE_CURVE('',#276,#224,#278,.T.);
#276 = VERTEX_POINT('',#277);
#277 = CARTESIAN_POINT('',(50.487848767654,131.19643054603,11.927378));
#278 = SURFACE_CURVE('',#279,(#284,#325),.PCURVE_S1.);
#279 = HYPERBOLA('',#280,1.905328851362,1.100042125229);
#280 = AXIS2_PLACEMENT_3D('',#281,#282,#283);
#281 = CARTESIAN_POINT('',(50.882255533489,124.08865400182,
    -0.548935972081));
#282 = DIRECTION('',(0.998464005442,5.540424023454E-02,0.));
#283 = DIRECTION('',(-0.,0.,1.));
#284 = PCURVE('',#175,#285);
#285 = DEFINITIONAL_REPRESENTATION('',(#286),#324);
#286 = B_SPLINE_CURVE_WITH_KNOTS('',8,(#287,#288,#289,#290,#291,#292,
    #293,#294,#295,#296,#297,#298,#299,#300,#301,#302,#303,#304,#305,
    #306,#307,#308,#309,#310,#311,#312,#313,#314,#315,#316,#317,#318,
    #319,#320,#321,#322,#323),.UNSPECIFIED.,.F.,.F.,(9,7,7,7,7,9),(
    -2.566442342364,-1.470455844753,-0.648465971545,-3.19735666382E-02,
    0.892765040721,1.817503648081),.UNSPECIFIED.);
#287 = CARTESIAN_POINT('',(1.779544563379,12.));
#288 = CARTESIAN_POINT('',(1.800466354656,10.31081507552));
#289 = CARTESIAN_POINT('',(1.824625684143,8.889243602521));
#290 = CARTESIAN_POINT('',(1.852591346686,7.686978568295));
#291 = CARTESIAN_POINT('',(1.885049960736,6.666415517483));
#292 = CARTESIAN_POINT('',(1.922807502611,5.797789684068));
#293 = CARTESIAN_POINT('',(1.966828326301,5.057284591118));
#294 = CARTESIAN_POINT('',(2.018214104732,4.425776462387));
#295 = CARTESIAN_POINT('',(2.12288339625,3.484455535542));
#296 = CARTESIAN_POINT('',(2.172480396695,3.133688379633));
#297 = CARTESIAN_POINT('',(2.227264788847,2.829087452864));
#298 = CARTESIAN_POINT('',(2.28766192563,2.565348305828));
#299 = CARTESIAN_POINT('',(2.354048814255,2.3381342147));
#300 = CARTESIAN_POINT('',(2.426643231786,2.143921251907));
#301 = CARTESIAN_POINT('',(2.505367161566,1.979890817838));
#302 = CARTESIAN_POINT('',(2.65302788216,1.741826963532));
#303 = CARTESIAN_POINT('',(2.719491422035,1.655546187008));
#304 = CARTESIAN_POINT('',(2.788935379906,1.584088818192));
#305 = CARTESIAN_POINT('',(2.861038562872,1.526730945962));
#306 = CARTESIAN_POINT('',(2.93533594264,1.482930854509));
#307 = CARTESIAN_POINT('',(3.011229852672,1.452317463162));
#308 = CARTESIAN_POINT('',(3.088034980887,1.434684281481));
#309 = CARTESIAN_POINT('',(3.280590423458,1.422945776867));
#310 = CARTESIAN_POINT('',(3.396611269632,1.445012650683));
#311 = CARTESIAN_POINT('',(3.510774300832,1.496046096107));
#312 = CARTESIAN_POINT('',(3.620654733939,1.57673249932));
#313 = CARTESIAN_POINT('',(3.724281501156,1.688581829224));
#314 = CARTESIAN_POINT('',(3.820656572739,1.833980821268));
#315 = CARTESIAN_POINT('',(3.909368211583,2.016303858141));
#316 = CARTESIAN_POINT('',(4.071523738589,2.463911533675));
#317 = CARTESIAN_POINT('',(4.144967640816,2.729196114371));
#318 = CARTESIAN_POINT('',(4.210801642974,3.040518591943));
#319 = CARTESIAN_POINT('',(4.269485307736,3.403616990149));
#320 = CARTESIAN_POINT('',(4.321663444645,3.82560949599));
#321 = CARTESIAN_POINT('',(4.368015824662,4.315267270133));
#322 = CARTESIAN_POINT('',(4.409186563586,4.883410631228));
#323 = CARTESIAN_POINT('',(4.4457727741,5.543478471062));
#324 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#325 = PCURVE('',#326,#331);
#326 = PLANE('',#327);
#327 = AXIS2_PLACEMENT_3D('',#328,#329,#330);
#328 = CARTESIAN_POINT('',(51.008936,121.80569,28.));
#329 = DIRECTION('',(0.998464005442,5.540424023454E-02,0.));
#330 = DIRECTION('',(-5.540424023454E-02,0.998464005442,0.));
#331 = DEFINITIONAL_REPRESENTATION('',(#332),#336);
#332 = ( BOUNDED_CURVE() B_SPLINE_CURVE(2,(#333,#334,#335),
.UNSPECIFIED.,.F.,.F.) B_SPLINE_CURVE_WITH_KNOTS((3,3),(-2.566442342364,
1.817503648081),.PIECEWISE_BEZIER_KNOTS.) CURVE() 
GEOMETRIC_REPRESENTATION_ITEM() RATIONAL_B_SPLINE_CURVE((1.,
4.53227790256,1.)) REPRESENTATION_ITEM('') );
#333 = CARTESIAN_POINT('',(9.40518686187,-16.072622));
#334 = CARTESIAN_POINT('',(2.379503660701,-28.09872377459));
#335 = CARTESIAN_POINT('',(-1.010372126087,-22.52914352893));
#336 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#337 = ORIENTED_EDGE('',*,*,#338,.F.);
#338 = EDGE_CURVE('',#167,#276,#339,.T.);
#339 = SURFACE_CURVE('',#340,(#345,#352),.PCURVE_S1.);
#340 = CIRCLE('',#341,7.203203230276);
#341 = AXIS2_PLACEMENT_3D('',#342,#343,#344);
#342 = CARTESIAN_POINT('',(51.980608,124.149601,11.927378));
#343 = DIRECTION('',(0.,0.,1.));
#344 = DIRECTION('',(1.,0.,0.));
#345 = PCURVE('',#175,#346);
#346 = DEFINITIONAL_REPRESENTATION('',(#347),#351);
#347 = LINE('',#348,#349);
#348 = CARTESIAN_POINT('',(0.,12.));
#349 = VECTOR('',#350,1.);
#350 = DIRECTION('',(1.,0.));
#351 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#352 = PCURVE('',#211,#353);
#353 = DEFINITIONAL_REPRESENTATION('',(#354),#358);
#354 = CIRCLE('',#355,7.203203230276);
#355 = AXIS2_PLACEMENT_2D('',#356,#357);
#356 = CARTESIAN_POINT('',(0.,0.));
#357 = DIRECTION('',(1.,0.));
#358 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#359 = ORIENTED_EDGE('',*,*,#164,.F.);
#360 = ORIENTED_EDGE('',*,*,#361,.T.);
#361 = EDGE_CURVE('',#165,#165,#362,.T.);
#362 = SURFACE_CURVE('',#363,(#368,#375),.PCURVE_S1.);
#363 = CIRCLE('',#364,0.275);
#364 = AXIS2_PLACEMENT_3D('',#365,#366,#367);
#365 = CARTESIAN_POINT('',(51.980608,124.149601,-7.2622E-02));
#366 = DIRECTION('',(0.,0.,1.));
#367 = DIRECTION('',(1.,0.,0.));
#368 = PCURVE('',#175,#369);
#369 = DEFINITIONAL_REPRESENTATION('',(#370),#374);
#370 = LINE('',#371,#372);
#371 = CARTESIAN_POINT('',(0.,0.));
#372 = VECTOR('',#373,1.);
#373 = DIRECTION('',(1.,0.));
#374 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#375 = PCURVE('',#376,#381);
#376 = CYLINDRICAL_SURFACE('',#377,0.275);
#377 = AXIS2_PLACEMENT_3D('',#378,#379,#380);
#378 = CARTESIAN_POINT('',(51.980608,124.149601,-7.2622E-02));
#379 = DIRECTION('',(0.,0.,1.));
#380 = DIRECTION('',(1.,0.,0.));
#381 = DEFINITIONAL_REPRESENTATION('',(#382),#385);
#382 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#383,#384),.UNSPECIFIED.,.F.,.F.,
  (2,2),(0.,6.28318530718),.PIECEWISE_BEZIER_KNOTS.);
#383 = CARTESIAN_POINT('',(0.,0.));
#384 = CARTESIAN_POINT('',(6.28318530718,0.));
#385 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#386 = ADVANCED_FACE('',(#387),#376,.F.);
#387 = FACE_BOUND('',#388,.F.);
#388 = EDGE_LOOP('',(#389,#413,#434,#435));
#389 = ORIENTED_EDGE('',*,*,#390,.F.);
#390 = EDGE_CURVE('',#391,#391,#393,.T.);
#391 = VERTEX_POINT('',#392);
#392 = CARTESIAN_POINT('',(52.255608,124.149601,11.927378));
#393 = SURFACE_CURVE('',#394,(#399,#406),.PCURVE_S1.);
#394 = CIRCLE('',#395,0.275);
#395 = AXIS2_PLACEMENT_3D('',#396,#397,#398);
#396 = CARTESIAN_POINT('',(51.980608,124.149601,11.927378));
#397 = DIRECTION('',(0.,0.,1.));
#398 = DIRECTION('',(1.,0.,0.));
#399 = PCURVE('',#376,#400);
#400 = DEFINITIONAL_REPRESENTATION('',(#401),#405);
#401 = LINE('',#402,#403);
#402 = CARTESIAN_POINT('',(0.,12.));
#403 = VECTOR('',#404,1.);
#404 = DIRECTION('',(1.,0.));
#405 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#406 = PCURVE('',#211,#407);
#407 = DEFINITIONAL_REPRESENTATION('',(#408),#412);
#408 = CIRCLE('',#409,0.275);
#409 = AXIS2_PLACEMENT_2D('',#410,#411);
#410 = CARTESIAN_POINT('',(0.,0.));
#411 = DIRECTION('',(1.,0.));
#412 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#413 = ORIENTED_EDGE('',*,*,#414,.F.);
#414 = EDGE_CURVE('',#165,#391,#415,.T.);
#415 = SEAM_CURVE('',#416,(#420,#427),.PCURVE_S1.);
#416 = LINE('',#417,#418);
#417 = CARTESIAN_POINT('',(52.255608,124.149601,-7.2622E-02));
#418 = VECTOR('',#419,1.);
#419 = DIRECTION('',(0.,0.,1.));
#420 = PCURVE('',#376,#421);
#421 = DEFINITIONAL_REPRESENTATION('',(#422),#426);
#422 = LINE('',#423,#424);
#423 = CARTESIAN_POINT('',(6.28318530718,-0.));
#424 = VECTOR('',#425,1.);
#425 = DIRECTION('',(0.,1.));
#426 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#427 = PCURVE('',#376,#428);
#428 = DEFINITIONAL_REPRESENTATION('',(#429),#433);
#429 = LINE('',#430,#431);
#430 = CARTESIAN_POINT('',(0.,-0.));
#431 = VECTOR('',#432,1.);
#432 = DIRECTION('',(0.,1.));
#433 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#434 = ORIENTED_EDGE('',*,*,#361,.T.);
#435 = ORIENTED_EDGE('',*,*,#414,.T.);
#436 = ADVANCED_FACE('',(#437,#481),#211,.T.);
#437 = FACE_BOUND('',#438,.T.);
#438 = EDGE_LOOP('',(#439,#440,#441,#462));
#439 = ORIENTED_EDGE('',*,*,#194,.T.);
#440 = ORIENTED_EDGE('',*,*,#338,.T.);
#441 = ORIENTED_EDGE('',*,*,#442,.F.);
#442 = EDGE_CURVE('',#443,#276,#445,.T.);
#443 = VERTEX_POINT('',#444);
#444 = CARTESIAN_POINT('',(51.008936,121.80569,11.927378));
#445 = SURFACE_CURVE('',#446,(#450,#456),.PCURVE_S1.);
#446 = LINE('',#447,#448);
#447 = CARTESIAN_POINT('',(50.945595766744,122.94717200091,11.927378));
#448 = VECTOR('',#449,1.);
#449 = DIRECTION('',(-5.540424023454E-02,0.998464005442,0.));
#450 = PCURVE('',#211,#451);
#451 = DEFINITIONAL_REPRESENTATION('',(#452),#455);
#452 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#453,#454),.UNSPECIFIED.,.F.,.F.,
  (2,2),(-1.143238008272,8.418563096462),.PIECEWISE_BEZIER_KNOTS.);
#453 = CARTESIAN_POINT('',(-0.971672,-2.343911));
#454 = CARTESIAN_POINT('',(-1.501436325482,7.203203230276));
#455 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#456 = PCURVE('',#326,#457);
#457 = DEFINITIONAL_REPRESENTATION('',(#458),#461);
#458 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#459,#460),.UNSPECIFIED.,.F.,.F.,
  (2,2),(-1.143238008272,8.418563096462),.PIECEWISE_BEZIER_KNOTS.);
#459 = CARTESIAN_POINT('',(0.,-16.072622));
#460 = CARTESIAN_POINT('',(9.561801104733,-16.072622));
#461 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#462 = ORIENTED_EDGE('',*,*,#463,.F.);
#463 = EDGE_CURVE('',#195,#443,#464,.T.);
#464 = SURFACE_CURVE('',#465,(#469,#475),.PCURVE_S1.);
#465 = LINE('',#466,#467);
#466 = CARTESIAN_POINT('',(64.849078903445,121.80640925132,11.927378));
#467 = VECTOR('',#468,1.);
#468 = DIRECTION('',(-0.99999999865,-5.196848995186E-05,0.));
#469 = PCURVE('',#211,#470);
#470 = DEFINITIONAL_REPRESENTATION('',(#471),#474);
#471 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#472,#473),.UNSPECIFIED.,.F.,.F.,
  (2,2),(5.66526768082,13.840142922135),.PIECEWISE_BEZIER_KNOTS.);
#472 = CARTESIAN_POINT('',(7.203203230276,-2.343486164078));
#473 = CARTESIAN_POINT('',(-0.971672,-2.343911));
#474 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#475 = PCURVE('',#263,#476);
#476 = DEFINITIONAL_REPRESENTATION('',(#477),#480);
#477 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#478,#479),.UNSPECIFIED.,.F.,.F.,
  (2,2),(5.66526768082,13.840142922135),.PIECEWISE_BEZIER_KNOTS.);
#478 = CARTESIAN_POINT('',(18.533616794752,-16.072622));
#479 = CARTESIAN_POINT('',(26.708492036066,-16.072622));
#480 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#481 = FACE_BOUND('',#482,.T.);
#482 = EDGE_LOOP('',(#483));
#483 = ORIENTED_EDGE('',*,*,#390,.F.);
#484 = ADVANCED_FACE('',(#485),#326,.F.);
#485 = FACE_BOUND('',#486,.F.);
#486 = EDGE_LOOP('',(#487,#488,#509));
#487 = ORIENTED_EDGE('',*,*,#442,.F.);
#488 = ORIENTED_EDGE('',*,*,#489,.T.);
#489 = EDGE_CURVE('',#443,#224,#490,.T.);
#490 = SURFACE_CURVE('',#491,(#495,#502),.PCURVE_S1.);
#491 = LINE('',#492,#493);
#492 = CARTESIAN_POINT('',(51.008936,121.80569,28.));
#493 = VECTOR('',#494,1.);
#494 = DIRECTION('',(0.,0.,-1.));
#495 = PCURVE('',#326,#496);
#496 = DEFINITIONAL_REPRESENTATION('',(#497),#501);
#497 = LINE('',#498,#499);
#498 = CARTESIAN_POINT('',(0.,0.));
#499 = VECTOR('',#500,1.);
#500 = DIRECTION('',(0.,-1.));
#501 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#502 = PCURVE('',#263,#503);
#503 = DEFINITIONAL_REPRESENTATION('',(#504),#508);
#504 = LINE('',#505,#506);
#505 = CARTESIAN_POINT('',(26.708492036066,0.));
#506 = VECTOR('',#507,1.);
#507 = DIRECTION('',(-0.,-1.));
#508 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#509 = ORIENTED_EDGE('',*,*,#275,.F.);
#510 = ADVANCED_FACE('',(#511),#263,.F.);
#511 = FACE_BOUND('',#512,.F.);
#512 = EDGE_LOOP('',(#513,#514,#515));
#513 = ORIENTED_EDGE('',*,*,#489,.F.);
#514 = ORIENTED_EDGE('',*,*,#463,.F.);
#515 = ORIENTED_EDGE('',*,*,#223,.F.);
#516 = ( GEOMETRIC_REPRESENTATION_CONTEXT(3) 
GLOBAL_UNCERTAINTY_ASSIGNED_CONTEXT((#520)) GLOBAL_UNIT_ASSIGNED_CONTEXT
((#517,#518,#519)) REPRESENTATION_CONTEXT('Context #1',
  '3D Context with UNIT and UNCERTAINTY') );
#517 = ( LENGTH_UNIT() NAMED_UNIT(*) SI_UNIT(.MILLI.,.METRE.) );
#518 = ( NAMED_UNIT(*) PLANE_ANGLE_UNIT() SI_UNIT($,.RADIAN.) );
#519 = ( NAMED_UNIT(*) SI_UNIT($,.STERADIAN.) SOLID_ANGLE_UNIT() );
#520 = UNCERTAINTY_MEASURE_WITH_UNIT(LENGTH_MEASURE(1.E-07),#517,
  'distance_accuracy_value','confusion accuracy');
#521 = CONTEXT_DEPENDENT_SHAPE_REPRESENTATION(#522,#524);
#522 = ( REPRESENTATION_RELATIONSHIP('','',#157,#10) 
REPRESENTATION_RELATIONSHIP_WITH_TRANSFORMATION(#523) 
SHAPE_REPRESENTATION_RELATIONSHIP() );
#523 = ITEM_DEFINED_TRANSFORMATION('','',#11,#19);
#524 = PRODUCT_DEFINITION_SHAPE('Placement','Placement of an item',#525
  );
#525 = NEXT_ASSEMBLY_USAGE_OCCURRENCE('52','=>[0:1:1:3]','',#5,#152,$);
#526 = PRODUCT_RELATED_PRODUCT_CATEGORY('part',$,(#154));
#527 = SHAPE_DEFINITION_REPRESENTATION(#528,#534);
#528 = PRODUCT_DEFINITION_SHAPE('','',#529);
#529 = PRODUCT_DEFINITION('design','',#530,#533);
#530 = PRODUCT_DEFINITION_FORMATION('','',#531);
#531 = PRODUCT('PC1-P7_Part3','PC1-P7_Part3','',(#532));
#532 = PRODUCT_CONTEXT('',#2,'mechanical');
#533 = PRODUCT_DEFINITION_CONTEXT('part definition',#2,'design');
#534 = ADVANCED_BREP_SHAPE_REPRESENTATION('',(#11,#535),#865);
#535 = MANIFOLD_SOLID_BREP('',#536);
#536 = CLOSED_SHELL('',(#537,#659,#734,#781,#831,#838));
#537 = ADVANCED_FACE('',(#538),#553,.T.);
#538 = FACE_BOUND('',#539,.T.);
#539 = EDGE_LOOP('',(#540,#576,#602,#635));
#540 = ORIENTED_EDGE('',*,*,#541,.T.);
#541 = EDGE_CURVE('',#542,#544,#546,.T.);
#542 = VERTEX_POINT('',#543);
#543 = CARTESIAN_POINT('',(58.791930479718,121.80609447047,11.927378));
#544 = VERTEX_POINT('',#545);
#545 = CARTESIAN_POINT('',(50.487848767654,131.19643054603,11.927378));
#546 = SURFACE_CURVE('',#547,(#552,#564),.PCURVE_S1.);
#547 = CIRCLE('',#548,7.203203230276);
#548 = AXIS2_PLACEMENT_3D('',#549,#550,#551);
#549 = CARTESIAN_POINT('',(51.980608,124.149601,11.927378));
#550 = DIRECTION('',(-0.,0.,1.));
#551 = DIRECTION('',(0.945596321799,-0.325342275459,0.));
#552 = PCURVE('',#553,#558);
#553 = CYLINDRICAL_SURFACE('',#554,7.203203230276);
#554 = AXIS2_PLACEMENT_3D('',#555,#556,#557);
#555 = CARTESIAN_POINT('',(51.980608,124.149601,11.927378));
#556 = DIRECTION('',(0.,0.,1.));
#557 = DIRECTION('',(1.,0.,0.));
#558 = DEFINITIONAL_REPRESENTATION('',(#559),#563);
#559 = LINE('',#560,#561);
#560 = CARTESIAN_POINT('',(5.951811633779,0.));
#561 = VECTOR('',#562,1.);
#562 = DIRECTION('',(1.,0.));
#563 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#564 = PCURVE('',#565,#570);
#565 = PLANE('',#566);
#566 = AXIS2_PLACEMENT_3D('',#567,#568,#569);
#567 = CARTESIAN_POINT('',(51.980608,124.149601,11.927378));
#568 = DIRECTION('',(0.,0.,1.));
#569 = DIRECTION('',(1.,0.,0.));
#570 = DEFINITIONAL_REPRESENTATION('',(#571),#575);
#571 = CIRCLE('',#572,7.203203230276);
#572 = AXIS2_PLACEMENT_2D('',#573,#574);
#573 = CARTESIAN_POINT('',(0.,0.));
#574 = DIRECTION('',(0.945596321799,-0.325342275459));
#575 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#576 = ORIENTED_EDGE('',*,*,#577,.T.);
#577 = EDGE_CURVE('',#544,#578,#580,.T.);
#578 = VERTEX_POINT('',#579);
#579 = CARTESIAN_POINT('',(50.487848767654,131.19643054603,18.));
#580 = SURFACE_CURVE('',#581,(#585,#591),.PCURVE_S1.);
#581 = LINE('',#582,#583);
#582 = CARTESIAN_POINT('',(50.487848767654,131.19643054603,11.927378));
#583 = VECTOR('',#584,1.);
#584 = DIRECTION('',(0.,0.,1.));
#585 = PCURVE('',#553,#586);
#586 = DEFINITIONAL_REPRESENTATION('',(#587),#590);
#587 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#588,#589),.UNSPECIFIED.,.F.,.F.,
  (2,2),(0.,6.072622),.PIECEWISE_BEZIER_KNOTS.);
#588 = CARTESIAN_POINT('',(8.062729870559,0.));
#589 = CARTESIAN_POINT('',(8.062729870559,6.072622));
#590 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#591 = PCURVE('',#592,#597);
#592 = PLANE('',#593);
#593 = AXIS2_PLACEMENT_3D('',#594,#595,#596);
#594 = CARTESIAN_POINT('',(51.008936,121.80569,28.));
#595 = DIRECTION('',(0.998464005442,5.540424023454E-02,0.));
#596 = DIRECTION('',(-5.540424023454E-02,0.998464005442,0.));
#597 = DEFINITIONAL_REPRESENTATION('',(#598),#601);
#598 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#599,#600),.UNSPECIFIED.,.F.,.F.,
  (2,2),(-5.999999999062E-07,6.0726226),.PIECEWISE_BEZIER_KNOTS.);
#599 = CARTESIAN_POINT('',(9.40518686187,-16.0726226));
#600 = CARTESIAN_POINT('',(9.40518686187,-9.9999994));
#601 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#602 = ORIENTED_EDGE('',*,*,#603,.T.);
#603 = EDGE_CURVE('',#578,#604,#606,.T.);
#604 = VERTEX_POINT('',#605);
#605 = CARTESIAN_POINT('',(58.791930479718,121.80609447047,18.));
#606 = SURFACE_CURVE('',#607,(#612,#619),.PCURVE_S1.);
#607 = CIRCLE('',#608,7.203203230276);
#608 = AXIS2_PLACEMENT_3D('',#609,#610,#611);
#609 = CARTESIAN_POINT('',(51.980608,124.149601,18.));
#610 = DIRECTION('',(0.,0.,-1.));
#611 = DIRECTION('',(-0.207235473528,0.978291090888,0.));
#612 = PCURVE('',#553,#613);
#613 = DEFINITIONAL_REPRESENTATION('',(#614),#618);
#614 = LINE('',#615,#616);
#615 = CARTESIAN_POINT('',(8.062729870559,6.072622));
#616 = VECTOR('',#617,1.);
#617 = DIRECTION('',(-1.,-0.));
#618 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#619 = PCURVE('',#620,#625);
#620 = PLANE('',#621);
#621 = AXIS2_PLACEMENT_3D('',#622,#623,#624);
#622 = CARTESIAN_POINT('',(51.980608,124.149601,18.));
#623 = DIRECTION('',(0.,0.,1.));
#624 = DIRECTION('',(1.,0.,0.));
#625 = DEFINITIONAL_REPRESENTATION('',(#626),#634);
#626 = ( BOUNDED_CURVE() B_SPLINE_CURVE(2,(#627,#628,#629,#630,#631,#632
,#633),.UNSPECIFIED.,.T.,.F.) B_SPLINE_CURVE_WITH_KNOTS((1,2,2,2,2,1),(
    -2.094395102393,0.,2.094395102393,4.188790204786,6.28318530718,
8.377580409573),.UNSPECIFIED.) CURVE() GEOMETRIC_REPRESENTATION_ITEM() 
RATIONAL_B_SPLINE_CURVE((1.,0.5,1.,0.5,1.,0.5,1.)) REPRESENTATION_ITEM(
  '') );
#627 = CARTESIAN_POINT('',(-1.492759232346,7.046829546037));
#628 = CARTESIAN_POINT('',(10.712707573668,9.632364379927));
#629 = CARTESIAN_POINT('',(6.84911301918,-2.230647356073));
#630 = CARTESIAN_POINT('',(2.985518464692,-14.09365909207));
#631 = CARTESIAN_POINT('',(-5.356353786834,-4.816182189964));
#632 = CARTESIAN_POINT('',(-13.69822603835,4.461294712146));
#633 = CARTESIAN_POINT('',(-1.492759232346,7.046829546037));
#634 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#635 = ORIENTED_EDGE('',*,*,#636,.F.);
#636 = EDGE_CURVE('',#542,#604,#637,.T.);
#637 = SURFACE_CURVE('',#638,(#642,#648),.PCURVE_S1.);
#638 = LINE('',#639,#640);
#639 = CARTESIAN_POINT('',(58.791930479718,121.80609447047,11.927378));
#640 = VECTOR('',#641,1.);
#641 = DIRECTION('',(0.,0.,1.));
#642 = PCURVE('',#553,#643);
#643 = DEFINITIONAL_REPRESENTATION('',(#644),#647);
#644 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#645,#646),.UNSPECIFIED.,.F.,.F.,
  (2,2),(0.,6.072622),.PIECEWISE_BEZIER_KNOTS.);
#645 = CARTESIAN_POINT('',(5.951811633779,0.));
#646 = CARTESIAN_POINT('',(5.951811633779,6.072622));
#647 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#648 = PCURVE('',#649,#654);
#649 = PLANE('',#650);
#650 = AXIS2_PLACEMENT_3D('',#651,#652,#653);
#651 = CARTESIAN_POINT('',(77.717428,121.807078,28.));
#652 = DIRECTION('',(-5.196848995186E-05,0.99999999865,0.));
#653 = DIRECTION('',(-0.99999999865,-5.196848995186E-05,0.));
#654 = DEFINITIONAL_REPRESENTATION('',(#655),#658);
#655 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#656,#657),.UNSPECIFIED.,.F.,.F.,
  (2,2),(-5.999999999062E-07,6.0726226),.PIECEWISE_BEZIER_KNOTS.);
#656 = CARTESIAN_POINT('',(18.925497545839,-16.0726226));
#657 = CARTESIAN_POINT('',(18.925497545839,-9.9999994));
#658 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#659 = ADVANCED_FACE('',(#660,#703),#565,.F.);
#660 = FACE_BOUND('',#661,.F.);
#661 = EDGE_LOOP('',(#662,#663,#684));
#662 = ORIENTED_EDGE('',*,*,#541,.T.);
#663 = ORIENTED_EDGE('',*,*,#664,.F.);
#664 = EDGE_CURVE('',#665,#544,#667,.T.);
#665 = VERTEX_POINT('',#666);
#666 = CARTESIAN_POINT('',(51.008936,121.80569,11.927378));
#667 = SURFACE_CURVE('',#668,(#672,#678),.PCURVE_S1.);
#668 = LINE('',#669,#670);
#669 = CARTESIAN_POINT('',(50.945595766744,122.94717200091,11.927378));
#670 = VECTOR('',#671,1.);
#671 = DIRECTION('',(-5.540424023454E-02,0.998464005442,0.));
#672 = PCURVE('',#565,#673);
#673 = DEFINITIONAL_REPRESENTATION('',(#674),#677);
#674 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#675,#676),.UNSPECIFIED.,.F.,.F.,
  (2,2),(-1.143238008272,8.418563096462),.PIECEWISE_BEZIER_KNOTS.);
#675 = CARTESIAN_POINT('',(-0.971672,-2.343911));
#676 = CARTESIAN_POINT('',(-1.501436325482,7.203203230276));
#677 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#678 = PCURVE('',#592,#679);
#679 = DEFINITIONAL_REPRESENTATION('',(#680),#683);
#680 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#681,#682),.UNSPECIFIED.,.F.,.F.,
  (2,2),(-1.143238008272,8.418563096462),.PIECEWISE_BEZIER_KNOTS.);
#681 = CARTESIAN_POINT('',(0.,-16.072622));
#682 = CARTESIAN_POINT('',(9.561801104733,-16.072622));
#683 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#684 = ORIENTED_EDGE('',*,*,#685,.F.);
#685 = EDGE_CURVE('',#542,#665,#686,.T.);
#686 = SURFACE_CURVE('',#687,(#691,#697),.PCURVE_S1.);
#687 = LINE('',#688,#689);
#688 = CARTESIAN_POINT('',(64.849078903445,121.80640925132,11.927378));
#689 = VECTOR('',#690,1.);
#690 = DIRECTION('',(-0.99999999865,-5.196848995186E-05,0.));
#691 = PCURVE('',#565,#692);
#692 = DEFINITIONAL_REPRESENTATION('',(#693),#696);
#693 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#694,#695),.UNSPECIFIED.,.F.,.F.,
  (2,2),(5.66526768082,13.840142922135),.PIECEWISE_BEZIER_KNOTS.);
#694 = CARTESIAN_POINT('',(7.203203230276,-2.343486164078));
#695 = CARTESIAN_POINT('',(-0.971672,-2.343911));
#696 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#697 = PCURVE('',#649,#698);
#698 = DEFINITIONAL_REPRESENTATION('',(#699),#702);
#699 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#700,#701),.UNSPECIFIED.,.F.,.F.,
  (2,2),(5.66526768082,13.840142922135),.PIECEWISE_BEZIER_KNOTS.);
#700 = CARTESIAN_POINT('',(18.533616794752,-16.072622));
#701 = CARTESIAN_POINT('',(26.708492036066,-16.072622));
#702 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#703 = FACE_BOUND('',#704,.F.);
#704 = EDGE_LOOP('',(#705));
#705 = ORIENTED_EDGE('',*,*,#706,.F.);
#706 = EDGE_CURVE('',#707,#707,#709,.T.);
#707 = VERTEX_POINT('',#708);
#708 = CARTESIAN_POINT('',(52.255608,124.149601,11.927378));
#709 = SURFACE_CURVE('',#710,(#715,#722),.PCURVE_S1.);
#710 = CIRCLE('',#711,0.275);
#711 = AXIS2_PLACEMENT_3D('',#712,#713,#714);
#712 = CARTESIAN_POINT('',(51.980608,124.149601,11.927378));
#713 = DIRECTION('',(0.,0.,1.));
#714 = DIRECTION('',(1.,0.,0.));
#715 = PCURVE('',#565,#716);
#716 = DEFINITIONAL_REPRESENTATION('',(#717),#721);
#717 = CIRCLE('',#718,0.275);
#718 = AXIS2_PLACEMENT_2D('',#719,#720);
#719 = CARTESIAN_POINT('',(0.,0.));
#720 = DIRECTION('',(1.,0.));
#721 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#722 = PCURVE('',#723,#728);
#723 = CYLINDRICAL_SURFACE('',#724,0.275);
#724 = AXIS2_PLACEMENT_3D('',#725,#726,#727);
#725 = CARTESIAN_POINT('',(51.980608,124.149601,11.927378));
#726 = DIRECTION('',(0.,0.,1.));
#727 = DIRECTION('',(1.,0.,0.));
#728 = DEFINITIONAL_REPRESENTATION('',(#729),#733);
#729 = LINE('',#730,#731);
#730 = CARTESIAN_POINT('',(0.,0.));
#731 = VECTOR('',#732,1.);
#732 = DIRECTION('',(1.,0.));
#733 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#734 = ADVANCED_FACE('',(#735),#649,.F.);
#735 = FACE_BOUND('',#736,.F.);
#736 = EDGE_LOOP('',(#737,#760,#779,#780));
#737 = ORIENTED_EDGE('',*,*,#738,.F.);
#738 = EDGE_CURVE('',#739,#665,#741,.T.);
#739 = VERTEX_POINT('',#740);
#740 = CARTESIAN_POINT('',(51.008936,121.80569,18.));
#741 = SURFACE_CURVE('',#742,(#746,#753),.PCURVE_S1.);
#742 = LINE('',#743,#744);
#743 = CARTESIAN_POINT('',(51.008936,121.80569,28.));
#744 = VECTOR('',#745,1.);
#745 = DIRECTION('',(0.,0.,-1.));
#746 = PCURVE('',#649,#747);
#747 = DEFINITIONAL_REPRESENTATION('',(#748),#752);
#748 = LINE('',#749,#750);
#749 = CARTESIAN_POINT('',(26.708492036066,0.));
#750 = VECTOR('',#751,1.);
#751 = DIRECTION('',(-0.,-1.));
#752 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#753 = PCURVE('',#592,#754);
#754 = DEFINITIONAL_REPRESENTATION('',(#755),#759);
#755 = LINE('',#756,#757);
#756 = CARTESIAN_POINT('',(0.,0.));
#757 = VECTOR('',#758,1.);
#758 = DIRECTION('',(0.,-1.));
#759 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#760 = ORIENTED_EDGE('',*,*,#761,.F.);
#761 = EDGE_CURVE('',#604,#739,#762,.T.);
#762 = SURFACE_CURVE('',#763,(#767,#773),.PCURVE_S1.);
#763 = LINE('',#764,#765);
#764 = CARTESIAN_POINT('',(64.849078903445,121.80640925132,18.));
#765 = VECTOR('',#766,1.);
#766 = DIRECTION('',(-0.99999999865,-5.196848995186E-05,0.));
#767 = PCURVE('',#649,#768);
#768 = DEFINITIONAL_REPRESENTATION('',(#769),#772);
#769 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#770,#771),.UNSPECIFIED.,.F.,.F.,
  (2,2),(5.66526768082,13.840142922135),.PIECEWISE_BEZIER_KNOTS.);
#770 = CARTESIAN_POINT('',(18.533616794752,-10.));
#771 = CARTESIAN_POINT('',(26.708492036066,-10.));
#772 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#773 = PCURVE('',#620,#774);
#774 = DEFINITIONAL_REPRESENTATION('',(#775),#778);
#775 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#776,#777),.UNSPECIFIED.,.F.,.F.,
  (2,2),(5.66526768082,13.840142922135),.PIECEWISE_BEZIER_KNOTS.);
#776 = CARTESIAN_POINT('',(7.203203230276,-2.343486164078));
#777 = CARTESIAN_POINT('',(-0.971672,-2.343911));
#778 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#779 = ORIENTED_EDGE('',*,*,#636,.F.);
#780 = ORIENTED_EDGE('',*,*,#685,.T.);
#781 = ADVANCED_FACE('',(#782,#805),#620,.T.);
#782 = FACE_BOUND('',#783,.T.);
#783 = EDGE_LOOP('',(#784,#785,#786));
#784 = ORIENTED_EDGE('',*,*,#761,.F.);
#785 = ORIENTED_EDGE('',*,*,#603,.F.);
#786 = ORIENTED_EDGE('',*,*,#787,.F.);
#787 = EDGE_CURVE('',#739,#578,#788,.T.);
#788 = SURFACE_CURVE('',#789,(#793,#799),.PCURVE_S1.);
#789 = LINE('',#790,#791);
#790 = CARTESIAN_POINT('',(50.945595766744,122.94717200091,18.));
#791 = VECTOR('',#792,1.);
#792 = DIRECTION('',(-5.540424023454E-02,0.998464005442,0.));
#793 = PCURVE('',#620,#794);
#794 = DEFINITIONAL_REPRESENTATION('',(#795),#798);
#795 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#796,#797),.UNSPECIFIED.,.F.,.F.,
  (2,2),(-1.143238008272,8.418563096462),.PIECEWISE_BEZIER_KNOTS.);
#796 = CARTESIAN_POINT('',(-0.971672,-2.343911));
#797 = CARTESIAN_POINT('',(-1.501436325482,7.203203230276));
#798 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#799 = PCURVE('',#592,#800);
#800 = DEFINITIONAL_REPRESENTATION('',(#801),#804);
#801 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#802,#803),.UNSPECIFIED.,.F.,.F.,
  (2,2),(-1.143238008272,8.418563096462),.PIECEWISE_BEZIER_KNOTS.);
#802 = CARTESIAN_POINT('',(0.,-10.));
#803 = CARTESIAN_POINT('',(9.561801104733,-10.));
#804 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#805 = FACE_BOUND('',#806,.T.);
#806 = EDGE_LOOP('',(#807));
#807 = ORIENTED_EDGE('',*,*,#808,.F.);
#808 = EDGE_CURVE('',#809,#809,#811,.T.);
#809 = VERTEX_POINT('',#810);
#810 = CARTESIAN_POINT('',(52.255608,124.149601,18.));
#811 = SURFACE_CURVE('',#812,(#817,#824),.PCURVE_S1.);
#812 = CIRCLE('',#813,0.275);
#813 = AXIS2_PLACEMENT_3D('',#814,#815,#816);
#814 = CARTESIAN_POINT('',(51.980608,124.149601,18.));
#815 = DIRECTION('',(0.,0.,1.));
#816 = DIRECTION('',(1.,0.,0.));
#817 = PCURVE('',#620,#818);
#818 = DEFINITIONAL_REPRESENTATION('',(#819),#823);
#819 = CIRCLE('',#820,0.275);
#820 = AXIS2_PLACEMENT_2D('',#821,#822);
#821 = CARTESIAN_POINT('',(0.,0.));
#822 = DIRECTION('',(1.,0.));
#823 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#824 = PCURVE('',#723,#825);
#825 = DEFINITIONAL_REPRESENTATION('',(#826),#830);
#826 = LINE('',#827,#828);
#827 = CARTESIAN_POINT('',(0.,6.072622));
#828 = VECTOR('',#829,1.);
#829 = DIRECTION('',(1.,0.));
#830 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#831 = ADVANCED_FACE('',(#832),#592,.F.);
#832 = FACE_BOUND('',#833,.F.);
#833 = EDGE_LOOP('',(#834,#835,#836,#837));
#834 = ORIENTED_EDGE('',*,*,#787,.F.);
#835 = ORIENTED_EDGE('',*,*,#738,.T.);
#836 = ORIENTED_EDGE('',*,*,#664,.T.);
#837 = ORIENTED_EDGE('',*,*,#577,.T.);
#838 = ADVANCED_FACE('',(#839),#723,.F.);
#839 = FACE_BOUND('',#840,.F.);
#840 = EDGE_LOOP('',(#841,#842,#863,#864));
#841 = ORIENTED_EDGE('',*,*,#808,.F.);
#842 = ORIENTED_EDGE('',*,*,#843,.F.);
#843 = EDGE_CURVE('',#707,#809,#844,.T.);
#844 = SEAM_CURVE('',#845,(#849,#856),.PCURVE_S1.);
#845 = LINE('',#846,#847);
#846 = CARTESIAN_POINT('',(52.255608,124.149601,11.927378));
#847 = VECTOR('',#848,1.);
#848 = DIRECTION('',(0.,0.,1.));
#849 = PCURVE('',#723,#850);
#850 = DEFINITIONAL_REPRESENTATION('',(#851),#855);
#851 = LINE('',#852,#853);
#852 = CARTESIAN_POINT('',(6.28318530718,-0.));
#853 = VECTOR('',#854,1.);
#854 = DIRECTION('',(0.,1.));
#855 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#856 = PCURVE('',#723,#857);
#857 = DEFINITIONAL_REPRESENTATION('',(#858),#862);
#858 = LINE('',#859,#860);
#859 = CARTESIAN_POINT('',(0.,-0.));
#860 = VECTOR('',#861,1.);
#861 = DIRECTION('',(0.,1.));
#862 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#863 = ORIENTED_EDGE('',*,*,#706,.T.);
#864 = ORIENTED_EDGE('',*,*,#843,.T.);
#865 = ( GEOMETRIC_REPRESENTATION_CONTEXT(3) 
GLOBAL_UNCERTAINTY_ASSIGNED_CONTEXT((#869)) GLOBAL_UNIT_ASSIGNED_CONTEXT
((#866,#867,#868)) REPRESENTATION_CONTEXT('Context #1',
  '3D Context with UNIT and UNCERTAINTY') );
#866 = ( LENGTH_UNIT() NAMED_UNIT(*) SI_UNIT(.MILLI.,.METRE.) );
#867 = ( NAMED_UNIT(*) PLANE_ANGLE_UNIT() SI_UNIT($,.RADIAN.) );
#868 = ( NAMED_UNIT(*) SI_UNIT($,.STERADIAN.) SOLID_ANGLE_UNIT() );
#869 = UNCERTAINTY_MEASURE_WITH_UNIT(LENGTH_MEASURE(1.E-07),#866,
  'distance_accuracy_value','confusion accuracy');
#870 = CONTEXT_DEPENDENT_SHAPE_REPRESENTATION(#871,#873);
#871 = ( REPRESENTATION_RELATIONSHIP('','',#534,#10) 
REPRESENTATION_RELATIONSHIP_WITH_TRANSFORMATION(#872) 
SHAPE_REPRESENTATION_RELATIONSHIP() );
#872 = ITEM_DEFINED_TRANSFORMATION('','',#11,#23);
#873 = PRODUCT_DEFINITION_SHAPE('Placement','Placement of an item',#874
  );
#874 = NEXT_ASSEMBLY_USAGE_OCCURRENCE('53','=>[0:1:1:4]','',#5,#529,$);
#875 = PRODUCT_RELATED_PRODUCT_CATEGORY('part',$,(#531));
#876 = MECHANICAL_DESIGN_GEOMETRIC_PRESENTATION_REPRESENTATION('',(#877)
  ,#865);
#877 = STYLED_ITEM('color',(#878),#535);
#878 = PRESENTATION_STYLE_ASSIGNMENT((#879));
#879 = SURFACE_STYLE_USAGE(.BOTH.,#880);
#880 = SURFACE_SIDE_STYLE('',(#881));
#881 = SURFACE_STYLE_FILL_AREA(#882);
#882 = FILL_AREA_STYLE('',(#883));
#883 = FILL_AREA_STYLE_COLOUR('',#884);
#884 = DRAUGHTING_PRE_DEFINED_COLOUR('red');
#885 = MECHANICAL_DESIGN_GEOMETRIC_PRESENTATION_REPRESENTATION('',(#886)
  ,#516);
#886 = STYLED_ITEM('color',(#887),#158);
#887 = PRESENTATION_STYLE_ASSIGNMENT((#888));
#888 = SURFACE_STYLE_USAGE(.BOTH.,#889);
#889 = SURFACE_SIDE_STYLE('',(#890));
#890 = SURFACE_STYLE_FILL_AREA(#891);
#891 = FILL_AREA_STYLE('',(#892));
#892 = FILL_AREA_STYLE_COLOUR('',#893);
#893 = DRAUGHTING_PRE_DEFINED_COLOUR('green');
#894 = MECHANICAL_DESIGN_GEOMETRIC_PRESENTATION_REPRESENTATION('',(#895)
  ,#139);
#895 = STYLED_ITEM('color',(#896),#41);
#896 = PRESENTATION_STYLE_ASSIGNMENT((#897));
#897 = SURFACE_STYLE_USAGE(.BOTH.,#898);
#898 = SURFACE_SIDE_STYLE('',(#899));
#899 = SURFACE_STYLE_FILL_AREA(#900);
#900 = FILL_AREA_STYLE('',(#901));
#901 = FILL_AREA_STYLE_COLOUR('',#902);
#902 = DRAUGHTING_PRE_DEFINED_COLOUR('blue');
ENDSEC;
END-ISO-10303-21;
