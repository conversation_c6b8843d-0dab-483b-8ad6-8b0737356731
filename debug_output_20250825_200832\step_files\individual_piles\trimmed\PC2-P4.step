ISO-10303-21;
HEADER;
FILE_DESCRIPTION(('Open CASCADE Model'),'2;1');
FILE_NAME('Pile_PC2-P4','2025-08-25T20:08:32',('Author'),('Open CASCADE'
    ),'Open CASCADE STEP processor 7.8','build123d','Unknown');
FILE_SCHEMA(('AUTOMOTIVE_DESIGN { 1 0 10303 214 1 1 1 1 }'));
ENDSEC;
DATA;
#1 = APPLICATION_PROTOCOL_DEFINITION('international standard',
  'automotive_design',2000,#2);
#2 = APPLICATION_CONTEXT(
  'core data for automotive mechanical design processes');
#3 = SHAPE_DEFINITION_REPRESENTATION(#4,#10);
#4 = PRODUCT_DEFINITION_SHAPE('','',#5);
#5 = PRODUCT_DEFINITION('design','',#6,#9);
#6 = PRODUCT_DEFINITION_FORMATION('','',#7);
#7 = PRODUCT('Pile_PC2-P4','Pile_PC2-P4','',(#8));
#8 = PRODUCT_CONTEXT('',#2,'mechanical');
#9 = PRODUCT_DEFINITION_CONTEXT('part definition',#2,'design');
#10 = SHAPE_REPRESENTATION('',(#11,#15,#19,#23),#27);
#11 = AXIS2_PLACEMENT_3D('',#12,#13,#14);
#12 = CARTESIAN_POINT('',(0.,0.,0.));
#13 = DIRECTION('',(0.,0.,1.));
#14 = DIRECTION('',(1.,0.,-0.));
#15 = AXIS2_PLACEMENT_3D('',#16,#17,#18);
#16 = CARTESIAN_POINT('',(51.864283,116.007127,8.270511));
#17 = DIRECTION('',(0.,0.,1.));
#18 = DIRECTION('',(1.,0.,0.));
#19 = AXIS2_PLACEMENT_3D('',#20,#21,#22);
#20 = CARTESIAN_POINT('',(0.,0.,0.));
#21 = DIRECTION('',(0.,0.,1.));
#22 = DIRECTION('',(1.,0.,-0.));
#23 = AXIS2_PLACEMENT_3D('',#24,#25,#26);
#24 = CARTESIAN_POINT('',(0.,0.,0.));
#25 = DIRECTION('',(0.,0.,1.));
#26 = DIRECTION('',(1.,0.,-0.));
#27 = ( GEOMETRIC_REPRESENTATION_CONTEXT(3) 
GLOBAL_UNCERTAINTY_ASSIGNED_CONTEXT((#31)) GLOBAL_UNIT_ASSIGNED_CONTEXT(
(#28,#29,#30)) REPRESENTATION_CONTEXT('Context #1',
  '3D Context with UNIT and UNCERTAINTY') );
#28 = ( LENGTH_UNIT() NAMED_UNIT(*) SI_UNIT(.MILLI.,.METRE.) );
#29 = ( NAMED_UNIT(*) PLANE_ANGLE_UNIT() SI_UNIT($,.RADIAN.) );
#30 = ( NAMED_UNIT(*) SI_UNIT($,.STERADIAN.) SOLID_ANGLE_UNIT() );
#31 = UNCERTAINTY_MEASURE_WITH_UNIT(LENGTH_MEASURE(1.E-07),#28,
  'distance_accuracy_value','confusion accuracy');
#32 = PRODUCT_RELATED_PRODUCT_CATEGORY('part',$,(#7));
#33 = SHAPE_DEFINITION_REPRESENTATION(#34,#40);
#34 = PRODUCT_DEFINITION_SHAPE('','',#35);
#35 = PRODUCT_DEFINITION('design','',#36,#39);
#36 = PRODUCT_DEFINITION_FORMATION('','',#37);
#37 = PRODUCT('PC2-P4_Part1','PC2-P4_Part1','',(#38));
#38 = PRODUCT_CONTEXT('',#2,'mechanical');
#39 = PRODUCT_DEFINITION_CONTEXT('part definition',#2,'design');
#40 = ADVANCED_BREP_SHAPE_REPRESENTATION('',(#11,#41),#139);
#41 = MANIFOLD_SOLID_BREP('',#42);
#42 = CLOSED_SHELL('',(#43,#131,#135));
#43 = ADVANCED_FACE('',(#44),#57,.T.);
#44 = FACE_BOUND('',#45,.T.);
#45 = EDGE_LOOP('',(#46,#80,#103,#130));
#46 = ORIENTED_EDGE('',*,*,#47,.F.);
#47 = EDGE_CURVE('',#48,#48,#50,.T.);
#48 = VERTEX_POINT('',#49);
#49 = CARTESIAN_POINT('',(0.275,-6.73555739531E-17,24.229489));
#50 = SURFACE_CURVE('',#51,(#56,#68),.PCURVE_S1.);
#51 = CIRCLE('',#52,0.275);
#52 = AXIS2_PLACEMENT_3D('',#53,#54,#55);
#53 = CARTESIAN_POINT('',(0.,0.,24.229489));
#54 = DIRECTION('',(0.,0.,1.));
#55 = DIRECTION('',(1.,0.,-0.));
#56 = PCURVE('',#57,#62);
#57 = CYLINDRICAL_SURFACE('',#58,0.275);
#58 = AXIS2_PLACEMENT_3D('',#59,#60,#61);
#59 = CARTESIAN_POINT('',(0.,0.,0.));
#60 = DIRECTION('',(0.,0.,1.));
#61 = DIRECTION('',(1.,0.,-0.));
#62 = DEFINITIONAL_REPRESENTATION('',(#63),#67);
#63 = LINE('',#64,#65);
#64 = CARTESIAN_POINT('',(0.,24.229489));
#65 = VECTOR('',#66,1.);
#66 = DIRECTION('',(1.,0.));
#67 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#68 = PCURVE('',#69,#74);
#69 = PLANE('',#70);
#70 = AXIS2_PLACEMENT_3D('',#71,#72,#73);
#71 = CARTESIAN_POINT('',(0.,0.,24.229489));
#72 = DIRECTION('',(0.,0.,1.));
#73 = DIRECTION('',(1.,0.,-0.));
#74 = DEFINITIONAL_REPRESENTATION('',(#75),#79);
#75 = CIRCLE('',#76,0.275);
#76 = AXIS2_PLACEMENT_2D('',#77,#78);
#77 = CARTESIAN_POINT('',(0.,0.));
#78 = DIRECTION('',(1.,0.));
#79 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#80 = ORIENTED_EDGE('',*,*,#81,.F.);
#81 = EDGE_CURVE('',#82,#48,#84,.T.);
#82 = VERTEX_POINT('',#83);
#83 = CARTESIAN_POINT('',(0.275,-6.73555739531E-17,0.));
#84 = SEAM_CURVE('',#85,(#89,#96),.PCURVE_S1.);
#85 = LINE('',#86,#87);
#86 = CARTESIAN_POINT('',(0.275,-6.73555739531E-17,0.));
#87 = VECTOR('',#88,1.);
#88 = DIRECTION('',(0.,0.,1.));
#89 = PCURVE('',#57,#90);
#90 = DEFINITIONAL_REPRESENTATION('',(#91),#95);
#91 = LINE('',#92,#93);
#92 = CARTESIAN_POINT('',(6.28318530718,-0.));
#93 = VECTOR('',#94,1.);
#94 = DIRECTION('',(0.,1.));
#95 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#96 = PCURVE('',#57,#97);
#97 = DEFINITIONAL_REPRESENTATION('',(#98),#102);
#98 = LINE('',#99,#100);
#99 = CARTESIAN_POINT('',(0.,-0.));
#100 = VECTOR('',#101,1.);
#101 = DIRECTION('',(0.,1.));
#102 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#103 = ORIENTED_EDGE('',*,*,#104,.T.);
#104 = EDGE_CURVE('',#82,#82,#105,.T.);
#105 = SURFACE_CURVE('',#106,(#111,#118),.PCURVE_S1.);
#106 = CIRCLE('',#107,0.275);
#107 = AXIS2_PLACEMENT_3D('',#108,#109,#110);
#108 = CARTESIAN_POINT('',(0.,0.,0.));
#109 = DIRECTION('',(0.,0.,1.));
#110 = DIRECTION('',(1.,0.,-0.));
#111 = PCURVE('',#57,#112);
#112 = DEFINITIONAL_REPRESENTATION('',(#113),#117);
#113 = LINE('',#114,#115);
#114 = CARTESIAN_POINT('',(0.,0.));
#115 = VECTOR('',#116,1.);
#116 = DIRECTION('',(1.,0.));
#117 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#118 = PCURVE('',#119,#124);
#119 = PLANE('',#120);
#120 = AXIS2_PLACEMENT_3D('',#121,#122,#123);
#121 = CARTESIAN_POINT('',(0.,0.,0.));
#122 = DIRECTION('',(0.,0.,1.));
#123 = DIRECTION('',(1.,0.,-0.));
#124 = DEFINITIONAL_REPRESENTATION('',(#125),#129);
#125 = CIRCLE('',#126,0.275);
#126 = AXIS2_PLACEMENT_2D('',#127,#128);
#127 = CARTESIAN_POINT('',(0.,0.));
#128 = DIRECTION('',(1.,0.));
#129 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#130 = ORIENTED_EDGE('',*,*,#81,.T.);
#131 = ADVANCED_FACE('',(#132),#69,.T.);
#132 = FACE_BOUND('',#133,.T.);
#133 = EDGE_LOOP('',(#134));
#134 = ORIENTED_EDGE('',*,*,#47,.T.);
#135 = ADVANCED_FACE('',(#136),#119,.F.);
#136 = FACE_BOUND('',#137,.T.);
#137 = EDGE_LOOP('',(#138));
#138 = ORIENTED_EDGE('',*,*,#104,.F.);
#139 = ( GEOMETRIC_REPRESENTATION_CONTEXT(3) 
GLOBAL_UNCERTAINTY_ASSIGNED_CONTEXT((#143)) GLOBAL_UNIT_ASSIGNED_CONTEXT
((#140,#141,#142)) REPRESENTATION_CONTEXT('Context #1',
  '3D Context with UNIT and UNCERTAINTY') );
#140 = ( LENGTH_UNIT() NAMED_UNIT(*) SI_UNIT(.MILLI.,.METRE.) );
#141 = ( NAMED_UNIT(*) PLANE_ANGLE_UNIT() SI_UNIT($,.RADIAN.) );
#142 = ( NAMED_UNIT(*) SI_UNIT($,.STERADIAN.) SOLID_ANGLE_UNIT() );
#143 = UNCERTAINTY_MEASURE_WITH_UNIT(LENGTH_MEASURE(1.E-07),#140,
  'distance_accuracy_value','confusion accuracy');
#144 = CONTEXT_DEPENDENT_SHAPE_REPRESENTATION(#145,#147);
#145 = ( REPRESENTATION_RELATIONSHIP('','',#40,#10) 
REPRESENTATION_RELATIONSHIP_WITH_TRANSFORMATION(#146) 
SHAPE_REPRESENTATION_RELATIONSHIP() );
#146 = ITEM_DEFINED_TRANSFORMATION('','',#11,#15);
#147 = PRODUCT_DEFINITION_SHAPE('Placement','Placement of an item',#148
  );
#148 = NEXT_ASSEMBLY_USAGE_OCCURRENCE('114','=>[0:1:1:2]','',#5,#35,$);
#149 = PRODUCT_RELATED_PRODUCT_CATEGORY('part',$,(#37));
#150 = SHAPE_DEFINITION_REPRESENTATION(#151,#157);
#151 = PRODUCT_DEFINITION_SHAPE('','',#152);
#152 = PRODUCT_DEFINITION('design','',#153,#156);
#153 = PRODUCT_DEFINITION_FORMATION('','',#154);
#154 = PRODUCT('PC2-P4_Part2','PC2-P4_Part2','',(#155));
#155 = PRODUCT_CONTEXT('',#2,'mechanical');
#156 = PRODUCT_DEFINITION_CONTEXT('part definition',#2,'design');
#157 = ADVANCED_BREP_SHAPE_REPRESENTATION('',(#11,#158),#584);
#158 = MANIFOLD_SOLID_BREP('',#159);
#159 = CLOSED_SHELL('',(#160,#406,#456,#525,#551,#578));
#160 = ADVANCED_FACE('',(#161),#175,.T.);
#161 = FACE_BOUND('',#162,.T.);
#162 = EDGE_LOOP('',(#163,#193,#222,#262,#294,#357,#379,#380));
#163 = ORIENTED_EDGE('',*,*,#164,.T.);
#164 = EDGE_CURVE('',#165,#167,#169,.T.);
#165 = VERTEX_POINT('',#166);
#166 = CARTESIAN_POINT('',(52.139283,116.007127,8.270511));
#167 = VERTEX_POINT('',#168);
#168 = CARTESIAN_POINT('',(56.180734884327,116.007127,15.270511));
#169 = SEAM_CURVE('',#170,(#174,#186),.PCURVE_S1.);
#170 = LINE('',#171,#172);
#171 = CARTESIAN_POINT('',(52.139283,116.007127,8.270511));
#172 = VECTOR('',#173,1.);
#173 = DIRECTION('',(0.5,-1.224646799147E-16,0.866025403784));
#174 = PCURVE('',#175,#180);
#175 = CONICAL_SURFACE('',#176,0.275,0.523598775598);
#176 = AXIS2_PLACEMENT_3D('',#177,#178,#179);
#177 = CARTESIAN_POINT('',(51.864283,116.007127,8.270511));
#178 = DIRECTION('',(0.,0.,1.));
#179 = DIRECTION('',(1.,0.,0.));
#180 = DEFINITIONAL_REPRESENTATION('',(#181),#185);
#181 = LINE('',#182,#183);
#182 = CARTESIAN_POINT('',(6.28318530718,-0.));
#183 = VECTOR('',#184,1.);
#184 = DIRECTION('',(0.,1.));
#185 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#186 = PCURVE('',#175,#187);
#187 = DEFINITIONAL_REPRESENTATION('',(#188),#192);
#188 = LINE('',#189,#190);
#189 = CARTESIAN_POINT('',(0.,-0.));
#190 = VECTOR('',#191,1.);
#191 = DIRECTION('',(0.,1.));
#192 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#193 = ORIENTED_EDGE('',*,*,#194,.F.);
#194 = EDGE_CURVE('',#195,#167,#197,.T.);
#195 = VERTEX_POINT('',#196);
#196 = CARTESIAN_POINT('',(54.867202913116,112.906445,15.270511));
#197 = SURFACE_CURVE('',#198,(#203,#210),.PCURVE_S1.);
#198 = CIRCLE('',#199,4.316451884327);
#199 = AXIS2_PLACEMENT_3D('',#200,#201,#202);
#200 = CARTESIAN_POINT('',(51.864283,116.007127,15.270511));
#201 = DIRECTION('',(0.,0.,1.));
#202 = DIRECTION('',(1.,0.,0.));
#203 = PCURVE('',#175,#204);
#204 = DEFINITIONAL_REPRESENTATION('',(#205),#209);
#205 = LINE('',#206,#207);
#206 = CARTESIAN_POINT('',(0.,7.));
#207 = VECTOR('',#208,1.);
#208 = DIRECTION('',(1.,0.));
#209 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#210 = PCURVE('',#211,#216);
#211 = PLANE('',#212);
#212 = AXIS2_PLACEMENT_3D('',#213,#214,#215);
#213 = CARTESIAN_POINT('',(51.864283,116.007127,15.270511));
#214 = DIRECTION('',(0.,0.,1.));
#215 = DIRECTION('',(1.,0.,0.));
#216 = DEFINITIONAL_REPRESENTATION('',(#217),#221);
#217 = CIRCLE('',#218,4.316451884327);
#218 = AXIS2_PLACEMENT_2D('',#219,#220);
#219 = CARTESIAN_POINT('',(0.,0.));
#220 = DIRECTION('',(1.,0.));
#221 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#222 = ORIENTED_EDGE('',*,*,#223,.F.);
#223 = EDGE_CURVE('',#224,#195,#226,.T.);
#224 = VERTEX_POINT('',#225);
#225 = CARTESIAN_POINT('',(51.710833,112.906445,13.171308455337));
#226 = SURFACE_CURVE('',#227,(#232,#250),.PCURVE_S1.);
#227 = HYPERBOLA('',#228,5.370538762114,3.100682);
#228 = AXIS2_PLACEMENT_3D('',#229,#230,#231);
#229 = CARTESIAN_POINT('',(51.864283,112.906445,7.794197027919));
#230 = DIRECTION('',(0.,1.,0.));
#231 = DIRECTION('',(0.,-0.,1.));
#232 = PCURVE('',#175,#233);
#233 = DEFINITIONAL_REPRESENTATION('',(#234),#249);
#234 = B_SPLINE_CURVE_WITH_KNOTS('',7,(#235,#236,#237,#238,#239,#240,
    #241,#242,#243,#244,#245,#246,#247,#248),.UNSPECIFIED.,.F.,.F.,(8,6,
    8),(-0.67983398899,8.953421399247E-02,0.858902416974),
  .UNSPECIFIED.);
#235 = CARTESIAN_POINT('',(4.079580872821,6.183828571754));
#236 = CARTESIAN_POINT('',(4.168208786226,5.750906773523));
#237 = CARTESIAN_POINT('',(4.263558780752,5.411849736271));
#238 = CARTESIAN_POINT('',(4.36509791425,5.158115733323));
#239 = CARTESIAN_POINT('',(4.471752136456,4.983940024381));
#240 = CARTESIAN_POINT('',(4.581609952944,4.885917789138));
#241 = CARTESIAN_POINT('',(4.692333139159,4.862844896033));
#242 = CARTESIAN_POINT('',(4.911274481329,4.968685707194));
#243 = CARTESIAN_POINT('',(5.01949099218,5.097599409827));
#244 = CARTESIAN_POINT('',(5.124333736885,5.303550801877));
#245 = CARTESIAN_POINT('',(5.223836393527,5.589832302765));
#246 = CARTESIAN_POINT('',(5.316822516902,5.962040198402));
#247 = CARTESIAN_POINT('',(5.402818784687,6.428336359462));
#248 = CARTESIAN_POINT('',(5.481771399417,7.));
#249 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#250 = PCURVE('',#251,#256);
#251 = PLANE('',#252);
#252 = AXIS2_PLACEMENT_3D('',#253,#254,#255);
#253 = CARTESIAN_POINT('',(72.917494,112.906445,42.5));
#254 = DIRECTION('',(0.,1.,0.));
#255 = DIRECTION('',(-1.,0.,0.));
#256 = DEFINITIONAL_REPRESENTATION('',(#257),#261);
#257 = ( BOUNDED_CURVE() B_SPLINE_CURVE(2,(#258,#259,#260),
.UNSPECIFIED.,.F.,.F.) B_SPLINE_CURVE_WITH_KNOTS((3,3),(-0.67983398899,
0.858902416974),.PIECEWISE_BEZIER_KNOTS.) CURVE() 
GEOMETRIC_REPRESENTATION_ITEM() RATIONAL_B_SPLINE_CURVE((1.,
1.310853920943,1.)) REPRESENTATION_ITEM('') );
#258 = CARTESIAN_POINT('',(23.3273271,-28.04566042824));
#259 = CARTESIAN_POINT('',(20.841144511263,-30.59239324156));
#260 = CARTESIAN_POINT('',(18.050291086884,-27.229489));
#261 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#262 = ORIENTED_EDGE('',*,*,#263,.F.);
#263 = EDGE_CURVE('',#264,#224,#266,.T.);
#264 = VERTEX_POINT('',#265);
#265 = CARTESIAN_POINT('',(51.468253,113.528152,12.14235415877));
#266 = SURFACE_CURVE('',#267,(#272,#282),.PCURVE_S1.);
#267 = HYPERBOLA('',#268,2.199760765741,1.270032470254);
#268 = AXIS2_PLACEMENT_3D('',#269,#270,#271);
#269 = CARTESIAN_POINT('',(50.681125093552,115.54547795158,
    7.794197027919));
#270 = DIRECTION('',(0.931596580528,0.363493894237,0.));
#271 = DIRECTION('',(-0.,0.,1.));
#272 = PCURVE('',#175,#273);
#273 = DEFINITIONAL_REPRESENTATION('',(#274),#281);
#274 = B_SPLINE_CURVE_WITH_KNOTS('',5,(#275,#276,#277,#278,#279,#280),
  .UNSPECIFIED.,.F.,.F.,(6,6),(1.276478346493,1.563497415371),
  .PIECEWISE_BEZIER_KNOTS.);
#275 = CARTESIAN_POINT('',(4.54020852424,3.772539412342));
#276 = CARTESIAN_POINT('',(4.569928226457,3.981206876008));
#277 = CARTESIAN_POINT('',(4.597823411469,4.207375274488));
#278 = CARTESIAN_POINT('',(4.623989021169,4.452477635881));
#279 = CARTESIAN_POINT('',(4.648533908614,4.718184945015));
#280 = CARTESIAN_POINT('',(4.671565004709,5.006475767119));
#281 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#282 = PCURVE('',#283,#288);
#283 = PLANE('',#284);
#284 = AXIS2_PLACEMENT_3D('',#285,#286,#287);
#285 = CARTESIAN_POINT('',(51.710833,112.906445,42.5));
#286 = DIRECTION('',(0.931596580528,0.363493894237,0.));
#287 = DIRECTION('',(-0.363493894237,0.931596580528,0.));
#288 = DEFINITIONAL_REPRESENTATION('',(#289),#293);
#289 = ( BOUNDED_CURVE() B_SPLINE_CURVE(2,(#290,#291,#292),
.UNSPECIFIED.,.F.,.F.) B_SPLINE_CURVE_WITH_KNOTS((3,3),(1.276478346493,
1.563497415371),.PIECEWISE_BEZIER_KNOTS.) CURVE() 
GEOMETRIC_REPRESENTATION_ITEM() RATIONAL_B_SPLINE_CURVE((1.,
1.010315178436,1.)) REPRESENTATION_ITEM('') );
#290 = CARTESIAN_POINT('',(0.73409211057,-30.45694958765));
#291 = CARTESIAN_POINT('',(0.384449230906,-29.93883292304));
#292 = CARTESIAN_POINT('',(-6.673564641546E-02,-29.22301323288));
#293 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#294 = ORIENTED_EDGE('',*,*,#295,.F.);
#295 = EDGE_CURVE('',#296,#264,#298,.T.);
#296 = VERTEX_POINT('',#297);
#297 = CARTESIAN_POINT('',(51.095012155986,120.25447667223,15.270511));
#298 = SURFACE_CURVE('',#299,(#304,#345),.PCURVE_S1.);
#299 = HYPERBOLA('',#300,0.922780655056,0.5327676596);
#300 = AXIS2_PLACEMENT_3D('',#301,#302,#303);
#301 = CARTESIAN_POINT('',(51.332333671431,115.97760936205,
    7.794197027919));
#302 = DIRECTION('',(0.998464000178,5.54043351018E-02,0.));
#303 = DIRECTION('',(-0.,0.,1.));
#304 = PCURVE('',#175,#305);
#305 = DEFINITIONAL_REPRESENTATION('',(#306),#344);
#306 = B_SPLINE_CURVE_WITH_KNOTS('',8,(#307,#308,#309,#310,#311,#312,
    #313,#314,#315,#316,#317,#318,#319,#320,#321,#322,#323,#324,#325,
    #326,#327,#328,#329,#330,#331,#332,#333,#334,#335,#336,#337,#338,
    #339,#340,#341,#342,#343),.UNSPECIFIED.,.F.,.F.,(9,7,7,7,7,9),(
    -2.781420269302,-1.456597791207,-0.462980932636,0.282231711292,
    1.400050677185,2.517869643077),.UNSPECIFIED.);
#307 = CARTESIAN_POINT('',(1.749971822646,7.));
#308 = CARTESIAN_POINT('',(1.770411717204,5.771368363173));
#309 = CARTESIAN_POINT('',(1.794690419214,4.777059650337));
#310 = CARTESIAN_POINT('',(1.823636755795,3.965730597703));
#311 = CARTESIAN_POINT('',(1.858279971235,3.299744287649));
#312 = CARTESIAN_POINT('',(1.899917364831,2.750676007879));
#313 = CARTESIAN_POINT('',(1.950131047525,2.296780001776));
#314 = CARTESIAN_POINT('',(2.010908310907,1.921268296056));
#315 = CARTESIAN_POINT('',(2.139016933726,1.378619150806));
#316 = CARTESIAN_POINT('',(2.200911210121,1.182857596692));
#317 = CARTESIAN_POINT('',(2.270581722622,1.01843164631));
#318 = CARTESIAN_POINT('',(2.34877784166,0.881085616908));
#319 = CARTESIAN_POINT('',(2.436069556301,0.767505280007));
#320 = CARTESIAN_POINT('',(2.53265655004,0.675146989893));
#321 = CARTESIAN_POINT('',(2.637765693274,0.60212454721));
#322 = CARTESIAN_POINT('',(2.833738044864,0.505911383518));
#323 = CARTESIAN_POINT('',(2.921593744208,0.474826631659));
#324 = CARTESIAN_POINT('',(3.01261945637,0.453346141794));
#325 = CARTESIAN_POINT('',(3.105850575939,0.441112527704));
#326 = CARTESIAN_POINT('',(3.200040061043,0.437941279678));
#327 = CARTESIAN_POINT('',(3.29383991101,0.443814014576));
#328 = CARTESIAN_POINT('',(3.386022298369,0.458879646608));
#329 = CARTESIAN_POINT('',(3.609924464912,0.520338562865));
#330 = CARTESIAN_POINT('',(3.738366149936,0.57862927332));
#331 = CARTESIAN_POINT('',(3.857523432413,0.659432380774));
#332 = CARTESIAN_POINT('',(3.9650841402,0.764737000654));
#333 = CARTESIAN_POINT('',(4.060769324295,0.897492403612));
#334 = CARTESIAN_POINT('',(4.145196188033,1.061770584584));
#335 = CARTESIAN_POINT('',(4.219339347362,1.263032239526));
#336 = CARTESIAN_POINT('',(4.349258173305,1.754132916345));
#337 = CARTESIAN_POINT('',(4.405033174431,2.043971716736));
#338 = CARTESIAN_POINT('',(4.452723032832,2.385405717709));
#339 = CARTESIAN_POINT('',(4.493532543786,2.787580493248));
#340 = CARTESIAN_POINT('',(4.52852866019,3.261953705524));
#341 = CARTESIAN_POINT('',(4.558603881604,3.822920317805));
#342 = CARTESIAN_POINT('',(4.584520202994,4.488765302489));
#343 = CARTESIAN_POINT('',(4.606907425283,5.28311937932));
#344 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#345 = PCURVE('',#346,#351);
#346 = PLANE('',#347);
#347 = AXIS2_PLACEMENT_3D('',#348,#349,#350);
#348 = CARTESIAN_POINT('',(51.468253,113.528152,42.5));
#349 = DIRECTION('',(0.998464000178,5.54043351018E-02,0.));
#350 = DIRECTION('',(-5.54043351018E-02,0.998464000178,0.));
#351 = DEFINITIONAL_REPRESENTATION('',(#352),#356);
#352 = ( BOUNDED_CURVE() B_SPLINE_CURVE(2,(#353,#354,#355),
.UNSPECIFIED.,.F.,.F.) B_SPLINE_CURVE_WITH_KNOTS((3,3),(-2.781420269302,
2.517869643077),.PIECEWISE_BEZIER_KNOTS.) CURVE() 
GEOMETRIC_REPRESENTATION_ITEM() RATIONAL_B_SPLINE_CURVE((1.,
7.109845267674,1.)) REPRESENTATION_ITEM('') );
#353 = CARTESIAN_POINT('',(6.736672199536,-27.229489));
#354 = CARTESIAN_POINT('',(2.4631285424,-34.57488533036));
#355 = CARTESIAN_POINT('',(-0.829027185609,-28.94636962068));
#356 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#357 = ORIENTED_EDGE('',*,*,#358,.F.);
#358 = EDGE_CURVE('',#167,#296,#359,.T.);
#359 = SURFACE_CURVE('',#360,(#365,#372),.PCURVE_S1.);
#360 = CIRCLE('',#361,4.316451884327);
#361 = AXIS2_PLACEMENT_3D('',#362,#363,#364);
#362 = CARTESIAN_POINT('',(51.864283,116.007127,15.270511));
#363 = DIRECTION('',(0.,0.,1.));
#364 = DIRECTION('',(1.,0.,0.));
#365 = PCURVE('',#175,#366);
#366 = DEFINITIONAL_REPRESENTATION('',(#367),#371);
#367 = LINE('',#368,#369);
#368 = CARTESIAN_POINT('',(0.,7.));
#369 = VECTOR('',#370,1.);
#370 = DIRECTION('',(1.,0.));
#371 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#372 = PCURVE('',#211,#373);
#373 = DEFINITIONAL_REPRESENTATION('',(#374),#378);
#374 = CIRCLE('',#375,4.316451884327);
#375 = AXIS2_PLACEMENT_2D('',#376,#377);
#376 = CARTESIAN_POINT('',(0.,0.));
#377 = DIRECTION('',(1.,0.));
#378 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#379 = ORIENTED_EDGE('',*,*,#164,.F.);
#380 = ORIENTED_EDGE('',*,*,#381,.T.);
#381 = EDGE_CURVE('',#165,#165,#382,.T.);
#382 = SURFACE_CURVE('',#383,(#388,#395),.PCURVE_S1.);
#383 = CIRCLE('',#384,0.275);
#384 = AXIS2_PLACEMENT_3D('',#385,#386,#387);
#385 = CARTESIAN_POINT('',(51.864283,116.007127,8.270511));
#386 = DIRECTION('',(0.,0.,1.));
#387 = DIRECTION('',(1.,0.,0.));
#388 = PCURVE('',#175,#389);
#389 = DEFINITIONAL_REPRESENTATION('',(#390),#394);
#390 = LINE('',#391,#392);
#391 = CARTESIAN_POINT('',(0.,0.));
#392 = VECTOR('',#393,1.);
#393 = DIRECTION('',(1.,0.));
#394 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#395 = PCURVE('',#396,#401);
#396 = CYLINDRICAL_SURFACE('',#397,0.275);
#397 = AXIS2_PLACEMENT_3D('',#398,#399,#400);
#398 = CARTESIAN_POINT('',(51.864283,116.007127,8.270511));
#399 = DIRECTION('',(0.,0.,1.));
#400 = DIRECTION('',(1.,0.,0.));
#401 = DEFINITIONAL_REPRESENTATION('',(#402),#405);
#402 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#403,#404),.UNSPECIFIED.,.F.,.F.,
  (2,2),(0.,6.28318530718),.PIECEWISE_BEZIER_KNOTS.);
#403 = CARTESIAN_POINT('',(0.,0.));
#404 = CARTESIAN_POINT('',(6.28318530718,0.));
#405 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#406 = ADVANCED_FACE('',(#407),#396,.F.);
#407 = FACE_BOUND('',#408,.F.);
#408 = EDGE_LOOP('',(#409,#433,#454,#455));
#409 = ORIENTED_EDGE('',*,*,#410,.F.);
#410 = EDGE_CURVE('',#411,#411,#413,.T.);
#411 = VERTEX_POINT('',#412);
#412 = CARTESIAN_POINT('',(52.139283,116.007127,15.270511));
#413 = SURFACE_CURVE('',#414,(#419,#426),.PCURVE_S1.);
#414 = CIRCLE('',#415,0.275);
#415 = AXIS2_PLACEMENT_3D('',#416,#417,#418);
#416 = CARTESIAN_POINT('',(51.864283,116.007127,15.270511));
#417 = DIRECTION('',(0.,0.,1.));
#418 = DIRECTION('',(1.,0.,0.));
#419 = PCURVE('',#396,#420);
#420 = DEFINITIONAL_REPRESENTATION('',(#421),#425);
#421 = LINE('',#422,#423);
#422 = CARTESIAN_POINT('',(0.,7.));
#423 = VECTOR('',#424,1.);
#424 = DIRECTION('',(1.,0.));
#425 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#426 = PCURVE('',#211,#427);
#427 = DEFINITIONAL_REPRESENTATION('',(#428),#432);
#428 = CIRCLE('',#429,0.275);
#429 = AXIS2_PLACEMENT_2D('',#430,#431);
#430 = CARTESIAN_POINT('',(0.,0.));
#431 = DIRECTION('',(1.,0.));
#432 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#433 = ORIENTED_EDGE('',*,*,#434,.F.);
#434 = EDGE_CURVE('',#165,#411,#435,.T.);
#435 = SEAM_CURVE('',#436,(#440,#447),.PCURVE_S1.);
#436 = LINE('',#437,#438);
#437 = CARTESIAN_POINT('',(52.139283,116.007127,8.270511));
#438 = VECTOR('',#439,1.);
#439 = DIRECTION('',(0.,0.,1.));
#440 = PCURVE('',#396,#441);
#441 = DEFINITIONAL_REPRESENTATION('',(#442),#446);
#442 = LINE('',#443,#444);
#443 = CARTESIAN_POINT('',(6.28318530718,-0.));
#444 = VECTOR('',#445,1.);
#445 = DIRECTION('',(0.,1.));
#446 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#447 = PCURVE('',#396,#448);
#448 = DEFINITIONAL_REPRESENTATION('',(#449),#453);
#449 = LINE('',#450,#451);
#450 = CARTESIAN_POINT('',(0.,-0.));
#451 = VECTOR('',#452,1.);
#452 = DIRECTION('',(0.,1.));
#453 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#454 = ORIENTED_EDGE('',*,*,#381,.T.);
#455 = ORIENTED_EDGE('',*,*,#434,.T.);
#456 = ADVANCED_FACE('',(#457,#522),#211,.T.);
#457 = FACE_BOUND('',#458,.T.);
#458 = EDGE_LOOP('',(#459,#460,#461,#482,#503));
#459 = ORIENTED_EDGE('',*,*,#194,.T.);
#460 = ORIENTED_EDGE('',*,*,#358,.T.);
#461 = ORIENTED_EDGE('',*,*,#462,.F.);
#462 = EDGE_CURVE('',#463,#296,#465,.T.);
#463 = VERTEX_POINT('',#464);
#464 = CARTESIAN_POINT('',(51.468253,113.528152,15.270511));
#465 = SURFACE_CURVE('',#466,(#470,#476),.PCURVE_S1.);
#466 = LINE('',#467,#468);
#467 = CARTESIAN_POINT('',(51.400293335715,114.75288068102,15.270511));
#468 = VECTOR('',#469,1.);
#469 = DIRECTION('',(-5.54043351018E-02,0.998464000178,0.));
#470 = PCURVE('',#211,#471);
#471 = DEFINITIONAL_REPRESENTATION('',(#472),#475);
#472 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#473,#474),.UNSPECIFIED.,.F.,.F.,
  (2,2),(-1.226612758006,5.579267957888),.PIECEWISE_BEZIER_KNOTS.);
#473 = CARTESIAN_POINT('',(-0.39603,-2.478975));
#474 = CARTESIAN_POINT('',(-0.773105295846,4.316451884327));
#475 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#476 = PCURVE('',#346,#477);
#477 = DEFINITIONAL_REPRESENTATION('',(#478),#481);
#478 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#479,#480),.UNSPECIFIED.,.F.,.F.,
  (2,2),(-1.226612758006,5.579267957888),.PIECEWISE_BEZIER_KNOTS.);
#479 = CARTESIAN_POINT('',(-1.42108547152E-14,-27.229489));
#480 = CARTESIAN_POINT('',(6.805880715894,-27.229489));
#481 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#482 = ORIENTED_EDGE('',*,*,#483,.F.);
#483 = EDGE_CURVE('',#484,#463,#486,.T.);
#484 = VERTEX_POINT('',#485);
#485 = CARTESIAN_POINT('',(51.710833,112.906445,15.270511));
#486 = SURFACE_CURVE('',#487,(#491,#497),.PCURVE_S1.);
#487 = LINE('',#488,#489);
#488 = CARTESIAN_POINT('',(51.195979046776,114.22596147579,15.270511));
#489 = VECTOR('',#490,1.);
#490 = DIRECTION('',(-0.363493894237,0.931596580528,0.));
#491 = PCURVE('',#211,#492);
#492 = DEFINITIONAL_REPRESENTATION('',(#493),#496);
#493 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#494,#495),.UNSPECIFIED.,.F.,.F.,
  (2,2),(-1.416403305218,-0.749046841063),.PIECEWISE_BEZIER_KNOTS.);
#494 = CARTESIAN_POINT('',(-0.15345,-3.100682));
#495 = CARTESIAN_POINT('',(-0.39603,-2.478975));
#496 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#497 = PCURVE('',#283,#498);
#498 = DEFINITIONAL_REPRESENTATION('',(#499),#502);
#499 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#500,#501),.UNSPECIFIED.,.F.,.F.,
  (2,2),(-1.416403305218,-0.749046841063),.PIECEWISE_BEZIER_KNOTS.);
#500 = CARTESIAN_POINT('',(0.,-27.229489));
#501 = CARTESIAN_POINT('',(0.667356464155,-27.229489));
#502 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#503 = ORIENTED_EDGE('',*,*,#504,.F.);
#504 = EDGE_CURVE('',#195,#484,#505,.T.);
#505 = SURFACE_CURVE('',#506,(#510,#516),.PCURVE_S1.);
#506 = LINE('',#507,#508);
#507 = CARTESIAN_POINT('',(62.3908885,112.906445,15.270511));
#508 = VECTOR('',#509,1.);
#509 = DIRECTION('',(-1.,0.,0.));
#510 = PCURVE('',#211,#511);
#511 = DEFINITIONAL_REPRESENTATION('',(#512),#515);
#512 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#513,#514),.UNSPECIFIED.,.F.,.F.,
  (2,2),(6.210153615673,10.6800555),.PIECEWISE_BEZIER_KNOTS.);
#513 = CARTESIAN_POINT('',(4.316451884327,-3.100682));
#514 = CARTESIAN_POINT('',(-0.15345,-3.100682));
#515 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#516 = PCURVE('',#251,#517);
#517 = DEFINITIONAL_REPRESENTATION('',(#518),#521);
#518 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#519,#520),.UNSPECIFIED.,.F.,.F.,
  (2,2),(6.210153615673,10.6800555),.PIECEWISE_BEZIER_KNOTS.);
#519 = CARTESIAN_POINT('',(16.736759115673,-27.229489));
#520 = CARTESIAN_POINT('',(21.206661,-27.229489));
#521 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#522 = FACE_BOUND('',#523,.T.);
#523 = EDGE_LOOP('',(#524));
#524 = ORIENTED_EDGE('',*,*,#410,.F.);
#525 = ADVANCED_FACE('',(#526),#346,.F.);
#526 = FACE_BOUND('',#527,.F.);
#527 = EDGE_LOOP('',(#528,#529,#550));
#528 = ORIENTED_EDGE('',*,*,#462,.F.);
#529 = ORIENTED_EDGE('',*,*,#530,.T.);
#530 = EDGE_CURVE('',#463,#264,#531,.T.);
#531 = SURFACE_CURVE('',#532,(#536,#543),.PCURVE_S1.);
#532 = LINE('',#533,#534);
#533 = CARTESIAN_POINT('',(51.468253,113.528152,42.5));
#534 = VECTOR('',#535,1.);
#535 = DIRECTION('',(0.,0.,-1.));
#536 = PCURVE('',#346,#537);
#537 = DEFINITIONAL_REPRESENTATION('',(#538),#542);
#538 = LINE('',#539,#540);
#539 = CARTESIAN_POINT('',(0.,0.));
#540 = VECTOR('',#541,1.);
#541 = DIRECTION('',(0.,-1.));
#542 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#543 = PCURVE('',#283,#544);
#544 = DEFINITIONAL_REPRESENTATION('',(#545),#549);
#545 = LINE('',#546,#547);
#546 = CARTESIAN_POINT('',(0.667356464155,0.));
#547 = VECTOR('',#548,1.);
#548 = DIRECTION('',(0.,-1.));
#549 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#550 = ORIENTED_EDGE('',*,*,#295,.F.);
#551 = ADVANCED_FACE('',(#552),#283,.F.);
#552 = FACE_BOUND('',#553,.F.);
#553 = EDGE_LOOP('',(#554,#555,#576,#577));
#554 = ORIENTED_EDGE('',*,*,#483,.F.);
#555 = ORIENTED_EDGE('',*,*,#556,.T.);
#556 = EDGE_CURVE('',#484,#224,#557,.T.);
#557 = SURFACE_CURVE('',#558,(#562,#569),.PCURVE_S1.);
#558 = LINE('',#559,#560);
#559 = CARTESIAN_POINT('',(51.710833,112.906445,42.5));
#560 = VECTOR('',#561,1.);
#561 = DIRECTION('',(0.,0.,-1.));
#562 = PCURVE('',#283,#563);
#563 = DEFINITIONAL_REPRESENTATION('',(#564),#568);
#564 = LINE('',#565,#566);
#565 = CARTESIAN_POINT('',(0.,0.));
#566 = VECTOR('',#567,1.);
#567 = DIRECTION('',(0.,-1.));
#568 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#569 = PCURVE('',#251,#570);
#570 = DEFINITIONAL_REPRESENTATION('',(#571),#575);
#571 = LINE('',#572,#573);
#572 = CARTESIAN_POINT('',(21.206661,0.));
#573 = VECTOR('',#574,1.);
#574 = DIRECTION('',(0.,-1.));
#575 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#576 = ORIENTED_EDGE('',*,*,#263,.F.);
#577 = ORIENTED_EDGE('',*,*,#530,.F.);
#578 = ADVANCED_FACE('',(#579),#251,.F.);
#579 = FACE_BOUND('',#580,.F.);
#580 = EDGE_LOOP('',(#581,#582,#583));
#581 = ORIENTED_EDGE('',*,*,#556,.F.);
#582 = ORIENTED_EDGE('',*,*,#504,.F.);
#583 = ORIENTED_EDGE('',*,*,#223,.F.);
#584 = ( GEOMETRIC_REPRESENTATION_CONTEXT(3) 
GLOBAL_UNCERTAINTY_ASSIGNED_CONTEXT((#588)) GLOBAL_UNIT_ASSIGNED_CONTEXT
((#585,#586,#587)) REPRESENTATION_CONTEXT('Context #1',
  '3D Context with UNIT and UNCERTAINTY') );
#585 = ( LENGTH_UNIT() NAMED_UNIT(*) SI_UNIT(.MILLI.,.METRE.) );
#586 = ( NAMED_UNIT(*) PLANE_ANGLE_UNIT() SI_UNIT($,.RADIAN.) );
#587 = ( NAMED_UNIT(*) SI_UNIT($,.STERADIAN.) SOLID_ANGLE_UNIT() );
#588 = UNCERTAINTY_MEASURE_WITH_UNIT(LENGTH_MEASURE(1.E-07),#585,
  'distance_accuracy_value','confusion accuracy');
#589 = CONTEXT_DEPENDENT_SHAPE_REPRESENTATION(#590,#592);
#590 = ( REPRESENTATION_RELATIONSHIP('','',#157,#10) 
REPRESENTATION_RELATIONSHIP_WITH_TRANSFORMATION(#591) 
SHAPE_REPRESENTATION_RELATIONSHIP() );
#591 = ITEM_DEFINED_TRANSFORMATION('','',#11,#19);
#592 = PRODUCT_DEFINITION_SHAPE('Placement','Placement of an item',#593
  );
#593 = NEXT_ASSEMBLY_USAGE_OCCURRENCE('115','=>[0:1:1:3]','',#5,#152,$);
#594 = PRODUCT_RELATED_PRODUCT_CATEGORY('part',$,(#154));
#595 = SHAPE_DEFINITION_REPRESENTATION(#596,#602);
#596 = PRODUCT_DEFINITION_SHAPE('','',#597);
#597 = PRODUCT_DEFINITION('design','',#598,#601);
#598 = PRODUCT_DEFINITION_FORMATION('','',#599);
#599 = PRODUCT('PC2-P4_Part3','PC2-P4_Part3','',(#600));
#600 = PRODUCT_CONTEXT('',#2,'mechanical');
#601 = PRODUCT_DEFINITION_CONTEXT('part definition',#2,'design');
#602 = ADVANCED_BREP_SHAPE_REPRESENTATION('',(#11,#603),#1007);
#603 = MANIFOLD_SOLID_BREP('',#604);
#604 = CLOSED_SHELL('',(#605,#727,#828,#875,#946,#973,#980));
#605 = ADVANCED_FACE('',(#606),#621,.T.);
#606 = FACE_BOUND('',#607,.T.);
#607 = EDGE_LOOP('',(#608,#644,#670,#703));
#608 = ORIENTED_EDGE('',*,*,#609,.T.);
#609 = EDGE_CURVE('',#610,#612,#614,.T.);
#610 = VERTEX_POINT('',#611);
#611 = CARTESIAN_POINT('',(54.867202913116,112.906445,15.270511));
#612 = VERTEX_POINT('',#613);
#613 = CARTESIAN_POINT('',(51.095012155986,120.25447667223,15.270511));
#614 = SURFACE_CURVE('',#615,(#620,#632),.PCURVE_S1.);
#615 = CIRCLE('',#616,4.316451884327);
#616 = AXIS2_PLACEMENT_3D('',#617,#618,#619);
#617 = CARTESIAN_POINT('',(51.864283,116.007127,15.270511));
#618 = DIRECTION('',(-0.,0.,1.));
#619 = DIRECTION('',(0.695691737934,-0.71834045255,0.));
#620 = PCURVE('',#621,#626);
#621 = CYLINDRICAL_SURFACE('',#622,4.316451884327);
#622 = AXIS2_PLACEMENT_3D('',#623,#624,#625);
#623 = CARTESIAN_POINT('',(51.864283,116.007127,15.270511));
#624 = DIRECTION('',(0.,0.,1.));
#625 = DIRECTION('',(1.,0.,0.));
#626 = DEFINITIONAL_REPRESENTATION('',(#627),#631);
#627 = LINE('',#628,#629);
#628 = CARTESIAN_POINT('',(5.481771399417,0.));
#629 = VECTOR('',#630,1.);
#630 = DIRECTION('',(1.,0.));
#631 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#632 = PCURVE('',#633,#638);
#633 = PLANE('',#634);
#634 = AXIS2_PLACEMENT_3D('',#635,#636,#637);
#635 = CARTESIAN_POINT('',(51.864283,116.007127,15.270511));
#636 = DIRECTION('',(0.,0.,1.));
#637 = DIRECTION('',(1.,0.,0.));
#638 = DEFINITIONAL_REPRESENTATION('',(#639),#643);
#639 = CIRCLE('',#640,4.316451884327);
#640 = AXIS2_PLACEMENT_2D('',#641,#642);
#641 = CARTESIAN_POINT('',(0.,0.));
#642 = DIRECTION('',(0.695691737934,-0.71834045255));
#643 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#644 = ORIENTED_EDGE('',*,*,#645,.T.);
#645 = EDGE_CURVE('',#612,#646,#648,.T.);
#646 = VERTEX_POINT('',#647);
#647 = CARTESIAN_POINT('',(51.095012155986,120.25447667223,32.5));
#648 = SURFACE_CURVE('',#649,(#653,#659),.PCURVE_S1.);
#649 = LINE('',#650,#651);
#650 = CARTESIAN_POINT('',(51.095012155986,120.25447667223,15.270511));
#651 = VECTOR('',#652,1.);
#652 = DIRECTION('',(0.,0.,1.));
#653 = PCURVE('',#621,#654);
#654 = DEFINITIONAL_REPRESENTATION('',(#655),#658);
#655 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#656,#657),.UNSPECIFIED.,.F.,.F.,
  (2,2),(0.,17.229489),.PIECEWISE_BEZIER_KNOTS.);
#656 = CARTESIAN_POINT('',(8.033157129826,0.));
#657 = CARTESIAN_POINT('',(8.033157129826,17.229489));
#658 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#659 = PCURVE('',#660,#665);
#660 = PLANE('',#661);
#661 = AXIS2_PLACEMENT_3D('',#662,#663,#664);
#662 = CARTESIAN_POINT('',(51.468253,113.528152,42.5));
#663 = DIRECTION('',(0.998464000178,5.54043351018E-02,0.));
#664 = DIRECTION('',(-5.54043351018E-02,0.998464000178,0.));
#665 = DEFINITIONAL_REPRESENTATION('',(#666),#669);
#666 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#667,#668),.UNSPECIFIED.,.F.,.F.,
  (2,2),(-5.999999999062E-07,17.2294896),.PIECEWISE_BEZIER_KNOTS.);
#667 = CARTESIAN_POINT('',(6.736672199536,-27.2294896));
#668 = CARTESIAN_POINT('',(6.736672199536,-9.9999994));
#669 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#670 = ORIENTED_EDGE('',*,*,#671,.T.);
#671 = EDGE_CURVE('',#646,#672,#674,.T.);
#672 = VERTEX_POINT('',#673);
#673 = CARTESIAN_POINT('',(54.867202913116,112.906445,32.5));
#674 = SURFACE_CURVE('',#675,(#680,#687),.PCURVE_S1.);
#675 = CIRCLE('',#676,4.316451884327);
#676 = AXIS2_PLACEMENT_3D('',#677,#678,#679);
#677 = CARTESIAN_POINT('',(51.864283,116.007127,32.5));
#678 = DIRECTION('',(0.,0.,-1.));
#679 = DIRECTION('',(-0.178218329459,0.983990968985,0.));
#680 = PCURVE('',#621,#681);
#681 = DEFINITIONAL_REPRESENTATION('',(#682),#686);
#682 = LINE('',#683,#684);
#683 = CARTESIAN_POINT('',(8.033157129826,17.229489));
#684 = VECTOR('',#685,1.);
#685 = DIRECTION('',(-1.,-0.));
#686 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#687 = PCURVE('',#688,#693);
#688 = PLANE('',#689);
#689 = AXIS2_PLACEMENT_3D('',#690,#691,#692);
#690 = CARTESIAN_POINT('',(51.864283,116.007127,32.5));
#691 = DIRECTION('',(0.,0.,1.));
#692 = DIRECTION('',(1.,0.,0.));
#693 = DEFINITIONAL_REPRESENTATION('',(#694),#702);
#694 = ( BOUNDED_CURVE() B_SPLINE_CURVE(2,(#695,#696,#697,#698,#699,#700
,#701),.UNSPECIFIED.,.T.,.F.) B_SPLINE_CURVE_WITH_KNOTS((1,2,2,2,2,1),(
    -2.094395102393,0.,2.094395102393,4.188790204786,6.28318530718,
8.377580409573),.UNSPECIFIED.) CURVE() GEOMETRIC_REPRESENTATION_ITEM() 
RATIONAL_B_SPLINE_CURVE((1.,0.5,1.,0.5,1.,0.5,1.)) REPRESENTATION_ITEM(
  '') );
#695 = CARTESIAN_POINT('',(-0.769270844014,4.247349672238));
#696 = CARTESIAN_POINT('',(6.587354585814,5.579765858852));
#697 = CARTESIAN_POINT('',(4.062948136921,-1.457466742812));
#698 = CARTESIAN_POINT('',(1.538541688028,-8.494699344477));
#699 = CARTESIAN_POINT('',(-3.293677292907,-2.789882929426));
#700 = CARTESIAN_POINT('',(-8.125896273842,2.914933485625));
#701 = CARTESIAN_POINT('',(-0.769270844014,4.247349672238));
#702 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#703 = ORIENTED_EDGE('',*,*,#704,.F.);
#704 = EDGE_CURVE('',#610,#672,#705,.T.);
#705 = SURFACE_CURVE('',#706,(#710,#716),.PCURVE_S1.);
#706 = LINE('',#707,#708);
#707 = CARTESIAN_POINT('',(54.867202913116,112.906445,15.270511));
#708 = VECTOR('',#709,1.);
#709 = DIRECTION('',(0.,0.,1.));
#710 = PCURVE('',#621,#711);
#711 = DEFINITIONAL_REPRESENTATION('',(#712),#715);
#712 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#713,#714),.UNSPECIFIED.,.F.,.F.,
  (2,2),(0.,17.229489),.PIECEWISE_BEZIER_KNOTS.);
#713 = CARTESIAN_POINT('',(5.481771399417,0.));
#714 = CARTESIAN_POINT('',(5.481771399417,17.229489));
#715 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#716 = PCURVE('',#717,#722);
#717 = PLANE('',#718);
#718 = AXIS2_PLACEMENT_3D('',#719,#720,#721);
#719 = CARTESIAN_POINT('',(72.917494,112.906445,42.5));
#720 = DIRECTION('',(0.,1.,0.));
#721 = DIRECTION('',(-1.,0.,0.));
#722 = DEFINITIONAL_REPRESENTATION('',(#723),#726);
#723 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#724,#725),.UNSPECIFIED.,.F.,.F.,
  (2,2),(-5.999999999062E-07,17.2294896),.PIECEWISE_BEZIER_KNOTS.);
#724 = CARTESIAN_POINT('',(18.050291086884,-27.2294896));
#725 = CARTESIAN_POINT('',(18.050291086884,-9.9999994));
#726 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#727 = ADVANCED_FACE('',(#728,#797),#633,.F.);
#728 = FACE_BOUND('',#729,.F.);
#729 = EDGE_LOOP('',(#730,#731,#752,#778));
#730 = ORIENTED_EDGE('',*,*,#609,.T.);
#731 = ORIENTED_EDGE('',*,*,#732,.F.);
#732 = EDGE_CURVE('',#733,#612,#735,.T.);
#733 = VERTEX_POINT('',#734);
#734 = CARTESIAN_POINT('',(51.468253,113.528152,15.270511));
#735 = SURFACE_CURVE('',#736,(#740,#746),.PCURVE_S1.);
#736 = LINE('',#737,#738);
#737 = CARTESIAN_POINT('',(51.400293335715,114.75288068102,15.270511));
#738 = VECTOR('',#739,1.);
#739 = DIRECTION('',(-5.54043351018E-02,0.998464000178,0.));
#740 = PCURVE('',#633,#741);
#741 = DEFINITIONAL_REPRESENTATION('',(#742),#745);
#742 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#743,#744),.UNSPECIFIED.,.F.,.F.,
  (2,2),(-1.226612758006,5.579267957888),.PIECEWISE_BEZIER_KNOTS.);
#743 = CARTESIAN_POINT('',(-0.39603,-2.478975));
#744 = CARTESIAN_POINT('',(-0.773105295846,4.316451884327));
#745 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#746 = PCURVE('',#660,#747);
#747 = DEFINITIONAL_REPRESENTATION('',(#748),#751);
#748 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#749,#750),.UNSPECIFIED.,.F.,.F.,
  (2,2),(-1.226612758006,5.579267957888),.PIECEWISE_BEZIER_KNOTS.);
#749 = CARTESIAN_POINT('',(-1.42108547152E-14,-27.229489));
#750 = CARTESIAN_POINT('',(6.805880715894,-27.229489));
#751 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#752 = ORIENTED_EDGE('',*,*,#753,.F.);
#753 = EDGE_CURVE('',#754,#733,#756,.T.);
#754 = VERTEX_POINT('',#755);
#755 = CARTESIAN_POINT('',(51.710833,112.906445,15.270511));
#756 = SURFACE_CURVE('',#757,(#761,#767),.PCURVE_S1.);
#757 = LINE('',#758,#759);
#758 = CARTESIAN_POINT('',(51.195979046776,114.22596147579,15.270511));
#759 = VECTOR('',#760,1.);
#760 = DIRECTION('',(-0.363493894237,0.931596580528,0.));
#761 = PCURVE('',#633,#762);
#762 = DEFINITIONAL_REPRESENTATION('',(#763),#766);
#763 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#764,#765),.UNSPECIFIED.,.F.,.F.,
  (2,2),(-1.416403305218,-0.749046841063),.PIECEWISE_BEZIER_KNOTS.);
#764 = CARTESIAN_POINT('',(-0.15345,-3.100682));
#765 = CARTESIAN_POINT('',(-0.39603,-2.478975));
#766 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#767 = PCURVE('',#768,#773);
#768 = PLANE('',#769);
#769 = AXIS2_PLACEMENT_3D('',#770,#771,#772);
#770 = CARTESIAN_POINT('',(51.710833,112.906445,42.5));
#771 = DIRECTION('',(0.931596580528,0.363493894237,0.));
#772 = DIRECTION('',(-0.363493894237,0.931596580528,0.));
#773 = DEFINITIONAL_REPRESENTATION('',(#774),#777);
#774 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#775,#776),.UNSPECIFIED.,.F.,.F.,
  (2,2),(-1.416403305218,-0.749046841063),.PIECEWISE_BEZIER_KNOTS.);
#775 = CARTESIAN_POINT('',(0.,-27.229489));
#776 = CARTESIAN_POINT('',(0.667356464155,-27.229489));
#777 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#778 = ORIENTED_EDGE('',*,*,#779,.F.);
#779 = EDGE_CURVE('',#610,#754,#780,.T.);
#780 = SURFACE_CURVE('',#781,(#785,#791),.PCURVE_S1.);
#781 = LINE('',#782,#783);
#782 = CARTESIAN_POINT('',(62.3908885,112.906445,15.270511));
#783 = VECTOR('',#784,1.);
#784 = DIRECTION('',(-1.,0.,0.));
#785 = PCURVE('',#633,#786);
#786 = DEFINITIONAL_REPRESENTATION('',(#787),#790);
#787 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#788,#789),.UNSPECIFIED.,.F.,.F.,
  (2,2),(6.210153615673,10.6800555),.PIECEWISE_BEZIER_KNOTS.);
#788 = CARTESIAN_POINT('',(4.316451884327,-3.100682));
#789 = CARTESIAN_POINT('',(-0.15345,-3.100682));
#790 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#791 = PCURVE('',#717,#792);
#792 = DEFINITIONAL_REPRESENTATION('',(#793),#796);
#793 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#794,#795),.UNSPECIFIED.,.F.,.F.,
  (2,2),(6.210153615673,10.6800555),.PIECEWISE_BEZIER_KNOTS.);
#794 = CARTESIAN_POINT('',(16.736759115673,-27.229489));
#795 = CARTESIAN_POINT('',(21.206661,-27.229489));
#796 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#797 = FACE_BOUND('',#798,.F.);
#798 = EDGE_LOOP('',(#799));
#799 = ORIENTED_EDGE('',*,*,#800,.F.);
#800 = EDGE_CURVE('',#801,#801,#803,.T.);
#801 = VERTEX_POINT('',#802);
#802 = CARTESIAN_POINT('',(52.139283,116.007127,15.270511));
#803 = SURFACE_CURVE('',#804,(#809,#816),.PCURVE_S1.);
#804 = CIRCLE('',#805,0.275);
#805 = AXIS2_PLACEMENT_3D('',#806,#807,#808);
#806 = CARTESIAN_POINT('',(51.864283,116.007127,15.270511));
#807 = DIRECTION('',(0.,0.,1.));
#808 = DIRECTION('',(1.,0.,0.));
#809 = PCURVE('',#633,#810);
#810 = DEFINITIONAL_REPRESENTATION('',(#811),#815);
#811 = CIRCLE('',#812,0.275);
#812 = AXIS2_PLACEMENT_2D('',#813,#814);
#813 = CARTESIAN_POINT('',(0.,0.));
#814 = DIRECTION('',(1.,0.));
#815 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#816 = PCURVE('',#817,#822);
#817 = CYLINDRICAL_SURFACE('',#818,0.275);
#818 = AXIS2_PLACEMENT_3D('',#819,#820,#821);
#819 = CARTESIAN_POINT('',(51.864283,116.007127,15.270511));
#820 = DIRECTION('',(0.,0.,1.));
#821 = DIRECTION('',(1.,0.,0.));
#822 = DEFINITIONAL_REPRESENTATION('',(#823),#827);
#823 = LINE('',#824,#825);
#824 = CARTESIAN_POINT('',(0.,0.));
#825 = VECTOR('',#826,1.);
#826 = DIRECTION('',(1.,0.));
#827 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#828 = ADVANCED_FACE('',(#829),#717,.F.);
#829 = FACE_BOUND('',#830,.F.);
#830 = EDGE_LOOP('',(#831,#854,#873,#874));
#831 = ORIENTED_EDGE('',*,*,#832,.F.);
#832 = EDGE_CURVE('',#833,#754,#835,.T.);
#833 = VERTEX_POINT('',#834);
#834 = CARTESIAN_POINT('',(51.710833,112.906445,32.5));
#835 = SURFACE_CURVE('',#836,(#840,#847),.PCURVE_S1.);
#836 = LINE('',#837,#838);
#837 = CARTESIAN_POINT('',(51.710833,112.906445,42.5));
#838 = VECTOR('',#839,1.);
#839 = DIRECTION('',(0.,0.,-1.));
#840 = PCURVE('',#717,#841);
#841 = DEFINITIONAL_REPRESENTATION('',(#842),#846);
#842 = LINE('',#843,#844);
#843 = CARTESIAN_POINT('',(21.206661,0.));
#844 = VECTOR('',#845,1.);
#845 = DIRECTION('',(0.,-1.));
#846 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#847 = PCURVE('',#768,#848);
#848 = DEFINITIONAL_REPRESENTATION('',(#849),#853);
#849 = LINE('',#850,#851);
#850 = CARTESIAN_POINT('',(0.,0.));
#851 = VECTOR('',#852,1.);
#852 = DIRECTION('',(0.,-1.));
#853 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#854 = ORIENTED_EDGE('',*,*,#855,.F.);
#855 = EDGE_CURVE('',#672,#833,#856,.T.);
#856 = SURFACE_CURVE('',#857,(#861,#867),.PCURVE_S1.);
#857 = LINE('',#858,#859);
#858 = CARTESIAN_POINT('',(62.3908885,112.906445,32.5));
#859 = VECTOR('',#860,1.);
#860 = DIRECTION('',(-1.,0.,0.));
#861 = PCURVE('',#717,#862);
#862 = DEFINITIONAL_REPRESENTATION('',(#863),#866);
#863 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#864,#865),.UNSPECIFIED.,.F.,.F.,
  (2,2),(6.210153615673,10.6800555),.PIECEWISE_BEZIER_KNOTS.);
#864 = CARTESIAN_POINT('',(16.736759115673,-10.));
#865 = CARTESIAN_POINT('',(21.206661,-10.));
#866 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#867 = PCURVE('',#688,#868);
#868 = DEFINITIONAL_REPRESENTATION('',(#869),#872);
#869 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#870,#871),.UNSPECIFIED.,.F.,.F.,
  (2,2),(6.210153615673,10.6800555),.PIECEWISE_BEZIER_KNOTS.);
#870 = CARTESIAN_POINT('',(4.316451884327,-3.100682));
#871 = CARTESIAN_POINT('',(-0.15345,-3.100682));
#872 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#873 = ORIENTED_EDGE('',*,*,#704,.F.);
#874 = ORIENTED_EDGE('',*,*,#779,.T.);
#875 = ADVANCED_FACE('',(#876,#920),#688,.T.);
#876 = FACE_BOUND('',#877,.T.);
#877 = EDGE_LOOP('',(#878,#879,#880,#901));
#878 = ORIENTED_EDGE('',*,*,#855,.F.);
#879 = ORIENTED_EDGE('',*,*,#671,.F.);
#880 = ORIENTED_EDGE('',*,*,#881,.F.);
#881 = EDGE_CURVE('',#882,#646,#884,.T.);
#882 = VERTEX_POINT('',#883);
#883 = CARTESIAN_POINT('',(51.468253,113.528152,32.5));
#884 = SURFACE_CURVE('',#885,(#889,#895),.PCURVE_S1.);
#885 = LINE('',#886,#887);
#886 = CARTESIAN_POINT('',(51.400293335715,114.75288068102,32.5));
#887 = VECTOR('',#888,1.);
#888 = DIRECTION('',(-5.54043351018E-02,0.998464000178,0.));
#889 = PCURVE('',#688,#890);
#890 = DEFINITIONAL_REPRESENTATION('',(#891),#894);
#891 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#892,#893),.UNSPECIFIED.,.F.,.F.,
  (2,2),(-1.226612758006,5.579267957888),.PIECEWISE_BEZIER_KNOTS.);
#892 = CARTESIAN_POINT('',(-0.39603,-2.478975));
#893 = CARTESIAN_POINT('',(-0.773105295846,4.316451884327));
#894 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#895 = PCURVE('',#660,#896);
#896 = DEFINITIONAL_REPRESENTATION('',(#897),#900);
#897 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#898,#899),.UNSPECIFIED.,.F.,.F.,
  (2,2),(-1.226612758006,5.579267957888),.PIECEWISE_BEZIER_KNOTS.);
#898 = CARTESIAN_POINT('',(-1.42108547152E-14,-10.));
#899 = CARTESIAN_POINT('',(6.805880715894,-10.));
#900 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#901 = ORIENTED_EDGE('',*,*,#902,.F.);
#902 = EDGE_CURVE('',#833,#882,#903,.T.);
#903 = SURFACE_CURVE('',#904,(#908,#914),.PCURVE_S1.);
#904 = LINE('',#905,#906);
#905 = CARTESIAN_POINT('',(51.195979046776,114.22596147579,32.5));
#906 = VECTOR('',#907,1.);
#907 = DIRECTION('',(-0.363493894237,0.931596580528,0.));
#908 = PCURVE('',#688,#909);
#909 = DEFINITIONAL_REPRESENTATION('',(#910),#913);
#910 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#911,#912),.UNSPECIFIED.,.F.,.F.,
  (2,2),(-1.416403305218,-0.749046841063),.PIECEWISE_BEZIER_KNOTS.);
#911 = CARTESIAN_POINT('',(-0.15345,-3.100682));
#912 = CARTESIAN_POINT('',(-0.39603,-2.478975));
#913 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#914 = PCURVE('',#768,#915);
#915 = DEFINITIONAL_REPRESENTATION('',(#916),#919);
#916 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#917,#918),.UNSPECIFIED.,.F.,.F.,
  (2,2),(-1.416403305218,-0.749046841063),.PIECEWISE_BEZIER_KNOTS.);
#917 = CARTESIAN_POINT('',(0.,-10.));
#918 = CARTESIAN_POINT('',(0.667356464155,-10.));
#919 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#920 = FACE_BOUND('',#921,.T.);
#921 = EDGE_LOOP('',(#922));
#922 = ORIENTED_EDGE('',*,*,#923,.F.);
#923 = EDGE_CURVE('',#924,#924,#926,.T.);
#924 = VERTEX_POINT('',#925);
#925 = CARTESIAN_POINT('',(52.139283,116.007127,32.5));
#926 = SURFACE_CURVE('',#927,(#932,#939),.PCURVE_S1.);
#927 = CIRCLE('',#928,0.275);
#928 = AXIS2_PLACEMENT_3D('',#929,#930,#931);
#929 = CARTESIAN_POINT('',(51.864283,116.007127,32.5));
#930 = DIRECTION('',(0.,0.,1.));
#931 = DIRECTION('',(1.,0.,0.));
#932 = PCURVE('',#688,#933);
#933 = DEFINITIONAL_REPRESENTATION('',(#934),#938);
#934 = CIRCLE('',#935,0.275);
#935 = AXIS2_PLACEMENT_2D('',#936,#937);
#936 = CARTESIAN_POINT('',(0.,0.));
#937 = DIRECTION('',(1.,0.));
#938 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#939 = PCURVE('',#817,#940);
#940 = DEFINITIONAL_REPRESENTATION('',(#941),#945);
#941 = LINE('',#942,#943);
#942 = CARTESIAN_POINT('',(0.,17.229489));
#943 = VECTOR('',#944,1.);
#944 = DIRECTION('',(1.,0.));
#945 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#946 = ADVANCED_FACE('',(#947),#660,.F.);
#947 = FACE_BOUND('',#948,.F.);
#948 = EDGE_LOOP('',(#949,#950,#971,#972));
#949 = ORIENTED_EDGE('',*,*,#881,.F.);
#950 = ORIENTED_EDGE('',*,*,#951,.T.);
#951 = EDGE_CURVE('',#882,#733,#952,.T.);
#952 = SURFACE_CURVE('',#953,(#957,#964),.PCURVE_S1.);
#953 = LINE('',#954,#955);
#954 = CARTESIAN_POINT('',(51.468253,113.528152,42.5));
#955 = VECTOR('',#956,1.);
#956 = DIRECTION('',(0.,0.,-1.));
#957 = PCURVE('',#660,#958);
#958 = DEFINITIONAL_REPRESENTATION('',(#959),#963);
#959 = LINE('',#960,#961);
#960 = CARTESIAN_POINT('',(0.,0.));
#961 = VECTOR('',#962,1.);
#962 = DIRECTION('',(0.,-1.));
#963 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#964 = PCURVE('',#768,#965);
#965 = DEFINITIONAL_REPRESENTATION('',(#966),#970);
#966 = LINE('',#967,#968);
#967 = CARTESIAN_POINT('',(0.667356464155,0.));
#968 = VECTOR('',#969,1.);
#969 = DIRECTION('',(0.,-1.));
#970 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#971 = ORIENTED_EDGE('',*,*,#732,.T.);
#972 = ORIENTED_EDGE('',*,*,#645,.T.);
#973 = ADVANCED_FACE('',(#974),#768,.F.);
#974 = FACE_BOUND('',#975,.F.);
#975 = EDGE_LOOP('',(#976,#977,#978,#979));
#976 = ORIENTED_EDGE('',*,*,#902,.F.);
#977 = ORIENTED_EDGE('',*,*,#832,.T.);
#978 = ORIENTED_EDGE('',*,*,#753,.T.);
#979 = ORIENTED_EDGE('',*,*,#951,.F.);
#980 = ADVANCED_FACE('',(#981),#817,.F.);
#981 = FACE_BOUND('',#982,.F.);
#982 = EDGE_LOOP('',(#983,#984,#1005,#1006));
#983 = ORIENTED_EDGE('',*,*,#923,.F.);
#984 = ORIENTED_EDGE('',*,*,#985,.F.);
#985 = EDGE_CURVE('',#801,#924,#986,.T.);
#986 = SEAM_CURVE('',#987,(#991,#998),.PCURVE_S1.);
#987 = LINE('',#988,#989);
#988 = CARTESIAN_POINT('',(52.139283,116.007127,15.270511));
#989 = VECTOR('',#990,1.);
#990 = DIRECTION('',(0.,0.,1.));
#991 = PCURVE('',#817,#992);
#992 = DEFINITIONAL_REPRESENTATION('',(#993),#997);
#993 = LINE('',#994,#995);
#994 = CARTESIAN_POINT('',(6.28318530718,-0.));
#995 = VECTOR('',#996,1.);
#996 = DIRECTION('',(0.,1.));
#997 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#998 = PCURVE('',#817,#999);
#999 = DEFINITIONAL_REPRESENTATION('',(#1000),#1004);
#1000 = LINE('',#1001,#1002);
#1001 = CARTESIAN_POINT('',(0.,-0.));
#1002 = VECTOR('',#1003,1.);
#1003 = DIRECTION('',(0.,1.));
#1004 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1005 = ORIENTED_EDGE('',*,*,#800,.T.);
#1006 = ORIENTED_EDGE('',*,*,#985,.T.);
#1007 = ( GEOMETRIC_REPRESENTATION_CONTEXT(3) 
GLOBAL_UNCERTAINTY_ASSIGNED_CONTEXT((#1011)) 
GLOBAL_UNIT_ASSIGNED_CONTEXT((#1008,#1009,#1010)) REPRESENTATION_CONTEXT
('Context #1','3D Context with UNIT and UNCERTAINTY') );
#1008 = ( LENGTH_UNIT() NAMED_UNIT(*) SI_UNIT(.MILLI.,.METRE.) );
#1009 = ( NAMED_UNIT(*) PLANE_ANGLE_UNIT() SI_UNIT($,.RADIAN.) );
#1010 = ( NAMED_UNIT(*) SI_UNIT($,.STERADIAN.) SOLID_ANGLE_UNIT() );
#1011 = UNCERTAINTY_MEASURE_WITH_UNIT(LENGTH_MEASURE(1.E-07),#1008,
  'distance_accuracy_value','confusion accuracy');
#1012 = CONTEXT_DEPENDENT_SHAPE_REPRESENTATION(#1013,#1015);
#1013 = ( REPRESENTATION_RELATIONSHIP('','',#602,#10) 
REPRESENTATION_RELATIONSHIP_WITH_TRANSFORMATION(#1014) 
SHAPE_REPRESENTATION_RELATIONSHIP() );
#1014 = ITEM_DEFINED_TRANSFORMATION('','',#11,#23);
#1015 = PRODUCT_DEFINITION_SHAPE('Placement','Placement of an item',
  #1016);
#1016 = NEXT_ASSEMBLY_USAGE_OCCURRENCE('116','=>[0:1:1:4]','',#5,#597,$
  );
#1017 = PRODUCT_RELATED_PRODUCT_CATEGORY('part',$,(#599));
#1018 = MECHANICAL_DESIGN_GEOMETRIC_PRESENTATION_REPRESENTATION('',(
    #1019),#1007);
#1019 = STYLED_ITEM('color',(#1020),#603);
#1020 = PRESENTATION_STYLE_ASSIGNMENT((#1021));
#1021 = SURFACE_STYLE_USAGE(.BOTH.,#1022);
#1022 = SURFACE_SIDE_STYLE('',(#1023));
#1023 = SURFACE_STYLE_FILL_AREA(#1024);
#1024 = FILL_AREA_STYLE('',(#1025));
#1025 = FILL_AREA_STYLE_COLOUR('',#1026);
#1026 = DRAUGHTING_PRE_DEFINED_COLOUR('red');
#1027 = MECHANICAL_DESIGN_GEOMETRIC_PRESENTATION_REPRESENTATION('',(
    #1028),#584);
#1028 = STYLED_ITEM('color',(#1029),#158);
#1029 = PRESENTATION_STYLE_ASSIGNMENT((#1030));
#1030 = SURFACE_STYLE_USAGE(.BOTH.,#1031);
#1031 = SURFACE_SIDE_STYLE('',(#1032));
#1032 = SURFACE_STYLE_FILL_AREA(#1033);
#1033 = FILL_AREA_STYLE('',(#1034));
#1034 = FILL_AREA_STYLE_COLOUR('',#1035);
#1035 = DRAUGHTING_PRE_DEFINED_COLOUR('green');
#1036 = MECHANICAL_DESIGN_GEOMETRIC_PRESENTATION_REPRESENTATION('',(
    #1037),#139);
#1037 = STYLED_ITEM('color',(#1038),#41);
#1038 = PRESENTATION_STYLE_ASSIGNMENT((#1039));
#1039 = SURFACE_STYLE_USAGE(.BOTH.,#1040);
#1040 = SURFACE_SIDE_STYLE('',(#1041));
#1041 = SURFACE_STYLE_FILL_AREA(#1042);
#1042 = FILL_AREA_STYLE('',(#1043));
#1043 = FILL_AREA_STYLE_COLOUR('',#1044);
#1044 = DRAUGHTING_PRE_DEFINED_COLOUR('blue');
ENDSEC;
END-ISO-10303-21;
