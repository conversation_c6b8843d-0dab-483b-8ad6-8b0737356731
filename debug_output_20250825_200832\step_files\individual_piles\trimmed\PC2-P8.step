ISO-10303-21;
HEADER;
FILE_DESCRIPTION(('Open CASCADE Model'),'2;1');
FILE_NAME('Pile_PC2-P8','2025-08-25T20:08:32',('Author'),('Open CASCADE'
    ),'Open CASCADE STEP processor 7.8','build123d','Unknown');
FILE_SCHEMA(('AUTOMOTIVE_DESIGN { 1 0 10303 214 1 1 1 1 }'));
ENDSEC;
DATA;
#1 = APPLICATION_PROTOCOL_DEFINITION('international standard',
  'automotive_design',2000,#2);
#2 = APPLICATION_CONTEXT(
  'core data for automotive mechanical design processes');
#3 = SHAPE_DEFINITION_REPRESENTATION(#4,#10);
#4 = PRODUCT_DEFINITION_SHAPE('','',#5);
#5 = PRODUCT_DEFINITION('design','',#6,#9);
#6 = PRODUCT_DEFINITION_FORMATION('','',#7);
#7 = PRODUCT('Pile_PC2-P8','Pile_PC2-P8','',(#8));
#8 = PRODUCT_CONTEXT('',#2,'mechanical');
#9 = PRODUCT_DEFINITION_CONTEXT('part definition',#2,'design');
#10 = SHAPE_REPRESENTATION('',(#11,#15,#19,#23),#27);
#11 = AXIS2_PLACEMENT_3D('',#12,#13,#14);
#12 = CARTESIAN_POINT('',(0.,0.,0.));
#13 = DIRECTION('',(0.,0.,1.));
#14 = DIRECTION('',(1.,0.,-0.));
#15 = AXIS2_PLACEMENT_3D('',#16,#17,#18);
#16 = CARTESIAN_POINT('',(54.696931,115.506924,7.75987));
#17 = DIRECTION('',(0.,0.,1.));
#18 = DIRECTION('',(1.,0.,0.));
#19 = AXIS2_PLACEMENT_3D('',#20,#21,#22);
#20 = CARTESIAN_POINT('',(0.,0.,0.));
#21 = DIRECTION('',(0.,0.,1.));
#22 = DIRECTION('',(1.,0.,-0.));
#23 = AXIS2_PLACEMENT_3D('',#24,#25,#26);
#24 = CARTESIAN_POINT('',(0.,0.,0.));
#25 = DIRECTION('',(0.,0.,1.));
#26 = DIRECTION('',(1.,0.,-0.));
#27 = ( GEOMETRIC_REPRESENTATION_CONTEXT(3) 
GLOBAL_UNCERTAINTY_ASSIGNED_CONTEXT((#31)) GLOBAL_UNIT_ASSIGNED_CONTEXT(
(#28,#29,#30)) REPRESENTATION_CONTEXT('Context #1',
  '3D Context with UNIT and UNCERTAINTY') );
#28 = ( LENGTH_UNIT() NAMED_UNIT(*) SI_UNIT(.MILLI.,.METRE.) );
#29 = ( NAMED_UNIT(*) PLANE_ANGLE_UNIT() SI_UNIT($,.RADIAN.) );
#30 = ( NAMED_UNIT(*) SI_UNIT($,.STERADIAN.) SOLID_ANGLE_UNIT() );
#31 = UNCERTAINTY_MEASURE_WITH_UNIT(LENGTH_MEASURE(2.E-07),#28,
  'distance_accuracy_value','confusion accuracy');
#32 = PRODUCT_RELATED_PRODUCT_CATEGORY('part',$,(#7));
#33 = SHAPE_DEFINITION_REPRESENTATION(#34,#40);
#34 = PRODUCT_DEFINITION_SHAPE('','',#35);
#35 = PRODUCT_DEFINITION('design','',#36,#39);
#36 = PRODUCT_DEFINITION_FORMATION('','',#37);
#37 = PRODUCT('PC2-P8_Part1','PC2-P8_Part1','',(#38));
#38 = PRODUCT_CONTEXT('',#2,'mechanical');
#39 = PRODUCT_DEFINITION_CONTEXT('part definition',#2,'design');
#40 = ADVANCED_BREP_SHAPE_REPRESENTATION('',(#11,#41),#139);
#41 = MANIFOLD_SOLID_BREP('',#42);
#42 = CLOSED_SHELL('',(#43,#131,#135));
#43 = ADVANCED_FACE('',(#44),#57,.T.);
#44 = FACE_BOUND('',#45,.T.);
#45 = EDGE_LOOP('',(#46,#80,#103,#130));
#46 = ORIENTED_EDGE('',*,*,#47,.F.);
#47 = EDGE_CURVE('',#48,#48,#50,.T.);
#48 = VERTEX_POINT('',#49);
#49 = CARTESIAN_POINT('',(0.275,-6.73555739531E-17,24.74013));
#50 = SURFACE_CURVE('',#51,(#56,#68),.PCURVE_S1.);
#51 = CIRCLE('',#52,0.275);
#52 = AXIS2_PLACEMENT_3D('',#53,#54,#55);
#53 = CARTESIAN_POINT('',(0.,0.,24.74013));
#54 = DIRECTION('',(0.,0.,1.));
#55 = DIRECTION('',(1.,0.,-0.));
#56 = PCURVE('',#57,#62);
#57 = CYLINDRICAL_SURFACE('',#58,0.275);
#58 = AXIS2_PLACEMENT_3D('',#59,#60,#61);
#59 = CARTESIAN_POINT('',(0.,0.,0.));
#60 = DIRECTION('',(0.,0.,1.));
#61 = DIRECTION('',(1.,0.,-0.));
#62 = DEFINITIONAL_REPRESENTATION('',(#63),#67);
#63 = LINE('',#64,#65);
#64 = CARTESIAN_POINT('',(0.,24.74013));
#65 = VECTOR('',#66,1.);
#66 = DIRECTION('',(1.,0.));
#67 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#68 = PCURVE('',#69,#74);
#69 = PLANE('',#70);
#70 = AXIS2_PLACEMENT_3D('',#71,#72,#73);
#71 = CARTESIAN_POINT('',(0.,0.,24.74013));
#72 = DIRECTION('',(0.,0.,1.));
#73 = DIRECTION('',(1.,0.,-0.));
#74 = DEFINITIONAL_REPRESENTATION('',(#75),#79);
#75 = CIRCLE('',#76,0.275);
#76 = AXIS2_PLACEMENT_2D('',#77,#78);
#77 = CARTESIAN_POINT('',(0.,0.));
#78 = DIRECTION('',(1.,0.));
#79 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#80 = ORIENTED_EDGE('',*,*,#81,.F.);
#81 = EDGE_CURVE('',#82,#48,#84,.T.);
#82 = VERTEX_POINT('',#83);
#83 = CARTESIAN_POINT('',(0.275,-6.73555739531E-17,0.));
#84 = SEAM_CURVE('',#85,(#89,#96),.PCURVE_S1.);
#85 = LINE('',#86,#87);
#86 = CARTESIAN_POINT('',(0.275,-6.73555739531E-17,0.));
#87 = VECTOR('',#88,1.);
#88 = DIRECTION('',(0.,0.,1.));
#89 = PCURVE('',#57,#90);
#90 = DEFINITIONAL_REPRESENTATION('',(#91),#95);
#91 = LINE('',#92,#93);
#92 = CARTESIAN_POINT('',(6.28318530718,-0.));
#93 = VECTOR('',#94,1.);
#94 = DIRECTION('',(0.,1.));
#95 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#96 = PCURVE('',#57,#97);
#97 = DEFINITIONAL_REPRESENTATION('',(#98),#102);
#98 = LINE('',#99,#100);
#99 = CARTESIAN_POINT('',(0.,-0.));
#100 = VECTOR('',#101,1.);
#101 = DIRECTION('',(0.,1.));
#102 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#103 = ORIENTED_EDGE('',*,*,#104,.T.);
#104 = EDGE_CURVE('',#82,#82,#105,.T.);
#105 = SURFACE_CURVE('',#106,(#111,#118),.PCURVE_S1.);
#106 = CIRCLE('',#107,0.275);
#107 = AXIS2_PLACEMENT_3D('',#108,#109,#110);
#108 = CARTESIAN_POINT('',(0.,0.,0.));
#109 = DIRECTION('',(0.,0.,1.));
#110 = DIRECTION('',(1.,0.,-0.));
#111 = PCURVE('',#57,#112);
#112 = DEFINITIONAL_REPRESENTATION('',(#113),#117);
#113 = LINE('',#114,#115);
#114 = CARTESIAN_POINT('',(0.,0.));
#115 = VECTOR('',#116,1.);
#116 = DIRECTION('',(1.,0.));
#117 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#118 = PCURVE('',#119,#124);
#119 = PLANE('',#120);
#120 = AXIS2_PLACEMENT_3D('',#121,#122,#123);
#121 = CARTESIAN_POINT('',(0.,0.,0.));
#122 = DIRECTION('',(0.,0.,1.));
#123 = DIRECTION('',(1.,0.,-0.));
#124 = DEFINITIONAL_REPRESENTATION('',(#125),#129);
#125 = CIRCLE('',#126,0.275);
#126 = AXIS2_PLACEMENT_2D('',#127,#128);
#127 = CARTESIAN_POINT('',(0.,0.));
#128 = DIRECTION('',(1.,0.));
#129 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#130 = ORIENTED_EDGE('',*,*,#81,.T.);
#131 = ADVANCED_FACE('',(#132),#69,.T.);
#132 = FACE_BOUND('',#133,.T.);
#133 = EDGE_LOOP('',(#134));
#134 = ORIENTED_EDGE('',*,*,#47,.T.);
#135 = ADVANCED_FACE('',(#136),#119,.F.);
#136 = FACE_BOUND('',#137,.T.);
#137 = EDGE_LOOP('',(#138));
#138 = ORIENTED_EDGE('',*,*,#104,.F.);
#139 = ( GEOMETRIC_REPRESENTATION_CONTEXT(3) 
GLOBAL_UNCERTAINTY_ASSIGNED_CONTEXT((#143)) GLOBAL_UNIT_ASSIGNED_CONTEXT
((#140,#141,#142)) REPRESENTATION_CONTEXT('Context #1',
  '3D Context with UNIT and UNCERTAINTY') );
#140 = ( LENGTH_UNIT() NAMED_UNIT(*) SI_UNIT(.MILLI.,.METRE.) );
#141 = ( NAMED_UNIT(*) PLANE_ANGLE_UNIT() SI_UNIT($,.RADIAN.) );
#142 = ( NAMED_UNIT(*) SI_UNIT($,.STERADIAN.) SOLID_ANGLE_UNIT() );
#143 = UNCERTAINTY_MEASURE_WITH_UNIT(LENGTH_MEASURE(1.E-07),#140,
  'distance_accuracy_value','confusion accuracy');
#144 = CONTEXT_DEPENDENT_SHAPE_REPRESENTATION(#145,#147);
#145 = ( REPRESENTATION_RELATIONSHIP('','',#40,#10) 
REPRESENTATION_RELATIONSHIP_WITH_TRANSFORMATION(#146) 
SHAPE_REPRESENTATION_RELATIONSHIP() );
#146 = ITEM_DEFINED_TRANSFORMATION('','',#11,#15);
#147 = PRODUCT_DEFINITION_SHAPE('Placement','Placement of an item',#148
  );
#148 = NEXT_ASSEMBLY_USAGE_OCCURRENCE('126','=>[0:1:1:2]','',#5,#35,$);
#149 = PRODUCT_RELATED_PRODUCT_CATEGORY('part',$,(#37));
#150 = SHAPE_DEFINITION_REPRESENTATION(#151,#157);
#151 = PRODUCT_DEFINITION_SHAPE('','',#152);
#152 = PRODUCT_DEFINITION('design','',#153,#156);
#153 = PRODUCT_DEFINITION_FORMATION('','',#154);
#154 = PRODUCT('PC2-P8_Part2','PC2-P8_Part2','',(#155));
#155 = PRODUCT_CONTEXT('',#2,'mechanical');
#156 = PRODUCT_DEFINITION_CONTEXT('part definition',#2,'design');
#157 = ADVANCED_BREP_SHAPE_REPRESENTATION('',(#11,#158),#561);
#158 = MANIFOLD_SOLID_BREP('',#159);
#159 = CLOSED_SHELL('',(#160,#383,#433,#502,#528,#555));
#160 = ADVANCED_FACE('',(#161),#175,.T.);
#161 = FACE_BOUND('',#162,.T.);
#162 = EDGE_LOOP('',(#163,#193,#222,#264,#296,#334,#356,#357));
#163 = ORIENTED_EDGE('',*,*,#164,.T.);
#164 = EDGE_CURVE('',#165,#167,#169,.T.);
#165 = VERTEX_POINT('',#166);
#166 = CARTESIAN_POINT('',(54.971931,115.506924,7.75987));
#167 = VERTEX_POINT('',#168);
#168 = CARTESIAN_POINT('',(59.013382884327,115.506924,14.75987));
#169 = SEAM_CURVE('',#170,(#174,#186),.PCURVE_S1.);
#170 = LINE('',#171,#172);
#171 = CARTESIAN_POINT('',(54.971931,115.506924,7.75987));
#172 = VECTOR('',#173,1.);
#173 = DIRECTION('',(0.5,-1.224646799147E-16,0.866025403784));
#174 = PCURVE('',#175,#180);
#175 = CONICAL_SURFACE('',#176,0.275,0.523598775598);
#176 = AXIS2_PLACEMENT_3D('',#177,#178,#179);
#177 = CARTESIAN_POINT('',(54.696931,115.506924,7.75987));
#178 = DIRECTION('',(0.,0.,1.));
#179 = DIRECTION('',(1.,0.,0.));
#180 = DEFINITIONAL_REPRESENTATION('',(#181),#185);
#181 = LINE('',#182,#183);
#182 = CARTESIAN_POINT('',(6.28318530718,-0.));
#183 = VECTOR('',#184,1.);
#184 = DIRECTION('',(0.,1.));
#185 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#186 = PCURVE('',#175,#187);
#187 = DEFINITIONAL_REPRESENTATION('',(#188),#192);
#188 = LINE('',#189,#190);
#189 = CARTESIAN_POINT('',(0.,-0.));
#190 = VECTOR('',#191,1.);
#191 = DIRECTION('',(0.,1.));
#192 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#193 = ORIENTED_EDGE('',*,*,#194,.F.);
#194 = EDGE_CURVE('',#195,#167,#197,.T.);
#195 = VERTEX_POINT('',#196);
#196 = CARTESIAN_POINT('',(58.142111088221,112.906445,14.75987));
#197 = SURFACE_CURVE('',#198,(#203,#210),.PCURVE_S1.);
#198 = CIRCLE('',#199,4.316451884327);
#199 = AXIS2_PLACEMENT_3D('',#200,#201,#202);
#200 = CARTESIAN_POINT('',(54.696931,115.506924,14.75987));
#201 = DIRECTION('',(0.,0.,1.));
#202 = DIRECTION('',(1.,0.,0.));
#203 = PCURVE('',#175,#204);
#204 = DEFINITIONAL_REPRESENTATION('',(#205),#209);
#205 = LINE('',#206,#207);
#206 = CARTESIAN_POINT('',(0.,7.));
#207 = VECTOR('',#208,1.);
#208 = DIRECTION('',(1.,0.));
#209 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#210 = PCURVE('',#211,#216);
#211 = PLANE('',#212);
#212 = AXIS2_PLACEMENT_3D('',#213,#214,#215);
#213 = CARTESIAN_POINT('',(54.696931,115.506924,14.75987));
#214 = DIRECTION('',(0.,0.,1.));
#215 = DIRECTION('',(1.,0.,0.));
#216 = DEFINITIONAL_REPRESENTATION('',(#217),#221);
#217 = CIRCLE('',#218,4.316451884327);
#218 = AXIS2_PLACEMENT_2D('',#219,#220);
#219 = CARTESIAN_POINT('',(0.,0.));
#220 = DIRECTION('',(1.,0.));
#221 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#222 = ORIENTED_EDGE('',*,*,#223,.F.);
#223 = EDGE_CURVE('',#224,#195,#226,.T.);
#224 = VERTEX_POINT('',#225);
#225 = CARTESIAN_POINT('',(51.710833,112.906445,14.141968153559));
#226 = SURFACE_CURVE('',#227,(#232,#252),.PCURVE_S1.);
#227 = HYPERBOLA('',#228,4.504161752016,2.600479);
#228 = AXIS2_PLACEMENT_3D('',#229,#230,#231);
#229 = CARTESIAN_POINT('',(54.696931,112.906445,7.283556027919));
#230 = DIRECTION('',(0.,1.,0.));
#231 = DIRECTION('',(0.,-0.,1.));
#232 = PCURVE('',#175,#233);
#233 = DEFINITIONAL_REPRESENTATION('',(#234),#251);
#234 = B_SPLINE_CURVE_WITH_KNOTS('',8,(#235,#236,#237,#238,#239,#240,
    #241,#242,#243,#244,#245,#246,#247,#248,#249,#250),.UNSPECIFIED.,.F.
  ,.F.,(9,7,9),(-1.093496958996,0.,1.093496958996),.UNSPECIFIED.);
#235 = CARTESIAN_POINT('',(3.788169243901,7.));
#236 = CARTESIAN_POINT('',(3.870517430405,6.184356934017));
#237 = CARTESIAN_POINT('',(3.963131464678,5.528351267725));
#238 = CARTESIAN_POINT('',(4.066667331168,5.008761792144));
#239 = CARTESIAN_POINT('',(4.18123002506,4.60872975528));
#240 = CARTESIAN_POINT('',(4.306210255287,4.316372024592));
#241 = CARTESIAN_POINT('',(4.439016252576,4.124022545751));
#242 = CARTESIAN_POINT('',(4.57570186051,4.027847779934));
#243 = CARTESIAN_POINT('',(4.849076100259,4.027847779934));
#244 = CARTESIAN_POINT('',(4.985761708181,4.124022545755));
#245 = CARTESIAN_POINT('',(5.118567705509,4.31637202457));
#246 = CARTESIAN_POINT('',(5.243547935625,4.608729755303));
#247 = CARTESIAN_POINT('',(5.35811062963,5.008761792133));
#248 = CARTESIAN_POINT('',(5.461646496079,5.52835126773));
#249 = CARTESIAN_POINT('',(5.554260530364,6.184356934017));
#250 = CARTESIAN_POINT('',(5.636608716868,7.));
#251 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#252 = PCURVE('',#253,#258);
#253 = PLANE('',#254);
#254 = AXIS2_PLACEMENT_3D('',#255,#256,#257);
#255 = CARTESIAN_POINT('',(72.917494,112.906445,42.5));
#256 = DIRECTION('',(0.,1.,0.));
#257 = DIRECTION('',(-1.,0.,0.));
#258 = DEFINITIONAL_REPRESENTATION('',(#259),#263);
#259 = ( BOUNDED_CURVE() B_SPLINE_CURVE(2,(#260,#261,#262),
.UNSPECIFIED.,.F.,.F.) B_SPLINE_CURVE_WITH_KNOTS((3,3),(-1.093496958996,
1.093496958996),.PIECEWISE_BEZIER_KNOTS.) CURVE() 
GEOMETRIC_REPRESENTATION_ITEM() RATIONAL_B_SPLINE_CURVE((1.,
1.659868002905,1.)) REPRESENTATION_ITEM('') );
#260 = CARTESIAN_POINT('',(21.665743088221,-27.74013));
#261 = CARTESIAN_POINT('',(18.220563,-32.50287774625));
#262 = CARTESIAN_POINT('',(14.775382911779,-27.74013));
#263 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#264 = ORIENTED_EDGE('',*,*,#265,.F.);
#265 = EDGE_CURVE('',#266,#224,#268,.T.);
#266 = VERTEX_POINT('',#267);
#267 = CARTESIAN_POINT('',(51.468253,113.528152,13.842496549604));
#268 = SURFACE_CURVE('',#269,(#274,#284),.PCURVE_S1.);
#269 = HYPERBOLA('',#270,6.455521237994,3.727096924515);
#270 = AXIS2_PLACEMENT_3D('',#271,#272,#273);
#271 = CARTESIAN_POINT('',(51.224780249824,114.1521470247,7.283556027919
    ));
#272 = DIRECTION('',(0.931596580528,0.363493894237,0.));
#273 = DIRECTION('',(-0.,0.,1.));
#274 = PCURVE('',#175,#275);
#275 = DEFINITIONAL_REPRESENTATION('',(#276),#283);
#276 = B_SPLINE_CURVE_WITH_KNOTS('',5,(#277,#278,#279,#280,#281,#282),
  .UNSPECIFIED.,.F.,.F.,(6,6),(0.161110846599,0.36829261524),
  .PIECEWISE_BEZIER_KNOTS.);
#277 = CARTESIAN_POINT('',(3.674026603807,6.063170678086));
#278 = CARTESIAN_POINT('',(3.714930937378,6.106453424668));
#279 = CARTESIAN_POINT('',(3.755496883956,6.163771290448));
#280 = CARTESIAN_POINT('',(3.795585407232,6.235279203987));
#281 = CARTESIAN_POINT('',(3.835070022506,6.321232032107));
#282 = CARTESIAN_POINT('',(3.873846692742,6.42199008265));
#283 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#284 = PCURVE('',#285,#290);
#285 = PLANE('',#286);
#286 = AXIS2_PLACEMENT_3D('',#287,#288,#289);
#287 = CARTESIAN_POINT('',(51.710833,112.906445,42.5));
#288 = DIRECTION('',(0.931596580528,0.363493894237,0.));
#289 = DIRECTION('',(-0.363493894237,0.931596580528,0.));
#290 = DEFINITIONAL_REPRESENTATION('',(#291),#295);
#291 = ( BOUNDED_CURVE() B_SPLINE_CURVE(2,(#292,#293,#294),
.UNSPECIFIED.,.F.,.F.) B_SPLINE_CURVE_WITH_KNOTS((3,3),(0.161110846599,
0.36829261524),.PIECEWISE_BEZIER_KNOTS.) CURVE() 
GEOMETRIC_REPRESENTATION_ITEM() RATIONAL_B_SPLINE_CURVE((1.,
1.005370335536,1.)) REPRESENTATION_ITEM('') );
#292 = CARTESIAN_POINT('',(0.73409211057,-28.67695932191));
#293 = CARTESIAN_POINT('',(0.344370189855,-28.56913785975));
#294 = CARTESIAN_POINT('',(-6.673564641546E-02,-28.31813991735));
#295 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#296 = ORIENTED_EDGE('',*,*,#297,.F.);
#297 = EDGE_CURVE('',#298,#266,#300,.T.);
#298 = VERTEX_POINT('',#299);
#299 = CARTESIAN_POINT('',(51.216761221469,118.06038737007,14.75987));
#300 = SURFACE_CURVE('',#301,(#306,#322),.PCURVE_S1.);
#301 = HYPERBOLA('',#302,5.773533807864,3.333351298146);
#302 = AXIS2_PLACEMENT_3D('',#303,#304,#305);
#303 = CARTESIAN_POINT('',(51.368699728854,115.32224188766,
    7.283556027919));
#304 = DIRECTION('',(0.998464000178,5.54043351018E-02,0.));
#305 = DIRECTION('',(-0.,0.,1.));
#306 = PCURVE('',#175,#307);
#307 = DEFINITIONAL_REPRESENTATION('',(#308),#321);
#308 = B_SPLINE_CURVE_WITH_KNOTS('',6,(#309,#310,#311,#312,#313,#314,
    #315,#316,#317,#318,#319,#320),.UNSPECIFIED.,.F.,.F.,(7,5,7),(
    -0.750298235566,-1.360863167177E-02,0.723080972222),.UNSPECIFIED.);
#309 = CARTESIAN_POINT('',(2.508593689981,7.));
#310 = CARTESIAN_POINT('',(2.603410965576,6.416799313593));
#311 = CARTESIAN_POINT('',(2.707104954396,5.968847694836));
#312 = CARTESIAN_POINT('',(2.819169872551,5.640322199127));
#313 = CARTESIAN_POINT('',(2.938123426335,5.421504333712));
#314 = CARTESIAN_POINT('',(3.060646929232,5.307401693653));
#315 = CARTESIAN_POINT('',(3.306187393764,5.288107223175));
#316 = CARTESIAN_POINT('',(3.429202461601,5.382915370147));
#317 = CARTESIAN_POINT('',(3.54914451802,5.581915317942));
#318 = CARTESIAN_POINT('',(3.662564203036,5.889575313611));
#319 = CARTESIAN_POINT('',(3.7678079643,6.315044563283));
#320 = CARTESIAN_POINT('',(3.86425751156,6.873473999601));
#321 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#322 = PCURVE('',#323,#328);
#323 = PLANE('',#324);
#324 = AXIS2_PLACEMENT_3D('',#325,#326,#327);
#325 = CARTESIAN_POINT('',(51.468253,113.528152,42.5));
#326 = DIRECTION('',(0.998464000178,5.54043351018E-02,0.));
#327 = DIRECTION('',(-5.54043351018E-02,0.998464000178,0.));
#328 = DEFINITIONAL_REPRESENTATION('',(#329),#333);
#329 = ( BOUNDED_CURVE() B_SPLINE_CURVE(2,(#330,#331,#332),
.UNSPECIFIED.,.F.,.F.) B_SPLINE_CURVE_WITH_KNOTS((3,3),(-0.750298235566,
0.723080972222),.PIECEWISE_BEZIER_KNOTS.) CURVE() 
GEOMETRIC_REPRESENTATION_ITEM() RATIONAL_B_SPLINE_CURVE((1.,
1.283852289095,1.)) REPRESENTATION_ITEM('') );
#330 = CARTESIAN_POINT('',(4.539207592127,-27.74013));
#331 = CARTESIAN_POINT('',(1.83218393706,-30.71898855642));
#332 = CARTESIAN_POINT('',(-0.829027185609,-27.86665600039));
#333 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#334 = ORIENTED_EDGE('',*,*,#335,.F.);
#335 = EDGE_CURVE('',#167,#298,#336,.T.);
#336 = SURFACE_CURVE('',#337,(#342,#349),.PCURVE_S1.);
#337 = CIRCLE('',#338,4.316451884327);
#338 = AXIS2_PLACEMENT_3D('',#339,#340,#341);
#339 = CARTESIAN_POINT('',(54.696931,115.506924,14.75987));
#340 = DIRECTION('',(0.,0.,1.));
#341 = DIRECTION('',(1.,0.,0.));
#342 = PCURVE('',#175,#343);
#343 = DEFINITIONAL_REPRESENTATION('',(#344),#348);
#344 = LINE('',#345,#346);
#345 = CARTESIAN_POINT('',(0.,7.));
#346 = VECTOR('',#347,1.);
#347 = DIRECTION('',(1.,0.));
#348 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#349 = PCURVE('',#211,#350);
#350 = DEFINITIONAL_REPRESENTATION('',(#351),#355);
#351 = CIRCLE('',#352,4.316451884327);
#352 = AXIS2_PLACEMENT_2D('',#353,#354);
#353 = CARTESIAN_POINT('',(0.,0.));
#354 = DIRECTION('',(1.,0.));
#355 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#356 = ORIENTED_EDGE('',*,*,#164,.F.);
#357 = ORIENTED_EDGE('',*,*,#358,.T.);
#358 = EDGE_CURVE('',#165,#165,#359,.T.);
#359 = SURFACE_CURVE('',#360,(#365,#372),.PCURVE_S1.);
#360 = CIRCLE('',#361,0.275);
#361 = AXIS2_PLACEMENT_3D('',#362,#363,#364);
#362 = CARTESIAN_POINT('',(54.696931,115.506924,7.75987));
#363 = DIRECTION('',(0.,0.,1.));
#364 = DIRECTION('',(1.,0.,0.));
#365 = PCURVE('',#175,#366);
#366 = DEFINITIONAL_REPRESENTATION('',(#367),#371);
#367 = LINE('',#368,#369);
#368 = CARTESIAN_POINT('',(0.,0.));
#369 = VECTOR('',#370,1.);
#370 = DIRECTION('',(1.,0.));
#371 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#372 = PCURVE('',#373,#378);
#373 = CYLINDRICAL_SURFACE('',#374,0.275);
#374 = AXIS2_PLACEMENT_3D('',#375,#376,#377);
#375 = CARTESIAN_POINT('',(54.696931,115.506924,7.75987));
#376 = DIRECTION('',(0.,0.,1.));
#377 = DIRECTION('',(1.,0.,0.));
#378 = DEFINITIONAL_REPRESENTATION('',(#379),#382);
#379 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#380,#381),.UNSPECIFIED.,.F.,.F.,
  (2,2),(0.,6.28318530718),.PIECEWISE_BEZIER_KNOTS.);
#380 = CARTESIAN_POINT('',(0.,0.));
#381 = CARTESIAN_POINT('',(6.28318530718,0.));
#382 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#383 = ADVANCED_FACE('',(#384),#373,.F.);
#384 = FACE_BOUND('',#385,.F.);
#385 = EDGE_LOOP('',(#386,#410,#431,#432));
#386 = ORIENTED_EDGE('',*,*,#387,.F.);
#387 = EDGE_CURVE('',#388,#388,#390,.T.);
#388 = VERTEX_POINT('',#389);
#389 = CARTESIAN_POINT('',(54.971931,115.506924,14.75987));
#390 = SURFACE_CURVE('',#391,(#396,#403),.PCURVE_S1.);
#391 = CIRCLE('',#392,0.275);
#392 = AXIS2_PLACEMENT_3D('',#393,#394,#395);
#393 = CARTESIAN_POINT('',(54.696931,115.506924,14.75987));
#394 = DIRECTION('',(0.,0.,1.));
#395 = DIRECTION('',(1.,0.,0.));
#396 = PCURVE('',#373,#397);
#397 = DEFINITIONAL_REPRESENTATION('',(#398),#402);
#398 = LINE('',#399,#400);
#399 = CARTESIAN_POINT('',(0.,7.));
#400 = VECTOR('',#401,1.);
#401 = DIRECTION('',(1.,0.));
#402 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#403 = PCURVE('',#211,#404);
#404 = DEFINITIONAL_REPRESENTATION('',(#405),#409);
#405 = CIRCLE('',#406,0.275);
#406 = AXIS2_PLACEMENT_2D('',#407,#408);
#407 = CARTESIAN_POINT('',(0.,0.));
#408 = DIRECTION('',(1.,0.));
#409 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#410 = ORIENTED_EDGE('',*,*,#411,.F.);
#411 = EDGE_CURVE('',#165,#388,#412,.T.);
#412 = SEAM_CURVE('',#413,(#417,#424),.PCURVE_S1.);
#413 = LINE('',#414,#415);
#414 = CARTESIAN_POINT('',(54.971931,115.506924,7.75987));
#415 = VECTOR('',#416,1.);
#416 = DIRECTION('',(0.,0.,1.));
#417 = PCURVE('',#373,#418);
#418 = DEFINITIONAL_REPRESENTATION('',(#419),#423);
#419 = LINE('',#420,#421);
#420 = CARTESIAN_POINT('',(6.28318530718,-0.));
#421 = VECTOR('',#422,1.);
#422 = DIRECTION('',(0.,1.));
#423 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#424 = PCURVE('',#373,#425);
#425 = DEFINITIONAL_REPRESENTATION('',(#426),#430);
#426 = LINE('',#427,#428);
#427 = CARTESIAN_POINT('',(0.,-0.));
#428 = VECTOR('',#429,1.);
#429 = DIRECTION('',(0.,1.));
#430 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#431 = ORIENTED_EDGE('',*,*,#358,.T.);
#432 = ORIENTED_EDGE('',*,*,#411,.T.);
#433 = ADVANCED_FACE('',(#434,#499),#211,.T.);
#434 = FACE_BOUND('',#435,.T.);
#435 = EDGE_LOOP('',(#436,#437,#438,#459,#480));
#436 = ORIENTED_EDGE('',*,*,#194,.T.);
#437 = ORIENTED_EDGE('',*,*,#335,.T.);
#438 = ORIENTED_EDGE('',*,*,#439,.F.);
#439 = EDGE_CURVE('',#440,#298,#442,.T.);
#440 = VERTEX_POINT('',#441);
#441 = CARTESIAN_POINT('',(51.468253,113.528152,14.75987));
#442 = SURFACE_CURVE('',#443,(#447,#453),.PCURVE_S1.);
#443 = LINE('',#444,#445);
#444 = CARTESIAN_POINT('',(51.418476364427,114.42519694383,14.75987));
#445 = VECTOR('',#446,1.);
#446 = DIRECTION('',(-5.54043351018E-02,0.998464000178,0.));
#447 = PCURVE('',#211,#448);
#448 = DEFINITIONAL_REPRESENTATION('',(#449),#452);
#449 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#450,#451),.UNSPECIFIED.,.F.,.F.,
  (2,2),(-0.898424924356,5.406483297877),.PIECEWISE_BEZIER_KNOTS.);
#450 = CARTESIAN_POINT('',(-3.228678,-1.978772));
#451 = CARTESIAN_POINT('',(-3.577997247931,4.316451884327));
#452 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#453 = PCURVE('',#323,#454);
#454 = DEFINITIONAL_REPRESENTATION('',(#455),#458);
#455 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#456,#457),.UNSPECIFIED.,.F.,.F.,
  (2,2),(-0.898424924356,5.406483297877),.PIECEWISE_BEZIER_KNOTS.);
#456 = CARTESIAN_POINT('',(-1.42108547152E-14,-27.74013));
#457 = CARTESIAN_POINT('',(6.304908222233,-27.74013));
#458 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#459 = ORIENTED_EDGE('',*,*,#460,.F.);
#460 = EDGE_CURVE('',#461,#440,#463,.T.);
#461 = VERTEX_POINT('',#462);
#462 = CARTESIAN_POINT('',(51.710833,112.906445,14.75987));
#463 = SURFACE_CURVE('',#464,(#468,#474),.PCURVE_S1.);
#464 = LINE('',#465,#466);
#465 = CARTESIAN_POINT('',(51.467806624912,113.52929601235,14.75987));
#466 = VECTOR('',#467,1.);
#467 = DIRECTION('',(-0.363493894237,0.931596580528,0.));
#468 = PCURVE('',#211,#469);
#469 = DEFINITIONAL_REPRESENTATION('',(#470),#473);
#470 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#471,#472),.UNSPECIFIED.,.F.,.F.,
  (2,2),(-0.668584476771,-1.228012616207E-03),.PIECEWISE_BEZIER_KNOTS.);
#471 = CARTESIAN_POINT('',(-2.986098,-2.600479));
#472 = CARTESIAN_POINT('',(-3.228678,-1.978772));
#473 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#474 = PCURVE('',#285,#475);
#475 = DEFINITIONAL_REPRESENTATION('',(#476),#479);
#476 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#477,#478),.UNSPECIFIED.,.F.,.F.,
  (2,2),(-0.668584476771,-1.228012616207E-03),.PIECEWISE_BEZIER_KNOTS.);
#477 = CARTESIAN_POINT('',(0.,-27.74013));
#478 = CARTESIAN_POINT('',(0.667356464155,-27.74013));
#479 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#480 = ORIENTED_EDGE('',*,*,#481,.F.);
#481 = EDGE_CURVE('',#195,#461,#482,.T.);
#482 = SURFACE_CURVE('',#483,(#487,#493),.PCURVE_S1.);
#483 = LINE('',#484,#485);
#484 = CARTESIAN_POINT('',(63.8072125,112.906445,14.75987));
#485 = VECTOR('',#486,1.);
#486 = DIRECTION('',(-1.,0.,0.));
#487 = PCURVE('',#211,#488);
#488 = DEFINITIONAL_REPRESENTATION('',(#489),#492);
#489 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#490,#491),.UNSPECIFIED.,.F.,.F.,
  (2,2),(4.793829615673,12.0963795),.PIECEWISE_BEZIER_KNOTS.);
#490 = CARTESIAN_POINT('',(4.316451884327,-2.600479));
#491 = CARTESIAN_POINT('',(-2.986098,-2.600479));
#492 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#493 = PCURVE('',#253,#494);
#494 = DEFINITIONAL_REPRESENTATION('',(#495),#498);
#495 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#496,#497),.UNSPECIFIED.,.F.,.F.,
  (2,2),(4.793829615673,12.0963795),.PIECEWISE_BEZIER_KNOTS.);
#496 = CARTESIAN_POINT('',(13.904111115673,-27.74013));
#497 = CARTESIAN_POINT('',(21.206661,-27.74013));
#498 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#499 = FACE_BOUND('',#500,.T.);
#500 = EDGE_LOOP('',(#501));
#501 = ORIENTED_EDGE('',*,*,#387,.F.);
#502 = ADVANCED_FACE('',(#503),#323,.F.);
#503 = FACE_BOUND('',#504,.F.);
#504 = EDGE_LOOP('',(#505,#506,#527));
#505 = ORIENTED_EDGE('',*,*,#439,.F.);
#506 = ORIENTED_EDGE('',*,*,#507,.T.);
#507 = EDGE_CURVE('',#440,#266,#508,.T.);
#508 = SURFACE_CURVE('',#509,(#513,#520),.PCURVE_S1.);
#509 = LINE('',#510,#511);
#510 = CARTESIAN_POINT('',(51.468253,113.528152,42.5));
#511 = VECTOR('',#512,1.);
#512 = DIRECTION('',(0.,0.,-1.));
#513 = PCURVE('',#323,#514);
#514 = DEFINITIONAL_REPRESENTATION('',(#515),#519);
#515 = LINE('',#516,#517);
#516 = CARTESIAN_POINT('',(0.,0.));
#517 = VECTOR('',#518,1.);
#518 = DIRECTION('',(0.,-1.));
#519 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#520 = PCURVE('',#285,#521);
#521 = DEFINITIONAL_REPRESENTATION('',(#522),#526);
#522 = LINE('',#523,#524);
#523 = CARTESIAN_POINT('',(0.667356464155,0.));
#524 = VECTOR('',#525,1.);
#525 = DIRECTION('',(0.,-1.));
#526 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#527 = ORIENTED_EDGE('',*,*,#297,.F.);
#528 = ADVANCED_FACE('',(#529),#285,.F.);
#529 = FACE_BOUND('',#530,.F.);
#530 = EDGE_LOOP('',(#531,#532,#553,#554));
#531 = ORIENTED_EDGE('',*,*,#460,.F.);
#532 = ORIENTED_EDGE('',*,*,#533,.T.);
#533 = EDGE_CURVE('',#461,#224,#534,.T.);
#534 = SURFACE_CURVE('',#535,(#539,#546),.PCURVE_S1.);
#535 = LINE('',#536,#537);
#536 = CARTESIAN_POINT('',(51.710833,112.906445,42.5));
#537 = VECTOR('',#538,1.);
#538 = DIRECTION('',(0.,0.,-1.));
#539 = PCURVE('',#285,#540);
#540 = DEFINITIONAL_REPRESENTATION('',(#541),#545);
#541 = LINE('',#542,#543);
#542 = CARTESIAN_POINT('',(0.,0.));
#543 = VECTOR('',#544,1.);
#544 = DIRECTION('',(0.,-1.));
#545 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#546 = PCURVE('',#253,#547);
#547 = DEFINITIONAL_REPRESENTATION('',(#548),#552);
#548 = LINE('',#549,#550);
#549 = CARTESIAN_POINT('',(21.206661,0.));
#550 = VECTOR('',#551,1.);
#551 = DIRECTION('',(0.,-1.));
#552 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#553 = ORIENTED_EDGE('',*,*,#265,.F.);
#554 = ORIENTED_EDGE('',*,*,#507,.F.);
#555 = ADVANCED_FACE('',(#556),#253,.F.);
#556 = FACE_BOUND('',#557,.F.);
#557 = EDGE_LOOP('',(#558,#559,#560));
#558 = ORIENTED_EDGE('',*,*,#533,.F.);
#559 = ORIENTED_EDGE('',*,*,#481,.F.);
#560 = ORIENTED_EDGE('',*,*,#223,.F.);
#561 = ( GEOMETRIC_REPRESENTATION_CONTEXT(3) 
GLOBAL_UNCERTAINTY_ASSIGNED_CONTEXT((#565)) GLOBAL_UNIT_ASSIGNED_CONTEXT
((#562,#563,#564)) REPRESENTATION_CONTEXT('Context #1',
  '3D Context with UNIT and UNCERTAINTY') );
#562 = ( LENGTH_UNIT() NAMED_UNIT(*) SI_UNIT(.MILLI.,.METRE.) );
#563 = ( NAMED_UNIT(*) PLANE_ANGLE_UNIT() SI_UNIT($,.RADIAN.) );
#564 = ( NAMED_UNIT(*) SI_UNIT($,.STERADIAN.) SOLID_ANGLE_UNIT() );
#565 = UNCERTAINTY_MEASURE_WITH_UNIT(LENGTH_MEASURE(2.E-07),#562,
  'distance_accuracy_value','confusion accuracy');
#566 = CONTEXT_DEPENDENT_SHAPE_REPRESENTATION(#567,#569);
#567 = ( REPRESENTATION_RELATIONSHIP('','',#157,#10) 
REPRESENTATION_RELATIONSHIP_WITH_TRANSFORMATION(#568) 
SHAPE_REPRESENTATION_RELATIONSHIP() );
#568 = ITEM_DEFINED_TRANSFORMATION('','',#11,#19);
#569 = PRODUCT_DEFINITION_SHAPE('Placement','Placement of an item',#570
  );
#570 = NEXT_ASSEMBLY_USAGE_OCCURRENCE('127','=>[0:1:1:3]','',#5,#152,$);
#571 = PRODUCT_RELATED_PRODUCT_CATEGORY('part',$,(#154));
#572 = SHAPE_DEFINITION_REPRESENTATION(#573,#579);
#573 = PRODUCT_DEFINITION_SHAPE('','',#574);
#574 = PRODUCT_DEFINITION('design','',#575,#578);
#575 = PRODUCT_DEFINITION_FORMATION('','',#576);
#576 = PRODUCT('PC2-P8_Part3','PC2-P8_Part3','',(#577));
#577 = PRODUCT_CONTEXT('',#2,'mechanical');
#578 = PRODUCT_DEFINITION_CONTEXT('part definition',#2,'design');
#579 = ADVANCED_BREP_SHAPE_REPRESENTATION('',(#11,#580),#984);
#580 = MANIFOLD_SOLID_BREP('',#581);
#581 = CLOSED_SHELL('',(#582,#704,#805,#852,#923,#950,#957));
#582 = ADVANCED_FACE('',(#583),#598,.T.);
#583 = FACE_BOUND('',#584,.T.);
#584 = EDGE_LOOP('',(#585,#621,#647,#680));
#585 = ORIENTED_EDGE('',*,*,#586,.T.);
#586 = EDGE_CURVE('',#587,#589,#591,.T.);
#587 = VERTEX_POINT('',#588);
#588 = CARTESIAN_POINT('',(58.142111088221,112.906445,14.75987));
#589 = VERTEX_POINT('',#590);
#590 = CARTESIAN_POINT('',(51.216761221469,118.06038737007,14.75987));
#591 = SURFACE_CURVE('',#592,(#597,#609),.PCURVE_S1.);
#592 = CIRCLE('',#593,4.316451884327);
#593 = AXIS2_PLACEMENT_3D('',#594,#595,#596);
#594 = CARTESIAN_POINT('',(54.696931,115.506924,14.75987));
#595 = DIRECTION('',(-0.,0.,1.));
#596 = DIRECTION('',(0.798150930566,-0.602457543762,0.));
#597 = PCURVE('',#598,#603);
#598 = CYLINDRICAL_SURFACE('',#599,4.316451884327);
#599 = AXIS2_PLACEMENT_3D('',#600,#601,#602);
#600 = CARTESIAN_POINT('',(54.696931,115.506924,14.75987));
#601 = DIRECTION('',(0.,0.,1.));
#602 = DIRECTION('',(1.,0.,0.));
#603 = DEFINITIONAL_REPRESENTATION('',(#604),#608);
#604 = LINE('',#605,#606);
#605 = CARTESIAN_POINT('',(5.636608716868,0.));
#606 = VECTOR('',#607,1.);
#607 = DIRECTION('',(1.,0.));
#608 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#609 = PCURVE('',#610,#615);
#610 = PLANE('',#611);
#611 = AXIS2_PLACEMENT_3D('',#612,#613,#614);
#612 = CARTESIAN_POINT('',(54.696931,115.506924,14.75987));
#613 = DIRECTION('',(0.,0.,1.));
#614 = DIRECTION('',(1.,0.,0.));
#615 = DEFINITIONAL_REPRESENTATION('',(#616),#620);
#616 = CIRCLE('',#617,4.316451884327);
#617 = AXIS2_PLACEMENT_2D('',#618,#619);
#618 = CARTESIAN_POINT('',(0.,0.));
#619 = DIRECTION('',(0.798150930566,-0.602457543762));
#620 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#621 = ORIENTED_EDGE('',*,*,#622,.T.);
#622 = EDGE_CURVE('',#589,#623,#625,.T.);
#623 = VERTEX_POINT('',#624);
#624 = CARTESIAN_POINT('',(51.216761221469,118.06038737007,32.5));
#625 = SURFACE_CURVE('',#626,(#630,#636),.PCURVE_S1.);
#626 = LINE('',#627,#628);
#627 = CARTESIAN_POINT('',(51.216761221469,118.06038737007,14.75987));
#628 = VECTOR('',#629,1.);
#629 = DIRECTION('',(0.,0.,1.));
#630 = PCURVE('',#598,#631);
#631 = DEFINITIONAL_REPRESENTATION('',(#632),#635);
#632 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#633,#634),.UNSPECIFIED.,.F.,.F.,
  (2,2),(0.,17.74013),.PIECEWISE_BEZIER_KNOTS.);
#633 = CARTESIAN_POINT('',(8.79177899716,0.));
#634 = CARTESIAN_POINT('',(8.79177899716,17.74013));
#635 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#636 = PCURVE('',#637,#642);
#637 = PLANE('',#638);
#638 = AXIS2_PLACEMENT_3D('',#639,#640,#641);
#639 = CARTESIAN_POINT('',(51.468253,113.528152,42.5));
#640 = DIRECTION('',(0.998464000178,5.54043351018E-02,0.));
#641 = DIRECTION('',(-5.54043351018E-02,0.998464000178,0.));
#642 = DEFINITIONAL_REPRESENTATION('',(#643),#646);
#643 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#644,#645),.UNSPECIFIED.,.F.,.F.,
  (2,2),(-5.999999999062E-07,17.7401306),.PIECEWISE_BEZIER_KNOTS.);
#644 = CARTESIAN_POINT('',(4.539207592127,-27.7401306));
#645 = CARTESIAN_POINT('',(4.539207592127,-9.9999994));
#646 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#647 = ORIENTED_EDGE('',*,*,#648,.T.);
#648 = EDGE_CURVE('',#623,#649,#651,.T.);
#649 = VERTEX_POINT('',#650);
#650 = CARTESIAN_POINT('',(58.142111088221,112.906445,32.5));
#651 = SURFACE_CURVE('',#652,(#657,#664),.PCURVE_S1.);
#652 = CIRCLE('',#653,4.316451884327);
#653 = AXIS2_PLACEMENT_3D('',#654,#655,#656);
#654 = CARTESIAN_POINT('',(54.696931,115.506924,32.5));
#655 = DIRECTION('',(0.,0.,-1.));
#656 = DIRECTION('',(-0.806257053662,0.591565350084,0.));
#657 = PCURVE('',#598,#658);
#658 = DEFINITIONAL_REPRESENTATION('',(#659),#663);
#659 = LINE('',#660,#661);
#660 = CARTESIAN_POINT('',(8.79177899716,17.74013));
#661 = VECTOR('',#662,1.);
#662 = DIRECTION('',(-1.,-0.));
#663 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#664 = PCURVE('',#665,#670);
#665 = PLANE('',#666);
#666 = AXIS2_PLACEMENT_3D('',#667,#668,#669);
#667 = CARTESIAN_POINT('',(54.696931,115.506924,32.5));
#668 = DIRECTION('',(0.,0.,1.));
#669 = DIRECTION('',(1.,0.,0.));
#670 = DEFINITIONAL_REPRESENTATION('',(#671),#679);
#671 = ( BOUNDED_CURVE() B_SPLINE_CURVE(2,(#672,#673,#674,#675,#676,#677
,#678),.UNSPECIFIED.,.T.,.F.) B_SPLINE_CURVE_WITH_KNOTS((1,2,2,2,2,1),(
    -2.094395102393,0.,2.094395102393,4.188790204786,6.28318530718,
8.377580409573),.UNSPECIFIED.) CURVE() GEOMETRIC_REPRESENTATION_ITEM() 
RATIONAL_B_SPLINE_CURVE((1.,0.5,1.,0.5,1.,0.5,1.)) REPRESENTATION_ITEM(
  '') );
#672 = CARTESIAN_POINT('',(-3.480169778531,2.553463370075));
#673 = CARTESIAN_POINT('',(0.942558513705,8.581294245456));
#674 = CARTESIAN_POINT('',(3.951449035383,1.737183752653));
#675 = CARTESIAN_POINT('',(6.960339557062,-5.10692674015));
#676 = CARTESIAN_POINT('',(-0.471279256852,-4.290647122728));
#677 = CARTESIAN_POINT('',(-7.902898070767,-3.474367505306));
#678 = CARTESIAN_POINT('',(-3.480169778531,2.553463370075));
#679 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#680 = ORIENTED_EDGE('',*,*,#681,.F.);
#681 = EDGE_CURVE('',#587,#649,#682,.T.);
#682 = SURFACE_CURVE('',#683,(#687,#693),.PCURVE_S1.);
#683 = LINE('',#684,#685);
#684 = CARTESIAN_POINT('',(58.142111088221,112.906445,14.75987));
#685 = VECTOR('',#686,1.);
#686 = DIRECTION('',(0.,0.,1.));
#687 = PCURVE('',#598,#688);
#688 = DEFINITIONAL_REPRESENTATION('',(#689),#692);
#689 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#690,#691),.UNSPECIFIED.,.F.,.F.,
  (2,2),(0.,17.74013),.PIECEWISE_BEZIER_KNOTS.);
#690 = CARTESIAN_POINT('',(5.636608716868,0.));
#691 = CARTESIAN_POINT('',(5.636608716868,17.74013));
#692 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#693 = PCURVE('',#694,#699);
#694 = PLANE('',#695);
#695 = AXIS2_PLACEMENT_3D('',#696,#697,#698);
#696 = CARTESIAN_POINT('',(72.917494,112.906445,42.5));
#697 = DIRECTION('',(0.,1.,0.));
#698 = DIRECTION('',(-1.,0.,0.));
#699 = DEFINITIONAL_REPRESENTATION('',(#700),#703);
#700 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#701,#702),.UNSPECIFIED.,.F.,.F.,
  (2,2),(-5.999999999062E-07,17.7401306),.PIECEWISE_BEZIER_KNOTS.);
#701 = CARTESIAN_POINT('',(14.775382911779,-27.7401306));
#702 = CARTESIAN_POINT('',(14.775382911779,-9.9999994));
#703 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#704 = ADVANCED_FACE('',(#705,#774),#610,.F.);
#705 = FACE_BOUND('',#706,.F.);
#706 = EDGE_LOOP('',(#707,#708,#729,#755));
#707 = ORIENTED_EDGE('',*,*,#586,.T.);
#708 = ORIENTED_EDGE('',*,*,#709,.F.);
#709 = EDGE_CURVE('',#710,#589,#712,.T.);
#710 = VERTEX_POINT('',#711);
#711 = CARTESIAN_POINT('',(51.468253,113.528152,14.75987));
#712 = SURFACE_CURVE('',#713,(#717,#723),.PCURVE_S1.);
#713 = LINE('',#714,#715);
#714 = CARTESIAN_POINT('',(51.418476364427,114.42519694383,14.75987));
#715 = VECTOR('',#716,1.);
#716 = DIRECTION('',(-5.54043351018E-02,0.998464000178,0.));
#717 = PCURVE('',#610,#718);
#718 = DEFINITIONAL_REPRESENTATION('',(#719),#722);
#719 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#720,#721),.UNSPECIFIED.,.F.,.F.,
  (2,2),(-0.898424924356,5.406483297877),.PIECEWISE_BEZIER_KNOTS.);
#720 = CARTESIAN_POINT('',(-3.228678,-1.978772));
#721 = CARTESIAN_POINT('',(-3.577997247931,4.316451884327));
#722 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#723 = PCURVE('',#637,#724);
#724 = DEFINITIONAL_REPRESENTATION('',(#725),#728);
#725 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#726,#727),.UNSPECIFIED.,.F.,.F.,
  (2,2),(-0.898424924356,5.406483297877),.PIECEWISE_BEZIER_KNOTS.);
#726 = CARTESIAN_POINT('',(-1.42108547152E-14,-27.74013));
#727 = CARTESIAN_POINT('',(6.304908222233,-27.74013));
#728 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#729 = ORIENTED_EDGE('',*,*,#730,.F.);
#730 = EDGE_CURVE('',#731,#710,#733,.T.);
#731 = VERTEX_POINT('',#732);
#732 = CARTESIAN_POINT('',(51.710833,112.906445,14.75987));
#733 = SURFACE_CURVE('',#734,(#738,#744),.PCURVE_S1.);
#734 = LINE('',#735,#736);
#735 = CARTESIAN_POINT('',(51.467806624912,113.52929601235,14.75987));
#736 = VECTOR('',#737,1.);
#737 = DIRECTION('',(-0.363493894237,0.931596580528,0.));
#738 = PCURVE('',#610,#739);
#739 = DEFINITIONAL_REPRESENTATION('',(#740),#743);
#740 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#741,#742),.UNSPECIFIED.,.F.,.F.,
  (2,2),(-0.668584476771,-1.228012616207E-03),.PIECEWISE_BEZIER_KNOTS.);
#741 = CARTESIAN_POINT('',(-2.986098,-2.600479));
#742 = CARTESIAN_POINT('',(-3.228678,-1.978772));
#743 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#744 = PCURVE('',#745,#750);
#745 = PLANE('',#746);
#746 = AXIS2_PLACEMENT_3D('',#747,#748,#749);
#747 = CARTESIAN_POINT('',(51.710833,112.906445,42.5));
#748 = DIRECTION('',(0.931596580528,0.363493894237,0.));
#749 = DIRECTION('',(-0.363493894237,0.931596580528,0.));
#750 = DEFINITIONAL_REPRESENTATION('',(#751),#754);
#751 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#752,#753),.UNSPECIFIED.,.F.,.F.,
  (2,2),(-0.668584476771,-1.228012616207E-03),.PIECEWISE_BEZIER_KNOTS.);
#752 = CARTESIAN_POINT('',(0.,-27.74013));
#753 = CARTESIAN_POINT('',(0.667356464155,-27.74013));
#754 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#755 = ORIENTED_EDGE('',*,*,#756,.F.);
#756 = EDGE_CURVE('',#587,#731,#757,.T.);
#757 = SURFACE_CURVE('',#758,(#762,#768),.PCURVE_S1.);
#758 = LINE('',#759,#760);
#759 = CARTESIAN_POINT('',(63.8072125,112.906445,14.75987));
#760 = VECTOR('',#761,1.);
#761 = DIRECTION('',(-1.,0.,0.));
#762 = PCURVE('',#610,#763);
#763 = DEFINITIONAL_REPRESENTATION('',(#764),#767);
#764 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#765,#766),.UNSPECIFIED.,.F.,.F.,
  (2,2),(4.793829615673,12.0963795),.PIECEWISE_BEZIER_KNOTS.);
#765 = CARTESIAN_POINT('',(4.316451884327,-2.600479));
#766 = CARTESIAN_POINT('',(-2.986098,-2.600479));
#767 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#768 = PCURVE('',#694,#769);
#769 = DEFINITIONAL_REPRESENTATION('',(#770),#773);
#770 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#771,#772),.UNSPECIFIED.,.F.,.F.,
  (2,2),(4.793829615673,12.0963795),.PIECEWISE_BEZIER_KNOTS.);
#771 = CARTESIAN_POINT('',(13.904111115673,-27.74013));
#772 = CARTESIAN_POINT('',(21.206661,-27.74013));
#773 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#774 = FACE_BOUND('',#775,.F.);
#775 = EDGE_LOOP('',(#776));
#776 = ORIENTED_EDGE('',*,*,#777,.F.);
#777 = EDGE_CURVE('',#778,#778,#780,.T.);
#778 = VERTEX_POINT('',#779);
#779 = CARTESIAN_POINT('',(54.971931,115.506924,14.75987));
#780 = SURFACE_CURVE('',#781,(#786,#793),.PCURVE_S1.);
#781 = CIRCLE('',#782,0.275);
#782 = AXIS2_PLACEMENT_3D('',#783,#784,#785);
#783 = CARTESIAN_POINT('',(54.696931,115.506924,14.75987));
#784 = DIRECTION('',(0.,0.,1.));
#785 = DIRECTION('',(1.,0.,0.));
#786 = PCURVE('',#610,#787);
#787 = DEFINITIONAL_REPRESENTATION('',(#788),#792);
#788 = CIRCLE('',#789,0.275);
#789 = AXIS2_PLACEMENT_2D('',#790,#791);
#790 = CARTESIAN_POINT('',(0.,0.));
#791 = DIRECTION('',(1.,0.));
#792 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#793 = PCURVE('',#794,#799);
#794 = CYLINDRICAL_SURFACE('',#795,0.275);
#795 = AXIS2_PLACEMENT_3D('',#796,#797,#798);
#796 = CARTESIAN_POINT('',(54.696931,115.506924,14.75987));
#797 = DIRECTION('',(0.,0.,1.));
#798 = DIRECTION('',(1.,0.,0.));
#799 = DEFINITIONAL_REPRESENTATION('',(#800),#804);
#800 = LINE('',#801,#802);
#801 = CARTESIAN_POINT('',(0.,0.));
#802 = VECTOR('',#803,1.);
#803 = DIRECTION('',(1.,0.));
#804 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#805 = ADVANCED_FACE('',(#806),#694,.F.);
#806 = FACE_BOUND('',#807,.F.);
#807 = EDGE_LOOP('',(#808,#831,#850,#851));
#808 = ORIENTED_EDGE('',*,*,#809,.F.);
#809 = EDGE_CURVE('',#810,#731,#812,.T.);
#810 = VERTEX_POINT('',#811);
#811 = CARTESIAN_POINT('',(51.710833,112.906445,32.5));
#812 = SURFACE_CURVE('',#813,(#817,#824),.PCURVE_S1.);
#813 = LINE('',#814,#815);
#814 = CARTESIAN_POINT('',(51.710833,112.906445,42.5));
#815 = VECTOR('',#816,1.);
#816 = DIRECTION('',(0.,0.,-1.));
#817 = PCURVE('',#694,#818);
#818 = DEFINITIONAL_REPRESENTATION('',(#819),#823);
#819 = LINE('',#820,#821);
#820 = CARTESIAN_POINT('',(21.206661,0.));
#821 = VECTOR('',#822,1.);
#822 = DIRECTION('',(0.,-1.));
#823 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#824 = PCURVE('',#745,#825);
#825 = DEFINITIONAL_REPRESENTATION('',(#826),#830);
#826 = LINE('',#827,#828);
#827 = CARTESIAN_POINT('',(0.,0.));
#828 = VECTOR('',#829,1.);
#829 = DIRECTION('',(0.,-1.));
#830 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#831 = ORIENTED_EDGE('',*,*,#832,.F.);
#832 = EDGE_CURVE('',#649,#810,#833,.T.);
#833 = SURFACE_CURVE('',#834,(#838,#844),.PCURVE_S1.);
#834 = LINE('',#835,#836);
#835 = CARTESIAN_POINT('',(63.8072125,112.906445,32.5));
#836 = VECTOR('',#837,1.);
#837 = DIRECTION('',(-1.,0.,0.));
#838 = PCURVE('',#694,#839);
#839 = DEFINITIONAL_REPRESENTATION('',(#840),#843);
#840 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#841,#842),.UNSPECIFIED.,.F.,.F.,
  (2,2),(4.793829615673,12.0963795),.PIECEWISE_BEZIER_KNOTS.);
#841 = CARTESIAN_POINT('',(13.904111115673,-10.));
#842 = CARTESIAN_POINT('',(21.206661,-10.));
#843 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#844 = PCURVE('',#665,#845);
#845 = DEFINITIONAL_REPRESENTATION('',(#846),#849);
#846 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#847,#848),.UNSPECIFIED.,.F.,.F.,
  (2,2),(4.793829615673,12.0963795),.PIECEWISE_BEZIER_KNOTS.);
#847 = CARTESIAN_POINT('',(4.316451884327,-2.600479));
#848 = CARTESIAN_POINT('',(-2.986098,-2.600479));
#849 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#850 = ORIENTED_EDGE('',*,*,#681,.F.);
#851 = ORIENTED_EDGE('',*,*,#756,.T.);
#852 = ADVANCED_FACE('',(#853,#897),#665,.T.);
#853 = FACE_BOUND('',#854,.T.);
#854 = EDGE_LOOP('',(#855,#856,#857,#878));
#855 = ORIENTED_EDGE('',*,*,#832,.F.);
#856 = ORIENTED_EDGE('',*,*,#648,.F.);
#857 = ORIENTED_EDGE('',*,*,#858,.F.);
#858 = EDGE_CURVE('',#859,#623,#861,.T.);
#859 = VERTEX_POINT('',#860);
#860 = CARTESIAN_POINT('',(51.468253,113.528152,32.5));
#861 = SURFACE_CURVE('',#862,(#866,#872),.PCURVE_S1.);
#862 = LINE('',#863,#864);
#863 = CARTESIAN_POINT('',(51.418476364427,114.42519694383,32.5));
#864 = VECTOR('',#865,1.);
#865 = DIRECTION('',(-5.54043351018E-02,0.998464000178,0.));
#866 = PCURVE('',#665,#867);
#867 = DEFINITIONAL_REPRESENTATION('',(#868),#871);
#868 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#869,#870),.UNSPECIFIED.,.F.,.F.,
  (2,2),(-0.898424924356,5.406483297877),.PIECEWISE_BEZIER_KNOTS.);
#869 = CARTESIAN_POINT('',(-3.228678,-1.978772));
#870 = CARTESIAN_POINT('',(-3.577997247931,4.316451884327));
#871 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#872 = PCURVE('',#637,#873);
#873 = DEFINITIONAL_REPRESENTATION('',(#874),#877);
#874 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#875,#876),.UNSPECIFIED.,.F.,.F.,
  (2,2),(-0.898424924356,5.406483297877),.PIECEWISE_BEZIER_KNOTS.);
#875 = CARTESIAN_POINT('',(-1.42108547152E-14,-10.));
#876 = CARTESIAN_POINT('',(6.304908222233,-10.));
#877 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#878 = ORIENTED_EDGE('',*,*,#879,.F.);
#879 = EDGE_CURVE('',#810,#859,#880,.T.);
#880 = SURFACE_CURVE('',#881,(#885,#891),.PCURVE_S1.);
#881 = LINE('',#882,#883);
#882 = CARTESIAN_POINT('',(51.467806624912,113.52929601235,32.5));
#883 = VECTOR('',#884,1.);
#884 = DIRECTION('',(-0.363493894237,0.931596580528,0.));
#885 = PCURVE('',#665,#886);
#886 = DEFINITIONAL_REPRESENTATION('',(#887),#890);
#887 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#888,#889),.UNSPECIFIED.,.F.,.F.,
  (2,2),(-0.668584476771,-1.228012616207E-03),.PIECEWISE_BEZIER_KNOTS.);
#888 = CARTESIAN_POINT('',(-2.986098,-2.600479));
#889 = CARTESIAN_POINT('',(-3.228678,-1.978772));
#890 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#891 = PCURVE('',#745,#892);
#892 = DEFINITIONAL_REPRESENTATION('',(#893),#896);
#893 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#894,#895),.UNSPECIFIED.,.F.,.F.,
  (2,2),(-0.668584476771,-1.228012616207E-03),.PIECEWISE_BEZIER_KNOTS.);
#894 = CARTESIAN_POINT('',(0.,-10.));
#895 = CARTESIAN_POINT('',(0.667356464155,-10.));
#896 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#897 = FACE_BOUND('',#898,.T.);
#898 = EDGE_LOOP('',(#899));
#899 = ORIENTED_EDGE('',*,*,#900,.F.);
#900 = EDGE_CURVE('',#901,#901,#903,.T.);
#901 = VERTEX_POINT('',#902);
#902 = CARTESIAN_POINT('',(54.971931,115.506924,32.5));
#903 = SURFACE_CURVE('',#904,(#909,#916),.PCURVE_S1.);
#904 = CIRCLE('',#905,0.275);
#905 = AXIS2_PLACEMENT_3D('',#906,#907,#908);
#906 = CARTESIAN_POINT('',(54.696931,115.506924,32.5));
#907 = DIRECTION('',(0.,0.,1.));
#908 = DIRECTION('',(1.,0.,0.));
#909 = PCURVE('',#665,#910);
#910 = DEFINITIONAL_REPRESENTATION('',(#911),#915);
#911 = CIRCLE('',#912,0.275);
#912 = AXIS2_PLACEMENT_2D('',#913,#914);
#913 = CARTESIAN_POINT('',(0.,0.));
#914 = DIRECTION('',(1.,0.));
#915 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#916 = PCURVE('',#794,#917);
#917 = DEFINITIONAL_REPRESENTATION('',(#918),#922);
#918 = LINE('',#919,#920);
#919 = CARTESIAN_POINT('',(0.,17.74013));
#920 = VECTOR('',#921,1.);
#921 = DIRECTION('',(1.,0.));
#922 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#923 = ADVANCED_FACE('',(#924),#637,.F.);
#924 = FACE_BOUND('',#925,.F.);
#925 = EDGE_LOOP('',(#926,#927,#948,#949));
#926 = ORIENTED_EDGE('',*,*,#858,.F.);
#927 = ORIENTED_EDGE('',*,*,#928,.T.);
#928 = EDGE_CURVE('',#859,#710,#929,.T.);
#929 = SURFACE_CURVE('',#930,(#934,#941),.PCURVE_S1.);
#930 = LINE('',#931,#932);
#931 = CARTESIAN_POINT('',(51.468253,113.528152,42.5));
#932 = VECTOR('',#933,1.);
#933 = DIRECTION('',(0.,0.,-1.));
#934 = PCURVE('',#637,#935);
#935 = DEFINITIONAL_REPRESENTATION('',(#936),#940);
#936 = LINE('',#937,#938);
#937 = CARTESIAN_POINT('',(0.,0.));
#938 = VECTOR('',#939,1.);
#939 = DIRECTION('',(0.,-1.));
#940 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#941 = PCURVE('',#745,#942);
#942 = DEFINITIONAL_REPRESENTATION('',(#943),#947);
#943 = LINE('',#944,#945);
#944 = CARTESIAN_POINT('',(0.667356464155,0.));
#945 = VECTOR('',#946,1.);
#946 = DIRECTION('',(0.,-1.));
#947 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#948 = ORIENTED_EDGE('',*,*,#709,.T.);
#949 = ORIENTED_EDGE('',*,*,#622,.T.);
#950 = ADVANCED_FACE('',(#951),#745,.F.);
#951 = FACE_BOUND('',#952,.F.);
#952 = EDGE_LOOP('',(#953,#954,#955,#956));
#953 = ORIENTED_EDGE('',*,*,#879,.F.);
#954 = ORIENTED_EDGE('',*,*,#809,.T.);
#955 = ORIENTED_EDGE('',*,*,#730,.T.);
#956 = ORIENTED_EDGE('',*,*,#928,.F.);
#957 = ADVANCED_FACE('',(#958),#794,.F.);
#958 = FACE_BOUND('',#959,.F.);
#959 = EDGE_LOOP('',(#960,#961,#982,#983));
#960 = ORIENTED_EDGE('',*,*,#900,.F.);
#961 = ORIENTED_EDGE('',*,*,#962,.F.);
#962 = EDGE_CURVE('',#778,#901,#963,.T.);
#963 = SEAM_CURVE('',#964,(#968,#975),.PCURVE_S1.);
#964 = LINE('',#965,#966);
#965 = CARTESIAN_POINT('',(54.971931,115.506924,14.75987));
#966 = VECTOR('',#967,1.);
#967 = DIRECTION('',(0.,0.,1.));
#968 = PCURVE('',#794,#969);
#969 = DEFINITIONAL_REPRESENTATION('',(#970),#974);
#970 = LINE('',#971,#972);
#971 = CARTESIAN_POINT('',(6.28318530718,-0.));
#972 = VECTOR('',#973,1.);
#973 = DIRECTION('',(0.,1.));
#974 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#975 = PCURVE('',#794,#976);
#976 = DEFINITIONAL_REPRESENTATION('',(#977),#981);
#977 = LINE('',#978,#979);
#978 = CARTESIAN_POINT('',(0.,-0.));
#979 = VECTOR('',#980,1.);
#980 = DIRECTION('',(0.,1.));
#981 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#982 = ORIENTED_EDGE('',*,*,#777,.T.);
#983 = ORIENTED_EDGE('',*,*,#962,.T.);
#984 = ( GEOMETRIC_REPRESENTATION_CONTEXT(3) 
GLOBAL_UNCERTAINTY_ASSIGNED_CONTEXT((#988)) GLOBAL_UNIT_ASSIGNED_CONTEXT
((#985,#986,#987)) REPRESENTATION_CONTEXT('Context #1',
  '3D Context with UNIT and UNCERTAINTY') );
#985 = ( LENGTH_UNIT() NAMED_UNIT(*) SI_UNIT(.MILLI.,.METRE.) );
#986 = ( NAMED_UNIT(*) PLANE_ANGLE_UNIT() SI_UNIT($,.RADIAN.) );
#987 = ( NAMED_UNIT(*) SI_UNIT($,.STERADIAN.) SOLID_ANGLE_UNIT() );
#988 = UNCERTAINTY_MEASURE_WITH_UNIT(LENGTH_MEASURE(1.E-07),#985,
  'distance_accuracy_value','confusion accuracy');
#989 = CONTEXT_DEPENDENT_SHAPE_REPRESENTATION(#990,#992);
#990 = ( REPRESENTATION_RELATIONSHIP('','',#579,#10) 
REPRESENTATION_RELATIONSHIP_WITH_TRANSFORMATION(#991) 
SHAPE_REPRESENTATION_RELATIONSHIP() );
#991 = ITEM_DEFINED_TRANSFORMATION('','',#11,#23);
#992 = PRODUCT_DEFINITION_SHAPE('Placement','Placement of an item',#993
  );
#993 = NEXT_ASSEMBLY_USAGE_OCCURRENCE('128','=>[0:1:1:4]','',#5,#574,$);
#994 = PRODUCT_RELATED_PRODUCT_CATEGORY('part',$,(#576));
#995 = MECHANICAL_DESIGN_GEOMETRIC_PRESENTATION_REPRESENTATION('',(#996)
  ,#561);
#996 = STYLED_ITEM('color',(#997),#158);
#997 = PRESENTATION_STYLE_ASSIGNMENT((#998));
#998 = SURFACE_STYLE_USAGE(.BOTH.,#999);
#999 = SURFACE_SIDE_STYLE('',(#1000));
#1000 = SURFACE_STYLE_FILL_AREA(#1001);
#1001 = FILL_AREA_STYLE('',(#1002));
#1002 = FILL_AREA_STYLE_COLOUR('',#1003);
#1003 = DRAUGHTING_PRE_DEFINED_COLOUR('green');
#1004 = MECHANICAL_DESIGN_GEOMETRIC_PRESENTATION_REPRESENTATION('',(
    #1005),#139);
#1005 = STYLED_ITEM('color',(#1006),#41);
#1006 = PRESENTATION_STYLE_ASSIGNMENT((#1007));
#1007 = SURFACE_STYLE_USAGE(.BOTH.,#1008);
#1008 = SURFACE_SIDE_STYLE('',(#1009));
#1009 = SURFACE_STYLE_FILL_AREA(#1010);
#1010 = FILL_AREA_STYLE('',(#1011));
#1011 = FILL_AREA_STYLE_COLOUR('',#1012);
#1012 = DRAUGHTING_PRE_DEFINED_COLOUR('blue');
#1013 = MECHANICAL_DESIGN_GEOMETRIC_PRESENTATION_REPRESENTATION('',(
    #1014),#984);
#1014 = STYLED_ITEM('color',(#1015),#580);
#1015 = PRESENTATION_STYLE_ASSIGNMENT((#1016));
#1016 = SURFACE_STYLE_USAGE(.BOTH.,#1017);
#1017 = SURFACE_SIDE_STYLE('',(#1018));
#1018 = SURFACE_STYLE_FILL_AREA(#1019);
#1019 = FILL_AREA_STYLE('',(#1020));
#1020 = FILL_AREA_STYLE_COLOUR('',#1021);
#1021 = DRAUGHTING_PRE_DEFINED_COLOUR('red');
ENDSEC;
END-ISO-10303-21;
