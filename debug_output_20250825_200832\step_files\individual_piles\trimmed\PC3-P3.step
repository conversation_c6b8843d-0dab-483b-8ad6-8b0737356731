ISO-10303-21;
HEADER;
FILE_DESCRIPTION(('Open CASCADE Model'),'2;1');
FILE_NAME('Pile_PC3-P3','2025-08-25T20:08:32',('Author'),('Open CASCADE'
    ),'Open CASCADE STEP processor 7.8','build123d','Unknown');
FILE_SCHEMA(('AUTOMOTIVE_DESIGN { 1 0 10303 214 1 1 1 1 }'));
ENDSEC;
DATA;
#1 = APPLICATION_PROTOCOL_DEFINITION('international standard',
  'automotive_design',2000,#2);
#2 = APPLICATION_CONTEXT(
  'core data for automotive mechanical design processes');
#3 = SHAPE_DEFINITION_REPRESENTATION(#4,#10);
#4 = PRODUCT_DEFINITION_SHAPE('','',#5);
#5 = PRODUCT_DEFINITION('design','',#6,#9);
#6 = PRODUCT_DEFINITION_FORMATION('','',#7);
#7 = PRODUCT('Pile_PC3-P3','Pile_PC3-P3','',(#8));
#8 = PRODUCT_CONTEXT('',#2,'mechanical');
#9 = PRODUCT_DEFINITION_CONTEXT('part definition',#2,'design');
#10 = SHAPE_REPRESENTATION('',(#11,#15,#19,#23),#27);
#11 = AXIS2_PLACEMENT_3D('',#12,#13,#14);
#12 = CARTESIAN_POINT('',(0.,0.,0.));
#13 = DIRECTION('',(0.,0.,1.));
#14 = DIRECTION('',(1.,0.,-0.));
#15 = AXIS2_PLACEMENT_3D('',#16,#17,#18);
#16 = CARTESIAN_POINT('',(73.317494,111.800042,12.973447));
#17 = DIRECTION('',(0.,0.,1.));
#18 = DIRECTION('',(1.,0.,0.));
#19 = AXIS2_PLACEMENT_3D('',#20,#21,#22);
#20 = CARTESIAN_POINT('',(0.,0.,0.));
#21 = DIRECTION('',(0.,0.,1.));
#22 = DIRECTION('',(1.,0.,-0.));
#23 = AXIS2_PLACEMENT_3D('',#24,#25,#26);
#24 = CARTESIAN_POINT('',(0.,0.,0.));
#25 = DIRECTION('',(0.,0.,1.));
#26 = DIRECTION('',(1.,0.,-0.));
#27 = ( GEOMETRIC_REPRESENTATION_CONTEXT(3) 
GLOBAL_UNCERTAINTY_ASSIGNED_CONTEXT((#31)) GLOBAL_UNIT_ASSIGNED_CONTEXT(
(#28,#29,#30)) REPRESENTATION_CONTEXT('Context #1',
  '3D Context with UNIT and UNCERTAINTY') );
#28 = ( LENGTH_UNIT() NAMED_UNIT(*) SI_UNIT(.MILLI.,.METRE.) );
#29 = ( NAMED_UNIT(*) PLANE_ANGLE_UNIT() SI_UNIT($,.RADIAN.) );
#30 = ( NAMED_UNIT(*) SI_UNIT($,.STERADIAN.) SOLID_ANGLE_UNIT() );
#31 = UNCERTAINTY_MEASURE_WITH_UNIT(LENGTH_MEASURE(1.E-07),#28,
  'distance_accuracy_value','confusion accuracy');
#32 = PRODUCT_RELATED_PRODUCT_CATEGORY('part',$,(#7));
#33 = SHAPE_DEFINITION_REPRESENTATION(#34,#40);
#34 = PRODUCT_DEFINITION_SHAPE('','',#35);
#35 = PRODUCT_DEFINITION('design','',#36,#39);
#36 = PRODUCT_DEFINITION_FORMATION('','',#37);
#37 = PRODUCT('PC3-P3_Part1','PC3-P3_Part1','',(#38));
#38 = PRODUCT_CONTEXT('',#2,'mechanical');
#39 = PRODUCT_DEFINITION_CONTEXT('part definition',#2,'design');
#40 = ADVANCED_BREP_SHAPE_REPRESENTATION('',(#11,#41),#139);
#41 = MANIFOLD_SOLID_BREP('',#42);
#42 = CLOSED_SHELL('',(#43,#131,#135));
#43 = ADVANCED_FACE('',(#44),#57,.T.);
#44 = FACE_BOUND('',#45,.T.);
#45 = EDGE_LOOP('',(#46,#80,#103,#130));
#46 = ORIENTED_EDGE('',*,*,#47,.F.);
#47 = EDGE_CURVE('',#48,#48,#50,.T.);
#48 = VERTEX_POINT('',#49);
#49 = CARTESIAN_POINT('',(0.275,-6.73555739531E-17,24.526553));
#50 = SURFACE_CURVE('',#51,(#56,#68),.PCURVE_S1.);
#51 = CIRCLE('',#52,0.275);
#52 = AXIS2_PLACEMENT_3D('',#53,#54,#55);
#53 = CARTESIAN_POINT('',(0.,0.,24.526553));
#54 = DIRECTION('',(0.,0.,1.));
#55 = DIRECTION('',(1.,0.,-0.));
#56 = PCURVE('',#57,#62);
#57 = CYLINDRICAL_SURFACE('',#58,0.275);
#58 = AXIS2_PLACEMENT_3D('',#59,#60,#61);
#59 = CARTESIAN_POINT('',(0.,0.,0.));
#60 = DIRECTION('',(0.,0.,1.));
#61 = DIRECTION('',(1.,0.,-0.));
#62 = DEFINITIONAL_REPRESENTATION('',(#63),#67);
#63 = LINE('',#64,#65);
#64 = CARTESIAN_POINT('',(0.,24.526553));
#65 = VECTOR('',#66,1.);
#66 = DIRECTION('',(1.,0.));
#67 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#68 = PCURVE('',#69,#74);
#69 = PLANE('',#70);
#70 = AXIS2_PLACEMENT_3D('',#71,#72,#73);
#71 = CARTESIAN_POINT('',(0.,0.,24.526553));
#72 = DIRECTION('',(0.,0.,1.));
#73 = DIRECTION('',(1.,0.,-0.));
#74 = DEFINITIONAL_REPRESENTATION('',(#75),#79);
#75 = CIRCLE('',#76,0.275);
#76 = AXIS2_PLACEMENT_2D('',#77,#78);
#77 = CARTESIAN_POINT('',(0.,0.));
#78 = DIRECTION('',(1.,0.));
#79 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#80 = ORIENTED_EDGE('',*,*,#81,.F.);
#81 = EDGE_CURVE('',#82,#48,#84,.T.);
#82 = VERTEX_POINT('',#83);
#83 = CARTESIAN_POINT('',(0.275,-6.73555739531E-17,0.));
#84 = SEAM_CURVE('',#85,(#89,#96),.PCURVE_S1.);
#85 = LINE('',#86,#87);
#86 = CARTESIAN_POINT('',(0.275,-6.73555739531E-17,0.));
#87 = VECTOR('',#88,1.);
#88 = DIRECTION('',(0.,0.,1.));
#89 = PCURVE('',#57,#90);
#90 = DEFINITIONAL_REPRESENTATION('',(#91),#95);
#91 = LINE('',#92,#93);
#92 = CARTESIAN_POINT('',(6.28318530718,-0.));
#93 = VECTOR('',#94,1.);
#94 = DIRECTION('',(0.,1.));
#95 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#96 = PCURVE('',#57,#97);
#97 = DEFINITIONAL_REPRESENTATION('',(#98),#102);
#98 = LINE('',#99,#100);
#99 = CARTESIAN_POINT('',(0.,-0.));
#100 = VECTOR('',#101,1.);
#101 = DIRECTION('',(0.,1.));
#102 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#103 = ORIENTED_EDGE('',*,*,#104,.T.);
#104 = EDGE_CURVE('',#82,#82,#105,.T.);
#105 = SURFACE_CURVE('',#106,(#111,#118),.PCURVE_S1.);
#106 = CIRCLE('',#107,0.275);
#107 = AXIS2_PLACEMENT_3D('',#108,#109,#110);
#108 = CARTESIAN_POINT('',(0.,0.,0.));
#109 = DIRECTION('',(0.,0.,1.));
#110 = DIRECTION('',(1.,0.,-0.));
#111 = PCURVE('',#57,#112);
#112 = DEFINITIONAL_REPRESENTATION('',(#113),#117);
#113 = LINE('',#114,#115);
#114 = CARTESIAN_POINT('',(0.,0.));
#115 = VECTOR('',#116,1.);
#116 = DIRECTION('',(1.,0.));
#117 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#118 = PCURVE('',#119,#124);
#119 = PLANE('',#120);
#120 = AXIS2_PLACEMENT_3D('',#121,#122,#123);
#121 = CARTESIAN_POINT('',(0.,0.,0.));
#122 = DIRECTION('',(0.,0.,1.));
#123 = DIRECTION('',(1.,0.,-0.));
#124 = DEFINITIONAL_REPRESENTATION('',(#125),#129);
#125 = CIRCLE('',#126,0.275);
#126 = AXIS2_PLACEMENT_2D('',#127,#128);
#127 = CARTESIAN_POINT('',(0.,0.));
#128 = DIRECTION('',(1.,0.));
#129 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#130 = ORIENTED_EDGE('',*,*,#81,.T.);
#131 = ADVANCED_FACE('',(#132),#69,.T.);
#132 = FACE_BOUND('',#133,.T.);
#133 = EDGE_LOOP('',(#134));
#134 = ORIENTED_EDGE('',*,*,#47,.T.);
#135 = ADVANCED_FACE('',(#136),#119,.F.);
#136 = FACE_BOUND('',#137,.T.);
#137 = EDGE_LOOP('',(#138));
#138 = ORIENTED_EDGE('',*,*,#104,.F.);
#139 = ( GEOMETRIC_REPRESENTATION_CONTEXT(3) 
GLOBAL_UNCERTAINTY_ASSIGNED_CONTEXT((#143)) GLOBAL_UNIT_ASSIGNED_CONTEXT
((#140,#141,#142)) REPRESENTATION_CONTEXT('Context #1',
  '3D Context with UNIT and UNCERTAINTY') );
#140 = ( LENGTH_UNIT() NAMED_UNIT(*) SI_UNIT(.MILLI.,.METRE.) );
#141 = ( NAMED_UNIT(*) PLANE_ANGLE_UNIT() SI_UNIT($,.RADIAN.) );
#142 = ( NAMED_UNIT(*) SI_UNIT($,.STERADIAN.) SOLID_ANGLE_UNIT() );
#143 = UNCERTAINTY_MEASURE_WITH_UNIT(LENGTH_MEASURE(1.E-07),#140,
  'distance_accuracy_value','confusion accuracy');
#144 = CONTEXT_DEPENDENT_SHAPE_REPRESENTATION(#145,#147);
#145 = ( REPRESENTATION_RELATIONSHIP('','',#40,#10) 
REPRESENTATION_RELATIONSHIP_WITH_TRANSFORMATION(#146) 
SHAPE_REPRESENTATION_RELATIONSHIP() );
#146 = ITEM_DEFINED_TRANSFORMATION('','',#11,#15);
#147 = PRODUCT_DEFINITION_SHAPE('Placement','Placement of an item',#148
  );
#148 = NEXT_ASSEMBLY_USAGE_OCCURRENCE('135','=>[0:1:1:2]','',#5,#35,$);
#149 = PRODUCT_RELATED_PRODUCT_CATEGORY('part',$,(#37));
#150 = SHAPE_DEFINITION_REPRESENTATION(#151,#157);
#151 = PRODUCT_DEFINITION_SHAPE('','',#152);
#152 = PRODUCT_DEFINITION('design','',#153,#156);
#153 = PRODUCT_DEFINITION_FORMATION('','',#154);
#154 = PRODUCT('PC3-P3_Part2','PC3-P3_Part2','',(#155));
#155 = PRODUCT_CONTEXT('',#2,'mechanical');
#156 = PRODUCT_DEFINITION_CONTEXT('part definition',#2,'design');
#157 = ADVANCED_BREP_SHAPE_REPRESENTATION('',(#11,#158),#597);
#158 = MANIFOLD_SOLID_BREP('',#159);
#159 = CLOSED_SHELL('',(#160,#419,#469,#538,#564,#591));
#160 = ADVANCED_FACE('',(#161),#175,.T.);
#161 = FACE_BOUND('',#162,.T.);
#162 = EDGE_LOOP('',(#163,#193,#222,#274,#307,#370,#392,#393));
#163 = ORIENTED_EDGE('',*,*,#164,.T.);
#164 = EDGE_CURVE('',#165,#167,#169,.T.);
#165 = VERTEX_POINT('',#166);
#166 = CARTESIAN_POINT('',(73.592494,111.800042,12.973447));
#167 = VERTEX_POINT('',#168);
#168 = CARTESIAN_POINT('',(77.633945884327,111.800042,19.973447));
#169 = SEAM_CURVE('',#170,(#174,#186),.PCURVE_S1.);
#170 = LINE('',#171,#172);
#171 = CARTESIAN_POINT('',(73.592494,111.800042,12.973447));
#172 = VECTOR('',#173,1.);
#173 = DIRECTION('',(0.5,-1.224646799147E-16,0.866025403784));
#174 = PCURVE('',#175,#180);
#175 = CONICAL_SURFACE('',#176,0.275,0.523598775598);
#176 = AXIS2_PLACEMENT_3D('',#177,#178,#179);
#177 = CARTESIAN_POINT('',(73.317494,111.800042,12.973447));
#178 = DIRECTION('',(0.,0.,1.));
#179 = DIRECTION('',(1.,0.,0.));
#180 = DEFINITIONAL_REPRESENTATION('',(#181),#185);
#181 = LINE('',#182,#183);
#182 = CARTESIAN_POINT('',(6.28318530718,-0.));
#183 = VECTOR('',#184,1.);
#184 = DIRECTION('',(0.,1.));
#185 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#186 = PCURVE('',#175,#187);
#187 = DEFINITIONAL_REPRESENTATION('',(#188),#192);
#188 = LINE('',#189,#190);
#189 = CARTESIAN_POINT('',(0.,-0.));
#190 = VECTOR('',#191,1.);
#191 = DIRECTION('',(0.,1.));
#192 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#193 = ORIENTED_EDGE('',*,*,#194,.F.);
#194 = EDGE_CURVE('',#195,#167,#197,.T.);
#195 = VERTEX_POINT('',#196);
#196 = CARTESIAN_POINT('',(69.145248888396,112.906445,19.973447));
#197 = SURFACE_CURVE('',#198,(#203,#210),.PCURVE_S1.);
#198 = CIRCLE('',#199,4.316451884327);
#199 = AXIS2_PLACEMENT_3D('',#200,#201,#202);
#200 = CARTESIAN_POINT('',(73.317494,111.800042,19.973447));
#201 = DIRECTION('',(0.,0.,1.));
#202 = DIRECTION('',(1.,0.,0.));
#203 = PCURVE('',#175,#204);
#204 = DEFINITIONAL_REPRESENTATION('',(#205),#209);
#205 = LINE('',#206,#207);
#206 = CARTESIAN_POINT('',(0.,7.));
#207 = VECTOR('',#208,1.);
#208 = DIRECTION('',(1.,0.));
#209 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#210 = PCURVE('',#211,#216);
#211 = PLANE('',#212);
#212 = AXIS2_PLACEMENT_3D('',#213,#214,#215);
#213 = CARTESIAN_POINT('',(73.317494,111.800042,19.973447));
#214 = DIRECTION('',(0.,0.,1.));
#215 = DIRECTION('',(1.,0.,0.));
#216 = DEFINITIONAL_REPRESENTATION('',(#217),#221);
#217 = CIRCLE('',#218,4.316451884327);
#218 = AXIS2_PLACEMENT_2D('',#219,#220);
#219 = CARTESIAN_POINT('',(0.,0.));
#220 = DIRECTION('',(1.,0.));
#221 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#222 = ORIENTED_EDGE('',*,*,#223,.F.);
#223 = EDGE_CURVE('',#224,#195,#226,.T.);
#224 = VERTEX_POINT('',#225);
#225 = CARTESIAN_POINT('',(72.917494,112.906445,14.534872656828));
#226 = SURFACE_CURVE('',#227,(#232,#262),.PCURVE_S1.);
#227 = HYPERBOLA('',#228,1.916346209647,1.106403);
#228 = AXIS2_PLACEMENT_3D('',#229,#230,#231);
#229 = CARTESIAN_POINT('',(73.317494,112.906445,12.497133027919));
#230 = DIRECTION('',(0.,-1.,0.));
#231 = DIRECTION('',(0.,0.,1.));
#232 = PCURVE('',#175,#233);
#233 = DEFINITIONAL_REPRESENTATION('',(#234),#261);
#234 = B_SPLINE_CURVE_WITH_KNOTS('',7,(#235,#236,#237,#238,#239,#240,
    #241,#242,#243,#244,#245,#246,#247,#248,#249,#250,#251,#252,#253,
    #254,#255,#256,#257,#258,#259,#260),.UNSPECIFIED.,.F.,.F.,(8,6,6,6,8
    ),(-1.224991942673,-0.409338631873,0.202401351227,1.120011325878,
    2.037621300528),.UNSPECIFIED.);
#235 = CARTESIAN_POINT('',(0.571444599553,3.066913127371));
#236 = CARTESIAN_POINT('',(0.634465201421,2.719645105527));
#237 = CARTESIAN_POINT('',(0.70469137886,2.42850265209));
#238 = CARTESIAN_POINT('',(0.782706720734,2.18578479428));
#239 = CARTESIAN_POINT('',(0.868895553691,1.985656741258));
#240 = CARTESIAN_POINT('',(0.963299700981,1.823726189775));
#241 = CARTESIAN_POINT('',(1.06503444736,1.696814108686));
#242 = CARTESIAN_POINT('',(1.252980718257,1.532352872363));
#243 = CARTESIAN_POINT('',(1.336714923795,1.480394999953));
#244 = CARTESIAN_POINT('',(1.422931642782,1.44608343047));
#245 = CARTESIAN_POINT('',(1.510688045566,1.42888553056));
#246 = CARTESIAN_POINT('',(1.598823576562,1.428588057134));
#247 = CARTESIAN_POINT('',(1.686198297303,1.445290721536));
#248 = CARTESIAN_POINT('',(1.900276868226,1.530612012209));
#249 = CARTESIAN_POINT('',(2.024800870287,1.621012912345));
#250 = CARTESIAN_POINT('',(2.142089996233,1.752059120635));
#251 = CARTESIAN_POINT('',(2.249581319315,1.926836749811));
#252 = CARTESIAN_POINT('',(2.346651163826,2.150188522973));
#253 = CARTESIAN_POINT('',(2.433510369369,2.429027946152));
#254 = CARTESIAN_POINT('',(2.588132322191,3.11699033226));
#255 = CARTESIAN_POINT('',(2.655894978954,3.52611325124));
#256 = CARTESIAN_POINT('',(2.71476909542,4.01003312001));
#257 = CARTESIAN_POINT('',(2.765855325427,4.581144134707));
#258 = CARTESIAN_POINT('',(2.810209018464,5.255270588862));
#259 = CARTESIAN_POINT('',(2.848776604062,6.052693477327));
#260 = CARTESIAN_POINT('',(2.882377164892,7.));
#261 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#262 = PCURVE('',#263,#268);
#263 = PLANE('',#264);
#264 = AXIS2_PLACEMENT_3D('',#265,#266,#267);
#265 = CARTESIAN_POINT('',(51.710833,112.906445,47.5));
#266 = DIRECTION('',(0.,-1.,0.));
#267 = DIRECTION('',(1.,0.,0.));
#268 = DEFINITIONAL_REPRESENTATION('',(#269),#273);
#269 = ( BOUNDED_CURVE() B_SPLINE_CURVE(2,(#270,#271,#272),
.UNSPECIFIED.,.F.,.F.) B_SPLINE_CURVE_WITH_KNOTS((3,3),(-1.224991942673,
2.037621300528),.PIECEWISE_BEZIER_KNOTS.) CURVE() 
GEOMETRIC_REPRESENTATION_ITEM() RATIONAL_B_SPLINE_CURVE((1.,
2.65311082285,1.)) REPRESENTATION_ITEM('') );
#270 = CARTESIAN_POINT('',(23.3273271,-31.45963987262));
#271 = CARTESIAN_POINT('',(21.432518363041,-34.2201175966));
#272 = CARTESIAN_POINT('',(17.434415888396,-27.526553));
#273 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#274 = ORIENTED_EDGE('',*,*,#275,.F.);
#275 = EDGE_CURVE('',#276,#224,#278,.T.);
#276 = VERTEX_POINT('',#277);
#277 = CARTESIAN_POINT('',(72.917494,112.30648,13.614915808038));
#278 = SURFACE_CURVE('',#279,(#284,#295),.PCURVE_S1.);
#279 = HYPERBOLA('',#280,0.692820323028,0.4);
#280 = AXIS2_PLACEMENT_3D('',#281,#282,#283);
#281 = CARTESIAN_POINT('',(72.917494,111.800042,12.497133027919));
#282 = DIRECTION('',(-1.,0.,-0.));
#283 = DIRECTION('',(-0.,0.,1.));
#284 = PCURVE('',#175,#285);
#285 = DEFINITIONAL_REPRESENTATION('',(#286),#294);
#286 = B_SPLINE_CURVE_WITH_KNOTS('',6,(#287,#288,#289,#290,#291,#292,
    #293),.UNSPECIFIED.,.F.,.F.,(7,7),(0.961136206924,1.791542825409),
  .PIECEWISE_BEZIER_KNOTS.);
#287 = CARTESIAN_POINT('',(2.301382690323,0.561920124241));
#288 = CARTESIAN_POINT('',(2.209026739692,0.668940072717));
#289 = CARTESIAN_POINT('',(2.12809510413,0.799825090339));
#290 = CARTESIAN_POINT('',(2.058237443732,0.95826230281));
#291 = CARTESIAN_POINT('',(1.998008447199,1.149322758311));
#292 = CARTESIAN_POINT('',(1.94605999557,1.379837641433));
#293 = CARTESIAN_POINT('',(1.901163951684,1.659444296263));
#294 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#295 = PCURVE('',#296,#301);
#296 = PLANE('',#297);
#297 = AXIS2_PLACEMENT_3D('',#298,#299,#300);
#298 = CARTESIAN_POINT('',(72.917494,112.906445,47.5));
#299 = DIRECTION('',(-1.,-0.,-0.));
#300 = DIRECTION('',(0.,-1.,0.));
#301 = DEFINITIONAL_REPRESENTATION('',(#302),#306);
#302 = ( BOUNDED_CURVE() B_SPLINE_CURVE(2,(#303,#304,#305),
.UNSPECIFIED.,.F.,.F.) B_SPLINE_CURVE_WITH_KNOTS((3,3),(0.961136206924,
1.791542825409),.PIECEWISE_BEZIER_KNOTS.) CURVE() 
GEOMETRIC_REPRESENTATION_ITEM() RATIONAL_B_SPLINE_CURVE((1.,
1.087442349315,1.)) REPRESENTATION_ITEM('') );
#303 = CARTESIAN_POINT('',(0.6599615,-33.96463287575));
#304 = CARTESIAN_POINT('',(0.424458430793,-33.66083334583));
#305 = CARTESIAN_POINT('',(-5.999650000001E-02,-32.86710870373));
#306 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#307 = ORIENTED_EDGE('',*,*,#308,.F.);
#308 = EDGE_CURVE('',#309,#276,#311,.T.);
#309 = VERTEX_POINT('',#310);
#310 = CARTESIAN_POINT('',(77.604133408892,112.30648,19.973447));
#311 = SURFACE_CURVE('',#312,(#317,#358),.PCURVE_S1.);
#312 = HYPERBOLA('',#313,0.877176346884,0.506438);
#313 = AXIS2_PLACEMENT_3D('',#314,#315,#316);
#314 = CARTESIAN_POINT('',(73.317494,112.30648,12.497133027919));
#315 = DIRECTION('',(0.,-1.,0.));
#316 = DIRECTION('',(0.,0.,1.));
#317 = PCURVE('',#175,#318);
#318 = DEFINITIONAL_REPRESENTATION('',(#319),#357);
#319 = B_SPLINE_CURVE_WITH_KNOTS('',8,(#320,#321,#322,#323,#324,#325,
    #326,#327,#328,#329,#330,#331,#332,#333,#334,#335,#336,#337,#338,
    #339,#340,#341,#342,#343,#344,#345,#346,#347,#348,#349,#350,#351,
    #352,#353,#354,#355,#356),.UNSPECIFIED.,.F.,.F.,(9,7,7,7,7,9),(
    -2.832474962845,-1.598977848284,-0.673855012363,1.998711457723E-02,
    1.060750304988,2.101513495399),.UNSPECIFIED.);
#320 = CARTESIAN_POINT('',(0.117598246011,7.));
#321 = CARTESIAN_POINT('',(0.135688619437,5.855210254458));
#322 = CARTESIAN_POINT('',(0.156945217624,4.913550652526));
#323 = CARTESIAN_POINT('',(0.18200227101,4.133553949271));
#324 = CARTESIAN_POINT('',(0.211652934122,3.484036817935));
#325 = CARTESIAN_POINT('',(0.246853190726,2.941001246097));
#326 = CARTESIAN_POINT('',(0.288802140939,2.485693447814));
#327 = CARTESIAN_POINT('',(0.338956069625,2.10338590184));
#328 = CARTESIAN_POINT('',(0.443741914712,1.541744794308));
#329 = CARTESIAN_POINT('',(0.494118755136,1.335562271728));
#330 = CARTESIAN_POINT('',(0.550607548578,1.158995805451));
#331 = CARTESIAN_POINT('',(0.613850718783,1.008124813081));
#332 = CARTESIAN_POINT('',(0.684475045017,0.879804978696));
#333 = CARTESIAN_POINT('',(0.76292687032,0.771525444212));
#334 = CARTESIAN_POINT('',(0.849239870699,0.681313321642));
#335 = CARTESIAN_POINT('',(1.013001599867,0.552433574339));
#336 = CARTESIAN_POINT('',(1.087272186696,0.506518080065));
#337 = CARTESIAN_POINT('',(1.165380564208,0.469288235695));
#338 = CARTESIAN_POINT('',(1.246887785025,0.44026031999));
#339 = CARTESIAN_POINT('',(1.331119163318,0.419085175713));
#340 = CARTESIAN_POINT('',(1.417185163373,0.405538605875));
#341 = CARTESIAN_POINT('',(1.504069165706,0.399516914217));
#342 = CARTESIAN_POINT('',(1.720851528309,0.403318603479));
#343 = CARTESIAN_POINT('',(1.850533478064,0.422569915986));
#344 = CARTESIAN_POINT('',(1.976484912602,0.458850348691));
#345 = CARTESIAN_POINT('',(2.095387681469,0.512831490655));
#346 = CARTESIAN_POINT('',(2.205026522403,0.585800751378));
#347 = CARTESIAN_POINT('',(2.304757276889,0.679720178617));
#348 = CARTESIAN_POINT('',(2.394592286257,0.797340158014));
#349 = CARTESIAN_POINT('',(2.555465386916,1.087463543002));
#350 = CARTESIAN_POINT('',(2.626502634428,1.259966864854));
#351 = CARTESIAN_POINT('',(2.688627708806,1.463653552759));
#352 = CARTESIAN_POINT('',(2.74277326196,1.703253959347));
#353 = CARTESIAN_POINT('',(2.789938507873,1.984696072016));
#354 = CARTESIAN_POINT('',(2.831044452782,2.315388650847));
#355 = CARTESIAN_POINT('',(2.86691280234,2.704646152536));
#356 = CARTESIAN_POINT('',(2.898258032359,3.164323107268));
#357 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#358 = PCURVE('',#359,#364);
#359 = PLANE('',#360);
#360 = AXIS2_PLACEMENT_3D('',#361,#362,#363);
#361 = CARTESIAN_POINT('',(72.917494,112.30648,47.5));
#362 = DIRECTION('',(0.,-1.,0.));
#363 = DIRECTION('',(1.,0.,0.));
#364 = DEFINITIONAL_REPRESENTATION('',(#365),#369);
#365 = ( BOUNDED_CURVE() B_SPLINE_CURVE(2,(#366,#367,#368),
.UNSPECIFIED.,.F.,.F.) B_SPLINE_CURVE_WITH_KNOTS((3,3),(-2.832474962845,
2.101513495399),.PIECEWISE_BEZIER_KNOTS.) CURVE() 
GEOMETRIC_REPRESENTATION_ITEM() RATIONAL_B_SPLINE_CURVE((1.,
5.935902060898,1.)) REPRESENTATION_ITEM('') );
#366 = CARTESIAN_POINT('',(4.686639408892,-27.526553));
#367 = CARTESIAN_POINT('',(0.431880852324,-34.84511229486));
#368 = CARTESIAN_POINT('',(-1.64,-31.36222989273));
#369 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#370 = ORIENTED_EDGE('',*,*,#371,.F.);
#371 = EDGE_CURVE('',#167,#309,#372,.T.);
#372 = SURFACE_CURVE('',#373,(#378,#385),.PCURVE_S1.);
#373 = CIRCLE('',#374,4.316451884327);
#374 = AXIS2_PLACEMENT_3D('',#375,#376,#377);
#375 = CARTESIAN_POINT('',(73.317494,111.800042,19.973447));
#376 = DIRECTION('',(0.,0.,1.));
#377 = DIRECTION('',(1.,0.,0.));
#378 = PCURVE('',#175,#379);
#379 = DEFINITIONAL_REPRESENTATION('',(#380),#384);
#380 = LINE('',#381,#382);
#381 = CARTESIAN_POINT('',(0.,7.));
#382 = VECTOR('',#383,1.);
#383 = DIRECTION('',(1.,0.));
#384 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#385 = PCURVE('',#211,#386);
#386 = DEFINITIONAL_REPRESENTATION('',(#387),#391);
#387 = CIRCLE('',#388,4.316451884327);
#388 = AXIS2_PLACEMENT_2D('',#389,#390);
#389 = CARTESIAN_POINT('',(0.,0.));
#390 = DIRECTION('',(1.,0.));
#391 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#392 = ORIENTED_EDGE('',*,*,#164,.F.);
#393 = ORIENTED_EDGE('',*,*,#394,.T.);
#394 = EDGE_CURVE('',#165,#165,#395,.T.);
#395 = SURFACE_CURVE('',#396,(#401,#408),.PCURVE_S1.);
#396 = CIRCLE('',#397,0.275);
#397 = AXIS2_PLACEMENT_3D('',#398,#399,#400);
#398 = CARTESIAN_POINT('',(73.317494,111.800042,12.973447));
#399 = DIRECTION('',(0.,0.,1.));
#400 = DIRECTION('',(1.,0.,0.));
#401 = PCURVE('',#175,#402);
#402 = DEFINITIONAL_REPRESENTATION('',(#403),#407);
#403 = LINE('',#404,#405);
#404 = CARTESIAN_POINT('',(0.,0.));
#405 = VECTOR('',#406,1.);
#406 = DIRECTION('',(1.,0.));
#407 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#408 = PCURVE('',#409,#414);
#409 = CYLINDRICAL_SURFACE('',#410,0.275);
#410 = AXIS2_PLACEMENT_3D('',#411,#412,#413);
#411 = CARTESIAN_POINT('',(73.317494,111.800042,12.973447));
#412 = DIRECTION('',(0.,0.,1.));
#413 = DIRECTION('',(1.,0.,0.));
#414 = DEFINITIONAL_REPRESENTATION('',(#415),#418);
#415 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#416,#417),.UNSPECIFIED.,.F.,.F.,
  (2,2),(0.,6.28318530718),.PIECEWISE_BEZIER_KNOTS.);
#416 = CARTESIAN_POINT('',(0.,0.));
#417 = CARTESIAN_POINT('',(6.28318530718,0.));
#418 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#419 = ADVANCED_FACE('',(#420),#409,.F.);
#420 = FACE_BOUND('',#421,.F.);
#421 = EDGE_LOOP('',(#422,#446,#467,#468));
#422 = ORIENTED_EDGE('',*,*,#423,.F.);
#423 = EDGE_CURVE('',#424,#424,#426,.T.);
#424 = VERTEX_POINT('',#425);
#425 = CARTESIAN_POINT('',(73.592494,111.800042,19.973447));
#426 = SURFACE_CURVE('',#427,(#432,#439),.PCURVE_S1.);
#427 = CIRCLE('',#428,0.275);
#428 = AXIS2_PLACEMENT_3D('',#429,#430,#431);
#429 = CARTESIAN_POINT('',(73.317494,111.800042,19.973447));
#430 = DIRECTION('',(0.,0.,1.));
#431 = DIRECTION('',(1.,0.,0.));
#432 = PCURVE('',#409,#433);
#433 = DEFINITIONAL_REPRESENTATION('',(#434),#438);
#434 = LINE('',#435,#436);
#435 = CARTESIAN_POINT('',(0.,7.));
#436 = VECTOR('',#437,1.);
#437 = DIRECTION('',(1.,0.));
#438 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#439 = PCURVE('',#211,#440);
#440 = DEFINITIONAL_REPRESENTATION('',(#441),#445);
#441 = CIRCLE('',#442,0.275);
#442 = AXIS2_PLACEMENT_2D('',#443,#444);
#443 = CARTESIAN_POINT('',(0.,0.));
#444 = DIRECTION('',(1.,0.));
#445 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#446 = ORIENTED_EDGE('',*,*,#447,.F.);
#447 = EDGE_CURVE('',#165,#424,#448,.T.);
#448 = SEAM_CURVE('',#449,(#453,#460),.PCURVE_S1.);
#449 = LINE('',#450,#451);
#450 = CARTESIAN_POINT('',(73.592494,111.800042,12.973447));
#451 = VECTOR('',#452,1.);
#452 = DIRECTION('',(0.,0.,1.));
#453 = PCURVE('',#409,#454);
#454 = DEFINITIONAL_REPRESENTATION('',(#455),#459);
#455 = LINE('',#456,#457);
#456 = CARTESIAN_POINT('',(6.28318530718,-0.));
#457 = VECTOR('',#458,1.);
#458 = DIRECTION('',(0.,1.));
#459 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#460 = PCURVE('',#409,#461);
#461 = DEFINITIONAL_REPRESENTATION('',(#462),#466);
#462 = LINE('',#463,#464);
#463 = CARTESIAN_POINT('',(0.,-0.));
#464 = VECTOR('',#465,1.);
#465 = DIRECTION('',(0.,1.));
#466 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#467 = ORIENTED_EDGE('',*,*,#394,.T.);
#468 = ORIENTED_EDGE('',*,*,#447,.T.);
#469 = ADVANCED_FACE('',(#470,#535),#211,.T.);
#470 = FACE_BOUND('',#471,.T.);
#471 = EDGE_LOOP('',(#472,#473,#474,#495,#516));
#472 = ORIENTED_EDGE('',*,*,#194,.T.);
#473 = ORIENTED_EDGE('',*,*,#371,.T.);
#474 = ORIENTED_EDGE('',*,*,#475,.F.);
#475 = EDGE_CURVE('',#476,#309,#478,.T.);
#476 = VERTEX_POINT('',#477);
#477 = CARTESIAN_POINT('',(72.917494,112.30648,19.973447));
#478 = SURFACE_CURVE('',#479,(#483,#489),.PCURVE_S1.);
#479 = LINE('',#480,#481);
#480 = CARTESIAN_POINT('',(73.117494,112.30648,19.973447));
#481 = VECTOR('',#482,1.);
#482 = DIRECTION('',(1.,0.,-0.));
#483 = PCURVE('',#211,#484);
#484 = DEFINITIONAL_REPRESENTATION('',(#485),#488);
#485 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#486,#487),.UNSPECIFIED.,.F.,.F.,
  (2,2),(-0.2,4.516451884327),.PIECEWISE_BEZIER_KNOTS.);
#486 = CARTESIAN_POINT('',(-0.4,0.506438));
#487 = CARTESIAN_POINT('',(4.316451884327,0.506438));
#488 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#489 = PCURVE('',#359,#490);
#490 = DEFINITIONAL_REPRESENTATION('',(#491),#494);
#491 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#492,#493),.UNSPECIFIED.,.F.,.F.,
  (2,2),(-0.2,4.516451884327),.PIECEWISE_BEZIER_KNOTS.);
#492 = CARTESIAN_POINT('',(0.,-27.526553));
#493 = CARTESIAN_POINT('',(4.716451884327,-27.526553));
#494 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#495 = ORIENTED_EDGE('',*,*,#496,.F.);
#496 = EDGE_CURVE('',#497,#476,#499,.T.);
#497 = VERTEX_POINT('',#498);
#498 = CARTESIAN_POINT('',(72.917494,112.906445,19.973447));
#499 = SURFACE_CURVE('',#500,(#504,#510),.PCURVE_S1.);
#500 = LINE('',#501,#502);
#501 = CARTESIAN_POINT('',(72.917494,112.3532435,19.973447));
#502 = VECTOR('',#503,1.);
#503 = DIRECTION('',(0.,-1.,0.));
#504 = PCURVE('',#211,#505);
#505 = DEFINITIONAL_REPRESENTATION('',(#506),#509);
#506 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#507,#508),.UNSPECIFIED.,.F.,.F.,
  (2,2),(-0.5532015,4.676350000001E-02),.PIECEWISE_BEZIER_KNOTS.);
#507 = CARTESIAN_POINT('',(-0.4,1.106403));
#508 = CARTESIAN_POINT('',(-0.4,0.506438));
#509 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#510 = PCURVE('',#296,#511);
#511 = DEFINITIONAL_REPRESENTATION('',(#512),#515);
#512 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#513,#514),.UNSPECIFIED.,.F.,.F.,
  (2,2),(-0.5532015,4.676350000001E-02),.PIECEWISE_BEZIER_KNOTS.);
#513 = CARTESIAN_POINT('',(-1.42108547152E-14,-27.526553));
#514 = CARTESIAN_POINT('',(0.599965,-27.526553));
#515 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#516 = ORIENTED_EDGE('',*,*,#517,.F.);
#517 = EDGE_CURVE('',#195,#497,#518,.T.);
#518 = SURFACE_CURVE('',#519,(#523,#529),.PCURVE_S1.);
#519 = LINE('',#520,#521);
#520 = CARTESIAN_POINT('',(62.5141635,112.906445,19.973447));
#521 = VECTOR('',#522,1.);
#522 = DIRECTION('',(1.,0.,-0.));
#523 = PCURVE('',#211,#524);
#524 = DEFINITIONAL_REPRESENTATION('',(#525),#528);
#525 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#526,#527),.UNSPECIFIED.,.F.,.F.,
  (2,2),(6.486878615673,10.4033305),.PIECEWISE_BEZIER_KNOTS.);
#526 = CARTESIAN_POINT('',(-4.316451884327,1.106403));
#527 = CARTESIAN_POINT('',(-0.4,1.106403));
#528 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#529 = PCURVE('',#263,#530);
#530 = DEFINITIONAL_REPRESENTATION('',(#531),#534);
#531 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#532,#533),.UNSPECIFIED.,.F.,.F.,
  (2,2),(6.486878615673,10.4033305),.PIECEWISE_BEZIER_KNOTS.);
#532 = CARTESIAN_POINT('',(17.290209115673,-27.526553));
#533 = CARTESIAN_POINT('',(21.206661,-27.526553));
#534 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#535 = FACE_BOUND('',#536,.T.);
#536 = EDGE_LOOP('',(#537));
#537 = ORIENTED_EDGE('',*,*,#423,.F.);
#538 = ADVANCED_FACE('',(#539),#359,.F.);
#539 = FACE_BOUND('',#540,.F.);
#540 = EDGE_LOOP('',(#541,#542,#563));
#541 = ORIENTED_EDGE('',*,*,#475,.F.);
#542 = ORIENTED_EDGE('',*,*,#543,.T.);
#543 = EDGE_CURVE('',#476,#276,#544,.T.);
#544 = SURFACE_CURVE('',#545,(#549,#556),.PCURVE_S1.);
#545 = LINE('',#546,#547);
#546 = CARTESIAN_POINT('',(72.917494,112.30648,47.5));
#547 = VECTOR('',#548,1.);
#548 = DIRECTION('',(0.,0.,-1.));
#549 = PCURVE('',#359,#550);
#550 = DEFINITIONAL_REPRESENTATION('',(#551),#555);
#551 = LINE('',#552,#553);
#552 = CARTESIAN_POINT('',(0.,0.));
#553 = VECTOR('',#554,1.);
#554 = DIRECTION('',(0.,-1.));
#555 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#556 = PCURVE('',#296,#557);
#557 = DEFINITIONAL_REPRESENTATION('',(#558),#562);
#558 = LINE('',#559,#560);
#559 = CARTESIAN_POINT('',(0.599965,0.));
#560 = VECTOR('',#561,1.);
#561 = DIRECTION('',(0.,-1.));
#562 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#563 = ORIENTED_EDGE('',*,*,#308,.F.);
#564 = ADVANCED_FACE('',(#565),#296,.F.);
#565 = FACE_BOUND('',#566,.F.);
#566 = EDGE_LOOP('',(#567,#568,#589,#590));
#567 = ORIENTED_EDGE('',*,*,#496,.F.);
#568 = ORIENTED_EDGE('',*,*,#569,.T.);
#569 = EDGE_CURVE('',#497,#224,#570,.T.);
#570 = SURFACE_CURVE('',#571,(#575,#582),.PCURVE_S1.);
#571 = LINE('',#572,#573);
#572 = CARTESIAN_POINT('',(72.917494,112.906445,47.5));
#573 = VECTOR('',#574,1.);
#574 = DIRECTION('',(0.,0.,-1.));
#575 = PCURVE('',#296,#576);
#576 = DEFINITIONAL_REPRESENTATION('',(#577),#581);
#577 = LINE('',#578,#579);
#578 = CARTESIAN_POINT('',(0.,0.));
#579 = VECTOR('',#580,1.);
#580 = DIRECTION('',(0.,-1.));
#581 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#582 = PCURVE('',#263,#583);
#583 = DEFINITIONAL_REPRESENTATION('',(#584),#588);
#584 = LINE('',#585,#586);
#585 = CARTESIAN_POINT('',(21.206661,0.));
#586 = VECTOR('',#587,1.);
#587 = DIRECTION('',(0.,-1.));
#588 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#589 = ORIENTED_EDGE('',*,*,#275,.F.);
#590 = ORIENTED_EDGE('',*,*,#543,.F.);
#591 = ADVANCED_FACE('',(#592),#263,.F.);
#592 = FACE_BOUND('',#593,.F.);
#593 = EDGE_LOOP('',(#594,#595,#596));
#594 = ORIENTED_EDGE('',*,*,#569,.F.);
#595 = ORIENTED_EDGE('',*,*,#517,.F.);
#596 = ORIENTED_EDGE('',*,*,#223,.F.);
#597 = ( GEOMETRIC_REPRESENTATION_CONTEXT(3) 
GLOBAL_UNCERTAINTY_ASSIGNED_CONTEXT((#601)) GLOBAL_UNIT_ASSIGNED_CONTEXT
((#598,#599,#600)) REPRESENTATION_CONTEXT('Context #1',
  '3D Context with UNIT and UNCERTAINTY') );
#598 = ( LENGTH_UNIT() NAMED_UNIT(*) SI_UNIT(.MILLI.,.METRE.) );
#599 = ( NAMED_UNIT(*) PLANE_ANGLE_UNIT() SI_UNIT($,.RADIAN.) );
#600 = ( NAMED_UNIT(*) SI_UNIT($,.STERADIAN.) SOLID_ANGLE_UNIT() );
#601 = UNCERTAINTY_MEASURE_WITH_UNIT(LENGTH_MEASURE(1.E-07),#598,
  'distance_accuracy_value','confusion accuracy');
#602 = CONTEXT_DEPENDENT_SHAPE_REPRESENTATION(#603,#605);
#603 = ( REPRESENTATION_RELATIONSHIP('','',#157,#10) 
REPRESENTATION_RELATIONSHIP_WITH_TRANSFORMATION(#604) 
SHAPE_REPRESENTATION_RELATIONSHIP() );
#604 = ITEM_DEFINED_TRANSFORMATION('','',#11,#19);
#605 = PRODUCT_DEFINITION_SHAPE('Placement','Placement of an item',#606
  );
#606 = NEXT_ASSEMBLY_USAGE_OCCURRENCE('136','=>[0:1:1:3]','',#5,#152,$);
#607 = PRODUCT_RELATED_PRODUCT_CATEGORY('part',$,(#154));
#608 = SHAPE_DEFINITION_REPRESENTATION(#609,#615);
#609 = PRODUCT_DEFINITION_SHAPE('','',#610);
#610 = PRODUCT_DEFINITION('design','',#611,#614);
#611 = PRODUCT_DEFINITION_FORMATION('','',#612);
#612 = PRODUCT('PC3-P3_Part3','PC3-P3_Part3','',(#613));
#613 = PRODUCT_CONTEXT('',#2,'mechanical');
#614 = PRODUCT_DEFINITION_CONTEXT('part definition',#2,'design');
#615 = ADVANCED_BREP_SHAPE_REPRESENTATION('',(#11,#616),#1020);
#616 = MANIFOLD_SOLID_BREP('',#617);
#617 = CLOSED_SHELL('',(#618,#740,#841,#888,#959,#986,#993));
#618 = ADVANCED_FACE('',(#619),#634,.T.);
#619 = FACE_BOUND('',#620,.T.);
#620 = EDGE_LOOP('',(#621,#657,#683,#716));
#621 = ORIENTED_EDGE('',*,*,#622,.T.);
#622 = EDGE_CURVE('',#623,#625,#627,.T.);
#623 = VERTEX_POINT('',#624);
#624 = CARTESIAN_POINT('',(69.145248888396,112.906445,19.973447));
#625 = VERTEX_POINT('',#626);
#626 = CARTESIAN_POINT('',(77.604133408892,112.30648,19.973447));
#627 = SURFACE_CURVE('',#628,(#633,#645),.PCURVE_S1.);
#628 = CIRCLE('',#629,4.316451884327);
#629 = AXIS2_PLACEMENT_3D('',#630,#631,#632);
#630 = CARTESIAN_POINT('',(73.317494,111.800042,19.973447));
#631 = DIRECTION('',(0.,0.,1.));
#632 = DIRECTION('',(-0.966591363326,0.256322328998,0.));
#633 = PCURVE('',#634,#639);
#634 = CYLINDRICAL_SURFACE('',#635,4.316451884327);
#635 = AXIS2_PLACEMENT_3D('',#636,#637,#638);
#636 = CARTESIAN_POINT('',(73.317494,111.800042,19.973447));
#637 = DIRECTION('',(0.,0.,1.));
#638 = DIRECTION('',(1.,0.,0.));
#639 = DEFINITIONAL_REPRESENTATION('',(#640),#644);
#640 = LINE('',#641,#642);
#641 = CARTESIAN_POINT('',(2.882377164892,0.));
#642 = VECTOR('',#643,1.);
#643 = DIRECTION('',(1.,0.));
#644 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#645 = PCURVE('',#646,#651);
#646 = PLANE('',#647);
#647 = AXIS2_PLACEMENT_3D('',#648,#649,#650);
#648 = CARTESIAN_POINT('',(73.317494,111.800042,19.973447));
#649 = DIRECTION('',(0.,0.,1.));
#650 = DIRECTION('',(1.,0.,0.));
#651 = DEFINITIONAL_REPRESENTATION('',(#652),#656);
#652 = CIRCLE('',#653,4.316451884327);
#653 = AXIS2_PLACEMENT_2D('',#654,#655);
#654 = CARTESIAN_POINT('',(0.,0.));
#655 = DIRECTION('',(-0.966591363326,0.256322328998));
#656 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#657 = ORIENTED_EDGE('',*,*,#658,.T.);
#658 = EDGE_CURVE('',#625,#659,#661,.T.);
#659 = VERTEX_POINT('',#660);
#660 = CARTESIAN_POINT('',(77.604133408892,112.30648,37.5));
#661 = SURFACE_CURVE('',#662,(#666,#672),.PCURVE_S1.);
#662 = LINE('',#663,#664);
#663 = CARTESIAN_POINT('',(77.604133408892,112.30648,19.973447));
#664 = VECTOR('',#665,1.);
#665 = DIRECTION('',(0.,0.,1.));
#666 = PCURVE('',#634,#667);
#667 = DEFINITIONAL_REPRESENTATION('',(#668),#671);
#668 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#669,#670),.UNSPECIFIED.,.F.,.F.,
  (2,2),(0.,17.526553),.PIECEWISE_BEZIER_KNOTS.);
#669 = CARTESIAN_POINT('',(6.40078355319,0.));
#670 = CARTESIAN_POINT('',(6.40078355319,17.526553));
#671 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#672 = PCURVE('',#673,#678);
#673 = PLANE('',#674);
#674 = AXIS2_PLACEMENT_3D('',#675,#676,#677);
#675 = CARTESIAN_POINT('',(72.917494,112.30648,47.5));
#676 = DIRECTION('',(0.,-1.,0.));
#677 = DIRECTION('',(1.,0.,0.));
#678 = DEFINITIONAL_REPRESENTATION('',(#679),#682);
#679 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#680,#681),.UNSPECIFIED.,.F.,.F.,
  (2,2),(-5.999999999062E-07,17.5265536),.PIECEWISE_BEZIER_KNOTS.);
#680 = CARTESIAN_POINT('',(4.686639408892,-27.5265536));
#681 = CARTESIAN_POINT('',(4.686639408892,-9.9999994));
#682 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#683 = ORIENTED_EDGE('',*,*,#684,.T.);
#684 = EDGE_CURVE('',#659,#685,#687,.T.);
#685 = VERTEX_POINT('',#686);
#686 = CARTESIAN_POINT('',(69.145248888396,112.906445,37.5));
#687 = SURFACE_CURVE('',#688,(#693,#700),.PCURVE_S1.);
#688 = CIRCLE('',#689,4.316451884327);
#689 = AXIS2_PLACEMENT_3D('',#690,#691,#692);
#690 = CARTESIAN_POINT('',(73.317494,111.800042,37.5));
#691 = DIRECTION('',(0.,0.,-1.));
#692 = DIRECTION('',(0.993093291381,0.117327382205,0.));
#693 = PCURVE('',#634,#694);
#694 = DEFINITIONAL_REPRESENTATION('',(#695),#699);
#695 = LINE('',#696,#697);
#696 = CARTESIAN_POINT('',(6.40078355319,17.526553));
#697 = VECTOR('',#698,1.);
#698 = DIRECTION('',(-1.,-0.));
#699 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#700 = PCURVE('',#701,#706);
#701 = PLANE('',#702);
#702 = AXIS2_PLACEMENT_3D('',#703,#704,#705);
#703 = CARTESIAN_POINT('',(73.317494,111.800042,37.5));
#704 = DIRECTION('',(0.,0.,1.));
#705 = DIRECTION('',(1.,0.,0.));
#706 = DEFINITIONAL_REPRESENTATION('',(#707),#715);
#707 = ( BOUNDED_CURVE() B_SPLINE_CURVE(2,(#708,#709,#710,#711,#712,#713
,#714),.UNSPECIFIED.,.T.,.F.) B_SPLINE_CURVE_WITH_KNOTS((1,2,2,2,2,1),(
    -2.094395102393,0.,2.094395102393,4.188790204786,6.28318530718,
8.377580409573),.UNSPECIFIED.) CURVE() GEOMETRIC_REPRESENTATION_ITEM() 
RATIONAL_B_SPLINE_CURVE((1.,0.5,1.,0.5,1.,0.5,1.)) REPRESENTATION_ITEM(
  '') );
#708 = CARTESIAN_POINT('',(4.286639408892,0.506438));
#709 = CARTESIAN_POINT('',(5.163815755776,-6.918239249929));
#710 = CARTESIAN_POINT('',(-1.704731531004,-3.965557624964));
#711 = CARTESIAN_POINT('',(-8.573278817785,-1.012876));
#712 = CARTESIAN_POINT('',(-2.581907877888,3.459119624964));
#713 = CARTESIAN_POINT('',(3.409463062009,7.931115249929));
#714 = CARTESIAN_POINT('',(4.286639408892,0.506438));
#715 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#716 = ORIENTED_EDGE('',*,*,#717,.F.);
#717 = EDGE_CURVE('',#623,#685,#718,.T.);
#718 = SURFACE_CURVE('',#719,(#723,#729),.PCURVE_S1.);
#719 = LINE('',#720,#721);
#720 = CARTESIAN_POINT('',(69.145248888396,112.906445,19.973447));
#721 = VECTOR('',#722,1.);
#722 = DIRECTION('',(0.,0.,1.));
#723 = PCURVE('',#634,#724);
#724 = DEFINITIONAL_REPRESENTATION('',(#725),#728);
#725 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#726,#727),.UNSPECIFIED.,.F.,.F.,
  (2,2),(0.,17.526553),.PIECEWISE_BEZIER_KNOTS.);
#726 = CARTESIAN_POINT('',(2.882377164892,0.));
#727 = CARTESIAN_POINT('',(2.882377164892,17.526553));
#728 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#729 = PCURVE('',#730,#735);
#730 = PLANE('',#731);
#731 = AXIS2_PLACEMENT_3D('',#732,#733,#734);
#732 = CARTESIAN_POINT('',(51.710833,112.906445,47.5));
#733 = DIRECTION('',(0.,-1.,0.));
#734 = DIRECTION('',(1.,0.,0.));
#735 = DEFINITIONAL_REPRESENTATION('',(#736),#739);
#736 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#737,#738),.UNSPECIFIED.,.F.,.F.,
  (2,2),(-5.999999999062E-07,17.5265536),.PIECEWISE_BEZIER_KNOTS.);
#737 = CARTESIAN_POINT('',(17.434415888396,-27.5265536));
#738 = CARTESIAN_POINT('',(17.434415888396,-9.9999994));
#739 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#740 = ADVANCED_FACE('',(#741,#810),#646,.F.);
#741 = FACE_BOUND('',#742,.F.);
#742 = EDGE_LOOP('',(#743,#744,#765,#791));
#743 = ORIENTED_EDGE('',*,*,#622,.T.);
#744 = ORIENTED_EDGE('',*,*,#745,.F.);
#745 = EDGE_CURVE('',#746,#625,#748,.T.);
#746 = VERTEX_POINT('',#747);
#747 = CARTESIAN_POINT('',(72.917494,112.30648,19.973447));
#748 = SURFACE_CURVE('',#749,(#753,#759),.PCURVE_S1.);
#749 = LINE('',#750,#751);
#750 = CARTESIAN_POINT('',(73.117494,112.30648,19.973447));
#751 = VECTOR('',#752,1.);
#752 = DIRECTION('',(1.,0.,-0.));
#753 = PCURVE('',#646,#754);
#754 = DEFINITIONAL_REPRESENTATION('',(#755),#758);
#755 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#756,#757),.UNSPECIFIED.,.F.,.F.,
  (2,2),(-0.2,4.516451884327),.PIECEWISE_BEZIER_KNOTS.);
#756 = CARTESIAN_POINT('',(-0.4,0.506438));
#757 = CARTESIAN_POINT('',(4.316451884327,0.506438));
#758 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#759 = PCURVE('',#673,#760);
#760 = DEFINITIONAL_REPRESENTATION('',(#761),#764);
#761 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#762,#763),.UNSPECIFIED.,.F.,.F.,
  (2,2),(-0.2,4.516451884327),.PIECEWISE_BEZIER_KNOTS.);
#762 = CARTESIAN_POINT('',(0.,-27.526553));
#763 = CARTESIAN_POINT('',(4.716451884327,-27.526553));
#764 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#765 = ORIENTED_EDGE('',*,*,#766,.F.);
#766 = EDGE_CURVE('',#767,#746,#769,.T.);
#767 = VERTEX_POINT('',#768);
#768 = CARTESIAN_POINT('',(72.917494,112.906445,19.973447));
#769 = SURFACE_CURVE('',#770,(#774,#780),.PCURVE_S1.);
#770 = LINE('',#771,#772);
#771 = CARTESIAN_POINT('',(72.917494,112.3532435,19.973447));
#772 = VECTOR('',#773,1.);
#773 = DIRECTION('',(0.,-1.,0.));
#774 = PCURVE('',#646,#775);
#775 = DEFINITIONAL_REPRESENTATION('',(#776),#779);
#776 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#777,#778),.UNSPECIFIED.,.F.,.F.,
  (2,2),(-0.5532015,4.676350000001E-02),.PIECEWISE_BEZIER_KNOTS.);
#777 = CARTESIAN_POINT('',(-0.4,1.106403));
#778 = CARTESIAN_POINT('',(-0.4,0.506438));
#779 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#780 = PCURVE('',#781,#786);
#781 = PLANE('',#782);
#782 = AXIS2_PLACEMENT_3D('',#783,#784,#785);
#783 = CARTESIAN_POINT('',(72.917494,112.906445,47.5));
#784 = DIRECTION('',(-1.,-0.,-0.));
#785 = DIRECTION('',(0.,-1.,0.));
#786 = DEFINITIONAL_REPRESENTATION('',(#787),#790);
#787 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#788,#789),.UNSPECIFIED.,.F.,.F.,
  (2,2),(-0.5532015,4.676350000001E-02),.PIECEWISE_BEZIER_KNOTS.);
#788 = CARTESIAN_POINT('',(-1.42108547152E-14,-27.526553));
#789 = CARTESIAN_POINT('',(0.599965,-27.526553));
#790 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#791 = ORIENTED_EDGE('',*,*,#792,.F.);
#792 = EDGE_CURVE('',#623,#767,#793,.T.);
#793 = SURFACE_CURVE('',#794,(#798,#804),.PCURVE_S1.);
#794 = LINE('',#795,#796);
#795 = CARTESIAN_POINT('',(62.5141635,112.906445,19.973447));
#796 = VECTOR('',#797,1.);
#797 = DIRECTION('',(1.,0.,-0.));
#798 = PCURVE('',#646,#799);
#799 = DEFINITIONAL_REPRESENTATION('',(#800),#803);
#800 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#801,#802),.UNSPECIFIED.,.F.,.F.,
  (2,2),(6.486878615673,10.4033305),.PIECEWISE_BEZIER_KNOTS.);
#801 = CARTESIAN_POINT('',(-4.316451884327,1.106403));
#802 = CARTESIAN_POINT('',(-0.4,1.106403));
#803 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#804 = PCURVE('',#730,#805);
#805 = DEFINITIONAL_REPRESENTATION('',(#806),#809);
#806 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#807,#808),.UNSPECIFIED.,.F.,.F.,
  (2,2),(6.486878615673,10.4033305),.PIECEWISE_BEZIER_KNOTS.);
#807 = CARTESIAN_POINT('',(17.290209115673,-27.526553));
#808 = CARTESIAN_POINT('',(21.206661,-27.526553));
#809 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#810 = FACE_BOUND('',#811,.F.);
#811 = EDGE_LOOP('',(#812));
#812 = ORIENTED_EDGE('',*,*,#813,.F.);
#813 = EDGE_CURVE('',#814,#814,#816,.T.);
#814 = VERTEX_POINT('',#815);
#815 = CARTESIAN_POINT('',(73.592494,111.800042,19.973447));
#816 = SURFACE_CURVE('',#817,(#822,#829),.PCURVE_S1.);
#817 = CIRCLE('',#818,0.275);
#818 = AXIS2_PLACEMENT_3D('',#819,#820,#821);
#819 = CARTESIAN_POINT('',(73.317494,111.800042,19.973447));
#820 = DIRECTION('',(0.,0.,1.));
#821 = DIRECTION('',(1.,0.,0.));
#822 = PCURVE('',#646,#823);
#823 = DEFINITIONAL_REPRESENTATION('',(#824),#828);
#824 = CIRCLE('',#825,0.275);
#825 = AXIS2_PLACEMENT_2D('',#826,#827);
#826 = CARTESIAN_POINT('',(0.,0.));
#827 = DIRECTION('',(1.,0.));
#828 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#829 = PCURVE('',#830,#835);
#830 = CYLINDRICAL_SURFACE('',#831,0.275);
#831 = AXIS2_PLACEMENT_3D('',#832,#833,#834);
#832 = CARTESIAN_POINT('',(73.317494,111.800042,19.973447));
#833 = DIRECTION('',(0.,0.,1.));
#834 = DIRECTION('',(1.,0.,0.));
#835 = DEFINITIONAL_REPRESENTATION('',(#836),#840);
#836 = LINE('',#837,#838);
#837 = CARTESIAN_POINT('',(0.,0.));
#838 = VECTOR('',#839,1.);
#839 = DIRECTION('',(1.,0.));
#840 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#841 = ADVANCED_FACE('',(#842),#730,.F.);
#842 = FACE_BOUND('',#843,.F.);
#843 = EDGE_LOOP('',(#844,#867,#886,#887));
#844 = ORIENTED_EDGE('',*,*,#845,.F.);
#845 = EDGE_CURVE('',#846,#767,#848,.T.);
#846 = VERTEX_POINT('',#847);
#847 = CARTESIAN_POINT('',(72.917494,112.906445,37.5));
#848 = SURFACE_CURVE('',#849,(#853,#860),.PCURVE_S1.);
#849 = LINE('',#850,#851);
#850 = CARTESIAN_POINT('',(72.917494,112.906445,47.5));
#851 = VECTOR('',#852,1.);
#852 = DIRECTION('',(0.,0.,-1.));
#853 = PCURVE('',#730,#854);
#854 = DEFINITIONAL_REPRESENTATION('',(#855),#859);
#855 = LINE('',#856,#857);
#856 = CARTESIAN_POINT('',(21.206661,0.));
#857 = VECTOR('',#858,1.);
#858 = DIRECTION('',(0.,-1.));
#859 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#860 = PCURVE('',#781,#861);
#861 = DEFINITIONAL_REPRESENTATION('',(#862),#866);
#862 = LINE('',#863,#864);
#863 = CARTESIAN_POINT('',(0.,0.));
#864 = VECTOR('',#865,1.);
#865 = DIRECTION('',(0.,-1.));
#866 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#867 = ORIENTED_EDGE('',*,*,#868,.F.);
#868 = EDGE_CURVE('',#685,#846,#869,.T.);
#869 = SURFACE_CURVE('',#870,(#874,#880),.PCURVE_S1.);
#870 = LINE('',#871,#872);
#871 = CARTESIAN_POINT('',(62.5141635,112.906445,37.5));
#872 = VECTOR('',#873,1.);
#873 = DIRECTION('',(1.,0.,-0.));
#874 = PCURVE('',#730,#875);
#875 = DEFINITIONAL_REPRESENTATION('',(#876),#879);
#876 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#877,#878),.UNSPECIFIED.,.F.,.F.,
  (2,2),(6.486878615673,10.4033305),.PIECEWISE_BEZIER_KNOTS.);
#877 = CARTESIAN_POINT('',(17.290209115673,-10.));
#878 = CARTESIAN_POINT('',(21.206661,-10.));
#879 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#880 = PCURVE('',#701,#881);
#881 = DEFINITIONAL_REPRESENTATION('',(#882),#885);
#882 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#883,#884),.UNSPECIFIED.,.F.,.F.,
  (2,2),(6.486878615673,10.4033305),.PIECEWISE_BEZIER_KNOTS.);
#883 = CARTESIAN_POINT('',(-4.316451884327,1.106403));
#884 = CARTESIAN_POINT('',(-0.4,1.106403));
#885 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#886 = ORIENTED_EDGE('',*,*,#717,.F.);
#887 = ORIENTED_EDGE('',*,*,#792,.T.);
#888 = ADVANCED_FACE('',(#889,#933),#701,.T.);
#889 = FACE_BOUND('',#890,.T.);
#890 = EDGE_LOOP('',(#891,#892,#893,#914));
#891 = ORIENTED_EDGE('',*,*,#868,.F.);
#892 = ORIENTED_EDGE('',*,*,#684,.F.);
#893 = ORIENTED_EDGE('',*,*,#894,.F.);
#894 = EDGE_CURVE('',#895,#659,#897,.T.);
#895 = VERTEX_POINT('',#896);
#896 = CARTESIAN_POINT('',(72.917494,112.30648,37.5));
#897 = SURFACE_CURVE('',#898,(#902,#908),.PCURVE_S1.);
#898 = LINE('',#899,#900);
#899 = CARTESIAN_POINT('',(73.117494,112.30648,37.5));
#900 = VECTOR('',#901,1.);
#901 = DIRECTION('',(1.,0.,-0.));
#902 = PCURVE('',#701,#903);
#903 = DEFINITIONAL_REPRESENTATION('',(#904),#907);
#904 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#905,#906),.UNSPECIFIED.,.F.,.F.,
  (2,2),(-0.2,4.516451884327),.PIECEWISE_BEZIER_KNOTS.);
#905 = CARTESIAN_POINT('',(-0.4,0.506438));
#906 = CARTESIAN_POINT('',(4.316451884327,0.506438));
#907 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#908 = PCURVE('',#673,#909);
#909 = DEFINITIONAL_REPRESENTATION('',(#910),#913);
#910 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#911,#912),.UNSPECIFIED.,.F.,.F.,
  (2,2),(-0.2,4.516451884327),.PIECEWISE_BEZIER_KNOTS.);
#911 = CARTESIAN_POINT('',(0.,-10.));
#912 = CARTESIAN_POINT('',(4.716451884327,-10.));
#913 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#914 = ORIENTED_EDGE('',*,*,#915,.F.);
#915 = EDGE_CURVE('',#846,#895,#916,.T.);
#916 = SURFACE_CURVE('',#917,(#921,#927),.PCURVE_S1.);
#917 = LINE('',#918,#919);
#918 = CARTESIAN_POINT('',(72.917494,112.3532435,37.5));
#919 = VECTOR('',#920,1.);
#920 = DIRECTION('',(0.,-1.,0.));
#921 = PCURVE('',#701,#922);
#922 = DEFINITIONAL_REPRESENTATION('',(#923),#926);
#923 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#924,#925),.UNSPECIFIED.,.F.,.F.,
  (2,2),(-0.5532015,4.676350000001E-02),.PIECEWISE_BEZIER_KNOTS.);
#924 = CARTESIAN_POINT('',(-0.4,1.106403));
#925 = CARTESIAN_POINT('',(-0.4,0.506438));
#926 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#927 = PCURVE('',#781,#928);
#928 = DEFINITIONAL_REPRESENTATION('',(#929),#932);
#929 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#930,#931),.UNSPECIFIED.,.F.,.F.,
  (2,2),(-0.5532015,4.676350000001E-02),.PIECEWISE_BEZIER_KNOTS.);
#930 = CARTESIAN_POINT('',(-1.42108547152E-14,-10.));
#931 = CARTESIAN_POINT('',(0.599965,-10.));
#932 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#933 = FACE_BOUND('',#934,.T.);
#934 = EDGE_LOOP('',(#935));
#935 = ORIENTED_EDGE('',*,*,#936,.F.);
#936 = EDGE_CURVE('',#937,#937,#939,.T.);
#937 = VERTEX_POINT('',#938);
#938 = CARTESIAN_POINT('',(73.592494,111.800042,37.5));
#939 = SURFACE_CURVE('',#940,(#945,#952),.PCURVE_S1.);
#940 = CIRCLE('',#941,0.275);
#941 = AXIS2_PLACEMENT_3D('',#942,#943,#944);
#942 = CARTESIAN_POINT('',(73.317494,111.800042,37.5));
#943 = DIRECTION('',(0.,0.,1.));
#944 = DIRECTION('',(1.,0.,0.));
#945 = PCURVE('',#701,#946);
#946 = DEFINITIONAL_REPRESENTATION('',(#947),#951);
#947 = CIRCLE('',#948,0.275);
#948 = AXIS2_PLACEMENT_2D('',#949,#950);
#949 = CARTESIAN_POINT('',(0.,0.));
#950 = DIRECTION('',(1.,0.));
#951 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#952 = PCURVE('',#830,#953);
#953 = DEFINITIONAL_REPRESENTATION('',(#954),#958);
#954 = LINE('',#955,#956);
#955 = CARTESIAN_POINT('',(0.,17.526553));
#956 = VECTOR('',#957,1.);
#957 = DIRECTION('',(1.,0.));
#958 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#959 = ADVANCED_FACE('',(#960),#673,.F.);
#960 = FACE_BOUND('',#961,.F.);
#961 = EDGE_LOOP('',(#962,#963,#984,#985));
#962 = ORIENTED_EDGE('',*,*,#894,.F.);
#963 = ORIENTED_EDGE('',*,*,#964,.T.);
#964 = EDGE_CURVE('',#895,#746,#965,.T.);
#965 = SURFACE_CURVE('',#966,(#970,#977),.PCURVE_S1.);
#966 = LINE('',#967,#968);
#967 = CARTESIAN_POINT('',(72.917494,112.30648,47.5));
#968 = VECTOR('',#969,1.);
#969 = DIRECTION('',(0.,0.,-1.));
#970 = PCURVE('',#673,#971);
#971 = DEFINITIONAL_REPRESENTATION('',(#972),#976);
#972 = LINE('',#973,#974);
#973 = CARTESIAN_POINT('',(0.,0.));
#974 = VECTOR('',#975,1.);
#975 = DIRECTION('',(0.,-1.));
#976 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#977 = PCURVE('',#781,#978);
#978 = DEFINITIONAL_REPRESENTATION('',(#979),#983);
#979 = LINE('',#980,#981);
#980 = CARTESIAN_POINT('',(0.599965,0.));
#981 = VECTOR('',#982,1.);
#982 = DIRECTION('',(0.,-1.));
#983 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#984 = ORIENTED_EDGE('',*,*,#745,.T.);
#985 = ORIENTED_EDGE('',*,*,#658,.T.);
#986 = ADVANCED_FACE('',(#987),#781,.F.);
#987 = FACE_BOUND('',#988,.F.);
#988 = EDGE_LOOP('',(#989,#990,#991,#992));
#989 = ORIENTED_EDGE('',*,*,#915,.F.);
#990 = ORIENTED_EDGE('',*,*,#845,.T.);
#991 = ORIENTED_EDGE('',*,*,#766,.T.);
#992 = ORIENTED_EDGE('',*,*,#964,.F.);
#993 = ADVANCED_FACE('',(#994),#830,.F.);
#994 = FACE_BOUND('',#995,.F.);
#995 = EDGE_LOOP('',(#996,#997,#1018,#1019));
#996 = ORIENTED_EDGE('',*,*,#936,.F.);
#997 = ORIENTED_EDGE('',*,*,#998,.F.);
#998 = EDGE_CURVE('',#814,#937,#999,.T.);
#999 = SEAM_CURVE('',#1000,(#1004,#1011),.PCURVE_S1.);
#1000 = LINE('',#1001,#1002);
#1001 = CARTESIAN_POINT('',(73.592494,111.800042,19.973447));
#1002 = VECTOR('',#1003,1.);
#1003 = DIRECTION('',(0.,0.,1.));
#1004 = PCURVE('',#830,#1005);
#1005 = DEFINITIONAL_REPRESENTATION('',(#1006),#1010);
#1006 = LINE('',#1007,#1008);
#1007 = CARTESIAN_POINT('',(6.28318530718,-0.));
#1008 = VECTOR('',#1009,1.);
#1009 = DIRECTION('',(0.,1.));
#1010 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1011 = PCURVE('',#830,#1012);
#1012 = DEFINITIONAL_REPRESENTATION('',(#1013),#1017);
#1013 = LINE('',#1014,#1015);
#1014 = CARTESIAN_POINT('',(0.,-0.));
#1015 = VECTOR('',#1016,1.);
#1016 = DIRECTION('',(0.,1.));
#1017 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1018 = ORIENTED_EDGE('',*,*,#813,.T.);
#1019 = ORIENTED_EDGE('',*,*,#998,.T.);
#1020 = ( GEOMETRIC_REPRESENTATION_CONTEXT(3) 
GLOBAL_UNCERTAINTY_ASSIGNED_CONTEXT((#1024)) 
GLOBAL_UNIT_ASSIGNED_CONTEXT((#1021,#1022,#1023)) REPRESENTATION_CONTEXT
('Context #1','3D Context with UNIT and UNCERTAINTY') );
#1021 = ( LENGTH_UNIT() NAMED_UNIT(*) SI_UNIT(.MILLI.,.METRE.) );
#1022 = ( NAMED_UNIT(*) PLANE_ANGLE_UNIT() SI_UNIT($,.RADIAN.) );
#1023 = ( NAMED_UNIT(*) SI_UNIT($,.STERADIAN.) SOLID_ANGLE_UNIT() );
#1024 = UNCERTAINTY_MEASURE_WITH_UNIT(LENGTH_MEASURE(1.E-07),#1021,
  'distance_accuracy_value','confusion accuracy');
#1025 = CONTEXT_DEPENDENT_SHAPE_REPRESENTATION(#1026,#1028);
#1026 = ( REPRESENTATION_RELATIONSHIP('','',#615,#10) 
REPRESENTATION_RELATIONSHIP_WITH_TRANSFORMATION(#1027) 
SHAPE_REPRESENTATION_RELATIONSHIP() );
#1027 = ITEM_DEFINED_TRANSFORMATION('','',#11,#23);
#1028 = PRODUCT_DEFINITION_SHAPE('Placement','Placement of an item',
  #1029);
#1029 = NEXT_ASSEMBLY_USAGE_OCCURRENCE('137','=>[0:1:1:4]','',#5,#610,$
  );
#1030 = PRODUCT_RELATED_PRODUCT_CATEGORY('part',$,(#612));
#1031 = MECHANICAL_DESIGN_GEOMETRIC_PRESENTATION_REPRESENTATION('',(
    #1032),#1020);
#1032 = STYLED_ITEM('color',(#1033),#616);
#1033 = PRESENTATION_STYLE_ASSIGNMENT((#1034));
#1034 = SURFACE_STYLE_USAGE(.BOTH.,#1035);
#1035 = SURFACE_SIDE_STYLE('',(#1036));
#1036 = SURFACE_STYLE_FILL_AREA(#1037);
#1037 = FILL_AREA_STYLE('',(#1038));
#1038 = FILL_AREA_STYLE_COLOUR('',#1039);
#1039 = DRAUGHTING_PRE_DEFINED_COLOUR('red');
#1040 = MECHANICAL_DESIGN_GEOMETRIC_PRESENTATION_REPRESENTATION('',(
    #1041),#597);
#1041 = STYLED_ITEM('color',(#1042),#158);
#1042 = PRESENTATION_STYLE_ASSIGNMENT((#1043));
#1043 = SURFACE_STYLE_USAGE(.BOTH.,#1044);
#1044 = SURFACE_SIDE_STYLE('',(#1045));
#1045 = SURFACE_STYLE_FILL_AREA(#1046);
#1046 = FILL_AREA_STYLE('',(#1047));
#1047 = FILL_AREA_STYLE_COLOUR('',#1048);
#1048 = DRAUGHTING_PRE_DEFINED_COLOUR('green');
#1049 = MECHANICAL_DESIGN_GEOMETRIC_PRESENTATION_REPRESENTATION('',(
    #1050),#139);
#1050 = STYLED_ITEM('color',(#1051),#41);
#1051 = PRESENTATION_STYLE_ASSIGNMENT((#1052));
#1052 = SURFACE_STYLE_USAGE(.BOTH.,#1053);
#1053 = SURFACE_SIDE_STYLE('',(#1054));
#1054 = SURFACE_STYLE_FILL_AREA(#1055);
#1055 = FILL_AREA_STYLE('',(#1056));
#1056 = FILL_AREA_STYLE_COLOUR('',#1057);
#1057 = DRAUGHTING_PRE_DEFINED_COLOUR('blue');
ENDSEC;
END-ISO-10303-21;
