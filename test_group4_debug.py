#!/usr/bin/env python3
"""
Test script to debug Group 4 Part 2 missing issue with comprehensive logging.
"""

import sys
import os
import logging
from pathlib import Path

# Add the project root to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def setup_debug_logging():
    """Setup comprehensive debug logging to capture all Part 2 processing details."""
    
    # Create logs directory if it doesn't exist
    logs_dir = project_root / "logs"
    logs_dir.mkdir(exist_ok=True)
    
    # Setup file handler for debug logs
    log_file = logs_dir / "group4_part2_debug.log"
    
    # Configure root logger
    logging.basicConfig(
        level=logging.DEBUG,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(log_file, mode='w'),
            logging.StreamHandler(sys.stdout)
        ]
    )
    
    # Set specific loggers to DEBUG level
    debug_loggers = [
        'tension_mass.export.formats.step.step_exporter',
        'tension_mass.export.geometry.component_processing', 
        'tension_mass.export.geometry.part_processors',
        'tension_mass.export.geometry.union_operations',
        'tension_mass.export.formats.step.step_writer'
    ]
    
    for logger_name in debug_loggers:
        logger = logging.getLogger(logger_name)
        logger.setLevel(logging.DEBUG)
    
    print(f"Debug logging setup complete. Logs will be written to: {log_file}")
    return log_file

def run_group_processing_test():
    """Run the group processing to capture Part 2 debugging information."""
    
    try:
        # Import after setting up logging
        from tension_mass import process_pile_volume_analysis

        print("🔧 Starting Group 4 Part 2 debugging test...")

        # Load test data (using example file)
        test_file = project_root / "tension_mass" / "example" / "A.SAFEInput_Geometry.xlsx"
        if not test_file.exists():
            print(f"❌ Test file not found: {test_file}")
            print("Please ensure the example file exists.")
            return False

        print(f"📁 Loading test data from: {test_file}")

        # Process with the correct API - use a larger subset to get multiple groups
        # Select piles from different groups to trigger multiple group processing
        results = process_pile_volume_analysis(
            str(test_file),
            selected_piles=['PC1-P1', 'PC1-P2', 'PC1-P3', 'PC1-P4', 'PC1-P5', 'PC1-P6', 'PC1-P7', 'PC1-P8',
                          'PC2-P1', 'PC2-P2', 'PC2-P3', 'PC2-P4', 'PC2-P5', 'PC2-P6', 'PC2-P7', 'PC2-P8',
                          'PC3-P1', 'PC3-P2', 'PC3-P3', 'PC3-P4'],  # Multiple groups for testing
            base_output_dir="debug_output"
        )
        
        print("✅ Processing completed successfully!")
        print(f"📊 Results: {len(results)} pile results")
        
        # Check for group STEP files
        output_dir = project_root / "debug_output"
        if output_dir.exists():
            step_files = list(output_dir.glob("**/*.step"))
            print(f"📦 Generated {len(step_files)} STEP files:")
            for step_file in step_files:
                print(f"  - {step_file.name}")
                
            # Look specifically for group files
            group_files = [f for f in step_files if "group_" in f.name.lower()]
            print(f"🔍 Found {len(group_files)} group files:")
            for group_file in group_files:
                print(f"  - {group_file.name}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error during processing: {e}")
        import traceback
        traceback.print_exc()
        return False

def analyze_logs(log_file):
    """Analyze the debug logs to identify Part 2 issues."""
    
    print(f"\n🔍 Analyzing debug logs from: {log_file}")
    
    try:
        with open(log_file, 'r') as f:
            log_content = f.read()
        
        # Look for Part 2 debug messages
        part2_lines = [line for line in log_content.split('\n') if 'PART2 DEBUG' in line]
        
        print(f"📊 Found {len(part2_lines)} Part 2 debug messages")
        
        # Group by group ID
        group_messages = {}
        for line in part2_lines:
            if 'Group' in line:
                # Extract group number
                parts = line.split('Group')
                if len(parts) > 1:
                    group_part = parts[1].split()[0]
                    try:
                        group_num = int(group_part)
                        if group_num not in group_messages:
                            group_messages[group_num] = []
                        group_messages[group_num].append(line)
                    except ValueError:
                        pass
        
        # Analyze each group
        for group_id in sorted(group_messages.keys()):
            messages = group_messages[group_id]
            print(f"\n📋 Group {group_id} Part 2 Analysis ({len(messages)} messages):")
            
            # Look for critical issues
            critical_messages = [msg for msg in messages if 'CRITICAL' in msg]
            if critical_messages:
                print(f"  ❌ CRITICAL ISSUES FOUND:")
                for msg in critical_messages:
                    print(f"    {msg.split(' - ')[-1] if ' - ' in msg else msg}")
            
            # Look for volume changes
            volume_messages = [msg for msg in messages if 'volume' in msg.lower()]
            if volume_messages:
                print(f"  📊 Volume tracking ({len(volume_messages)} messages):")
                for msg in volume_messages[-3:]:  # Show last 3 volume messages
                    print(f"    {msg.split(' - ')[-1] if ' - ' in msg else msg}")
        
        # Look for specific Group 4 issues
        if 4 in group_messages:
            print(f"\n🎯 Group 4 Detailed Analysis:")
            group4_messages = group_messages[4]
            
            # Check for elimination patterns
            elimination_msgs = [msg for msg in group4_messages if 'ELIMINATED' in msg or 'eliminated' in msg]
            if elimination_msgs:
                print(f"  ⚠️  Elimination detected:")
                for msg in elimination_msgs:
                    print(f"    {msg}")
            
            # Check for volume progression
            print(f"  📈 Volume progression:")
            for msg in group4_messages:
                if 'total' in msg and 'm³' in msg:
                    print(f"    {msg.split(' - ')[-1] if ' - ' in msg else msg}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error analyzing logs: {e}")
        return False

def main():
    """Main function to run the debugging test."""
    
    print("🚀 Group 4 Part 2 Debugging Test")
    print("=" * 50)
    
    # Setup logging
    log_file = setup_debug_logging()
    
    # Run the test
    success = run_group_processing_test()
    
    if success:
        print("\n" + "=" * 50)
        # Analyze the logs
        analyze_logs(log_file)
    
    print(f"\n📝 Complete debug logs available at: {log_file}")
    print("🔍 Review the logs to identify where Group 4 Part 2 is being eliminated.")

if __name__ == "__main__":
    main()
