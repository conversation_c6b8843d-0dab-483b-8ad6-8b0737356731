ISO-10303-21;
HEADER;
FILE_DESCRIPTION(('Open CASCADE Model'),'2;1');
FILE_NAME('Pile_PC2-P1','2025-08-25T20:08:32',('Author'),('Open CASCADE'
    ),'Open CASCADE STEP processor 7.8','build123d','Unknown');
FILE_SCHEMA(('AUTOMOTIVE_DESIGN { 1 0 10303 214 1 1 1 1 }'));
ENDSEC;
DATA;
#1 = APPLICATION_PROTOCOL_DEFINITION('international standard',
  'automotive_design',2000,#2);
#2 = APPLICATION_CONTEXT(
  'core data for automotive mechanical design processes');
#3 = SHAPE_DEFINITION_REPRESENTATION(#4,#10);
#4 = PRODUCT_DEFINITION_SHAPE('','',#5);
#5 = PRODUCT_DEFINITION('design','',#6,#9);
#6 = PRODUCT_DEFINITION_FORMATION('','',#7);
#7 = PRODUCT('Pile_PC2-P1','Pile_PC2-P1','',(#8));
#8 = PRODUCT_CONTEXT('',#2,'mechanical');
#9 = PRODUCT_DEFINITION_CONTEXT('part definition',#2,'design');
#10 = SHAPE_REPRESENTATION('',(#11,#15,#19,#23),#27);
#11 = AXIS2_PLACEMENT_3D('',#12,#13,#14);
#12 = CARTESIAN_POINT('',(0.,0.,0.));
#13 = DIRECTION('',(0.,0.,1.));
#14 = DIRECTION('',(1.,0.,-0.));
#15 = AXIS2_PLACEMENT_3D('',#16,#17,#18);
#16 = CARTESIAN_POINT('',(51.790674,117.607553,7.565494));
#17 = DIRECTION('',(0.,0.,1.));
#18 = DIRECTION('',(1.,0.,0.));
#19 = AXIS2_PLACEMENT_3D('',#20,#21,#22);
#20 = CARTESIAN_POINT('',(0.,0.,0.));
#21 = DIRECTION('',(0.,0.,1.));
#22 = DIRECTION('',(1.,0.,-0.));
#23 = AXIS2_PLACEMENT_3D('',#24,#25,#26);
#24 = CARTESIAN_POINT('',(0.,0.,0.));
#25 = DIRECTION('',(0.,0.,1.));
#26 = DIRECTION('',(1.,0.,-0.));
#27 = ( GEOMETRIC_REPRESENTATION_CONTEXT(3) 
GLOBAL_UNCERTAINTY_ASSIGNED_CONTEXT((#31)) GLOBAL_UNIT_ASSIGNED_CONTEXT(
(#28,#29,#30)) REPRESENTATION_CONTEXT('Context #1',
  '3D Context with UNIT and UNCERTAINTY') );
#28 = ( LENGTH_UNIT() NAMED_UNIT(*) SI_UNIT(.MILLI.,.METRE.) );
#29 = ( NAMED_UNIT(*) PLANE_ANGLE_UNIT() SI_UNIT($,.RADIAN.) );
#30 = ( NAMED_UNIT(*) SI_UNIT($,.STERADIAN.) SOLID_ANGLE_UNIT() );
#31 = UNCERTAINTY_MEASURE_WITH_UNIT(LENGTH_MEASURE(1.E-07),#28,
  'distance_accuracy_value','confusion accuracy');
#32 = PRODUCT_RELATED_PRODUCT_CATEGORY('part',$,(#7));
#33 = SHAPE_DEFINITION_REPRESENTATION(#34,#40);
#34 = PRODUCT_DEFINITION_SHAPE('','',#35);
#35 = PRODUCT_DEFINITION('design','',#36,#39);
#36 = PRODUCT_DEFINITION_FORMATION('','',#37);
#37 = PRODUCT('PC2-P1_Part1','PC2-P1_Part1','',(#38));
#38 = PRODUCT_CONTEXT('',#2,'mechanical');
#39 = PRODUCT_DEFINITION_CONTEXT('part definition',#2,'design');
#40 = ADVANCED_BREP_SHAPE_REPRESENTATION('',(#11,#41),#139);
#41 = MANIFOLD_SOLID_BREP('',#42);
#42 = CLOSED_SHELL('',(#43,#131,#135));
#43 = ADVANCED_FACE('',(#44),#57,.T.);
#44 = FACE_BOUND('',#45,.T.);
#45 = EDGE_LOOP('',(#46,#80,#103,#130));
#46 = ORIENTED_EDGE('',*,*,#47,.F.);
#47 = EDGE_CURVE('',#48,#48,#50,.T.);
#48 = VERTEX_POINT('',#49);
#49 = CARTESIAN_POINT('',(0.275,-6.73555739531E-17,24.934506));
#50 = SURFACE_CURVE('',#51,(#56,#68),.PCURVE_S1.);
#51 = CIRCLE('',#52,0.275);
#52 = AXIS2_PLACEMENT_3D('',#53,#54,#55);
#53 = CARTESIAN_POINT('',(0.,0.,24.934506));
#54 = DIRECTION('',(0.,0.,1.));
#55 = DIRECTION('',(1.,0.,-0.));
#56 = PCURVE('',#57,#62);
#57 = CYLINDRICAL_SURFACE('',#58,0.275);
#58 = AXIS2_PLACEMENT_3D('',#59,#60,#61);
#59 = CARTESIAN_POINT('',(0.,0.,0.));
#60 = DIRECTION('',(0.,0.,1.));
#61 = DIRECTION('',(1.,0.,-0.));
#62 = DEFINITIONAL_REPRESENTATION('',(#63),#67);
#63 = LINE('',#64,#65);
#64 = CARTESIAN_POINT('',(0.,24.934506));
#65 = VECTOR('',#66,1.);
#66 = DIRECTION('',(1.,0.));
#67 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#68 = PCURVE('',#69,#74);
#69 = PLANE('',#70);
#70 = AXIS2_PLACEMENT_3D('',#71,#72,#73);
#71 = CARTESIAN_POINT('',(0.,0.,24.934506));
#72 = DIRECTION('',(0.,0.,1.));
#73 = DIRECTION('',(1.,0.,-0.));
#74 = DEFINITIONAL_REPRESENTATION('',(#75),#79);
#75 = CIRCLE('',#76,0.275);
#76 = AXIS2_PLACEMENT_2D('',#77,#78);
#77 = CARTESIAN_POINT('',(0.,0.));
#78 = DIRECTION('',(1.,0.));
#79 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#80 = ORIENTED_EDGE('',*,*,#81,.F.);
#81 = EDGE_CURVE('',#82,#48,#84,.T.);
#82 = VERTEX_POINT('',#83);
#83 = CARTESIAN_POINT('',(0.275,-6.73555739531E-17,0.));
#84 = SEAM_CURVE('',#85,(#89,#96),.PCURVE_S1.);
#85 = LINE('',#86,#87);
#86 = CARTESIAN_POINT('',(0.275,-6.73555739531E-17,0.));
#87 = VECTOR('',#88,1.);
#88 = DIRECTION('',(0.,0.,1.));
#89 = PCURVE('',#57,#90);
#90 = DEFINITIONAL_REPRESENTATION('',(#91),#95);
#91 = LINE('',#92,#93);
#92 = CARTESIAN_POINT('',(6.28318530718,-0.));
#93 = VECTOR('',#94,1.);
#94 = DIRECTION('',(0.,1.));
#95 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#96 = PCURVE('',#57,#97);
#97 = DEFINITIONAL_REPRESENTATION('',(#98),#102);
#98 = LINE('',#99,#100);
#99 = CARTESIAN_POINT('',(0.,-0.));
#100 = VECTOR('',#101,1.);
#101 = DIRECTION('',(0.,1.));
#102 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#103 = ORIENTED_EDGE('',*,*,#104,.T.);
#104 = EDGE_CURVE('',#82,#82,#105,.T.);
#105 = SURFACE_CURVE('',#106,(#111,#118),.PCURVE_S1.);
#106 = CIRCLE('',#107,0.275);
#107 = AXIS2_PLACEMENT_3D('',#108,#109,#110);
#108 = CARTESIAN_POINT('',(0.,0.,0.));
#109 = DIRECTION('',(0.,0.,1.));
#110 = DIRECTION('',(1.,0.,-0.));
#111 = PCURVE('',#57,#112);
#112 = DEFINITIONAL_REPRESENTATION('',(#113),#117);
#113 = LINE('',#114,#115);
#114 = CARTESIAN_POINT('',(0.,0.));
#115 = VECTOR('',#116,1.);
#116 = DIRECTION('',(1.,0.));
#117 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#118 = PCURVE('',#119,#124);
#119 = PLANE('',#120);
#120 = AXIS2_PLACEMENT_3D('',#121,#122,#123);
#121 = CARTESIAN_POINT('',(0.,0.,0.));
#122 = DIRECTION('',(0.,0.,1.));
#123 = DIRECTION('',(1.,0.,-0.));
#124 = DEFINITIONAL_REPRESENTATION('',(#125),#129);
#125 = CIRCLE('',#126,0.275);
#126 = AXIS2_PLACEMENT_2D('',#127,#128);
#127 = CARTESIAN_POINT('',(0.,0.));
#128 = DIRECTION('',(1.,0.));
#129 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#130 = ORIENTED_EDGE('',*,*,#81,.T.);
#131 = ADVANCED_FACE('',(#132),#69,.T.);
#132 = FACE_BOUND('',#133,.T.);
#133 = EDGE_LOOP('',(#134));
#134 = ORIENTED_EDGE('',*,*,#47,.T.);
#135 = ADVANCED_FACE('',(#136),#119,.F.);
#136 = FACE_BOUND('',#137,.T.);
#137 = EDGE_LOOP('',(#138));
#138 = ORIENTED_EDGE('',*,*,#104,.F.);
#139 = ( GEOMETRIC_REPRESENTATION_CONTEXT(3) 
GLOBAL_UNCERTAINTY_ASSIGNED_CONTEXT((#143)) GLOBAL_UNIT_ASSIGNED_CONTEXT
((#140,#141,#142)) REPRESENTATION_CONTEXT('Context #1',
  '3D Context with UNIT and UNCERTAINTY') );
#140 = ( LENGTH_UNIT() NAMED_UNIT(*) SI_UNIT(.MILLI.,.METRE.) );
#141 = ( NAMED_UNIT(*) PLANE_ANGLE_UNIT() SI_UNIT($,.RADIAN.) );
#142 = ( NAMED_UNIT(*) SI_UNIT($,.STERADIAN.) SOLID_ANGLE_UNIT() );
#143 = UNCERTAINTY_MEASURE_WITH_UNIT(LENGTH_MEASURE(1.E-07),#140,
  'distance_accuracy_value','confusion accuracy');
#144 = CONTEXT_DEPENDENT_SHAPE_REPRESENTATION(#145,#147);
#145 = ( REPRESENTATION_RELATIONSHIP('','',#40,#10) 
REPRESENTATION_RELATIONSHIP_WITH_TRANSFORMATION(#146) 
SHAPE_REPRESENTATION_RELATIONSHIP() );
#146 = ITEM_DEFINED_TRANSFORMATION('','',#11,#15);
#147 = PRODUCT_DEFINITION_SHAPE('Placement','Placement of an item',#148
  );
#148 = NEXT_ASSEMBLY_USAGE_OCCURRENCE('105','=>[0:1:1:2]','',#5,#35,$);
#149 = PRODUCT_RELATED_PRODUCT_CATEGORY('part',$,(#37));
#150 = SHAPE_DEFINITION_REPRESENTATION(#151,#157);
#151 = PRODUCT_DEFINITION_SHAPE('','',#152);
#152 = PRODUCT_DEFINITION('design','',#153,#156);
#153 = PRODUCT_DEFINITION_FORMATION('','',#154);
#154 = PRODUCT('PC2-P1_Part2','PC2-P1_Part2','',(#155));
#155 = PRODUCT_CONTEXT('',#2,'mechanical');
#156 = PRODUCT_DEFINITION_CONTEXT('part definition',#2,'design');
#157 = ADVANCED_BREP_SHAPE_REPRESENTATION('',(#11,#158),#577);
#158 = MANIFOLD_SOLID_BREP('',#159);
#159 = CLOSED_SHELL('',(#160,#399,#449,#518,#544,#571));
#160 = ADVANCED_FACE('',(#161),#175,.T.);
#161 = FACE_BOUND('',#162,.T.);
#162 = EDGE_LOOP('',(#163,#193,#222,#254,#317,#350,#372,#373));
#163 = ORIENTED_EDGE('',*,*,#164,.T.);
#164 = EDGE_CURVE('',#165,#167,#169,.T.);
#165 = VERTEX_POINT('',#166);
#166 = CARTESIAN_POINT('',(52.065674,117.607553,7.565494));
#167 = VERTEX_POINT('',#168);
#168 = CARTESIAN_POINT('',(56.107125884327,117.607553,14.565494));
#169 = SEAM_CURVE('',#170,(#174,#186),.PCURVE_S1.);
#170 = LINE('',#171,#172);
#171 = CARTESIAN_POINT('',(52.065674,117.607553,7.565494));
#172 = VECTOR('',#173,1.);
#173 = DIRECTION('',(0.5,-1.224646799147E-16,0.866025403784));
#174 = PCURVE('',#175,#180);
#175 = CONICAL_SURFACE('',#176,0.275,0.523598775598);
#176 = AXIS2_PLACEMENT_3D('',#177,#178,#179);
#177 = CARTESIAN_POINT('',(51.790674,117.607553,7.565494));
#178 = DIRECTION('',(0.,0.,1.));
#179 = DIRECTION('',(1.,0.,0.));
#180 = DEFINITIONAL_REPRESENTATION('',(#181),#185);
#181 = LINE('',#182,#183);
#182 = CARTESIAN_POINT('',(6.28318530718,-0.));
#183 = VECTOR('',#184,1.);
#184 = DIRECTION('',(0.,1.));
#185 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#186 = PCURVE('',#175,#187);
#187 = DEFINITIONAL_REPRESENTATION('',(#188),#192);
#188 = LINE('',#189,#190);
#189 = CARTESIAN_POINT('',(0.,-0.));
#190 = VECTOR('',#191,1.);
#191 = DIRECTION('',(0.,1.));
#192 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#193 = ORIENTED_EDGE('',*,*,#194,.F.);
#194 = EDGE_CURVE('',#195,#167,#197,.T.);
#195 = VERTEX_POINT('',#196);
#196 = CARTESIAN_POINT('',(51.558304183836,113.29736029685,14.565494));
#197 = SURFACE_CURVE('',#198,(#203,#210),.PCURVE_S1.);
#198 = CIRCLE('',#199,4.316451884327);
#199 = AXIS2_PLACEMENT_3D('',#200,#201,#202);
#200 = CARTESIAN_POINT('',(51.790674,117.607553,14.565494));
#201 = DIRECTION('',(0.,0.,1.));
#202 = DIRECTION('',(1.,0.,0.));
#203 = PCURVE('',#175,#204);
#204 = DEFINITIONAL_REPRESENTATION('',(#205),#209);
#205 = LINE('',#206,#207);
#206 = CARTESIAN_POINT('',(0.,7.));
#207 = VECTOR('',#208,1.);
#208 = DIRECTION('',(1.,0.));
#209 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#210 = PCURVE('',#211,#216);
#211 = PLANE('',#212);
#212 = AXIS2_PLACEMENT_3D('',#213,#214,#215);
#213 = CARTESIAN_POINT('',(51.790674,117.607553,14.565494));
#214 = DIRECTION('',(0.,0.,1.));
#215 = DIRECTION('',(1.,0.,0.));
#216 = DEFINITIONAL_REPRESENTATION('',(#217),#221);
#217 = CIRCLE('',#218,4.316451884327);
#218 = AXIS2_PLACEMENT_2D('',#219,#220);
#219 = CARTESIAN_POINT('',(0.,0.));
#220 = DIRECTION('',(1.,0.));
#221 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#222 = ORIENTED_EDGE('',*,*,#223,.F.);
#223 = EDGE_CURVE('',#224,#195,#226,.T.);
#224 = VERTEX_POINT('',#225);
#225 = CARTESIAN_POINT('',(51.468253,113.528152,14.176944376437));
#226 = SURFACE_CURVE('',#227,(#232,#242),.PCURVE_S1.);
#227 = HYPERBOLA('',#228,3.088599333709,1.783203656736);
#228 = AXIS2_PLACEMENT_3D('',#229,#230,#231);
#229 = CARTESIAN_POINT('',(50.129447570999,116.95936935859,
    7.089180027919));
#230 = DIRECTION('',(0.931596580528,0.363493894237,0.));
#231 = DIRECTION('',(-0.,0.,1.));
#232 = PCURVE('',#175,#233);
#233 = DEFINITIONAL_REPRESENTATION('',(#234),#241);
#234 = B_SPLINE_CURVE_WITH_KNOTS('',5,(#235,#236,#237,#238,#239,#240),
  .UNSPECIFIED.,.F.,.F.,(6,6),(1.456108942042,1.531480098983),
  .PIECEWISE_BEZIER_KNOTS.);
#235 = CARTESIAN_POINT('',(4.626304243781,6.507594449199));
#236 = CARTESIAN_POINT('',(4.632970748868,6.602016829549));
#237 = CARTESIAN_POINT('',(4.639524590081,6.698422923153));
#238 = CARTESIAN_POINT('',(4.645967688954,6.796857429916));
#239 = CARTESIAN_POINT('',(4.652301985148,6.897366926756));
#240 = CARTESIAN_POINT('',(4.658529420378,7.));
#241 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#242 = PCURVE('',#243,#248);
#243 = PLANE('',#244);
#244 = AXIS2_PLACEMENT_3D('',#245,#246,#247);
#245 = CARTESIAN_POINT('',(51.710833,112.906445,42.5));
#246 = DIRECTION('',(0.931596580528,0.363493894237,0.));
#247 = DIRECTION('',(-0.363493894237,0.931596580528,0.));
#248 = DEFINITIONAL_REPRESENTATION('',(#249),#253);
#249 = ( BOUNDED_CURVE() B_SPLINE_CURVE(2,(#250,#251,#252),
.UNSPECIFIED.,.F.,.F.) B_SPLINE_CURVE_WITH_KNOTS((3,3),(1.456108942042,
1.531480098983),.PIECEWISE_BEZIER_KNOTS.) CURVE() 
GEOMETRIC_REPRESENTATION_ITEM() RATIONAL_B_SPLINE_CURVE((1.,
1.000710185457,1.)) REPRESENTATION_ITEM('') );
#250 = CARTESIAN_POINT('',(0.73409211057,-28.4269115508));
#251 = CARTESIAN_POINT('',(0.582209669942,-28.19096728558));
#252 = CARTESIAN_POINT('',(0.419618647197,-27.934506));
#253 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#254 = ORIENTED_EDGE('',*,*,#255,.F.);
#255 = EDGE_CURVE('',#256,#224,#258,.T.);
#256 = VERTEX_POINT('',#257);
#257 = CARTESIAN_POINT('',(51.008936,121.80569,14.485557905923));
#258 = SURFACE_CURVE('',#259,(#264,#305),.PCURVE_S1.);
#259 = HYPERBOLA('',#260,0.949063836394,0.54794226142);
#260 = AXIS2_PLACEMENT_3D('',#261,#262,#263);
#261 = CARTESIAN_POINT('',(51.243573377796,117.57719462333,
    7.089180027919));
#262 = DIRECTION('',(0.998464000178,5.54043351018E-02,0.));
#263 = DIRECTION('',(-0.,0.,1.));
#264 = PCURVE('',#175,#265);
#265 = DEFINITIONAL_REPRESENTATION('',(#266),#304);
#266 = B_SPLINE_CURVE_WITH_KNOTS('',8,(#267,#268,#269,#270,#271,#272,
    #273,#274,#275,#276,#277,#278,#279,#280,#281,#282,#283,#284,#285,
    #286,#287,#288,#289,#290,#291,#292,#293,#294,#295,#296,#297,#298,
    #299,#300,#301,#302,#303),.UNSPECIFIED.,.F.,.F.,(9,7,7,7,7,9),(
    -2.75311310206,-1.37655655103,-0.344139137757,0.430173922197,
    1.591643512128,2.75311310206),.UNSPECIFIED.);
#267 = CARTESIAN_POINT('',(1.753515225737,7.));
#268 = CARTESIAN_POINT('',(1.775358209562,5.723961168287));
#269 = CARTESIAN_POINT('',(1.801461796997,4.700903094061));
#270 = CARTESIAN_POINT('',(1.83278055041,3.873255619209));
#271 = CARTESIAN_POINT('',(1.87050755436,3.199423965282));
#272 = CARTESIAN_POINT('',(1.916157694563,2.648350272778));
#273 = CARTESIAN_POINT('',(1.971581451796,2.196537221231));
#274 = CARTESIAN_POINT('',(2.039116805859,1.826045748932));
#275 = CARTESIAN_POINT('',(2.182042443168,1.296089567715));
#276 = CARTESIAN_POINT('',(2.251247885325,1.107023844423));
#277 = CARTESIAN_POINT('',(2.329259928307,0.950252481227));
#278 = CARTESIAN_POINT('',(2.416830614064,0.821363608127));
#279 = CARTESIAN_POINT('',(2.514427874561,0.716990591679));
#280 = CARTESIAN_POINT('',(2.621892547049,0.634624113104));
#281 = CARTESIAN_POINT('',(2.73771460436,0.572493005097));
#282 = CARTESIAN_POINT('',(2.950812126562,0.497266365781));
#283 = CARTESIAN_POINT('',(3.045485134883,0.475795130318));
#284 = CARTESIAN_POINT('',(3.14248596292,0.464632437412));
#285 = CARTESIAN_POINT('',(3.240507871865,0.463533200402));
#286 = CARTESIAN_POINT('',(3.338031313506,0.472453939061));
#287 = CARTESIAN_POINT('',(3.433594298676,0.491549316555));
#288 = CARTESIAN_POINT('',(3.526038741892,0.521178900534));
#289 = CARTESIAN_POINT('',(3.747228487668,0.62304244508));
#290 = CARTESIAN_POINT('',(3.871013829926,0.709171210441));
#291 = CARTESIAN_POINT('',(3.983009727053,0.822273646303));
#292 = CARTESIAN_POINT('',(4.081961404103,0.965437652938));
#293 = CARTESIAN_POINT('',(4.168570511665,1.143007550152));
#294 = CARTESIAN_POINT('',(4.243934426281,1.360845066798));
#295 = CARTESIAN_POINT('',(4.309366799357,1.626739481802));
#296 = CARTESIAN_POINT('',(4.422893435121,2.275479092919));
#297 = CARTESIAN_POINT('',(4.470987678271,2.658323909338));
#298 = CARTESIAN_POINT('',(4.511681155623,3.11006485327));
#299 = CARTESIAN_POINT('',(4.546199777993,3.643742545037));
#300 = CARTESIAN_POINT('',(4.575573977595,4.275748662812));
#301 = CARTESIAN_POINT('',(4.600641924143,5.026784665303));
#302 = CARTESIAN_POINT('',(4.622105502957,5.923342235742));
#303 = CARTESIAN_POINT('',(4.640535520559,7.));
#304 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#305 = PCURVE('',#306,#311);
#306 = PLANE('',#307);
#307 = AXIS2_PLACEMENT_3D('',#308,#309,#310);
#308 = CARTESIAN_POINT('',(51.468253,113.528152,42.5));
#309 = DIRECTION('',(0.998464000178,5.54043351018E-02,0.));
#310 = DIRECTION('',(-5.54043351018E-02,0.998464000178,0.));
#311 = DEFINITIONAL_REPRESENTATION('',(#312),#316);
#312 = ( BOUNDED_CURVE() B_SPLINE_CURVE(2,(#313,#314,#315),
.UNSPECIFIED.,.F.,.F.) B_SPLINE_CURVE_WITH_KNOTS((3,3),(-2.75311310206,
2.75311310206),.PIECEWISE_BEZIER_KNOTS.) CURVE() 
GEOMETRIC_REPRESENTATION_ITEM() RATIONAL_B_SPLINE_CURVE((1.,
7.877567014343,1.)) REPRESENTATION_ITEM('') );
#313 = CARTESIAN_POINT('',(8.336803505748,-27.934506));
#314 = CARTESIAN_POINT('',(4.055271519663,-35.29034320118));
#315 = CARTESIAN_POINT('',(-0.226260466421,-27.934506));
#316 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#317 = ORIENTED_EDGE('',*,*,#318,.F.);
#318 = EDGE_CURVE('',#319,#256,#321,.T.);
#319 = VERTEX_POINT('',#320);
#320 = CARTESIAN_POINT('',(52.793980384446,121.80578276606,14.565494));
#321 = SURFACE_CURVE('',#322,(#327,#338),.PCURVE_S1.);
#322 = HYPERBOLA('',#323,7.271456937167,4.198177620074);
#323 = AXIS2_PLACEMENT_3D('',#324,#325,#326);
#324 = CARTESIAN_POINT('',(51.790455827049,121.8057306144,7.089180027919
    ));
#325 = DIRECTION('',(5.196848995186E-05,-0.99999999865,0.));
#326 = DIRECTION('',(0.,0.,1.));
#327 = PCURVE('',#175,#328);
#328 = DEFINITIONAL_REPRESENTATION('',(#329),#337);
#329 = B_SPLINE_CURVE_WITH_KNOTS('',6,(#330,#331,#332,#333,#334,#335,
    #336),.UNSPECIFIED.,.F.,.F.,(7,7),(-0.236818344206,0.236818344206),
  .PIECEWISE_BEZIER_KNOTS.);
#330 = CARTESIAN_POINT('',(1.336212989617,7.));
#331 = CARTESIAN_POINT('',(1.412989433118,6.862790962404));
#332 = CARTESIAN_POINT('',(1.491456369711,6.781487734422));
#333 = CARTESIAN_POINT('',(1.570848295285,6.754551309043));
#334 = CARTESIAN_POINT('',(1.650240220859,6.781487734422));
#335 = CARTESIAN_POINT('',(1.728707157451,6.862790962404));
#336 = CARTESIAN_POINT('',(1.805483600953,7.));
#337 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#338 = PCURVE('',#339,#344);
#339 = PLANE('',#340);
#340 = AXIS2_PLACEMENT_3D('',#341,#342,#343);
#341 = CARTESIAN_POINT('',(51.008936,121.80569,42.5));
#342 = DIRECTION('',(5.196848995186E-05,-0.99999999865,0.));
#343 = DIRECTION('',(0.99999999865,5.196848995186E-05,0.));
#344 = DEFINITIONAL_REPRESENTATION('',(#345),#349);
#345 = ( BOUNDED_CURVE() B_SPLINE_CURVE(2,(#346,#347,#348),
.UNSPECIFIED.,.F.,.F.) B_SPLINE_CURVE_WITH_KNOTS((3,3),(-0.236818344206,
0.236818344206),.PIECEWISE_BEZIER_KNOTS.) CURVE() 
GEOMETRIC_REPRESENTATION_ITEM() RATIONAL_B_SPLINE_CURVE((1.,
1.028172763269,1.)) REPRESENTATION_ITEM('') );
#346 = CARTESIAN_POINT('',(1.785044386857,-27.934506));
#347 = CARTESIAN_POINT('',(0.781519828104,-28.33860682178));
#348 = CARTESIAN_POINT('',(-0.222004730649,-27.934506));
#349 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#350 = ORIENTED_EDGE('',*,*,#351,.F.);
#351 = EDGE_CURVE('',#167,#319,#352,.T.);
#352 = SURFACE_CURVE('',#353,(#358,#365),.PCURVE_S1.);
#353 = CIRCLE('',#354,4.316451884327);
#354 = AXIS2_PLACEMENT_3D('',#355,#356,#357);
#355 = CARTESIAN_POINT('',(51.790674,117.607553,14.565494));
#356 = DIRECTION('',(0.,0.,1.));
#357 = DIRECTION('',(1.,0.,0.));
#358 = PCURVE('',#175,#359);
#359 = DEFINITIONAL_REPRESENTATION('',(#360),#364);
#360 = LINE('',#361,#362);
#361 = CARTESIAN_POINT('',(0.,7.));
#362 = VECTOR('',#363,1.);
#363 = DIRECTION('',(1.,0.));
#364 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#365 = PCURVE('',#211,#366);
#366 = DEFINITIONAL_REPRESENTATION('',(#367),#371);
#367 = CIRCLE('',#368,4.316451884327);
#368 = AXIS2_PLACEMENT_2D('',#369,#370);
#369 = CARTESIAN_POINT('',(0.,0.));
#370 = DIRECTION('',(1.,0.));
#371 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#372 = ORIENTED_EDGE('',*,*,#164,.F.);
#373 = ORIENTED_EDGE('',*,*,#374,.T.);
#374 = EDGE_CURVE('',#165,#165,#375,.T.);
#375 = SURFACE_CURVE('',#376,(#381,#388),.PCURVE_S1.);
#376 = CIRCLE('',#377,0.275);
#377 = AXIS2_PLACEMENT_3D('',#378,#379,#380);
#378 = CARTESIAN_POINT('',(51.790674,117.607553,7.565494));
#379 = DIRECTION('',(0.,0.,1.));
#380 = DIRECTION('',(1.,0.,0.));
#381 = PCURVE('',#175,#382);
#382 = DEFINITIONAL_REPRESENTATION('',(#383),#387);
#383 = LINE('',#384,#385);
#384 = CARTESIAN_POINT('',(0.,0.));
#385 = VECTOR('',#386,1.);
#386 = DIRECTION('',(1.,0.));
#387 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#388 = PCURVE('',#389,#394);
#389 = CYLINDRICAL_SURFACE('',#390,0.275);
#390 = AXIS2_PLACEMENT_3D('',#391,#392,#393);
#391 = CARTESIAN_POINT('',(51.790674,117.607553,7.565494));
#392 = DIRECTION('',(0.,0.,1.));
#393 = DIRECTION('',(1.,0.,0.));
#394 = DEFINITIONAL_REPRESENTATION('',(#395),#398);
#395 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#396,#397),.UNSPECIFIED.,.F.,.F.,
  (2,2),(0.,6.28318530718),.PIECEWISE_BEZIER_KNOTS.);
#396 = CARTESIAN_POINT('',(0.,0.));
#397 = CARTESIAN_POINT('',(6.28318530718,0.));
#398 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#399 = ADVANCED_FACE('',(#400),#389,.F.);
#400 = FACE_BOUND('',#401,.F.);
#401 = EDGE_LOOP('',(#402,#426,#447,#448));
#402 = ORIENTED_EDGE('',*,*,#403,.F.);
#403 = EDGE_CURVE('',#404,#404,#406,.T.);
#404 = VERTEX_POINT('',#405);
#405 = CARTESIAN_POINT('',(52.065674,117.607553,14.565494));
#406 = SURFACE_CURVE('',#407,(#412,#419),.PCURVE_S1.);
#407 = CIRCLE('',#408,0.275);
#408 = AXIS2_PLACEMENT_3D('',#409,#410,#411);
#409 = CARTESIAN_POINT('',(51.790674,117.607553,14.565494));
#410 = DIRECTION('',(0.,0.,1.));
#411 = DIRECTION('',(1.,0.,0.));
#412 = PCURVE('',#389,#413);
#413 = DEFINITIONAL_REPRESENTATION('',(#414),#418);
#414 = LINE('',#415,#416);
#415 = CARTESIAN_POINT('',(0.,7.));
#416 = VECTOR('',#417,1.);
#417 = DIRECTION('',(1.,0.));
#418 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#419 = PCURVE('',#211,#420);
#420 = DEFINITIONAL_REPRESENTATION('',(#421),#425);
#421 = CIRCLE('',#422,0.275);
#422 = AXIS2_PLACEMENT_2D('',#423,#424);
#423 = CARTESIAN_POINT('',(0.,0.));
#424 = DIRECTION('',(1.,0.));
#425 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#426 = ORIENTED_EDGE('',*,*,#427,.F.);
#427 = EDGE_CURVE('',#165,#404,#428,.T.);
#428 = SEAM_CURVE('',#429,(#433,#440),.PCURVE_S1.);
#429 = LINE('',#430,#431);
#430 = CARTESIAN_POINT('',(52.065674,117.607553,7.565494));
#431 = VECTOR('',#432,1.);
#432 = DIRECTION('',(0.,0.,1.));
#433 = PCURVE('',#389,#434);
#434 = DEFINITIONAL_REPRESENTATION('',(#435),#439);
#435 = LINE('',#436,#437);
#436 = CARTESIAN_POINT('',(6.28318530718,-0.));
#437 = VECTOR('',#438,1.);
#438 = DIRECTION('',(0.,1.));
#439 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#440 = PCURVE('',#389,#441);
#441 = DEFINITIONAL_REPRESENTATION('',(#442),#446);
#442 = LINE('',#443,#444);
#443 = CARTESIAN_POINT('',(0.,-0.));
#444 = VECTOR('',#445,1.);
#445 = DIRECTION('',(0.,1.));
#446 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#447 = ORIENTED_EDGE('',*,*,#374,.T.);
#448 = ORIENTED_EDGE('',*,*,#427,.T.);
#449 = ADVANCED_FACE('',(#450,#515),#211,.T.);
#450 = FACE_BOUND('',#451,.T.);
#451 = EDGE_LOOP('',(#452,#453,#454,#475,#496));
#452 = ORIENTED_EDGE('',*,*,#194,.T.);
#453 = ORIENTED_EDGE('',*,*,#351,.T.);
#454 = ORIENTED_EDGE('',*,*,#455,.F.);
#455 = EDGE_CURVE('',#456,#319,#458,.T.);
#456 = VERTEX_POINT('',#457);
#457 = CARTESIAN_POINT('',(51.008936,121.80569,14.565494));
#458 = SURFACE_CURVE('',#459,(#463,#469),.PCURVE_S1.);
#459 = LINE('',#460,#461);
#460 = CARTESIAN_POINT('',(51.399695913524,121.8057103072,14.565494));
#461 = VECTOR('',#462,1.);
#462 = DIRECTION('',(0.99999999865,5.196848995186E-05,-0.));
#463 = PCURVE('',#211,#464);
#464 = DEFINITIONAL_REPRESENTATION('',(#465),#468);
#465 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#466,#467),.UNSPECIFIED.,.F.,.F.,
  (2,2),(-0.390759914052,4.70742997716),.PIECEWISE_BEZIER_KNOTS.);
#466 = CARTESIAN_POINT('',(-0.781738,4.198137));
#467 = CARTESIAN_POINT('',(4.316451884327,4.19840194523));
#468 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#469 = PCURVE('',#339,#470);
#470 = DEFINITIONAL_REPRESENTATION('',(#471),#474);
#471 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#472,#473),.UNSPECIFIED.,.F.,.F.,
  (2,2),(-0.390759914052,4.70742997716),.PIECEWISE_BEZIER_KNOTS.);
#472 = CARTESIAN_POINT('',(-7.105427357601E-15,-27.934506));
#473 = CARTESIAN_POINT('',(5.098189891212,-27.934506));
#474 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#475 = ORIENTED_EDGE('',*,*,#476,.F.);
#476 = EDGE_CURVE('',#477,#456,#479,.T.);
#477 = VERTEX_POINT('',#478);
#478 = CARTESIAN_POINT('',(51.468253,113.528152,14.565494));
#479 = SURFACE_CURVE('',#480,(#484,#490),.PCURVE_S1.);
#480 = LINE('',#481,#482);
#481 = CARTESIAN_POINT('',(51.355913188898,115.55267331166,14.565494));
#482 = VECTOR('',#483,1.);
#483 = DIRECTION('',(-5.54043351018E-02,0.998464000178,0.));
#484 = PCURVE('',#211,#485);
#485 = DEFINITIONAL_REPRESENTATION('',(#486),#489);
#486 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#487,#488),.UNSPECIFIED.,.F.,.F.,
  (2,2),(-2.027635759832,6.262636096262),.PIECEWISE_BEZIER_KNOTS.);
#487 = CARTESIAN_POINT('',(-0.322421,-4.079401));
#488 = CARTESIAN_POINT('',(-0.781738,4.198137));
#489 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#490 = PCURVE('',#306,#491);
#491 = DEFINITIONAL_REPRESENTATION('',(#492),#495);
#492 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#493,#494),.UNSPECIFIED.,.F.,.F.,
  (2,2),(-2.027635759832,6.262636096262),.PIECEWISE_BEZIER_KNOTS.);
#493 = CARTESIAN_POINT('',(-1.42108547152E-14,-27.934506));
#494 = CARTESIAN_POINT('',(8.290271856093,-27.934506));
#495 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#496 = ORIENTED_EDGE('',*,*,#497,.F.);
#497 = EDGE_CURVE('',#195,#477,#498,.T.);
#498 = SURFACE_CURVE('',#499,(#503,#509),.PCURVE_S1.);
#499 = LINE('',#500,#501);
#500 = CARTESIAN_POINT('',(50.920140285499,114.93290717929,14.565494));
#501 = VECTOR('',#502,1.);
#502 = DIRECTION('',(-0.363493894237,0.931596580528,0.));
#503 = PCURVE('',#211,#504);
#504 = DEFINITIONAL_REPRESENTATION('',(#505),#508);
#505 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#506,#507),.UNSPECIFIED.,.F.,.F.,
  (2,2),(-1.762357331425,-1.507900746588),.PIECEWISE_BEZIER_KNOTS.);
#506 = CARTESIAN_POINT('',(-0.229927585063,-4.316451884327));
#507 = CARTESIAN_POINT('',(-0.322421,-4.079401));
#508 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#509 = PCURVE('',#243,#510);
#510 = DEFINITIONAL_REPRESENTATION('',(#511),#514);
#511 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#512,#513),.UNSPECIFIED.,.F.,.F.,
  (2,2),(-1.762357331425,-1.507900746588),.PIECEWISE_BEZIER_KNOTS.);
#512 = CARTESIAN_POINT('',(0.412899879317,-27.934506));
#513 = CARTESIAN_POINT('',(0.667356464155,-27.934506));
#514 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#515 = FACE_BOUND('',#516,.T.);
#516 = EDGE_LOOP('',(#517));
#517 = ORIENTED_EDGE('',*,*,#403,.F.);
#518 = ADVANCED_FACE('',(#519),#339,.F.);
#519 = FACE_BOUND('',#520,.F.);
#520 = EDGE_LOOP('',(#521,#522,#543));
#521 = ORIENTED_EDGE('',*,*,#455,.F.);
#522 = ORIENTED_EDGE('',*,*,#523,.T.);
#523 = EDGE_CURVE('',#456,#256,#524,.T.);
#524 = SURFACE_CURVE('',#525,(#529,#536),.PCURVE_S1.);
#525 = LINE('',#526,#527);
#526 = CARTESIAN_POINT('',(51.008936,121.80569,42.5));
#527 = VECTOR('',#528,1.);
#528 = DIRECTION('',(0.,0.,-1.));
#529 = PCURVE('',#339,#530);
#530 = DEFINITIONAL_REPRESENTATION('',(#531),#535);
#531 = LINE('',#532,#533);
#532 = CARTESIAN_POINT('',(0.,0.));
#533 = VECTOR('',#534,1.);
#534 = DIRECTION('',(0.,-1.));
#535 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#536 = PCURVE('',#306,#537);
#537 = DEFINITIONAL_REPRESENTATION('',(#538),#542);
#538 = LINE('',#539,#540);
#539 = CARTESIAN_POINT('',(8.290271856093,0.));
#540 = VECTOR('',#541,1.);
#541 = DIRECTION('',(0.,-1.));
#542 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#543 = ORIENTED_EDGE('',*,*,#318,.F.);
#544 = ADVANCED_FACE('',(#545),#306,.F.);
#545 = FACE_BOUND('',#546,.F.);
#546 = EDGE_LOOP('',(#547,#548,#569,#570));
#547 = ORIENTED_EDGE('',*,*,#476,.F.);
#548 = ORIENTED_EDGE('',*,*,#549,.T.);
#549 = EDGE_CURVE('',#477,#224,#550,.T.);
#550 = SURFACE_CURVE('',#551,(#555,#562),.PCURVE_S1.);
#551 = LINE('',#552,#553);
#552 = CARTESIAN_POINT('',(51.468253,113.528152,42.5));
#553 = VECTOR('',#554,1.);
#554 = DIRECTION('',(0.,0.,-1.));
#555 = PCURVE('',#306,#556);
#556 = DEFINITIONAL_REPRESENTATION('',(#557),#561);
#557 = LINE('',#558,#559);
#558 = CARTESIAN_POINT('',(0.,0.));
#559 = VECTOR('',#560,1.);
#560 = DIRECTION('',(0.,-1.));
#561 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#562 = PCURVE('',#243,#563);
#563 = DEFINITIONAL_REPRESENTATION('',(#564),#568);
#564 = LINE('',#565,#566);
#565 = CARTESIAN_POINT('',(0.667356464155,0.));
#566 = VECTOR('',#567,1.);
#567 = DIRECTION('',(0.,-1.));
#568 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#569 = ORIENTED_EDGE('',*,*,#255,.F.);
#570 = ORIENTED_EDGE('',*,*,#523,.F.);
#571 = ADVANCED_FACE('',(#572),#243,.F.);
#572 = FACE_BOUND('',#573,.F.);
#573 = EDGE_LOOP('',(#574,#575,#576));
#574 = ORIENTED_EDGE('',*,*,#549,.F.);
#575 = ORIENTED_EDGE('',*,*,#497,.F.);
#576 = ORIENTED_EDGE('',*,*,#223,.F.);
#577 = ( GEOMETRIC_REPRESENTATION_CONTEXT(3) 
GLOBAL_UNCERTAINTY_ASSIGNED_CONTEXT((#581)) GLOBAL_UNIT_ASSIGNED_CONTEXT
((#578,#579,#580)) REPRESENTATION_CONTEXT('Context #1',
  '3D Context with UNIT and UNCERTAINTY') );
#578 = ( LENGTH_UNIT() NAMED_UNIT(*) SI_UNIT(.MILLI.,.METRE.) );
#579 = ( NAMED_UNIT(*) PLANE_ANGLE_UNIT() SI_UNIT($,.RADIAN.) );
#580 = ( NAMED_UNIT(*) SI_UNIT($,.STERADIAN.) SOLID_ANGLE_UNIT() );
#581 = UNCERTAINTY_MEASURE_WITH_UNIT(LENGTH_MEASURE(1.E-07),#578,
  'distance_accuracy_value','confusion accuracy');
#582 = CONTEXT_DEPENDENT_SHAPE_REPRESENTATION(#583,#585);
#583 = ( REPRESENTATION_RELATIONSHIP('','',#157,#10) 
REPRESENTATION_RELATIONSHIP_WITH_TRANSFORMATION(#584) 
SHAPE_REPRESENTATION_RELATIONSHIP() );
#584 = ITEM_DEFINED_TRANSFORMATION('','',#11,#19);
#585 = PRODUCT_DEFINITION_SHAPE('Placement','Placement of an item',#586
  );
#586 = NEXT_ASSEMBLY_USAGE_OCCURRENCE('106','=>[0:1:1:3]','',#5,#152,$);
#587 = PRODUCT_RELATED_PRODUCT_CATEGORY('part',$,(#154));
#588 = SHAPE_DEFINITION_REPRESENTATION(#589,#595);
#589 = PRODUCT_DEFINITION_SHAPE('','',#590);
#590 = PRODUCT_DEFINITION('design','',#591,#594);
#591 = PRODUCT_DEFINITION_FORMATION('','',#592);
#592 = PRODUCT('PC2-P1_Part3','PC2-P1_Part3','',(#593));
#593 = PRODUCT_CONTEXT('',#2,'mechanical');
#594 = PRODUCT_DEFINITION_CONTEXT('part definition',#2,'design');
#595 = ADVANCED_BREP_SHAPE_REPRESENTATION('',(#11,#596),#1000);
#596 = MANIFOLD_SOLID_BREP('',#597);
#597 = CLOSED_SHELL('',(#598,#720,#821,#868,#939,#966,#973));
#598 = ADVANCED_FACE('',(#599),#614,.T.);
#599 = FACE_BOUND('',#600,.T.);
#600 = EDGE_LOOP('',(#601,#637,#663,#696));
#601 = ORIENTED_EDGE('',*,*,#602,.T.);
#602 = EDGE_CURVE('',#603,#605,#607,.T.);
#603 = VERTEX_POINT('',#604);
#604 = CARTESIAN_POINT('',(51.558304183836,113.29736029685,14.565494));
#605 = VERTEX_POINT('',#606);
#606 = CARTESIAN_POINT('',(52.793980384446,121.80578276606,14.565494));
#607 = SURFACE_CURVE('',#608,(#613,#625),.PCURVE_S1.);
#608 = CIRCLE('',#609,4.316451884327);
#609 = AXIS2_PLACEMENT_3D('',#610,#611,#612);
#610 = CARTESIAN_POINT('',(51.790674,117.607553,14.565494));
#611 = DIRECTION('',(0.,0.,1.));
#612 = DIRECTION('',(-5.383352401259E-02,-0.998549924487,0.));
#613 = PCURVE('',#614,#619);
#614 = CYLINDRICAL_SURFACE('',#615,4.316451884327);
#615 = AXIS2_PLACEMENT_3D('',#616,#617,#618);
#616 = CARTESIAN_POINT('',(51.790674,117.607553,14.565494));
#617 = DIRECTION('',(0.,0.,1.));
#618 = DIRECTION('',(1.,0.,0.));
#619 = DEFINITIONAL_REPRESENTATION('',(#620),#624);
#620 = LINE('',#621,#622);
#621 = CARTESIAN_POINT('',(4.658529420378,0.));
#622 = VECTOR('',#623,1.);
#623 = DIRECTION('',(1.,0.));
#624 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#625 = PCURVE('',#626,#631);
#626 = PLANE('',#627);
#627 = AXIS2_PLACEMENT_3D('',#628,#629,#630);
#628 = CARTESIAN_POINT('',(51.790674,117.607553,14.565494));
#629 = DIRECTION('',(0.,0.,1.));
#630 = DIRECTION('',(1.,0.,0.));
#631 = DEFINITIONAL_REPRESENTATION('',(#632),#636);
#632 = CIRCLE('',#633,4.316451884327);
#633 = AXIS2_PLACEMENT_2D('',#634,#635);
#634 = CARTESIAN_POINT('',(0.,0.));
#635 = DIRECTION('',(-5.383352401259E-02,-0.998549924487));
#636 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#637 = ORIENTED_EDGE('',*,*,#638,.T.);
#638 = EDGE_CURVE('',#605,#639,#641,.T.);
#639 = VERTEX_POINT('',#640);
#640 = CARTESIAN_POINT('',(52.793980384446,121.80578276606,32.5));
#641 = SURFACE_CURVE('',#642,(#646,#652),.PCURVE_S1.);
#642 = LINE('',#643,#644);
#643 = CARTESIAN_POINT('',(52.793980384446,121.80578276606,14.565494));
#644 = VECTOR('',#645,1.);
#645 = DIRECTION('',(0.,0.,1.));
#646 = PCURVE('',#614,#647);
#647 = DEFINITIONAL_REPRESENTATION('',(#648),#651);
#648 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#649,#650),.UNSPECIFIED.,.F.,.F.,
  (2,2),(0.,17.934506),.PIECEWISE_BEZIER_KNOTS.);
#649 = CARTESIAN_POINT('',(7.619398296797,0.));
#650 = CARTESIAN_POINT('',(7.619398296797,17.934506));
#651 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#652 = PCURVE('',#653,#658);
#653 = PLANE('',#654);
#654 = AXIS2_PLACEMENT_3D('',#655,#656,#657);
#655 = CARTESIAN_POINT('',(51.008936,121.80569,42.5));
#656 = DIRECTION('',(5.196848995186E-05,-0.99999999865,0.));
#657 = DIRECTION('',(0.99999999865,5.196848995186E-05,0.));
#658 = DEFINITIONAL_REPRESENTATION('',(#659),#662);
#659 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#660,#661),.UNSPECIFIED.,.F.,.F.,
  (2,2),(-5.999999999062E-07,17.9345066),.PIECEWISE_BEZIER_KNOTS.);
#660 = CARTESIAN_POINT('',(1.785044386857,-27.9345066));
#661 = CARTESIAN_POINT('',(1.785044386857,-9.9999994));
#662 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#663 = ORIENTED_EDGE('',*,*,#664,.T.);
#664 = EDGE_CURVE('',#639,#665,#667,.T.);
#665 = VERTEX_POINT('',#666);
#666 = CARTESIAN_POINT('',(51.558304183836,113.29736029685,32.5));
#667 = SURFACE_CURVE('',#668,(#673,#680),.PCURVE_S1.);
#668 = CIRCLE('',#669,4.316451884327);
#669 = AXIS2_PLACEMENT_3D('',#670,#671,#672);
#670 = CARTESIAN_POINT('',(51.790674,117.607553,32.5));
#671 = DIRECTION('',(0.,0.,-1.));
#672 = DIRECTION('',(0.232437754742,0.972611273927,0.));
#673 = PCURVE('',#614,#674);
#674 = DEFINITIONAL_REPRESENTATION('',(#675),#679);
#675 = LINE('',#676,#677);
#676 = CARTESIAN_POINT('',(7.619398296797,17.934506));
#677 = VECTOR('',#678,1.);
#678 = DIRECTION('',(-1.,-0.));
#679 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#680 = PCURVE('',#681,#686);
#681 = PLANE('',#682);
#682 = AXIS2_PLACEMENT_3D('',#683,#684,#685);
#683 = CARTESIAN_POINT('',(51.790674,117.607553,32.5));
#684 = DIRECTION('',(0.,0.,1.));
#685 = DIRECTION('',(1.,0.,0.));
#686 = DEFINITIONAL_REPRESENTATION('',(#687),#695);
#687 = ( BOUNDED_CURVE() B_SPLINE_CURVE(2,(#688,#689,#690,#691,#692,#693
,#694),.UNSPECIFIED.,.T.,.F.) B_SPLINE_CURVE_WITH_KNOTS((1,2,2,2,2,1),(
    -2.094395102393,0.,2.094395102393,4.188790204786,6.28318530718,
8.377580409573),.UNSPECIFIED.) CURVE() GEOMETRIC_REPRESENTATION_ITEM() 
RATIONAL_B_SPLINE_CURVE((1.,0.5,1.,0.5,1.,0.5,1.)) REPRESENTATION_ITEM(
  '') );
#688 = CARTESIAN_POINT('',(1.003306384446,4.198229766061));
#689 = CARTESIAN_POINT('',(8.274853641112,2.460452132642));
#690 = CARTESIAN_POINT('',(3.13412043611,-2.96800369974));
#691 = CARTESIAN_POINT('',(-2.006612768892,-8.396459532123));
#692 = CARTESIAN_POINT('',(-4.137426820556,-1.230226066321));
#693 = CARTESIAN_POINT('',(-6.26824087222,5.93600739948));
#694 = CARTESIAN_POINT('',(1.003306384446,4.198229766061));
#695 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#696 = ORIENTED_EDGE('',*,*,#697,.F.);
#697 = EDGE_CURVE('',#603,#665,#698,.T.);
#698 = SURFACE_CURVE('',#699,(#703,#709),.PCURVE_S1.);
#699 = LINE('',#700,#701);
#700 = CARTESIAN_POINT('',(51.558304183836,113.29736029685,14.565494));
#701 = VECTOR('',#702,1.);
#702 = DIRECTION('',(0.,0.,1.));
#703 = PCURVE('',#614,#704);
#704 = DEFINITIONAL_REPRESENTATION('',(#705),#708);
#705 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#706,#707),.UNSPECIFIED.,.F.,.F.,
  (2,2),(0.,17.934506),.PIECEWISE_BEZIER_KNOTS.);
#706 = CARTESIAN_POINT('',(4.658529420378,0.));
#707 = CARTESIAN_POINT('',(4.658529420378,17.934506));
#708 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#709 = PCURVE('',#710,#715);
#710 = PLANE('',#711);
#711 = AXIS2_PLACEMENT_3D('',#712,#713,#714);
#712 = CARTESIAN_POINT('',(51.710833,112.906445,42.5));
#713 = DIRECTION('',(0.931596580528,0.363493894237,0.));
#714 = DIRECTION('',(-0.363493894237,0.931596580528,0.));
#715 = DEFINITIONAL_REPRESENTATION('',(#716),#719);
#716 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#717,#718),.UNSPECIFIED.,.F.,.F.,
  (2,2),(-5.999999999062E-07,17.9345066),.PIECEWISE_BEZIER_KNOTS.);
#717 = CARTESIAN_POINT('',(0.419618647197,-27.9345066));
#718 = CARTESIAN_POINT('',(0.419618647197,-9.9999994));
#719 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#720 = ADVANCED_FACE('',(#721,#790),#626,.F.);
#721 = FACE_BOUND('',#722,.F.);
#722 = EDGE_LOOP('',(#723,#724,#745,#771));
#723 = ORIENTED_EDGE('',*,*,#602,.T.);
#724 = ORIENTED_EDGE('',*,*,#725,.F.);
#725 = EDGE_CURVE('',#726,#605,#728,.T.);
#726 = VERTEX_POINT('',#727);
#727 = CARTESIAN_POINT('',(51.008936,121.80569,14.565494));
#728 = SURFACE_CURVE('',#729,(#733,#739),.PCURVE_S1.);
#729 = LINE('',#730,#731);
#730 = CARTESIAN_POINT('',(51.399695913524,121.8057103072,14.565494));
#731 = VECTOR('',#732,1.);
#732 = DIRECTION('',(0.99999999865,5.196848995186E-05,-0.));
#733 = PCURVE('',#626,#734);
#734 = DEFINITIONAL_REPRESENTATION('',(#735),#738);
#735 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#736,#737),.UNSPECIFIED.,.F.,.F.,
  (2,2),(-0.390759914052,4.70742997716),.PIECEWISE_BEZIER_KNOTS.);
#736 = CARTESIAN_POINT('',(-0.781738,4.198137));
#737 = CARTESIAN_POINT('',(4.316451884327,4.19840194523));
#738 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#739 = PCURVE('',#653,#740);
#740 = DEFINITIONAL_REPRESENTATION('',(#741),#744);
#741 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#742,#743),.UNSPECIFIED.,.F.,.F.,
  (2,2),(-0.390759914052,4.70742997716),.PIECEWISE_BEZIER_KNOTS.);
#742 = CARTESIAN_POINT('',(-7.105427357601E-15,-27.934506));
#743 = CARTESIAN_POINT('',(5.098189891212,-27.934506));
#744 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#745 = ORIENTED_EDGE('',*,*,#746,.F.);
#746 = EDGE_CURVE('',#747,#726,#749,.T.);
#747 = VERTEX_POINT('',#748);
#748 = CARTESIAN_POINT('',(51.468253,113.528152,14.565494));
#749 = SURFACE_CURVE('',#750,(#754,#760),.PCURVE_S1.);
#750 = LINE('',#751,#752);
#751 = CARTESIAN_POINT('',(51.355913188898,115.55267331166,14.565494));
#752 = VECTOR('',#753,1.);
#753 = DIRECTION('',(-5.54043351018E-02,0.998464000178,0.));
#754 = PCURVE('',#626,#755);
#755 = DEFINITIONAL_REPRESENTATION('',(#756),#759);
#756 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#757,#758),.UNSPECIFIED.,.F.,.F.,
  (2,2),(-2.027635759832,6.262636096262),.PIECEWISE_BEZIER_KNOTS.);
#757 = CARTESIAN_POINT('',(-0.322421,-4.079401));
#758 = CARTESIAN_POINT('',(-0.781738,4.198137));
#759 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#760 = PCURVE('',#761,#766);
#761 = PLANE('',#762);
#762 = AXIS2_PLACEMENT_3D('',#763,#764,#765);
#763 = CARTESIAN_POINT('',(51.468253,113.528152,42.5));
#764 = DIRECTION('',(0.998464000178,5.54043351018E-02,0.));
#765 = DIRECTION('',(-5.54043351018E-02,0.998464000178,0.));
#766 = DEFINITIONAL_REPRESENTATION('',(#767),#770);
#767 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#768,#769),.UNSPECIFIED.,.F.,.F.,
  (2,2),(-2.027635759832,6.262636096262),.PIECEWISE_BEZIER_KNOTS.);
#768 = CARTESIAN_POINT('',(-1.42108547152E-14,-27.934506));
#769 = CARTESIAN_POINT('',(8.290271856093,-27.934506));
#770 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#771 = ORIENTED_EDGE('',*,*,#772,.F.);
#772 = EDGE_CURVE('',#603,#747,#773,.T.);
#773 = SURFACE_CURVE('',#774,(#778,#784),.PCURVE_S1.);
#774 = LINE('',#775,#776);
#775 = CARTESIAN_POINT('',(50.920140285499,114.93290717929,14.565494));
#776 = VECTOR('',#777,1.);
#777 = DIRECTION('',(-0.363493894237,0.931596580528,0.));
#778 = PCURVE('',#626,#779);
#779 = DEFINITIONAL_REPRESENTATION('',(#780),#783);
#780 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#781,#782),.UNSPECIFIED.,.F.,.F.,
  (2,2),(-1.762357331425,-1.507900746588),.PIECEWISE_BEZIER_KNOTS.);
#781 = CARTESIAN_POINT('',(-0.229927585063,-4.316451884327));
#782 = CARTESIAN_POINT('',(-0.322421,-4.079401));
#783 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#784 = PCURVE('',#710,#785);
#785 = DEFINITIONAL_REPRESENTATION('',(#786),#789);
#786 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#787,#788),.UNSPECIFIED.,.F.,.F.,
  (2,2),(-1.762357331425,-1.507900746588),.PIECEWISE_BEZIER_KNOTS.);
#787 = CARTESIAN_POINT('',(0.412899879317,-27.934506));
#788 = CARTESIAN_POINT('',(0.667356464155,-27.934506));
#789 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#790 = FACE_BOUND('',#791,.F.);
#791 = EDGE_LOOP('',(#792));
#792 = ORIENTED_EDGE('',*,*,#793,.F.);
#793 = EDGE_CURVE('',#794,#794,#796,.T.);
#794 = VERTEX_POINT('',#795);
#795 = CARTESIAN_POINT('',(52.065674,117.607553,14.565494));
#796 = SURFACE_CURVE('',#797,(#802,#809),.PCURVE_S1.);
#797 = CIRCLE('',#798,0.275);
#798 = AXIS2_PLACEMENT_3D('',#799,#800,#801);
#799 = CARTESIAN_POINT('',(51.790674,117.607553,14.565494));
#800 = DIRECTION('',(0.,0.,1.));
#801 = DIRECTION('',(1.,0.,0.));
#802 = PCURVE('',#626,#803);
#803 = DEFINITIONAL_REPRESENTATION('',(#804),#808);
#804 = CIRCLE('',#805,0.275);
#805 = AXIS2_PLACEMENT_2D('',#806,#807);
#806 = CARTESIAN_POINT('',(0.,0.));
#807 = DIRECTION('',(1.,0.));
#808 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#809 = PCURVE('',#810,#815);
#810 = CYLINDRICAL_SURFACE('',#811,0.275);
#811 = AXIS2_PLACEMENT_3D('',#812,#813,#814);
#812 = CARTESIAN_POINT('',(51.790674,117.607553,14.565494));
#813 = DIRECTION('',(0.,0.,1.));
#814 = DIRECTION('',(1.,0.,0.));
#815 = DEFINITIONAL_REPRESENTATION('',(#816),#820);
#816 = LINE('',#817,#818);
#817 = CARTESIAN_POINT('',(0.,0.));
#818 = VECTOR('',#819,1.);
#819 = DIRECTION('',(1.,0.));
#820 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#821 = ADVANCED_FACE('',(#822),#710,.F.);
#822 = FACE_BOUND('',#823,.F.);
#823 = EDGE_LOOP('',(#824,#847,#866,#867));
#824 = ORIENTED_EDGE('',*,*,#825,.F.);
#825 = EDGE_CURVE('',#826,#747,#828,.T.);
#826 = VERTEX_POINT('',#827);
#827 = CARTESIAN_POINT('',(51.468253,113.528152,32.5));
#828 = SURFACE_CURVE('',#829,(#833,#840),.PCURVE_S1.);
#829 = LINE('',#830,#831);
#830 = CARTESIAN_POINT('',(51.468253,113.528152,42.5));
#831 = VECTOR('',#832,1.);
#832 = DIRECTION('',(0.,0.,-1.));
#833 = PCURVE('',#710,#834);
#834 = DEFINITIONAL_REPRESENTATION('',(#835),#839);
#835 = LINE('',#836,#837);
#836 = CARTESIAN_POINT('',(0.667356464155,0.));
#837 = VECTOR('',#838,1.);
#838 = DIRECTION('',(0.,-1.));
#839 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#840 = PCURVE('',#761,#841);
#841 = DEFINITIONAL_REPRESENTATION('',(#842),#846);
#842 = LINE('',#843,#844);
#843 = CARTESIAN_POINT('',(0.,0.));
#844 = VECTOR('',#845,1.);
#845 = DIRECTION('',(0.,-1.));
#846 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#847 = ORIENTED_EDGE('',*,*,#848,.F.);
#848 = EDGE_CURVE('',#665,#826,#849,.T.);
#849 = SURFACE_CURVE('',#850,(#854,#860),.PCURVE_S1.);
#850 = LINE('',#851,#852);
#851 = CARTESIAN_POINT('',(50.920140285499,114.93290717929,32.5));
#852 = VECTOR('',#853,1.);
#853 = DIRECTION('',(-0.363493894237,0.931596580528,0.));
#854 = PCURVE('',#710,#855);
#855 = DEFINITIONAL_REPRESENTATION('',(#856),#859);
#856 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#857,#858),.UNSPECIFIED.,.F.,.F.,
  (2,2),(-1.762357331425,-1.507900746588),.PIECEWISE_BEZIER_KNOTS.);
#857 = CARTESIAN_POINT('',(0.412899879317,-10.));
#858 = CARTESIAN_POINT('',(0.667356464155,-10.));
#859 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#860 = PCURVE('',#681,#861);
#861 = DEFINITIONAL_REPRESENTATION('',(#862),#865);
#862 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#863,#864),.UNSPECIFIED.,.F.,.F.,
  (2,2),(-1.762357331425,-1.507900746588),.PIECEWISE_BEZIER_KNOTS.);
#863 = CARTESIAN_POINT('',(-0.229927585063,-4.316451884327));
#864 = CARTESIAN_POINT('',(-0.322421,-4.079401));
#865 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#866 = ORIENTED_EDGE('',*,*,#697,.F.);
#867 = ORIENTED_EDGE('',*,*,#772,.T.);
#868 = ADVANCED_FACE('',(#869,#913),#681,.T.);
#869 = FACE_BOUND('',#870,.T.);
#870 = EDGE_LOOP('',(#871,#872,#873,#894));
#871 = ORIENTED_EDGE('',*,*,#848,.F.);
#872 = ORIENTED_EDGE('',*,*,#664,.F.);
#873 = ORIENTED_EDGE('',*,*,#874,.F.);
#874 = EDGE_CURVE('',#875,#639,#877,.T.);
#875 = VERTEX_POINT('',#876);
#876 = CARTESIAN_POINT('',(51.008936,121.80569,32.5));
#877 = SURFACE_CURVE('',#878,(#882,#888),.PCURVE_S1.);
#878 = LINE('',#879,#880);
#879 = CARTESIAN_POINT('',(51.399695913524,121.8057103072,32.5));
#880 = VECTOR('',#881,1.);
#881 = DIRECTION('',(0.99999999865,5.196848995186E-05,-0.));
#882 = PCURVE('',#681,#883);
#883 = DEFINITIONAL_REPRESENTATION('',(#884),#887);
#884 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#885,#886),.UNSPECIFIED.,.F.,.F.,
  (2,2),(-0.390759914052,4.70742997716),.PIECEWISE_BEZIER_KNOTS.);
#885 = CARTESIAN_POINT('',(-0.781738,4.198137));
#886 = CARTESIAN_POINT('',(4.316451884327,4.19840194523));
#887 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#888 = PCURVE('',#653,#889);
#889 = DEFINITIONAL_REPRESENTATION('',(#890),#893);
#890 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#891,#892),.UNSPECIFIED.,.F.,.F.,
  (2,2),(-0.390759914052,4.70742997716),.PIECEWISE_BEZIER_KNOTS.);
#891 = CARTESIAN_POINT('',(-7.105427357601E-15,-10.));
#892 = CARTESIAN_POINT('',(5.098189891212,-10.));
#893 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#894 = ORIENTED_EDGE('',*,*,#895,.F.);
#895 = EDGE_CURVE('',#826,#875,#896,.T.);
#896 = SURFACE_CURVE('',#897,(#901,#907),.PCURVE_S1.);
#897 = LINE('',#898,#899);
#898 = CARTESIAN_POINT('',(51.355913188898,115.55267331166,32.5));
#899 = VECTOR('',#900,1.);
#900 = DIRECTION('',(-5.54043351018E-02,0.998464000178,0.));
#901 = PCURVE('',#681,#902);
#902 = DEFINITIONAL_REPRESENTATION('',(#903),#906);
#903 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#904,#905),.UNSPECIFIED.,.F.,.F.,
  (2,2),(-2.027635759832,6.262636096262),.PIECEWISE_BEZIER_KNOTS.);
#904 = CARTESIAN_POINT('',(-0.322421,-4.079401));
#905 = CARTESIAN_POINT('',(-0.781738,4.198137));
#906 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#907 = PCURVE('',#761,#908);
#908 = DEFINITIONAL_REPRESENTATION('',(#909),#912);
#909 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#910,#911),.UNSPECIFIED.,.F.,.F.,
  (2,2),(-2.027635759832,6.262636096262),.PIECEWISE_BEZIER_KNOTS.);
#910 = CARTESIAN_POINT('',(-1.42108547152E-14,-10.));
#911 = CARTESIAN_POINT('',(8.290271856093,-10.));
#912 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#913 = FACE_BOUND('',#914,.T.);
#914 = EDGE_LOOP('',(#915));
#915 = ORIENTED_EDGE('',*,*,#916,.F.);
#916 = EDGE_CURVE('',#917,#917,#919,.T.);
#917 = VERTEX_POINT('',#918);
#918 = CARTESIAN_POINT('',(52.065674,117.607553,32.5));
#919 = SURFACE_CURVE('',#920,(#925,#932),.PCURVE_S1.);
#920 = CIRCLE('',#921,0.275);
#921 = AXIS2_PLACEMENT_3D('',#922,#923,#924);
#922 = CARTESIAN_POINT('',(51.790674,117.607553,32.5));
#923 = DIRECTION('',(0.,0.,1.));
#924 = DIRECTION('',(1.,0.,0.));
#925 = PCURVE('',#681,#926);
#926 = DEFINITIONAL_REPRESENTATION('',(#927),#931);
#927 = CIRCLE('',#928,0.275);
#928 = AXIS2_PLACEMENT_2D('',#929,#930);
#929 = CARTESIAN_POINT('',(0.,0.));
#930 = DIRECTION('',(1.,0.));
#931 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#932 = PCURVE('',#810,#933);
#933 = DEFINITIONAL_REPRESENTATION('',(#934),#938);
#934 = LINE('',#935,#936);
#935 = CARTESIAN_POINT('',(0.,17.934506));
#936 = VECTOR('',#937,1.);
#937 = DIRECTION('',(1.,0.));
#938 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#939 = ADVANCED_FACE('',(#940),#653,.F.);
#940 = FACE_BOUND('',#941,.F.);
#941 = EDGE_LOOP('',(#942,#943,#964,#965));
#942 = ORIENTED_EDGE('',*,*,#874,.F.);
#943 = ORIENTED_EDGE('',*,*,#944,.T.);
#944 = EDGE_CURVE('',#875,#726,#945,.T.);
#945 = SURFACE_CURVE('',#946,(#950,#957),.PCURVE_S1.);
#946 = LINE('',#947,#948);
#947 = CARTESIAN_POINT('',(51.008936,121.80569,42.5));
#948 = VECTOR('',#949,1.);
#949 = DIRECTION('',(0.,0.,-1.));
#950 = PCURVE('',#653,#951);
#951 = DEFINITIONAL_REPRESENTATION('',(#952),#956);
#952 = LINE('',#953,#954);
#953 = CARTESIAN_POINT('',(0.,0.));
#954 = VECTOR('',#955,1.);
#955 = DIRECTION('',(0.,-1.));
#956 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#957 = PCURVE('',#761,#958);
#958 = DEFINITIONAL_REPRESENTATION('',(#959),#963);
#959 = LINE('',#960,#961);
#960 = CARTESIAN_POINT('',(8.290271856093,0.));
#961 = VECTOR('',#962,1.);
#962 = DIRECTION('',(0.,-1.));
#963 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#964 = ORIENTED_EDGE('',*,*,#725,.T.);
#965 = ORIENTED_EDGE('',*,*,#638,.T.);
#966 = ADVANCED_FACE('',(#967),#761,.F.);
#967 = FACE_BOUND('',#968,.F.);
#968 = EDGE_LOOP('',(#969,#970,#971,#972));
#969 = ORIENTED_EDGE('',*,*,#895,.F.);
#970 = ORIENTED_EDGE('',*,*,#825,.T.);
#971 = ORIENTED_EDGE('',*,*,#746,.T.);
#972 = ORIENTED_EDGE('',*,*,#944,.F.);
#973 = ADVANCED_FACE('',(#974),#810,.F.);
#974 = FACE_BOUND('',#975,.F.);
#975 = EDGE_LOOP('',(#976,#977,#998,#999));
#976 = ORIENTED_EDGE('',*,*,#916,.F.);
#977 = ORIENTED_EDGE('',*,*,#978,.F.);
#978 = EDGE_CURVE('',#794,#917,#979,.T.);
#979 = SEAM_CURVE('',#980,(#984,#991),.PCURVE_S1.);
#980 = LINE('',#981,#982);
#981 = CARTESIAN_POINT('',(52.065674,117.607553,14.565494));
#982 = VECTOR('',#983,1.);
#983 = DIRECTION('',(0.,0.,1.));
#984 = PCURVE('',#810,#985);
#985 = DEFINITIONAL_REPRESENTATION('',(#986),#990);
#986 = LINE('',#987,#988);
#987 = CARTESIAN_POINT('',(6.28318530718,-0.));
#988 = VECTOR('',#989,1.);
#989 = DIRECTION('',(0.,1.));
#990 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#991 = PCURVE('',#810,#992);
#992 = DEFINITIONAL_REPRESENTATION('',(#993),#997);
#993 = LINE('',#994,#995);
#994 = CARTESIAN_POINT('',(0.,-0.));
#995 = VECTOR('',#996,1.);
#996 = DIRECTION('',(0.,1.));
#997 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#998 = ORIENTED_EDGE('',*,*,#793,.T.);
#999 = ORIENTED_EDGE('',*,*,#978,.T.);
#1000 = ( GEOMETRIC_REPRESENTATION_CONTEXT(3) 
GLOBAL_UNCERTAINTY_ASSIGNED_CONTEXT((#1004)) 
GLOBAL_UNIT_ASSIGNED_CONTEXT((#1001,#1002,#1003)) REPRESENTATION_CONTEXT
('Context #1','3D Context with UNIT and UNCERTAINTY') );
#1001 = ( LENGTH_UNIT() NAMED_UNIT(*) SI_UNIT(.MILLI.,.METRE.) );
#1002 = ( NAMED_UNIT(*) PLANE_ANGLE_UNIT() SI_UNIT($,.RADIAN.) );
#1003 = ( NAMED_UNIT(*) SI_UNIT($,.STERADIAN.) SOLID_ANGLE_UNIT() );
#1004 = UNCERTAINTY_MEASURE_WITH_UNIT(LENGTH_MEASURE(1.E-07),#1001,
  'distance_accuracy_value','confusion accuracy');
#1005 = CONTEXT_DEPENDENT_SHAPE_REPRESENTATION(#1006,#1008);
#1006 = ( REPRESENTATION_RELATIONSHIP('','',#595,#10) 
REPRESENTATION_RELATIONSHIP_WITH_TRANSFORMATION(#1007) 
SHAPE_REPRESENTATION_RELATIONSHIP() );
#1007 = ITEM_DEFINED_TRANSFORMATION('','',#11,#23);
#1008 = PRODUCT_DEFINITION_SHAPE('Placement','Placement of an item',
  #1009);
#1009 = NEXT_ASSEMBLY_USAGE_OCCURRENCE('107','=>[0:1:1:4]','',#5,#590,$
  );
#1010 = PRODUCT_RELATED_PRODUCT_CATEGORY('part',$,(#592));
#1011 = MECHANICAL_DESIGN_GEOMETRIC_PRESENTATION_REPRESENTATION('',(
    #1012),#577);
#1012 = STYLED_ITEM('color',(#1013),#158);
#1013 = PRESENTATION_STYLE_ASSIGNMENT((#1014));
#1014 = SURFACE_STYLE_USAGE(.BOTH.,#1015);
#1015 = SURFACE_SIDE_STYLE('',(#1016));
#1016 = SURFACE_STYLE_FILL_AREA(#1017);
#1017 = FILL_AREA_STYLE('',(#1018));
#1018 = FILL_AREA_STYLE_COLOUR('',#1019);
#1019 = DRAUGHTING_PRE_DEFINED_COLOUR('green');
#1020 = MECHANICAL_DESIGN_GEOMETRIC_PRESENTATION_REPRESENTATION('',(
    #1021),#139);
#1021 = STYLED_ITEM('color',(#1022),#41);
#1022 = PRESENTATION_STYLE_ASSIGNMENT((#1023));
#1023 = SURFACE_STYLE_USAGE(.BOTH.,#1024);
#1024 = SURFACE_SIDE_STYLE('',(#1025));
#1025 = SURFACE_STYLE_FILL_AREA(#1026);
#1026 = FILL_AREA_STYLE('',(#1027));
#1027 = FILL_AREA_STYLE_COLOUR('',#1028);
#1028 = DRAUGHTING_PRE_DEFINED_COLOUR('blue');
#1029 = MECHANICAL_DESIGN_GEOMETRIC_PRESENTATION_REPRESENTATION('',(
    #1030),#1000);
#1030 = STYLED_ITEM('color',(#1031),#596);
#1031 = PRESENTATION_STYLE_ASSIGNMENT((#1032));
#1032 = SURFACE_STYLE_USAGE(.BOTH.,#1033);
#1033 = SURFACE_SIDE_STYLE('',(#1034));
#1034 = SURFACE_STYLE_FILL_AREA(#1035);
#1035 = FILL_AREA_STYLE('',(#1036));
#1036 = FILL_AREA_STYLE_COLOUR('',#1037);
#1037 = DRAUGHTING_PRE_DEFINED_COLOUR('red');
ENDSEC;
END-ISO-10303-21;
