ISO-10303-21;
HEADER;
FILE_DESCRIPTION(('Open CASCADE Model'),'2;1');
FILE_NAME('Pile_PC3-P4','2025-08-25T20:08:32',('Author'),('Open CASCADE'
    ),'Open CASCADE STEP processor 7.8','build123d','Unknown');
FILE_SCHEMA(('AUTOMOTIVE_DESIGN { 1 0 10303 214 1 1 1 1 }'));
ENDSEC;
DATA;
#1 = APPLICATION_PROTOCOL_DEFINITION('international standard',
  'automotive_design',2000,#2);
#2 = APPLICATION_CONTEXT(
  'core data for automotive mechanical design processes');
#3 = SHAPE_DEFINITION_REPRESENTATION(#4,#10);
#4 = PRODUCT_DEFINITION_SHAPE('','',#5);
#5 = PRODUCT_DEFINITION('design','',#6,#9);
#6 = PRODUCT_DEFINITION_FORMATION('','',#7);
#7 = PRODUCT('Pile_PC3-P4','Pile_PC3-P4','',(#8));
#8 = PRODUCT_CONTEXT('',#2,'mechanical');
#9 = PRODUCT_DEFINITION_CONTEXT('part definition',#2,'design');
#10 = SHAPE_REPRESENTATION('',(#11,#15,#19,#23),#27);
#11 = AXIS2_PLACEMENT_3D('',#12,#13,#14);
#12 = CARTESIAN_POINT('',(0.,0.,0.));
#13 = DIRECTION('',(0.,0.,1.));
#14 = DIRECTION('',(1.,0.,-0.));
#15 = AXIS2_PLACEMENT_3D('',#16,#17,#18);
#16 = CARTESIAN_POINT('',(73.317494,109.853261,14.355218));
#17 = DIRECTION('',(0.,0.,1.));
#18 = DIRECTION('',(1.,0.,0.));
#19 = AXIS2_PLACEMENT_3D('',#20,#21,#22);
#20 = CARTESIAN_POINT('',(0.,0.,0.));
#21 = DIRECTION('',(0.,0.,1.));
#22 = DIRECTION('',(1.,0.,-0.));
#23 = AXIS2_PLACEMENT_3D('',#24,#25,#26);
#24 = CARTESIAN_POINT('',(0.,0.,0.));
#25 = DIRECTION('',(0.,0.,1.));
#26 = DIRECTION('',(1.,0.,-0.));
#27 = ( GEOMETRIC_REPRESENTATION_CONTEXT(3) 
GLOBAL_UNCERTAINTY_ASSIGNED_CONTEXT((#31)) GLOBAL_UNIT_ASSIGNED_CONTEXT(
(#28,#29,#30)) REPRESENTATION_CONTEXT('Context #1',
  '3D Context with UNIT and UNCERTAINTY') );
#28 = ( LENGTH_UNIT() NAMED_UNIT(*) SI_UNIT(.MILLI.,.METRE.) );
#29 = ( NAMED_UNIT(*) PLANE_ANGLE_UNIT() SI_UNIT($,.RADIAN.) );
#30 = ( NAMED_UNIT(*) SI_UNIT($,.STERADIAN.) SOLID_ANGLE_UNIT() );
#31 = UNCERTAINTY_MEASURE_WITH_UNIT(LENGTH_MEASURE(1.E-07),#28,
  'distance_accuracy_value','confusion accuracy');
#32 = PRODUCT_RELATED_PRODUCT_CATEGORY('part',$,(#7));
#33 = SHAPE_DEFINITION_REPRESENTATION(#34,#40);
#34 = PRODUCT_DEFINITION_SHAPE('','',#35);
#35 = PRODUCT_DEFINITION('design','',#36,#39);
#36 = PRODUCT_DEFINITION_FORMATION('','',#37);
#37 = PRODUCT('PC3-P4_Part1','PC3-P4_Part1','',(#38));
#38 = PRODUCT_CONTEXT('',#2,'mechanical');
#39 = PRODUCT_DEFINITION_CONTEXT('part definition',#2,'design');
#40 = ADVANCED_BREP_SHAPE_REPRESENTATION('',(#11,#41),#139);
#41 = MANIFOLD_SOLID_BREP('',#42);
#42 = CLOSED_SHELL('',(#43,#131,#135));
#43 = ADVANCED_FACE('',(#44),#57,.T.);
#44 = FACE_BOUND('',#45,.T.);
#45 = EDGE_LOOP('',(#46,#80,#103,#130));
#46 = ORIENTED_EDGE('',*,*,#47,.F.);
#47 = EDGE_CURVE('',#48,#48,#50,.T.);
#48 = VERTEX_POINT('',#49);
#49 = CARTESIAN_POINT('',(0.275,-6.73555739531E-17,23.144782));
#50 = SURFACE_CURVE('',#51,(#56,#68),.PCURVE_S1.);
#51 = CIRCLE('',#52,0.275);
#52 = AXIS2_PLACEMENT_3D('',#53,#54,#55);
#53 = CARTESIAN_POINT('',(0.,0.,23.144782));
#54 = DIRECTION('',(0.,0.,1.));
#55 = DIRECTION('',(1.,0.,-0.));
#56 = PCURVE('',#57,#62);
#57 = CYLINDRICAL_SURFACE('',#58,0.275);
#58 = AXIS2_PLACEMENT_3D('',#59,#60,#61);
#59 = CARTESIAN_POINT('',(0.,0.,0.));
#60 = DIRECTION('',(0.,0.,1.));
#61 = DIRECTION('',(1.,0.,-0.));
#62 = DEFINITIONAL_REPRESENTATION('',(#63),#67);
#63 = LINE('',#64,#65);
#64 = CARTESIAN_POINT('',(0.,23.144782));
#65 = VECTOR('',#66,1.);
#66 = DIRECTION('',(1.,0.));
#67 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#68 = PCURVE('',#69,#74);
#69 = PLANE('',#70);
#70 = AXIS2_PLACEMENT_3D('',#71,#72,#73);
#71 = CARTESIAN_POINT('',(0.,0.,23.144782));
#72 = DIRECTION('',(0.,0.,1.));
#73 = DIRECTION('',(1.,0.,-0.));
#74 = DEFINITIONAL_REPRESENTATION('',(#75),#79);
#75 = CIRCLE('',#76,0.275);
#76 = AXIS2_PLACEMENT_2D('',#77,#78);
#77 = CARTESIAN_POINT('',(0.,0.));
#78 = DIRECTION('',(1.,0.));
#79 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#80 = ORIENTED_EDGE('',*,*,#81,.F.);
#81 = EDGE_CURVE('',#82,#48,#84,.T.);
#82 = VERTEX_POINT('',#83);
#83 = CARTESIAN_POINT('',(0.275,-6.73555739531E-17,0.));
#84 = SEAM_CURVE('',#85,(#89,#96),.PCURVE_S1.);
#85 = LINE('',#86,#87);
#86 = CARTESIAN_POINT('',(0.275,-6.73555739531E-17,0.));
#87 = VECTOR('',#88,1.);
#88 = DIRECTION('',(0.,0.,1.));
#89 = PCURVE('',#57,#90);
#90 = DEFINITIONAL_REPRESENTATION('',(#91),#95);
#91 = LINE('',#92,#93);
#92 = CARTESIAN_POINT('',(6.28318530718,-0.));
#93 = VECTOR('',#94,1.);
#94 = DIRECTION('',(0.,1.));
#95 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#96 = PCURVE('',#57,#97);
#97 = DEFINITIONAL_REPRESENTATION('',(#98),#102);
#98 = LINE('',#99,#100);
#99 = CARTESIAN_POINT('',(0.,-0.));
#100 = VECTOR('',#101,1.);
#101 = DIRECTION('',(0.,1.));
#102 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#103 = ORIENTED_EDGE('',*,*,#104,.T.);
#104 = EDGE_CURVE('',#82,#82,#105,.T.);
#105 = SURFACE_CURVE('',#106,(#111,#118),.PCURVE_S1.);
#106 = CIRCLE('',#107,0.275);
#107 = AXIS2_PLACEMENT_3D('',#108,#109,#110);
#108 = CARTESIAN_POINT('',(0.,0.,0.));
#109 = DIRECTION('',(0.,0.,1.));
#110 = DIRECTION('',(1.,0.,-0.));
#111 = PCURVE('',#57,#112);
#112 = DEFINITIONAL_REPRESENTATION('',(#113),#117);
#113 = LINE('',#114,#115);
#114 = CARTESIAN_POINT('',(0.,0.));
#115 = VECTOR('',#116,1.);
#116 = DIRECTION('',(1.,0.));
#117 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#118 = PCURVE('',#119,#124);
#119 = PLANE('',#120);
#120 = AXIS2_PLACEMENT_3D('',#121,#122,#123);
#121 = CARTESIAN_POINT('',(0.,0.,0.));
#122 = DIRECTION('',(0.,0.,1.));
#123 = DIRECTION('',(1.,0.,-0.));
#124 = DEFINITIONAL_REPRESENTATION('',(#125),#129);
#125 = CIRCLE('',#126,0.275);
#126 = AXIS2_PLACEMENT_2D('',#127,#128);
#127 = CARTESIAN_POINT('',(0.,0.));
#128 = DIRECTION('',(1.,0.));
#129 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#130 = ORIENTED_EDGE('',*,*,#81,.T.);
#131 = ADVANCED_FACE('',(#132),#69,.T.);
#132 = FACE_BOUND('',#133,.T.);
#133 = EDGE_LOOP('',(#134));
#134 = ORIENTED_EDGE('',*,*,#47,.T.);
#135 = ADVANCED_FACE('',(#136),#119,.F.);
#136 = FACE_BOUND('',#137,.T.);
#137 = EDGE_LOOP('',(#138));
#138 = ORIENTED_EDGE('',*,*,#104,.F.);
#139 = ( GEOMETRIC_REPRESENTATION_CONTEXT(3) 
GLOBAL_UNCERTAINTY_ASSIGNED_CONTEXT((#143)) GLOBAL_UNIT_ASSIGNED_CONTEXT
((#140,#141,#142)) REPRESENTATION_CONTEXT('Context #1',
  '3D Context with UNIT and UNCERTAINTY') );
#140 = ( LENGTH_UNIT() NAMED_UNIT(*) SI_UNIT(.MILLI.,.METRE.) );
#141 = ( NAMED_UNIT(*) PLANE_ANGLE_UNIT() SI_UNIT($,.RADIAN.) );
#142 = ( NAMED_UNIT(*) SI_UNIT($,.STERADIAN.) SOLID_ANGLE_UNIT() );
#143 = UNCERTAINTY_MEASURE_WITH_UNIT(LENGTH_MEASURE(1.E-07),#140,
  'distance_accuracy_value','confusion accuracy');
#144 = CONTEXT_DEPENDENT_SHAPE_REPRESENTATION(#145,#147);
#145 = ( REPRESENTATION_RELATIONSHIP('','',#40,#10) 
REPRESENTATION_RELATIONSHIP_WITH_TRANSFORMATION(#146) 
SHAPE_REPRESENTATION_RELATIONSHIP() );
#146 = ITEM_DEFINED_TRANSFORMATION('','',#11,#15);
#147 = PRODUCT_DEFINITION_SHAPE('Placement','Placement of an item',#148
  );
#148 = NEXT_ASSEMBLY_USAGE_OCCURRENCE('138','=>[0:1:1:2]','',#5,#35,$);
#149 = PRODUCT_RELATED_PRODUCT_CATEGORY('part',$,(#37));
#150 = SHAPE_DEFINITION_REPRESENTATION(#151,#157);
#151 = PRODUCT_DEFINITION_SHAPE('','',#152);
#152 = PRODUCT_DEFINITION('design','',#153,#156);
#153 = PRODUCT_DEFINITION_FORMATION('','',#154);
#154 = PRODUCT('PC3-P4_Part2','PC3-P4_Part2','',(#155));
#155 = PRODUCT_CONTEXT('',#2,'mechanical');
#156 = PRODUCT_DEFINITION_CONTEXT('part definition',#2,'design');
#157 = ADVANCED_BREP_SHAPE_REPRESENTATION('',(#11,#158),#563);
#158 = MANIFOLD_SOLID_BREP('',#159);
#159 = CLOSED_SHELL('',(#160,#385,#435,#504,#530,#557));
#160 = ADVANCED_FACE('',(#161),#175,.T.);
#161 = FACE_BOUND('',#162,.T.);
#162 = EDGE_LOOP('',(#163,#193,#222,#262,#294,#336,#358,#359));
#163 = ORIENTED_EDGE('',*,*,#164,.T.);
#164 = EDGE_CURVE('',#165,#167,#169,.T.);
#165 = VERTEX_POINT('',#166);
#166 = CARTESIAN_POINT('',(73.592494,109.853261,14.355218));
#167 = VERTEX_POINT('',#168);
#168 = CARTESIAN_POINT('',(77.633945884327,109.853261,21.355218));
#169 = SEAM_CURVE('',#170,(#174,#186),.PCURVE_S1.);
#170 = LINE('',#171,#172);
#171 = CARTESIAN_POINT('',(73.592494,109.853261,14.355218));
#172 = VECTOR('',#173,1.);
#173 = DIRECTION('',(0.5,-1.224646799147E-16,0.866025403784));
#174 = PCURVE('',#175,#180);
#175 = CONICAL_SURFACE('',#176,0.275,0.523598775598);
#176 = AXIS2_PLACEMENT_3D('',#177,#178,#179);
#177 = CARTESIAN_POINT('',(73.317494,109.853261,14.355218));
#178 = DIRECTION('',(0.,0.,1.));
#179 = DIRECTION('',(1.,0.,0.));
#180 = DEFINITIONAL_REPRESENTATION('',(#181),#185);
#181 = LINE('',#182,#183);
#182 = CARTESIAN_POINT('',(6.28318530718,-0.));
#183 = VECTOR('',#184,1.);
#184 = DIRECTION('',(0.,1.));
#185 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#186 = PCURVE('',#175,#187);
#187 = DEFINITIONAL_REPRESENTATION('',(#188),#192);
#188 = LINE('',#189,#190);
#189 = CARTESIAN_POINT('',(0.,-0.));
#190 = VECTOR('',#191,1.);
#191 = DIRECTION('',(0.,1.));
#192 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#193 = ORIENTED_EDGE('',*,*,#194,.F.);
#194 = EDGE_CURVE('',#195,#167,#197,.T.);
#195 = VERTEX_POINT('',#196);
#196 = CARTESIAN_POINT('',(70.266293526111,112.906445,21.355218));
#197 = SURFACE_CURVE('',#198,(#203,#210),.PCURVE_S1.);
#198 = CIRCLE('',#199,4.316451884327);
#199 = AXIS2_PLACEMENT_3D('',#200,#201,#202);
#200 = CARTESIAN_POINT('',(73.317494,109.853261,21.355218));
#201 = DIRECTION('',(0.,0.,1.));
#202 = DIRECTION('',(1.,0.,0.));
#203 = PCURVE('',#175,#204);
#204 = DEFINITIONAL_REPRESENTATION('',(#205),#209);
#205 = LINE('',#206,#207);
#206 = CARTESIAN_POINT('',(0.,7.));
#207 = VECTOR('',#208,1.);
#208 = DIRECTION('',(1.,0.));
#209 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#210 = PCURVE('',#211,#216);
#211 = PLANE('',#212);
#212 = AXIS2_PLACEMENT_3D('',#213,#214,#215);
#213 = CARTESIAN_POINT('',(73.317494,109.853261,21.355218));
#214 = DIRECTION('',(0.,0.,1.));
#215 = DIRECTION('',(1.,0.,0.));
#216 = DEFINITIONAL_REPRESENTATION('',(#217),#221);
#217 = CIRCLE('',#218,4.316451884327);
#218 = AXIS2_PLACEMENT_2D('',#219,#220);
#219 = CARTESIAN_POINT('',(0.,0.));
#220 = DIRECTION('',(1.,0.));
#221 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#222 = ORIENTED_EDGE('',*,*,#223,.F.);
#223 = EDGE_CURVE('',#224,#195,#226,.T.);
#224 = VERTEX_POINT('',#225);
#225 = CARTESIAN_POINT('',(72.917494,112.906445,19.212364219349));
#226 = SURFACE_CURVE('',#227,(#232,#250),.PCURVE_S1.);
#227 = HYPERBOLA('',#228,5.288269812856,3.053184);
#228 = AXIS2_PLACEMENT_3D('',#229,#230,#231);
#229 = CARTESIAN_POINT('',(73.317494,112.906445,13.878904027919));
#230 = DIRECTION('',(0.,-1.,0.));
#231 = DIRECTION('',(0.,0.,1.));
#232 = PCURVE('',#175,#233);
#233 = DEFINITIONAL_REPRESENTATION('',(#234),#249);
#234 = B_SPLINE_CURVE_WITH_KNOTS('',7,(#235,#236,#237,#238,#239,#240,
    #241,#242,#243,#244,#245,#246,#247,#248),.UNSPECIFIED.,.F.,.F.,(8,6,
    8),(-0.537331753152,0.171791190755,0.880914134662),.UNSPECIFIED.);
#235 = CARTESIAN_POINT('',(1.057598596934,5.593930923854));
#236 = CARTESIAN_POINT('',(1.145851884008,5.292018679137));
#237 = CARTESIAN_POINT('',(1.239226607037,5.062783924112));
#238 = CARTESIAN_POINT('',(1.33695277501,4.90116615996));
#239 = CARTESIAN_POINT('',(1.437822122059,4.80393167049));
#240 = CARTESIAN_POINT('',(1.540088773796,4.769463578094));
#241 = CARTESIAN_POINT('',(1.641922109829,4.797697054788));
#242 = CARTESIAN_POINT('',(1.841575308708,4.982667378185));
#243 = CARTESIAN_POINT('',(1.939394507024,5.139404223408));
#244 = CARTESIAN_POINT('',(2.033632264825,5.36194309185));
#245 = CARTESIAN_POINT('',(2.122945503615,5.653449211738));
#246 = CARTESIAN_POINT('',(2.206571989379,6.018769934232));
#247 = CARTESIAN_POINT('',(2.284214048909,6.46462896889));
#248 = CARTESIAN_POINT('',(2.355869555541,7.));
#249 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#250 = PCURVE('',#251,#256);
#251 = PLANE('',#252);
#252 = AXIS2_PLACEMENT_3D('',#253,#254,#255);
#253 = CARTESIAN_POINT('',(51.710833,112.906445,47.5));
#254 = DIRECTION('',(0.,-1.,0.));
#255 = DIRECTION('',(1.,0.,0.));
#256 = DEFINITIONAL_REPRESENTATION('',(#257),#261);
#257 = ( BOUNDED_CURVE() B_SPLINE_CURVE(2,(#258,#259,#260),
.UNSPECIFIED.,.F.,.F.) B_SPLINE_CURVE_WITH_KNOTS((3,3),(-0.537331753152,
0.880914134662),.PIECEWISE_BEZIER_KNOTS.) CURVE() 
GEOMETRIC_REPRESENTATION_ITEM() RATIONAL_B_SPLINE_CURVE((1.,
1.262141851221,1.)) REPRESENTATION_ITEM('') );
#258 = CARTESIAN_POINT('',(23.3273271,-27.55085107614));
#259 = CARTESIAN_POINT('',(21.189042462813,-29.36919973444));
#260 = CARTESIAN_POINT('',(18.555460526111,-26.144782));
#261 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#262 = ORIENTED_EDGE('',*,*,#263,.F.);
#263 = EDGE_CURVE('',#264,#224,#266,.T.);
#264 = VERTEX_POINT('',#265);
#265 = CARTESIAN_POINT('',(72.917494,112.30648,18.18411603035));
#266 = SURFACE_CURVE('',#267,(#272,#282),.PCURVE_S1.);
#267 = HYPERBOLA('',#268,0.692820323028,0.4);
#268 = AXIS2_PLACEMENT_3D('',#269,#270,#271);
#269 = CARTESIAN_POINT('',(72.917494,109.853261,13.878904027919));
#270 = DIRECTION('',(-1.,0.,-0.));
#271 = DIRECTION('',(-0.,0.,1.));
#272 = PCURVE('',#175,#273);
#273 = DEFINITIONAL_REPRESENTATION('',(#274),#281);
#274 = B_SPLINE_CURVE_WITH_KNOTS('',5,(#275,#276,#277,#278,#279,#280),
  .UNSPECIFIED.,.F.,.F.,(6,6),(2.48899048356,2.74918462729),
  .PIECEWISE_BEZIER_KNOTS.);
#275 = CARTESIAN_POINT('',(1.736404205083,3.726368719191));
#276 = CARTESIAN_POINT('',(1.727825504098,3.942079185566));
#277 = CARTESIAN_POINT('',(1.719797200585,4.172015896917));
#278 = CARTESIAN_POINT('',(1.712273523831,4.417396127707));
#279 = CARTESIAN_POINT('',(1.705213913082,4.679596391105));
#280 = CARTESIAN_POINT('',(1.698582170425,4.960199476512));
#281 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#282 = PCURVE('',#283,#288);
#283 = PLANE('',#284);
#284 = AXIS2_PLACEMENT_3D('',#285,#286,#287);
#285 = CARTESIAN_POINT('',(72.917494,112.906445,47.5));
#286 = DIRECTION('',(-1.,-0.,-0.));
#287 = DIRECTION('',(0.,-1.,0.));
#288 = DEFINITIONAL_REPRESENTATION('',(#289),#293);
#289 = ( BOUNDED_CURVE() B_SPLINE_CURVE(2,(#290,#291,#292),
.UNSPECIFIED.,.F.,.F.) B_SPLINE_CURVE_WITH_KNOTS((3,3),(2.48899048356,
2.74918462729),.PIECEWISE_BEZIER_KNOTS.) CURVE() 
GEOMETRIC_REPRESENTATION_ITEM() RATIONAL_B_SPLINE_CURVE((1.,
1.008474566791,1.)) REPRESENTATION_ITEM('') );
#290 = CARTESIAN_POINT('',(0.6599615,-29.4184132808));
#291 = CARTESIAN_POINT('',(0.346060321817,-28.88215911822));
#292 = CARTESIAN_POINT('',(-5.999650000001E-02,-28.18458252348));
#293 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#294 = ORIENTED_EDGE('',*,*,#295,.F.);
#295 = EDGE_CURVE('',#296,#264,#298,.T.);
#296 = VERTEX_POINT('',#297);
#297 = CARTESIAN_POINT('',(76.869039214094,112.30648,21.355218));
#298 = SURFACE_CURVE('',#299,(#304,#324),.PCURVE_S1.);
#299 = HYPERBOLA('',#300,4.249099950093,2.453219);
#300 = AXIS2_PLACEMENT_3D('',#301,#302,#303);
#301 = CARTESIAN_POINT('',(73.317494,112.30648,13.878904027919));
#302 = DIRECTION('',(0.,-1.,0.));
#303 = DIRECTION('',(0.,0.,1.));
#304 = PCURVE('',#175,#305);
#305 = DEFINITIONAL_REPRESENTATION('',(#306),#323);
#306 = B_SPLINE_CURVE_WITH_KNOTS('',8,(#307,#308,#309,#310,#311,#312,
    #313,#314,#315,#316,#317,#318,#319,#320,#321,#322),.UNSPECIFIED.,.F.
  ,.F.,(9,7,9),(-1.165402492019,-0.204139441328,0.757123609363),
  .UNSPECIFIED.);
#307 = CARTESIAN_POINT('',(0.604488826333,7.));
#308 = CARTESIAN_POINT('',(0.672779544124,6.260853989044));
#309 = CARTESIAN_POINT('',(0.748785966666,5.64507060412));
#310 = CARTESIAN_POINT('',(0.833043053089,5.13638893667));
#311 = CARTESIAN_POINT('',(0.925847458002,4.722344679139));
#312 = CARTESIAN_POINT('',(1.027109129084,4.393528151474));
#313 = CARTESIAN_POINT('',(1.135920923013,4.143110934039));
#314 = CARTESIAN_POINT('',(1.250363172603,3.966581476468));
#315 = CARTESIAN_POINT('',(1.485757103353,3.756678627338));
#316 = CARTESIAN_POINT('',(1.606708589145,3.723305344584));
#317 = CARTESIAN_POINT('',(1.728533072853,3.759201198583));
#318 = CARTESIAN_POINT('',(1.848385617093,3.864261890412));
#319 = CARTESIAN_POINT('',(1.963356884617,4.040481012183));
#320 = CARTESIAN_POINT('',(2.071598944693,4.292013431418));
#321 = CARTESIAN_POINT('',(2.172099080551,4.625391254823));
#322 = CARTESIAN_POINT('',(2.264487423665,5.049955147848));
#323 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#324 = PCURVE('',#325,#330);
#325 = PLANE('',#326);
#326 = AXIS2_PLACEMENT_3D('',#327,#328,#329);
#327 = CARTESIAN_POINT('',(72.917494,112.30648,47.5));
#328 = DIRECTION('',(0.,-1.,0.));
#329 = DIRECTION('',(1.,0.,0.));
#330 = DEFINITIONAL_REPRESENTATION('',(#331),#335);
#331 = ( BOUNDED_CURVE() B_SPLINE_CURVE(2,(#332,#333,#334),
.UNSPECIFIED.,.F.,.F.) B_SPLINE_CURVE_WITH_KNOTS((3,3),(-1.165402492019,
0.757123609363),.PIECEWISE_BEZIER_KNOTS.) CURVE() 
GEOMETRIC_REPRESENTATION_ITEM() RATIONAL_B_SPLINE_CURVE((1.,
1.498703420344,1.)) REPRESENTATION_ITEM('') );
#332 = CARTESIAN_POINT('',(3.951545214094,-26.144782));
#333 = CARTESIAN_POINT('',(0.736480383219,-30.72663135346));
#334 = CARTESIAN_POINT('',(-1.64,-28.09482685215));
#335 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#336 = ORIENTED_EDGE('',*,*,#337,.F.);
#337 = EDGE_CURVE('',#167,#296,#338,.T.);
#338 = SURFACE_CURVE('',#339,(#344,#351),.PCURVE_S1.);
#339 = CIRCLE('',#340,4.316451884327);
#340 = AXIS2_PLACEMENT_3D('',#341,#342,#343);
#341 = CARTESIAN_POINT('',(73.317494,109.853261,21.355218));
#342 = DIRECTION('',(0.,0.,1.));
#343 = DIRECTION('',(1.,0.,0.));
#344 = PCURVE('',#175,#345);
#345 = DEFINITIONAL_REPRESENTATION('',(#346),#350);
#346 = LINE('',#347,#348);
#347 = CARTESIAN_POINT('',(0.,7.));
#348 = VECTOR('',#349,1.);
#349 = DIRECTION('',(1.,0.));
#350 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#351 = PCURVE('',#211,#352);
#352 = DEFINITIONAL_REPRESENTATION('',(#353),#357);
#353 = CIRCLE('',#354,4.316451884327);
#354 = AXIS2_PLACEMENT_2D('',#355,#356);
#355 = CARTESIAN_POINT('',(0.,0.));
#356 = DIRECTION('',(1.,0.));
#357 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#358 = ORIENTED_EDGE('',*,*,#164,.F.);
#359 = ORIENTED_EDGE('',*,*,#360,.T.);
#360 = EDGE_CURVE('',#165,#165,#361,.T.);
#361 = SURFACE_CURVE('',#362,(#367,#374),.PCURVE_S1.);
#362 = CIRCLE('',#363,0.275);
#363 = AXIS2_PLACEMENT_3D('',#364,#365,#366);
#364 = CARTESIAN_POINT('',(73.317494,109.853261,14.355218));
#365 = DIRECTION('',(0.,0.,1.));
#366 = DIRECTION('',(1.,0.,0.));
#367 = PCURVE('',#175,#368);
#368 = DEFINITIONAL_REPRESENTATION('',(#369),#373);
#369 = LINE('',#370,#371);
#370 = CARTESIAN_POINT('',(0.,0.));
#371 = VECTOR('',#372,1.);
#372 = DIRECTION('',(1.,0.));
#373 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#374 = PCURVE('',#375,#380);
#375 = CYLINDRICAL_SURFACE('',#376,0.275);
#376 = AXIS2_PLACEMENT_3D('',#377,#378,#379);
#377 = CARTESIAN_POINT('',(73.317494,109.853261,14.355218));
#378 = DIRECTION('',(0.,0.,1.));
#379 = DIRECTION('',(1.,0.,0.));
#380 = DEFINITIONAL_REPRESENTATION('',(#381),#384);
#381 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#382,#383),.UNSPECIFIED.,.F.,.F.,
  (2,2),(0.,6.28318530718),.PIECEWISE_BEZIER_KNOTS.);
#382 = CARTESIAN_POINT('',(0.,0.));
#383 = CARTESIAN_POINT('',(6.28318530718,0.));
#384 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#385 = ADVANCED_FACE('',(#386),#375,.F.);
#386 = FACE_BOUND('',#387,.F.);
#387 = EDGE_LOOP('',(#388,#412,#433,#434));
#388 = ORIENTED_EDGE('',*,*,#389,.F.);
#389 = EDGE_CURVE('',#390,#390,#392,.T.);
#390 = VERTEX_POINT('',#391);
#391 = CARTESIAN_POINT('',(73.592494,109.853261,21.355218));
#392 = SURFACE_CURVE('',#393,(#398,#405),.PCURVE_S1.);
#393 = CIRCLE('',#394,0.275);
#394 = AXIS2_PLACEMENT_3D('',#395,#396,#397);
#395 = CARTESIAN_POINT('',(73.317494,109.853261,21.355218));
#396 = DIRECTION('',(0.,0.,1.));
#397 = DIRECTION('',(1.,0.,0.));
#398 = PCURVE('',#375,#399);
#399 = DEFINITIONAL_REPRESENTATION('',(#400),#404);
#400 = LINE('',#401,#402);
#401 = CARTESIAN_POINT('',(0.,7.));
#402 = VECTOR('',#403,1.);
#403 = DIRECTION('',(1.,0.));
#404 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#405 = PCURVE('',#211,#406);
#406 = DEFINITIONAL_REPRESENTATION('',(#407),#411);
#407 = CIRCLE('',#408,0.275);
#408 = AXIS2_PLACEMENT_2D('',#409,#410);
#409 = CARTESIAN_POINT('',(0.,0.));
#410 = DIRECTION('',(1.,0.));
#411 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#412 = ORIENTED_EDGE('',*,*,#413,.F.);
#413 = EDGE_CURVE('',#165,#390,#414,.T.);
#414 = SEAM_CURVE('',#415,(#419,#426),.PCURVE_S1.);
#415 = LINE('',#416,#417);
#416 = CARTESIAN_POINT('',(73.592494,109.853261,14.355218));
#417 = VECTOR('',#418,1.);
#418 = DIRECTION('',(0.,0.,1.));
#419 = PCURVE('',#375,#420);
#420 = DEFINITIONAL_REPRESENTATION('',(#421),#425);
#421 = LINE('',#422,#423);
#422 = CARTESIAN_POINT('',(6.28318530718,-0.));
#423 = VECTOR('',#424,1.);
#424 = DIRECTION('',(0.,1.));
#425 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#426 = PCURVE('',#375,#427);
#427 = DEFINITIONAL_REPRESENTATION('',(#428),#432);
#428 = LINE('',#429,#430);
#429 = CARTESIAN_POINT('',(0.,-0.));
#430 = VECTOR('',#431,1.);
#431 = DIRECTION('',(0.,1.));
#432 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#433 = ORIENTED_EDGE('',*,*,#360,.T.);
#434 = ORIENTED_EDGE('',*,*,#413,.T.);
#435 = ADVANCED_FACE('',(#436,#501),#211,.T.);
#436 = FACE_BOUND('',#437,.T.);
#437 = EDGE_LOOP('',(#438,#439,#440,#461,#482));
#438 = ORIENTED_EDGE('',*,*,#194,.T.);
#439 = ORIENTED_EDGE('',*,*,#337,.T.);
#440 = ORIENTED_EDGE('',*,*,#441,.F.);
#441 = EDGE_CURVE('',#442,#296,#444,.T.);
#442 = VERTEX_POINT('',#443);
#443 = CARTESIAN_POINT('',(72.917494,112.30648,21.355218));
#444 = SURFACE_CURVE('',#445,(#449,#455),.PCURVE_S1.);
#445 = LINE('',#446,#447);
#446 = CARTESIAN_POINT('',(73.117494,112.30648,21.355218));
#447 = VECTOR('',#448,1.);
#448 = DIRECTION('',(1.,0.,-0.));
#449 = PCURVE('',#211,#450);
#450 = DEFINITIONAL_REPRESENTATION('',(#451),#454);
#451 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#452,#453),.UNSPECIFIED.,.F.,.F.,
  (2,2),(-0.2,4.516451884327),.PIECEWISE_BEZIER_KNOTS.);
#452 = CARTESIAN_POINT('',(-0.4,2.453219));
#453 = CARTESIAN_POINT('',(4.316451884327,2.453219));
#454 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#455 = PCURVE('',#325,#456);
#456 = DEFINITIONAL_REPRESENTATION('',(#457),#460);
#457 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#458,#459),.UNSPECIFIED.,.F.,.F.,
  (2,2),(-0.2,4.516451884327),.PIECEWISE_BEZIER_KNOTS.);
#458 = CARTESIAN_POINT('',(0.,-26.144782));
#459 = CARTESIAN_POINT('',(4.716451884327,-26.144782));
#460 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#461 = ORIENTED_EDGE('',*,*,#462,.F.);
#462 = EDGE_CURVE('',#463,#442,#465,.T.);
#463 = VERTEX_POINT('',#464);
#464 = CARTESIAN_POINT('',(72.917494,112.906445,21.355218));
#465 = SURFACE_CURVE('',#466,(#470,#476),.PCURVE_S1.);
#466 = LINE('',#467,#468);
#467 = CARTESIAN_POINT('',(72.917494,111.379853,21.355218));
#468 = VECTOR('',#469,1.);
#469 = DIRECTION('',(0.,-1.,0.));
#470 = PCURVE('',#211,#471);
#471 = DEFINITIONAL_REPRESENTATION('',(#472),#475);
#472 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#473,#474),.UNSPECIFIED.,.F.,.F.,
  (2,2),(-1.526592,-0.926627),.PIECEWISE_BEZIER_KNOTS.);
#473 = CARTESIAN_POINT('',(-0.4,3.053184));
#474 = CARTESIAN_POINT('',(-0.4,2.453219));
#475 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#476 = PCURVE('',#283,#477);
#477 = DEFINITIONAL_REPRESENTATION('',(#478),#481);
#478 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#479,#480),.UNSPECIFIED.,.F.,.F.,
  (2,2),(-1.526592,-0.926627),.PIECEWISE_BEZIER_KNOTS.);
#479 = CARTESIAN_POINT('',(-1.42108547152E-14,-26.144782));
#480 = CARTESIAN_POINT('',(0.599965,-26.144782));
#481 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#482 = ORIENTED_EDGE('',*,*,#483,.F.);
#483 = EDGE_CURVE('',#195,#463,#484,.T.);
#484 = SURFACE_CURVE('',#485,(#489,#495),.PCURVE_S1.);
#485 = LINE('',#486,#487);
#486 = CARTESIAN_POINT('',(62.5141635,112.906445,21.355218));
#487 = VECTOR('',#488,1.);
#488 = DIRECTION('',(1.,0.,-0.));
#489 = PCURVE('',#211,#490);
#490 = DEFINITIONAL_REPRESENTATION('',(#491),#494);
#491 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#492,#493),.UNSPECIFIED.,.F.,.F.,
  (2,2),(6.486878615673,10.4033305),.PIECEWISE_BEZIER_KNOTS.);
#492 = CARTESIAN_POINT('',(-4.316451884327,3.053184));
#493 = CARTESIAN_POINT('',(-0.4,3.053184));
#494 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#495 = PCURVE('',#251,#496);
#496 = DEFINITIONAL_REPRESENTATION('',(#497),#500);
#497 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#498,#499),.UNSPECIFIED.,.F.,.F.,
  (2,2),(6.486878615673,10.4033305),.PIECEWISE_BEZIER_KNOTS.);
#498 = CARTESIAN_POINT('',(17.290209115673,-26.144782));
#499 = CARTESIAN_POINT('',(21.206661,-26.144782));
#500 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#501 = FACE_BOUND('',#502,.T.);
#502 = EDGE_LOOP('',(#503));
#503 = ORIENTED_EDGE('',*,*,#389,.F.);
#504 = ADVANCED_FACE('',(#505),#325,.F.);
#505 = FACE_BOUND('',#506,.F.);
#506 = EDGE_LOOP('',(#507,#508,#529));
#507 = ORIENTED_EDGE('',*,*,#441,.F.);
#508 = ORIENTED_EDGE('',*,*,#509,.T.);
#509 = EDGE_CURVE('',#442,#264,#510,.T.);
#510 = SURFACE_CURVE('',#511,(#515,#522),.PCURVE_S1.);
#511 = LINE('',#512,#513);
#512 = CARTESIAN_POINT('',(72.917494,112.30648,47.5));
#513 = VECTOR('',#514,1.);
#514 = DIRECTION('',(0.,0.,-1.));
#515 = PCURVE('',#325,#516);
#516 = DEFINITIONAL_REPRESENTATION('',(#517),#521);
#517 = LINE('',#518,#519);
#518 = CARTESIAN_POINT('',(0.,0.));
#519 = VECTOR('',#520,1.);
#520 = DIRECTION('',(0.,-1.));
#521 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#522 = PCURVE('',#283,#523);
#523 = DEFINITIONAL_REPRESENTATION('',(#524),#528);
#524 = LINE('',#525,#526);
#525 = CARTESIAN_POINT('',(0.599965,0.));
#526 = VECTOR('',#527,1.);
#527 = DIRECTION('',(0.,-1.));
#528 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#529 = ORIENTED_EDGE('',*,*,#295,.F.);
#530 = ADVANCED_FACE('',(#531),#283,.F.);
#531 = FACE_BOUND('',#532,.F.);
#532 = EDGE_LOOP('',(#533,#534,#555,#556));
#533 = ORIENTED_EDGE('',*,*,#462,.F.);
#534 = ORIENTED_EDGE('',*,*,#535,.T.);
#535 = EDGE_CURVE('',#463,#224,#536,.T.);
#536 = SURFACE_CURVE('',#537,(#541,#548),.PCURVE_S1.);
#537 = LINE('',#538,#539);
#538 = CARTESIAN_POINT('',(72.917494,112.906445,47.5));
#539 = VECTOR('',#540,1.);
#540 = DIRECTION('',(0.,0.,-1.));
#541 = PCURVE('',#283,#542);
#542 = DEFINITIONAL_REPRESENTATION('',(#543),#547);
#543 = LINE('',#544,#545);
#544 = CARTESIAN_POINT('',(0.,0.));
#545 = VECTOR('',#546,1.);
#546 = DIRECTION('',(0.,-1.));
#547 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#548 = PCURVE('',#251,#549);
#549 = DEFINITIONAL_REPRESENTATION('',(#550),#554);
#550 = LINE('',#551,#552);
#551 = CARTESIAN_POINT('',(21.206661,0.));
#552 = VECTOR('',#553,1.);
#553 = DIRECTION('',(0.,-1.));
#554 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#555 = ORIENTED_EDGE('',*,*,#263,.F.);
#556 = ORIENTED_EDGE('',*,*,#509,.F.);
#557 = ADVANCED_FACE('',(#558),#251,.F.);
#558 = FACE_BOUND('',#559,.F.);
#559 = EDGE_LOOP('',(#560,#561,#562));
#560 = ORIENTED_EDGE('',*,*,#535,.F.);
#561 = ORIENTED_EDGE('',*,*,#483,.F.);
#562 = ORIENTED_EDGE('',*,*,#223,.F.);
#563 = ( GEOMETRIC_REPRESENTATION_CONTEXT(3) 
GLOBAL_UNCERTAINTY_ASSIGNED_CONTEXT((#567)) GLOBAL_UNIT_ASSIGNED_CONTEXT
((#564,#565,#566)) REPRESENTATION_CONTEXT('Context #1',
  '3D Context with UNIT and UNCERTAINTY') );
#564 = ( LENGTH_UNIT() NAMED_UNIT(*) SI_UNIT(.MILLI.,.METRE.) );
#565 = ( NAMED_UNIT(*) PLANE_ANGLE_UNIT() SI_UNIT($,.RADIAN.) );
#566 = ( NAMED_UNIT(*) SI_UNIT($,.STERADIAN.) SOLID_ANGLE_UNIT() );
#567 = UNCERTAINTY_MEASURE_WITH_UNIT(LENGTH_MEASURE(1.E-07),#564,
  'distance_accuracy_value','confusion accuracy');
#568 = CONTEXT_DEPENDENT_SHAPE_REPRESENTATION(#569,#571);
#569 = ( REPRESENTATION_RELATIONSHIP('','',#157,#10) 
REPRESENTATION_RELATIONSHIP_WITH_TRANSFORMATION(#570) 
SHAPE_REPRESENTATION_RELATIONSHIP() );
#570 = ITEM_DEFINED_TRANSFORMATION('','',#11,#19);
#571 = PRODUCT_DEFINITION_SHAPE('Placement','Placement of an item',#572
  );
#572 = NEXT_ASSEMBLY_USAGE_OCCURRENCE('139','=>[0:1:1:3]','',#5,#152,$);
#573 = PRODUCT_RELATED_PRODUCT_CATEGORY('part',$,(#154));
#574 = SHAPE_DEFINITION_REPRESENTATION(#575,#581);
#575 = PRODUCT_DEFINITION_SHAPE('','',#576);
#576 = PRODUCT_DEFINITION('design','',#577,#580);
#577 = PRODUCT_DEFINITION_FORMATION('','',#578);
#578 = PRODUCT('PC3-P4_Part3','PC3-P4_Part3','',(#579));
#579 = PRODUCT_CONTEXT('',#2,'mechanical');
#580 = PRODUCT_DEFINITION_CONTEXT('part definition',#2,'design');
#581 = ADVANCED_BREP_SHAPE_REPRESENTATION('',(#11,#582),#986);
#582 = MANIFOLD_SOLID_BREP('',#583);
#583 = CLOSED_SHELL('',(#584,#706,#807,#854,#925,#952,#959));
#584 = ADVANCED_FACE('',(#585),#600,.T.);
#585 = FACE_BOUND('',#586,.T.);
#586 = EDGE_LOOP('',(#587,#623,#649,#682));
#587 = ORIENTED_EDGE('',*,*,#588,.T.);
#588 = EDGE_CURVE('',#589,#591,#593,.T.);
#589 = VERTEX_POINT('',#590);
#590 = CARTESIAN_POINT('',(70.266293526111,112.906445,21.355218));
#591 = VERTEX_POINT('',#592);
#592 = CARTESIAN_POINT('',(76.869039214094,112.30648,21.355218));
#593 = SURFACE_CURVE('',#594,(#599,#611),.PCURVE_S1.);
#594 = CIRCLE('',#595,4.316451884327);
#595 = AXIS2_PLACEMENT_3D('',#596,#597,#598);
#596 = CARTESIAN_POINT('',(73.317494,109.853261,21.355218));
#597 = DIRECTION('',(0.,0.,1.));
#598 = DIRECTION('',(-0.706876980366,0.707336507349,0.));
#599 = PCURVE('',#600,#605);
#600 = CYLINDRICAL_SURFACE('',#601,4.316451884327);
#601 = AXIS2_PLACEMENT_3D('',#602,#603,#604);
#602 = CARTESIAN_POINT('',(73.317494,109.853261,21.355218));
#603 = DIRECTION('',(0.,0.,1.));
#604 = DIRECTION('',(1.,0.,0.));
#605 = DEFINITIONAL_REPRESENTATION('',(#606),#610);
#606 = LINE('',#607,#608);
#607 = CARTESIAN_POINT('',(2.355869555541,0.));
#608 = VECTOR('',#609,1.);
#609 = DIRECTION('',(1.,0.));
#610 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#611 = PCURVE('',#612,#617);
#612 = PLANE('',#613);
#613 = AXIS2_PLACEMENT_3D('',#614,#615,#616);
#614 = CARTESIAN_POINT('',(73.317494,109.853261,21.355218));
#615 = DIRECTION('',(0.,0.,1.));
#616 = DIRECTION('',(1.,0.,0.));
#617 = DEFINITIONAL_REPRESENTATION('',(#618),#622);
#618 = CIRCLE('',#619,4.316451884327);
#619 = AXIS2_PLACEMENT_2D('',#620,#621);
#620 = CARTESIAN_POINT('',(0.,0.));
#621 = DIRECTION('',(-0.706876980366,0.707336507349));
#622 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#623 = ORIENTED_EDGE('',*,*,#624,.T.);
#624 = EDGE_CURVE('',#591,#625,#627,.T.);
#625 = VERTEX_POINT('',#626);
#626 = CARTESIAN_POINT('',(76.869039214094,112.30648,37.5));
#627 = SURFACE_CURVE('',#628,(#632,#638),.PCURVE_S1.);
#628 = LINE('',#629,#630);
#629 = CARTESIAN_POINT('',(76.869039214094,112.30648,21.355218));
#630 = VECTOR('',#631,1.);
#631 = DIRECTION('',(0.,0.,1.));
#632 = PCURVE('',#600,#633);
#633 = DEFINITIONAL_REPRESENTATION('',(#634),#637);
#634 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#635,#636),.UNSPECIFIED.,.F.,.F.,
  (2,2),(0.,16.144782),.PIECEWISE_BEZIER_KNOTS.);
#635 = CARTESIAN_POINT('',(6.887674133512,0.));
#636 = CARTESIAN_POINT('',(6.887674133512,16.144782));
#637 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#638 = PCURVE('',#639,#644);
#639 = PLANE('',#640);
#640 = AXIS2_PLACEMENT_3D('',#641,#642,#643);
#641 = CARTESIAN_POINT('',(72.917494,112.30648,47.5));
#642 = DIRECTION('',(0.,-1.,0.));
#643 = DIRECTION('',(1.,0.,0.));
#644 = DEFINITIONAL_REPRESENTATION('',(#645),#648);
#645 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#646,#647),.UNSPECIFIED.,.F.,.F.,
  (2,2),(-5.999999999062E-07,16.1447826),.PIECEWISE_BEZIER_KNOTS.);
#646 = CARTESIAN_POINT('',(3.951545214094,-26.1447826));
#647 = CARTESIAN_POINT('',(3.951545214094,-9.9999994));
#648 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#649 = ORIENTED_EDGE('',*,*,#650,.T.);
#650 = EDGE_CURVE('',#625,#651,#653,.T.);
#651 = VERTEX_POINT('',#652);
#652 = CARTESIAN_POINT('',(70.266293526111,112.906445,37.5));
#653 = SURFACE_CURVE('',#654,(#659,#666),.PCURVE_S1.);
#654 = CIRCLE('',#655,4.316451884327);
#655 = AXIS2_PLACEMENT_3D('',#656,#657,#658);
#656 = CARTESIAN_POINT('',(73.317494,109.853261,37.5));
#657 = DIRECTION('',(0.,0.,-1.));
#658 = DIRECTION('',(0.822792726357,0.568341560555,0.));
#659 = PCURVE('',#600,#660);
#660 = DEFINITIONAL_REPRESENTATION('',(#661),#665);
#661 = LINE('',#662,#663);
#662 = CARTESIAN_POINT('',(6.887674133512,16.144782));
#663 = VECTOR('',#664,1.);
#664 = DIRECTION('',(-1.,-0.));
#665 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#666 = PCURVE('',#667,#672);
#667 = PLANE('',#668);
#668 = AXIS2_PLACEMENT_3D('',#669,#670,#671);
#669 = CARTESIAN_POINT('',(73.317494,109.853261,37.5));
#670 = DIRECTION('',(0.,0.,1.));
#671 = DIRECTION('',(1.,0.,0.));
#672 = DEFINITIONAL_REPRESENTATION('',(#673),#681);
#673 = ( BOUNDED_CURVE() B_SPLINE_CURVE(2,(#674,#675,#676,#677,#678,#679
,#680),.UNSPECIFIED.,.T.,.F.) B_SPLINE_CURVE_WITH_KNOTS((1,2,2,2,2,1),(
    -2.094395102393,0.,2.094395102393,4.188790204786,6.28318530718,
8.377580409573),.UNSPECIFIED.) CURVE() GEOMETRIC_REPRESENTATION_ITEM() 
RATIONAL_B_SPLINE_CURVE((1.,0.5,1.,0.5,1.,0.5,1.)) REPRESENTATION_ITEM(
  '') );
#674 = CARTESIAN_POINT('',(3.551545214094,2.453219));
#675 = CARTESIAN_POINT('',(7.800645164187,-3.698237756189));
#676 = CARTESIAN_POINT('',(0.348777368,-4.302337878094));
#677 = CARTESIAN_POINT('',(-7.103090428188,-4.906438));
#678 = CARTESIAN_POINT('',(-3.900322582094,1.849118878094));
#679 = CARTESIAN_POINT('',(-0.697554736,8.604675756189));
#680 = CARTESIAN_POINT('',(3.551545214094,2.453219));
#681 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#682 = ORIENTED_EDGE('',*,*,#683,.F.);
#683 = EDGE_CURVE('',#589,#651,#684,.T.);
#684 = SURFACE_CURVE('',#685,(#689,#695),.PCURVE_S1.);
#685 = LINE('',#686,#687);
#686 = CARTESIAN_POINT('',(70.266293526111,112.906445,21.355218));
#687 = VECTOR('',#688,1.);
#688 = DIRECTION('',(0.,0.,1.));
#689 = PCURVE('',#600,#690);
#690 = DEFINITIONAL_REPRESENTATION('',(#691),#694);
#691 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#692,#693),.UNSPECIFIED.,.F.,.F.,
  (2,2),(0.,16.144782),.PIECEWISE_BEZIER_KNOTS.);
#692 = CARTESIAN_POINT('',(2.355869555541,0.));
#693 = CARTESIAN_POINT('',(2.355869555541,16.144782));
#694 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#695 = PCURVE('',#696,#701);
#696 = PLANE('',#697);
#697 = AXIS2_PLACEMENT_3D('',#698,#699,#700);
#698 = CARTESIAN_POINT('',(51.710833,112.906445,47.5));
#699 = DIRECTION('',(0.,-1.,0.));
#700 = DIRECTION('',(1.,0.,0.));
#701 = DEFINITIONAL_REPRESENTATION('',(#702),#705);
#702 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#703,#704),.UNSPECIFIED.,.F.,.F.,
  (2,2),(-5.999999999062E-07,16.1447826),.PIECEWISE_BEZIER_KNOTS.);
#703 = CARTESIAN_POINT('',(18.555460526111,-26.1447826));
#704 = CARTESIAN_POINT('',(18.555460526111,-9.9999994));
#705 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#706 = ADVANCED_FACE('',(#707,#776),#612,.F.);
#707 = FACE_BOUND('',#708,.F.);
#708 = EDGE_LOOP('',(#709,#710,#731,#757));
#709 = ORIENTED_EDGE('',*,*,#588,.T.);
#710 = ORIENTED_EDGE('',*,*,#711,.F.);
#711 = EDGE_CURVE('',#712,#591,#714,.T.);
#712 = VERTEX_POINT('',#713);
#713 = CARTESIAN_POINT('',(72.917494,112.30648,21.355218));
#714 = SURFACE_CURVE('',#715,(#719,#725),.PCURVE_S1.);
#715 = LINE('',#716,#717);
#716 = CARTESIAN_POINT('',(73.117494,112.30648,21.355218));
#717 = VECTOR('',#718,1.);
#718 = DIRECTION('',(1.,0.,-0.));
#719 = PCURVE('',#612,#720);
#720 = DEFINITIONAL_REPRESENTATION('',(#721),#724);
#721 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#722,#723),.UNSPECIFIED.,.F.,.F.,
  (2,2),(-0.2,4.516451884327),.PIECEWISE_BEZIER_KNOTS.);
#722 = CARTESIAN_POINT('',(-0.4,2.453219));
#723 = CARTESIAN_POINT('',(4.316451884327,2.453219));
#724 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#725 = PCURVE('',#639,#726);
#726 = DEFINITIONAL_REPRESENTATION('',(#727),#730);
#727 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#728,#729),.UNSPECIFIED.,.F.,.F.,
  (2,2),(-0.2,4.516451884327),.PIECEWISE_BEZIER_KNOTS.);
#728 = CARTESIAN_POINT('',(0.,-26.144782));
#729 = CARTESIAN_POINT('',(4.716451884327,-26.144782));
#730 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#731 = ORIENTED_EDGE('',*,*,#732,.F.);
#732 = EDGE_CURVE('',#733,#712,#735,.T.);
#733 = VERTEX_POINT('',#734);
#734 = CARTESIAN_POINT('',(72.917494,112.906445,21.355218));
#735 = SURFACE_CURVE('',#736,(#740,#746),.PCURVE_S1.);
#736 = LINE('',#737,#738);
#737 = CARTESIAN_POINT('',(72.917494,111.379853,21.355218));
#738 = VECTOR('',#739,1.);
#739 = DIRECTION('',(0.,-1.,0.));
#740 = PCURVE('',#612,#741);
#741 = DEFINITIONAL_REPRESENTATION('',(#742),#745);
#742 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#743,#744),.UNSPECIFIED.,.F.,.F.,
  (2,2),(-1.526592,-0.926627),.PIECEWISE_BEZIER_KNOTS.);
#743 = CARTESIAN_POINT('',(-0.4,3.053184));
#744 = CARTESIAN_POINT('',(-0.4,2.453219));
#745 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#746 = PCURVE('',#747,#752);
#747 = PLANE('',#748);
#748 = AXIS2_PLACEMENT_3D('',#749,#750,#751);
#749 = CARTESIAN_POINT('',(72.917494,112.906445,47.5));
#750 = DIRECTION('',(-1.,-0.,-0.));
#751 = DIRECTION('',(0.,-1.,0.));
#752 = DEFINITIONAL_REPRESENTATION('',(#753),#756);
#753 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#754,#755),.UNSPECIFIED.,.F.,.F.,
  (2,2),(-1.526592,-0.926627),.PIECEWISE_BEZIER_KNOTS.);
#754 = CARTESIAN_POINT('',(-1.42108547152E-14,-26.144782));
#755 = CARTESIAN_POINT('',(0.599965,-26.144782));
#756 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#757 = ORIENTED_EDGE('',*,*,#758,.F.);
#758 = EDGE_CURVE('',#589,#733,#759,.T.);
#759 = SURFACE_CURVE('',#760,(#764,#770),.PCURVE_S1.);
#760 = LINE('',#761,#762);
#761 = CARTESIAN_POINT('',(62.5141635,112.906445,21.355218));
#762 = VECTOR('',#763,1.);
#763 = DIRECTION('',(1.,0.,-0.));
#764 = PCURVE('',#612,#765);
#765 = DEFINITIONAL_REPRESENTATION('',(#766),#769);
#766 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#767,#768),.UNSPECIFIED.,.F.,.F.,
  (2,2),(6.486878615673,10.4033305),.PIECEWISE_BEZIER_KNOTS.);
#767 = CARTESIAN_POINT('',(-4.316451884327,3.053184));
#768 = CARTESIAN_POINT('',(-0.4,3.053184));
#769 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#770 = PCURVE('',#696,#771);
#771 = DEFINITIONAL_REPRESENTATION('',(#772),#775);
#772 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#773,#774),.UNSPECIFIED.,.F.,.F.,
  (2,2),(6.486878615673,10.4033305),.PIECEWISE_BEZIER_KNOTS.);
#773 = CARTESIAN_POINT('',(17.290209115673,-26.144782));
#774 = CARTESIAN_POINT('',(21.206661,-26.144782));
#775 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#776 = FACE_BOUND('',#777,.F.);
#777 = EDGE_LOOP('',(#778));
#778 = ORIENTED_EDGE('',*,*,#779,.F.);
#779 = EDGE_CURVE('',#780,#780,#782,.T.);
#780 = VERTEX_POINT('',#781);
#781 = CARTESIAN_POINT('',(73.592494,109.853261,21.355218));
#782 = SURFACE_CURVE('',#783,(#788,#795),.PCURVE_S1.);
#783 = CIRCLE('',#784,0.275);
#784 = AXIS2_PLACEMENT_3D('',#785,#786,#787);
#785 = CARTESIAN_POINT('',(73.317494,109.853261,21.355218));
#786 = DIRECTION('',(0.,0.,1.));
#787 = DIRECTION('',(1.,0.,0.));
#788 = PCURVE('',#612,#789);
#789 = DEFINITIONAL_REPRESENTATION('',(#790),#794);
#790 = CIRCLE('',#791,0.275);
#791 = AXIS2_PLACEMENT_2D('',#792,#793);
#792 = CARTESIAN_POINT('',(0.,0.));
#793 = DIRECTION('',(1.,0.));
#794 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#795 = PCURVE('',#796,#801);
#796 = CYLINDRICAL_SURFACE('',#797,0.275);
#797 = AXIS2_PLACEMENT_3D('',#798,#799,#800);
#798 = CARTESIAN_POINT('',(73.317494,109.853261,21.355218));
#799 = DIRECTION('',(0.,0.,1.));
#800 = DIRECTION('',(1.,0.,0.));
#801 = DEFINITIONAL_REPRESENTATION('',(#802),#806);
#802 = LINE('',#803,#804);
#803 = CARTESIAN_POINT('',(0.,0.));
#804 = VECTOR('',#805,1.);
#805 = DIRECTION('',(1.,0.));
#806 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#807 = ADVANCED_FACE('',(#808),#696,.F.);
#808 = FACE_BOUND('',#809,.F.);
#809 = EDGE_LOOP('',(#810,#833,#852,#853));
#810 = ORIENTED_EDGE('',*,*,#811,.F.);
#811 = EDGE_CURVE('',#812,#733,#814,.T.);
#812 = VERTEX_POINT('',#813);
#813 = CARTESIAN_POINT('',(72.917494,112.906445,37.5));
#814 = SURFACE_CURVE('',#815,(#819,#826),.PCURVE_S1.);
#815 = LINE('',#816,#817);
#816 = CARTESIAN_POINT('',(72.917494,112.906445,47.5));
#817 = VECTOR('',#818,1.);
#818 = DIRECTION('',(0.,0.,-1.));
#819 = PCURVE('',#696,#820);
#820 = DEFINITIONAL_REPRESENTATION('',(#821),#825);
#821 = LINE('',#822,#823);
#822 = CARTESIAN_POINT('',(21.206661,0.));
#823 = VECTOR('',#824,1.);
#824 = DIRECTION('',(0.,-1.));
#825 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#826 = PCURVE('',#747,#827);
#827 = DEFINITIONAL_REPRESENTATION('',(#828),#832);
#828 = LINE('',#829,#830);
#829 = CARTESIAN_POINT('',(0.,0.));
#830 = VECTOR('',#831,1.);
#831 = DIRECTION('',(0.,-1.));
#832 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#833 = ORIENTED_EDGE('',*,*,#834,.F.);
#834 = EDGE_CURVE('',#651,#812,#835,.T.);
#835 = SURFACE_CURVE('',#836,(#840,#846),.PCURVE_S1.);
#836 = LINE('',#837,#838);
#837 = CARTESIAN_POINT('',(62.5141635,112.906445,37.5));
#838 = VECTOR('',#839,1.);
#839 = DIRECTION('',(1.,0.,-0.));
#840 = PCURVE('',#696,#841);
#841 = DEFINITIONAL_REPRESENTATION('',(#842),#845);
#842 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#843,#844),.UNSPECIFIED.,.F.,.F.,
  (2,2),(6.486878615673,10.4033305),.PIECEWISE_BEZIER_KNOTS.);
#843 = CARTESIAN_POINT('',(17.290209115673,-10.));
#844 = CARTESIAN_POINT('',(21.206661,-10.));
#845 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#846 = PCURVE('',#667,#847);
#847 = DEFINITIONAL_REPRESENTATION('',(#848),#851);
#848 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#849,#850),.UNSPECIFIED.,.F.,.F.,
  (2,2),(6.486878615673,10.4033305),.PIECEWISE_BEZIER_KNOTS.);
#849 = CARTESIAN_POINT('',(-4.316451884327,3.053184));
#850 = CARTESIAN_POINT('',(-0.4,3.053184));
#851 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#852 = ORIENTED_EDGE('',*,*,#683,.F.);
#853 = ORIENTED_EDGE('',*,*,#758,.T.);
#854 = ADVANCED_FACE('',(#855,#899),#667,.T.);
#855 = FACE_BOUND('',#856,.T.);
#856 = EDGE_LOOP('',(#857,#858,#859,#880));
#857 = ORIENTED_EDGE('',*,*,#834,.F.);
#858 = ORIENTED_EDGE('',*,*,#650,.F.);
#859 = ORIENTED_EDGE('',*,*,#860,.F.);
#860 = EDGE_CURVE('',#861,#625,#863,.T.);
#861 = VERTEX_POINT('',#862);
#862 = CARTESIAN_POINT('',(72.917494,112.30648,37.5));
#863 = SURFACE_CURVE('',#864,(#868,#874),.PCURVE_S1.);
#864 = LINE('',#865,#866);
#865 = CARTESIAN_POINT('',(73.117494,112.30648,37.5));
#866 = VECTOR('',#867,1.);
#867 = DIRECTION('',(1.,0.,-0.));
#868 = PCURVE('',#667,#869);
#869 = DEFINITIONAL_REPRESENTATION('',(#870),#873);
#870 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#871,#872),.UNSPECIFIED.,.F.,.F.,
  (2,2),(-0.2,4.516451884327),.PIECEWISE_BEZIER_KNOTS.);
#871 = CARTESIAN_POINT('',(-0.4,2.453219));
#872 = CARTESIAN_POINT('',(4.316451884327,2.453219));
#873 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#874 = PCURVE('',#639,#875);
#875 = DEFINITIONAL_REPRESENTATION('',(#876),#879);
#876 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#877,#878),.UNSPECIFIED.,.F.,.F.,
  (2,2),(-0.2,4.516451884327),.PIECEWISE_BEZIER_KNOTS.);
#877 = CARTESIAN_POINT('',(0.,-10.));
#878 = CARTESIAN_POINT('',(4.716451884327,-10.));
#879 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#880 = ORIENTED_EDGE('',*,*,#881,.F.);
#881 = EDGE_CURVE('',#812,#861,#882,.T.);
#882 = SURFACE_CURVE('',#883,(#887,#893),.PCURVE_S1.);
#883 = LINE('',#884,#885);
#884 = CARTESIAN_POINT('',(72.917494,111.379853,37.5));
#885 = VECTOR('',#886,1.);
#886 = DIRECTION('',(0.,-1.,0.));
#887 = PCURVE('',#667,#888);
#888 = DEFINITIONAL_REPRESENTATION('',(#889),#892);
#889 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#890,#891),.UNSPECIFIED.,.F.,.F.,
  (2,2),(-1.526592,-0.926627),.PIECEWISE_BEZIER_KNOTS.);
#890 = CARTESIAN_POINT('',(-0.4,3.053184));
#891 = CARTESIAN_POINT('',(-0.4,2.453219));
#892 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#893 = PCURVE('',#747,#894);
#894 = DEFINITIONAL_REPRESENTATION('',(#895),#898);
#895 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#896,#897),.UNSPECIFIED.,.F.,.F.,
  (2,2),(-1.526592,-0.926627),.PIECEWISE_BEZIER_KNOTS.);
#896 = CARTESIAN_POINT('',(-1.42108547152E-14,-10.));
#897 = CARTESIAN_POINT('',(0.599965,-10.));
#898 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#899 = FACE_BOUND('',#900,.T.);
#900 = EDGE_LOOP('',(#901));
#901 = ORIENTED_EDGE('',*,*,#902,.F.);
#902 = EDGE_CURVE('',#903,#903,#905,.T.);
#903 = VERTEX_POINT('',#904);
#904 = CARTESIAN_POINT('',(73.592494,109.853261,37.5));
#905 = SURFACE_CURVE('',#906,(#911,#918),.PCURVE_S1.);
#906 = CIRCLE('',#907,0.275);
#907 = AXIS2_PLACEMENT_3D('',#908,#909,#910);
#908 = CARTESIAN_POINT('',(73.317494,109.853261,37.5));
#909 = DIRECTION('',(0.,0.,1.));
#910 = DIRECTION('',(1.,0.,0.));
#911 = PCURVE('',#667,#912);
#912 = DEFINITIONAL_REPRESENTATION('',(#913),#917);
#913 = CIRCLE('',#914,0.275);
#914 = AXIS2_PLACEMENT_2D('',#915,#916);
#915 = CARTESIAN_POINT('',(0.,0.));
#916 = DIRECTION('',(1.,0.));
#917 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#918 = PCURVE('',#796,#919);
#919 = DEFINITIONAL_REPRESENTATION('',(#920),#924);
#920 = LINE('',#921,#922);
#921 = CARTESIAN_POINT('',(0.,16.144782));
#922 = VECTOR('',#923,1.);
#923 = DIRECTION('',(1.,0.));
#924 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#925 = ADVANCED_FACE('',(#926),#639,.F.);
#926 = FACE_BOUND('',#927,.F.);
#927 = EDGE_LOOP('',(#928,#929,#950,#951));
#928 = ORIENTED_EDGE('',*,*,#860,.F.);
#929 = ORIENTED_EDGE('',*,*,#930,.T.);
#930 = EDGE_CURVE('',#861,#712,#931,.T.);
#931 = SURFACE_CURVE('',#932,(#936,#943),.PCURVE_S1.);
#932 = LINE('',#933,#934);
#933 = CARTESIAN_POINT('',(72.917494,112.30648,47.5));
#934 = VECTOR('',#935,1.);
#935 = DIRECTION('',(0.,0.,-1.));
#936 = PCURVE('',#639,#937);
#937 = DEFINITIONAL_REPRESENTATION('',(#938),#942);
#938 = LINE('',#939,#940);
#939 = CARTESIAN_POINT('',(0.,0.));
#940 = VECTOR('',#941,1.);
#941 = DIRECTION('',(0.,-1.));
#942 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#943 = PCURVE('',#747,#944);
#944 = DEFINITIONAL_REPRESENTATION('',(#945),#949);
#945 = LINE('',#946,#947);
#946 = CARTESIAN_POINT('',(0.599965,0.));
#947 = VECTOR('',#948,1.);
#948 = DIRECTION('',(0.,-1.));
#949 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#950 = ORIENTED_EDGE('',*,*,#711,.T.);
#951 = ORIENTED_EDGE('',*,*,#624,.T.);
#952 = ADVANCED_FACE('',(#953),#747,.F.);
#953 = FACE_BOUND('',#954,.F.);
#954 = EDGE_LOOP('',(#955,#956,#957,#958));
#955 = ORIENTED_EDGE('',*,*,#881,.F.);
#956 = ORIENTED_EDGE('',*,*,#811,.T.);
#957 = ORIENTED_EDGE('',*,*,#732,.T.);
#958 = ORIENTED_EDGE('',*,*,#930,.F.);
#959 = ADVANCED_FACE('',(#960),#796,.F.);
#960 = FACE_BOUND('',#961,.F.);
#961 = EDGE_LOOP('',(#962,#963,#984,#985));
#962 = ORIENTED_EDGE('',*,*,#902,.F.);
#963 = ORIENTED_EDGE('',*,*,#964,.F.);
#964 = EDGE_CURVE('',#780,#903,#965,.T.);
#965 = SEAM_CURVE('',#966,(#970,#977),.PCURVE_S1.);
#966 = LINE('',#967,#968);
#967 = CARTESIAN_POINT('',(73.592494,109.853261,21.355218));
#968 = VECTOR('',#969,1.);
#969 = DIRECTION('',(0.,0.,1.));
#970 = PCURVE('',#796,#971);
#971 = DEFINITIONAL_REPRESENTATION('',(#972),#976);
#972 = LINE('',#973,#974);
#973 = CARTESIAN_POINT('',(6.28318530718,-0.));
#974 = VECTOR('',#975,1.);
#975 = DIRECTION('',(0.,1.));
#976 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#977 = PCURVE('',#796,#978);
#978 = DEFINITIONAL_REPRESENTATION('',(#979),#983);
#979 = LINE('',#980,#981);
#980 = CARTESIAN_POINT('',(0.,-0.));
#981 = VECTOR('',#982,1.);
#982 = DIRECTION('',(0.,1.));
#983 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#984 = ORIENTED_EDGE('',*,*,#779,.T.);
#985 = ORIENTED_EDGE('',*,*,#964,.T.);
#986 = ( GEOMETRIC_REPRESENTATION_CONTEXT(3) 
GLOBAL_UNCERTAINTY_ASSIGNED_CONTEXT((#990)) GLOBAL_UNIT_ASSIGNED_CONTEXT
((#987,#988,#989)) REPRESENTATION_CONTEXT('Context #1',
  '3D Context with UNIT and UNCERTAINTY') );
#987 = ( LENGTH_UNIT() NAMED_UNIT(*) SI_UNIT(.MILLI.,.METRE.) );
#988 = ( NAMED_UNIT(*) PLANE_ANGLE_UNIT() SI_UNIT($,.RADIAN.) );
#989 = ( NAMED_UNIT(*) SI_UNIT($,.STERADIAN.) SOLID_ANGLE_UNIT() );
#990 = UNCERTAINTY_MEASURE_WITH_UNIT(LENGTH_MEASURE(1.E-07),#987,
  'distance_accuracy_value','confusion accuracy');
#991 = CONTEXT_DEPENDENT_SHAPE_REPRESENTATION(#992,#994);
#992 = ( REPRESENTATION_RELATIONSHIP('','',#581,#10) 
REPRESENTATION_RELATIONSHIP_WITH_TRANSFORMATION(#993) 
SHAPE_REPRESENTATION_RELATIONSHIP() );
#993 = ITEM_DEFINED_TRANSFORMATION('','',#11,#23);
#994 = PRODUCT_DEFINITION_SHAPE('Placement','Placement of an item',#995
  );
#995 = NEXT_ASSEMBLY_USAGE_OCCURRENCE('140','=>[0:1:1:4]','',#5,#576,$);
#996 = PRODUCT_RELATED_PRODUCT_CATEGORY('part',$,(#578));
#997 = MECHANICAL_DESIGN_GEOMETRIC_PRESENTATION_REPRESENTATION('',(#998)
  ,#986);
#998 = STYLED_ITEM('color',(#999),#582);
#999 = PRESENTATION_STYLE_ASSIGNMENT((#1000));
#1000 = SURFACE_STYLE_USAGE(.BOTH.,#1001);
#1001 = SURFACE_SIDE_STYLE('',(#1002));
#1002 = SURFACE_STYLE_FILL_AREA(#1003);
#1003 = FILL_AREA_STYLE('',(#1004));
#1004 = FILL_AREA_STYLE_COLOUR('',#1005);
#1005 = DRAUGHTING_PRE_DEFINED_COLOUR('red');
#1006 = MECHANICAL_DESIGN_GEOMETRIC_PRESENTATION_REPRESENTATION('',(
    #1007),#563);
#1007 = STYLED_ITEM('color',(#1008),#158);
#1008 = PRESENTATION_STYLE_ASSIGNMENT((#1009));
#1009 = SURFACE_STYLE_USAGE(.BOTH.,#1010);
#1010 = SURFACE_SIDE_STYLE('',(#1011));
#1011 = SURFACE_STYLE_FILL_AREA(#1012);
#1012 = FILL_AREA_STYLE('',(#1013));
#1013 = FILL_AREA_STYLE_COLOUR('',#1014);
#1014 = DRAUGHTING_PRE_DEFINED_COLOUR('green');
#1015 = MECHANICAL_DESIGN_GEOMETRIC_PRESENTATION_REPRESENTATION('',(
    #1016),#139);
#1016 = STYLED_ITEM('color',(#1017),#41);
#1017 = PRESENTATION_STYLE_ASSIGNMENT((#1018));
#1018 = SURFACE_STYLE_USAGE(.BOTH.,#1019);
#1019 = SURFACE_SIDE_STYLE('',(#1020));
#1020 = SURFACE_STYLE_FILL_AREA(#1021);
#1021 = FILL_AREA_STYLE('',(#1022));
#1022 = FILL_AREA_STYLE_COLOUR('',#1023);
#1023 = DRAUGHTING_PRE_DEFINED_COLOUR('blue');
ENDSEC;
END-ISO-10303-21;
