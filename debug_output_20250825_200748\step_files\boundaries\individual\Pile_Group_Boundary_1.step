ISO-10303-21;
HEADER;
FILE_DESCRIPTION(('Open CASCADE Model'),'2;1');
FILE_NAME('Pile_Group_Boundary_1','2025-08-25T20:07:51',('Author'),(
    'Open CASCADE'),'Open CASCADE STEP processor 7.8','build123d',
  'Unknown');
FILE_SCHEMA(('AUTOMOTIVE_DESIGN { 1 0 10303 214 1 1 1 1 }'));
ENDSEC;
DATA;
#1 = APPLICATION_PROTOCOL_DEFINITION('international standard',
  'automotive_design',2000,#2);
#2 = APPLICATION_CONTEXT(
  'core data for automotive mechanical design processes');
#3 = SHAPE_DEFINITION_REPRESENTATION(#4,#10);
#4 = PRODUCT_DEFINITION_SHAPE('','',#5);
#5 = PRODUCT_DEFINITION('design','',#6,#9);
#6 = PRODUCT_DEFINITION_FORMATION('','',#7);
#7 = PRODUCT('Pile_Group_Boundary_1','Pile_Group_Boundary_1','',(#8));
#8 = PRODUCT_CONTEXT('',#2,'mechanical');
#9 = PRODUCT_DEFINITION_CONTEXT('part definition',#2,'design');
#10 = ADVANCED_BREP_SHAPE_REPRESENTATION('',(#11,#15),#1125);
#11 = AXIS2_PLACEMENT_3D('',#12,#13,#14);
#12 = CARTESIAN_POINT('',(0.,0.,0.));
#13 = DIRECTION('',(0.,0.,1.));
#14 = DIRECTION('',(1.,0.,-0.));
#15 = MANIFOLD_SOLID_BREP('',#16);
#16 = CLOSED_SHELL('',(#17,#137,#213,#289,#365,#441,#517,#593,#669,#745,
    #821,#897,#973,#1044,#1091,#1108));
#17 = ADVANCED_FACE('',(#18),#32,.F.);
#18 = FACE_BOUND('',#19,.F.);
#19 = EDGE_LOOP('',(#20,#55,#83,#111));
#20 = ORIENTED_EDGE('',*,*,#21,.T.);
#21 = EDGE_CURVE('',#22,#24,#26,.T.);
#22 = VERTEX_POINT('',#23);
#23 = CARTESIAN_POINT('',(46.945771,143.583803,28.));
#24 = VERTEX_POINT('',#25);
#25 = CARTESIAN_POINT('',(46.945771,143.583803,-13.767115));
#26 = SURFACE_CURVE('',#27,(#31,#43),.PCURVE_S1.);
#27 = LINE('',#28,#29);
#28 = CARTESIAN_POINT('',(46.945771,143.583803,28.));
#29 = VECTOR('',#30,1.);
#30 = DIRECTION('',(0.,0.,-1.));
#31 = PCURVE('',#32,#37);
#32 = PLANE('',#33);
#33 = AXIS2_PLACEMENT_3D('',#34,#35,#36);
#34 = CARTESIAN_POINT('',(46.945771,143.583803,28.));
#35 = DIRECTION('',(0.132487231294,-0.991184712122,0.));
#36 = DIRECTION('',(0.991184712122,0.132487231294,0.));
#37 = DEFINITIONAL_REPRESENTATION('',(#38),#42);
#38 = LINE('',#39,#40);
#39 = CARTESIAN_POINT('',(0.,0.));
#40 = VECTOR('',#41,1.);
#41 = DIRECTION('',(0.,-1.));
#42 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#43 = PCURVE('',#44,#49);
#44 = PLANE('',#45);
#45 = AXIS2_PLACEMENT_3D('',#46,#47,#48);
#46 = CARTESIAN_POINT('',(48.301331,131.800116,28.));
#47 = DIRECTION('',(0.993448200572,0.11428330053,0.));
#48 = DIRECTION('',(-0.11428330053,0.993448200572,0.));
#49 = DEFINITIONAL_REPRESENTATION('',(#50),#54);
#50 = LINE('',#51,#52);
#51 = CARTESIAN_POINT('',(11.861400517121,0.));
#52 = VECTOR('',#53,1.);
#53 = DIRECTION('',(0.,-1.));
#54 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#55 = ORIENTED_EDGE('',*,*,#56,.T.);
#56 = EDGE_CURVE('',#24,#57,#59,.T.);
#57 = VERTEX_POINT('',#58);
#58 = CARTESIAN_POINT('',(71.189538,146.824359,-13.767115));
#59 = SURFACE_CURVE('',#60,(#64,#71),.PCURVE_S1.);
#60 = LINE('',#61,#62);
#61 = CARTESIAN_POINT('',(46.945771,143.583803,-13.767115));
#62 = VECTOR('',#63,1.);
#63 = DIRECTION('',(0.991184712122,0.132487231294,0.));
#64 = PCURVE('',#32,#65);
#65 = DEFINITIONAL_REPRESENTATION('',(#66),#70);
#66 = LINE('',#67,#68);
#67 = CARTESIAN_POINT('',(-7.042790969946E-15,-41.767115));
#68 = VECTOR('',#69,1.);
#69 = DIRECTION('',(1.,0.));
#70 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#71 = PCURVE('',#72,#77);
#72 = PLANE('',#73);
#73 = AXIS2_PLACEMENT_3D('',#74,#75,#76);
#74 = CARTESIAN_POINT('',(80.007160191567,135.247798948,-13.767115));
#75 = DIRECTION('',(0.,0.,-1.));
#76 = DIRECTION('',(-1.,0.,0.));
#77 = DEFINITIONAL_REPRESENTATION('',(#78),#82);
#78 = LINE('',#79,#80);
#79 = CARTESIAN_POINT('',(33.061389191567,8.336004051997));
#80 = VECTOR('',#81,1.);
#81 = DIRECTION('',(-0.991184712122,0.132487231294));
#82 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#83 = ORIENTED_EDGE('',*,*,#84,.F.);
#84 = EDGE_CURVE('',#85,#57,#87,.T.);
#85 = VERTEX_POINT('',#86);
#86 = CARTESIAN_POINT('',(71.189538,146.824359,28.));
#87 = SURFACE_CURVE('',#88,(#92,#99),.PCURVE_S1.);
#88 = LINE('',#89,#90);
#89 = CARTESIAN_POINT('',(71.189538,146.824359,28.));
#90 = VECTOR('',#91,1.);
#91 = DIRECTION('',(0.,0.,-1.));
#92 = PCURVE('',#32,#93);
#93 = DEFINITIONAL_REPRESENTATION('',(#94),#98);
#94 = LINE('',#95,#96);
#95 = CARTESIAN_POINT('',(24.459383506937,0.));
#96 = VECTOR('',#97,1.);
#97 = DIRECTION('',(0.,-1.));
#98 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#99 = PCURVE('',#100,#105);
#100 = PLANE('',#101);
#101 = AXIS2_PLACEMENT_3D('',#102,#103,#104);
#102 = CARTESIAN_POINT('',(71.189538,146.824359,28.));
#103 = DIRECTION('',(0.993746145311,0.111662879591,0.));
#104 = DIRECTION('',(-0.111662879591,0.993746145311,0.));
#105 = DEFINITIONAL_REPRESENTATION('',(#106),#110);
#106 = LINE('',#107,#108);
#107 = CARTESIAN_POINT('',(0.,0.));
#108 = VECTOR('',#109,1.);
#109 = DIRECTION('',(0.,-1.));
#110 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#111 = ORIENTED_EDGE('',*,*,#112,.F.);
#112 = EDGE_CURVE('',#22,#85,#113,.T.);
#113 = SURFACE_CURVE('',#114,(#118,#125),.PCURVE_S1.);
#114 = LINE('',#115,#116);
#115 = CARTESIAN_POINT('',(46.945771,143.583803,28.));
#116 = VECTOR('',#117,1.);
#117 = DIRECTION('',(0.991184712122,0.132487231294,0.));
#118 = PCURVE('',#32,#119);
#119 = DEFINITIONAL_REPRESENTATION('',(#120),#124);
#120 = LINE('',#121,#122);
#121 = CARTESIAN_POINT('',(-7.042790969946E-15,0.));
#122 = VECTOR('',#123,1.);
#123 = DIRECTION('',(1.,0.));
#124 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#125 = PCURVE('',#126,#131);
#126 = PLANE('',#127);
#127 = AXIS2_PLACEMENT_3D('',#128,#129,#130);
#128 = CARTESIAN_POINT('',(80.007160191567,135.247798948,28.));
#129 = DIRECTION('',(0.,0.,-1.));
#130 = DIRECTION('',(-1.,0.,0.));
#131 = DEFINITIONAL_REPRESENTATION('',(#132),#136);
#132 = LINE('',#133,#134);
#133 = CARTESIAN_POINT('',(33.061389191567,8.336004051997));
#134 = VECTOR('',#135,1.);
#135 = DIRECTION('',(-0.991184712122,0.132487231294));
#136 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#137 = ADVANCED_FACE('',(#138),#100,.F.);
#138 = FACE_BOUND('',#139,.F.);
#139 = EDGE_LOOP('',(#140,#141,#164,#192));
#140 = ORIENTED_EDGE('',*,*,#84,.T.);
#141 = ORIENTED_EDGE('',*,*,#142,.T.);
#142 = EDGE_CURVE('',#57,#143,#145,.T.);
#143 = VERTEX_POINT('',#144);
#144 = CARTESIAN_POINT('',(71.017293,148.357257,-13.767115));
#145 = SURFACE_CURVE('',#146,(#150,#157),.PCURVE_S1.);
#146 = LINE('',#147,#148);
#147 = CARTESIAN_POINT('',(71.189538,146.824359,-13.767115));
#148 = VECTOR('',#149,1.);
#149 = DIRECTION('',(-0.111662879591,0.993746145311,0.));
#150 = PCURVE('',#100,#151);
#151 = DEFINITIONAL_REPRESENTATION('',(#152),#156);
#152 = LINE('',#153,#154);
#153 = CARTESIAN_POINT('',(-2.824396418963E-14,-41.767115));
#154 = VECTOR('',#155,1.);
#155 = DIRECTION('',(1.,0.));
#156 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#157 = PCURVE('',#72,#158);
#158 = DEFINITIONAL_REPRESENTATION('',(#159),#163);
#159 = LINE('',#160,#161);
#160 = CARTESIAN_POINT('',(8.817622191567,11.576560051997));
#161 = VECTOR('',#162,1.);
#162 = DIRECTION('',(0.111662879591,0.993746145311));
#163 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#164 = ORIENTED_EDGE('',*,*,#165,.F.);
#165 = EDGE_CURVE('',#166,#143,#168,.T.);
#166 = VERTEX_POINT('',#167);
#167 = CARTESIAN_POINT('',(71.017293,148.357257,28.));
#168 = SURFACE_CURVE('',#169,(#173,#180),.PCURVE_S1.);
#169 = LINE('',#170,#171);
#170 = CARTESIAN_POINT('',(71.017293,148.357257,28.));
#171 = VECTOR('',#172,1.);
#172 = DIRECTION('',(0.,0.,-1.));
#173 = PCURVE('',#100,#174);
#174 = DEFINITIONAL_REPRESENTATION('',(#175),#179);
#175 = LINE('',#176,#177);
#176 = CARTESIAN_POINT('',(1.542544851351,0.));
#177 = VECTOR('',#178,1.);
#178 = DIRECTION('',(0.,-1.));
#179 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#180 = PCURVE('',#181,#186);
#181 = PLANE('',#182);
#182 = AXIS2_PLACEMENT_3D('',#183,#184,#185);
#183 = CARTESIAN_POINT('',(71.017293,148.357257,28.));
#184 = DIRECTION('',(-7.3194503953E-03,-0.999973212464,-0.));
#185 = DIRECTION('',(0.999973212464,-7.3194503953E-03,0.));
#186 = DEFINITIONAL_REPRESENTATION('',(#187),#191);
#187 = LINE('',#188,#189);
#188 = CARTESIAN_POINT('',(0.,0.));
#189 = VECTOR('',#190,1.);
#190 = DIRECTION('',(0.,-1.));
#191 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#192 = ORIENTED_EDGE('',*,*,#193,.F.);
#193 = EDGE_CURVE('',#85,#166,#194,.T.);
#194 = SURFACE_CURVE('',#195,(#199,#206),.PCURVE_S1.);
#195 = LINE('',#196,#197);
#196 = CARTESIAN_POINT('',(71.189538,146.824359,28.));
#197 = VECTOR('',#198,1.);
#198 = DIRECTION('',(-0.111662879591,0.993746145311,0.));
#199 = PCURVE('',#100,#200);
#200 = DEFINITIONAL_REPRESENTATION('',(#201),#205);
#201 = LINE('',#202,#203);
#202 = CARTESIAN_POINT('',(-2.824396418963E-14,0.));
#203 = VECTOR('',#204,1.);
#204 = DIRECTION('',(1.,0.));
#205 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#206 = PCURVE('',#126,#207);
#207 = DEFINITIONAL_REPRESENTATION('',(#208),#212);
#208 = LINE('',#209,#210);
#209 = CARTESIAN_POINT('',(8.817622191567,11.576560051997));
#210 = VECTOR('',#211,1.);
#211 = DIRECTION('',(0.111662879591,0.993746145311));
#212 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#213 = ADVANCED_FACE('',(#214),#181,.F.);
#214 = FACE_BOUND('',#215,.F.);
#215 = EDGE_LOOP('',(#216,#217,#240,#268));
#216 = ORIENTED_EDGE('',*,*,#165,.T.);
#217 = ORIENTED_EDGE('',*,*,#218,.T.);
#218 = EDGE_CURVE('',#143,#219,#221,.T.);
#219 = VERTEX_POINT('',#220);
#220 = CARTESIAN_POINT('',(91.496834,148.207354,-13.767115));
#221 = SURFACE_CURVE('',#222,(#226,#233),.PCURVE_S1.);
#222 = LINE('',#223,#224);
#223 = CARTESIAN_POINT('',(71.017293,148.357257,-13.767115));
#224 = VECTOR('',#225,1.);
#225 = DIRECTION('',(0.999973212464,-7.3194503953E-03,0.));
#226 = PCURVE('',#181,#227);
#227 = DEFINITIONAL_REPRESENTATION('',(#228),#232);
#228 = LINE('',#229,#230);
#229 = CARTESIAN_POINT('',(-1.421047404142E-14,-41.767115));
#230 = VECTOR('',#231,1.);
#231 = DIRECTION('',(1.,0.));
#232 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#233 = PCURVE('',#72,#234);
#234 = DEFINITIONAL_REPRESENTATION('',(#235),#239);
#235 = LINE('',#236,#237);
#236 = CARTESIAN_POINT('',(8.989867191567,13.109458051997));
#237 = VECTOR('',#238,1.);
#238 = DIRECTION('',(-0.999973212464,-7.3194503953E-03));
#239 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#240 = ORIENTED_EDGE('',*,*,#241,.F.);
#241 = EDGE_CURVE('',#242,#219,#244,.T.);
#242 = VERTEX_POINT('',#243);
#243 = CARTESIAN_POINT('',(91.496834,148.207354,28.));
#244 = SURFACE_CURVE('',#245,(#249,#256),.PCURVE_S1.);
#245 = LINE('',#246,#247);
#246 = CARTESIAN_POINT('',(91.496834,148.207354,28.));
#247 = VECTOR('',#248,1.);
#248 = DIRECTION('',(0.,0.,-1.));
#249 = PCURVE('',#181,#250);
#250 = DEFINITIONAL_REPRESENTATION('',(#251),#255);
#251 = LINE('',#252,#253);
#252 = CARTESIAN_POINT('',(20.480089611134,0.));
#253 = VECTOR('',#254,1.);
#254 = DIRECTION('',(0.,-1.));
#255 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#256 = PCURVE('',#257,#262);
#257 = PLANE('',#258);
#258 = AXIS2_PLACEMENT_3D('',#259,#260,#261);
#259 = CARTESIAN_POINT('',(91.496834,148.207354,28.));
#260 = DIRECTION('',(-0.175155373413,-0.984540804215,-0.));
#261 = DIRECTION('',(0.984540804215,-0.175155373413,0.));
#262 = DEFINITIONAL_REPRESENTATION('',(#263),#267);
#263 = LINE('',#264,#265);
#264 = CARTESIAN_POINT('',(0.,0.));
#265 = VECTOR('',#266,1.);
#266 = DIRECTION('',(0.,-1.));
#267 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#268 = ORIENTED_EDGE('',*,*,#269,.F.);
#269 = EDGE_CURVE('',#166,#242,#270,.T.);
#270 = SURFACE_CURVE('',#271,(#275,#282),.PCURVE_S1.);
#271 = LINE('',#272,#273);
#272 = CARTESIAN_POINT('',(71.017293,148.357257,28.));
#273 = VECTOR('',#274,1.);
#274 = DIRECTION('',(0.999973212464,-7.3194503953E-03,0.));
#275 = PCURVE('',#181,#276);
#276 = DEFINITIONAL_REPRESENTATION('',(#277),#281);
#277 = LINE('',#278,#279);
#278 = CARTESIAN_POINT('',(-1.421047404142E-14,0.));
#279 = VECTOR('',#280,1.);
#280 = DIRECTION('',(1.,0.));
#281 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#282 = PCURVE('',#126,#283);
#283 = DEFINITIONAL_REPRESENTATION('',(#284),#288);
#284 = LINE('',#285,#286);
#285 = CARTESIAN_POINT('',(8.989867191567,13.109458051997));
#286 = VECTOR('',#287,1.);
#287 = DIRECTION('',(-0.999973212464,-7.3194503953E-03));
#288 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#289 = ADVANCED_FACE('',(#290),#257,.F.);
#290 = FACE_BOUND('',#291,.F.);
#291 = EDGE_LOOP('',(#292,#293,#316,#344));
#292 = ORIENTED_EDGE('',*,*,#241,.T.);
#293 = ORIENTED_EDGE('',*,*,#294,.T.);
#294 = EDGE_CURVE('',#219,#295,#297,.T.);
#295 = VERTEX_POINT('',#296);
#296 = CARTESIAN_POINT('',(96.153156,147.378968,-13.767115));
#297 = SURFACE_CURVE('',#298,(#302,#309),.PCURVE_S1.);
#298 = LINE('',#299,#300);
#299 = CARTESIAN_POINT('',(91.496834,148.207354,-13.767115));
#300 = VECTOR('',#301,1.);
#301 = DIRECTION('',(0.984540804215,-0.175155373413,0.));
#302 = PCURVE('',#257,#303);
#303 = DEFINITIONAL_REPRESENTATION('',(#304),#308);
#304 = LINE('',#305,#306);
#305 = CARTESIAN_POINT('',(1.399116632989E-14,-41.767115));
#306 = VECTOR('',#307,1.);
#307 = DIRECTION('',(1.,0.));
#308 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#309 = PCURVE('',#72,#310);
#310 = DEFINITIONAL_REPRESENTATION('',(#311),#315);
#311 = LINE('',#312,#313);
#312 = CARTESIAN_POINT('',(-11.48967380843,12.959555051997));
#313 = VECTOR('',#314,1.);
#314 = DIRECTION('',(-0.984540804215,-0.175155373413));
#315 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#316 = ORIENTED_EDGE('',*,*,#317,.F.);
#317 = EDGE_CURVE('',#318,#295,#320,.T.);
#318 = VERTEX_POINT('',#319);
#319 = CARTESIAN_POINT('',(96.153156,147.378968,28.));
#320 = SURFACE_CURVE('',#321,(#325,#332),.PCURVE_S1.);
#321 = LINE('',#322,#323);
#322 = CARTESIAN_POINT('',(96.153156,147.378968,28.));
#323 = VECTOR('',#324,1.);
#324 = DIRECTION('',(0.,0.,-1.));
#325 = PCURVE('',#257,#326);
#326 = DEFINITIONAL_REPRESENTATION('',(#327),#331);
#327 = LINE('',#328,#329);
#328 = CARTESIAN_POINT('',(4.729435265725,0.));
#329 = VECTOR('',#330,1.);
#330 = DIRECTION('',(0.,-1.));
#331 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#332 = PCURVE('',#333,#338);
#333 = PLANE('',#334);
#334 = AXIS2_PLACEMENT_3D('',#335,#336,#337);
#335 = CARTESIAN_POINT('',(96.153156,147.378968,28.));
#336 = DIRECTION('',(-3.993835685532E-03,-0.999992024606,-0.));
#337 = DIRECTION('',(0.999992024606,-3.993835685532E-03,0.));
#338 = DEFINITIONAL_REPRESENTATION('',(#339),#343);
#339 = LINE('',#340,#341);
#340 = CARTESIAN_POINT('',(0.,0.));
#341 = VECTOR('',#342,1.);
#342 = DIRECTION('',(0.,-1.));
#343 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#344 = ORIENTED_EDGE('',*,*,#345,.F.);
#345 = EDGE_CURVE('',#242,#318,#346,.T.);
#346 = SURFACE_CURVE('',#347,(#351,#358),.PCURVE_S1.);
#347 = LINE('',#348,#349);
#348 = CARTESIAN_POINT('',(91.496834,148.207354,28.));
#349 = VECTOR('',#350,1.);
#350 = DIRECTION('',(0.984540804215,-0.175155373413,0.));
#351 = PCURVE('',#257,#352);
#352 = DEFINITIONAL_REPRESENTATION('',(#353),#357);
#353 = LINE('',#354,#355);
#354 = CARTESIAN_POINT('',(1.399116632989E-14,0.));
#355 = VECTOR('',#356,1.);
#356 = DIRECTION('',(1.,0.));
#357 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#358 = PCURVE('',#126,#359);
#359 = DEFINITIONAL_REPRESENTATION('',(#360),#364);
#360 = LINE('',#361,#362);
#361 = CARTESIAN_POINT('',(-11.48967380843,12.959555051997));
#362 = VECTOR('',#363,1.);
#363 = DIRECTION('',(-0.984540804215,-0.175155373413));
#364 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#365 = ADVANCED_FACE('',(#366),#333,.F.);
#366 = FACE_BOUND('',#367,.F.);
#367 = EDGE_LOOP('',(#368,#369,#392,#420));
#368 = ORIENTED_EDGE('',*,*,#317,.T.);
#369 = ORIENTED_EDGE('',*,*,#370,.T.);
#370 = EDGE_CURVE('',#295,#371,#373,.T.);
#371 = VERTEX_POINT('',#372);
#372 = CARTESIAN_POINT('',(112.117381,147.315209,-13.767115));
#373 = SURFACE_CURVE('',#374,(#378,#385),.PCURVE_S1.);
#374 = LINE('',#375,#376);
#375 = CARTESIAN_POINT('',(96.153156,147.378968,-13.767115));
#376 = VECTOR('',#377,1.);
#377 = DIRECTION('',(0.999992024606,-3.993835685532E-03,0.));
#378 = PCURVE('',#333,#379);
#379 = DEFINITIONAL_REPRESENTATION('',(#380),#384);
#380 = LINE('',#381,#382);
#381 = CARTESIAN_POINT('',(1.421074137804E-14,-41.767115));
#382 = VECTOR('',#383,1.);
#383 = DIRECTION('',(1.,0.));
#384 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#385 = PCURVE('',#72,#386);
#386 = DEFINITIONAL_REPRESENTATION('',(#387),#391);
#387 = LINE('',#388,#389);
#388 = CARTESIAN_POINT('',(-16.14599580843,12.131169051997));
#389 = VECTOR('',#390,1.);
#390 = DIRECTION('',(-0.999992024606,-3.993835685532E-03));
#391 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#392 = ORIENTED_EDGE('',*,*,#393,.F.);
#393 = EDGE_CURVE('',#394,#371,#396,.T.);
#394 = VERTEX_POINT('',#395);
#395 = CARTESIAN_POINT('',(112.117381,147.315209,28.));
#396 = SURFACE_CURVE('',#397,(#401,#408),.PCURVE_S1.);
#397 = LINE('',#398,#399);
#398 = CARTESIAN_POINT('',(112.117381,147.315209,28.));
#399 = VECTOR('',#400,1.);
#400 = DIRECTION('',(0.,0.,-1.));
#401 = PCURVE('',#333,#402);
#402 = DEFINITIONAL_REPRESENTATION('',(#403),#407);
#403 = LINE('',#404,#405);
#404 = CARTESIAN_POINT('',(15.964352321992,0.));
#405 = VECTOR('',#406,1.);
#406 = DIRECTION('',(0.,-1.));
#407 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#408 = PCURVE('',#409,#414);
#409 = PLANE('',#410);
#410 = AXIS2_PLACEMENT_3D('',#411,#412,#413);
#411 = CARTESIAN_POINT('',(112.117381,147.315209,28.));
#412 = DIRECTION('',(-0.999997968285,-2.015794036709E-03,-0.));
#413 = DIRECTION('',(2.015794036709E-03,-0.999997968285,0.));
#414 = DEFINITIONAL_REPRESENTATION('',(#415),#419);
#415 = LINE('',#416,#417);
#416 = CARTESIAN_POINT('',(0.,0.));
#417 = VECTOR('',#418,1.);
#418 = DIRECTION('',(0.,-1.));
#419 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#420 = ORIENTED_EDGE('',*,*,#421,.F.);
#421 = EDGE_CURVE('',#318,#394,#422,.T.);
#422 = SURFACE_CURVE('',#423,(#427,#434),.PCURVE_S1.);
#423 = LINE('',#424,#425);
#424 = CARTESIAN_POINT('',(96.153156,147.378968,28.));
#425 = VECTOR('',#426,1.);
#426 = DIRECTION('',(0.999992024606,-3.993835685532E-03,0.));
#427 = PCURVE('',#333,#428);
#428 = DEFINITIONAL_REPRESENTATION('',(#429),#433);
#429 = LINE('',#430,#431);
#430 = CARTESIAN_POINT('',(1.421074137804E-14,0.));
#431 = VECTOR('',#432,1.);
#432 = DIRECTION('',(1.,0.));
#433 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#434 = PCURVE('',#126,#435);
#435 = DEFINITIONAL_REPRESENTATION('',(#436),#440);
#436 = LINE('',#437,#438);
#437 = CARTESIAN_POINT('',(-16.14599580843,12.131169051997));
#438 = VECTOR('',#439,1.);
#439 = DIRECTION('',(-0.999992024606,-3.993835685532E-03));
#440 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#441 = ADVANCED_FACE('',(#442),#409,.F.);
#442 = FACE_BOUND('',#443,.F.);
#443 = EDGE_LOOP('',(#444,#445,#468,#496));
#444 = ORIENTED_EDGE('',*,*,#393,.T.);
#445 = ORIENTED_EDGE('',*,*,#446,.T.);
#446 = EDGE_CURVE('',#371,#447,#449,.T.);
#447 = VERTEX_POINT('',#448);
#448 = CARTESIAN_POINT('',(112.160196,126.075483,-13.767115));
#449 = SURFACE_CURVE('',#450,(#454,#461),.PCURVE_S1.);
#450 = LINE('',#451,#452);
#451 = CARTESIAN_POINT('',(112.117381,147.315209,-13.767115));
#452 = VECTOR('',#453,1.);
#453 = DIRECTION('',(2.015794036709E-03,-0.999997968285,0.));
#454 = PCURVE('',#409,#455);
#455 = DEFINITIONAL_REPRESENTATION('',(#456),#460);
#456 = LINE('',#457,#458);
#457 = CARTESIAN_POINT('',(2.842165168559E-14,-41.767115));
#458 = VECTOR('',#459,1.);
#459 = DIRECTION('',(1.,0.));
#460 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#461 = PCURVE('',#72,#462);
#462 = DEFINITIONAL_REPRESENTATION('',(#463),#467);
#463 = LINE('',#464,#465);
#464 = CARTESIAN_POINT('',(-32.11022080843,12.067410051997));
#465 = VECTOR('',#466,1.);
#466 = DIRECTION('',(-2.015794036709E-03,-0.999997968285));
#467 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#468 = ORIENTED_EDGE('',*,*,#469,.F.);
#469 = EDGE_CURVE('',#470,#447,#472,.T.);
#470 = VERTEX_POINT('',#471);
#471 = CARTESIAN_POINT('',(112.160196,126.075483,28.));
#472 = SURFACE_CURVE('',#473,(#477,#484),.PCURVE_S1.);
#473 = LINE('',#474,#475);
#474 = CARTESIAN_POINT('',(112.160196,126.075483,28.));
#475 = VECTOR('',#476,1.);
#476 = DIRECTION('',(0.,0.,-1.));
#477 = PCURVE('',#409,#478);
#478 = DEFINITIONAL_REPRESENTATION('',(#479),#483);
#479 = LINE('',#480,#481);
#480 = CARTESIAN_POINT('',(21.239769153155,0.));
#481 = VECTOR('',#482,1.);
#482 = DIRECTION('',(0.,-1.));
#483 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#484 = PCURVE('',#485,#490);
#485 = PLANE('',#486);
#486 = AXIS2_PLACEMENT_3D('',#487,#488,#489);
#487 = CARTESIAN_POINT('',(112.160196,126.075483,28.));
#488 = DIRECTION('',(0.,1.,0.));
#489 = DIRECTION('',(-1.,0.,0.));
#490 = DEFINITIONAL_REPRESENTATION('',(#491),#495);
#491 = LINE('',#492,#493);
#492 = CARTESIAN_POINT('',(0.,0.));
#493 = VECTOR('',#494,1.);
#494 = DIRECTION('',(0.,-1.));
#495 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#496 = ORIENTED_EDGE('',*,*,#497,.F.);
#497 = EDGE_CURVE('',#394,#470,#498,.T.);
#498 = SURFACE_CURVE('',#499,(#503,#510),.PCURVE_S1.);
#499 = LINE('',#500,#501);
#500 = CARTESIAN_POINT('',(112.117381,147.315209,28.));
#501 = VECTOR('',#502,1.);
#502 = DIRECTION('',(2.015794036709E-03,-0.999997968285,0.));
#503 = PCURVE('',#409,#504);
#504 = DEFINITIONAL_REPRESENTATION('',(#505),#509);
#505 = LINE('',#506,#507);
#506 = CARTESIAN_POINT('',(2.842165168559E-14,0.));
#507 = VECTOR('',#508,1.);
#508 = DIRECTION('',(1.,0.));
#509 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#510 = PCURVE('',#126,#511);
#511 = DEFINITIONAL_REPRESENTATION('',(#512),#516);
#512 = LINE('',#513,#514);
#513 = CARTESIAN_POINT('',(-32.11022080843,12.067410051997));
#514 = VECTOR('',#515,1.);
#515 = DIRECTION('',(-2.015794036709E-03,-0.999997968285));
#516 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#517 = ADVANCED_FACE('',(#518),#485,.F.);
#518 = FACE_BOUND('',#519,.F.);
#519 = EDGE_LOOP('',(#520,#521,#544,#572));
#520 = ORIENTED_EDGE('',*,*,#469,.T.);
#521 = ORIENTED_EDGE('',*,*,#522,.T.);
#522 = EDGE_CURVE('',#447,#523,#525,.T.);
#523 = VERTEX_POINT('',#524);
#524 = CARTESIAN_POINT('',(85.917422,126.075483,-13.767115));
#525 = SURFACE_CURVE('',#526,(#530,#537),.PCURVE_S1.);
#526 = LINE('',#527,#528);
#527 = CARTESIAN_POINT('',(112.160196,126.075483,-13.767115));
#528 = VECTOR('',#529,1.);
#529 = DIRECTION('',(-1.,0.,0.));
#530 = PCURVE('',#485,#531);
#531 = DEFINITIONAL_REPRESENTATION('',(#532),#536);
#532 = LINE('',#533,#534);
#533 = CARTESIAN_POINT('',(0.,-41.767115));
#534 = VECTOR('',#535,1.);
#535 = DIRECTION('',(1.,0.));
#536 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#537 = PCURVE('',#72,#538);
#538 = DEFINITIONAL_REPRESENTATION('',(#539),#543);
#539 = LINE('',#540,#541);
#540 = CARTESIAN_POINT('',(-32.15303580843,-9.172315948003));
#541 = VECTOR('',#542,1.);
#542 = DIRECTION('',(1.,0.));
#543 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#544 = ORIENTED_EDGE('',*,*,#545,.F.);
#545 = EDGE_CURVE('',#546,#523,#548,.T.);
#546 = VERTEX_POINT('',#547);
#547 = CARTESIAN_POINT('',(85.917422,126.075483,28.));
#548 = SURFACE_CURVE('',#549,(#553,#560),.PCURVE_S1.);
#549 = LINE('',#550,#551);
#550 = CARTESIAN_POINT('',(85.917422,126.075483,28.));
#551 = VECTOR('',#552,1.);
#552 = DIRECTION('',(0.,0.,-1.));
#553 = PCURVE('',#485,#554);
#554 = DEFINITIONAL_REPRESENTATION('',(#555),#559);
#555 = LINE('',#556,#557);
#556 = CARTESIAN_POINT('',(26.242774,0.));
#557 = VECTOR('',#558,1.);
#558 = DIRECTION('',(0.,-1.));
#559 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#560 = PCURVE('',#561,#566);
#561 = PLANE('',#562);
#562 = AXIS2_PLACEMENT_3D('',#563,#564,#565);
#563 = CARTESIAN_POINT('',(85.917422,126.075483,28.));
#564 = DIRECTION('',(-1.,-0.,-0.));
#565 = DIRECTION('',(0.,-1.,0.));
#566 = DEFINITIONAL_REPRESENTATION('',(#567),#571);
#567 = LINE('',#568,#569);
#568 = CARTESIAN_POINT('',(0.,0.));
#569 = VECTOR('',#570,1.);
#570 = DIRECTION('',(0.,-1.));
#571 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#572 = ORIENTED_EDGE('',*,*,#573,.F.);
#573 = EDGE_CURVE('',#470,#546,#574,.T.);
#574 = SURFACE_CURVE('',#575,(#579,#586),.PCURVE_S1.);
#575 = LINE('',#576,#577);
#576 = CARTESIAN_POINT('',(112.160196,126.075483,28.));
#577 = VECTOR('',#578,1.);
#578 = DIRECTION('',(-1.,0.,0.));
#579 = PCURVE('',#485,#580);
#580 = DEFINITIONAL_REPRESENTATION('',(#581),#585);
#581 = LINE('',#582,#583);
#582 = CARTESIAN_POINT('',(0.,0.));
#583 = VECTOR('',#584,1.);
#584 = DIRECTION('',(1.,0.));
#585 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#586 = PCURVE('',#126,#587);
#587 = DEFINITIONAL_REPRESENTATION('',(#588),#592);
#588 = LINE('',#589,#590);
#589 = CARTESIAN_POINT('',(-32.15303580843,-9.172315948003));
#590 = VECTOR('',#591,1.);
#591 = DIRECTION('',(1.,0.));
#592 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#593 = ADVANCED_FACE('',(#594),#561,.F.);
#594 = FACE_BOUND('',#595,.F.);
#595 = EDGE_LOOP('',(#596,#597,#620,#648));
#596 = ORIENTED_EDGE('',*,*,#545,.T.);
#597 = ORIENTED_EDGE('',*,*,#598,.T.);
#598 = EDGE_CURVE('',#523,#599,#601,.T.);
#599 = VERTEX_POINT('',#600);
#600 = CARTESIAN_POINT('',(85.917422,122.856505,-13.767115));
#601 = SURFACE_CURVE('',#602,(#606,#613),.PCURVE_S1.);
#602 = LINE('',#603,#604);
#603 = CARTESIAN_POINT('',(85.917422,126.075483,-13.767115));
#604 = VECTOR('',#605,1.);
#605 = DIRECTION('',(0.,-1.,0.));
#606 = PCURVE('',#561,#607);
#607 = DEFINITIONAL_REPRESENTATION('',(#608),#612);
#608 = LINE('',#609,#610);
#609 = CARTESIAN_POINT('',(-1.42108547152E-14,-41.767115));
#610 = VECTOR('',#611,1.);
#611 = DIRECTION('',(1.,0.));
#612 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#613 = PCURVE('',#72,#614);
#614 = DEFINITIONAL_REPRESENTATION('',(#615),#619);
#615 = LINE('',#616,#617);
#616 = CARTESIAN_POINT('',(-5.910261808433,-9.172315948003));
#617 = VECTOR('',#618,1.);
#618 = DIRECTION('',(0.,-1.));
#619 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#620 = ORIENTED_EDGE('',*,*,#621,.F.);
#621 = EDGE_CURVE('',#622,#599,#624,.T.);
#622 = VERTEX_POINT('',#623);
#623 = CARTESIAN_POINT('',(85.917422,122.856505,28.));
#624 = SURFACE_CURVE('',#625,(#629,#636),.PCURVE_S1.);
#625 = LINE('',#626,#627);
#626 = CARTESIAN_POINT('',(85.917422,122.856505,28.));
#627 = VECTOR('',#628,1.);
#628 = DIRECTION('',(0.,0.,-1.));
#629 = PCURVE('',#561,#630);
#630 = DEFINITIONAL_REPRESENTATION('',(#631),#635);
#631 = LINE('',#632,#633);
#632 = CARTESIAN_POINT('',(3.218978,0.));
#633 = VECTOR('',#634,1.);
#634 = DIRECTION('',(0.,-1.));
#635 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#636 = PCURVE('',#637,#642);
#637 = PLANE('',#638);
#638 = AXIS2_PLACEMENT_3D('',#639,#640,#641);
#639 = CARTESIAN_POINT('',(85.917422,122.856505,28.));
#640 = DIRECTION('',(0.,1.,0.));
#641 = DIRECTION('',(-1.,0.,0.));
#642 = DEFINITIONAL_REPRESENTATION('',(#643),#647);
#643 = LINE('',#644,#645);
#644 = CARTESIAN_POINT('',(0.,0.));
#645 = VECTOR('',#646,1.);
#646 = DIRECTION('',(0.,-1.));
#647 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#648 = ORIENTED_EDGE('',*,*,#649,.F.);
#649 = EDGE_CURVE('',#546,#622,#650,.T.);
#650 = SURFACE_CURVE('',#651,(#655,#662),.PCURVE_S1.);
#651 = LINE('',#652,#653);
#652 = CARTESIAN_POINT('',(85.917422,126.075483,28.));
#653 = VECTOR('',#654,1.);
#654 = DIRECTION('',(0.,-1.,0.));
#655 = PCURVE('',#561,#656);
#656 = DEFINITIONAL_REPRESENTATION('',(#657),#661);
#657 = LINE('',#658,#659);
#658 = CARTESIAN_POINT('',(-1.42108547152E-14,0.));
#659 = VECTOR('',#660,1.);
#660 = DIRECTION('',(1.,0.));
#661 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#662 = PCURVE('',#126,#663);
#663 = DEFINITIONAL_REPRESENTATION('',(#664),#668);
#664 = LINE('',#665,#666);
#665 = CARTESIAN_POINT('',(-5.910261808433,-9.172315948003));
#666 = VECTOR('',#667,1.);
#667 = DIRECTION('',(0.,-1.));
#668 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#669 = ADVANCED_FACE('',(#670),#637,.F.);
#670 = FACE_BOUND('',#671,.F.);
#671 = EDGE_LOOP('',(#672,#673,#696,#724));
#672 = ORIENTED_EDGE('',*,*,#621,.T.);
#673 = ORIENTED_EDGE('',*,*,#674,.T.);
#674 = EDGE_CURVE('',#599,#675,#677,.T.);
#675 = VERTEX_POINT('',#676);
#676 = CARTESIAN_POINT('',(77.717435,122.856505,-13.767115));
#677 = SURFACE_CURVE('',#678,(#682,#689),.PCURVE_S1.);
#678 = LINE('',#679,#680);
#679 = CARTESIAN_POINT('',(85.917422,122.856505,-13.767115));
#680 = VECTOR('',#681,1.);
#681 = DIRECTION('',(-1.,0.,0.));
#682 = PCURVE('',#637,#683);
#683 = DEFINITIONAL_REPRESENTATION('',(#684),#688);
#684 = LINE('',#685,#686);
#685 = CARTESIAN_POINT('',(0.,-41.767115));
#686 = VECTOR('',#687,1.);
#687 = DIRECTION('',(1.,0.));
#688 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#689 = PCURVE('',#72,#690);
#690 = DEFINITIONAL_REPRESENTATION('',(#691),#695);
#691 = LINE('',#692,#693);
#692 = CARTESIAN_POINT('',(-5.910261808433,-12.391293948));
#693 = VECTOR('',#694,1.);
#694 = DIRECTION('',(1.,0.));
#695 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#696 = ORIENTED_EDGE('',*,*,#697,.F.);
#697 = EDGE_CURVE('',#698,#675,#700,.T.);
#698 = VERTEX_POINT('',#699);
#699 = CARTESIAN_POINT('',(77.717435,122.856505,28.));
#700 = SURFACE_CURVE('',#701,(#705,#712),.PCURVE_S1.);
#701 = LINE('',#702,#703);
#702 = CARTESIAN_POINT('',(77.717435,122.856505,28.));
#703 = VECTOR('',#704,1.);
#704 = DIRECTION('',(0.,0.,-1.));
#705 = PCURVE('',#637,#706);
#706 = DEFINITIONAL_REPRESENTATION('',(#707),#711);
#707 = LINE('',#708,#709);
#708 = CARTESIAN_POINT('',(8.199987,0.));
#709 = VECTOR('',#710,1.);
#710 = DIRECTION('',(0.,-1.));
#711 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#712 = PCURVE('',#713,#718);
#713 = PLANE('',#714);
#714 = AXIS2_PLACEMENT_3D('',#715,#716,#717);
#715 = CARTESIAN_POINT('',(77.717435,122.856505,28.));
#716 = DIRECTION('',(-0.999999999978,6.670306744902E-06,0.));
#717 = DIRECTION('',(-6.670306744902E-06,-0.999999999978,0.));
#718 = DEFINITIONAL_REPRESENTATION('',(#719),#723);
#719 = LINE('',#720,#721);
#720 = CARTESIAN_POINT('',(0.,0.));
#721 = VECTOR('',#722,1.);
#722 = DIRECTION('',(-0.,-1.));
#723 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#724 = ORIENTED_EDGE('',*,*,#725,.F.);
#725 = EDGE_CURVE('',#622,#698,#726,.T.);
#726 = SURFACE_CURVE('',#727,(#731,#738),.PCURVE_S1.);
#727 = LINE('',#728,#729);
#728 = CARTESIAN_POINT('',(85.917422,122.856505,28.));
#729 = VECTOR('',#730,1.);
#730 = DIRECTION('',(-1.,0.,0.));
#731 = PCURVE('',#637,#732);
#732 = DEFINITIONAL_REPRESENTATION('',(#733),#737);
#733 = LINE('',#734,#735);
#734 = CARTESIAN_POINT('',(0.,0.));
#735 = VECTOR('',#736,1.);
#736 = DIRECTION('',(1.,0.));
#737 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#738 = PCURVE('',#126,#739);
#739 = DEFINITIONAL_REPRESENTATION('',(#740),#744);
#740 = LINE('',#741,#742);
#741 = CARTESIAN_POINT('',(-5.910261808433,-12.391293948));
#742 = VECTOR('',#743,1.);
#743 = DIRECTION('',(1.,0.));
#744 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#745 = ADVANCED_FACE('',(#746),#713,.F.);
#746 = FACE_BOUND('',#747,.F.);
#747 = EDGE_LOOP('',(#748,#749,#772,#800));
#748 = ORIENTED_EDGE('',*,*,#697,.T.);
#749 = ORIENTED_EDGE('',*,*,#750,.T.);
#750 = EDGE_CURVE('',#675,#751,#753,.T.);
#751 = VERTEX_POINT('',#752);
#752 = CARTESIAN_POINT('',(77.717428,121.807078,-13.767115));
#753 = SURFACE_CURVE('',#754,(#758,#765),.PCURVE_S1.);
#754 = LINE('',#755,#756);
#755 = CARTESIAN_POINT('',(77.717435,122.856505,-13.767115));
#756 = VECTOR('',#757,1.);
#757 = DIRECTION('',(-6.670306744902E-06,-0.999999999978,0.));
#758 = PCURVE('',#713,#759);
#759 = DEFINITIONAL_REPRESENTATION('',(#760),#764);
#760 = LINE('',#761,#762);
#761 = CARTESIAN_POINT('',(2.842170942977E-14,-41.767115));
#762 = VECTOR('',#763,1.);
#763 = DIRECTION('',(1.,0.));
#764 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#765 = PCURVE('',#72,#766);
#766 = DEFINITIONAL_REPRESENTATION('',(#767),#771);
#767 = LINE('',#768,#769);
#768 = CARTESIAN_POINT('',(2.289725191567,-12.391293948));
#769 = VECTOR('',#770,1.);
#770 = DIRECTION('',(6.670306744902E-06,-0.999999999978));
#771 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#772 = ORIENTED_EDGE('',*,*,#773,.F.);
#773 = EDGE_CURVE('',#774,#751,#776,.T.);
#774 = VERTEX_POINT('',#775);
#775 = CARTESIAN_POINT('',(77.717428,121.807078,28.));
#776 = SURFACE_CURVE('',#777,(#781,#788),.PCURVE_S1.);
#777 = LINE('',#778,#779);
#778 = CARTESIAN_POINT('',(77.717428,121.807078,28.));
#779 = VECTOR('',#780,1.);
#780 = DIRECTION('',(0.,0.,-1.));
#781 = PCURVE('',#713,#782);
#782 = DEFINITIONAL_REPRESENTATION('',(#783),#787);
#783 = LINE('',#784,#785);
#784 = CARTESIAN_POINT('',(1.049427000023,0.));
#785 = VECTOR('',#786,1.);
#786 = DIRECTION('',(-0.,-1.));
#787 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#788 = PCURVE('',#789,#794);
#789 = PLANE('',#790);
#790 = AXIS2_PLACEMENT_3D('',#791,#792,#793);
#791 = CARTESIAN_POINT('',(77.717428,121.807078,28.));
#792 = DIRECTION('',(-5.196848995186E-05,0.99999999865,0.));
#793 = DIRECTION('',(-0.99999999865,-5.196848995186E-05,0.));
#794 = DEFINITIONAL_REPRESENTATION('',(#795),#799);
#795 = LINE('',#796,#797);
#796 = CARTESIAN_POINT('',(0.,0.));
#797 = VECTOR('',#798,1.);
#798 = DIRECTION('',(-0.,-1.));
#799 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#800 = ORIENTED_EDGE('',*,*,#801,.F.);
#801 = EDGE_CURVE('',#698,#774,#802,.T.);
#802 = SURFACE_CURVE('',#803,(#807,#814),.PCURVE_S1.);
#803 = LINE('',#804,#805);
#804 = CARTESIAN_POINT('',(77.717435,122.856505,28.));
#805 = VECTOR('',#806,1.);
#806 = DIRECTION('',(-6.670306744902E-06,-0.999999999978,0.));
#807 = PCURVE('',#713,#808);
#808 = DEFINITIONAL_REPRESENTATION('',(#809),#813);
#809 = LINE('',#810,#811);
#810 = CARTESIAN_POINT('',(2.842170942977E-14,0.));
#811 = VECTOR('',#812,1.);
#812 = DIRECTION('',(1.,0.));
#813 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#814 = PCURVE('',#126,#815);
#815 = DEFINITIONAL_REPRESENTATION('',(#816),#820);
#816 = LINE('',#817,#818);
#817 = CARTESIAN_POINT('',(2.289725191567,-12.391293948));
#818 = VECTOR('',#819,1.);
#819 = DIRECTION('',(6.670306744902E-06,-0.999999999978));
#820 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#821 = ADVANCED_FACE('',(#822),#789,.F.);
#822 = FACE_BOUND('',#823,.F.);
#823 = EDGE_LOOP('',(#824,#825,#848,#876));
#824 = ORIENTED_EDGE('',*,*,#773,.T.);
#825 = ORIENTED_EDGE('',*,*,#826,.T.);
#826 = EDGE_CURVE('',#751,#827,#829,.T.);
#827 = VERTEX_POINT('',#828);
#828 = CARTESIAN_POINT('',(51.008936,121.80569,-13.767115));
#829 = SURFACE_CURVE('',#830,(#834,#841),.PCURVE_S1.);
#830 = LINE('',#831,#832);
#831 = CARTESIAN_POINT('',(77.717428,121.807078,-13.767115));
#832 = VECTOR('',#833,1.);
#833 = DIRECTION('',(-0.99999999865,-5.196848995186E-05,0.));
#834 = PCURVE('',#789,#835);
#835 = DEFINITIONAL_REPRESENTATION('',(#836),#840);
#836 = LINE('',#837,#838);
#837 = CARTESIAN_POINT('',(-0.,-41.767115));
#838 = VECTOR('',#839,1.);
#839 = DIRECTION('',(1.,0.));
#840 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#841 = PCURVE('',#72,#842);
#842 = DEFINITIONAL_REPRESENTATION('',(#843),#847);
#843 = LINE('',#844,#845);
#844 = CARTESIAN_POINT('',(2.289732191567,-13.440720948));
#845 = VECTOR('',#846,1.);
#846 = DIRECTION('',(0.99999999865,-5.196848995186E-05));
#847 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#848 = ORIENTED_EDGE('',*,*,#849,.F.);
#849 = EDGE_CURVE('',#850,#827,#852,.T.);
#850 = VERTEX_POINT('',#851);
#851 = CARTESIAN_POINT('',(51.008936,121.80569,28.));
#852 = SURFACE_CURVE('',#853,(#857,#864),.PCURVE_S1.);
#853 = LINE('',#854,#855);
#854 = CARTESIAN_POINT('',(51.008936,121.80569,28.));
#855 = VECTOR('',#856,1.);
#856 = DIRECTION('',(0.,0.,-1.));
#857 = PCURVE('',#789,#858);
#858 = DEFINITIONAL_REPRESENTATION('',(#859),#863);
#859 = LINE('',#860,#861);
#860 = CARTESIAN_POINT('',(26.708492036066,0.));
#861 = VECTOR('',#862,1.);
#862 = DIRECTION('',(-0.,-1.));
#863 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#864 = PCURVE('',#865,#870);
#865 = PLANE('',#866);
#866 = AXIS2_PLACEMENT_3D('',#867,#868,#869);
#867 = CARTESIAN_POINT('',(51.008936,121.80569,28.));
#868 = DIRECTION('',(0.998464005442,5.540424023454E-02,0.));
#869 = DIRECTION('',(-5.540424023454E-02,0.998464005442,0.));
#870 = DEFINITIONAL_REPRESENTATION('',(#871),#875);
#871 = LINE('',#872,#873);
#872 = CARTESIAN_POINT('',(0.,0.));
#873 = VECTOR('',#874,1.);
#874 = DIRECTION('',(0.,-1.));
#875 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#876 = ORIENTED_EDGE('',*,*,#877,.F.);
#877 = EDGE_CURVE('',#774,#850,#878,.T.);
#878 = SURFACE_CURVE('',#879,(#883,#890),.PCURVE_S1.);
#879 = LINE('',#880,#881);
#880 = CARTESIAN_POINT('',(77.717428,121.807078,28.));
#881 = VECTOR('',#882,1.);
#882 = DIRECTION('',(-0.99999999865,-5.196848995186E-05,0.));
#883 = PCURVE('',#789,#884);
#884 = DEFINITIONAL_REPRESENTATION('',(#885),#889);
#885 = LINE('',#886,#887);
#886 = CARTESIAN_POINT('',(0.,0.));
#887 = VECTOR('',#888,1.);
#888 = DIRECTION('',(1.,0.));
#889 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#890 = PCURVE('',#126,#891);
#891 = DEFINITIONAL_REPRESENTATION('',(#892),#896);
#892 = LINE('',#893,#894);
#893 = CARTESIAN_POINT('',(2.289732191567,-13.440720948));
#894 = VECTOR('',#895,1.);
#895 = DIRECTION('',(0.99999999865,-5.196848995186E-05));
#896 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#897 = ADVANCED_FACE('',(#898),#865,.F.);
#898 = FACE_BOUND('',#899,.F.);
#899 = EDGE_LOOP('',(#900,#901,#924,#952));
#900 = ORIENTED_EDGE('',*,*,#849,.T.);
#901 = ORIENTED_EDGE('',*,*,#902,.T.);
#902 = EDGE_CURVE('',#827,#903,#905,.T.);
#903 = VERTEX_POINT('',#904);
#904 = CARTESIAN_POINT('',(50.449147,131.893892,-13.767115));
#905 = SURFACE_CURVE('',#906,(#910,#917),.PCURVE_S1.);
#906 = LINE('',#907,#908);
#907 = CARTESIAN_POINT('',(51.008936,121.80569,-13.767115));
#908 = VECTOR('',#909,1.);
#909 = DIRECTION('',(-5.540424023454E-02,0.998464005442,0.));
#910 = PCURVE('',#865,#911);
#911 = DEFINITIONAL_REPRESENTATION('',(#912),#916);
#912 = LINE('',#913,#914);
#913 = CARTESIAN_POINT('',(2.83780538394E-14,-41.767115));
#914 = VECTOR('',#915,1.);
#915 = DIRECTION('',(1.,0.));
#916 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#917 = PCURVE('',#72,#918);
#918 = DEFINITIONAL_REPRESENTATION('',(#919),#923);
#919 = LINE('',#920,#921);
#920 = CARTESIAN_POINT('',(28.998224191567,-13.442108948));
#921 = VECTOR('',#922,1.);
#922 = DIRECTION('',(5.540424023454E-02,0.998464005442));
#923 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#924 = ORIENTED_EDGE('',*,*,#925,.F.);
#925 = EDGE_CURVE('',#926,#903,#928,.T.);
#926 = VERTEX_POINT('',#927);
#927 = CARTESIAN_POINT('',(50.449147,131.893892,28.));
#928 = SURFACE_CURVE('',#929,(#933,#940),.PCURVE_S1.);
#929 = LINE('',#930,#931);
#930 = CARTESIAN_POINT('',(50.449147,131.893892,28.));
#931 = VECTOR('',#932,1.);
#932 = DIRECTION('',(0.,0.,-1.));
#933 = PCURVE('',#865,#934);
#934 = DEFINITIONAL_REPRESENTATION('',(#935),#939);
#935 = LINE('',#936,#937);
#936 = CARTESIAN_POINT('',(10.103721260868,0.));
#937 = VECTOR('',#938,1.);
#938 = DIRECTION('',(0.,-1.));
#939 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#940 = PCURVE('',#941,#946);
#941 = PLANE('',#942);
#942 = AXIS2_PLACEMENT_3D('',#943,#944,#945);
#943 = CARTESIAN_POINT('',(50.449147,131.893892,28.));
#944 = DIRECTION('',(-4.361953975718E-02,0.999048214928,0.));
#945 = DIRECTION('',(-0.999048214928,-4.361953975718E-02,0.));
#946 = DEFINITIONAL_REPRESENTATION('',(#947),#951);
#947 = LINE('',#948,#949);
#948 = CARTESIAN_POINT('',(0.,0.));
#949 = VECTOR('',#950,1.);
#950 = DIRECTION('',(-0.,-1.));
#951 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#952 = ORIENTED_EDGE('',*,*,#953,.F.);
#953 = EDGE_CURVE('',#850,#926,#954,.T.);
#954 = SURFACE_CURVE('',#955,(#959,#966),.PCURVE_S1.);
#955 = LINE('',#956,#957);
#956 = CARTESIAN_POINT('',(51.008936,121.80569,28.));
#957 = VECTOR('',#958,1.);
#958 = DIRECTION('',(-5.540424023454E-02,0.998464005442,0.));
#959 = PCURVE('',#865,#960);
#960 = DEFINITIONAL_REPRESENTATION('',(#961),#965);
#961 = LINE('',#962,#963);
#962 = CARTESIAN_POINT('',(2.83780538394E-14,0.));
#963 = VECTOR('',#964,1.);
#964 = DIRECTION('',(1.,0.));
#965 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#966 = PCURVE('',#126,#967);
#967 = DEFINITIONAL_REPRESENTATION('',(#968),#972);
#968 = LINE('',#969,#970);
#969 = CARTESIAN_POINT('',(28.998224191567,-13.442108948));
#970 = VECTOR('',#971,1.);
#971 = DIRECTION('',(5.540424023454E-02,0.998464005442));
#972 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#973 = ADVANCED_FACE('',(#974),#941,.F.);
#974 = FACE_BOUND('',#975,.F.);
#975 = EDGE_LOOP('',(#976,#977,#1000,#1023));
#976 = ORIENTED_EDGE('',*,*,#925,.T.);
#977 = ORIENTED_EDGE('',*,*,#978,.T.);
#978 = EDGE_CURVE('',#903,#979,#981,.T.);
#979 = VERTEX_POINT('',#980);
#980 = CARTESIAN_POINT('',(48.301331,131.800116,-13.767115));
#981 = SURFACE_CURVE('',#982,(#986,#993),.PCURVE_S1.);
#982 = LINE('',#983,#984);
#983 = CARTESIAN_POINT('',(50.449147,131.893892,-13.767115));
#984 = VECTOR('',#985,1.);
#985 = DIRECTION('',(-0.999048214928,-4.361953975718E-02,0.));
#986 = PCURVE('',#941,#987);
#987 = DEFINITIONAL_REPRESENTATION('',(#988),#992);
#988 = LINE('',#989,#990);
#989 = CARTESIAN_POINT('',(7.098664517914E-15,-41.767115));
#990 = VECTOR('',#991,1.);
#991 = DIRECTION('',(1.,0.));
#992 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#993 = PCURVE('',#72,#994);
#994 = DEFINITIONAL_REPRESENTATION('',(#995),#999);
#995 = LINE('',#996,#997);
#996 = CARTESIAN_POINT('',(29.558013191567,-3.353906948003));
#997 = VECTOR('',#998,1.);
#998 = DIRECTION('',(0.999048214928,-4.361953975718E-02));
#999 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1000 = ORIENTED_EDGE('',*,*,#1001,.F.);
#1001 = EDGE_CURVE('',#1002,#979,#1004,.T.);
#1002 = VERTEX_POINT('',#1003);
#1003 = CARTESIAN_POINT('',(48.301331,131.800116,28.));
#1004 = SURFACE_CURVE('',#1005,(#1009,#1016),.PCURVE_S1.);
#1005 = LINE('',#1006,#1007);
#1006 = CARTESIAN_POINT('',(48.301331,131.800116,28.));
#1007 = VECTOR('',#1008,1.);
#1008 = DIRECTION('',(0.,0.,-1.));
#1009 = PCURVE('',#941,#1010);
#1010 = DEFINITIONAL_REPRESENTATION('',(#1011),#1015);
#1011 = LINE('',#1012,#1013);
#1012 = CARTESIAN_POINT('',(2.149862206755,0.));
#1013 = VECTOR('',#1014,1.);
#1014 = DIRECTION('',(-0.,-1.));
#1015 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1016 = PCURVE('',#44,#1017);
#1017 = DEFINITIONAL_REPRESENTATION('',(#1018),#1022);
#1018 = LINE('',#1019,#1020);
#1019 = CARTESIAN_POINT('',(0.,0.));
#1020 = VECTOR('',#1021,1.);
#1021 = DIRECTION('',(0.,-1.));
#1022 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1023 = ORIENTED_EDGE('',*,*,#1024,.F.);
#1024 = EDGE_CURVE('',#926,#1002,#1025,.T.);
#1025 = SURFACE_CURVE('',#1026,(#1030,#1037),.PCURVE_S1.);
#1026 = LINE('',#1027,#1028);
#1027 = CARTESIAN_POINT('',(50.449147,131.893892,28.));
#1028 = VECTOR('',#1029,1.);
#1029 = DIRECTION('',(-0.999048214928,-4.361953975718E-02,0.));
#1030 = PCURVE('',#941,#1031);
#1031 = DEFINITIONAL_REPRESENTATION('',(#1032),#1036);
#1032 = LINE('',#1033,#1034);
#1033 = CARTESIAN_POINT('',(7.098664517914E-15,0.));
#1034 = VECTOR('',#1035,1.);
#1035 = DIRECTION('',(1.,0.));
#1036 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1037 = PCURVE('',#126,#1038);
#1038 = DEFINITIONAL_REPRESENTATION('',(#1039),#1043);
#1039 = LINE('',#1040,#1041);
#1040 = CARTESIAN_POINT('',(29.558013191567,-3.353906948003));
#1041 = VECTOR('',#1042,1.);
#1042 = DIRECTION('',(0.999048214928,-4.361953975718E-02));
#1043 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1044 = ADVANCED_FACE('',(#1045),#44,.F.);
#1045 = FACE_BOUND('',#1046,.F.);
#1046 = EDGE_LOOP('',(#1047,#1048,#1069,#1070));
#1047 = ORIENTED_EDGE('',*,*,#1001,.T.);
#1048 = ORIENTED_EDGE('',*,*,#1049,.T.);
#1049 = EDGE_CURVE('',#979,#24,#1050,.T.);
#1050 = SURFACE_CURVE('',#1051,(#1055,#1062),.PCURVE_S1.);
#1051 = LINE('',#1052,#1053);
#1052 = CARTESIAN_POINT('',(48.301331,131.800116,-13.767115));
#1053 = VECTOR('',#1054,1.);
#1054 = DIRECTION('',(-0.11428330053,0.993448200572,0.));
#1055 = PCURVE('',#44,#1056);
#1056 = DEFINITIONAL_REPRESENTATION('',(#1057),#1061);
#1057 = LINE('',#1058,#1059);
#1058 = CARTESIAN_POINT('',(0.,-41.767115));
#1059 = VECTOR('',#1060,1.);
#1060 = DIRECTION('',(1.,0.));
#1061 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1062 = PCURVE('',#72,#1063);
#1063 = DEFINITIONAL_REPRESENTATION('',(#1064),#1068);
#1064 = LINE('',#1065,#1066);
#1065 = CARTESIAN_POINT('',(31.705829191567,-3.447682948003));
#1066 = VECTOR('',#1067,1.);
#1067 = DIRECTION('',(0.11428330053,0.993448200572));
#1068 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1069 = ORIENTED_EDGE('',*,*,#21,.F.);
#1070 = ORIENTED_EDGE('',*,*,#1071,.F.);
#1071 = EDGE_CURVE('',#1002,#22,#1072,.T.);
#1072 = SURFACE_CURVE('',#1073,(#1077,#1084),.PCURVE_S1.);
#1073 = LINE('',#1074,#1075);
#1074 = CARTESIAN_POINT('',(48.301331,131.800116,28.));
#1075 = VECTOR('',#1076,1.);
#1076 = DIRECTION('',(-0.11428330053,0.993448200572,0.));
#1077 = PCURVE('',#44,#1078);
#1078 = DEFINITIONAL_REPRESENTATION('',(#1079),#1083);
#1079 = LINE('',#1080,#1081);
#1080 = CARTESIAN_POINT('',(0.,0.));
#1081 = VECTOR('',#1082,1.);
#1082 = DIRECTION('',(1.,0.));
#1083 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1084 = PCURVE('',#126,#1085);
#1085 = DEFINITIONAL_REPRESENTATION('',(#1086),#1090);
#1086 = LINE('',#1087,#1088);
#1087 = CARTESIAN_POINT('',(31.705829191567,-3.447682948003));
#1088 = VECTOR('',#1089,1.);
#1089 = DIRECTION('',(0.11428330053,0.993448200572));
#1090 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1091 = ADVANCED_FACE('',(#1092),#126,.F.);
#1092 = FACE_BOUND('',#1093,.F.);
#1093 = EDGE_LOOP('',(#1094,#1095,#1096,#1097,#1098,#1099,#1100,#1101,
    #1102,#1103,#1104,#1105,#1106,#1107));
#1094 = ORIENTED_EDGE('',*,*,#112,.T.);
#1095 = ORIENTED_EDGE('',*,*,#193,.T.);
#1096 = ORIENTED_EDGE('',*,*,#269,.T.);
#1097 = ORIENTED_EDGE('',*,*,#345,.T.);
#1098 = ORIENTED_EDGE('',*,*,#421,.T.);
#1099 = ORIENTED_EDGE('',*,*,#497,.T.);
#1100 = ORIENTED_EDGE('',*,*,#573,.T.);
#1101 = ORIENTED_EDGE('',*,*,#649,.T.);
#1102 = ORIENTED_EDGE('',*,*,#725,.T.);
#1103 = ORIENTED_EDGE('',*,*,#801,.T.);
#1104 = ORIENTED_EDGE('',*,*,#877,.T.);
#1105 = ORIENTED_EDGE('',*,*,#953,.T.);
#1106 = ORIENTED_EDGE('',*,*,#1024,.T.);
#1107 = ORIENTED_EDGE('',*,*,#1071,.T.);
#1108 = ADVANCED_FACE('',(#1109),#72,.T.);
#1109 = FACE_BOUND('',#1110,.T.);
#1110 = EDGE_LOOP('',(#1111,#1112,#1113,#1114,#1115,#1116,#1117,#1118,
    #1119,#1120,#1121,#1122,#1123,#1124));
#1111 = ORIENTED_EDGE('',*,*,#56,.T.);
#1112 = ORIENTED_EDGE('',*,*,#142,.T.);
#1113 = ORIENTED_EDGE('',*,*,#218,.T.);
#1114 = ORIENTED_EDGE('',*,*,#294,.T.);
#1115 = ORIENTED_EDGE('',*,*,#370,.T.);
#1116 = ORIENTED_EDGE('',*,*,#446,.T.);
#1117 = ORIENTED_EDGE('',*,*,#522,.T.);
#1118 = ORIENTED_EDGE('',*,*,#598,.T.);
#1119 = ORIENTED_EDGE('',*,*,#674,.T.);
#1120 = ORIENTED_EDGE('',*,*,#750,.T.);
#1121 = ORIENTED_EDGE('',*,*,#826,.T.);
#1122 = ORIENTED_EDGE('',*,*,#902,.T.);
#1123 = ORIENTED_EDGE('',*,*,#978,.T.);
#1124 = ORIENTED_EDGE('',*,*,#1049,.T.);
#1125 = ( GEOMETRIC_REPRESENTATION_CONTEXT(3) 
GLOBAL_UNCERTAINTY_ASSIGNED_CONTEXT((#1129)) 
GLOBAL_UNIT_ASSIGNED_CONTEXT((#1126,#1127,#1128)) REPRESENTATION_CONTEXT
('Context #1','3D Context with UNIT and UNCERTAINTY') );
#1126 = ( LENGTH_UNIT() NAMED_UNIT(*) SI_UNIT(.MILLI.,.METRE.) );
#1127 = ( NAMED_UNIT(*) PLANE_ANGLE_UNIT() SI_UNIT($,.RADIAN.) );
#1128 = ( NAMED_UNIT(*) SI_UNIT($,.STERADIAN.) SOLID_ANGLE_UNIT() );
#1129 = UNCERTAINTY_MEASURE_WITH_UNIT(LENGTH_MEASURE(1.E-07),#1126,
  'distance_accuracy_value','confusion accuracy');
#1130 = PRODUCT_RELATED_PRODUCT_CATEGORY('part',$,(#7));
#1131 = MECHANICAL_DESIGN_GEOMETRIC_PRESENTATION_REPRESENTATION('',(
    #1132),#1125);
#1132 = STYLED_ITEM('color',(#1133),#15);
#1133 = PRESENTATION_STYLE_ASSIGNMENT((#1134));
#1134 = SURFACE_STYLE_USAGE(.BOTH.,#1135);
#1135 = SURFACE_SIDE_STYLE('',(#1136));
#1136 = SURFACE_STYLE_FILL_AREA(#1137);
#1137 = FILL_AREA_STYLE('',(#1138));
#1138 = FILL_AREA_STYLE_COLOUR('',#1139);
#1139 = DRAUGHTING_PRE_DEFINED_COLOUR('cyan');
ENDSEC;
END-ISO-10303-21;
