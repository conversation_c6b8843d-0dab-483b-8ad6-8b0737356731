ISO-10303-21;
HEADER;
FILE_DESCRIPTION(('Open CASCADE Model'),'2;1');
FILE_NAME('Pile_Group_Boundary_3','2025-08-25T20:08:38',('Author'),(
    'Open CASCADE'),'Open CASCADE STEP processor 7.8','build123d',
  'Unknown');
FILE_SCHEMA(('AUTOMOTIVE_DESIGN { 1 0 10303 214 1 1 1 1 }'));
ENDSEC;
DATA;
#1 = APPLICATION_PROTOCOL_DEFINITION('international standard',
  'automotive_design',2000,#2);
#2 = APPLICATION_CONTEXT(
  'core data for automotive mechanical design processes');
#3 = SHAPE_DEFINITION_REPRESENTATION(#4,#10);
#4 = PRODUCT_DEFINITION_SHAPE('','',#5);
#5 = PRODUCT_DEFINITION('design','',#6,#9);
#6 = PRODUCT_DEFINITION_FORMATION('','',#7);
#7 = PRODUCT('Pile_Group_Boundary_3','Pile_Group_Boundary_3','',(#8));
#8 = PRODUCT_CONTEXT('',#2,'mechanical');
#9 = PRODUCT_DEFINITION_CONTEXT('part definition',#2,'design');
#10 = ADVANCED_BREP_SHAPE_REPRESENTATION('',(#11,#15),#969);
#11 = AXIS2_PLACEMENT_3D('',#12,#13,#14);
#12 = CARTESIAN_POINT('',(0.,0.,0.));
#13 = DIRECTION('',(0.,0.,1.));
#14 = DIRECTION('',(1.,0.,-0.));
#15 = MANIFOLD_SOLID_BREP('',#16);
#16 = CLOSED_SHELL('',(#17,#137,#213,#289,#365,#441,#517,#593,#669,#745,
    #821,#892,#939,#954));
#17 = ADVANCED_FACE('',(#18),#32,.F.);
#18 = FACE_BOUND('',#19,.F.);
#19 = EDGE_LOOP('',(#20,#55,#83,#111));
#20 = ORIENTED_EDGE('',*,*,#21,.T.);
#21 = EDGE_CURVE('',#22,#24,#26,.T.);
#22 = VERTEX_POINT('',#23);
#23 = CARTESIAN_POINT('',(51.710833,112.906445,47.5));
#24 = VERTEX_POINT('',#25);
#25 = CARTESIAN_POINT('',(51.710833,112.906445,2.973447));
#26 = SURFACE_CURVE('',#27,(#31,#43),.PCURVE_S1.);
#27 = LINE('',#28,#29);
#28 = CARTESIAN_POINT('',(51.710833,112.906445,47.5));
#29 = VECTOR('',#30,1.);
#30 = DIRECTION('',(0.,0.,-1.));
#31 = PCURVE('',#32,#37);
#32 = PLANE('',#33);
#33 = AXIS2_PLACEMENT_3D('',#34,#35,#36);
#34 = CARTESIAN_POINT('',(51.710833,112.906445,47.5));
#35 = DIRECTION('',(0.,-1.,0.));
#36 = DIRECTION('',(1.,0.,0.));
#37 = DEFINITIONAL_REPRESENTATION('',(#38),#42);
#38 = LINE('',#39,#40);
#39 = CARTESIAN_POINT('',(0.,0.));
#40 = VECTOR('',#41,1.);
#41 = DIRECTION('',(0.,-1.));
#42 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#43 = PCURVE('',#44,#49);
#44 = PLANE('',#45);
#45 = AXIS2_PLACEMENT_3D('',#46,#47,#48);
#46 = CARTESIAN_POINT('',(53.594997,108.077543,47.5));
#47 = DIRECTION('',(0.931596275714,0.363494675443,0.));
#48 = DIRECTION('',(-0.363494675443,0.931596275714,0.));
#49 = DEFINITIONAL_REPRESENTATION('',(#50),#54);
#50 = LINE('',#51,#52);
#51 = CARTESIAN_POINT('',(5.18347070065,0.));
#52 = VECTOR('',#53,1.);
#53 = DIRECTION('',(0.,-1.));
#54 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#55 = ORIENTED_EDGE('',*,*,#56,.T.);
#56 = EDGE_CURVE('',#24,#57,#59,.T.);
#57 = VERTEX_POINT('',#58);
#58 = CARTESIAN_POINT('',(72.917494,112.906445,2.973447));
#59 = SURFACE_CURVE('',#60,(#64,#71),.PCURVE_S1.);
#60 = LINE('',#61,#62);
#61 = CARTESIAN_POINT('',(51.710833,112.906445,2.973447));
#62 = VECTOR('',#63,1.);
#63 = DIRECTION('',(1.,0.,0.));
#64 = PCURVE('',#32,#65);
#65 = DEFINITIONAL_REPRESENTATION('',(#66),#70);
#66 = LINE('',#67,#68);
#67 = CARTESIAN_POINT('',(7.105427357601E-15,-44.526553));
#68 = VECTOR('',#69,1.);
#69 = DIRECTION('',(1.,0.));
#70 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#71 = PCURVE('',#72,#77);
#72 = PLANE('',#73);
#73 = AXIS2_PLACEMENT_3D('',#74,#75,#76);
#74 = CARTESIAN_POINT('',(72.632159577627,105.24663858753,2.973447));
#75 = DIRECTION('',(0.,0.,-1.));
#76 = DIRECTION('',(-1.,0.,0.));
#77 = DEFINITIONAL_REPRESENTATION('',(#78),#82);
#78 = LINE('',#79,#80);
#79 = CARTESIAN_POINT('',(20.921326577627,7.659806412461));
#80 = VECTOR('',#81,1.);
#81 = DIRECTION('',(-1.,0.));
#82 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#83 = ORIENTED_EDGE('',*,*,#84,.F.);
#84 = EDGE_CURVE('',#85,#57,#87,.T.);
#85 = VERTEX_POINT('',#86);
#86 = CARTESIAN_POINT('',(72.917494,112.906445,47.5));
#87 = SURFACE_CURVE('',#88,(#92,#99),.PCURVE_S1.);
#88 = LINE('',#89,#90);
#89 = CARTESIAN_POINT('',(72.917494,112.906445,47.5));
#90 = VECTOR('',#91,1.);
#91 = DIRECTION('',(0.,0.,-1.));
#92 = PCURVE('',#32,#93);
#93 = DEFINITIONAL_REPRESENTATION('',(#94),#98);
#94 = LINE('',#95,#96);
#95 = CARTESIAN_POINT('',(21.206661,0.));
#96 = VECTOR('',#97,1.);
#97 = DIRECTION('',(0.,-1.));
#98 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#99 = PCURVE('',#100,#105);
#100 = PLANE('',#101);
#101 = AXIS2_PLACEMENT_3D('',#102,#103,#104);
#102 = CARTESIAN_POINT('',(72.917494,112.906445,47.5));
#103 = DIRECTION('',(-1.,-0.,-0.));
#104 = DIRECTION('',(0.,-1.,0.));
#105 = DEFINITIONAL_REPRESENTATION('',(#106),#110);
#106 = LINE('',#107,#108);
#107 = CARTESIAN_POINT('',(0.,0.));
#108 = VECTOR('',#109,1.);
#109 = DIRECTION('',(0.,-1.));
#110 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#111 = ORIENTED_EDGE('',*,*,#112,.F.);
#112 = EDGE_CURVE('',#22,#85,#113,.T.);
#113 = SURFACE_CURVE('',#114,(#118,#125),.PCURVE_S1.);
#114 = LINE('',#115,#116);
#115 = CARTESIAN_POINT('',(51.710833,112.906445,47.5));
#116 = VECTOR('',#117,1.);
#117 = DIRECTION('',(1.,0.,0.));
#118 = PCURVE('',#32,#119);
#119 = DEFINITIONAL_REPRESENTATION('',(#120),#124);
#120 = LINE('',#121,#122);
#121 = CARTESIAN_POINT('',(7.105427357601E-15,0.));
#122 = VECTOR('',#123,1.);
#123 = DIRECTION('',(1.,0.));
#124 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#125 = PCURVE('',#126,#131);
#126 = PLANE('',#127);
#127 = AXIS2_PLACEMENT_3D('',#128,#129,#130);
#128 = CARTESIAN_POINT('',(72.632159577627,105.24663858753,47.5));
#129 = DIRECTION('',(0.,0.,-1.));
#130 = DIRECTION('',(-1.,0.,0.));
#131 = DEFINITIONAL_REPRESENTATION('',(#132),#136);
#132 = LINE('',#133,#134);
#133 = CARTESIAN_POINT('',(20.921326577627,7.659806412461));
#134 = VECTOR('',#135,1.);
#135 = DIRECTION('',(-1.,0.));
#136 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#137 = ADVANCED_FACE('',(#138),#100,.F.);
#138 = FACE_BOUND('',#139,.F.);
#139 = EDGE_LOOP('',(#140,#141,#164,#192));
#140 = ORIENTED_EDGE('',*,*,#84,.T.);
#141 = ORIENTED_EDGE('',*,*,#142,.T.);
#142 = EDGE_CURVE('',#57,#143,#145,.T.);
#143 = VERTEX_POINT('',#144);
#144 = CARTESIAN_POINT('',(72.917494,112.30648,2.973447));
#145 = SURFACE_CURVE('',#146,(#150,#157),.PCURVE_S1.);
#146 = LINE('',#147,#148);
#147 = CARTESIAN_POINT('',(72.917494,112.906445,2.973447));
#148 = VECTOR('',#149,1.);
#149 = DIRECTION('',(0.,-1.,0.));
#150 = PCURVE('',#100,#151);
#151 = DEFINITIONAL_REPRESENTATION('',(#152),#156);
#152 = LINE('',#153,#154);
#153 = CARTESIAN_POINT('',(-1.42108547152E-14,-44.526553));
#154 = VECTOR('',#155,1.);
#155 = DIRECTION('',(1.,0.));
#156 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#157 = PCURVE('',#72,#158);
#158 = DEFINITIONAL_REPRESENTATION('',(#159),#163);
#159 = LINE('',#160,#161);
#160 = CARTESIAN_POINT('',(-0.285334422373,7.659806412461));
#161 = VECTOR('',#162,1.);
#162 = DIRECTION('',(0.,-1.));
#163 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#164 = ORIENTED_EDGE('',*,*,#165,.F.);
#165 = EDGE_CURVE('',#166,#143,#168,.T.);
#166 = VERTEX_POINT('',#167);
#167 = CARTESIAN_POINT('',(72.917494,112.30648,47.5));
#168 = SURFACE_CURVE('',#169,(#173,#180),.PCURVE_S1.);
#169 = LINE('',#170,#171);
#170 = CARTESIAN_POINT('',(72.917494,112.30648,47.5));
#171 = VECTOR('',#172,1.);
#172 = DIRECTION('',(0.,0.,-1.));
#173 = PCURVE('',#100,#174);
#174 = DEFINITIONAL_REPRESENTATION('',(#175),#179);
#175 = LINE('',#176,#177);
#176 = CARTESIAN_POINT('',(0.599965,0.));
#177 = VECTOR('',#178,1.);
#178 = DIRECTION('',(0.,-1.));
#179 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#180 = PCURVE('',#181,#186);
#181 = PLANE('',#182);
#182 = AXIS2_PLACEMENT_3D('',#183,#184,#185);
#183 = CARTESIAN_POINT('',(72.917494,112.30648,47.5));
#184 = DIRECTION('',(0.,-1.,0.));
#185 = DIRECTION('',(1.,0.,0.));
#186 = DEFINITIONAL_REPRESENTATION('',(#187),#191);
#187 = LINE('',#188,#189);
#188 = CARTESIAN_POINT('',(0.,0.));
#189 = VECTOR('',#190,1.);
#190 = DIRECTION('',(0.,-1.));
#191 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#192 = ORIENTED_EDGE('',*,*,#193,.F.);
#193 = EDGE_CURVE('',#85,#166,#194,.T.);
#194 = SURFACE_CURVE('',#195,(#199,#206),.PCURVE_S1.);
#195 = LINE('',#196,#197);
#196 = CARTESIAN_POINT('',(72.917494,112.906445,47.5));
#197 = VECTOR('',#198,1.);
#198 = DIRECTION('',(0.,-1.,0.));
#199 = PCURVE('',#100,#200);
#200 = DEFINITIONAL_REPRESENTATION('',(#201),#205);
#201 = LINE('',#202,#203);
#202 = CARTESIAN_POINT('',(-1.42108547152E-14,0.));
#203 = VECTOR('',#204,1.);
#204 = DIRECTION('',(1.,0.));
#205 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#206 = PCURVE('',#126,#207);
#207 = DEFINITIONAL_REPRESENTATION('',(#208),#212);
#208 = LINE('',#209,#210);
#209 = CARTESIAN_POINT('',(-0.285334422373,7.659806412461));
#210 = VECTOR('',#211,1.);
#211 = DIRECTION('',(0.,-1.));
#212 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#213 = ADVANCED_FACE('',(#214),#181,.F.);
#214 = FACE_BOUND('',#215,.F.);
#215 = EDGE_LOOP('',(#216,#217,#240,#268));
#216 = ORIENTED_EDGE('',*,*,#165,.T.);
#217 = ORIENTED_EDGE('',*,*,#218,.T.);
#218 = EDGE_CURVE('',#143,#219,#221,.T.);
#219 = VERTEX_POINT('',#220);
#220 = CARTESIAN_POINT('',(89.317494,112.30648,2.973447));
#221 = SURFACE_CURVE('',#222,(#226,#233),.PCURVE_S1.);
#222 = LINE('',#223,#224);
#223 = CARTESIAN_POINT('',(72.917494,112.30648,2.973447));
#224 = VECTOR('',#225,1.);
#225 = DIRECTION('',(1.,0.,0.));
#226 = PCURVE('',#181,#227);
#227 = DEFINITIONAL_REPRESENTATION('',(#228),#232);
#228 = LINE('',#229,#230);
#229 = CARTESIAN_POINT('',(0.,-44.526553));
#230 = VECTOR('',#231,1.);
#231 = DIRECTION('',(1.,0.));
#232 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#233 = PCURVE('',#72,#234);
#234 = DEFINITIONAL_REPRESENTATION('',(#235),#239);
#235 = LINE('',#236,#237);
#236 = CARTESIAN_POINT('',(-0.285334422373,7.059841412461));
#237 = VECTOR('',#238,1.);
#238 = DIRECTION('',(-1.,0.));
#239 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#240 = ORIENTED_EDGE('',*,*,#241,.F.);
#241 = EDGE_CURVE('',#242,#219,#244,.T.);
#242 = VERTEX_POINT('',#243);
#243 = CARTESIAN_POINT('',(89.317494,112.30648,47.5));
#244 = SURFACE_CURVE('',#245,(#249,#256),.PCURVE_S1.);
#245 = LINE('',#246,#247);
#246 = CARTESIAN_POINT('',(89.317494,112.30648,47.5));
#247 = VECTOR('',#248,1.);
#248 = DIRECTION('',(0.,0.,-1.));
#249 = PCURVE('',#181,#250);
#250 = DEFINITIONAL_REPRESENTATION('',(#251),#255);
#251 = LINE('',#252,#253);
#252 = CARTESIAN_POINT('',(16.4,0.));
#253 = VECTOR('',#254,1.);
#254 = DIRECTION('',(0.,-1.));
#255 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#256 = PCURVE('',#257,#262);
#257 = PLANE('',#258);
#258 = AXIS2_PLACEMENT_3D('',#259,#260,#261);
#259 = CARTESIAN_POINT('',(89.317494,112.30648,47.5));
#260 = DIRECTION('',(-1.,-0.,-0.));
#261 = DIRECTION('',(0.,-1.,0.));
#262 = DEFINITIONAL_REPRESENTATION('',(#263),#267);
#263 = LINE('',#264,#265);
#264 = CARTESIAN_POINT('',(0.,0.));
#265 = VECTOR('',#266,1.);
#266 = DIRECTION('',(0.,-1.));
#267 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#268 = ORIENTED_EDGE('',*,*,#269,.F.);
#269 = EDGE_CURVE('',#166,#242,#270,.T.);
#270 = SURFACE_CURVE('',#271,(#275,#282),.PCURVE_S1.);
#271 = LINE('',#272,#273);
#272 = CARTESIAN_POINT('',(72.917494,112.30648,47.5));
#273 = VECTOR('',#274,1.);
#274 = DIRECTION('',(1.,0.,0.));
#275 = PCURVE('',#181,#276);
#276 = DEFINITIONAL_REPRESENTATION('',(#277),#281);
#277 = LINE('',#278,#279);
#278 = CARTESIAN_POINT('',(0.,0.));
#279 = VECTOR('',#280,1.);
#280 = DIRECTION('',(1.,0.));
#281 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#282 = PCURVE('',#126,#283);
#283 = DEFINITIONAL_REPRESENTATION('',(#284),#288);
#284 = LINE('',#285,#286);
#285 = CARTESIAN_POINT('',(-0.285334422373,7.059841412461));
#286 = VECTOR('',#287,1.);
#287 = DIRECTION('',(-1.,0.));
#288 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#289 = ADVANCED_FACE('',(#290),#257,.F.);
#290 = FACE_BOUND('',#291,.F.);
#291 = EDGE_LOOP('',(#292,#293,#316,#344));
#292 = ORIENTED_EDGE('',*,*,#241,.T.);
#293 = ORIENTED_EDGE('',*,*,#294,.T.);
#294 = EDGE_CURVE('',#219,#295,#297,.T.);
#295 = VERTEX_POINT('',#296);
#296 = CARTESIAN_POINT('',(89.317494,98.606207,2.973447));
#297 = SURFACE_CURVE('',#298,(#302,#309),.PCURVE_S1.);
#298 = LINE('',#299,#300);
#299 = CARTESIAN_POINT('',(89.317494,112.30648,2.973447));
#300 = VECTOR('',#301,1.);
#301 = DIRECTION('',(0.,-1.,0.));
#302 = PCURVE('',#257,#303);
#303 = DEFINITIONAL_REPRESENTATION('',(#304),#308);
#304 = LINE('',#305,#306);
#305 = CARTESIAN_POINT('',(0.,-44.526553));
#306 = VECTOR('',#307,1.);
#307 = DIRECTION('',(1.,0.));
#308 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#309 = PCURVE('',#72,#310);
#310 = DEFINITIONAL_REPRESENTATION('',(#311),#315);
#311 = LINE('',#312,#313);
#312 = CARTESIAN_POINT('',(-16.68533442237,7.059841412461));
#313 = VECTOR('',#314,1.);
#314 = DIRECTION('',(0.,-1.));
#315 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#316 = ORIENTED_EDGE('',*,*,#317,.F.);
#317 = EDGE_CURVE('',#318,#295,#320,.T.);
#318 = VERTEX_POINT('',#319);
#319 = CARTESIAN_POINT('',(89.317494,98.606207,47.5));
#320 = SURFACE_CURVE('',#321,(#325,#332),.PCURVE_S1.);
#321 = LINE('',#322,#323);
#322 = CARTESIAN_POINT('',(89.317494,98.606207,47.5));
#323 = VECTOR('',#324,1.);
#324 = DIRECTION('',(0.,0.,-1.));
#325 = PCURVE('',#257,#326);
#326 = DEFINITIONAL_REPRESENTATION('',(#327),#331);
#327 = LINE('',#328,#329);
#328 = CARTESIAN_POINT('',(13.700273,0.));
#329 = VECTOR('',#330,1.);
#330 = DIRECTION('',(0.,-1.));
#331 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#332 = PCURVE('',#333,#338);
#333 = PLANE('',#334);
#334 = AXIS2_PLACEMENT_3D('',#335,#336,#337);
#335 = CARTESIAN_POINT('',(89.317494,98.606207,47.5));
#336 = DIRECTION('',(-0.999970353247,7.700170607099E-03,0.));
#337 = DIRECTION('',(-7.700170607099E-03,-0.999970353247,0.));
#338 = DEFINITIONAL_REPRESENTATION('',(#339),#343);
#339 = LINE('',#340,#341);
#340 = CARTESIAN_POINT('',(0.,0.));
#341 = VECTOR('',#342,1.);
#342 = DIRECTION('',(-0.,-1.));
#343 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#344 = ORIENTED_EDGE('',*,*,#345,.F.);
#345 = EDGE_CURVE('',#242,#318,#346,.T.);
#346 = SURFACE_CURVE('',#347,(#351,#358),.PCURVE_S1.);
#347 = LINE('',#348,#349);
#348 = CARTESIAN_POINT('',(89.317494,112.30648,47.5));
#349 = VECTOR('',#350,1.);
#350 = DIRECTION('',(0.,-1.,0.));
#351 = PCURVE('',#257,#352);
#352 = DEFINITIONAL_REPRESENTATION('',(#353),#357);
#353 = LINE('',#354,#355);
#354 = CARTESIAN_POINT('',(0.,0.));
#355 = VECTOR('',#356,1.);
#356 = DIRECTION('',(1.,0.));
#357 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#358 = PCURVE('',#126,#359);
#359 = DEFINITIONAL_REPRESENTATION('',(#360),#364);
#360 = LINE('',#361,#362);
#361 = CARTESIAN_POINT('',(-16.68533442237,7.059841412461));
#362 = VECTOR('',#363,1.);
#363 = DIRECTION('',(0.,-1.));
#364 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#365 = ADVANCED_FACE('',(#366),#333,.F.);
#366 = FACE_BOUND('',#367,.F.);
#367 = EDGE_LOOP('',(#368,#369,#392,#420));
#368 = ORIENTED_EDGE('',*,*,#317,.T.);
#369 = ORIENTED_EDGE('',*,*,#370,.T.);
#370 = EDGE_CURVE('',#295,#371,#373,.T.);
#371 = VERTEX_POINT('',#372);
#372 = CARTESIAN_POINT('',(89.283511,94.193059,2.973447));
#373 = SURFACE_CURVE('',#374,(#378,#385),.PCURVE_S1.);
#374 = LINE('',#375,#376);
#375 = CARTESIAN_POINT('',(89.317494,98.606207,2.973447));
#376 = VECTOR('',#377,1.);
#377 = DIRECTION('',(-7.700170607099E-03,-0.999970353247,0.));
#378 = PCURVE('',#333,#379);
#379 = DEFINITIONAL_REPRESENTATION('',(#380),#384);
#380 = LINE('',#381,#382);
#381 = CARTESIAN_POINT('',(1.42104334095E-14,-44.526553));
#382 = VECTOR('',#383,1.);
#383 = DIRECTION('',(1.,0.));
#384 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#385 = PCURVE('',#72,#386);
#386 = DEFINITIONAL_REPRESENTATION('',(#387),#391);
#387 = LINE('',#388,#389);
#388 = CARTESIAN_POINT('',(-16.68533442237,-6.640431587539));
#389 = VECTOR('',#390,1.);
#390 = DIRECTION('',(7.700170607099E-03,-0.999970353247));
#391 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#392 = ORIENTED_EDGE('',*,*,#393,.F.);
#393 = EDGE_CURVE('',#394,#371,#396,.T.);
#394 = VERTEX_POINT('',#395);
#395 = CARTESIAN_POINT('',(89.283511,94.193059,47.5));
#396 = SURFACE_CURVE('',#397,(#401,#408),.PCURVE_S1.);
#397 = LINE('',#398,#399);
#398 = CARTESIAN_POINT('',(89.283511,94.193059,47.5));
#399 = VECTOR('',#400,1.);
#400 = DIRECTION('',(0.,0.,-1.));
#401 = PCURVE('',#333,#402);
#402 = DEFINITIONAL_REPRESENTATION('',(#403),#407);
#403 = LINE('',#404,#405);
#404 = CARTESIAN_POINT('',(4.413278839388,0.));
#405 = VECTOR('',#406,1.);
#406 = DIRECTION('',(-0.,-1.));
#407 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#408 = PCURVE('',#409,#414);
#409 = PLANE('',#410);
#410 = AXIS2_PLACEMENT_3D('',#411,#412,#413);
#411 = CARTESIAN_POINT('',(89.283511,94.193059,47.5));
#412 = DIRECTION('',(0.918584115575,0.395225533858,0.));
#413 = DIRECTION('',(-0.395225533858,0.918584115575,0.));
#414 = DEFINITIONAL_REPRESENTATION('',(#415),#419);
#415 = LINE('',#416,#417);
#416 = CARTESIAN_POINT('',(0.,0.));
#417 = VECTOR('',#418,1.);
#418 = DIRECTION('',(0.,-1.));
#419 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#420 = ORIENTED_EDGE('',*,*,#421,.F.);
#421 = EDGE_CURVE('',#318,#394,#422,.T.);
#422 = SURFACE_CURVE('',#423,(#427,#434),.PCURVE_S1.);
#423 = LINE('',#424,#425);
#424 = CARTESIAN_POINT('',(89.317494,98.606207,47.5));
#425 = VECTOR('',#426,1.);
#426 = DIRECTION('',(-7.700170607099E-03,-0.999970353247,0.));
#427 = PCURVE('',#333,#428);
#428 = DEFINITIONAL_REPRESENTATION('',(#429),#433);
#429 = LINE('',#430,#431);
#430 = CARTESIAN_POINT('',(1.42104334095E-14,0.));
#431 = VECTOR('',#432,1.);
#432 = DIRECTION('',(1.,0.));
#433 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#434 = PCURVE('',#126,#435);
#435 = DEFINITIONAL_REPRESENTATION('',(#436),#440);
#436 = LINE('',#437,#438);
#437 = CARTESIAN_POINT('',(-16.68533442237,-6.640431587539));
#438 = VECTOR('',#439,1.);
#439 = DIRECTION('',(7.700170607099E-03,-0.999970353247));
#440 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#441 = ADVANCED_FACE('',(#442),#409,.F.);
#442 = FACE_BOUND('',#443,.F.);
#443 = EDGE_LOOP('',(#444,#445,#468,#496));
#444 = ORIENTED_EDGE('',*,*,#393,.T.);
#445 = ORIENTED_EDGE('',*,*,#446,.T.);
#446 = EDGE_CURVE('',#371,#447,#449,.T.);
#447 = VERTEX_POINT('',#448);
#448 = CARTESIAN_POINT('',(88.214367,96.677966,2.973447));
#449 = SURFACE_CURVE('',#450,(#454,#461),.PCURVE_S1.);
#450 = LINE('',#451,#452);
#451 = CARTESIAN_POINT('',(89.283511,94.193059,2.973447));
#452 = VECTOR('',#453,1.);
#453 = DIRECTION('',(-0.395225533858,0.918584115575,0.));
#454 = PCURVE('',#409,#455);
#455 = DEFINITIONAL_REPRESENTATION('',(#456),#460);
#456 = LINE('',#457,#458);
#457 = CARTESIAN_POINT('',(0.,-44.526553));
#458 = VECTOR('',#459,1.);
#459 = DIRECTION('',(1.,0.));
#460 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#461 = PCURVE('',#72,#462);
#462 = DEFINITIONAL_REPRESENTATION('',(#463),#467);
#463 = LINE('',#464,#465);
#464 = CARTESIAN_POINT('',(-16.65135142237,-11.05357958753));
#465 = VECTOR('',#466,1.);
#466 = DIRECTION('',(0.395225533858,0.918584115575));
#467 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#468 = ORIENTED_EDGE('',*,*,#469,.F.);
#469 = EDGE_CURVE('',#470,#447,#472,.T.);
#470 = VERTEX_POINT('',#471);
#471 = CARTESIAN_POINT('',(88.214367,96.677966,47.5));
#472 = SURFACE_CURVE('',#473,(#477,#484),.PCURVE_S1.);
#473 = LINE('',#474,#475);
#474 = CARTESIAN_POINT('',(88.214367,96.677966,47.5));
#475 = VECTOR('',#476,1.);
#476 = DIRECTION('',(0.,0.,-1.));
#477 = PCURVE('',#409,#478);
#478 = DEFINITIONAL_REPRESENTATION('',(#479),#483);
#479 = LINE('',#480,#481);
#480 = CARTESIAN_POINT('',(2.705149107052,0.));
#481 = VECTOR('',#482,1.);
#482 = DIRECTION('',(0.,-1.));
#483 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#484 = PCURVE('',#485,#490);
#485 = PLANE('',#486);
#486 = AXIS2_PLACEMENT_3D('',#487,#488,#489);
#487 = CARTESIAN_POINT('',(88.214367,96.677966,47.5));
#488 = DIRECTION('',(-6.761582055227E-02,0.997711431633,0.));
#489 = DIRECTION('',(-0.997711431633,-6.761582055227E-02,0.));
#490 = DEFINITIONAL_REPRESENTATION('',(#491),#495);
#491 = LINE('',#492,#493);
#492 = CARTESIAN_POINT('',(0.,0.));
#493 = VECTOR('',#494,1.);
#494 = DIRECTION('',(-0.,-1.));
#495 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#496 = ORIENTED_EDGE('',*,*,#497,.F.);
#497 = EDGE_CURVE('',#394,#470,#498,.T.);
#498 = SURFACE_CURVE('',#499,(#503,#510),.PCURVE_S1.);
#499 = LINE('',#500,#501);
#500 = CARTESIAN_POINT('',(89.283511,94.193059,47.5));
#501 = VECTOR('',#502,1.);
#502 = DIRECTION('',(-0.395225533858,0.918584115575,0.));
#503 = PCURVE('',#409,#504);
#504 = DEFINITIONAL_REPRESENTATION('',(#505),#509);
#505 = LINE('',#506,#507);
#506 = CARTESIAN_POINT('',(0.,0.));
#507 = VECTOR('',#508,1.);
#508 = DIRECTION('',(1.,0.));
#509 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#510 = PCURVE('',#126,#511);
#511 = DEFINITIONAL_REPRESENTATION('',(#512),#516);
#512 = LINE('',#513,#514);
#513 = CARTESIAN_POINT('',(-16.65135142237,-11.05357958753));
#514 = VECTOR('',#515,1.);
#515 = DIRECTION('',(0.395225533858,0.918584115575));
#516 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#517 = ADVANCED_FACE('',(#518),#485,.F.);
#518 = FACE_BOUND('',#519,.F.);
#519 = EDGE_LOOP('',(#520,#521,#544,#572));
#520 = ORIENTED_EDGE('',*,*,#469,.T.);
#521 = ORIENTED_EDGE('',*,*,#522,.T.);
#522 = EDGE_CURVE('',#447,#523,#525,.T.);
#523 = VERTEX_POINT('',#524);
#524 = CARTESIAN_POINT('',(66.252779,95.189609,2.973447));
#525 = SURFACE_CURVE('',#526,(#530,#537),.PCURVE_S1.);
#526 = LINE('',#527,#528);
#527 = CARTESIAN_POINT('',(88.214367,96.677966,2.973447));
#528 = VECTOR('',#529,1.);
#529 = DIRECTION('',(-0.997711431633,-6.761582055227E-02,0.));
#530 = PCURVE('',#485,#531);
#531 = DEFINITIONAL_REPRESENTATION('',(#532),#536);
#532 = LINE('',#533,#534);
#533 = CARTESIAN_POINT('',(1.417833220263E-14,-44.526553));
#534 = VECTOR('',#535,1.);
#535 = DIRECTION('',(1.,0.));
#536 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#537 = PCURVE('',#72,#538);
#538 = DEFINITIONAL_REPRESENTATION('',(#539),#543);
#539 = LINE('',#540,#541);
#540 = CARTESIAN_POINT('',(-15.58220742237,-8.568672587539));
#541 = VECTOR('',#542,1.);
#542 = DIRECTION('',(0.997711431633,-6.761582055227E-02));
#543 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#544 = ORIENTED_EDGE('',*,*,#545,.F.);
#545 = EDGE_CURVE('',#546,#523,#548,.T.);
#546 = VERTEX_POINT('',#547);
#547 = CARTESIAN_POINT('',(66.252779,95.189609,47.5));
#548 = SURFACE_CURVE('',#549,(#553,#560),.PCURVE_S1.);
#549 = LINE('',#550,#551);
#550 = CARTESIAN_POINT('',(66.252779,95.189609,47.5));
#551 = VECTOR('',#552,1.);
#552 = DIRECTION('',(0.,0.,-1.));
#553 = PCURVE('',#485,#554);
#554 = DEFINITIONAL_REPRESENTATION('',(#555),#559);
#555 = LINE('',#556,#557);
#556 = CARTESIAN_POINT('',(22.011963884242,0.));
#557 = VECTOR('',#558,1.);
#558 = DIRECTION('',(-0.,-1.));
#559 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#560 = PCURVE('',#561,#566);
#561 = PLANE('',#562);
#562 = AXIS2_PLACEMENT_3D('',#563,#564,#565);
#563 = CARTESIAN_POINT('',(66.252779,95.189609,47.5));
#564 = DIRECTION('',(0.999268509488,3.824193960457E-02,0.));
#565 = DIRECTION('',(-3.824193960457E-02,0.999268509488,0.));
#566 = DEFINITIONAL_REPRESENTATION('',(#567),#571);
#567 = LINE('',#568,#569);
#568 = CARTESIAN_POINT('',(0.,0.));
#569 = VECTOR('',#570,1.);
#570 = DIRECTION('',(0.,-1.));
#571 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#572 = ORIENTED_EDGE('',*,*,#573,.F.);
#573 = EDGE_CURVE('',#470,#546,#574,.T.);
#574 = SURFACE_CURVE('',#575,(#579,#586),.PCURVE_S1.);
#575 = LINE('',#576,#577);
#576 = CARTESIAN_POINT('',(88.214367,96.677966,47.5));
#577 = VECTOR('',#578,1.);
#578 = DIRECTION('',(-0.997711431633,-6.761582055227E-02,0.));
#579 = PCURVE('',#485,#580);
#580 = DEFINITIONAL_REPRESENTATION('',(#581),#585);
#581 = LINE('',#582,#583);
#582 = CARTESIAN_POINT('',(1.417833220263E-14,0.));
#583 = VECTOR('',#584,1.);
#584 = DIRECTION('',(1.,0.));
#585 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#586 = PCURVE('',#126,#587);
#587 = DEFINITIONAL_REPRESENTATION('',(#588),#592);
#588 = LINE('',#589,#590);
#589 = CARTESIAN_POINT('',(-15.58220742237,-8.568672587539));
#590 = VECTOR('',#591,1.);
#591 = DIRECTION('',(0.997711431633,-6.761582055227E-02));
#592 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#593 = ADVANCED_FACE('',(#594),#561,.F.);
#594 = FACE_BOUND('',#595,.F.);
#595 = EDGE_LOOP('',(#596,#597,#620,#648));
#596 = ORIENTED_EDGE('',*,*,#545,.T.);
#597 = ORIENTED_EDGE('',*,*,#598,.T.);
#598 = EDGE_CURVE('',#523,#599,#601,.T.);
#599 = VERTEX_POINT('',#600);
#600 = CARTESIAN_POINT('',(66.084004,99.599729,2.973447));
#601 = SURFACE_CURVE('',#602,(#606,#613),.PCURVE_S1.);
#602 = LINE('',#603,#604);
#603 = CARTESIAN_POINT('',(66.252779,95.189609,2.973447));
#604 = VECTOR('',#605,1.);
#605 = DIRECTION('',(-3.824193960457E-02,0.999268509488,0.));
#606 = PCURVE('',#561,#607);
#607 = DEFINITIONAL_REPRESENTATION('',(#608),#612);
#608 = LINE('',#609,#610);
#609 = CARTESIAN_POINT('',(1.420045960982E-14,-44.526553));
#610 = VECTOR('',#611,1.);
#611 = DIRECTION('',(1.,0.));
#612 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#613 = PCURVE('',#72,#614);
#614 = DEFINITIONAL_REPRESENTATION('',(#615),#619);
#615 = LINE('',#616,#617);
#616 = CARTESIAN_POINT('',(6.379380577627,-10.05702958753));
#617 = VECTOR('',#618,1.);
#618 = DIRECTION('',(3.824193960457E-02,0.999268509488));
#619 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#620 = ORIENTED_EDGE('',*,*,#621,.F.);
#621 = EDGE_CURVE('',#622,#599,#624,.T.);
#622 = VERTEX_POINT('',#623);
#623 = CARTESIAN_POINT('',(66.084004,99.599729,47.5));
#624 = SURFACE_CURVE('',#625,(#629,#636),.PCURVE_S1.);
#625 = LINE('',#626,#627);
#626 = CARTESIAN_POINT('',(66.084004,99.599729,47.5));
#627 = VECTOR('',#628,1.);
#628 = DIRECTION('',(0.,0.,-1.));
#629 = PCURVE('',#561,#630);
#630 = DEFINITIONAL_REPRESENTATION('',(#631),#635);
#631 = LINE('',#632,#633);
#632 = CARTESIAN_POINT('',(4.413348322422,0.));
#633 = VECTOR('',#634,1.);
#634 = DIRECTION('',(0.,-1.));
#635 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#636 = PCURVE('',#637,#642);
#637 = PLANE('',#638);
#638 = AXIS2_PLACEMENT_3D('',#639,#640,#641);
#639 = CARTESIAN_POINT('',(66.084004,99.599729,47.5));
#640 = DIRECTION('',(0.473439417635,0.880826383477,0.));
#641 = DIRECTION('',(-0.880826383477,0.473439417635,0.));
#642 = DEFINITIONAL_REPRESENTATION('',(#643),#647);
#643 = LINE('',#644,#645);
#644 = CARTESIAN_POINT('',(0.,0.));
#645 = VECTOR('',#646,1.);
#646 = DIRECTION('',(0.,-1.));
#647 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#648 = ORIENTED_EDGE('',*,*,#649,.F.);
#649 = EDGE_CURVE('',#546,#622,#650,.T.);
#650 = SURFACE_CURVE('',#651,(#655,#662),.PCURVE_S1.);
#651 = LINE('',#652,#653);
#652 = CARTESIAN_POINT('',(66.252779,95.189609,47.5));
#653 = VECTOR('',#654,1.);
#654 = DIRECTION('',(-3.824193960457E-02,0.999268509488,0.));
#655 = PCURVE('',#561,#656);
#656 = DEFINITIONAL_REPRESENTATION('',(#657),#661);
#657 = LINE('',#658,#659);
#658 = CARTESIAN_POINT('',(1.420045960982E-14,0.));
#659 = VECTOR('',#660,1.);
#660 = DIRECTION('',(1.,0.));
#661 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#662 = PCURVE('',#126,#663);
#663 = DEFINITIONAL_REPRESENTATION('',(#664),#668);
#664 = LINE('',#665,#666);
#665 = CARTESIAN_POINT('',(6.379380577627,-10.05702958753));
#666 = VECTOR('',#667,1.);
#667 = DIRECTION('',(3.824193960457E-02,0.999268509488));
#668 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#669 = ADVANCED_FACE('',(#670),#637,.F.);
#670 = FACE_BOUND('',#671,.F.);
#671 = EDGE_LOOP('',(#672,#673,#696,#724));
#672 = ORIENTED_EDGE('',*,*,#621,.T.);
#673 = ORIENTED_EDGE('',*,*,#674,.T.);
#674 = EDGE_CURVE('',#599,#675,#677,.T.);
#675 = VERTEX_POINT('',#676);
#676 = CARTESIAN_POINT('',(59.910247,102.91809,2.973447));
#677 = SURFACE_CURVE('',#678,(#682,#689),.PCURVE_S1.);
#678 = LINE('',#679,#680);
#679 = CARTESIAN_POINT('',(66.084004,99.599729,2.973447));
#680 = VECTOR('',#681,1.);
#681 = DIRECTION('',(-0.880826383477,0.473439417635,0.));
#682 = PCURVE('',#637,#683);
#683 = DEFINITIONAL_REPRESENTATION('',(#684),#688);
#684 = LINE('',#685,#686);
#685 = CARTESIAN_POINT('',(0.,-44.526553));
#686 = VECTOR('',#687,1.);
#687 = DIRECTION('',(1.,0.));
#688 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#689 = PCURVE('',#72,#690);
#690 = DEFINITIONAL_REPRESENTATION('',(#691),#695);
#691 = LINE('',#692,#693);
#692 = CARTESIAN_POINT('',(6.548155577627,-5.646909587539));
#693 = VECTOR('',#694,1.);
#694 = DIRECTION('',(0.880826383477,0.473439417635));
#695 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#696 = ORIENTED_EDGE('',*,*,#697,.F.);
#697 = EDGE_CURVE('',#698,#675,#700,.T.);
#698 = VERTEX_POINT('',#699);
#699 = CARTESIAN_POINT('',(59.910247,102.91809,47.5));
#700 = SURFACE_CURVE('',#701,(#705,#712),.PCURVE_S1.);
#701 = LINE('',#702,#703);
#702 = CARTESIAN_POINT('',(59.910247,102.91809,47.5));
#703 = VECTOR('',#704,1.);
#704 = DIRECTION('',(0.,0.,-1.));
#705 = PCURVE('',#637,#706);
#706 = DEFINITIONAL_REPRESENTATION('',(#707),#711);
#707 = LINE('',#708,#709);
#708 = CARTESIAN_POINT('',(7.009050950119,0.));
#709 = VECTOR('',#710,1.);
#710 = DIRECTION('',(0.,-1.));
#711 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#712 = PCURVE('',#713,#718);
#713 = PLANE('',#714);
#714 = AXIS2_PLACEMENT_3D('',#715,#716,#717);
#715 = CARTESIAN_POINT('',(59.910247,102.91809,47.5));
#716 = DIRECTION('',(0.999261289289,3.843014089773E-02,0.));
#717 = DIRECTION('',(-3.843014089773E-02,0.999261289289,0.));
#718 = DEFINITIONAL_REPRESENTATION('',(#719),#723);
#719 = LINE('',#720,#721);
#720 = CARTESIAN_POINT('',(0.,0.));
#721 = VECTOR('',#722,1.);
#722 = DIRECTION('',(0.,-1.));
#723 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#724 = ORIENTED_EDGE('',*,*,#725,.F.);
#725 = EDGE_CURVE('',#622,#698,#726,.T.);
#726 = SURFACE_CURVE('',#727,(#731,#738),.PCURVE_S1.);
#727 = LINE('',#728,#729);
#728 = CARTESIAN_POINT('',(66.084004,99.599729,47.5));
#729 = VECTOR('',#730,1.);
#730 = DIRECTION('',(-0.880826383477,0.473439417635,0.));
#731 = PCURVE('',#637,#732);
#732 = DEFINITIONAL_REPRESENTATION('',(#733),#737);
#733 = LINE('',#734,#735);
#734 = CARTESIAN_POINT('',(0.,0.));
#735 = VECTOR('',#736,1.);
#736 = DIRECTION('',(1.,0.));
#737 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#738 = PCURVE('',#126,#739);
#739 = DEFINITIONAL_REPRESENTATION('',(#740),#744);
#740 = LINE('',#741,#742);
#741 = CARTESIAN_POINT('',(6.548155577627,-5.646909587539));
#742 = VECTOR('',#743,1.);
#743 = DIRECTION('',(0.880826383477,0.473439417635));
#744 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#745 = ADVANCED_FACE('',(#746),#713,.F.);
#746 = FACE_BOUND('',#747,.F.);
#747 = EDGE_LOOP('',(#748,#749,#772,#800));
#748 = ORIENTED_EDGE('',*,*,#697,.T.);
#749 = ORIENTED_EDGE('',*,*,#750,.T.);
#750 = EDGE_CURVE('',#675,#751,#753,.T.);
#751 = VERTEX_POINT('',#752);
#752 = CARTESIAN_POINT('',(59.699843,108.389019,2.973447));
#753 = SURFACE_CURVE('',#754,(#758,#765),.PCURVE_S1.);
#754 = LINE('',#755,#756);
#755 = CARTESIAN_POINT('',(59.910247,102.91809,2.973447));
#756 = VECTOR('',#757,1.);
#757 = DIRECTION('',(-3.843014089773E-02,0.999261289289,0.));
#758 = PCURVE('',#713,#759);
#759 = DEFINITIONAL_REPRESENTATION('',(#760),#764);
#760 = LINE('',#761,#762);
#761 = CARTESIAN_POINT('',(0.,-44.526553));
#762 = VECTOR('',#763,1.);
#763 = DIRECTION('',(1.,0.));
#764 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#765 = PCURVE('',#72,#766);
#766 = DEFINITIONAL_REPRESENTATION('',(#767),#771);
#767 = LINE('',#768,#769);
#768 = CARTESIAN_POINT('',(12.721912577627,-2.328548587539));
#769 = VECTOR('',#770,1.);
#770 = DIRECTION('',(3.843014089773E-02,0.999261289289));
#771 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#772 = ORIENTED_EDGE('',*,*,#773,.F.);
#773 = EDGE_CURVE('',#774,#751,#776,.T.);
#774 = VERTEX_POINT('',#775);
#775 = CARTESIAN_POINT('',(59.699843,108.389019,47.5));
#776 = SURFACE_CURVE('',#777,(#781,#788),.PCURVE_S1.);
#777 = LINE('',#778,#779);
#778 = CARTESIAN_POINT('',(59.699843,108.389019,47.5));
#779 = VECTOR('',#780,1.);
#780 = DIRECTION('',(0.,0.,-1.));
#781 = PCURVE('',#713,#782);
#782 = DEFINITIONAL_REPRESENTATION('',(#783),#787);
#783 = LINE('',#784,#785);
#784 = CARTESIAN_POINT('',(5.474973421511,0.));
#785 = VECTOR('',#786,1.);
#786 = DIRECTION('',(0.,-1.));
#787 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#788 = PCURVE('',#789,#794);
#789 = PLANE('',#790);
#790 = AXIS2_PLACEMENT_3D('',#791,#792,#793);
#791 = CARTESIAN_POINT('',(59.699843,108.389019,47.5));
#792 = DIRECTION('',(-5.095482832812E-02,0.998700958981,0.));
#793 = DIRECTION('',(-0.998700958981,-5.095482832812E-02,0.));
#794 = DEFINITIONAL_REPRESENTATION('',(#795),#799);
#795 = LINE('',#796,#797);
#796 = CARTESIAN_POINT('',(0.,0.));
#797 = VECTOR('',#798,1.);
#798 = DIRECTION('',(-0.,-1.));
#799 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#800 = ORIENTED_EDGE('',*,*,#801,.F.);
#801 = EDGE_CURVE('',#698,#774,#802,.T.);
#802 = SURFACE_CURVE('',#803,(#807,#814),.PCURVE_S1.);
#803 = LINE('',#804,#805);
#804 = CARTESIAN_POINT('',(59.910247,102.91809,47.5));
#805 = VECTOR('',#806,1.);
#806 = DIRECTION('',(-3.843014089773E-02,0.999261289289,0.));
#807 = PCURVE('',#713,#808);
#808 = DEFINITIONAL_REPRESENTATION('',(#809),#813);
#809 = LINE('',#810,#811);
#810 = CARTESIAN_POINT('',(0.,0.));
#811 = VECTOR('',#812,1.);
#812 = DIRECTION('',(1.,0.));
#813 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#814 = PCURVE('',#126,#815);
#815 = DEFINITIONAL_REPRESENTATION('',(#816),#820);
#816 = LINE('',#817,#818);
#817 = CARTESIAN_POINT('',(12.721912577627,-2.328548587539));
#818 = VECTOR('',#819,1.);
#819 = DIRECTION('',(3.843014089773E-02,0.999261289289));
#820 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#821 = ADVANCED_FACE('',(#822),#789,.F.);
#822 = FACE_BOUND('',#823,.F.);
#823 = EDGE_LOOP('',(#824,#825,#848,#871));
#824 = ORIENTED_EDGE('',*,*,#773,.T.);
#825 = ORIENTED_EDGE('',*,*,#826,.T.);
#826 = EDGE_CURVE('',#751,#827,#829,.T.);
#827 = VERTEX_POINT('',#828);
#828 = CARTESIAN_POINT('',(53.594997,108.077543,2.973447));
#829 = SURFACE_CURVE('',#830,(#834,#841),.PCURVE_S1.);
#830 = LINE('',#831,#832);
#831 = CARTESIAN_POINT('',(59.699843,108.389019,2.973447));
#832 = VECTOR('',#833,1.);
#833 = DIRECTION('',(-0.998700958981,-5.095482832812E-02,0.));
#834 = PCURVE('',#789,#835);
#835 = DEFINITIONAL_REPRESENTATION('',(#836),#840);
#836 = LINE('',#837,#838);
#837 = CARTESIAN_POINT('',(1.419239423202E-14,-44.526553));
#838 = VECTOR('',#839,1.);
#839 = DIRECTION('',(1.,0.));
#840 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#841 = PCURVE('',#72,#842);
#842 = DEFINITIONAL_REPRESENTATION('',(#843),#847);
#843 = LINE('',#844,#845);
#844 = CARTESIAN_POINT('',(12.932316577627,3.142380412461));
#845 = VECTOR('',#846,1.);
#846 = DIRECTION('',(0.998700958981,-5.095482832812E-02));
#847 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#848 = ORIENTED_EDGE('',*,*,#849,.F.);
#849 = EDGE_CURVE('',#850,#827,#852,.T.);
#850 = VERTEX_POINT('',#851);
#851 = CARTESIAN_POINT('',(53.594997,108.077543,47.5));
#852 = SURFACE_CURVE('',#853,(#857,#864),.PCURVE_S1.);
#853 = LINE('',#854,#855);
#854 = CARTESIAN_POINT('',(53.594997,108.077543,47.5));
#855 = VECTOR('',#856,1.);
#856 = DIRECTION('',(0.,0.,-1.));
#857 = PCURVE('',#789,#858);
#858 = DEFINITIONAL_REPRESENTATION('',(#859),#863);
#859 = LINE('',#860,#861);
#860 = CARTESIAN_POINT('',(6.112786760741,0.));
#861 = VECTOR('',#862,1.);
#862 = DIRECTION('',(-0.,-1.));
#863 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#864 = PCURVE('',#44,#865);
#865 = DEFINITIONAL_REPRESENTATION('',(#866),#870);
#866 = LINE('',#867,#868);
#867 = CARTESIAN_POINT('',(0.,0.));
#868 = VECTOR('',#869,1.);
#869 = DIRECTION('',(0.,-1.));
#870 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#871 = ORIENTED_EDGE('',*,*,#872,.F.);
#872 = EDGE_CURVE('',#774,#850,#873,.T.);
#873 = SURFACE_CURVE('',#874,(#878,#885),.PCURVE_S1.);
#874 = LINE('',#875,#876);
#875 = CARTESIAN_POINT('',(59.699843,108.389019,47.5));
#876 = VECTOR('',#877,1.);
#877 = DIRECTION('',(-0.998700958981,-5.095482832812E-02,0.));
#878 = PCURVE('',#789,#879);
#879 = DEFINITIONAL_REPRESENTATION('',(#880),#884);
#880 = LINE('',#881,#882);
#881 = CARTESIAN_POINT('',(1.419239423202E-14,0.));
#882 = VECTOR('',#883,1.);
#883 = DIRECTION('',(1.,0.));
#884 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#885 = PCURVE('',#126,#886);
#886 = DEFINITIONAL_REPRESENTATION('',(#887),#891);
#887 = LINE('',#888,#889);
#888 = CARTESIAN_POINT('',(12.932316577627,3.142380412461));
#889 = VECTOR('',#890,1.);
#890 = DIRECTION('',(0.998700958981,-5.095482832812E-02));
#891 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#892 = ADVANCED_FACE('',(#893),#44,.F.);
#893 = FACE_BOUND('',#894,.F.);
#894 = EDGE_LOOP('',(#895,#896,#917,#918));
#895 = ORIENTED_EDGE('',*,*,#849,.T.);
#896 = ORIENTED_EDGE('',*,*,#897,.T.);
#897 = EDGE_CURVE('',#827,#24,#898,.T.);
#898 = SURFACE_CURVE('',#899,(#903,#910),.PCURVE_S1.);
#899 = LINE('',#900,#901);
#900 = CARTESIAN_POINT('',(53.594997,108.077543,2.973447));
#901 = VECTOR('',#902,1.);
#902 = DIRECTION('',(-0.363494675443,0.931596275714,0.));
#903 = PCURVE('',#44,#904);
#904 = DEFINITIONAL_REPRESENTATION('',(#905),#909);
#905 = LINE('',#906,#907);
#906 = CARTESIAN_POINT('',(-1.582156433863E-14,-44.526553));
#907 = VECTOR('',#908,1.);
#908 = DIRECTION('',(1.,0.));
#909 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#910 = PCURVE('',#72,#911);
#911 = DEFINITIONAL_REPRESENTATION('',(#912),#916);
#912 = LINE('',#913,#914);
#913 = CARTESIAN_POINT('',(19.037162577627,2.830904412461));
#914 = VECTOR('',#915,1.);
#915 = DIRECTION('',(0.363494675443,0.931596275714));
#916 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#917 = ORIENTED_EDGE('',*,*,#21,.F.);
#918 = ORIENTED_EDGE('',*,*,#919,.F.);
#919 = EDGE_CURVE('',#850,#22,#920,.T.);
#920 = SURFACE_CURVE('',#921,(#925,#932),.PCURVE_S1.);
#921 = LINE('',#922,#923);
#922 = CARTESIAN_POINT('',(53.594997,108.077543,47.5));
#923 = VECTOR('',#924,1.);
#924 = DIRECTION('',(-0.363494675443,0.931596275714,0.));
#925 = PCURVE('',#44,#926);
#926 = DEFINITIONAL_REPRESENTATION('',(#927),#931);
#927 = LINE('',#928,#929);
#928 = CARTESIAN_POINT('',(-1.582156433863E-14,0.));
#929 = VECTOR('',#930,1.);
#930 = DIRECTION('',(1.,0.));
#931 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#932 = PCURVE('',#126,#933);
#933 = DEFINITIONAL_REPRESENTATION('',(#934),#938);
#934 = LINE('',#935,#936);
#935 = CARTESIAN_POINT('',(19.037162577627,2.830904412461));
#936 = VECTOR('',#937,1.);
#937 = DIRECTION('',(0.363494675443,0.931596275714));
#938 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#939 = ADVANCED_FACE('',(#940),#126,.F.);
#940 = FACE_BOUND('',#941,.F.);
#941 = EDGE_LOOP('',(#942,#943,#944,#945,#946,#947,#948,#949,#950,#951,
    #952,#953));
#942 = ORIENTED_EDGE('',*,*,#112,.T.);
#943 = ORIENTED_EDGE('',*,*,#193,.T.);
#944 = ORIENTED_EDGE('',*,*,#269,.T.);
#945 = ORIENTED_EDGE('',*,*,#345,.T.);
#946 = ORIENTED_EDGE('',*,*,#421,.T.);
#947 = ORIENTED_EDGE('',*,*,#497,.T.);
#948 = ORIENTED_EDGE('',*,*,#573,.T.);
#949 = ORIENTED_EDGE('',*,*,#649,.T.);
#950 = ORIENTED_EDGE('',*,*,#725,.T.);
#951 = ORIENTED_EDGE('',*,*,#801,.T.);
#952 = ORIENTED_EDGE('',*,*,#872,.T.);
#953 = ORIENTED_EDGE('',*,*,#919,.T.);
#954 = ADVANCED_FACE('',(#955),#72,.T.);
#955 = FACE_BOUND('',#956,.T.);
#956 = EDGE_LOOP('',(#957,#958,#959,#960,#961,#962,#963,#964,#965,#966,
    #967,#968));
#957 = ORIENTED_EDGE('',*,*,#56,.T.);
#958 = ORIENTED_EDGE('',*,*,#142,.T.);
#959 = ORIENTED_EDGE('',*,*,#218,.T.);
#960 = ORIENTED_EDGE('',*,*,#294,.T.);
#961 = ORIENTED_EDGE('',*,*,#370,.T.);
#962 = ORIENTED_EDGE('',*,*,#446,.T.);
#963 = ORIENTED_EDGE('',*,*,#522,.T.);
#964 = ORIENTED_EDGE('',*,*,#598,.T.);
#965 = ORIENTED_EDGE('',*,*,#674,.T.);
#966 = ORIENTED_EDGE('',*,*,#750,.T.);
#967 = ORIENTED_EDGE('',*,*,#826,.T.);
#968 = ORIENTED_EDGE('',*,*,#897,.T.);
#969 = ( GEOMETRIC_REPRESENTATION_CONTEXT(3) 
GLOBAL_UNCERTAINTY_ASSIGNED_CONTEXT((#973)) GLOBAL_UNIT_ASSIGNED_CONTEXT
((#970,#971,#972)) REPRESENTATION_CONTEXT('Context #1',
  '3D Context with UNIT and UNCERTAINTY') );
#970 = ( LENGTH_UNIT() NAMED_UNIT(*) SI_UNIT(.MILLI.,.METRE.) );
#971 = ( NAMED_UNIT(*) PLANE_ANGLE_UNIT() SI_UNIT($,.RADIAN.) );
#972 = ( NAMED_UNIT(*) SI_UNIT($,.STERADIAN.) SOLID_ANGLE_UNIT() );
#973 = UNCERTAINTY_MEASURE_WITH_UNIT(LENGTH_MEASURE(1.E-07),#970,
  'distance_accuracy_value','confusion accuracy');
#974 = PRODUCT_RELATED_PRODUCT_CATEGORY('part',$,(#7));
#975 = MECHANICAL_DESIGN_GEOMETRIC_PRESENTATION_REPRESENTATION('',(#976)
  ,#969);
#976 = STYLED_ITEM('color',(#977),#15);
#977 = PRESENTATION_STYLE_ASSIGNMENT((#978));
#978 = SURFACE_STYLE_USAGE(.BOTH.,#979);
#979 = SURFACE_SIDE_STYLE('',(#980));
#980 = SURFACE_STYLE_FILL_AREA(#981);
#981 = FILL_AREA_STYLE('',(#982));
#982 = FILL_AREA_STYLE_COLOUR('',#983);
#983 = DRAUGHTING_PRE_DEFINED_COLOUR('cyan');
ENDSEC;
END-ISO-10303-21;
