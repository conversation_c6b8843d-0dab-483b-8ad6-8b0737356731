#!/usr/bin/env python3
"""
Union Operations Module

Handles efficient union operations using build123d algebra mode.
Implements progressive union strategy with multiple fallback approaches.
"""

from typing import Any, List, Optional, Tuple, Dict
import logging
from .base_types import GeometryError
from .union_validation import EnhancedUnionOperations, UnionValidationEngine


class UnionOperations:
    """Efficient union operations for geometry components with enhanced precision handling.
    
    Features:
    - Progressive union strategy with fallback approaches
    - Enhanced precision union operations for complete spatial inclusion
    - Comprehensive validation and debugging capabilities
    """
    
    # Constants for union operations
    MAX_UNION_ATTEMPTS = 10
    VOLUME_PRECISION_THRESHOLD = 1e-12
    LARGE_GEOMETRY_THRESHOLD = 100.0
    
    def __init__(self, logger: Optional[logging.Logger] = None):
        """Initialize union operations."""
        self.logger = logger or logging.getLogger(__name__)
        self.enhanced_operations = EnhancedUnionOperations(logger)
        self.validation_engine = UnionValidationEngine(logger)
    
    def _is_valid_geometry(self, obj: Any) -> bool:
        """Check if object is valid geometry."""
        if obj is None:
            return False
            
        # Check for Compound objects first (from union operations that don't merge)
        if hasattr(obj, '__class__') and 'Compound' in str(obj.__class__):
            return True
        
        # Standard validation for Solid objects
        return (hasattr(obj, 'volume') and 
                hasattr(obj, '__class__') and
                getattr(obj, 'volume', 0) > 0)
    
    def _extract_valid_components_from_list(self, components: List[Any]) -> List[Any]:
        """
        Extract and validate components from input list using latest build123d patterns.
        
        Args:
            components: List of geometry components
            
        Returns:
            List of valid geometry components
        """
        valid_components = []
        self.logger.debug("=== COMPONENT EXTRACTION START ===")
        
        for i, c in enumerate(components):
            # Removed verbose component processing log
            
            # Check if it has .solids() method first (Compound objects)
            if hasattr(c, 'solids'):
                try:
                    solids_result = c.solids()
                    if hasattr(solids_result, '__iter__') and not isinstance(solids_result, str):
                        # It's iterable (ShapeList) - extract individual solids
                        try:
                            for j, solid in enumerate(solids_result):
                                if self._is_valid_geometry(solid):
                                    valid_components.append(solid)
                        except TypeError:
                            # ShapeList appeared iterable but isn't - treat as single object
                            if self._is_valid_geometry(solids_result):
                                valid_components.append(solids_result)
                    else:
                        # Single solid from .solids() method
                        if self._is_valid_geometry(solids_result):
                            valid_components.append(solids_result)
                except (TypeError, AttributeError):
                    # .solids() failed, treat as single object
                    if self._is_valid_geometry(c):
                        valid_components.append(c)
            elif self._is_valid_geometry(c):
                # Single solid object
                valid_components.append(c)
        
        self.logger.debug(f"Extraction complete. Valid components: {len(valid_components)}")
        return valid_components
    
    def _union_component_pair(self, comp1: Any, comp2: Any) -> Tuple[Any, bool, str]:
        """
        Attempt to union two components with improved fallback strategies for large groups.
        
        Args:
            comp1: First component
            comp2: Second component
            
        Returns:
            Tuple of (result, success_flag, method_used)
        """
        vol1 = getattr(comp1, 'volume', 0.0)
        vol2 = getattr(comp2, 'volume', 0.0)
        
        # NEW: For very small volumes, use more tolerant union approach
        if vol1 < 1.0 or vol2 < 1.0:
            return self._union_small_components(comp1, comp2, vol1, vol2)
        
        try:
            # Primary approach: use '+' operator with improved error handling
            self.logger.debug(f"🔧 Attempting PRIMARY union (+) for volumes {vol1:.2f} + {vol2:.2f} m³")
            try:
                union_result = comp1 + comp2
                result, success = self._process_union_result(union_result)
                if success:
                    result_vol = getattr(result, 'volume', 0.0)
                    self.logger.info(f"✅ PRIMARY union (+) successful: {vol1:.2f} + {vol2:.2f} → {result_vol:.2f} m³")
                    return result, True, "primary"
                else:
                    self.logger.warning(f"⚠️  PRIMARY union (+) failed: {vol1:.2f} + {vol2:.2f} m³ - trying fallbacks")
            except Exception as e:
                self.logger.warning(f"⚠️  PRIMARY union (+) exception: {type(e).__name__}: {str(e)}")
            
            # Alternative 1: Try .fuse() method with improved error handling
            try:
                self.logger.debug(f"🔄 Attempting FALLBACK 1: fuse() method for volumes {vol1:.2f} + {vol2:.2f} m³")
                fuse_result = comp1.fuse(comp2)
                result, success = self._process_union_result(fuse_result)
                if success:
                    result_vol = getattr(result, 'volume', 0.0)
                    self.logger.info(f"✅ FALLBACK 1: fuse() successful: {vol1:.2f} + {vol2:.2f} → {result_vol:.2f} m³")
                    return result, True, "fuse"
                else:
                    self.logger.warning(f"⚠️  FALLBACK 1: fuse() failed: {vol1:.2f} + {vol2:.2f} m³")
            except Exception as e:
                self.logger.warning(f"⚠️  FALLBACK 1: fuse() failed: {vol1:.2f} + {vol2:.2f} m³")
            
            # NEW: Alternative 2.5: Try intersection-based union for complex geometries
            try:
                intersection_result = self._try_intersection_based_union(comp1, comp2, vol1, vol2)
                if intersection_result is not None:
                    return intersection_result
            except Exception as e:
                self.logger.debug(f"Intersection-based union failed: {e}")
            
            # Alternative 2: Create compound with improved metadata
            try:
                self.logger.warning(f"🔄 Using FALLBACK 2: compound for volumes {vol1:.2f} + {vol2:.2f} m³")
                from build123d import Compound
                compound_result = Compound(children=[comp1, comp2])
                compound_result._is_union_fallback = True
                compound_result._original_components = [comp1, comp2]
                compound_vol = getattr(compound_result, 'volume', vol1 + vol2)
                self.logger.warning(f"⚠️  FALLBACK 2: compound created: {vol1:.2f} + {vol2:.2f} → {compound_vol:.2f} m³ (no union)")
                return compound_result, False, "compound"
            except Exception as e:
                self.logger.error(f"❌ FALLBACK 2: compound creation failed: {type(e).__name__}: {str(e)}")
            
            # Final fallback - return both components separately
            self.logger.error(f"❌ ALL UNION METHODS FAILED for volumes {vol1:.2f} + {vol2:.2f} m³ - returning separate components")
            return [comp1, comp2], False, "failed"
            
        except Exception as e:
            error_msg = f"❌ UNION OPERATION EXCEPTION between components with volumes {vol1:.3f} and {vol2:.3f} m³"
            error_msg += f" - Exception: {type(e).__name__}: {str(e)}"
            self.logger.error(error_msg)
            return [comp1, comp2], False, "failed"
    
    def _union_small_components(self, comp1: Any, comp2: Any, vol1: float, vol2: float) -> Tuple[Any, bool, str]:
        """
        Handle union of very small components with special tolerance.
        
        Args:
            comp1: First component
            comp2: Second component  
            vol1: Volume of first component
            vol2: Volume of second component
            
        Returns:
            Tuple of (result, success_flag, method_used)
        """
        self.logger.debug(f"🔬 Processing small components: {vol1:.3f} + {vol2:.3f} m³")
        
        # For very small components, compound is often the most reliable approach
        try:
            from build123d import Compound
            compound_result = Compound(children=[comp1, comp2])
            compound_result._is_union_fallback = True
            compound_result._original_components = [comp1, comp2]
            compound_vol = getattr(compound_result, 'volume', vol1 + vol2)
            self.logger.debug(f"✅ Small component compound: {vol1:.3f} + {vol2:.3f} → {compound_vol:.3f} m³")
            return compound_result, False, "compound"
        except Exception as e:
            self.logger.warning(f"⚠️  Small component compound failed: {e}")
            return [comp1, comp2], False, "failed"
    
    def _try_intersection_based_union(self, comp1: Any, comp2: Any, vol1: float, vol2: float) -> Optional[Tuple[Any, bool, str]]:
        """
        Try union using intersection analysis for better geometry handling.
        
        Args:
            comp1: First component
            comp2: Second component
            vol1: Volume of first component
            vol2: Volume of second component
            
        Returns:
            Union result or None if not applicable
        """
        try:
            # Check if components actually intersect
            intersection = comp1 & comp2
            intersection_vol = getattr(intersection, 'volume', 0.0)
            
            if intersection_vol > 0.01:  # Components intersect meaningfully
                self.logger.debug(f"🔍 Components intersect: {intersection_vol:.3f} m³")
                # Try difference-based approach
                try:
                    diff1 = comp1 - comp2
                    diff2 = comp2 - comp1
                    
                    # Combine differences with intersection
                    from build123d import Compound
                    combined = Compound(children=[diff1, diff2, intersection])
                    combined_vol = getattr(combined, 'volume', 0.0)
                    
                    if abs(combined_vol - (vol1 + vol2 - intersection_vol)) < 0.1:
                        self.logger.info(f"✅ INTERSECTION union successful: {vol1:.2f} + {vol2:.2f} → {combined_vol:.2f} m³")
                        return combined, True, "intersection"
                        
                except Exception as e:
                    self.logger.debug(f"Intersection-based union failed: {e}")
                    
        except Exception as e:
            self.logger.debug(f"Intersection test failed: {e}")
            
        return None
    
    def _process_union_result(self, union_result: Any) -> Tuple[Any, bool]:
        """
        Process union result and check if it represents a successful union.
        
        Args:
            union_result: Result from union operation
            
        Returns:
            Tuple of (processed_result, success_flag)
        """
        if hasattr(union_result, '__iter__') and not isinstance(union_result, str):
            # Handle ShapeList result
            try:
                result_list = list(union_result)
                result_volumes = [getattr(obj, 'volume', 0.0) for obj in result_list]
                # Removed verbose union result volumes log
                
                if len(result_list) == 1:
                    # Successful union - single merged solid
                    self.logger.debug(f"Successfully merged 2 -> 1 solid (volume: {result_volumes[0]:.3f})")
                    return result_list[0], True
                else:
                    # Failed union - multiple objects remain
                    self.logger.debug(f"Union failed, {len(result_list)} objects remain separate")
                    return result_list, False
            except Exception:
                return union_result, False
        else:
            # Single object result - assume successful union
            result_vol = getattr(union_result, 'volume', 0.0)
            self.logger.debug(f"Successfully merged (single object, volume: {result_vol:.3f})")
            return union_result, True
    
    def _perform_progressive_union(self, valid_components: List[Any]) -> Any:
        """
        Perform progressive union strategy with multiple rounds.
        
        Args:
            valid_components: List of valid geometry components
            
        Returns:
            Union result
        """
        from datetime import datetime
        
        remaining_components = valid_components.copy()
        union_attempts = 0
        successful_unions = 0
        
        # Track union method statistics
        primary_success = 0
        fallback1_success = 0
        fallback2_used = 0
        total_failures = 0
        
        self.logger.info(f"🔄 Starting progressive union with {len(valid_components)} components")
        
        while len(remaining_components) > 1 and union_attempts < self.MAX_UNION_ATTEMPTS:
            union_attempts += 1
            round_start = datetime.now()
            next_round = []
            pairs_processed = 0
            
            # Track method usage this round
            round_primary = 0
            round_fallback1 = 0
            round_fallback2 = 0
            round_failures = 0
            
            self.logger.info(f"⏱️  Round {union_attempts}: Processing {len(remaining_components)} components...")
            
            # Process pairs
            i = 0
            while i < len(remaining_components):
                if i + 1 < len(remaining_components):
                    # Try to union pair
                    comp1 = remaining_components[i]
                    comp2 = remaining_components[i + 1]
                    pairs_processed += 1
                    
                    # Log progress for large operations
                    if pairs_processed % 5 == 0 or pairs_processed == 1:
                        self.logger.info(f"🔧 Processing pair {pairs_processed}...")
                    
                    # Track what method was used by checking the logs or result type
                    result, success, method_used = self._union_component_pair(comp1, comp2)
                    
                    # Track method usage statistics
                    if method_used == "primary":
                        round_primary += 1
                        primary_success += 1
                    elif method_used == "fuse":
                        round_fallback1 += 1
                        fallback1_success += 1
                    elif method_used == "compound":
                        round_fallback2 += 1
                        fallback2_used += 1
                    elif method_used == "failed":
                        round_failures += 1
                        total_failures += 1
                    
                    # Check result characteristics to determine which method was used
                    if isinstance(result, list):
                        next_round.extend(result)
                    else:
                        if success:
                            successful_unions += 1
                        next_round.append(result)
                    
                    i += 2  # Skip next component since we processed a pair
                else:
                    # Odd component out, carry forward to next round
                    odd_vol = getattr(remaining_components[i], 'volume', 0.0)
                    self.logger.debug(f"Carrying forward odd component with volume {odd_vol:.3f}")
                    next_round.append(remaining_components[i])
                    i += 1
            
            remaining_components = next_round
            round_duration = datetime.now() - round_start
            
            # Report round statistics
            if pairs_processed > 0:
                primary_pct = (round_primary / pairs_processed) * 100
                fuse_pct = (round_fallback1 / pairs_processed) * 100
                fallback2_pct = (round_fallback2 / pairs_processed) * 100
                failure_pct = (round_failures / pairs_processed) * 100
                
                self.logger.info(f"✅ Round {union_attempts} completed in {round_duration.total_seconds():.2f}s: {len(remaining_components)} components remain")
                self.logger.info(f"📊 Round {union_attempts} methods: {primary_pct:.0f}% primary (+), {fuse_pct:.0f}% fuse(), {fallback2_pct:.0f}% compound, {failure_pct:.0f}% failed")
            
            # If no progress was made, break to avoid infinite loop
            if pairs_processed == 0:
                self.logger.info("ℹ️  No pairs processed, terminating progressive union")
                break
            
            # Try exhaustive pairing for small remaining sets
            if len(remaining_components) <= 3 and union_attempts >= 2:
                self.logger.info("⏱️  Attempting exhaustive search for remaining components...")
                exhaustive_start = datetime.now()
                remaining_components, additional_unions = self._try_exhaustive_union_search(remaining_components)
                exhaustive_duration = datetime.now() - exhaustive_start
                self.logger.info(f"✅ Exhaustive search completed in {exhaustive_duration.total_seconds():.2f}s")
                successful_unions += additional_unions
        
        # Final statistics
        total_attempts = primary_success + fallback1_success + fallback2_used + total_failures
        if total_attempts > 0:
            primary_pct = (primary_success / total_attempts) * 100
            fallback1_pct = (fallback1_success / total_attempts) * 100
            fallback2_pct = (fallback2_used / total_attempts) * 100
            failure_pct = (total_failures / total_attempts) * 100
            
            self.logger.info(f"🎉 Progressive union complete: {successful_unions} successful unions in {union_attempts} rounds")
            self.logger.info(f"📈 Overall method usage: {primary_pct:.1f}% primary (+), {fallback1_pct:.1f}% fuse(), {fallback2_pct:.1f}% compound, {failure_pct:.1f}% failed")
            
            # Enhanced explanation of union behavior based on results
            if len(remaining_components) > 1:
                self.logger.info(f"📦 MULTIPLE UNION PARTS RESULT: {len(remaining_components)} separate objects remain")
                self.logger.info("🔍 Why multiple parts are created:")
                
                if fallback2_pct > 50:
                    self.logger.info("   • HIGH COMPOUND USAGE: Most unions fell back to compound operations")
                    self.logger.info("   • Compound operations preserve separate objects without true merging")
                    self.logger.info("   • Complex pile overlaps resist geometric merging")
                    
                if primary_pct < 30:
                    self.logger.info("   • LOW PRIMARY SUCCESS: Basic union operations mostly failed")
                    self.logger.info("   • Indicates challenging geometry with complex intersections")
                    
                if len(remaining_components) == total_attempts:
                    self.logger.info("   • NO SUCCESSFUL MERGES: All union attempts preserved separate objects")
                    self.logger.info("   • Each original component becomes a separate final part")
                else:
                    merged_ratio = (len(remaining_components) / total_attempts) * 100
                    self.logger.info(f"   • PARTIAL MERGING: {merged_ratio:.1f}% reduction in object count")
                    self.logger.info("   • Some components merged successfully, others remained separate")
                    
                self.logger.info("📋 Each remaining object will become a separate 'Part3_Union_X' in the export")
            else:
                self.logger.info("✅ SINGLE UNIFIED RESULT: All components successfully merged into one solid")
        
        return self._process_final_union_result(remaining_components, len(valid_components), successful_unions)
    
    def _try_exhaustive_union_search(self, remaining_components: List[Any]) -> Tuple[List[Any], int]:
        """
        Try all possible pairings for small component sets.
        
        Args:
            remaining_components: Components to try exhaustive pairing on
            
        Returns:
            Tuple of (updated_components, successful_unions_count)
        """
        self.logger.debug(f"Attempting exhaustive pairing for {len(remaining_components)} components")
        successful_unions = 0
        
        # Try all possible pairs
        for i in range(len(remaining_components)):
            for j in range(i + 1, len(remaining_components)):
                comp1 = remaining_components[i]
                comp2 = remaining_components[j]
                vol1 = getattr(comp1, 'volume', 0.0)
                vol2 = getattr(comp2, 'volume', 0.0)
                
                self.logger.debug(f"Exhaustive attempt: components {i},{j} volumes {vol1:.3f} + {vol2:.3f}")
                
                result, success, method_used = self._union_component_pair(comp1, comp2)
                
                if success and not isinstance(result, list):
                    # Found a successful union!
                    self.logger.info(f"Exhaustive search found successful union: {i},{j} -> single object")
                    
                    # Create new component list with the merged result
                    new_components = []
                    for k, comp in enumerate(remaining_components):
                        if k != i and k != j:
                            new_components.append(comp)
                    new_components.append(result)
                    
                    successful_unions += 1
                    self.logger.debug(f"Exhaustive union successful: {len(new_components)} components remain")
                    return new_components, successful_unions
        
        return remaining_components, successful_unions
    
    def _process_final_union_result(self, remaining_components: List[Any], 
                                   original_count: int, successful_unions: int) -> Any:
        """
        Process the final result of union operations.
        
        Args:
            remaining_components: Components remaining after union attempts
            original_count: Original number of components
            successful_unions: Number of successful unions achieved
            
        Returns:
            Final union result
        """
        if len(remaining_components) == 1:
            # Successfully reduced to single component
            result = remaining_components[0]
            self.logger.debug(f"Union successful: {original_count} components -> 1 merged object")
            return result
        else:
            # Multiple components remain - create compound with failure tracking
            return self._create_union_fallback_compound(remaining_components, successful_unions, original_count)
    
    def _create_union_fallback_compound(self, remaining_components: List[Any], 
                                       successful_unions: int, original_count: int) -> Any:
        """
        Create a compound object for failed union with tracking metadata.
        
        Args:
            remaining_components: Components that couldn't be unioned
            successful_unions: Number of successful unions achieved
            original_count: Original number of components
            
        Returns:
            Compound object with failure metadata
        """
        failure_msg = f"GROUP UNION INCOMPLETE: {len(remaining_components)} separate objects remain"
        failure_msg += f" (Success rate: {successful_unions}/{original_count-1} attempted unions)"
        self.logger.warning(failure_msg)
        print(f"⚠️  UNION WARNING: {failure_msg}")
        
        try:
            from build123d import Compound
            result = Compound(children=remaining_components)
            result._union_failed = True
            result._separate_objects = remaining_components
            result._union_success_rate = successful_unions / max(1, original_count - 1)
            
            total_volume = sum(getattr(comp, 'volume', 0.0) for comp in remaining_components)
            self.logger.warning(f"Compound created with {len(remaining_components)} separate objects, total volume: {total_volume:.3f} m³")
            return result
        except Exception as e:
            self.logger.error(f"Failed to create compound: {e}")
            return remaining_components[0]  # Fallback to first component
    
    def _log_union_progress(self, union_attempts: int, remaining_components: List[Any]):
        """
        Log progress information for union operations.
        
        Args:
            union_attempts: Current attempt number
            remaining_components: Components remaining in this round
        """
        # Removed verbose union round log
        volumes = [getattr(comp, 'volume', 'unknown') for comp in remaining_components]
        # Removed verbose component volumes log
    
    def _union_component_cluster(self, components: List[Any], cluster_indices: List[int]) -> Any:
        """
        Union components in a cluster using build123d operations.
        
        Args:
            components: Full list of components
            cluster_indices: Indices of components to union
            
        Returns:
            Union result or individual component if cluster size is 1
        """
        if len(cluster_indices) == 1:
            # Return individual component for single-item clusters
            return components[cluster_indices[0]]
        
        # Extract components for this cluster
        cluster_components = [components[i] for i in cluster_indices]
        
        try:
            # Use existing union_components method with vectorized approach
            union_result = self.union_components(cluster_components)
            
            # Check if union actually succeeded or failed
            if hasattr(union_result, '_union_failed') and union_result._union_failed:
                self.logger.warning(f"Union cluster {cluster_indices}: Partial union failure - "
                                  f"{len(union_result._separate_objects)} objects remain separate")
            else:
                self.logger.debug(f"Successfully unioned cluster with {len(cluster_indices)} components")
            
            return union_result
            
        except Exception as e:
            error_msg = f"🚨 CLUSTER UNION FAILED for components {cluster_indices}: {type(e).__name__}: {str(e)}"
            self.logger.warning(error_msg)
            print(f"⚠️  CLUSTER WARNING: {error_msg}")
            # Fallback: return first component if union fails
            return cluster_components[0]
    
    def union_components(self, components: List[Any]) -> Any:
        """
        Union multiple components using build123d algebra mode.
        
        Based on Context7 MCP research:
        - Use '+' operator for fastest performance (not .fuse() method)
        - build123d union returns Solid objects for successful operations  
        - Vectorized operations are much faster than sequential
        
        Args:
            components: List of geometry components
            
        Returns:
            Unified geometry object (typically a Solid)
            
        Raises:
            GeometryError: If union operation fails
        """
        from datetime import datetime
        
        union_start = datetime.now()
        
        # Input validation
        if not components:
            raise GeometryError("No components provided for union")
        
        self.logger.info(f"🔧 Starting union operation with {len(components)} components...")
        self.logger.debug(f"Component types: {[type(c).__name__ for c in components[:5]]}{'...' if len(components) > 5 else ''}")
        
        # Handle single component case
        if not isinstance(components, list):
            if self._is_valid_geometry(components):
                return components
            else:
                raise GeometryError("Single component provided is not valid geometry")
        
        # 🔍 PART2 DEBUG: Log input volumes before extraction
        input_volumes = [getattr(comp, 'volume', 0.0) for comp in components]
        total_input_volume = sum(input_volumes)
        self.logger.info(f"🔍 UNION DEBUG: Input components total volume: {total_input_volume:.3f} m³")

        # Extract and validate components
        self.logger.info("⏱️  Extracting and validating components...")
        extract_start = datetime.now()
        valid_components = self._extract_valid_components_from_list(components)
        extract_duration = datetime.now() - extract_start
        self.logger.info(f"✅ Component validation completed in {extract_duration.total_seconds():.2f}s ({len(valid_components)} valid)")

        # 🔍 PART2 DEBUG: Log volumes after extraction
        valid_volumes = [getattr(comp, 'volume', 0.0) for comp in valid_components]
        total_valid_volume = sum(valid_volumes)
        self.logger.info(f"🔍 UNION DEBUG: Valid components total volume: {total_valid_volume:.3f} m³")

        if len(components) != len(valid_components):
            self.logger.warning(f"🔍 UNION DEBUG: Component count reduced during extraction! {len(components)} → {len(valid_components)}")
        if abs(total_input_volume - total_valid_volume) > 0.001:
            self.logger.warning(f"🔍 UNION DEBUG: Volume changed during extraction! {total_input_volume:.3f} → {total_valid_volume:.3f} m³")

        if not valid_components:
            raise GeometryError("No valid geometry components found")

        if len(valid_components) == 1:
            self.logger.info("ℹ️  Single component - returning directly (no union needed)")
            return valid_components[0]
        
        try:
            # ENHANCED: Use precision union operations for better spatial inclusion
            self.logger.info(f"⏱️  Performing ENHANCED PRECISION union on {len(valid_components)} components...")
            union_op_start = datetime.now()
            
            # Try enhanced precision union first for better results
            try:
                result = self.enhanced_operations.enhanced_union_with_validation(valid_components)
                union_method = "enhanced_precision"
                self.logger.info("✅ Enhanced precision union successful!")
            except Exception as e:
                self.logger.warning(f"Enhanced precision union failed: {e}")
                self.logger.info("🔄 Falling back to progressive union...")
                result = self._perform_progressive_union(valid_components)
                union_method = "progressive_fallback"
            
            union_op_duration = datetime.now() - union_op_start
            self.logger.info(f"✅ Union operation ({union_method}) completed in {union_op_duration.total_seconds():.2f}s")

            # 🔍 PART2 DEBUG: Log union result volume
            result_volume = getattr(result, 'volume', 0.0)
            self.logger.info(f"🔍 UNION DEBUG: Union result volume: {result_volume:.3f} m³")

            if total_valid_volume > 0.1 and result_volume < 0.001:
                self.logger.error(f"🔍 UNION DEBUG: CRITICAL - Union result volume eliminated! Input: {total_valid_volume:.3f} → Output: {result_volume:.3f} m³")
            elif abs(total_valid_volume - result_volume) > total_valid_volume * 0.2:
                self.logger.warning(f"🔍 UNION DEBUG: Significant volume change during union! Input: {total_valid_volume:.3f} → Output: {result_volume:.3f} m³")

            # Enhanced result analysis and logging
            self._log_union_result_analysis(result, len(valid_components))
            
            # Final validation
            validation_start = datetime.now()
            if not self._is_valid_geometry(result):
                raise GeometryError("Union operation produced invalid geometry")
            validation_duration = datetime.now() - validation_start
            
            total_duration = datetime.now() - union_start
            self.logger.info(f"🎉 Union operation successful in {total_duration.total_seconds():.2f}s: {len(valid_components)} components -> {type(result).__name__}")
            return result
            
        except Exception as e:
            error_duration = datetime.now() - union_start
            # Add more detailed error information
            error_details = f"Union operation failed after {error_duration.total_seconds():.2f}s: {e}"
            if hasattr(e, '__class__'):
                error_details += f" (Error type: {e.__class__.__name__})"
            self.logger.error(f"❌ Union failed with {len(valid_components)} components")
            self.logger.error(f"Component types: {[type(c).__name__ for c in valid_components]}")
            raise GeometryError(error_details) from e

    
    def union_components_with_validation(self, components: List[Any], 
                                       pile_attribution: Optional[Dict[str, str]] = None) -> Tuple[Any, Dict[str, Any]]:
        """
        Union components with comprehensive validation and debugging.
        
        Args:
            components: List of geometry components to union
            pile_attribution: Optional mapping of components to pile IDs for tracking
            
        Returns:
            Tuple of (union_result, validation_results)
        """
        self.logger.info(f"🔧 ENHANCED UNION WITH VALIDATION - Processing {len(components)} components...")
        
        # Use enhanced validation system
        pile_attribution = pile_attribution or {}
        
        # Perform validated union
        union_result, validation_results = self.enhanced_operations.perform_validated_union(
            components, pile_attribution
        )
        
        # Log validation summary
        if validation_results['overall_status'] != 'PASS':
            self.logger.warning("⚠️  UNION VALIDATION FAILED - Some components may be missing!")
            self._log_validation_details(validation_results)
        else:
            self.logger.info("✅ UNION VALIDATION PASSED - All components properly unioned")
            
        return union_result, validation_results
        
    def _log_validation_details(self, validation_results: Dict[str, Any]) -> None:
        """Log detailed validation results for debugging."""
        post_results = validation_results.get('post_union', {})
        
        if post_results.get('missing_components'):
            self.logger.warning("🚨 MISSING COMPONENTS DETECTED:")
            for missing in post_results['missing_components']:
                self.logger.warning(f"   • Component: {missing}")
                
        bbox_results = post_results.get('bounding_box_validation', {})
        if bbox_results.get('spatial_issues'):
            self.logger.warning("📦 SPATIAL CONTAINMENT ISSUES:")
            for issue in bbox_results['spatial_issues']:
                self.logger.warning(f"   • Component {issue['component']}: Volume {issue['volume']:.3f} m³, Pile: {issue['pile_id']}")
                
        volume_results = post_results.get('volume_validation', {})
        if volume_results.get('status') == 'FAIL':
            self.logger.error(f"📊 VOLUME CONSERVATION FAILURE: {volume_results['volume_ratio']:.1%} difference")
        
    def validate_existing_union(self, original_components: List[Any], 
                               union_result: Any,
                               pile_attribution: Optional[Dict[str, str]] = None) -> Dict[str, Any]:
        """
        Validate an existing union result against original components.
        
        Args:
            original_components: Original components that were unioned
            union_result: Result of the union operation
            pile_attribution: Optional component to pile mapping
            
        Returns:
            Dictionary with validation results
        """
        pile_attribution = pile_attribution or {}
        return self.validation_engine.validate_union_completeness(
            original_components, union_result, pile_attribution
        )

    def _log_union_result_analysis(self, result: Any, original_component_count: int) -> None:
        """
        Log detailed analysis of union operation results to explain multiple parts creation.
        
        Args:
            result: The union operation result 
            original_component_count: Number of original components that were unioned
        """
        result_type = type(result).__name__
        
        # Check if result is compound or contains multiple objects
        try:
            if hasattr(result, 'solids'):
                # Try to extract solids to see if there are multiple
                solids_result = result.solids()
                if hasattr(solids_result, '__iter__') and not isinstance(solids_result, str):
                    solid_count = len(list(solids_result))
                    if solid_count > 1:
                        self.logger.info(f"🔍 UNION RESULT ANALYSIS: {result_type} containing {solid_count} separate solids")
                        self.logger.info("📦 Multiple solids will create multiple 'Part3_Union_X' exports:")
                        self.logger.info(f"   • Started with {original_component_count} components")
                        self.logger.info(f"   • Progressive union created {solid_count} separate clusters")
                        self.logger.info("   • Each cluster becomes a distinct union part in the export")
                        self.logger.info("   • This occurs when pile geometries resist full topological merging")
                        # Enhanced spatial analysis for multiple union results
                        self._analyze_union_result_spatial_relationships(result, solid_count)
                        return
                        
            # Check for failed union markers
            if hasattr(result, '_union_failed') and result._union_failed:
                separate_count = len(getattr(result, '_separate_objects', []))
                self.logger.info(f"🔍 UNION RESULT ANALYSIS: Failed union with {separate_count} separate objects")
                self.logger.info("⚠️  Union operation could not merge geometries:")
                self.logger.info(f"   • Started with {original_component_count} components")
                self.logger.info(f"   • Failed to merge, preserved {separate_count} separate objects")
                self.logger.info("   • Each object will become a separate 'Part3_Union_X' export")
                return
                
            # Single successful union
            result_volume = getattr(result, 'volume', 0.0)
            self.logger.info(f"✅ UNION RESULT ANALYSIS: Single {result_type} (volume: {result_volume:.3f} m³)")
            self.logger.info(f"🎯 Successfully merged {original_component_count} components into unified geometry")
            
        except Exception as e:
            self.logger.debug(f"Could not analyze union result: {e}")
            self.logger.info(f"🔍 UNION RESULT: {result_type} (analysis skipped due to error)")

    def _analyze_union_result_spatial_relationships(self, result: Any, solid_count: int) -> None:
        """
        Analyze spatial relationships between multiple union results.
        This helps investigate why Part2 might contain Part3_2.
        """
        try:
            self.logger.info("📐 UNION SPATIAL ANALYSIS: Investigating multiple union parts...")
            
            # Extract individual solids from the result
            solids_result = result.solids()
            if hasattr(solids_result, '__iter__') and not isinstance(solids_result, str):
                solids_list = list(solids_result)
                
                # Analyze each solid's spatial properties
                for i, solid in enumerate(solids_list):
                    solid_num = i + 1
                    self.logger.info(f"🔍 Union Part {solid_num} Analysis:")
                    
                    try:
                        # Get spatial properties
                        bbox = solid.bounding_box()
                        volume = getattr(solid, 'volume', 0.0)
                        center = bbox.center()
                        size = bbox.size
                        
                        self.logger.info(f"   📦 Center: ({center.X:.3f}, {center.Y:.3f}, {center.Z:.3f})")
                        self.logger.info(f"   📦 Size: {size.X:.3f} x {size.Y:.3f} x {size.Z:.3f}")
                        self.logger.info(f"   📊 Volume: {volume:.6f} m³")
                        
                        # Check for potential containment by comparing sizes
                        if volume < 0.001:
                            self.logger.warning(f"   ⚠️  TINY VOLUME: Part {solid_num} has very small volume")
                        
                        # Analyze relative to other parts for potential containment
                        for j, other_solid in enumerate(solids_list):
                            if i != j:
                                other_num = j + 1
                                try:
                                    other_bbox = other_solid.bounding_box()
                                    other_center = other_bbox.center()
                                    other_size = other_bbox.size
                                    other_volume = getattr(other_solid, 'volume', 0.0)
                                    
                                    # Check if one might be contained in the other
                                    distance = ((center.X - other_center.X)**2 + 
                                              (center.Y - other_center.Y)**2 + 
                                              (center.Z - other_center.Z)**2)**0.5
                                    
                                    self.logger.info(f"   📏 Distance to Part {other_num}: {distance:.3f} m")
                                    
                                    # Check for potential containment scenarios
                                    if (size.X < other_size.X * 0.8 and 
                                        size.Y < other_size.Y * 0.8 and 
                                        distance < max(other_size.X, other_size.Y) * 0.5):
                                        self.logger.warning(f"   ⚠️  POTENTIAL CONTAINMENT: Part {solid_num} might be inside Part {other_num}")
                                        self.logger.warning(f"        Part {solid_num}: {size.X:.3f}x{size.Y:.3f}, Vol: {volume:.6f}")
                                        self.logger.warning(f"        Part {other_num}: {other_size.X:.3f}x{other_size.Y:.3f}, Vol: {other_volume:.6f}")
                                        self.logger.warning(f"        Distance: {distance:.3f} m")
                                    
                                except Exception:
                                    pass
                                    
                    except Exception as e:
                        self.logger.error(f"   ❌ Failed spatial analysis for Part {solid_num}: {e}")
                        
            self.logger.info("📐 UNION SPATIAL ANALYSIS: Complete")
            
        except Exception as e:
            self.logger.error(f"❌ Union spatial analysis failed: {e}")
