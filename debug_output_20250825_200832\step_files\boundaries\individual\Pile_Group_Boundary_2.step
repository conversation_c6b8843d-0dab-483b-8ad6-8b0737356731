ISO-10303-21;
HEADER;
FILE_DESCRIPTION(('Open CASCADE Model'),'2;1');
FILE_NAME('Pile_Group_Boundary_2','2025-08-25T20:08:38',('Author'),(
    'Open CASCADE'),'Open CASCADE STEP processor 7.8','build123d',
  'Unknown');
FILE_SCHEMA(('AUTOMOTIVE_DESIGN { 1 0 10303 214 1 1 1 1 }'));
ENDSEC;
DATA;
#1 = APPLICATION_PROTOCOL_DEFINITION('international standard',
  'automotive_design',2000,#2);
#2 = APPLICATION_CONTEXT(
  'core data for automotive mechanical design processes');
#3 = SHAPE_DEFINITION_REPRESENTATION(#4,#10);
#4 = PRODUCT_DEFINITION_SHAPE('','',#5);
#5 = PRODUCT_DEFINITION('design','',#6,#9);
#6 = PRODUCT_DEFINITION_FORMATION('','',#7);
#7 = PRODUCT('Pile_Group_Boundary_2','Pile_Group_Boundary_2','',(#8));
#8 = PRODUCT_CONTEXT('',#2,'mechanical');
#9 = PRODUCT_DEFINITION_CONTEXT('part definition',#2,'design');
#10 = ADVANCED_BREP_SHAPE_REPRESENTATION('',(#11,#15),#1281);
#11 = AXIS2_PLACEMENT_3D('',#12,#13,#14);
#12 = CARTESIAN_POINT('',(0.,0.,0.));
#13 = DIRECTION('',(0.,0.,1.));
#14 = DIRECTION('',(1.,0.,-0.));
#15 = MANIFOLD_SOLID_BREP('',#16);
#16 = CLOSED_SHELL('',(#17,#137,#213,#289,#365,#441,#517,#593,#669,#745,
    #821,#897,#973,#1049,#1125,#1196,#1243,#1262));
#17 = ADVANCED_FACE('',(#18),#32,.F.);
#18 = FACE_BOUND('',#19,.F.);
#19 = EDGE_LOOP('',(#20,#55,#83,#111));
#20 = ORIENTED_EDGE('',*,*,#21,.T.);
#21 = EDGE_CURVE('',#22,#24,#26,.T.);
#22 = VERTEX_POINT('',#23);
#23 = CARTESIAN_POINT('',(85.917422,126.075483,42.5));
#24 = VERTEX_POINT('',#25);
#25 = CARTESIAN_POINT('',(85.917422,126.075483,-4.866559));
#26 = SURFACE_CURVE('',#27,(#31,#43),.PCURVE_S1.);
#27 = LINE('',#28,#29);
#28 = CARTESIAN_POINT('',(85.917422,126.075483,42.5));
#29 = VECTOR('',#30,1.);
#30 = DIRECTION('',(0.,0.,-1.));
#31 = PCURVE('',#32,#37);
#32 = PLANE('',#33);
#33 = AXIS2_PLACEMENT_3D('',#34,#35,#36);
#34 = CARTESIAN_POINT('',(85.917422,126.075483,42.5));
#35 = DIRECTION('',(0.,-1.,0.));
#36 = DIRECTION('',(1.,0.,0.));
#37 = DEFINITIONAL_REPRESENTATION('',(#38),#42);
#38 = LINE('',#39,#40);
#39 = CARTESIAN_POINT('',(0.,0.));
#40 = VECTOR('',#41,1.);
#41 = DIRECTION('',(0.,-1.));
#42 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#43 = PCURVE('',#44,#49);
#44 = PLANE('',#45);
#45 = AXIS2_PLACEMENT_3D('',#46,#47,#48);
#46 = CARTESIAN_POINT('',(85.917422,122.856505,42.5));
#47 = DIRECTION('',(1.,-0.,0.));
#48 = DIRECTION('',(0.,1.,0.));
#49 = DEFINITIONAL_REPRESENTATION('',(#50),#54);
#50 = LINE('',#51,#52);
#51 = CARTESIAN_POINT('',(3.218978,0.));
#52 = VECTOR('',#53,1.);
#53 = DIRECTION('',(0.,-1.));
#54 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#55 = ORIENTED_EDGE('',*,*,#56,.T.);
#56 = EDGE_CURVE('',#24,#57,#59,.T.);
#57 = VERTEX_POINT('',#58);
#58 = CARTESIAN_POINT('',(112.160196,126.075483,-4.866559));
#59 = SURFACE_CURVE('',#60,(#64,#71),.PCURVE_S1.);
#60 = LINE('',#61,#62);
#61 = CARTESIAN_POINT('',(85.917422,126.075483,-4.866559));
#62 = VECTOR('',#63,1.);
#63 = DIRECTION('',(1.,0.,0.));
#64 = PCURVE('',#32,#65);
#65 = DEFINITIONAL_REPRESENTATION('',(#66),#70);
#66 = LINE('',#67,#68);
#67 = CARTESIAN_POINT('',(0.,-47.366559));
#68 = VECTOR('',#69,1.);
#69 = DIRECTION('',(1.,0.));
#70 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#71 = PCURVE('',#72,#77);
#72 = PLANE('',#73);
#73 = AXIS2_PLACEMENT_3D('',#74,#75,#76);
#74 = CARTESIAN_POINT('',(86.921861882064,112.03332182876,-4.866559));
#75 = DIRECTION('',(0.,0.,-1.));
#76 = DIRECTION('',(-1.,0.,0.));
#77 = DEFINITIONAL_REPRESENTATION('',(#78),#82);
#78 = LINE('',#79,#80);
#79 = CARTESIAN_POINT('',(1.004439882064,14.042161171239));
#80 = VECTOR('',#81,1.);
#81 = DIRECTION('',(-1.,0.));
#82 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#83 = ORIENTED_EDGE('',*,*,#84,.F.);
#84 = EDGE_CURVE('',#85,#57,#87,.T.);
#85 = VERTEX_POINT('',#86);
#86 = CARTESIAN_POINT('',(112.160196,126.075483,42.5));
#87 = SURFACE_CURVE('',#88,(#92,#99),.PCURVE_S1.);
#88 = LINE('',#89,#90);
#89 = CARTESIAN_POINT('',(112.160196,126.075483,42.5));
#90 = VECTOR('',#91,1.);
#91 = DIRECTION('',(0.,0.,-1.));
#92 = PCURVE('',#32,#93);
#93 = DEFINITIONAL_REPRESENTATION('',(#94),#98);
#94 = LINE('',#95,#96);
#95 = CARTESIAN_POINT('',(26.242774,0.));
#96 = VECTOR('',#97,1.);
#97 = DIRECTION('',(0.,-1.));
#98 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#99 = PCURVE('',#100,#105);
#100 = PLANE('',#101);
#101 = AXIS2_PLACEMENT_3D('',#102,#103,#104);
#102 = CARTESIAN_POINT('',(112.160196,126.075483,42.5));
#103 = DIRECTION('',(-0.999997968291,-2.015791161217E-03,-0.));
#104 = DIRECTION('',(2.015791161217E-03,-0.999997968291,0.));
#105 = DEFINITIONAL_REPRESENTATION('',(#106),#110);
#106 = LINE('',#107,#108);
#107 = CARTESIAN_POINT('',(0.,0.));
#108 = VECTOR('',#109,1.);
#109 = DIRECTION('',(0.,-1.));
#110 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#111 = ORIENTED_EDGE('',*,*,#112,.F.);
#112 = EDGE_CURVE('',#22,#85,#113,.T.);
#113 = SURFACE_CURVE('',#114,(#118,#125),.PCURVE_S1.);
#114 = LINE('',#115,#116);
#115 = CARTESIAN_POINT('',(85.917422,126.075483,42.5));
#116 = VECTOR('',#117,1.);
#117 = DIRECTION('',(1.,0.,0.));
#118 = PCURVE('',#32,#119);
#119 = DEFINITIONAL_REPRESENTATION('',(#120),#124);
#120 = LINE('',#121,#122);
#121 = CARTESIAN_POINT('',(0.,0.));
#122 = VECTOR('',#123,1.);
#123 = DIRECTION('',(1.,0.));
#124 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#125 = PCURVE('',#126,#131);
#126 = PLANE('',#127);
#127 = AXIS2_PLACEMENT_3D('',#128,#129,#130);
#128 = CARTESIAN_POINT('',(86.921861882064,112.03332182876,42.5));
#129 = DIRECTION('',(0.,0.,-1.));
#130 = DIRECTION('',(-1.,0.,0.));
#131 = DEFINITIONAL_REPRESENTATION('',(#132),#136);
#132 = LINE('',#133,#134);
#133 = CARTESIAN_POINT('',(1.004439882064,14.042161171239));
#134 = VECTOR('',#135,1.);
#135 = DIRECTION('',(-1.,0.));
#136 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#137 = ADVANCED_FACE('',(#138),#100,.F.);
#138 = FACE_BOUND('',#139,.F.);
#139 = EDGE_LOOP('',(#140,#141,#164,#192));
#140 = ORIENTED_EDGE('',*,*,#84,.T.);
#141 = ORIENTED_EDGE('',*,*,#142,.T.);
#142 = EDGE_CURVE('',#57,#143,#145,.T.);
#143 = VERTEX_POINT('',#144);
#144 = CARTESIAN_POINT('',(112.233326,89.796997,-4.866559));
#145 = SURFACE_CURVE('',#146,(#150,#157),.PCURVE_S1.);
#146 = LINE('',#147,#148);
#147 = CARTESIAN_POINT('',(112.160196,126.075483,-4.866559));
#148 = VECTOR('',#149,1.);
#149 = DIRECTION('',(2.015791161217E-03,-0.999997968291,0.));
#150 = PCURVE('',#100,#151);
#151 = DEFINITIONAL_REPRESENTATION('',(#152),#156);
#152 = LINE('',#153,#154);
#153 = CARTESIAN_POINT('',(0.,-47.366559));
#154 = VECTOR('',#155,1.);
#155 = DIRECTION('',(1.,0.));
#156 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#157 = PCURVE('',#72,#158);
#158 = DEFINITIONAL_REPRESENTATION('',(#159),#163);
#159 = LINE('',#160,#161);
#160 = CARTESIAN_POINT('',(-25.23833411793,14.042161171239));
#161 = VECTOR('',#162,1.);
#162 = DIRECTION('',(-2.015791161217E-03,-0.999997968291));
#163 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#164 = ORIENTED_EDGE('',*,*,#165,.F.);
#165 = EDGE_CURVE('',#166,#143,#168,.T.);
#166 = VERTEX_POINT('',#167);
#167 = CARTESIAN_POINT('',(112.233326,89.796997,42.5));
#168 = SURFACE_CURVE('',#169,(#173,#180),.PCURVE_S1.);
#169 = LINE('',#170,#171);
#170 = CARTESIAN_POINT('',(112.233326,89.796997,42.5));
#171 = VECTOR('',#172,1.);
#172 = DIRECTION('',(0.,0.,-1.));
#173 = PCURVE('',#100,#174);
#174 = DEFINITIONAL_REPRESENTATION('',(#175),#179);
#175 = LINE('',#176,#177);
#176 = CARTESIAN_POINT('',(36.278559707479,0.));
#177 = VECTOR('',#178,1.);
#178 = DIRECTION('',(0.,-1.));
#179 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#180 = PCURVE('',#181,#186);
#181 = PLANE('',#182);
#182 = AXIS2_PLACEMENT_3D('',#183,#184,#185);
#183 = CARTESIAN_POINT('',(112.233326,89.796997,42.5));
#184 = DIRECTION('',(-5.882771802765E-03,0.999982696348,0.));
#185 = DIRECTION('',(-0.999982696348,-5.882771802765E-03,0.));
#186 = DEFINITIONAL_REPRESENTATION('',(#187),#191);
#187 = LINE('',#188,#189);
#188 = CARTESIAN_POINT('',(0.,0.));
#189 = VECTOR('',#190,1.);
#190 = DIRECTION('',(-0.,-1.));
#191 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#192 = ORIENTED_EDGE('',*,*,#193,.F.);
#193 = EDGE_CURVE('',#85,#166,#194,.T.);
#194 = SURFACE_CURVE('',#195,(#199,#206),.PCURVE_S1.);
#195 = LINE('',#196,#197);
#196 = CARTESIAN_POINT('',(112.160196,126.075483,42.5));
#197 = VECTOR('',#198,1.);
#198 = DIRECTION('',(2.015791161217E-03,-0.999997968291,0.));
#199 = PCURVE('',#100,#200);
#200 = DEFINITIONAL_REPRESENTATION('',(#201),#205);
#201 = LINE('',#202,#203);
#202 = CARTESIAN_POINT('',(0.,0.));
#203 = VECTOR('',#204,1.);
#204 = DIRECTION('',(1.,0.));
#205 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#206 = PCURVE('',#126,#207);
#207 = DEFINITIONAL_REPRESENTATION('',(#208),#212);
#208 = LINE('',#209,#210);
#209 = CARTESIAN_POINT('',(-25.23833411793,14.042161171239));
#210 = VECTOR('',#211,1.);
#211 = DIRECTION('',(-2.015791161217E-03,-0.999997968291));
#212 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#213 = ADVANCED_FACE('',(#214),#181,.F.);
#214 = FACE_BOUND('',#215,.F.);
#215 = EDGE_LOOP('',(#216,#217,#240,#268));
#216 = ORIENTED_EDGE('',*,*,#165,.T.);
#217 = ORIENTED_EDGE('',*,*,#218,.T.);
#218 = EDGE_CURVE('',#143,#219,#221,.T.);
#219 = VERTEX_POINT('',#220);
#220 = CARTESIAN_POINT('',(101.285615,89.732593,-4.866559));
#221 = SURFACE_CURVE('',#222,(#226,#233),.PCURVE_S1.);
#222 = LINE('',#223,#224);
#223 = CARTESIAN_POINT('',(112.233326,89.796997,-4.866559));
#224 = VECTOR('',#225,1.);
#225 = DIRECTION('',(-0.999982696348,-5.882771802765E-03,0.));
#226 = PCURVE('',#181,#227);
#227 = DEFINITIONAL_REPRESENTATION('',(#228),#232);
#228 = LINE('',#229,#230);
#229 = CARTESIAN_POINT('',(-1.421060881552E-14,-47.366559));
#230 = VECTOR('',#231,1.);
#231 = DIRECTION('',(1.,0.));
#232 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#233 = PCURVE('',#72,#234);
#234 = DEFINITIONAL_REPRESENTATION('',(#235),#239);
#235 = LINE('',#236,#237);
#236 = CARTESIAN_POINT('',(-25.31146411793,-22.23632482876));
#237 = VECTOR('',#238,1.);
#238 = DIRECTION('',(0.999982696348,-5.882771802765E-03));
#239 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#240 = ORIENTED_EDGE('',*,*,#241,.F.);
#241 = EDGE_CURVE('',#242,#219,#244,.T.);
#242 = VERTEX_POINT('',#243);
#243 = CARTESIAN_POINT('',(101.285615,89.732593,42.5));
#244 = SURFACE_CURVE('',#245,(#249,#256),.PCURVE_S1.);
#245 = LINE('',#246,#247);
#246 = CARTESIAN_POINT('',(101.285615,89.732593,42.5));
#247 = VECTOR('',#248,1.);
#248 = DIRECTION('',(0.,0.,-1.));
#249 = PCURVE('',#181,#250);
#250 = DEFINITIONAL_REPRESENTATION('',(#251),#255);
#251 = LINE('',#252,#253);
#252 = CARTESIAN_POINT('',(10.947900438657,0.));
#253 = VECTOR('',#254,1.);
#254 = DIRECTION('',(-0.,-1.));
#255 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#256 = PCURVE('',#257,#262);
#257 = PLANE('',#258);
#258 = AXIS2_PLACEMENT_3D('',#259,#260,#261);
#259 = CARTESIAN_POINT('',(101.285615,89.732593,42.5));
#260 = DIRECTION('',(0.177817912754,0.984063407461,0.));
#261 = DIRECTION('',(-0.984063407461,0.177817912754,0.));
#262 = DEFINITIONAL_REPRESENTATION('',(#263),#267);
#263 = LINE('',#264,#265);
#264 = CARTESIAN_POINT('',(0.,0.));
#265 = VECTOR('',#266,1.);
#266 = DIRECTION('',(0.,-1.));
#267 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#268 = ORIENTED_EDGE('',*,*,#269,.F.);
#269 = EDGE_CURVE('',#166,#242,#270,.T.);
#270 = SURFACE_CURVE('',#271,(#275,#282),.PCURVE_S1.);
#271 = LINE('',#272,#273);
#272 = CARTESIAN_POINT('',(112.233326,89.796997,42.5));
#273 = VECTOR('',#274,1.);
#274 = DIRECTION('',(-0.999982696348,-5.882771802765E-03,0.));
#275 = PCURVE('',#181,#276);
#276 = DEFINITIONAL_REPRESENTATION('',(#277),#281);
#277 = LINE('',#278,#279);
#278 = CARTESIAN_POINT('',(-1.421060881552E-14,0.));
#279 = VECTOR('',#280,1.);
#280 = DIRECTION('',(1.,0.));
#281 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#282 = PCURVE('',#126,#283);
#283 = DEFINITIONAL_REPRESENTATION('',(#284),#288);
#284 = LINE('',#285,#286);
#285 = CARTESIAN_POINT('',(-25.31146411793,-22.23632482876));
#286 = VECTOR('',#287,1.);
#287 = DIRECTION('',(0.999982696348,-5.882771802765E-03));
#288 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#289 = ADVANCED_FACE('',(#290),#257,.F.);
#290 = FACE_BOUND('',#291,.F.);
#291 = EDGE_LOOP('',(#292,#293,#316,#344));
#292 = ORIENTED_EDGE('',*,*,#241,.T.);
#293 = ORIENTED_EDGE('',*,*,#294,.T.);
#294 = EDGE_CURVE('',#219,#295,#297,.T.);
#295 = VERTEX_POINT('',#296);
#296 = CARTESIAN_POINT('',(90.352654,91.708153,-4.866559));
#297 = SURFACE_CURVE('',#298,(#302,#309),.PCURVE_S1.);
#298 = LINE('',#299,#300);
#299 = CARTESIAN_POINT('',(101.285615,89.732593,-4.866559));
#300 = VECTOR('',#301,1.);
#301 = DIRECTION('',(-0.984063407461,0.177817912754,0.));
#302 = PCURVE('',#257,#303);
#303 = DEFINITIONAL_REPRESENTATION('',(#304),#308);
#304 = LINE('',#305,#306);
#305 = CARTESIAN_POINT('',(1.398438211397E-14,-47.366559));
#306 = VECTOR('',#307,1.);
#307 = DIRECTION('',(1.,0.));
#308 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#309 = PCURVE('',#72,#310);
#310 = DEFINITIONAL_REPRESENTATION('',(#311),#315);
#311 = LINE('',#312,#313);
#312 = CARTESIAN_POINT('',(-14.36375311793,-22.30072882876));
#313 = VECTOR('',#314,1.);
#314 = DIRECTION('',(0.984063407461,0.177817912754));
#315 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#316 = ORIENTED_EDGE('',*,*,#317,.F.);
#317 = EDGE_CURVE('',#318,#295,#320,.T.);
#318 = VERTEX_POINT('',#319);
#319 = CARTESIAN_POINT('',(90.352654,91.708153,42.5));
#320 = SURFACE_CURVE('',#321,(#325,#332),.PCURVE_S1.);
#321 = LINE('',#322,#323);
#322 = CARTESIAN_POINT('',(90.352654,91.708153,42.5));
#323 = VECTOR('',#324,1.);
#324 = DIRECTION('',(0.,0.,-1.));
#325 = PCURVE('',#257,#326);
#326 = DEFINITIONAL_REPRESENTATION('',(#327),#331);
#327 = LINE('',#328,#329);
#328 = CARTESIAN_POINT('',(11.110016811019,0.));
#329 = VECTOR('',#330,1.);
#330 = DIRECTION('',(0.,-1.));
#331 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#332 = PCURVE('',#333,#338);
#333 = PLANE('',#334);
#334 = AXIS2_PLACEMENT_3D('',#335,#336,#337);
#335 = CARTESIAN_POINT('',(90.352654,91.708153,42.5));
#336 = DIRECTION('',(0.918584192038,0.395225356142,0.));
#337 = DIRECTION('',(-0.395225356142,0.918584192038,0.));
#338 = DEFINITIONAL_REPRESENTATION('',(#339),#343);
#339 = LINE('',#340,#341);
#340 = CARTESIAN_POINT('',(0.,0.));
#341 = VECTOR('',#342,1.);
#342 = DIRECTION('',(0.,-1.));
#343 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#344 = ORIENTED_EDGE('',*,*,#345,.F.);
#345 = EDGE_CURVE('',#242,#318,#346,.T.);
#346 = SURFACE_CURVE('',#347,(#351,#358),.PCURVE_S1.);
#347 = LINE('',#348,#349);
#348 = CARTESIAN_POINT('',(101.285615,89.732593,42.5));
#349 = VECTOR('',#350,1.);
#350 = DIRECTION('',(-0.984063407461,0.177817912754,0.));
#351 = PCURVE('',#257,#352);
#352 = DEFINITIONAL_REPRESENTATION('',(#353),#357);
#353 = LINE('',#354,#355);
#354 = CARTESIAN_POINT('',(1.398438211397E-14,0.));
#355 = VECTOR('',#356,1.);
#356 = DIRECTION('',(1.,0.));
#357 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#358 = PCURVE('',#126,#359);
#359 = DEFINITIONAL_REPRESENTATION('',(#360),#364);
#360 = LINE('',#361,#362);
#361 = CARTESIAN_POINT('',(-14.36375311793,-22.30072882876));
#362 = VECTOR('',#363,1.);
#363 = DIRECTION('',(0.984063407461,0.177817912754));
#364 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#365 = ADVANCED_FACE('',(#366),#333,.F.);
#366 = FACE_BOUND('',#367,.F.);
#367 = EDGE_LOOP('',(#368,#369,#392,#420));
#368 = ORIENTED_EDGE('',*,*,#317,.T.);
#369 = ORIENTED_EDGE('',*,*,#370,.T.);
#370 = EDGE_CURVE('',#295,#371,#373,.T.);
#371 = VERTEX_POINT('',#372);
#372 = CARTESIAN_POINT('',(89.283511,94.193059,-4.866559));
#373 = SURFACE_CURVE('',#374,(#378,#385),.PCURVE_S1.);
#374 = LINE('',#375,#376);
#375 = CARTESIAN_POINT('',(90.352654,91.708153,-4.866559));
#376 = VECTOR('',#377,1.);
#377 = DIRECTION('',(-0.395225356142,0.918584192038,0.));
#378 = PCURVE('',#333,#379);
#379 = DEFINITIONAL_REPRESENTATION('',(#380),#384);
#380 = LINE('',#381,#382);
#381 = CARTESIAN_POINT('',(0.,-47.366559));
#382 = VECTOR('',#383,1.);
#383 = DIRECTION('',(1.,0.));
#384 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#385 = PCURVE('',#72,#386);
#386 = DEFINITIONAL_REPRESENTATION('',(#387),#391);
#387 = LINE('',#388,#389);
#388 = CARTESIAN_POINT('',(-3.430792117936,-20.32516882876));
#389 = VECTOR('',#390,1.);
#390 = DIRECTION('',(0.395225356142,0.918584192038));
#391 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#392 = ORIENTED_EDGE('',*,*,#393,.F.);
#393 = EDGE_CURVE('',#394,#371,#396,.T.);
#394 = VERTEX_POINT('',#395);
#395 = CARTESIAN_POINT('',(89.283511,94.193059,42.5));
#396 = SURFACE_CURVE('',#397,(#401,#408),.PCURVE_S1.);
#397 = LINE('',#398,#399);
#398 = CARTESIAN_POINT('',(89.283511,94.193059,42.5));
#399 = VECTOR('',#400,1.);
#400 = DIRECTION('',(0.,0.,-1.));
#401 = PCURVE('',#333,#402);
#402 = DEFINITIONAL_REPRESENTATION('',(#403),#407);
#403 = LINE('',#404,#405);
#404 = CARTESIAN_POINT('',(2.705147793243,0.));
#405 = VECTOR('',#406,1.);
#406 = DIRECTION('',(0.,-1.));
#407 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#408 = PCURVE('',#409,#414);
#409 = PLANE('',#410);
#410 = AXIS2_PLACEMENT_3D('',#411,#412,#413);
#411 = CARTESIAN_POINT('',(89.283511,94.193059,42.5));
#412 = DIRECTION('',(0.999970353247,-7.700170607099E-03,0.));
#413 = DIRECTION('',(7.700170607099E-03,0.999970353247,0.));
#414 = DEFINITIONAL_REPRESENTATION('',(#415),#419);
#415 = LINE('',#416,#417);
#416 = CARTESIAN_POINT('',(0.,0.));
#417 = VECTOR('',#418,1.);
#418 = DIRECTION('',(0.,-1.));
#419 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#420 = ORIENTED_EDGE('',*,*,#421,.F.);
#421 = EDGE_CURVE('',#318,#394,#422,.T.);
#422 = SURFACE_CURVE('',#423,(#427,#434),.PCURVE_S1.);
#423 = LINE('',#424,#425);
#424 = CARTESIAN_POINT('',(90.352654,91.708153,42.5));
#425 = VECTOR('',#426,1.);
#426 = DIRECTION('',(-0.395225356142,0.918584192038,0.));
#427 = PCURVE('',#333,#428);
#428 = DEFINITIONAL_REPRESENTATION('',(#429),#433);
#429 = LINE('',#430,#431);
#430 = CARTESIAN_POINT('',(0.,0.));
#431 = VECTOR('',#432,1.);
#432 = DIRECTION('',(1.,0.));
#433 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#434 = PCURVE('',#126,#435);
#435 = DEFINITIONAL_REPRESENTATION('',(#436),#440);
#436 = LINE('',#437,#438);
#437 = CARTESIAN_POINT('',(-3.430792117936,-20.32516882876));
#438 = VECTOR('',#439,1.);
#439 = DIRECTION('',(0.395225356142,0.918584192038));
#440 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#441 = ADVANCED_FACE('',(#442),#409,.F.);
#442 = FACE_BOUND('',#443,.F.);
#443 = EDGE_LOOP('',(#444,#445,#468,#496));
#444 = ORIENTED_EDGE('',*,*,#393,.T.);
#445 = ORIENTED_EDGE('',*,*,#446,.T.);
#446 = EDGE_CURVE('',#371,#447,#449,.T.);
#447 = VERTEX_POINT('',#448);
#448 = CARTESIAN_POINT('',(89.317494,98.606207,-4.866559));
#449 = SURFACE_CURVE('',#450,(#454,#461),.PCURVE_S1.);
#450 = LINE('',#451,#452);
#451 = CARTESIAN_POINT('',(89.283511,94.193059,-4.866559));
#452 = VECTOR('',#453,1.);
#453 = DIRECTION('',(7.700170607099E-03,0.999970353247,0.));
#454 = PCURVE('',#409,#455);
#455 = DEFINITIONAL_REPRESENTATION('',(#456),#460);
#456 = LINE('',#457,#458);
#457 = CARTESIAN_POINT('',(1.42104334095E-14,-47.366559));
#458 = VECTOR('',#459,1.);
#459 = DIRECTION('',(1.,0.));
#460 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#461 = PCURVE('',#72,#462);
#462 = DEFINITIONAL_REPRESENTATION('',(#463),#467);
#463 = LINE('',#464,#465);
#464 = CARTESIAN_POINT('',(-2.361649117936,-17.84026282876));
#465 = VECTOR('',#466,1.);
#466 = DIRECTION('',(-7.700170607099E-03,0.999970353247));
#467 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#468 = ORIENTED_EDGE('',*,*,#469,.F.);
#469 = EDGE_CURVE('',#470,#447,#472,.T.);
#470 = VERTEX_POINT('',#471);
#471 = CARTESIAN_POINT('',(89.317494,98.606207,42.5));
#472 = SURFACE_CURVE('',#473,(#477,#484),.PCURVE_S1.);
#473 = LINE('',#474,#475);
#474 = CARTESIAN_POINT('',(89.317494,98.606207,42.5));
#475 = VECTOR('',#476,1.);
#476 = DIRECTION('',(0.,0.,-1.));
#477 = PCURVE('',#409,#478);
#478 = DEFINITIONAL_REPRESENTATION('',(#479),#483);
#479 = LINE('',#480,#481);
#480 = CARTESIAN_POINT('',(4.413278839388,0.));
#481 = VECTOR('',#482,1.);
#482 = DIRECTION('',(0.,-1.));
#483 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#484 = PCURVE('',#485,#490);
#485 = PLANE('',#486);
#486 = AXIS2_PLACEMENT_3D('',#487,#488,#489);
#487 = CARTESIAN_POINT('',(89.317494,98.606207,42.5));
#488 = DIRECTION('',(1.,-0.,0.));
#489 = DIRECTION('',(0.,1.,0.));
#490 = DEFINITIONAL_REPRESENTATION('',(#491),#495);
#491 = LINE('',#492,#493);
#492 = CARTESIAN_POINT('',(0.,0.));
#493 = VECTOR('',#494,1.);
#494 = DIRECTION('',(0.,-1.));
#495 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#496 = ORIENTED_EDGE('',*,*,#497,.F.);
#497 = EDGE_CURVE('',#394,#470,#498,.T.);
#498 = SURFACE_CURVE('',#499,(#503,#510),.PCURVE_S1.);
#499 = LINE('',#500,#501);
#500 = CARTESIAN_POINT('',(89.283511,94.193059,42.5));
#501 = VECTOR('',#502,1.);
#502 = DIRECTION('',(7.700170607099E-03,0.999970353247,0.));
#503 = PCURVE('',#409,#504);
#504 = DEFINITIONAL_REPRESENTATION('',(#505),#509);
#505 = LINE('',#506,#507);
#506 = CARTESIAN_POINT('',(1.42104334095E-14,0.));
#507 = VECTOR('',#508,1.);
#508 = DIRECTION('',(1.,0.));
#509 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#510 = PCURVE('',#126,#511);
#511 = DEFINITIONAL_REPRESENTATION('',(#512),#516);
#512 = LINE('',#513,#514);
#513 = CARTESIAN_POINT('',(-2.361649117936,-17.84026282876));
#514 = VECTOR('',#515,1.);
#515 = DIRECTION('',(-7.700170607099E-03,0.999970353247));
#516 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#517 = ADVANCED_FACE('',(#518),#485,.F.);
#518 = FACE_BOUND('',#519,.F.);
#519 = EDGE_LOOP('',(#520,#521,#544,#572));
#520 = ORIENTED_EDGE('',*,*,#469,.T.);
#521 = ORIENTED_EDGE('',*,*,#522,.T.);
#522 = EDGE_CURVE('',#447,#523,#525,.T.);
#523 = VERTEX_POINT('',#524);
#524 = CARTESIAN_POINT('',(89.317494,112.30648,-4.866559));
#525 = SURFACE_CURVE('',#526,(#530,#537),.PCURVE_S1.);
#526 = LINE('',#527,#528);
#527 = CARTESIAN_POINT('',(89.317494,98.606207,-4.866559));
#528 = VECTOR('',#529,1.);
#529 = DIRECTION('',(0.,1.,0.));
#530 = PCURVE('',#485,#531);
#531 = DEFINITIONAL_REPRESENTATION('',(#532),#536);
#532 = LINE('',#533,#534);
#533 = CARTESIAN_POINT('',(0.,-47.366559));
#534 = VECTOR('',#535,1.);
#535 = DIRECTION('',(1.,0.));
#536 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#537 = PCURVE('',#72,#538);
#538 = DEFINITIONAL_REPRESENTATION('',(#539),#543);
#539 = LINE('',#540,#541);
#540 = CARTESIAN_POINT('',(-2.395632117936,-13.42711482876));
#541 = VECTOR('',#542,1.);
#542 = DIRECTION('',(0.,1.));
#543 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#544 = ORIENTED_EDGE('',*,*,#545,.F.);
#545 = EDGE_CURVE('',#546,#523,#548,.T.);
#546 = VERTEX_POINT('',#547);
#547 = CARTESIAN_POINT('',(89.317494,112.30648,42.5));
#548 = SURFACE_CURVE('',#549,(#553,#560),.PCURVE_S1.);
#549 = LINE('',#550,#551);
#550 = CARTESIAN_POINT('',(89.317494,112.30648,42.5));
#551 = VECTOR('',#552,1.);
#552 = DIRECTION('',(0.,0.,-1.));
#553 = PCURVE('',#485,#554);
#554 = DEFINITIONAL_REPRESENTATION('',(#555),#559);
#555 = LINE('',#556,#557);
#556 = CARTESIAN_POINT('',(13.700273,0.));
#557 = VECTOR('',#558,1.);
#558 = DIRECTION('',(0.,-1.));
#559 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#560 = PCURVE('',#561,#566);
#561 = PLANE('',#562);
#562 = AXIS2_PLACEMENT_3D('',#563,#564,#565);
#563 = CARTESIAN_POINT('',(89.317494,112.30648,42.5));
#564 = DIRECTION('',(0.,1.,0.));
#565 = DIRECTION('',(-1.,0.,0.));
#566 = DEFINITIONAL_REPRESENTATION('',(#567),#571);
#567 = LINE('',#568,#569);
#568 = CARTESIAN_POINT('',(0.,0.));
#569 = VECTOR('',#570,1.);
#570 = DIRECTION('',(0.,-1.));
#571 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#572 = ORIENTED_EDGE('',*,*,#573,.F.);
#573 = EDGE_CURVE('',#470,#546,#574,.T.);
#574 = SURFACE_CURVE('',#575,(#579,#586),.PCURVE_S1.);
#575 = LINE('',#576,#577);
#576 = CARTESIAN_POINT('',(89.317494,98.606207,42.5));
#577 = VECTOR('',#578,1.);
#578 = DIRECTION('',(0.,1.,0.));
#579 = PCURVE('',#485,#580);
#580 = DEFINITIONAL_REPRESENTATION('',(#581),#585);
#581 = LINE('',#582,#583);
#582 = CARTESIAN_POINT('',(0.,0.));
#583 = VECTOR('',#584,1.);
#584 = DIRECTION('',(1.,0.));
#585 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#586 = PCURVE('',#126,#587);
#587 = DEFINITIONAL_REPRESENTATION('',(#588),#592);
#588 = LINE('',#589,#590);
#589 = CARTESIAN_POINT('',(-2.395632117936,-13.42711482876));
#590 = VECTOR('',#591,1.);
#591 = DIRECTION('',(0.,1.));
#592 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#593 = ADVANCED_FACE('',(#594),#561,.F.);
#594 = FACE_BOUND('',#595,.F.);
#595 = EDGE_LOOP('',(#596,#597,#620,#648));
#596 = ORIENTED_EDGE('',*,*,#545,.T.);
#597 = ORIENTED_EDGE('',*,*,#598,.T.);
#598 = EDGE_CURVE('',#523,#599,#601,.T.);
#599 = VERTEX_POINT('',#600);
#600 = CARTESIAN_POINT('',(72.917494,112.30648,-4.866559));
#601 = SURFACE_CURVE('',#602,(#606,#613),.PCURVE_S1.);
#602 = LINE('',#603,#604);
#603 = CARTESIAN_POINT('',(89.317494,112.30648,-4.866559));
#604 = VECTOR('',#605,1.);
#605 = DIRECTION('',(-1.,0.,0.));
#606 = PCURVE('',#561,#607);
#607 = DEFINITIONAL_REPRESENTATION('',(#608),#612);
#608 = LINE('',#609,#610);
#609 = CARTESIAN_POINT('',(0.,-47.366559));
#610 = VECTOR('',#611,1.);
#611 = DIRECTION('',(1.,0.));
#612 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#613 = PCURVE('',#72,#614);
#614 = DEFINITIONAL_REPRESENTATION('',(#615),#619);
#615 = LINE('',#616,#617);
#616 = CARTESIAN_POINT('',(-2.395632117936,0.273158171239));
#617 = VECTOR('',#618,1.);
#618 = DIRECTION('',(1.,0.));
#619 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#620 = ORIENTED_EDGE('',*,*,#621,.F.);
#621 = EDGE_CURVE('',#622,#599,#624,.T.);
#622 = VERTEX_POINT('',#623);
#623 = CARTESIAN_POINT('',(72.917494,112.30648,42.5));
#624 = SURFACE_CURVE('',#625,(#629,#636),.PCURVE_S1.);
#625 = LINE('',#626,#627);
#626 = CARTESIAN_POINT('',(72.917494,112.30648,42.5));
#627 = VECTOR('',#628,1.);
#628 = DIRECTION('',(0.,0.,-1.));
#629 = PCURVE('',#561,#630);
#630 = DEFINITIONAL_REPRESENTATION('',(#631),#635);
#631 = LINE('',#632,#633);
#632 = CARTESIAN_POINT('',(16.4,0.));
#633 = VECTOR('',#634,1.);
#634 = DIRECTION('',(0.,-1.));
#635 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#636 = PCURVE('',#637,#642);
#637 = PLANE('',#638);
#638 = AXIS2_PLACEMENT_3D('',#639,#640,#641);
#639 = CARTESIAN_POINT('',(72.917494,112.30648,42.5));
#640 = DIRECTION('',(1.,-0.,0.));
#641 = DIRECTION('',(0.,1.,0.));
#642 = DEFINITIONAL_REPRESENTATION('',(#643),#647);
#643 = LINE('',#644,#645);
#644 = CARTESIAN_POINT('',(0.,0.));
#645 = VECTOR('',#646,1.);
#646 = DIRECTION('',(0.,-1.));
#647 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#648 = ORIENTED_EDGE('',*,*,#649,.F.);
#649 = EDGE_CURVE('',#546,#622,#650,.T.);
#650 = SURFACE_CURVE('',#651,(#655,#662),.PCURVE_S1.);
#651 = LINE('',#652,#653);
#652 = CARTESIAN_POINT('',(89.317494,112.30648,42.5));
#653 = VECTOR('',#654,1.);
#654 = DIRECTION('',(-1.,0.,0.));
#655 = PCURVE('',#561,#656);
#656 = DEFINITIONAL_REPRESENTATION('',(#657),#661);
#657 = LINE('',#658,#659);
#658 = CARTESIAN_POINT('',(0.,0.));
#659 = VECTOR('',#660,1.);
#660 = DIRECTION('',(1.,0.));
#661 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#662 = PCURVE('',#126,#663);
#663 = DEFINITIONAL_REPRESENTATION('',(#664),#668);
#664 = LINE('',#665,#666);
#665 = CARTESIAN_POINT('',(-2.395632117936,0.273158171239));
#666 = VECTOR('',#667,1.);
#667 = DIRECTION('',(1.,0.));
#668 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#669 = ADVANCED_FACE('',(#670),#637,.F.);
#670 = FACE_BOUND('',#671,.F.);
#671 = EDGE_LOOP('',(#672,#673,#696,#724));
#672 = ORIENTED_EDGE('',*,*,#621,.T.);
#673 = ORIENTED_EDGE('',*,*,#674,.T.);
#674 = EDGE_CURVE('',#599,#675,#677,.T.);
#675 = VERTEX_POINT('',#676);
#676 = CARTESIAN_POINT('',(72.917494,112.906445,-4.866559));
#677 = SURFACE_CURVE('',#678,(#682,#689),.PCURVE_S1.);
#678 = LINE('',#679,#680);
#679 = CARTESIAN_POINT('',(72.917494,112.30648,-4.866559));
#680 = VECTOR('',#681,1.);
#681 = DIRECTION('',(0.,1.,0.));
#682 = PCURVE('',#637,#683);
#683 = DEFINITIONAL_REPRESENTATION('',(#684),#688);
#684 = LINE('',#685,#686);
#685 = CARTESIAN_POINT('',(-1.42108547152E-14,-47.366559));
#686 = VECTOR('',#687,1.);
#687 = DIRECTION('',(1.,0.));
#688 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#689 = PCURVE('',#72,#690);
#690 = DEFINITIONAL_REPRESENTATION('',(#691),#695);
#691 = LINE('',#692,#693);
#692 = CARTESIAN_POINT('',(14.004367882064,0.273158171239));
#693 = VECTOR('',#694,1.);
#694 = DIRECTION('',(0.,1.));
#695 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#696 = ORIENTED_EDGE('',*,*,#697,.F.);
#697 = EDGE_CURVE('',#698,#675,#700,.T.);
#698 = VERTEX_POINT('',#699);
#699 = CARTESIAN_POINT('',(72.917494,112.906445,42.5));
#700 = SURFACE_CURVE('',#701,(#705,#712),.PCURVE_S1.);
#701 = LINE('',#702,#703);
#702 = CARTESIAN_POINT('',(72.917494,112.906445,42.5));
#703 = VECTOR('',#704,1.);
#704 = DIRECTION('',(0.,0.,-1.));
#705 = PCURVE('',#637,#706);
#706 = DEFINITIONAL_REPRESENTATION('',(#707),#711);
#707 = LINE('',#708,#709);
#708 = CARTESIAN_POINT('',(0.599965,0.));
#709 = VECTOR('',#710,1.);
#710 = DIRECTION('',(0.,-1.));
#711 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#712 = PCURVE('',#713,#718);
#713 = PLANE('',#714);
#714 = AXIS2_PLACEMENT_3D('',#715,#716,#717);
#715 = CARTESIAN_POINT('',(72.917494,112.906445,42.5));
#716 = DIRECTION('',(0.,1.,0.));
#717 = DIRECTION('',(-1.,0.,0.));
#718 = DEFINITIONAL_REPRESENTATION('',(#719),#723);
#719 = LINE('',#720,#721);
#720 = CARTESIAN_POINT('',(0.,0.));
#721 = VECTOR('',#722,1.);
#722 = DIRECTION('',(0.,-1.));
#723 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#724 = ORIENTED_EDGE('',*,*,#725,.F.);
#725 = EDGE_CURVE('',#622,#698,#726,.T.);
#726 = SURFACE_CURVE('',#727,(#731,#738),.PCURVE_S1.);
#727 = LINE('',#728,#729);
#728 = CARTESIAN_POINT('',(72.917494,112.30648,42.5));
#729 = VECTOR('',#730,1.);
#730 = DIRECTION('',(0.,1.,0.));
#731 = PCURVE('',#637,#732);
#732 = DEFINITIONAL_REPRESENTATION('',(#733),#737);
#733 = LINE('',#734,#735);
#734 = CARTESIAN_POINT('',(-1.42108547152E-14,0.));
#735 = VECTOR('',#736,1.);
#736 = DIRECTION('',(1.,0.));
#737 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#738 = PCURVE('',#126,#739);
#739 = DEFINITIONAL_REPRESENTATION('',(#740),#744);
#740 = LINE('',#741,#742);
#741 = CARTESIAN_POINT('',(14.004367882064,0.273158171239));
#742 = VECTOR('',#743,1.);
#743 = DIRECTION('',(0.,1.));
#744 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#745 = ADVANCED_FACE('',(#746),#713,.F.);
#746 = FACE_BOUND('',#747,.F.);
#747 = EDGE_LOOP('',(#748,#749,#772,#800));
#748 = ORIENTED_EDGE('',*,*,#697,.T.);
#749 = ORIENTED_EDGE('',*,*,#750,.T.);
#750 = EDGE_CURVE('',#675,#751,#753,.T.);
#751 = VERTEX_POINT('',#752);
#752 = CARTESIAN_POINT('',(51.710833,112.906445,-4.866559));
#753 = SURFACE_CURVE('',#754,(#758,#765),.PCURVE_S1.);
#754 = LINE('',#755,#756);
#755 = CARTESIAN_POINT('',(72.917494,112.906445,-4.866559));
#756 = VECTOR('',#757,1.);
#757 = DIRECTION('',(-1.,0.,0.));
#758 = PCURVE('',#713,#759);
#759 = DEFINITIONAL_REPRESENTATION('',(#760),#764);
#760 = LINE('',#761,#762);
#761 = CARTESIAN_POINT('',(-1.42108547152E-14,-47.366559));
#762 = VECTOR('',#763,1.);
#763 = DIRECTION('',(1.,0.));
#764 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#765 = PCURVE('',#72,#766);
#766 = DEFINITIONAL_REPRESENTATION('',(#767),#771);
#767 = LINE('',#768,#769);
#768 = CARTESIAN_POINT('',(14.004367882064,0.873123171239));
#769 = VECTOR('',#770,1.);
#770 = DIRECTION('',(1.,0.));
#771 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#772 = ORIENTED_EDGE('',*,*,#773,.F.);
#773 = EDGE_CURVE('',#774,#751,#776,.T.);
#774 = VERTEX_POINT('',#775);
#775 = CARTESIAN_POINT('',(51.710833,112.906445,42.5));
#776 = SURFACE_CURVE('',#777,(#781,#788),.PCURVE_S1.);
#777 = LINE('',#778,#779);
#778 = CARTESIAN_POINT('',(51.710833,112.906445,42.5));
#779 = VECTOR('',#780,1.);
#780 = DIRECTION('',(0.,0.,-1.));
#781 = PCURVE('',#713,#782);
#782 = DEFINITIONAL_REPRESENTATION('',(#783),#787);
#783 = LINE('',#784,#785);
#784 = CARTESIAN_POINT('',(21.206661,0.));
#785 = VECTOR('',#786,1.);
#786 = DIRECTION('',(0.,-1.));
#787 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#788 = PCURVE('',#789,#794);
#789 = PLANE('',#790);
#790 = AXIS2_PLACEMENT_3D('',#791,#792,#793);
#791 = CARTESIAN_POINT('',(51.710833,112.906445,42.5));
#792 = DIRECTION('',(0.931596580528,0.363493894237,0.));
#793 = DIRECTION('',(-0.363493894237,0.931596580528,0.));
#794 = DEFINITIONAL_REPRESENTATION('',(#795),#799);
#795 = LINE('',#796,#797);
#796 = CARTESIAN_POINT('',(0.,0.));
#797 = VECTOR('',#798,1.);
#798 = DIRECTION('',(0.,-1.));
#799 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#800 = ORIENTED_EDGE('',*,*,#801,.F.);
#801 = EDGE_CURVE('',#698,#774,#802,.T.);
#802 = SURFACE_CURVE('',#803,(#807,#814),.PCURVE_S1.);
#803 = LINE('',#804,#805);
#804 = CARTESIAN_POINT('',(72.917494,112.906445,42.5));
#805 = VECTOR('',#806,1.);
#806 = DIRECTION('',(-1.,0.,0.));
#807 = PCURVE('',#713,#808);
#808 = DEFINITIONAL_REPRESENTATION('',(#809),#813);
#809 = LINE('',#810,#811);
#810 = CARTESIAN_POINT('',(-1.42108547152E-14,0.));
#811 = VECTOR('',#812,1.);
#812 = DIRECTION('',(1.,0.));
#813 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#814 = PCURVE('',#126,#815);
#815 = DEFINITIONAL_REPRESENTATION('',(#816),#820);
#816 = LINE('',#817,#818);
#817 = CARTESIAN_POINT('',(14.004367882064,0.873123171239));
#818 = VECTOR('',#819,1.);
#819 = DIRECTION('',(1.,0.));
#820 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#821 = ADVANCED_FACE('',(#822),#789,.F.);
#822 = FACE_BOUND('',#823,.F.);
#823 = EDGE_LOOP('',(#824,#825,#848,#876));
#824 = ORIENTED_EDGE('',*,*,#773,.T.);
#825 = ORIENTED_EDGE('',*,*,#826,.T.);
#826 = EDGE_CURVE('',#751,#827,#829,.T.);
#827 = VERTEX_POINT('',#828);
#828 = CARTESIAN_POINT('',(51.468253,113.528152,-4.866559));
#829 = SURFACE_CURVE('',#830,(#834,#841),.PCURVE_S1.);
#830 = LINE('',#831,#832);
#831 = CARTESIAN_POINT('',(51.710833,112.906445,-4.866559));
#832 = VECTOR('',#833,1.);
#833 = DIRECTION('',(-0.363493894237,0.931596580528,0.));
#834 = PCURVE('',#789,#835);
#835 = DEFINITIONAL_REPRESENTATION('',(#836),#840);
#836 = LINE('',#837,#838);
#837 = CARTESIAN_POINT('',(0.,-47.366559));
#838 = VECTOR('',#839,1.);
#839 = DIRECTION('',(1.,0.));
#840 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#841 = PCURVE('',#72,#842);
#842 = DEFINITIONAL_REPRESENTATION('',(#843),#847);
#843 = LINE('',#844,#845);
#844 = CARTESIAN_POINT('',(35.211028882064,0.873123171239));
#845 = VECTOR('',#846,1.);
#846 = DIRECTION('',(0.363493894237,0.931596580528));
#847 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#848 = ORIENTED_EDGE('',*,*,#849,.F.);
#849 = EDGE_CURVE('',#850,#827,#852,.T.);
#850 = VERTEX_POINT('',#851);
#851 = CARTESIAN_POINT('',(51.468253,113.528152,42.5));
#852 = SURFACE_CURVE('',#853,(#857,#864),.PCURVE_S1.);
#853 = LINE('',#854,#855);
#854 = CARTESIAN_POINT('',(51.468253,113.528152,42.5));
#855 = VECTOR('',#856,1.);
#856 = DIRECTION('',(0.,0.,-1.));
#857 = PCURVE('',#789,#858);
#858 = DEFINITIONAL_REPRESENTATION('',(#859),#863);
#859 = LINE('',#860,#861);
#860 = CARTESIAN_POINT('',(0.667356464155,0.));
#861 = VECTOR('',#862,1.);
#862 = DIRECTION('',(0.,-1.));
#863 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#864 = PCURVE('',#865,#870);
#865 = PLANE('',#866);
#866 = AXIS2_PLACEMENT_3D('',#867,#868,#869);
#867 = CARTESIAN_POINT('',(51.468253,113.528152,42.5));
#868 = DIRECTION('',(0.998464000178,5.54043351018E-02,0.));
#869 = DIRECTION('',(-5.54043351018E-02,0.998464000178,0.));
#870 = DEFINITIONAL_REPRESENTATION('',(#871),#875);
#871 = LINE('',#872,#873);
#872 = CARTESIAN_POINT('',(0.,0.));
#873 = VECTOR('',#874,1.);
#874 = DIRECTION('',(0.,-1.));
#875 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#876 = ORIENTED_EDGE('',*,*,#877,.F.);
#877 = EDGE_CURVE('',#774,#850,#878,.T.);
#878 = SURFACE_CURVE('',#879,(#883,#890),.PCURVE_S1.);
#879 = LINE('',#880,#881);
#880 = CARTESIAN_POINT('',(51.710833,112.906445,42.5));
#881 = VECTOR('',#882,1.);
#882 = DIRECTION('',(-0.363493894237,0.931596580528,0.));
#883 = PCURVE('',#789,#884);
#884 = DEFINITIONAL_REPRESENTATION('',(#885),#889);
#885 = LINE('',#886,#887);
#886 = CARTESIAN_POINT('',(0.,0.));
#887 = VECTOR('',#888,1.);
#888 = DIRECTION('',(1.,0.));
#889 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#890 = PCURVE('',#126,#891);
#891 = DEFINITIONAL_REPRESENTATION('',(#892),#896);
#892 = LINE('',#893,#894);
#893 = CARTESIAN_POINT('',(35.211028882064,0.873123171239));
#894 = VECTOR('',#895,1.);
#895 = DIRECTION('',(0.363493894237,0.931596580528));
#896 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#897 = ADVANCED_FACE('',(#898),#865,.F.);
#898 = FACE_BOUND('',#899,.F.);
#899 = EDGE_LOOP('',(#900,#901,#924,#952));
#900 = ORIENTED_EDGE('',*,*,#849,.T.);
#901 = ORIENTED_EDGE('',*,*,#902,.T.);
#902 = EDGE_CURVE('',#827,#903,#905,.T.);
#903 = VERTEX_POINT('',#904);
#904 = CARTESIAN_POINT('',(51.008936,121.80569,-4.866559));
#905 = SURFACE_CURVE('',#906,(#910,#917),.PCURVE_S1.);
#906 = LINE('',#907,#908);
#907 = CARTESIAN_POINT('',(51.468253,113.528152,-4.866559));
#908 = VECTOR('',#909,1.);
#909 = DIRECTION('',(-5.54043351018E-02,0.998464000178,0.));
#910 = PCURVE('',#865,#911);
#911 = DEFINITIONAL_REPRESENTATION('',(#912),#916);
#912 = LINE('',#913,#914);
#913 = CARTESIAN_POINT('',(-1.418902684489E-14,-47.366559));
#914 = VECTOR('',#915,1.);
#915 = DIRECTION('',(1.,0.));
#916 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#917 = PCURVE('',#72,#918);
#918 = DEFINITIONAL_REPRESENTATION('',(#919),#923);
#919 = LINE('',#920,#921);
#920 = CARTESIAN_POINT('',(35.453608882064,1.494830171239));
#921 = VECTOR('',#922,1.);
#922 = DIRECTION('',(5.54043351018E-02,0.998464000178));
#923 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#924 = ORIENTED_EDGE('',*,*,#925,.F.);
#925 = EDGE_CURVE('',#926,#903,#928,.T.);
#926 = VERTEX_POINT('',#927);
#927 = CARTESIAN_POINT('',(51.008936,121.80569,42.5));
#928 = SURFACE_CURVE('',#929,(#933,#940),.PCURVE_S1.);
#929 = LINE('',#930,#931);
#930 = CARTESIAN_POINT('',(51.008936,121.80569,42.5));
#931 = VECTOR('',#932,1.);
#932 = DIRECTION('',(0.,0.,-1.));
#933 = PCURVE('',#865,#934);
#934 = DEFINITIONAL_REPRESENTATION('',(#935),#939);
#935 = LINE('',#936,#937);
#936 = CARTESIAN_POINT('',(8.290271856093,0.));
#937 = VECTOR('',#938,1.);
#938 = DIRECTION('',(0.,-1.));
#939 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#940 = PCURVE('',#941,#946);
#941 = PLANE('',#942);
#942 = AXIS2_PLACEMENT_3D('',#943,#944,#945);
#943 = CARTESIAN_POINT('',(51.008936,121.80569,42.5));
#944 = DIRECTION('',(5.196848995186E-05,-0.99999999865,0.));
#945 = DIRECTION('',(0.99999999865,5.196848995186E-05,0.));
#946 = DEFINITIONAL_REPRESENTATION('',(#947),#951);
#947 = LINE('',#948,#949);
#948 = CARTESIAN_POINT('',(0.,0.));
#949 = VECTOR('',#950,1.);
#950 = DIRECTION('',(0.,-1.));
#951 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#952 = ORIENTED_EDGE('',*,*,#953,.F.);
#953 = EDGE_CURVE('',#850,#926,#954,.T.);
#954 = SURFACE_CURVE('',#955,(#959,#966),.PCURVE_S1.);
#955 = LINE('',#956,#957);
#956 = CARTESIAN_POINT('',(51.468253,113.528152,42.5));
#957 = VECTOR('',#958,1.);
#958 = DIRECTION('',(-5.54043351018E-02,0.998464000178,0.));
#959 = PCURVE('',#865,#960);
#960 = DEFINITIONAL_REPRESENTATION('',(#961),#965);
#961 = LINE('',#962,#963);
#962 = CARTESIAN_POINT('',(-1.418902684489E-14,0.));
#963 = VECTOR('',#964,1.);
#964 = DIRECTION('',(1.,0.));
#965 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#966 = PCURVE('',#126,#967);
#967 = DEFINITIONAL_REPRESENTATION('',(#968),#972);
#968 = LINE('',#969,#970);
#969 = CARTESIAN_POINT('',(35.453608882064,1.494830171239));
#970 = VECTOR('',#971,1.);
#971 = DIRECTION('',(5.54043351018E-02,0.998464000178));
#972 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#973 = ADVANCED_FACE('',(#974),#941,.F.);
#974 = FACE_BOUND('',#975,.F.);
#975 = EDGE_LOOP('',(#976,#977,#1000,#1028));
#976 = ORIENTED_EDGE('',*,*,#925,.T.);
#977 = ORIENTED_EDGE('',*,*,#978,.T.);
#978 = EDGE_CURVE('',#903,#979,#981,.T.);
#979 = VERTEX_POINT('',#980);
#980 = CARTESIAN_POINT('',(77.717428,121.807078,-4.866559));
#981 = SURFACE_CURVE('',#982,(#986,#993),.PCURVE_S1.);
#982 = LINE('',#983,#984);
#983 = CARTESIAN_POINT('',(51.008936,121.80569,-4.866559));
#984 = VECTOR('',#985,1.);
#985 = DIRECTION('',(0.99999999865,5.196848995186E-05,0.));
#986 = PCURVE('',#941,#987);
#987 = DEFINITIONAL_REPRESENTATION('',(#988),#992);
#988 = LINE('',#989,#990);
#989 = CARTESIAN_POINT('',(-7.105427348006E-15,-47.366559));
#990 = VECTOR('',#991,1.);
#991 = DIRECTION('',(1.,0.));
#992 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#993 = PCURVE('',#72,#994);
#994 = DEFINITIONAL_REPRESENTATION('',(#995),#999);
#995 = LINE('',#996,#997);
#996 = CARTESIAN_POINT('',(35.912925882064,9.772368171239));
#997 = VECTOR('',#998,1.);
#998 = DIRECTION('',(-0.99999999865,5.196848995186E-05));
#999 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1000 = ORIENTED_EDGE('',*,*,#1001,.F.);
#1001 = EDGE_CURVE('',#1002,#979,#1004,.T.);
#1002 = VERTEX_POINT('',#1003);
#1003 = CARTESIAN_POINT('',(77.717428,121.807078,42.5));
#1004 = SURFACE_CURVE('',#1005,(#1009,#1016),.PCURVE_S1.);
#1005 = LINE('',#1006,#1007);
#1006 = CARTESIAN_POINT('',(77.717428,121.807078,42.5));
#1007 = VECTOR('',#1008,1.);
#1008 = DIRECTION('',(0.,0.,-1.));
#1009 = PCURVE('',#941,#1010);
#1010 = DEFINITIONAL_REPRESENTATION('',(#1011),#1015);
#1011 = LINE('',#1012,#1013);
#1012 = CARTESIAN_POINT('',(26.708492036066,0.));
#1013 = VECTOR('',#1014,1.);
#1014 = DIRECTION('',(0.,-1.));
#1015 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1016 = PCURVE('',#1017,#1022);
#1017 = PLANE('',#1018);
#1018 = AXIS2_PLACEMENT_3D('',#1019,#1020,#1021);
#1019 = CARTESIAN_POINT('',(77.717428,121.807078,42.5));
#1020 = DIRECTION('',(0.999999999978,-6.670306744902E-06,0.));
#1021 = DIRECTION('',(6.670306744902E-06,0.999999999978,0.));
#1022 = DEFINITIONAL_REPRESENTATION('',(#1023),#1027);
#1023 = LINE('',#1024,#1025);
#1024 = CARTESIAN_POINT('',(0.,0.));
#1025 = VECTOR('',#1026,1.);
#1026 = DIRECTION('',(0.,-1.));
#1027 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1028 = ORIENTED_EDGE('',*,*,#1029,.F.);
#1029 = EDGE_CURVE('',#926,#1002,#1030,.T.);
#1030 = SURFACE_CURVE('',#1031,(#1035,#1042),.PCURVE_S1.);
#1031 = LINE('',#1032,#1033);
#1032 = CARTESIAN_POINT('',(51.008936,121.80569,42.5));
#1033 = VECTOR('',#1034,1.);
#1034 = DIRECTION('',(0.99999999865,5.196848995186E-05,0.));
#1035 = PCURVE('',#941,#1036);
#1036 = DEFINITIONAL_REPRESENTATION('',(#1037),#1041);
#1037 = LINE('',#1038,#1039);
#1038 = CARTESIAN_POINT('',(-7.105427348006E-15,0.));
#1039 = VECTOR('',#1040,1.);
#1040 = DIRECTION('',(1.,0.));
#1041 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1042 = PCURVE('',#126,#1043);
#1043 = DEFINITIONAL_REPRESENTATION('',(#1044),#1048);
#1044 = LINE('',#1045,#1046);
#1045 = CARTESIAN_POINT('',(35.912925882064,9.772368171239));
#1046 = VECTOR('',#1047,1.);
#1047 = DIRECTION('',(-0.99999999865,5.196848995186E-05));
#1048 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1049 = ADVANCED_FACE('',(#1050),#1017,.F.);
#1050 = FACE_BOUND('',#1051,.F.);
#1051 = EDGE_LOOP('',(#1052,#1053,#1076,#1104));
#1052 = ORIENTED_EDGE('',*,*,#1001,.T.);
#1053 = ORIENTED_EDGE('',*,*,#1054,.T.);
#1054 = EDGE_CURVE('',#979,#1055,#1057,.T.);
#1055 = VERTEX_POINT('',#1056);
#1056 = CARTESIAN_POINT('',(77.717435,122.856505,-4.866559));
#1057 = SURFACE_CURVE('',#1058,(#1062,#1069),.PCURVE_S1.);
#1058 = LINE('',#1059,#1060);
#1059 = CARTESIAN_POINT('',(77.717428,121.807078,-4.866559));
#1060 = VECTOR('',#1061,1.);
#1061 = DIRECTION('',(6.670306744902E-06,0.999999999978,0.));
#1062 = PCURVE('',#1017,#1063);
#1063 = DEFINITIONAL_REPRESENTATION('',(#1064),#1068);
#1064 = LINE('',#1065,#1066);
#1065 = CARTESIAN_POINT('',(0.,-47.366559));
#1066 = VECTOR('',#1067,1.);
#1067 = DIRECTION('',(1.,0.));
#1068 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1069 = PCURVE('',#72,#1070);
#1070 = DEFINITIONAL_REPRESENTATION('',(#1071),#1075);
#1071 = LINE('',#1072,#1073);
#1072 = CARTESIAN_POINT('',(9.204433882064,9.773756171239));
#1073 = VECTOR('',#1074,1.);
#1074 = DIRECTION('',(-6.670306744902E-06,0.999999999978));
#1075 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1076 = ORIENTED_EDGE('',*,*,#1077,.F.);
#1077 = EDGE_CURVE('',#1078,#1055,#1080,.T.);
#1078 = VERTEX_POINT('',#1079);
#1079 = CARTESIAN_POINT('',(77.717435,122.856505,42.5));
#1080 = SURFACE_CURVE('',#1081,(#1085,#1092),.PCURVE_S1.);
#1081 = LINE('',#1082,#1083);
#1082 = CARTESIAN_POINT('',(77.717435,122.856505,42.5));
#1083 = VECTOR('',#1084,1.);
#1084 = DIRECTION('',(0.,0.,-1.));
#1085 = PCURVE('',#1017,#1086);
#1086 = DEFINITIONAL_REPRESENTATION('',(#1087),#1091);
#1087 = LINE('',#1088,#1089);
#1088 = CARTESIAN_POINT('',(1.049427000023,0.));
#1089 = VECTOR('',#1090,1.);
#1090 = DIRECTION('',(0.,-1.));
#1091 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1092 = PCURVE('',#1093,#1098);
#1093 = PLANE('',#1094);
#1094 = AXIS2_PLACEMENT_3D('',#1095,#1096,#1097);
#1095 = CARTESIAN_POINT('',(77.717435,122.856505,42.5));
#1096 = DIRECTION('',(0.,-1.,0.));
#1097 = DIRECTION('',(1.,0.,0.));
#1098 = DEFINITIONAL_REPRESENTATION('',(#1099),#1103);
#1099 = LINE('',#1100,#1101);
#1100 = CARTESIAN_POINT('',(0.,0.));
#1101 = VECTOR('',#1102,1.);
#1102 = DIRECTION('',(0.,-1.));
#1103 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1104 = ORIENTED_EDGE('',*,*,#1105,.F.);
#1105 = EDGE_CURVE('',#1002,#1078,#1106,.T.);
#1106 = SURFACE_CURVE('',#1107,(#1111,#1118),.PCURVE_S1.);
#1107 = LINE('',#1108,#1109);
#1108 = CARTESIAN_POINT('',(77.717428,121.807078,42.5));
#1109 = VECTOR('',#1110,1.);
#1110 = DIRECTION('',(6.670306744902E-06,0.999999999978,0.));
#1111 = PCURVE('',#1017,#1112);
#1112 = DEFINITIONAL_REPRESENTATION('',(#1113),#1117);
#1113 = LINE('',#1114,#1115);
#1114 = CARTESIAN_POINT('',(0.,0.));
#1115 = VECTOR('',#1116,1.);
#1116 = DIRECTION('',(1.,0.));
#1117 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1118 = PCURVE('',#126,#1119);
#1119 = DEFINITIONAL_REPRESENTATION('',(#1120),#1124);
#1120 = LINE('',#1121,#1122);
#1121 = CARTESIAN_POINT('',(9.204433882064,9.773756171239));
#1122 = VECTOR('',#1123,1.);
#1123 = DIRECTION('',(-6.670306744902E-06,0.999999999978));
#1124 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1125 = ADVANCED_FACE('',(#1126),#1093,.F.);
#1126 = FACE_BOUND('',#1127,.F.);
#1127 = EDGE_LOOP('',(#1128,#1129,#1152,#1175));
#1128 = ORIENTED_EDGE('',*,*,#1077,.T.);
#1129 = ORIENTED_EDGE('',*,*,#1130,.T.);
#1130 = EDGE_CURVE('',#1055,#1131,#1133,.T.);
#1131 = VERTEX_POINT('',#1132);
#1132 = CARTESIAN_POINT('',(85.917422,122.856505,-4.866559));
#1133 = SURFACE_CURVE('',#1134,(#1138,#1145),.PCURVE_S1.);
#1134 = LINE('',#1135,#1136);
#1135 = CARTESIAN_POINT('',(77.717435,122.856505,-4.866559));
#1136 = VECTOR('',#1137,1.);
#1137 = DIRECTION('',(1.,0.,0.));
#1138 = PCURVE('',#1093,#1139);
#1139 = DEFINITIONAL_REPRESENTATION('',(#1140),#1144);
#1140 = LINE('',#1141,#1142);
#1141 = CARTESIAN_POINT('',(1.42108547152E-14,-47.366559));
#1142 = VECTOR('',#1143,1.);
#1143 = DIRECTION('',(1.,0.));
#1144 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1145 = PCURVE('',#72,#1146);
#1146 = DEFINITIONAL_REPRESENTATION('',(#1147),#1151);
#1147 = LINE('',#1148,#1149);
#1148 = CARTESIAN_POINT('',(9.204426882064,10.823183171239));
#1149 = VECTOR('',#1150,1.);
#1150 = DIRECTION('',(-1.,0.));
#1151 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1152 = ORIENTED_EDGE('',*,*,#1153,.F.);
#1153 = EDGE_CURVE('',#1154,#1131,#1156,.T.);
#1154 = VERTEX_POINT('',#1155);
#1155 = CARTESIAN_POINT('',(85.917422,122.856505,42.5));
#1156 = SURFACE_CURVE('',#1157,(#1161,#1168),.PCURVE_S1.);
#1157 = LINE('',#1158,#1159);
#1158 = CARTESIAN_POINT('',(85.917422,122.856505,42.5));
#1159 = VECTOR('',#1160,1.);
#1160 = DIRECTION('',(0.,0.,-1.));
#1161 = PCURVE('',#1093,#1162);
#1162 = DEFINITIONAL_REPRESENTATION('',(#1163),#1167);
#1163 = LINE('',#1164,#1165);
#1164 = CARTESIAN_POINT('',(8.199987,0.));
#1165 = VECTOR('',#1166,1.);
#1166 = DIRECTION('',(0.,-1.));
#1167 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1168 = PCURVE('',#44,#1169);
#1169 = DEFINITIONAL_REPRESENTATION('',(#1170),#1174);
#1170 = LINE('',#1171,#1172);
#1171 = CARTESIAN_POINT('',(0.,0.));
#1172 = VECTOR('',#1173,1.);
#1173 = DIRECTION('',(0.,-1.));
#1174 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1175 = ORIENTED_EDGE('',*,*,#1176,.F.);
#1176 = EDGE_CURVE('',#1078,#1154,#1177,.T.);
#1177 = SURFACE_CURVE('',#1178,(#1182,#1189),.PCURVE_S1.);
#1178 = LINE('',#1179,#1180);
#1179 = CARTESIAN_POINT('',(77.717435,122.856505,42.5));
#1180 = VECTOR('',#1181,1.);
#1181 = DIRECTION('',(1.,0.,0.));
#1182 = PCURVE('',#1093,#1183);
#1183 = DEFINITIONAL_REPRESENTATION('',(#1184),#1188);
#1184 = LINE('',#1185,#1186);
#1185 = CARTESIAN_POINT('',(1.42108547152E-14,0.));
#1186 = VECTOR('',#1187,1.);
#1187 = DIRECTION('',(1.,0.));
#1188 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1189 = PCURVE('',#126,#1190);
#1190 = DEFINITIONAL_REPRESENTATION('',(#1191),#1195);
#1191 = LINE('',#1192,#1193);
#1192 = CARTESIAN_POINT('',(9.204426882064,10.823183171239));
#1193 = VECTOR('',#1194,1.);
#1194 = DIRECTION('',(-1.,0.));
#1195 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1196 = ADVANCED_FACE('',(#1197),#44,.F.);
#1197 = FACE_BOUND('',#1198,.F.);
#1198 = EDGE_LOOP('',(#1199,#1200,#1221,#1222));
#1199 = ORIENTED_EDGE('',*,*,#1153,.T.);
#1200 = ORIENTED_EDGE('',*,*,#1201,.T.);
#1201 = EDGE_CURVE('',#1131,#24,#1202,.T.);
#1202 = SURFACE_CURVE('',#1203,(#1207,#1214),.PCURVE_S1.);
#1203 = LINE('',#1204,#1205);
#1204 = CARTESIAN_POINT('',(85.917422,122.856505,-4.866559));
#1205 = VECTOR('',#1206,1.);
#1206 = DIRECTION('',(0.,1.,0.));
#1207 = PCURVE('',#44,#1208);
#1208 = DEFINITIONAL_REPRESENTATION('',(#1209),#1213);
#1209 = LINE('',#1210,#1211);
#1210 = CARTESIAN_POINT('',(-1.42108547152E-14,-47.366559));
#1211 = VECTOR('',#1212,1.);
#1212 = DIRECTION('',(1.,0.));
#1213 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1214 = PCURVE('',#72,#1215);
#1215 = DEFINITIONAL_REPRESENTATION('',(#1216),#1220);
#1216 = LINE('',#1217,#1218);
#1217 = CARTESIAN_POINT('',(1.004439882064,10.823183171239));
#1218 = VECTOR('',#1219,1.);
#1219 = DIRECTION('',(0.,1.));
#1220 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1221 = ORIENTED_EDGE('',*,*,#21,.F.);
#1222 = ORIENTED_EDGE('',*,*,#1223,.F.);
#1223 = EDGE_CURVE('',#1154,#22,#1224,.T.);
#1224 = SURFACE_CURVE('',#1225,(#1229,#1236),.PCURVE_S1.);
#1225 = LINE('',#1226,#1227);
#1226 = CARTESIAN_POINT('',(85.917422,122.856505,42.5));
#1227 = VECTOR('',#1228,1.);
#1228 = DIRECTION('',(0.,1.,0.));
#1229 = PCURVE('',#44,#1230);
#1230 = DEFINITIONAL_REPRESENTATION('',(#1231),#1235);
#1231 = LINE('',#1232,#1233);
#1232 = CARTESIAN_POINT('',(-1.42108547152E-14,0.));
#1233 = VECTOR('',#1234,1.);
#1234 = DIRECTION('',(1.,0.));
#1235 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1236 = PCURVE('',#126,#1237);
#1237 = DEFINITIONAL_REPRESENTATION('',(#1238),#1242);
#1238 = LINE('',#1239,#1240);
#1239 = CARTESIAN_POINT('',(1.004439882064,10.823183171239));
#1240 = VECTOR('',#1241,1.);
#1241 = DIRECTION('',(0.,1.));
#1242 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1243 = ADVANCED_FACE('',(#1244),#126,.F.);
#1244 = FACE_BOUND('',#1245,.F.);
#1245 = EDGE_LOOP('',(#1246,#1247,#1248,#1249,#1250,#1251,#1252,#1253,
    #1254,#1255,#1256,#1257,#1258,#1259,#1260,#1261));
#1246 = ORIENTED_EDGE('',*,*,#112,.T.);
#1247 = ORIENTED_EDGE('',*,*,#193,.T.);
#1248 = ORIENTED_EDGE('',*,*,#269,.T.);
#1249 = ORIENTED_EDGE('',*,*,#345,.T.);
#1250 = ORIENTED_EDGE('',*,*,#421,.T.);
#1251 = ORIENTED_EDGE('',*,*,#497,.T.);
#1252 = ORIENTED_EDGE('',*,*,#573,.T.);
#1253 = ORIENTED_EDGE('',*,*,#649,.T.);
#1254 = ORIENTED_EDGE('',*,*,#725,.T.);
#1255 = ORIENTED_EDGE('',*,*,#801,.T.);
#1256 = ORIENTED_EDGE('',*,*,#877,.T.);
#1257 = ORIENTED_EDGE('',*,*,#953,.T.);
#1258 = ORIENTED_EDGE('',*,*,#1029,.T.);
#1259 = ORIENTED_EDGE('',*,*,#1105,.T.);
#1260 = ORIENTED_EDGE('',*,*,#1176,.T.);
#1261 = ORIENTED_EDGE('',*,*,#1223,.T.);
#1262 = ADVANCED_FACE('',(#1263),#72,.T.);
#1263 = FACE_BOUND('',#1264,.T.);
#1264 = EDGE_LOOP('',(#1265,#1266,#1267,#1268,#1269,#1270,#1271,#1272,
    #1273,#1274,#1275,#1276,#1277,#1278,#1279,#1280));
#1265 = ORIENTED_EDGE('',*,*,#56,.T.);
#1266 = ORIENTED_EDGE('',*,*,#142,.T.);
#1267 = ORIENTED_EDGE('',*,*,#218,.T.);
#1268 = ORIENTED_EDGE('',*,*,#294,.T.);
#1269 = ORIENTED_EDGE('',*,*,#370,.T.);
#1270 = ORIENTED_EDGE('',*,*,#446,.T.);
#1271 = ORIENTED_EDGE('',*,*,#522,.T.);
#1272 = ORIENTED_EDGE('',*,*,#598,.T.);
#1273 = ORIENTED_EDGE('',*,*,#674,.T.);
#1274 = ORIENTED_EDGE('',*,*,#750,.T.);
#1275 = ORIENTED_EDGE('',*,*,#826,.T.);
#1276 = ORIENTED_EDGE('',*,*,#902,.T.);
#1277 = ORIENTED_EDGE('',*,*,#978,.T.);
#1278 = ORIENTED_EDGE('',*,*,#1054,.T.);
#1279 = ORIENTED_EDGE('',*,*,#1130,.T.);
#1280 = ORIENTED_EDGE('',*,*,#1201,.T.);
#1281 = ( GEOMETRIC_REPRESENTATION_CONTEXT(3) 
GLOBAL_UNCERTAINTY_ASSIGNED_CONTEXT((#1285)) 
GLOBAL_UNIT_ASSIGNED_CONTEXT((#1282,#1283,#1284)) REPRESENTATION_CONTEXT
('Context #1','3D Context with UNIT and UNCERTAINTY') );
#1282 = ( LENGTH_UNIT() NAMED_UNIT(*) SI_UNIT(.MILLI.,.METRE.) );
#1283 = ( NAMED_UNIT(*) PLANE_ANGLE_UNIT() SI_UNIT($,.RADIAN.) );
#1284 = ( NAMED_UNIT(*) SI_UNIT($,.STERADIAN.) SOLID_ANGLE_UNIT() );
#1285 = UNCERTAINTY_MEASURE_WITH_UNIT(LENGTH_MEASURE(1.E-07),#1282,
  'distance_accuracy_value','confusion accuracy');
#1286 = PRODUCT_RELATED_PRODUCT_CATEGORY('part',$,(#7));
#1287 = MECHANICAL_DESIGN_GEOMETRIC_PRESENTATION_REPRESENTATION('',(
    #1288),#1281);
#1288 = STYLED_ITEM('color',(#1289),#15);
#1289 = PRESENTATION_STYLE_ASSIGNMENT((#1290));
#1290 = SURFACE_STYLE_USAGE(.BOTH.,#1291);
#1291 = SURFACE_SIDE_STYLE('',(#1292));
#1292 = SURFACE_STYLE_FILL_AREA(#1293);
#1293 = FILL_AREA_STYLE('',(#1294));
#1294 = FILL_AREA_STYLE_COLOUR('',#1295);
#1295 = DRAUGHTING_PRE_DEFINED_COLOUR('cyan');
ENDSEC;
END-ISO-10303-21;
