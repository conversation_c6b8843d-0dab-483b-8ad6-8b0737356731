ISO-10303-21;
HEADER;
FILE_DESCRIPTION(('Open CASCADE Model'),'2;1');
FILE_NAME('Pile_PC1-P1','2025-08-25T20:07:48',('Author'),('Open CASCADE'
    ),'Open CASCADE STEP processor 7.8','build123d','Unknown');
FILE_SCHEMA(('AUTOMOTIVE_DESIGN { 1 0 10303 214 1 1 1 1 }'));
ENDSEC;
DATA;
#1 = APPLICATION_PROTOCOL_DEFINITION('international standard',
  'automotive_design',2000,#2);
#2 = APPLICATION_CONTEXT(
  'core data for automotive mechanical design processes');
#3 = SHAPE_DEFINITION_REPRESENTATION(#4,#10);
#4 = PRODUCT_DEFINITION_SHAPE('','',#5);
#5 = PRODUCT_DEFINITION('design','',#6,#9);
#6 = PRODUCT_DEFINITION_FORMATION('','',#7);
#7 = PRODUCT('Pile_PC1-P1','Pile_PC1-P1','',(#8));
#8 = PRODUCT_CONTEXT('',#2,'mechanical');
#9 = PRODUCT_DEFINITION_CONTEXT('part definition',#2,'design');
#10 = SHAPE_REPRESENTATION('',(#11,#15,#19,#23),#27);
#11 = AXIS2_PLACEMENT_3D('',#12,#13,#14);
#12 = CARTESIAN_POINT('',(0.,0.,0.));
#13 = DIRECTION('',(0.,0.,1.));
#14 = DIRECTION('',(1.,0.,-0.));
#15 = AXIS2_PLACEMENT_3D('',#16,#17,#18);
#16 = CARTESIAN_POINT('',(51.523496,132.387429,1.909425));
#17 = DIRECTION('',(0.,0.,1.));
#18 = DIRECTION('',(1.,0.,0.));
#19 = AXIS2_PLACEMENT_3D('',#20,#21,#22);
#20 = CARTESIAN_POINT('',(0.,0.,0.));
#21 = DIRECTION('',(0.,0.,1.));
#22 = DIRECTION('',(1.,0.,-0.));
#23 = AXIS2_PLACEMENT_3D('',#24,#25,#26);
#24 = CARTESIAN_POINT('',(0.,0.,0.));
#25 = DIRECTION('',(0.,0.,1.));
#26 = DIRECTION('',(1.,0.,-0.));
#27 = ( GEOMETRIC_REPRESENTATION_CONTEXT(3) 
GLOBAL_UNCERTAINTY_ASSIGNED_CONTEXT((#31)) GLOBAL_UNIT_ASSIGNED_CONTEXT(
(#28,#29,#30)) REPRESENTATION_CONTEXT('Context #1',
  '3D Context with UNIT and UNCERTAINTY') );
#28 = ( LENGTH_UNIT() NAMED_UNIT(*) SI_UNIT(.MILLI.,.METRE.) );
#29 = ( NAMED_UNIT(*) PLANE_ANGLE_UNIT() SI_UNIT($,.RADIAN.) );
#30 = ( NAMED_UNIT(*) SI_UNIT($,.STERADIAN.) SOLID_ANGLE_UNIT() );
#31 = UNCERTAINTY_MEASURE_WITH_UNIT(LENGTH_MEASURE(1.E-07),#28,
  'distance_accuracy_value','confusion accuracy');
#32 = PRODUCT_RELATED_PRODUCT_CATEGORY('part',$,(#7));
#33 = SHAPE_DEFINITION_REPRESENTATION(#34,#40);
#34 = PRODUCT_DEFINITION_SHAPE('','',#35);
#35 = PRODUCT_DEFINITION('design','',#36,#39);
#36 = PRODUCT_DEFINITION_FORMATION('','',#37);
#37 = PRODUCT('PC1-P1_Part1','PC1-P1_Part1','',(#38));
#38 = PRODUCT_CONTEXT('',#2,'mechanical');
#39 = PRODUCT_DEFINITION_CONTEXT('part definition',#2,'design');
#40 = ADVANCED_BREP_SHAPE_REPRESENTATION('',(#11,#41),#139);
#41 = MANIFOLD_SOLID_BREP('',#42);
#42 = CLOSED_SHELL('',(#43,#131,#135));
#43 = ADVANCED_FACE('',(#44),#57,.T.);
#44 = FACE_BOUND('',#45,.T.);
#45 = EDGE_LOOP('',(#46,#80,#103,#130));
#46 = ORIENTED_EDGE('',*,*,#47,.F.);
#47 = EDGE_CURVE('',#48,#48,#50,.T.);
#48 = VERTEX_POINT('',#49);
#49 = CARTESIAN_POINT('',(0.275,-6.73555739531E-17,16.090575));
#50 = SURFACE_CURVE('',#51,(#56,#68),.PCURVE_S1.);
#51 = CIRCLE('',#52,0.275);
#52 = AXIS2_PLACEMENT_3D('',#53,#54,#55);
#53 = CARTESIAN_POINT('',(0.,0.,16.090575));
#54 = DIRECTION('',(0.,0.,1.));
#55 = DIRECTION('',(1.,0.,-0.));
#56 = PCURVE('',#57,#62);
#57 = CYLINDRICAL_SURFACE('',#58,0.275);
#58 = AXIS2_PLACEMENT_3D('',#59,#60,#61);
#59 = CARTESIAN_POINT('',(0.,0.,0.));
#60 = DIRECTION('',(0.,0.,1.));
#61 = DIRECTION('',(1.,0.,-0.));
#62 = DEFINITIONAL_REPRESENTATION('',(#63),#67);
#63 = LINE('',#64,#65);
#64 = CARTESIAN_POINT('',(0.,16.090575));
#65 = VECTOR('',#66,1.);
#66 = DIRECTION('',(1.,0.));
#67 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#68 = PCURVE('',#69,#74);
#69 = PLANE('',#70);
#70 = AXIS2_PLACEMENT_3D('',#71,#72,#73);
#71 = CARTESIAN_POINT('',(0.,0.,16.090575));
#72 = DIRECTION('',(0.,0.,1.));
#73 = DIRECTION('',(1.,0.,-0.));
#74 = DEFINITIONAL_REPRESENTATION('',(#75),#79);
#75 = CIRCLE('',#76,0.275);
#76 = AXIS2_PLACEMENT_2D('',#77,#78);
#77 = CARTESIAN_POINT('',(0.,0.));
#78 = DIRECTION('',(1.,0.));
#79 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#80 = ORIENTED_EDGE('',*,*,#81,.F.);
#81 = EDGE_CURVE('',#82,#48,#84,.T.);
#82 = VERTEX_POINT('',#83);
#83 = CARTESIAN_POINT('',(0.275,-6.73555739531E-17,0.));
#84 = SEAM_CURVE('',#85,(#89,#96),.PCURVE_S1.);
#85 = LINE('',#86,#87);
#86 = CARTESIAN_POINT('',(0.275,-6.73555739531E-17,0.));
#87 = VECTOR('',#88,1.);
#88 = DIRECTION('',(0.,0.,1.));
#89 = PCURVE('',#57,#90);
#90 = DEFINITIONAL_REPRESENTATION('',(#91),#95);
#91 = LINE('',#92,#93);
#92 = CARTESIAN_POINT('',(6.28318530718,-0.));
#93 = VECTOR('',#94,1.);
#94 = DIRECTION('',(0.,1.));
#95 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#96 = PCURVE('',#57,#97);
#97 = DEFINITIONAL_REPRESENTATION('',(#98),#102);
#98 = LINE('',#99,#100);
#99 = CARTESIAN_POINT('',(0.,-0.));
#100 = VECTOR('',#101,1.);
#101 = DIRECTION('',(0.,1.));
#102 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#103 = ORIENTED_EDGE('',*,*,#104,.T.);
#104 = EDGE_CURVE('',#82,#82,#105,.T.);
#105 = SURFACE_CURVE('',#106,(#111,#118),.PCURVE_S1.);
#106 = CIRCLE('',#107,0.275);
#107 = AXIS2_PLACEMENT_3D('',#108,#109,#110);
#108 = CARTESIAN_POINT('',(0.,0.,0.));
#109 = DIRECTION('',(0.,0.,1.));
#110 = DIRECTION('',(1.,0.,-0.));
#111 = PCURVE('',#57,#112);
#112 = DEFINITIONAL_REPRESENTATION('',(#113),#117);
#113 = LINE('',#114,#115);
#114 = CARTESIAN_POINT('',(0.,0.));
#115 = VECTOR('',#116,1.);
#116 = DIRECTION('',(1.,0.));
#117 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#118 = PCURVE('',#119,#124);
#119 = PLANE('',#120);
#120 = AXIS2_PLACEMENT_3D('',#121,#122,#123);
#121 = CARTESIAN_POINT('',(0.,0.,0.));
#122 = DIRECTION('',(0.,0.,1.));
#123 = DIRECTION('',(1.,0.,-0.));
#124 = DEFINITIONAL_REPRESENTATION('',(#125),#129);
#125 = CIRCLE('',#126,0.275);
#126 = AXIS2_PLACEMENT_2D('',#127,#128);
#127 = CARTESIAN_POINT('',(0.,0.));
#128 = DIRECTION('',(1.,0.));
#129 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#130 = ORIENTED_EDGE('',*,*,#81,.T.);
#131 = ADVANCED_FACE('',(#132),#69,.T.);
#132 = FACE_BOUND('',#133,.T.);
#133 = EDGE_LOOP('',(#134));
#134 = ORIENTED_EDGE('',*,*,#47,.T.);
#135 = ADVANCED_FACE('',(#136),#119,.F.);
#136 = FACE_BOUND('',#137,.T.);
#137 = EDGE_LOOP('',(#138));
#138 = ORIENTED_EDGE('',*,*,#104,.F.);
#139 = ( GEOMETRIC_REPRESENTATION_CONTEXT(3) 
GLOBAL_UNCERTAINTY_ASSIGNED_CONTEXT((#143)) GLOBAL_UNIT_ASSIGNED_CONTEXT
((#140,#141,#142)) REPRESENTATION_CONTEXT('Context #1',
  '3D Context with UNIT and UNCERTAINTY') );
#140 = ( LENGTH_UNIT() NAMED_UNIT(*) SI_UNIT(.MILLI.,.METRE.) );
#141 = ( NAMED_UNIT(*) PLANE_ANGLE_UNIT() SI_UNIT($,.RADIAN.) );
#142 = ( NAMED_UNIT(*) SI_UNIT($,.STERADIAN.) SOLID_ANGLE_UNIT() );
#143 = UNCERTAINTY_MEASURE_WITH_UNIT(LENGTH_MEASURE(1.E-07),#140,
  'distance_accuracy_value','confusion accuracy');
#144 = CONTEXT_DEPENDENT_SHAPE_REPRESENTATION(#145,#147);
#145 = ( REPRESENTATION_RELATIONSHIP('','',#40,#10) 
REPRESENTATION_RELATIONSHIP_WITH_TRANSFORMATION(#146) 
SHAPE_REPRESENTATION_RELATIONSHIP() );
#146 = ITEM_DEFINED_TRANSFORMATION('','',#11,#15);
#147 = PRODUCT_DEFINITION_SHAPE('Placement','Placement of an item',#148
  );
#148 = NEXT_ASSEMBLY_USAGE_OCCURRENCE('33','=>[0:1:1:2]','',#5,#35,$);
#149 = PRODUCT_RELATED_PRODUCT_CATEGORY('part',$,(#37));
#150 = SHAPE_DEFINITION_REPRESENTATION(#151,#157);
#151 = PRODUCT_DEFINITION_SHAPE('','',#152);
#152 = PRODUCT_DEFINITION('design','',#153,#156);
#153 = PRODUCT_DEFINITION_FORMATION('','',#154);
#154 = PRODUCT('PC1-P1_Part2','PC1-P1_Part2','',(#155));
#155 = PRODUCT_CONTEXT('',#2,'mechanical');
#156 = PRODUCT_DEFINITION_CONTEXT('part definition',#2,'design');
#157 = ADVANCED_BREP_SHAPE_REPRESENTATION('',(#11,#158),#570);
#158 = MANIFOLD_SOLID_BREP('',#159);
#159 = CLOSED_SHELL('',(#160,#392,#442,#511,#537,#564));
#160 = ADVANCED_FACE('',(#161),#175,.T.);
#161 = FACE_BOUND('',#162,.T.);
#162 = EDGE_LOOP('',(#163,#193,#222,#268,#303,#343,#365,#366));
#163 = ORIENTED_EDGE('',*,*,#164,.T.);
#164 = EDGE_CURVE('',#165,#167,#169,.T.);
#165 = VERTEX_POINT('',#166);
#166 = CARTESIAN_POINT('',(51.798496,132.387429,1.909425));
#167 = VERTEX_POINT('',#168);
#168 = CARTESIAN_POINT('',(55.839947884327,132.387429,8.909425));
#169 = SEAM_CURVE('',#170,(#174,#186),.PCURVE_S1.);
#170 = LINE('',#171,#172);
#171 = CARTESIAN_POINT('',(51.798496,132.387429,1.909425));
#172 = VECTOR('',#173,1.);
#173 = DIRECTION('',(0.5,-1.224646799147E-16,0.866025403784));
#174 = PCURVE('',#175,#180);
#175 = CONICAL_SURFACE('',#176,0.275,0.523598775598);
#176 = AXIS2_PLACEMENT_3D('',#177,#178,#179);
#177 = CARTESIAN_POINT('',(51.523496,132.387429,1.909425));
#178 = DIRECTION('',(0.,0.,1.));
#179 = DIRECTION('',(1.,0.,0.));
#180 = DEFINITIONAL_REPRESENTATION('',(#181),#185);
#181 = LINE('',#182,#183);
#182 = CARTESIAN_POINT('',(6.28318530718,-0.));
#183 = VECTOR('',#184,1.);
#184 = DIRECTION('',(0.,1.));
#185 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#186 = PCURVE('',#175,#187);
#187 = DEFINITIONAL_REPRESENTATION('',(#188),#192);
#188 = LINE('',#189,#190);
#189 = CARTESIAN_POINT('',(0.,-0.));
#190 = VECTOR('',#191,1.);
#191 = DIRECTION('',(0.,1.));
#192 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#193 = ORIENTED_EDGE('',*,*,#194,.F.);
#194 = EDGE_CURVE('',#195,#167,#197,.T.);
#195 = VERTEX_POINT('',#196);
#196 = CARTESIAN_POINT('',(50.656396039216,128.15896656931,8.909425));
#197 = SURFACE_CURVE('',#198,(#203,#210),.PCURVE_S1.);
#198 = CIRCLE('',#199,4.316451884327);
#199 = AXIS2_PLACEMENT_3D('',#200,#201,#202);
#200 = CARTESIAN_POINT('',(51.523496,132.387429,8.909425));
#201 = DIRECTION('',(0.,0.,1.));
#202 = DIRECTION('',(1.,0.,0.));
#203 = PCURVE('',#175,#204);
#204 = DEFINITIONAL_REPRESENTATION('',(#205),#209);
#205 = LINE('',#206,#207);
#206 = CARTESIAN_POINT('',(0.,7.));
#207 = VECTOR('',#208,1.);
#208 = DIRECTION('',(1.,0.));
#209 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#210 = PCURVE('',#211,#216);
#211 = PLANE('',#212);
#212 = AXIS2_PLACEMENT_3D('',#213,#214,#215);
#213 = CARTESIAN_POINT('',(51.523496,132.387429,8.909425));
#214 = DIRECTION('',(0.,0.,1.));
#215 = DIRECTION('',(1.,0.,0.));
#216 = DEFINITIONAL_REPRESENTATION('',(#217),#221);
#217 = CIRCLE('',#218,4.316451884327);
#218 = AXIS2_PLACEMENT_2D('',#219,#220);
#219 = CARTESIAN_POINT('',(0.,0.));
#220 = DIRECTION('',(1.,0.));
#221 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#222 = ORIENTED_EDGE('',*,*,#223,.F.);
#223 = EDGE_CURVE('',#224,#195,#226,.T.);
#224 = VERTEX_POINT('',#225);
#225 = CARTESIAN_POINT('',(50.449147,131.893892,3.480893641506));
#226 = SURFACE_CURVE('',#227,(#232,#256),.PCURVE_S1.);
#227 = HYPERBOLA('',#228,1.905330103751,1.100042848296);
#228 = AXIS2_PLACEMENT_3D('',#229,#230,#231);
#229 = CARTESIAN_POINT('',(50.425142811532,132.32648196176,
    1.433111027919));
#230 = DIRECTION('',(0.998464005442,5.540424023454E-02,0.));
#231 = DIRECTION('',(-0.,0.,1.));
#232 = PCURVE('',#175,#233);
#233 = DEFINITIONAL_REPRESENTATION('',(#234),#255);
#234 = B_SPLINE_CURVE_WITH_KNOTS('',7,(#235,#236,#237,#238,#239,#240,
    #241,#242,#243,#244,#245,#246,#247,#248,#249,#250,#251,#252,#253,
    #254),.UNSPECIFIED.,.F.,.F.,(8,6,6,8),(-0.503133333129,
    0.133546109649,1.088565273816,2.043584437983),.UNSPECIFIED.);
#235 = CARTESIAN_POINT('',(2.713867514344,1.675307233699));
#236 = CARTESIAN_POINT('',(2.794410402888,1.584389825603));
#237 = CARTESIAN_POINT('',(2.878924084936,1.514238670408));
#238 = CARTESIAN_POINT('',(2.966788373961,1.46362530626));
#239 = CARTESIAN_POINT('',(3.057093257368,1.431742081853));
#240 = CARTESIAN_POINT('',(3.148608150206,1.418160979984));
#241 = CARTESIAN_POINT('',(3.24002706695,1.422819665025));
#242 = CARTESIAN_POINT('',(3.46539988072,1.48084991063));
#243 = CARTESIAN_POINT('',(3.59776449686,1.55741318328));
#244 = CARTESIAN_POINT('',(3.723319148411,1.676780601584));
#245 = CARTESIAN_POINT('',(3.838663499098,1.841912563017));
#246 = CARTESIAN_POINT('',(3.942772195366,2.057760448314));
#247 = CARTESIAN_POINT('',(4.035750154341,2.331612026611));
#248 = CARTESIAN_POINT('',(4.200785911388,3.016132387337));
#249 = CARTESIAN_POINT('',(4.272844302094,3.426801109378));
#250 = CARTESIAN_POINT('',(4.335115360032,3.916284766459));
#251 = CARTESIAN_POINT('',(4.388847875973,4.498104636899));
#252 = CARTESIAN_POINT('',(4.43523999271,5.189706233181));
#253 = CARTESIAN_POINT('',(4.475360840532,6.013676289772));
#254 = CARTESIAN_POINT('',(4.510130204342,7.));
#255 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#256 = PCURVE('',#257,#262);
#257 = PLANE('',#258);
#258 = AXIS2_PLACEMENT_3D('',#259,#260,#261);
#259 = CARTESIAN_POINT('',(51.008936,121.80569,28.));
#260 = DIRECTION('',(0.998464005442,5.540424023454E-02,0.));
#261 = DIRECTION('',(-5.540424023454E-02,0.998464005442,0.));
#262 = DEFINITIONAL_REPRESENTATION('',(#263),#267);
#263 = ( BOUNDED_CURVE() B_SPLINE_CURVE(2,(#264,#265,#266),
.UNSPECIFIED.,.F.,.F.) B_SPLINE_CURVE_WITH_KNOTS((3,3),(-0.503133333129,
2.043584437983),.PIECEWISE_BEZIER_KNOTS.) CURVE() 
GEOMETRIC_REPRESENTATION_ITEM() RATIONAL_B_SPLINE_CURVE((1.,
1.926361545493,1.)) REPRESENTATION_ITEM('') );
#264 = CARTESIAN_POINT('',(11.114093386955,-24.4152677663));
#265 = CARTESIAN_POINT('',(10.052344930476,-25.2696273756));
#266 = CARTESIAN_POINT('',(6.36305017976,-19.090575));
#267 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#268 = ORIENTED_EDGE('',*,*,#269,.F.);
#269 = EDGE_CURVE('',#270,#224,#272,.T.);
#270 = VERTEX_POINT('',#271);
#271 = CARTESIAN_POINT('',(48.301331,131.800116,7.106016063399));
#272 = SURFACE_CURVE('',#273,(#278,#291),.PCURVE_S1.);
#273 = HYPERBOLA('',#274,0.772849124257,0.446204649932);
#274 = AXIS2_PLACEMENT_3D('',#275,#276,#277);
#275 = CARTESIAN_POINT('',(51.542959241468,131.94164904099,
    1.433111027919));
#276 = DIRECTION('',(-4.361953975718E-02,0.999048214928,0.));
#277 = DIRECTION('',(0.,0.,1.));
#278 = PCURVE('',#175,#279);
#279 = DEFINITIONAL_REPRESENTATION('',(#280),#290);
#280 = B_SPLINE_CURVE_WITH_KNOTS('',8,(#281,#282,#283,#284,#285,#286,
    #287,#288,#289),.UNSPECIFIED.,.F.,.F.,(9,9),(-2.745440166929,
    -1.43099478095),.PIECEWISE_BEZIER_KNOTS.);
#281 = CARTESIAN_POINT('',(3.313489956121,5.565699335595));
#282 = CARTESIAN_POINT('',(3.33450670819,4.581117154854));
#283 = CARTESIAN_POINT('',(3.359437457884,3.782949025608));
#284 = CARTESIAN_POINT('',(3.389119091478,3.130692278326));
#285 = CARTESIAN_POINT('',(3.424588514988,2.594578163818));
#286 = CARTESIAN_POINT('',(3.467147067719,2.152081677413));
#287 = CARTESIAN_POINT('',(3.518376414741,1.78594375289));
#288 = CARTESIAN_POINT('',(3.580244760661,1.482825996736));
#289 = CARTESIAN_POINT('',(3.654558784415,1.232428056211));
#290 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#291 = PCURVE('',#292,#297);
#292 = PLANE('',#293);
#293 = AXIS2_PLACEMENT_3D('',#294,#295,#296);
#294 = CARTESIAN_POINT('',(50.449147,131.893892,28.));
#295 = DIRECTION('',(-4.361953975718E-02,0.999048214928,0.));
#296 = DIRECTION('',(-0.999048214928,-4.361953975718E-02,0.));
#297 = DEFINITIONAL_REPRESENTATION('',(#298),#302);
#298 = ( BOUNDED_CURVE() B_SPLINE_CURVE(2,(#299,#300,#301),
.UNSPECIFIED.,.F.,.F.) B_SPLINE_CURVE_WITH_KNOTS((3,3),(-2.745440166929,
-1.43099478095),.PIECEWISE_BEZIER_KNOTS.) CURVE() 
GEOMETRIC_REPRESENTATION_ITEM() RATIONAL_B_SPLINE_CURVE((1.,
1.223857530827,1.)) REPRESENTATION_ITEM('') );
#299 = CARTESIAN_POINT('',(2.36484842743,-20.5248756644));
#300 = CARTESIAN_POINT('',(0.35376762307,-23.97955611839));
#301 = CARTESIAN_POINT('',(-0.214986220675,-24.85814694378));
#302 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#303 = ORIENTED_EDGE('',*,*,#304,.F.);
#304 = EDGE_CURVE('',#305,#270,#307,.T.);
#305 = VERTEX_POINT('',#306);
#306 = CARTESIAN_POINT('',(47.95448794827,134.81517247829,8.909425));
#307 = SURFACE_CURVE('',#308,(#313,#331),.PCURVE_S1.);
#308 = HYPERBOLA('',#309,5.660643570614,3.268174089281);
#309 = AXIS2_PLACEMENT_3D('',#310,#311,#312);
#310 = CARTESIAN_POINT('',(48.276734331848,132.01393127837,
    1.433111027919));
#311 = DIRECTION('',(0.993448200572,0.11428330053,0.));
#312 = DIRECTION('',(-0.,0.,1.));
#313 = PCURVE('',#175,#314);
#314 = DEFINITIONAL_REPRESENTATION('',(#315),#330);
#315 = B_SPLINE_CURVE_WITH_KNOTS('',7,(#316,#317,#318,#319,#320,#321,
    #322,#323,#324,#325,#326,#327,#328,#329),.UNSPECIFIED.,.F.,.F.,(8,6,
    8),(-0.780944392204,-0.182155649481,0.416633093242),.UNSPECIFIED.);
#316 = CARTESIAN_POINT('',(2.544259286126,7.));
#317 = CARTESIAN_POINT('',(2.609026298689,6.582225919458));
#318 = CARTESIAN_POINT('',(2.678016167579,6.228275987451));
#319 = CARTESIAN_POINT('',(2.751112394062,5.933157545508));
#320 = CARTESIAN_POINT('',(2.828025030325,5.693020669904));
#321 = CARTESIAN_POINT('',(2.908199262521,5.505013098957));
#322 = CARTESIAN_POINT('',(2.990828163141,5.367193283594));
#323 = CARTESIAN_POINT('',(3.159111062272,5.18980983877));
#324 = CARTESIAN_POINT('',(3.244765336425,5.150246145181));
#325 = CARTESIAN_POINT('',(3.330992615756,5.158750501645));
#326 = CARTESIAN_POINT('',(3.416720508113,5.215143523923));
#327 = CARTESIAN_POINT('',(3.500874720768,5.320095318697));
#328 = CARTESIAN_POINT('',(3.582585389355,5.475144888455));
#329 = CARTESIAN_POINT('',(3.66120393592,5.682773700114));
#330 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#331 = PCURVE('',#332,#337);
#332 = PLANE('',#333);
#333 = AXIS2_PLACEMENT_3D('',#334,#335,#336);
#334 = CARTESIAN_POINT('',(48.301331,131.800116,28.));
#335 = DIRECTION('',(0.993448200572,0.11428330053,0.));
#336 = DIRECTION('',(-0.11428330053,0.993448200572,0.));
#337 = DEFINITIONAL_REPRESENTATION('',(#338),#342);
#338 = ( BOUNDED_CURVE() B_SPLINE_CURVE(2,(#339,#340,#341),
.UNSPECIFIED.,.F.,.F.) B_SPLINE_CURVE_WITH_KNOTS((3,3),(-0.780944392204,
0.416633093242),.PIECEWISE_BEZIER_KNOTS.) CURVE() 
GEOMETRIC_REPRESENTATION_ITEM() RATIONAL_B_SPLINE_CURVE((1.,
1.184694936394,1.)) REPRESENTATION_ITEM('') );
#339 = CARTESIAN_POINT('',(3.034940801702,-19.090575));
#340 = CARTESIAN_POINT('',(0.720514972162,-21.70925401773));
#341 = CARTESIAN_POINT('',(-1.186140051712,-20.40780129988));
#342 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#343 = ORIENTED_EDGE('',*,*,#344,.F.);
#344 = EDGE_CURVE('',#167,#305,#345,.T.);
#345 = SURFACE_CURVE('',#346,(#351,#358),.PCURVE_S1.);
#346 = CIRCLE('',#347,4.316451884327);
#347 = AXIS2_PLACEMENT_3D('',#348,#349,#350);
#348 = CARTESIAN_POINT('',(51.523496,132.387429,8.909425));
#349 = DIRECTION('',(0.,0.,1.));
#350 = DIRECTION('',(1.,0.,0.));
#351 = PCURVE('',#175,#352);
#352 = DEFINITIONAL_REPRESENTATION('',(#353),#357);
#353 = LINE('',#354,#355);
#354 = CARTESIAN_POINT('',(0.,7.));
#355 = VECTOR('',#356,1.);
#356 = DIRECTION('',(1.,0.));
#357 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#358 = PCURVE('',#211,#359);
#359 = DEFINITIONAL_REPRESENTATION('',(#360),#364);
#360 = CIRCLE('',#361,4.316451884327);
#361 = AXIS2_PLACEMENT_2D('',#362,#363);
#362 = CARTESIAN_POINT('',(0.,0.));
#363 = DIRECTION('',(1.,0.));
#364 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#365 = ORIENTED_EDGE('',*,*,#164,.F.);
#366 = ORIENTED_EDGE('',*,*,#367,.T.);
#367 = EDGE_CURVE('',#165,#165,#368,.T.);
#368 = SURFACE_CURVE('',#369,(#374,#381),.PCURVE_S1.);
#369 = CIRCLE('',#370,0.275);
#370 = AXIS2_PLACEMENT_3D('',#371,#372,#373);
#371 = CARTESIAN_POINT('',(51.523496,132.387429,1.909425));
#372 = DIRECTION('',(0.,0.,1.));
#373 = DIRECTION('',(1.,0.,0.));
#374 = PCURVE('',#175,#375);
#375 = DEFINITIONAL_REPRESENTATION('',(#376),#380);
#376 = LINE('',#377,#378);
#377 = CARTESIAN_POINT('',(0.,0.));
#378 = VECTOR('',#379,1.);
#379 = DIRECTION('',(1.,0.));
#380 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#381 = PCURVE('',#382,#387);
#382 = CYLINDRICAL_SURFACE('',#383,0.275);
#383 = AXIS2_PLACEMENT_3D('',#384,#385,#386);
#384 = CARTESIAN_POINT('',(51.523496,132.387429,1.909425));
#385 = DIRECTION('',(0.,0.,1.));
#386 = DIRECTION('',(1.,0.,0.));
#387 = DEFINITIONAL_REPRESENTATION('',(#388),#391);
#388 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#389,#390),.UNSPECIFIED.,.F.,.F.,
  (2,2),(0.,6.28318530718),.PIECEWISE_BEZIER_KNOTS.);
#389 = CARTESIAN_POINT('',(0.,0.));
#390 = CARTESIAN_POINT('',(6.28318530718,0.));
#391 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#392 = ADVANCED_FACE('',(#393),#382,.F.);
#393 = FACE_BOUND('',#394,.F.);
#394 = EDGE_LOOP('',(#395,#419,#440,#441));
#395 = ORIENTED_EDGE('',*,*,#396,.F.);
#396 = EDGE_CURVE('',#397,#397,#399,.T.);
#397 = VERTEX_POINT('',#398);
#398 = CARTESIAN_POINT('',(51.798496,132.387429,8.909425));
#399 = SURFACE_CURVE('',#400,(#405,#412),.PCURVE_S1.);
#400 = CIRCLE('',#401,0.275);
#401 = AXIS2_PLACEMENT_3D('',#402,#403,#404);
#402 = CARTESIAN_POINT('',(51.523496,132.387429,8.909425));
#403 = DIRECTION('',(0.,0.,1.));
#404 = DIRECTION('',(1.,0.,0.));
#405 = PCURVE('',#382,#406);
#406 = DEFINITIONAL_REPRESENTATION('',(#407),#411);
#407 = LINE('',#408,#409);
#408 = CARTESIAN_POINT('',(0.,7.));
#409 = VECTOR('',#410,1.);
#410 = DIRECTION('',(1.,0.));
#411 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#412 = PCURVE('',#211,#413);
#413 = DEFINITIONAL_REPRESENTATION('',(#414),#418);
#414 = CIRCLE('',#415,0.275);
#415 = AXIS2_PLACEMENT_2D('',#416,#417);
#416 = CARTESIAN_POINT('',(0.,0.));
#417 = DIRECTION('',(1.,0.));
#418 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#419 = ORIENTED_EDGE('',*,*,#420,.F.);
#420 = EDGE_CURVE('',#165,#397,#421,.T.);
#421 = SEAM_CURVE('',#422,(#426,#433),.PCURVE_S1.);
#422 = LINE('',#423,#424);
#423 = CARTESIAN_POINT('',(51.798496,132.387429,1.909425));
#424 = VECTOR('',#425,1.);
#425 = DIRECTION('',(0.,0.,1.));
#426 = PCURVE('',#382,#427);
#427 = DEFINITIONAL_REPRESENTATION('',(#428),#432);
#428 = LINE('',#429,#430);
#429 = CARTESIAN_POINT('',(6.28318530718,-0.));
#430 = VECTOR('',#431,1.);
#431 = DIRECTION('',(0.,1.));
#432 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#433 = PCURVE('',#382,#434);
#434 = DEFINITIONAL_REPRESENTATION('',(#435),#439);
#435 = LINE('',#436,#437);
#436 = CARTESIAN_POINT('',(0.,-0.));
#437 = VECTOR('',#438,1.);
#438 = DIRECTION('',(0.,1.));
#439 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#440 = ORIENTED_EDGE('',*,*,#367,.T.);
#441 = ORIENTED_EDGE('',*,*,#420,.T.);
#442 = ADVANCED_FACE('',(#443,#508),#211,.T.);
#443 = FACE_BOUND('',#444,.T.);
#444 = EDGE_LOOP('',(#445,#446,#447,#468,#489));
#445 = ORIENTED_EDGE('',*,*,#194,.T.);
#446 = ORIENTED_EDGE('',*,*,#344,.T.);
#447 = ORIENTED_EDGE('',*,*,#448,.F.);
#448 = EDGE_CURVE('',#449,#305,#451,.T.);
#449 = VERTEX_POINT('',#450);
#450 = CARTESIAN_POINT('',(48.301331,131.800116,8.909425));
#451 = SURFACE_CURVE('',#452,(#456,#462),.PCURVE_S1.);
#452 = LINE('',#453,#454);
#453 = CARTESIAN_POINT('',(48.289032665924,131.90702363918,8.909425));
#454 = VECTOR('',#455,1.);
#455 = DIRECTION('',(-0.11428330053,0.993448200572,0.));
#456 = PCURVE('',#211,#457);
#457 = DEFINITIONAL_REPRESENTATION('',(#458),#461);
#458 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#459,#460),.UNSPECIFIED.,.F.,.F.,
  (2,2),(-0.107612695986,4.828492559933),.PIECEWISE_BEZIER_KNOTS.);
#459 = CARTESIAN_POINT('',(-3.222165,-0.587313));
#460 = CARTESIAN_POINT('',(-3.786279400408,4.316451884327));
#461 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#462 = PCURVE('',#332,#463);
#463 = DEFINITIONAL_REPRESENTATION('',(#464),#467);
#464 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#465,#466),.UNSPECIFIED.,.F.,.F.,
  (2,2),(-0.107612695986,4.828492559933),.PIECEWISE_BEZIER_KNOTS.);
#465 = CARTESIAN_POINT('',(0.,-19.090575));
#466 = CARTESIAN_POINT('',(4.936105255919,-19.090575));
#467 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#468 = ORIENTED_EDGE('',*,*,#469,.F.);
#469 = EDGE_CURVE('',#470,#449,#472,.T.);
#470 = VERTEX_POINT('',#471);
#471 = CARTESIAN_POINT('',(50.449147,131.893892,8.909425));
#472 = SURFACE_CURVE('',#473,(#477,#483),.PCURVE_S1.);
#473 = LINE('',#474,#475);
#474 = CARTESIAN_POINT('',(50.996053120734,131.91777052049,8.909425));
#475 = VECTOR('',#476,1.);
#476 = DIRECTION('',(-0.999048214928,-4.361953975718E-02,0.));
#477 = PCURVE('',#211,#478);
#478 = DEFINITIONAL_REPRESENTATION('',(#479),#482);
#479 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#480,#481),.UNSPECIFIED.,.F.,.F.,
  (2,2),(0.547427153727,2.697289360481),.PIECEWISE_BEZIER_KNOTS.);
#480 = CARTESIAN_POINT('',(-1.074349,-0.493537));
#481 = CARTESIAN_POINT('',(-3.222165,-0.587313));
#482 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#483 = PCURVE('',#292,#484);
#484 = DEFINITIONAL_REPRESENTATION('',(#485),#488);
#485 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#486,#487),.UNSPECIFIED.,.F.,.F.,
  (2,2),(0.547427153727,2.697289360481),.PIECEWISE_BEZIER_KNOTS.);
#486 = CARTESIAN_POINT('',(0.,-19.090575));
#487 = CARTESIAN_POINT('',(2.149862206755,-19.090575));
#488 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#489 = ORIENTED_EDGE('',*,*,#490,.F.);
#490 = EDGE_CURVE('',#195,#470,#491,.T.);
#491 = SURFACE_CURVE('',#492,(#496,#502),.PCURVE_S1.);
#492 = LINE('',#493,#494);
#493 = CARTESIAN_POINT('',(50.717039405766,127.06608598088,8.909425));
#494 = VECTOR('',#495,1.);
#495 = DIRECTION('',(-5.540424023454E-02,0.998464005442,0.));
#496 = PCURVE('',#211,#497);
#497 = DEFINITIONAL_REPRESENTATION('',(#498),#501);
#498 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#499,#500),.UNSPECIFIED.,.F.,.F.,
  (2,2),(1.00643701657,4.835232910553),.PIECEWISE_BEZIER_KNOTS.);
#499 = CARTESIAN_POINT('',(-0.862217472481,-4.316451884327));
#500 = CARTESIAN_POINT('',(-1.074349,-0.493537));
#501 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#502 = PCURVE('',#257,#503);
#503 = DEFINITIONAL_REPRESENTATION('',(#504),#507);
#504 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#505,#506),.UNSPECIFIED.,.F.,.F.,
  (2,2),(1.00643701657,4.835232910553),.PIECEWISE_BEZIER_KNOTS.);
#505 = CARTESIAN_POINT('',(6.274925366886,-19.090575));
#506 = CARTESIAN_POINT('',(10.103721260868,-19.090575));
#507 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#508 = FACE_BOUND('',#509,.T.);
#509 = EDGE_LOOP('',(#510));
#510 = ORIENTED_EDGE('',*,*,#396,.F.);
#511 = ADVANCED_FACE('',(#512),#332,.F.);
#512 = FACE_BOUND('',#513,.F.);
#513 = EDGE_LOOP('',(#514,#515,#536));
#514 = ORIENTED_EDGE('',*,*,#448,.F.);
#515 = ORIENTED_EDGE('',*,*,#516,.T.);
#516 = EDGE_CURVE('',#449,#270,#517,.T.);
#517 = SURFACE_CURVE('',#518,(#522,#529),.PCURVE_S1.);
#518 = LINE('',#519,#520);
#519 = CARTESIAN_POINT('',(48.301331,131.800116,28.));
#520 = VECTOR('',#521,1.);
#521 = DIRECTION('',(0.,0.,-1.));
#522 = PCURVE('',#332,#523);
#523 = DEFINITIONAL_REPRESENTATION('',(#524),#528);
#524 = LINE('',#525,#526);
#525 = CARTESIAN_POINT('',(0.,0.));
#526 = VECTOR('',#527,1.);
#527 = DIRECTION('',(0.,-1.));
#528 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#529 = PCURVE('',#292,#530);
#530 = DEFINITIONAL_REPRESENTATION('',(#531),#535);
#531 = LINE('',#532,#533);
#532 = CARTESIAN_POINT('',(2.149862206755,0.));
#533 = VECTOR('',#534,1.);
#534 = DIRECTION('',(-0.,-1.));
#535 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#536 = ORIENTED_EDGE('',*,*,#304,.F.);
#537 = ADVANCED_FACE('',(#538),#292,.F.);
#538 = FACE_BOUND('',#539,.F.);
#539 = EDGE_LOOP('',(#540,#541,#562,#563));
#540 = ORIENTED_EDGE('',*,*,#469,.F.);
#541 = ORIENTED_EDGE('',*,*,#542,.T.);
#542 = EDGE_CURVE('',#470,#224,#543,.T.);
#543 = SURFACE_CURVE('',#544,(#548,#555),.PCURVE_S1.);
#544 = LINE('',#545,#546);
#545 = CARTESIAN_POINT('',(50.449147,131.893892,28.));
#546 = VECTOR('',#547,1.);
#547 = DIRECTION('',(0.,0.,-1.));
#548 = PCURVE('',#292,#549);
#549 = DEFINITIONAL_REPRESENTATION('',(#550),#554);
#550 = LINE('',#551,#552);
#551 = CARTESIAN_POINT('',(0.,0.));
#552 = VECTOR('',#553,1.);
#553 = DIRECTION('',(-0.,-1.));
#554 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#555 = PCURVE('',#257,#556);
#556 = DEFINITIONAL_REPRESENTATION('',(#557),#561);
#557 = LINE('',#558,#559);
#558 = CARTESIAN_POINT('',(10.103721260868,0.));
#559 = VECTOR('',#560,1.);
#560 = DIRECTION('',(0.,-1.));
#561 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#562 = ORIENTED_EDGE('',*,*,#269,.F.);
#563 = ORIENTED_EDGE('',*,*,#516,.F.);
#564 = ADVANCED_FACE('',(#565),#257,.F.);
#565 = FACE_BOUND('',#566,.F.);
#566 = EDGE_LOOP('',(#567,#568,#569));
#567 = ORIENTED_EDGE('',*,*,#542,.F.);
#568 = ORIENTED_EDGE('',*,*,#490,.F.);
#569 = ORIENTED_EDGE('',*,*,#223,.F.);
#570 = ( GEOMETRIC_REPRESENTATION_CONTEXT(3) 
GLOBAL_UNCERTAINTY_ASSIGNED_CONTEXT((#574)) GLOBAL_UNIT_ASSIGNED_CONTEXT
((#571,#572,#573)) REPRESENTATION_CONTEXT('Context #1',
  '3D Context with UNIT and UNCERTAINTY') );
#571 = ( LENGTH_UNIT() NAMED_UNIT(*) SI_UNIT(.MILLI.,.METRE.) );
#572 = ( NAMED_UNIT(*) PLANE_ANGLE_UNIT() SI_UNIT($,.RADIAN.) );
#573 = ( NAMED_UNIT(*) SI_UNIT($,.STERADIAN.) SOLID_ANGLE_UNIT() );
#574 = UNCERTAINTY_MEASURE_WITH_UNIT(LENGTH_MEASURE(1.E-07),#571,
  'distance_accuracy_value','confusion accuracy');
#575 = CONTEXT_DEPENDENT_SHAPE_REPRESENTATION(#576,#578);
#576 = ( REPRESENTATION_RELATIONSHIP('','',#157,#10) 
REPRESENTATION_RELATIONSHIP_WITH_TRANSFORMATION(#577) 
SHAPE_REPRESENTATION_RELATIONSHIP() );
#577 = ITEM_DEFINED_TRANSFORMATION('','',#11,#19);
#578 = PRODUCT_DEFINITION_SHAPE('Placement','Placement of an item',#579
  );
#579 = NEXT_ASSEMBLY_USAGE_OCCURRENCE('34','=>[0:1:1:3]','',#5,#152,$);
#580 = PRODUCT_RELATED_PRODUCT_CATEGORY('part',$,(#154));
#581 = SHAPE_DEFINITION_REPRESENTATION(#582,#588);
#582 = PRODUCT_DEFINITION_SHAPE('','',#583);
#583 = PRODUCT_DEFINITION('design','',#584,#587);
#584 = PRODUCT_DEFINITION_FORMATION('','',#585);
#585 = PRODUCT('PC1-P1_Part3','PC1-P1_Part3','',(#586));
#586 = PRODUCT_CONTEXT('',#2,'mechanical');
#587 = PRODUCT_DEFINITION_CONTEXT('part definition',#2,'design');
#588 = ADVANCED_BREP_SHAPE_REPRESENTATION('',(#11,#589),#993);
#589 = MANIFOLD_SOLID_BREP('',#590);
#590 = CLOSED_SHELL('',(#591,#713,#814,#861,#932,#959,#966));
#591 = ADVANCED_FACE('',(#592),#607,.T.);
#592 = FACE_BOUND('',#593,.T.);
#593 = EDGE_LOOP('',(#594,#630,#656,#689));
#594 = ORIENTED_EDGE('',*,*,#595,.T.);
#595 = EDGE_CURVE('',#596,#598,#600,.T.);
#596 = VERTEX_POINT('',#597);
#597 = CARTESIAN_POINT('',(50.656396039216,128.15896656931,8.909425));
#598 = VERTEX_POINT('',#599);
#599 = CARTESIAN_POINT('',(47.95448794827,134.81517247829,8.909425));
#600 = SURFACE_CURVE('',#601,(#606,#618),.PCURVE_S1.);
#601 = CIRCLE('',#602,4.316451884327);
#602 = AXIS2_PLACEMENT_3D('',#603,#604,#605);
#603 = CARTESIAN_POINT('',(51.523496,132.387429,8.909425));
#604 = DIRECTION('',(0.,0.,1.));
#605 = DIRECTION('',(-0.200882573007,-0.979615328515,0.));
#606 = PCURVE('',#607,#612);
#607 = CYLINDRICAL_SURFACE('',#608,4.316451884327);
#608 = AXIS2_PLACEMENT_3D('',#609,#610,#611);
#609 = CARTESIAN_POINT('',(51.523496,132.387429,8.909425));
#610 = DIRECTION('',(0.,0.,1.));
#611 = DIRECTION('',(1.,0.,0.));
#612 = DEFINITIONAL_REPRESENTATION('',(#613),#617);
#613 = LINE('',#614,#615);
#614 = CARTESIAN_POINT('',(4.510130204342,0.));
#615 = VECTOR('',#616,1.);
#616 = DIRECTION('',(1.,0.));
#617 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#618 = PCURVE('',#619,#624);
#619 = PLANE('',#620);
#620 = AXIS2_PLACEMENT_3D('',#621,#622,#623);
#621 = CARTESIAN_POINT('',(51.523496,132.387429,8.909425));
#622 = DIRECTION('',(0.,0.,1.));
#623 = DIRECTION('',(1.,0.,0.));
#624 = DEFINITIONAL_REPRESENTATION('',(#625),#629);
#625 = CIRCLE('',#626,4.316451884327);
#626 = AXIS2_PLACEMENT_2D('',#627,#628);
#627 = CARTESIAN_POINT('',(0.,0.));
#628 = DIRECTION('',(-0.200882573007,-0.979615328515));
#629 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#630 = ORIENTED_EDGE('',*,*,#631,.T.);
#631 = EDGE_CURVE('',#598,#632,#634,.T.);
#632 = VERTEX_POINT('',#633);
#633 = CARTESIAN_POINT('',(47.95448794827,134.81517247829,18.));
#634 = SURFACE_CURVE('',#635,(#639,#645),.PCURVE_S1.);
#635 = LINE('',#636,#637);
#636 = CARTESIAN_POINT('',(47.95448794827,134.81517247829,8.909425));
#637 = VECTOR('',#638,1.);
#638 = DIRECTION('',(0.,0.,1.));
#639 = PCURVE('',#607,#640);
#640 = DEFINITIONAL_REPRESENTATION('',(#641),#644);
#641 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#642,#643),.UNSPECIFIED.,.F.,.F.,
  (2,2),(0.,9.090575),.PIECEWISE_BEZIER_KNOTS.);
#642 = CARTESIAN_POINT('',(8.827444593306,0.));
#643 = CARTESIAN_POINT('',(8.827444593306,9.090575));
#644 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#645 = PCURVE('',#646,#651);
#646 = PLANE('',#647);
#647 = AXIS2_PLACEMENT_3D('',#648,#649,#650);
#648 = CARTESIAN_POINT('',(48.301331,131.800116,28.));
#649 = DIRECTION('',(0.993448200572,0.11428330053,0.));
#650 = DIRECTION('',(-0.11428330053,0.993448200572,0.));
#651 = DEFINITIONAL_REPRESENTATION('',(#652),#655);
#652 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#653,#654),.UNSPECIFIED.,.F.,.F.,
  (2,2),(-5.999999999062E-07,9.0905756),.PIECEWISE_BEZIER_KNOTS.);
#653 = CARTESIAN_POINT('',(3.034940801702,-19.0905756));
#654 = CARTESIAN_POINT('',(3.034940801702,-9.9999994));
#655 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#656 = ORIENTED_EDGE('',*,*,#657,.T.);
#657 = EDGE_CURVE('',#632,#658,#660,.T.);
#658 = VERTEX_POINT('',#659);
#659 = CARTESIAN_POINT('',(50.656396039216,128.15896656931,18.));
#660 = SURFACE_CURVE('',#661,(#666,#673),.PCURVE_S1.);
#661 = CIRCLE('',#662,4.316451884327);
#662 = AXIS2_PLACEMENT_3D('',#663,#664,#665);
#663 = CARTESIAN_POINT('',(51.523496,132.387429,18.));
#664 = DIRECTION('',(0.,0.,-1.));
#665 = DIRECTION('',(-0.826838372666,0.562439601634,0.));
#666 = PCURVE('',#607,#667);
#667 = DEFINITIONAL_REPRESENTATION('',(#668),#672);
#668 = LINE('',#669,#670);
#669 = CARTESIAN_POINT('',(8.827444593306,9.090575));
#670 = VECTOR('',#671,1.);
#671 = DIRECTION('',(-1.,-0.));
#672 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#673 = PCURVE('',#674,#679);
#674 = PLANE('',#675);
#675 = AXIS2_PLACEMENT_3D('',#676,#677,#678);
#676 = CARTESIAN_POINT('',(51.523496,132.387429,18.));
#677 = DIRECTION('',(0.,0.,1.));
#678 = DIRECTION('',(1.,0.,0.));
#679 = DEFINITIONAL_REPRESENTATION('',(#680),#688);
#680 = ( BOUNDED_CURVE() B_SPLINE_CURVE(2,(#681,#682,#683,#684,#685,#686
,#687),.UNSPECIFIED.,.T.,.F.) B_SPLINE_CURVE_WITH_KNOTS((1,2,2,2,2,1),(
    -2.094395102393,0.,2.094395102393,4.188790204786,6.28318530718,
8.377580409573),.UNSPECIFIED.) CURVE() GEOMETRIC_REPRESENTATION_ITEM() 
RATIONAL_B_SPLINE_CURVE((1.,0.5,1.,0.5,1.,0.5,1.)) REPRESENTATION_ITEM(
  '') );
#681 = CARTESIAN_POINT('',(-3.56900805173,2.427743478294));
#682 = CARTESIAN_POINT('',(0.635967000418,8.609446756513));
#683 = CARTESIAN_POINT('',(3.886991551939,1.876979899963));
#684 = CARTESIAN_POINT('',(7.138016103461,-4.855486956587));
#685 = CARTESIAN_POINT('',(-0.317983500209,-4.304723378257));
#686 = CARTESIAN_POINT('',(-7.773983103879,-3.753959799926));
#687 = CARTESIAN_POINT('',(-3.56900805173,2.427743478294));
#688 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#689 = ORIENTED_EDGE('',*,*,#690,.F.);
#690 = EDGE_CURVE('',#596,#658,#691,.T.);
#691 = SURFACE_CURVE('',#692,(#696,#702),.PCURVE_S1.);
#692 = LINE('',#693,#694);
#693 = CARTESIAN_POINT('',(50.656396039216,128.15896656931,8.909425));
#694 = VECTOR('',#695,1.);
#695 = DIRECTION('',(0.,0.,1.));
#696 = PCURVE('',#607,#697);
#697 = DEFINITIONAL_REPRESENTATION('',(#698),#701);
#698 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#699,#700),.UNSPECIFIED.,.F.,.F.,
  (2,2),(0.,9.090575),.PIECEWISE_BEZIER_KNOTS.);
#699 = CARTESIAN_POINT('',(4.510130204342,0.));
#700 = CARTESIAN_POINT('',(4.510130204342,9.090575));
#701 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#702 = PCURVE('',#703,#708);
#703 = PLANE('',#704);
#704 = AXIS2_PLACEMENT_3D('',#705,#706,#707);
#705 = CARTESIAN_POINT('',(51.008936,121.80569,28.));
#706 = DIRECTION('',(0.998464005442,5.540424023454E-02,0.));
#707 = DIRECTION('',(-5.540424023454E-02,0.998464005442,0.));
#708 = DEFINITIONAL_REPRESENTATION('',(#709),#712);
#709 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#710,#711),.UNSPECIFIED.,.F.,.F.,
  (2,2),(-5.999999999062E-07,9.0905756),.PIECEWISE_BEZIER_KNOTS.);
#710 = CARTESIAN_POINT('',(6.36305017976,-19.0905756));
#711 = CARTESIAN_POINT('',(6.36305017976,-9.9999994));
#712 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#713 = ADVANCED_FACE('',(#714,#783),#619,.F.);
#714 = FACE_BOUND('',#715,.F.);
#715 = EDGE_LOOP('',(#716,#717,#738,#764));
#716 = ORIENTED_EDGE('',*,*,#595,.T.);
#717 = ORIENTED_EDGE('',*,*,#718,.F.);
#718 = EDGE_CURVE('',#719,#598,#721,.T.);
#719 = VERTEX_POINT('',#720);
#720 = CARTESIAN_POINT('',(48.301331,131.800116,8.909425));
#721 = SURFACE_CURVE('',#722,(#726,#732),.PCURVE_S1.);
#722 = LINE('',#723,#724);
#723 = CARTESIAN_POINT('',(48.289032665924,131.90702363918,8.909425));
#724 = VECTOR('',#725,1.);
#725 = DIRECTION('',(-0.11428330053,0.993448200572,0.));
#726 = PCURVE('',#619,#727);
#727 = DEFINITIONAL_REPRESENTATION('',(#728),#731);
#728 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#729,#730),.UNSPECIFIED.,.F.,.F.,
  (2,2),(-0.107612695986,4.828492559933),.PIECEWISE_BEZIER_KNOTS.);
#729 = CARTESIAN_POINT('',(-3.222165,-0.587313));
#730 = CARTESIAN_POINT('',(-3.786279400408,4.316451884327));
#731 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#732 = PCURVE('',#646,#733);
#733 = DEFINITIONAL_REPRESENTATION('',(#734),#737);
#734 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#735,#736),.UNSPECIFIED.,.F.,.F.,
  (2,2),(-0.107612695986,4.828492559933),.PIECEWISE_BEZIER_KNOTS.);
#735 = CARTESIAN_POINT('',(0.,-19.090575));
#736 = CARTESIAN_POINT('',(4.936105255919,-19.090575));
#737 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#738 = ORIENTED_EDGE('',*,*,#739,.F.);
#739 = EDGE_CURVE('',#740,#719,#742,.T.);
#740 = VERTEX_POINT('',#741);
#741 = CARTESIAN_POINT('',(50.449147,131.893892,8.909425));
#742 = SURFACE_CURVE('',#743,(#747,#753),.PCURVE_S1.);
#743 = LINE('',#744,#745);
#744 = CARTESIAN_POINT('',(50.996053120734,131.91777052049,8.909425));
#745 = VECTOR('',#746,1.);
#746 = DIRECTION('',(-0.999048214928,-4.361953975718E-02,0.));
#747 = PCURVE('',#619,#748);
#748 = DEFINITIONAL_REPRESENTATION('',(#749),#752);
#749 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#750,#751),.UNSPECIFIED.,.F.,.F.,
  (2,2),(0.547427153727,2.697289360481),.PIECEWISE_BEZIER_KNOTS.);
#750 = CARTESIAN_POINT('',(-1.074349,-0.493537));
#751 = CARTESIAN_POINT('',(-3.222165,-0.587313));
#752 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#753 = PCURVE('',#754,#759);
#754 = PLANE('',#755);
#755 = AXIS2_PLACEMENT_3D('',#756,#757,#758);
#756 = CARTESIAN_POINT('',(50.449147,131.893892,28.));
#757 = DIRECTION('',(-4.361953975718E-02,0.999048214928,0.));
#758 = DIRECTION('',(-0.999048214928,-4.361953975718E-02,0.));
#759 = DEFINITIONAL_REPRESENTATION('',(#760),#763);
#760 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#761,#762),.UNSPECIFIED.,.F.,.F.,
  (2,2),(0.547427153727,2.697289360481),.PIECEWISE_BEZIER_KNOTS.);
#761 = CARTESIAN_POINT('',(0.,-19.090575));
#762 = CARTESIAN_POINT('',(2.149862206755,-19.090575));
#763 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#764 = ORIENTED_EDGE('',*,*,#765,.F.);
#765 = EDGE_CURVE('',#596,#740,#766,.T.);
#766 = SURFACE_CURVE('',#767,(#771,#777),.PCURVE_S1.);
#767 = LINE('',#768,#769);
#768 = CARTESIAN_POINT('',(50.717039405766,127.06608598088,8.909425));
#769 = VECTOR('',#770,1.);
#770 = DIRECTION('',(-5.540424023454E-02,0.998464005442,0.));
#771 = PCURVE('',#619,#772);
#772 = DEFINITIONAL_REPRESENTATION('',(#773),#776);
#773 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#774,#775),.UNSPECIFIED.,.F.,.F.,
  (2,2),(1.00643701657,4.835232910553),.PIECEWISE_BEZIER_KNOTS.);
#774 = CARTESIAN_POINT('',(-0.862217472481,-4.316451884327));
#775 = CARTESIAN_POINT('',(-1.074349,-0.493537));
#776 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#777 = PCURVE('',#703,#778);
#778 = DEFINITIONAL_REPRESENTATION('',(#779),#782);
#779 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#780,#781),.UNSPECIFIED.,.F.,.F.,
  (2,2),(1.00643701657,4.835232910553),.PIECEWISE_BEZIER_KNOTS.);
#780 = CARTESIAN_POINT('',(6.274925366886,-19.090575));
#781 = CARTESIAN_POINT('',(10.103721260868,-19.090575));
#782 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#783 = FACE_BOUND('',#784,.F.);
#784 = EDGE_LOOP('',(#785));
#785 = ORIENTED_EDGE('',*,*,#786,.F.);
#786 = EDGE_CURVE('',#787,#787,#789,.T.);
#787 = VERTEX_POINT('',#788);
#788 = CARTESIAN_POINT('',(51.798496,132.387429,8.909425));
#789 = SURFACE_CURVE('',#790,(#795,#802),.PCURVE_S1.);
#790 = CIRCLE('',#791,0.275);
#791 = AXIS2_PLACEMENT_3D('',#792,#793,#794);
#792 = CARTESIAN_POINT('',(51.523496,132.387429,8.909425));
#793 = DIRECTION('',(0.,0.,1.));
#794 = DIRECTION('',(1.,0.,0.));
#795 = PCURVE('',#619,#796);
#796 = DEFINITIONAL_REPRESENTATION('',(#797),#801);
#797 = CIRCLE('',#798,0.275);
#798 = AXIS2_PLACEMENT_2D('',#799,#800);
#799 = CARTESIAN_POINT('',(0.,0.));
#800 = DIRECTION('',(1.,0.));
#801 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#802 = PCURVE('',#803,#808);
#803 = CYLINDRICAL_SURFACE('',#804,0.275);
#804 = AXIS2_PLACEMENT_3D('',#805,#806,#807);
#805 = CARTESIAN_POINT('',(51.523496,132.387429,8.909425));
#806 = DIRECTION('',(0.,0.,1.));
#807 = DIRECTION('',(1.,0.,0.));
#808 = DEFINITIONAL_REPRESENTATION('',(#809),#813);
#809 = LINE('',#810,#811);
#810 = CARTESIAN_POINT('',(0.,0.));
#811 = VECTOR('',#812,1.);
#812 = DIRECTION('',(1.,0.));
#813 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#814 = ADVANCED_FACE('',(#815),#703,.F.);
#815 = FACE_BOUND('',#816,.F.);
#816 = EDGE_LOOP('',(#817,#840,#859,#860));
#817 = ORIENTED_EDGE('',*,*,#818,.F.);
#818 = EDGE_CURVE('',#819,#740,#821,.T.);
#819 = VERTEX_POINT('',#820);
#820 = CARTESIAN_POINT('',(50.449147,131.893892,18.));
#821 = SURFACE_CURVE('',#822,(#826,#833),.PCURVE_S1.);
#822 = LINE('',#823,#824);
#823 = CARTESIAN_POINT('',(50.449147,131.893892,28.));
#824 = VECTOR('',#825,1.);
#825 = DIRECTION('',(0.,0.,-1.));
#826 = PCURVE('',#703,#827);
#827 = DEFINITIONAL_REPRESENTATION('',(#828),#832);
#828 = LINE('',#829,#830);
#829 = CARTESIAN_POINT('',(10.103721260868,0.));
#830 = VECTOR('',#831,1.);
#831 = DIRECTION('',(0.,-1.));
#832 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#833 = PCURVE('',#754,#834);
#834 = DEFINITIONAL_REPRESENTATION('',(#835),#839);
#835 = LINE('',#836,#837);
#836 = CARTESIAN_POINT('',(0.,0.));
#837 = VECTOR('',#838,1.);
#838 = DIRECTION('',(-0.,-1.));
#839 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#840 = ORIENTED_EDGE('',*,*,#841,.F.);
#841 = EDGE_CURVE('',#658,#819,#842,.T.);
#842 = SURFACE_CURVE('',#843,(#847,#853),.PCURVE_S1.);
#843 = LINE('',#844,#845);
#844 = CARTESIAN_POINT('',(50.717039405766,127.06608598088,18.));
#845 = VECTOR('',#846,1.);
#846 = DIRECTION('',(-5.540424023454E-02,0.998464005442,0.));
#847 = PCURVE('',#703,#848);
#848 = DEFINITIONAL_REPRESENTATION('',(#849),#852);
#849 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#850,#851),.UNSPECIFIED.,.F.,.F.,
  (2,2),(1.00643701657,4.835232910553),.PIECEWISE_BEZIER_KNOTS.);
#850 = CARTESIAN_POINT('',(6.274925366886,-10.));
#851 = CARTESIAN_POINT('',(10.103721260868,-10.));
#852 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#853 = PCURVE('',#674,#854);
#854 = DEFINITIONAL_REPRESENTATION('',(#855),#858);
#855 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#856,#857),.UNSPECIFIED.,.F.,.F.,
  (2,2),(1.00643701657,4.835232910553),.PIECEWISE_BEZIER_KNOTS.);
#856 = CARTESIAN_POINT('',(-0.862217472481,-4.316451884327));
#857 = CARTESIAN_POINT('',(-1.074349,-0.493537));
#858 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#859 = ORIENTED_EDGE('',*,*,#690,.F.);
#860 = ORIENTED_EDGE('',*,*,#765,.T.);
#861 = ADVANCED_FACE('',(#862,#906),#674,.T.);
#862 = FACE_BOUND('',#863,.T.);
#863 = EDGE_LOOP('',(#864,#865,#866,#887));
#864 = ORIENTED_EDGE('',*,*,#841,.F.);
#865 = ORIENTED_EDGE('',*,*,#657,.F.);
#866 = ORIENTED_EDGE('',*,*,#867,.F.);
#867 = EDGE_CURVE('',#868,#632,#870,.T.);
#868 = VERTEX_POINT('',#869);
#869 = CARTESIAN_POINT('',(48.301331,131.800116,18.));
#870 = SURFACE_CURVE('',#871,(#875,#881),.PCURVE_S1.);
#871 = LINE('',#872,#873);
#872 = CARTESIAN_POINT('',(48.289032665924,131.90702363918,18.));
#873 = VECTOR('',#874,1.);
#874 = DIRECTION('',(-0.11428330053,0.993448200572,0.));
#875 = PCURVE('',#674,#876);
#876 = DEFINITIONAL_REPRESENTATION('',(#877),#880);
#877 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#878,#879),.UNSPECIFIED.,.F.,.F.,
  (2,2),(-0.107612695986,4.828492559933),.PIECEWISE_BEZIER_KNOTS.);
#878 = CARTESIAN_POINT('',(-3.222165,-0.587313));
#879 = CARTESIAN_POINT('',(-3.786279400408,4.316451884327));
#880 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#881 = PCURVE('',#646,#882);
#882 = DEFINITIONAL_REPRESENTATION('',(#883),#886);
#883 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#884,#885),.UNSPECIFIED.,.F.,.F.,
  (2,2),(-0.107612695986,4.828492559933),.PIECEWISE_BEZIER_KNOTS.);
#884 = CARTESIAN_POINT('',(0.,-10.));
#885 = CARTESIAN_POINT('',(4.936105255919,-10.));
#886 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#887 = ORIENTED_EDGE('',*,*,#888,.F.);
#888 = EDGE_CURVE('',#819,#868,#889,.T.);
#889 = SURFACE_CURVE('',#890,(#894,#900),.PCURVE_S1.);
#890 = LINE('',#891,#892);
#891 = CARTESIAN_POINT('',(50.996053120734,131.91777052049,18.));
#892 = VECTOR('',#893,1.);
#893 = DIRECTION('',(-0.999048214928,-4.361953975718E-02,0.));
#894 = PCURVE('',#674,#895);
#895 = DEFINITIONAL_REPRESENTATION('',(#896),#899);
#896 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#897,#898),.UNSPECIFIED.,.F.,.F.,
  (2,2),(0.547427153727,2.697289360481),.PIECEWISE_BEZIER_KNOTS.);
#897 = CARTESIAN_POINT('',(-1.074349,-0.493537));
#898 = CARTESIAN_POINT('',(-3.222165,-0.587313));
#899 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#900 = PCURVE('',#754,#901);
#901 = DEFINITIONAL_REPRESENTATION('',(#902),#905);
#902 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#903,#904),.UNSPECIFIED.,.F.,.F.,
  (2,2),(0.547427153727,2.697289360481),.PIECEWISE_BEZIER_KNOTS.);
#903 = CARTESIAN_POINT('',(0.,-10.));
#904 = CARTESIAN_POINT('',(2.149862206755,-10.));
#905 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#906 = FACE_BOUND('',#907,.T.);
#907 = EDGE_LOOP('',(#908));
#908 = ORIENTED_EDGE('',*,*,#909,.F.);
#909 = EDGE_CURVE('',#910,#910,#912,.T.);
#910 = VERTEX_POINT('',#911);
#911 = CARTESIAN_POINT('',(51.798496,132.387429,18.));
#912 = SURFACE_CURVE('',#913,(#918,#925),.PCURVE_S1.);
#913 = CIRCLE('',#914,0.275);
#914 = AXIS2_PLACEMENT_3D('',#915,#916,#917);
#915 = CARTESIAN_POINT('',(51.523496,132.387429,18.));
#916 = DIRECTION('',(0.,0.,1.));
#917 = DIRECTION('',(1.,0.,0.));
#918 = PCURVE('',#674,#919);
#919 = DEFINITIONAL_REPRESENTATION('',(#920),#924);
#920 = CIRCLE('',#921,0.275);
#921 = AXIS2_PLACEMENT_2D('',#922,#923);
#922 = CARTESIAN_POINT('',(0.,0.));
#923 = DIRECTION('',(1.,0.));
#924 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#925 = PCURVE('',#803,#926);
#926 = DEFINITIONAL_REPRESENTATION('',(#927),#931);
#927 = LINE('',#928,#929);
#928 = CARTESIAN_POINT('',(0.,9.090575));
#929 = VECTOR('',#930,1.);
#930 = DIRECTION('',(1.,0.));
#931 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#932 = ADVANCED_FACE('',(#933),#646,.F.);
#933 = FACE_BOUND('',#934,.F.);
#934 = EDGE_LOOP('',(#935,#936,#957,#958));
#935 = ORIENTED_EDGE('',*,*,#867,.F.);
#936 = ORIENTED_EDGE('',*,*,#937,.T.);
#937 = EDGE_CURVE('',#868,#719,#938,.T.);
#938 = SURFACE_CURVE('',#939,(#943,#950),.PCURVE_S1.);
#939 = LINE('',#940,#941);
#940 = CARTESIAN_POINT('',(48.301331,131.800116,28.));
#941 = VECTOR('',#942,1.);
#942 = DIRECTION('',(0.,0.,-1.));
#943 = PCURVE('',#646,#944);
#944 = DEFINITIONAL_REPRESENTATION('',(#945),#949);
#945 = LINE('',#946,#947);
#946 = CARTESIAN_POINT('',(0.,0.));
#947 = VECTOR('',#948,1.);
#948 = DIRECTION('',(0.,-1.));
#949 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#950 = PCURVE('',#754,#951);
#951 = DEFINITIONAL_REPRESENTATION('',(#952),#956);
#952 = LINE('',#953,#954);
#953 = CARTESIAN_POINT('',(2.149862206755,0.));
#954 = VECTOR('',#955,1.);
#955 = DIRECTION('',(-0.,-1.));
#956 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#957 = ORIENTED_EDGE('',*,*,#718,.T.);
#958 = ORIENTED_EDGE('',*,*,#631,.T.);
#959 = ADVANCED_FACE('',(#960),#754,.F.);
#960 = FACE_BOUND('',#961,.F.);
#961 = EDGE_LOOP('',(#962,#963,#964,#965));
#962 = ORIENTED_EDGE('',*,*,#888,.F.);
#963 = ORIENTED_EDGE('',*,*,#818,.T.);
#964 = ORIENTED_EDGE('',*,*,#739,.T.);
#965 = ORIENTED_EDGE('',*,*,#937,.F.);
#966 = ADVANCED_FACE('',(#967),#803,.F.);
#967 = FACE_BOUND('',#968,.F.);
#968 = EDGE_LOOP('',(#969,#970,#991,#992));
#969 = ORIENTED_EDGE('',*,*,#909,.F.);
#970 = ORIENTED_EDGE('',*,*,#971,.F.);
#971 = EDGE_CURVE('',#787,#910,#972,.T.);
#972 = SEAM_CURVE('',#973,(#977,#984),.PCURVE_S1.);
#973 = LINE('',#974,#975);
#974 = CARTESIAN_POINT('',(51.798496,132.387429,8.909425));
#975 = VECTOR('',#976,1.);
#976 = DIRECTION('',(0.,0.,1.));
#977 = PCURVE('',#803,#978);
#978 = DEFINITIONAL_REPRESENTATION('',(#979),#983);
#979 = LINE('',#980,#981);
#980 = CARTESIAN_POINT('',(6.28318530718,-0.));
#981 = VECTOR('',#982,1.);
#982 = DIRECTION('',(0.,1.));
#983 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#984 = PCURVE('',#803,#985);
#985 = DEFINITIONAL_REPRESENTATION('',(#986),#990);
#986 = LINE('',#987,#988);
#987 = CARTESIAN_POINT('',(0.,-0.));
#988 = VECTOR('',#989,1.);
#989 = DIRECTION('',(0.,1.));
#990 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#991 = ORIENTED_EDGE('',*,*,#786,.T.);
#992 = ORIENTED_EDGE('',*,*,#971,.T.);
#993 = ( GEOMETRIC_REPRESENTATION_CONTEXT(3) 
GLOBAL_UNCERTAINTY_ASSIGNED_CONTEXT((#997)) GLOBAL_UNIT_ASSIGNED_CONTEXT
((#994,#995,#996)) REPRESENTATION_CONTEXT('Context #1',
  '3D Context with UNIT and UNCERTAINTY') );
#994 = ( LENGTH_UNIT() NAMED_UNIT(*) SI_UNIT(.MILLI.,.METRE.) );
#995 = ( NAMED_UNIT(*) PLANE_ANGLE_UNIT() SI_UNIT($,.RADIAN.) );
#996 = ( NAMED_UNIT(*) SI_UNIT($,.STERADIAN.) SOLID_ANGLE_UNIT() );
#997 = UNCERTAINTY_MEASURE_WITH_UNIT(LENGTH_MEASURE(1.E-07),#994,
  'distance_accuracy_value','confusion accuracy');
#998 = CONTEXT_DEPENDENT_SHAPE_REPRESENTATION(#999,#1001);
#999 = ( REPRESENTATION_RELATIONSHIP('','',#588,#10) 
REPRESENTATION_RELATIONSHIP_WITH_TRANSFORMATION(#1000) 
SHAPE_REPRESENTATION_RELATIONSHIP() );
#1000 = ITEM_DEFINED_TRANSFORMATION('','',#11,#23);
#1001 = PRODUCT_DEFINITION_SHAPE('Placement','Placement of an item',
  #1002);
#1002 = NEXT_ASSEMBLY_USAGE_OCCURRENCE('35','=>[0:1:1:4]','',#5,#583,$);
#1003 = PRODUCT_RELATED_PRODUCT_CATEGORY('part',$,(#585));
#1004 = MECHANICAL_DESIGN_GEOMETRIC_PRESENTATION_REPRESENTATION('',(
    #1005),#139);
#1005 = STYLED_ITEM('color',(#1006),#41);
#1006 = PRESENTATION_STYLE_ASSIGNMENT((#1007));
#1007 = SURFACE_STYLE_USAGE(.BOTH.,#1008);
#1008 = SURFACE_SIDE_STYLE('',(#1009));
#1009 = SURFACE_STYLE_FILL_AREA(#1010);
#1010 = FILL_AREA_STYLE('',(#1011));
#1011 = FILL_AREA_STYLE_COLOUR('',#1012);
#1012 = DRAUGHTING_PRE_DEFINED_COLOUR('blue');
#1013 = MECHANICAL_DESIGN_GEOMETRIC_PRESENTATION_REPRESENTATION('',(
    #1014),#993);
#1014 = STYLED_ITEM('color',(#1015),#589);
#1015 = PRESENTATION_STYLE_ASSIGNMENT((#1016));
#1016 = SURFACE_STYLE_USAGE(.BOTH.,#1017);
#1017 = SURFACE_SIDE_STYLE('',(#1018));
#1018 = SURFACE_STYLE_FILL_AREA(#1019);
#1019 = FILL_AREA_STYLE('',(#1020));
#1020 = FILL_AREA_STYLE_COLOUR('',#1021);
#1021 = DRAUGHTING_PRE_DEFINED_COLOUR('red');
#1022 = MECHANICAL_DESIGN_GEOMETRIC_PRESENTATION_REPRESENTATION('',(
    #1023),#570);
#1023 = STYLED_ITEM('color',(#1024),#158);
#1024 = PRESENTATION_STYLE_ASSIGNMENT((#1025));
#1025 = SURFACE_STYLE_USAGE(.BOTH.,#1026);
#1026 = SURFACE_SIDE_STYLE('',(#1027));
#1027 = SURFACE_STYLE_FILL_AREA(#1028);
#1028 = FILL_AREA_STYLE('',(#1029));
#1029 = FILL_AREA_STYLE_COLOUR('',#1030);
#1030 = DRAUGHTING_PRE_DEFINED_COLOUR('green');
ENDSEC;
END-ISO-10303-21;
