ISO-10303-21;
HEADER;
FILE_DESCRIPTION(('Open CASCADE Model'),'2;1');
FILE_NAME('Pile_PC2-P2','2025-08-25T20:08:32',('Author'),('Open CASCADE'
    ),'Open CASCADE STEP processor 7.8','build123d','Unknown');
FILE_SCHEMA(('AUTOMOTIVE_DESIGN { 1 0 10303 214 1 1 1 1 }'));
ENDSEC;
DATA;
#1 = APPLICATION_PROTOCOL_DEFINITION('international standard',
  'automotive_design',2000,#2);
#2 = APPLICATION_CONTEXT(
  'core data for automotive mechanical design processes');
#3 = SHAPE_DEFINITION_REPRESENTATION(#4,#10);
#4 = PRODUCT_DEFINITION_SHAPE('','',#5);
#5 = PRODUCT_DEFINITION('design','',#6,#9);
#6 = PRODUCT_DEFINITION_FORMATION('','',#7);
#7 = PRODUCT('Pile_PC2-P2','Pile_PC2-P2','',(#8));
#8 = PRODUCT_CONTEXT('',#2,'mechanical');
#9 = PRODUCT_DEFINITION_CONTEXT('part definition',#2,'design');
#10 = SHAPE_REPRESENTATION('',(#11,#15,#19,#23),#27);
#11 = AXIS2_PLACEMENT_3D('',#12,#13,#14);
#12 = CARTESIAN_POINT('',(0.,0.,0.));
#13 = DIRECTION('',(0.,0.,1.));
#14 = DIRECTION('',(1.,0.,-0.));
#15 = AXIS2_PLACEMENT_3D('',#16,#17,#18);
#16 = CARTESIAN_POINT('',(51.791249,119.410221,6.836624));
#17 = DIRECTION('',(0.,0.,1.));
#18 = DIRECTION('',(1.,0.,0.));
#19 = AXIS2_PLACEMENT_3D('',#20,#21,#22);
#20 = CARTESIAN_POINT('',(0.,0.,0.));
#21 = DIRECTION('',(0.,0.,1.));
#22 = DIRECTION('',(1.,0.,-0.));
#23 = AXIS2_PLACEMENT_3D('',#24,#25,#26);
#24 = CARTESIAN_POINT('',(0.,0.,0.));
#25 = DIRECTION('',(0.,0.,1.));
#26 = DIRECTION('',(1.,0.,-0.));
#27 = ( GEOMETRIC_REPRESENTATION_CONTEXT(3) 
GLOBAL_UNCERTAINTY_ASSIGNED_CONTEXT((#31)) GLOBAL_UNIT_ASSIGNED_CONTEXT(
(#28,#29,#30)) REPRESENTATION_CONTEXT('Context #1',
  '3D Context with UNIT and UNCERTAINTY') );
#28 = ( LENGTH_UNIT() NAMED_UNIT(*) SI_UNIT(.MILLI.,.METRE.) );
#29 = ( NAMED_UNIT(*) PLANE_ANGLE_UNIT() SI_UNIT($,.RADIAN.) );
#30 = ( NAMED_UNIT(*) SI_UNIT($,.STERADIAN.) SOLID_ANGLE_UNIT() );
#31 = UNCERTAINTY_MEASURE_WITH_UNIT(LENGTH_MEASURE(1.E-07),#28,
  'distance_accuracy_value','confusion accuracy');
#32 = PRODUCT_RELATED_PRODUCT_CATEGORY('part',$,(#7));
#33 = SHAPE_DEFINITION_REPRESENTATION(#34,#40);
#34 = PRODUCT_DEFINITION_SHAPE('','',#35);
#35 = PRODUCT_DEFINITION('design','',#36,#39);
#36 = PRODUCT_DEFINITION_FORMATION('','',#37);
#37 = PRODUCT('PC2-P2_Part1','PC2-P2_Part1','',(#38));
#38 = PRODUCT_CONTEXT('',#2,'mechanical');
#39 = PRODUCT_DEFINITION_CONTEXT('part definition',#2,'design');
#40 = ADVANCED_BREP_SHAPE_REPRESENTATION('',(#11,#41),#139);
#41 = MANIFOLD_SOLID_BREP('',#42);
#42 = CLOSED_SHELL('',(#43,#131,#135));
#43 = ADVANCED_FACE('',(#44),#57,.T.);
#44 = FACE_BOUND('',#45,.T.);
#45 = EDGE_LOOP('',(#46,#80,#103,#130));
#46 = ORIENTED_EDGE('',*,*,#47,.F.);
#47 = EDGE_CURVE('',#48,#48,#50,.T.);
#48 = VERTEX_POINT('',#49);
#49 = CARTESIAN_POINT('',(0.275,-6.73555739531E-17,25.663376));
#50 = SURFACE_CURVE('',#51,(#56,#68),.PCURVE_S1.);
#51 = CIRCLE('',#52,0.275);
#52 = AXIS2_PLACEMENT_3D('',#53,#54,#55);
#53 = CARTESIAN_POINT('',(0.,0.,25.663376));
#54 = DIRECTION('',(0.,0.,1.));
#55 = DIRECTION('',(1.,0.,-0.));
#56 = PCURVE('',#57,#62);
#57 = CYLINDRICAL_SURFACE('',#58,0.275);
#58 = AXIS2_PLACEMENT_3D('',#59,#60,#61);
#59 = CARTESIAN_POINT('',(0.,0.,0.));
#60 = DIRECTION('',(0.,0.,1.));
#61 = DIRECTION('',(1.,0.,-0.));
#62 = DEFINITIONAL_REPRESENTATION('',(#63),#67);
#63 = LINE('',#64,#65);
#64 = CARTESIAN_POINT('',(0.,25.663376));
#65 = VECTOR('',#66,1.);
#66 = DIRECTION('',(1.,0.));
#67 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#68 = PCURVE('',#69,#74);
#69 = PLANE('',#70);
#70 = AXIS2_PLACEMENT_3D('',#71,#72,#73);
#71 = CARTESIAN_POINT('',(0.,0.,25.663376));
#72 = DIRECTION('',(0.,0.,1.));
#73 = DIRECTION('',(1.,0.,-0.));
#74 = DEFINITIONAL_REPRESENTATION('',(#75),#79);
#75 = CIRCLE('',#76,0.275);
#76 = AXIS2_PLACEMENT_2D('',#77,#78);
#77 = CARTESIAN_POINT('',(0.,0.));
#78 = DIRECTION('',(1.,0.));
#79 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#80 = ORIENTED_EDGE('',*,*,#81,.F.);
#81 = EDGE_CURVE('',#82,#48,#84,.T.);
#82 = VERTEX_POINT('',#83);
#83 = CARTESIAN_POINT('',(0.275,-6.73555739531E-17,0.));
#84 = SEAM_CURVE('',#85,(#89,#96),.PCURVE_S1.);
#85 = LINE('',#86,#87);
#86 = CARTESIAN_POINT('',(0.275,-6.73555739531E-17,0.));
#87 = VECTOR('',#88,1.);
#88 = DIRECTION('',(0.,0.,1.));
#89 = PCURVE('',#57,#90);
#90 = DEFINITIONAL_REPRESENTATION('',(#91),#95);
#91 = LINE('',#92,#93);
#92 = CARTESIAN_POINT('',(6.28318530718,-0.));
#93 = VECTOR('',#94,1.);
#94 = DIRECTION('',(0.,1.));
#95 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#96 = PCURVE('',#57,#97);
#97 = DEFINITIONAL_REPRESENTATION('',(#98),#102);
#98 = LINE('',#99,#100);
#99 = CARTESIAN_POINT('',(0.,-0.));
#100 = VECTOR('',#101,1.);
#101 = DIRECTION('',(0.,1.));
#102 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#103 = ORIENTED_EDGE('',*,*,#104,.T.);
#104 = EDGE_CURVE('',#82,#82,#105,.T.);
#105 = SURFACE_CURVE('',#106,(#111,#118),.PCURVE_S1.);
#106 = CIRCLE('',#107,0.275);
#107 = AXIS2_PLACEMENT_3D('',#108,#109,#110);
#108 = CARTESIAN_POINT('',(0.,0.,0.));
#109 = DIRECTION('',(0.,0.,1.));
#110 = DIRECTION('',(1.,0.,-0.));
#111 = PCURVE('',#57,#112);
#112 = DEFINITIONAL_REPRESENTATION('',(#113),#117);
#113 = LINE('',#114,#115);
#114 = CARTESIAN_POINT('',(0.,0.));
#115 = VECTOR('',#116,1.);
#116 = DIRECTION('',(1.,0.));
#117 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#118 = PCURVE('',#119,#124);
#119 = PLANE('',#120);
#120 = AXIS2_PLACEMENT_3D('',#121,#122,#123);
#121 = CARTESIAN_POINT('',(0.,0.,0.));
#122 = DIRECTION('',(0.,0.,1.));
#123 = DIRECTION('',(1.,0.,-0.));
#124 = DEFINITIONAL_REPRESENTATION('',(#125),#129);
#125 = CIRCLE('',#126,0.275);
#126 = AXIS2_PLACEMENT_2D('',#127,#128);
#127 = CARTESIAN_POINT('',(0.,0.));
#128 = DIRECTION('',(1.,0.));
#129 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#130 = ORIENTED_EDGE('',*,*,#81,.T.);
#131 = ADVANCED_FACE('',(#132),#69,.T.);
#132 = FACE_BOUND('',#133,.T.);
#133 = EDGE_LOOP('',(#134));
#134 = ORIENTED_EDGE('',*,*,#47,.T.);
#135 = ADVANCED_FACE('',(#136),#119,.F.);
#136 = FACE_BOUND('',#137,.T.);
#137 = EDGE_LOOP('',(#138));
#138 = ORIENTED_EDGE('',*,*,#104,.F.);
#139 = ( GEOMETRIC_REPRESENTATION_CONTEXT(3) 
GLOBAL_UNCERTAINTY_ASSIGNED_CONTEXT((#143)) GLOBAL_UNIT_ASSIGNED_CONTEXT
((#140,#141,#142)) REPRESENTATION_CONTEXT('Context #1',
  '3D Context with UNIT and UNCERTAINTY') );
#140 = ( LENGTH_UNIT() NAMED_UNIT(*) SI_UNIT(.MILLI.,.METRE.) );
#141 = ( NAMED_UNIT(*) PLANE_ANGLE_UNIT() SI_UNIT($,.RADIAN.) );
#142 = ( NAMED_UNIT(*) SI_UNIT($,.STERADIAN.) SOLID_ANGLE_UNIT() );
#143 = UNCERTAINTY_MEASURE_WITH_UNIT(LENGTH_MEASURE(1.E-07),#140,
  'distance_accuracy_value','confusion accuracy');
#144 = CONTEXT_DEPENDENT_SHAPE_REPRESENTATION(#145,#147);
#145 = ( REPRESENTATION_RELATIONSHIP('','',#40,#10) 
REPRESENTATION_RELATIONSHIP_WITH_TRANSFORMATION(#146) 
SHAPE_REPRESENTATION_RELATIONSHIP() );
#146 = ITEM_DEFINED_TRANSFORMATION('','',#11,#15);
#147 = PRODUCT_DEFINITION_SHAPE('Placement','Placement of an item',#148
  );
#148 = NEXT_ASSEMBLY_USAGE_OCCURRENCE('108','=>[0:1:1:2]','',#5,#35,$);
#149 = PRODUCT_RELATED_PRODUCT_CATEGORY('part',$,(#37));
#150 = SHAPE_DEFINITION_REPRESENTATION(#151,#157);
#151 = PRODUCT_DEFINITION_SHAPE('','',#152);
#152 = PRODUCT_DEFINITION('design','',#153,#156);
#153 = PRODUCT_DEFINITION_FORMATION('','',#154);
#154 = PRODUCT('PC2-P2_Part2','PC2-P2_Part2','',(#155));
#155 = PRODUCT_CONTEXT('',#2,'mechanical');
#156 = PRODUCT_DEFINITION_CONTEXT('part definition',#2,'design');
#157 = ADVANCED_BREP_SHAPE_REPRESENTATION('',(#11,#158),#513);
#158 = MANIFOLD_SOLID_BREP('',#159);
#159 = CLOSED_SHELL('',(#160,#383,#433,#481,#507));
#160 = ADVANCED_FACE('',(#161),#175,.T.);
#161 = FACE_BOUND('',#162,.T.);
#162 = EDGE_LOOP('',(#163,#193,#222,#285,#334,#356,#357));
#163 = ORIENTED_EDGE('',*,*,#164,.T.);
#164 = EDGE_CURVE('',#165,#167,#169,.T.);
#165 = VERTEX_POINT('',#166);
#166 = CARTESIAN_POINT('',(52.066249,119.410221,6.836624));
#167 = VERTEX_POINT('',#168);
#168 = CARTESIAN_POINT('',(56.107700884327,119.410221,13.836624));
#169 = SEAM_CURVE('',#170,(#174,#186),.PCURVE_S1.);
#170 = LINE('',#171,#172);
#171 = CARTESIAN_POINT('',(52.066249,119.410221,6.836624));
#172 = VECTOR('',#173,1.);
#173 = DIRECTION('',(0.5,-1.224646799147E-16,0.866025403784));
#174 = PCURVE('',#175,#180);
#175 = CONICAL_SURFACE('',#176,0.275,0.523598775598);
#176 = AXIS2_PLACEMENT_3D('',#177,#178,#179);
#177 = CARTESIAN_POINT('',(51.791249,119.410221,6.836624));
#178 = DIRECTION('',(0.,0.,1.));
#179 = DIRECTION('',(1.,0.,0.));
#180 = DEFINITIONAL_REPRESENTATION('',(#181),#185);
#181 = LINE('',#182,#183);
#182 = CARTESIAN_POINT('',(6.28318530718,-0.));
#183 = VECTOR('',#184,1.);
#184 = DIRECTION('',(0.,1.));
#185 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#186 = PCURVE('',#175,#187);
#187 = DEFINITIONAL_REPRESENTATION('',(#188),#192);
#188 = LINE('',#189,#190);
#189 = CARTESIAN_POINT('',(0.,-0.));
#190 = VECTOR('',#191,1.);
#191 = DIRECTION('',(0.,1.));
#192 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#193 = ORIENTED_EDGE('',*,*,#194,.F.);
#194 = EDGE_CURVE('',#195,#167,#197,.T.);
#195 = VERTEX_POINT('',#196);
#196 = CARTESIAN_POINT('',(51.380289556504,115.11337690165,13.836624));
#197 = SURFACE_CURVE('',#198,(#203,#210),.PCURVE_S1.);
#198 = CIRCLE('',#199,4.316451884327);
#199 = AXIS2_PLACEMENT_3D('',#200,#201,#202);
#200 = CARTESIAN_POINT('',(51.791249,119.410221,13.836624));
#201 = DIRECTION('',(0.,0.,1.));
#202 = DIRECTION('',(1.,0.,0.));
#203 = PCURVE('',#175,#204);
#204 = DEFINITIONAL_REPRESENTATION('',(#205),#209);
#205 = LINE('',#206,#207);
#206 = CARTESIAN_POINT('',(0.,7.));
#207 = VECTOR('',#208,1.);
#208 = DIRECTION('',(1.,0.));
#209 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#210 = PCURVE('',#211,#216);
#211 = PLANE('',#212);
#212 = AXIS2_PLACEMENT_3D('',#213,#214,#215);
#213 = CARTESIAN_POINT('',(51.791249,119.410221,13.836624));
#214 = DIRECTION('',(0.,0.,1.));
#215 = DIRECTION('',(1.,0.,0.));
#216 = DEFINITIONAL_REPRESENTATION('',(#217),#221);
#217 = CIRCLE('',#218,4.316451884327);
#218 = AXIS2_PLACEMENT_2D('',#219,#220);
#219 = CARTESIAN_POINT('',(0.,0.));
#220 = DIRECTION('',(1.,0.));
#221 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#222 = ORIENTED_EDGE('',*,*,#223,.F.);
#223 = EDGE_CURVE('',#224,#195,#226,.T.);
#224 = VERTEX_POINT('',#225);
#225 = CARTESIAN_POINT('',(51.008936,121.80569,10.725038665508));
#226 = SURFACE_CURVE('',#227,(#232,#273),.PCURVE_S1.);
#227 = HYPERBOLA('',#228,1.123047887515,0.648392000169);
#228 = AXIS2_PLACEMENT_3D('',#229,#230,#231);
#229 = CARTESIAN_POINT('',(51.143852929827,119.37429727234,
    6.360310027919));
#230 = DIRECTION('',(0.998464000178,5.54043351018E-02,0.));
#231 = DIRECTION('',(-0.,0.,1.));
#232 = PCURVE('',#175,#233);
#233 = DEFINITIONAL_REPRESENTATION('',(#234),#272);
#234 = B_SPLINE_CURVE_WITH_KNOTS('',8,(#235,#236,#237,#238,#239,#240,
    #241,#242,#243,#244,#245,#246,#247,#248,#249,#250,#251,#252,#253,
    #254,#255,#256,#257,#258,#259,#260,#261,#262,#263,#264,#265,#266,
    #267,#268,#269,#270,#271),.UNSPECIFIED.,.F.,.F.,(9,7,7,7,7,9),(
    -2.319131144472,-1.093560523529,-0.174382557821,0.51500091646,
    1.549076127881,2.583151339302),.UNSPECIFIED.);
#235 = CARTESIAN_POINT('',(1.822316337552,5.28783934862));
#236 = CARTESIAN_POINT('',(1.852164053692,4.421714581874));
#237 = CARTESIAN_POINT('',(1.887137815328,3.710194555881));
#238 = CARTESIAN_POINT('',(1.928221048556,3.122307892994));
#239 = CARTESIAN_POINT('',(1.976607902329,2.634810307618));
#240 = CARTESIAN_POINT('',(2.033685184043,2.229900207322));
#241 = CARTESIAN_POINT('',(2.101085275627,1.8937930598));
#242 = CARTESIAN_POINT('',(2.18047774187,1.615841963524));
#243 = CARTESIAN_POINT('',(2.34198454333,1.216929282164));
#244 = CARTESIAN_POINT('',(2.418456421233,1.074092494695));
#245 = CARTESIAN_POINT('',(2.502564719346,0.955942486728));
#246 = CARTESIAN_POINT('',(2.594447845991,0.85983180283));
#247 = CARTESIAN_POINT('',(2.693837597818,0.783760922715));
#248 = CARTESIAN_POINT('',(2.799741870639,0.726286148342));
#249 = CARTESIAN_POINT('',(2.910347823714,0.686468455539));
#250 = CARTESIAN_POINT('',(3.108399067511,0.646891001291));
#251 = CARTESIAN_POINT('',(3.194721047956,0.639605372068));
#252 = CARTESIAN_POINT('',(3.281584124767,0.641803960646));
#253 = CARTESIAN_POINT('',(3.367971186033,0.653448089856));
#254 = CARTESIAN_POINT('',(3.452858333305,0.674647894848));
#255 = CARTESIAN_POINT('',(3.535351376233,0.705663638145));
#256 = CARTESIAN_POINT('',(3.614777978305,0.746913123651));
#257 = CARTESIAN_POINT('',(3.804490922353,0.877094224413));
#258 = CARTESIAN_POINT('',(3.910351174116,0.979554538887));
#259 = CARTESIAN_POINT('',(4.006646644822,1.108355120498));
#260 = CARTESIAN_POINT('',(4.092819168123,1.266352657958));
#261 = CARTESIAN_POINT('',(4.169338608823,1.457377867317));
#262 = CARTESIAN_POINT('',(4.236966988672,1.686420096271));
#263 = CARTESIAN_POINT('',(4.296608647049,1.959905117762));
#264 = CARTESIAN_POINT('',(4.401706914474,2.612372391705));
#265 = CARTESIAN_POINT('',(4.447163516068,2.991354483791));
#266 = CARTESIAN_POINT('',(4.486424347783,3.431391964237));
#267 = CARTESIAN_POINT('',(4.520379642101,3.942667699166));
#268 = CARTESIAN_POINT('',(4.549808295341,4.537696822084));
#269 = CARTESIAN_POINT('',(4.575367766718,5.231921345069));
#270 = CARTESIAN_POINT('',(4.597620271684,6.044581217107));
#271 = CARTESIAN_POINT('',(4.617036858287,7.));
#272 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#273 = PCURVE('',#274,#279);
#274 = PLANE('',#275);
#275 = AXIS2_PLACEMENT_3D('',#276,#277,#278);
#276 = CARTESIAN_POINT('',(51.468253,113.528152,42.5));
#277 = DIRECTION('',(0.998464000178,5.54043351018E-02,0.));
#278 = DIRECTION('',(-5.54043351018E-02,0.998464000178,0.));
#279 = DEFINITIONAL_REPRESENTATION('',(#280),#284);
#280 = ( BOUNDED_CURVE() B_SPLINE_CURVE(2,(#281,#282,#283),
.UNSPECIFIED.,.F.,.F.) B_SPLINE_CURVE_WITH_KNOTS((3,3),(-2.319131144472,
2.583151339302),.PIECEWISE_BEZIER_KNOTS.) CURVE() 
GEOMETRIC_REPRESENTATION_ITEM() RATIONAL_B_SPLINE_CURVE((1.,
5.843887268057,1.)) REPRESENTATION_ITEM('') );
#281 = CARTESIAN_POINT('',(9.119299041703,-30.37553665138));
#282 = CARTESIAN_POINT('',(5.840449379155,-35.94583825239));
#283 = CARTESIAN_POINT('',(1.587663552585,-28.663376));
#284 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#285 = ORIENTED_EDGE('',*,*,#286,.F.);
#286 = EDGE_CURVE('',#287,#224,#289,.T.);
#287 = VERTEX_POINT('',#288);
#288 = CARTESIAN_POINT('',(55.381847324821,121.80591725359,13.836624));
#289 = SURFACE_CURVE('',#290,(#295,#322),.PCURVE_S1.);
#290 = HYPERBOLA('',#291,4.149144427962,2.395509652391);
#291 = AXIS2_PLACEMENT_3D('',#292,#293,#294);
#292 = CARTESIAN_POINT('',(51.791124508981,121.80573064915,
    6.360310027919));
#293 = DIRECTION('',(5.196848995186E-05,-0.99999999865,0.));
#294 = DIRECTION('',(0.,0.,1.));
#295 = PCURVE('',#175,#296);
#296 = DEFINITIONAL_REPRESENTATION('',(#297),#321);
#297 = B_SPLINE_CURVE_WITH_KNOTS('',8,(#298,#299,#300,#301,#302,#303,
    #304,#305,#306,#307,#308,#309,#310,#311,#312,#313,#314,#315,#316,
    #317,#318,#319,#320),.UNSPECIFIED.,.F.,.F.,(9,7,7,9),(
    -1.194174531109,-0.605168970393,0.27833937068,1.161847711753),
  .UNSPECIFIED.);
#298 = CARTESIAN_POINT('',(0.588381196357,7.));
#299 = CARTESIAN_POINT('',(0.629241390789,6.542098657167));
#300 = CARTESIAN_POINT('',(0.672961927176,6.130514020256));
#301 = CARTESIAN_POINT('',(0.719671146896,5.761464074321));
#302 = CARTESIAN_POINT('',(0.769472631532,5.431701481893));
#303 = CARTESIAN_POINT('',(0.822422502831,5.138449916422));
#304 = CARTESIAN_POINT('',(0.87850756008,4.879352756634));
#305 = CARTESIAN_POINT('',(0.937624254891,4.652434141785));
#306 = CARTESIAN_POINT('',(1.092459876153,4.161529756856));
#307 = CARTESIAN_POINT('',(1.19170167009,3.935739922316));
#308 = CARTESIAN_POINT('',(1.296565354688,3.773228693033));
#309 = CARTESIAN_POINT('',(1.405804131785,3.670310780226));
#310 = CARTESIAN_POINT('',(1.517542953822,3.624876212404));
#311 = CARTESIAN_POINT('',(1.629431976718,3.636292838554));
#312 = CARTESIAN_POINT('',(1.739367362421,3.705398286268));
#313 = CARTESIAN_POINT('',(1.951956217474,3.9637891265));
#314 = CARTESIAN_POINT('',(2.054610023191,4.153074463977));
#315 = CARTESIAN_POINT('',(2.151938804486,4.404851238237));
#316 = CARTESIAN_POINT('',(2.242686614781,4.723083037175));
#317 = CARTESIAN_POINT('',(2.326284990751,5.113393485215));
#318 = CARTESIAN_POINT('',(2.402703574109,5.583255534762));
#319 = CARTESIAN_POINT('',(2.472181973274,6.142306234802));
#320 = CARTESIAN_POINT('',(2.535132510303,6.802821130362));
#321 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#322 = PCURVE('',#323,#328);
#323 = PLANE('',#324);
#324 = AXIS2_PLACEMENT_3D('',#325,#326,#327);
#325 = CARTESIAN_POINT('',(51.008936,121.80569,42.5));
#326 = DIRECTION('',(5.196848995186E-05,-0.99999999865,0.));
#327 = DIRECTION('',(0.99999999865,5.196848995186E-05,0.));
#328 = DEFINITIONAL_REPRESENTATION('',(#329),#333);
#329 = ( BOUNDED_CURVE() B_SPLINE_CURVE(2,(#330,#331,#332),
.UNSPECIFIED.,.F.,.F.) B_SPLINE_CURVE_WITH_KNOTS((3,3),(-1.194174531109,
1.161847711753),.PIECEWISE_BEZIER_KNOTS.) CURVE() 
GEOMETRIC_REPRESENTATION_ITEM() RATIONAL_B_SPLINE_CURVE((1.,
1.777899283461,1.)) REPRESENTATION_ITEM('') );
#330 = CARTESIAN_POINT('',(4.372911330726,-28.663376));
#331 = CARTESIAN_POINT('',(0.803967747047,-33.8056508775));
#332 = CARTESIAN_POINT('',(-2.670849203607,-28.86055486963));
#333 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#334 = ORIENTED_EDGE('',*,*,#335,.F.);
#335 = EDGE_CURVE('',#167,#287,#336,.T.);
#336 = SURFACE_CURVE('',#337,(#342,#349),.PCURVE_S1.);
#337 = CIRCLE('',#338,4.316451884327);
#338 = AXIS2_PLACEMENT_3D('',#339,#340,#341);
#339 = CARTESIAN_POINT('',(51.791249,119.410221,13.836624));
#340 = DIRECTION('',(0.,0.,1.));
#341 = DIRECTION('',(1.,0.,0.));
#342 = PCURVE('',#175,#343);
#343 = DEFINITIONAL_REPRESENTATION('',(#344),#348);
#344 = LINE('',#345,#346);
#345 = CARTESIAN_POINT('',(0.,7.));
#346 = VECTOR('',#347,1.);
#347 = DIRECTION('',(1.,0.));
#348 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#349 = PCURVE('',#211,#350);
#350 = DEFINITIONAL_REPRESENTATION('',(#351),#355);
#351 = CIRCLE('',#352,4.316451884327);
#352 = AXIS2_PLACEMENT_2D('',#353,#354);
#353 = CARTESIAN_POINT('',(0.,0.));
#354 = DIRECTION('',(1.,0.));
#355 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#356 = ORIENTED_EDGE('',*,*,#164,.F.);
#357 = ORIENTED_EDGE('',*,*,#358,.T.);
#358 = EDGE_CURVE('',#165,#165,#359,.T.);
#359 = SURFACE_CURVE('',#360,(#365,#372),.PCURVE_S1.);
#360 = CIRCLE('',#361,0.275);
#361 = AXIS2_PLACEMENT_3D('',#362,#363,#364);
#362 = CARTESIAN_POINT('',(51.791249,119.410221,6.836624));
#363 = DIRECTION('',(0.,0.,1.));
#364 = DIRECTION('',(1.,0.,0.));
#365 = PCURVE('',#175,#366);
#366 = DEFINITIONAL_REPRESENTATION('',(#367),#371);
#367 = LINE('',#368,#369);
#368 = CARTESIAN_POINT('',(0.,0.));
#369 = VECTOR('',#370,1.);
#370 = DIRECTION('',(1.,0.));
#371 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#372 = PCURVE('',#373,#378);
#373 = CYLINDRICAL_SURFACE('',#374,0.275);
#374 = AXIS2_PLACEMENT_3D('',#375,#376,#377);
#375 = CARTESIAN_POINT('',(51.791249,119.410221,6.836624));
#376 = DIRECTION('',(0.,0.,1.));
#377 = DIRECTION('',(1.,0.,0.));
#378 = DEFINITIONAL_REPRESENTATION('',(#379),#382);
#379 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#380,#381),.UNSPECIFIED.,.F.,.F.,
  (2,2),(0.,6.28318530718),.PIECEWISE_BEZIER_KNOTS.);
#380 = CARTESIAN_POINT('',(0.,0.));
#381 = CARTESIAN_POINT('',(6.28318530718,0.));
#382 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#383 = ADVANCED_FACE('',(#384),#373,.F.);
#384 = FACE_BOUND('',#385,.F.);
#385 = EDGE_LOOP('',(#386,#410,#431,#432));
#386 = ORIENTED_EDGE('',*,*,#387,.F.);
#387 = EDGE_CURVE('',#388,#388,#390,.T.);
#388 = VERTEX_POINT('',#389);
#389 = CARTESIAN_POINT('',(52.066249,119.410221,13.836624));
#390 = SURFACE_CURVE('',#391,(#396,#403),.PCURVE_S1.);
#391 = CIRCLE('',#392,0.275);
#392 = AXIS2_PLACEMENT_3D('',#393,#394,#395);
#393 = CARTESIAN_POINT('',(51.791249,119.410221,13.836624));
#394 = DIRECTION('',(0.,0.,1.));
#395 = DIRECTION('',(1.,0.,0.));
#396 = PCURVE('',#373,#397);
#397 = DEFINITIONAL_REPRESENTATION('',(#398),#402);
#398 = LINE('',#399,#400);
#399 = CARTESIAN_POINT('',(0.,7.));
#400 = VECTOR('',#401,1.);
#401 = DIRECTION('',(1.,0.));
#402 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#403 = PCURVE('',#211,#404);
#404 = DEFINITIONAL_REPRESENTATION('',(#405),#409);
#405 = CIRCLE('',#406,0.275);
#406 = AXIS2_PLACEMENT_2D('',#407,#408);
#407 = CARTESIAN_POINT('',(0.,0.));
#408 = DIRECTION('',(1.,0.));
#409 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#410 = ORIENTED_EDGE('',*,*,#411,.F.);
#411 = EDGE_CURVE('',#165,#388,#412,.T.);
#412 = SEAM_CURVE('',#413,(#417,#424),.PCURVE_S1.);
#413 = LINE('',#414,#415);
#414 = CARTESIAN_POINT('',(52.066249,119.410221,6.836624));
#415 = VECTOR('',#416,1.);
#416 = DIRECTION('',(0.,0.,1.));
#417 = PCURVE('',#373,#418);
#418 = DEFINITIONAL_REPRESENTATION('',(#419),#423);
#419 = LINE('',#420,#421);
#420 = CARTESIAN_POINT('',(6.28318530718,-0.));
#421 = VECTOR('',#422,1.);
#422 = DIRECTION('',(0.,1.));
#423 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#424 = PCURVE('',#373,#425);
#425 = DEFINITIONAL_REPRESENTATION('',(#426),#430);
#426 = LINE('',#427,#428);
#427 = CARTESIAN_POINT('',(0.,-0.));
#428 = VECTOR('',#429,1.);
#429 = DIRECTION('',(0.,1.));
#430 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#431 = ORIENTED_EDGE('',*,*,#358,.T.);
#432 = ORIENTED_EDGE('',*,*,#411,.T.);
#433 = ADVANCED_FACE('',(#434,#478),#211,.T.);
#434 = FACE_BOUND('',#435,.T.);
#435 = EDGE_LOOP('',(#436,#437,#438,#459));
#436 = ORIENTED_EDGE('',*,*,#194,.T.);
#437 = ORIENTED_EDGE('',*,*,#335,.T.);
#438 = ORIENTED_EDGE('',*,*,#439,.F.);
#439 = EDGE_CURVE('',#440,#287,#442,.T.);
#440 = VERTEX_POINT('',#441);
#441 = CARTESIAN_POINT('',(51.008936,121.80569,13.836624));
#442 = SURFACE_CURVE('',#443,(#447,#453),.PCURVE_S1.);
#443 = LINE('',#444,#445);
#444 = CARTESIAN_POINT('',(51.40003025449,121.80571032457,13.836624));
#445 = VECTOR('',#446,1.);
#446 = DIRECTION('',(0.99999999865,5.196848995186E-05,-0.));
#447 = PCURVE('',#211,#448);
#448 = DEFINITIONAL_REPRESENTATION('',(#449),#452);
#449 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#450,#451),.UNSPECIFIED.,.F.,.F.,
  (2,2),(-0.391094255018,4.707670636194),.PIECEWISE_BEZIER_KNOTS.);
#450 = CARTESIAN_POINT('',(-0.782313,2.395469));
#451 = CARTESIAN_POINT('',(4.316451884327,2.395733975112));
#452 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#453 = PCURVE('',#323,#454);
#454 = DEFINITIONAL_REPRESENTATION('',(#455),#458);
#455 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#456,#457),.UNSPECIFIED.,.F.,.F.,
  (2,2),(-0.391094255018,4.707670636194),.PIECEWISE_BEZIER_KNOTS.);
#456 = CARTESIAN_POINT('',(-7.105427357601E-15,-28.663376));
#457 = CARTESIAN_POINT('',(5.098764891213,-28.663376));
#458 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#459 = ORIENTED_EDGE('',*,*,#460,.F.);
#460 = EDGE_CURVE('',#195,#440,#461,.T.);
#461 = SURFACE_CURVE('',#462,(#466,#472),.PCURVE_S1.);
#462 = LINE('',#463,#464);
#463 = CARTESIAN_POINT('',(51.306052964914,116.45122463617,13.836624));
#464 = VECTOR('',#465,1.);
#465 = DIRECTION('',(-5.54043351018E-02,0.998464000178,0.));
#466 = PCURVE('',#211,#467);
#467 = DEFINITIONAL_REPRESENTATION('',(#468),#471);
#468 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#469,#470),.UNSPECIFIED.,.F.,.F.,
  (2,2),(-1.359543779503,5.362702473871),.PIECEWISE_BEZIER_KNOTS.);
#469 = CARTESIAN_POINT('',(-0.409871415941,-4.316451884327));
#470 = CARTESIAN_POINT('',(-0.782313,2.395469));
#471 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#472 = PCURVE('',#274,#473);
#473 = DEFINITIONAL_REPRESENTATION('',(#474),#477);
#474 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#475,#476),.UNSPECIFIED.,.F.,.F.,
  (2,2),(-1.359543779503,5.362702473871),.PIECEWISE_BEZIER_KNOTS.);
#475 = CARTESIAN_POINT('',(1.568025602719,-28.663376));
#476 = CARTESIAN_POINT('',(8.290271856093,-28.663376));
#477 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#478 = FACE_BOUND('',#479,.T.);
#479 = EDGE_LOOP('',(#480));
#480 = ORIENTED_EDGE('',*,*,#387,.F.);
#481 = ADVANCED_FACE('',(#482),#323,.F.);
#482 = FACE_BOUND('',#483,.F.);
#483 = EDGE_LOOP('',(#484,#485,#506));
#484 = ORIENTED_EDGE('',*,*,#439,.F.);
#485 = ORIENTED_EDGE('',*,*,#486,.T.);
#486 = EDGE_CURVE('',#440,#224,#487,.T.);
#487 = SURFACE_CURVE('',#488,(#492,#499),.PCURVE_S1.);
#488 = LINE('',#489,#490);
#489 = CARTESIAN_POINT('',(51.008936,121.80569,42.5));
#490 = VECTOR('',#491,1.);
#491 = DIRECTION('',(0.,0.,-1.));
#492 = PCURVE('',#323,#493);
#493 = DEFINITIONAL_REPRESENTATION('',(#494),#498);
#494 = LINE('',#495,#496);
#495 = CARTESIAN_POINT('',(0.,0.));
#496 = VECTOR('',#497,1.);
#497 = DIRECTION('',(0.,-1.));
#498 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#499 = PCURVE('',#274,#500);
#500 = DEFINITIONAL_REPRESENTATION('',(#501),#505);
#501 = LINE('',#502,#503);
#502 = CARTESIAN_POINT('',(8.290271856093,0.));
#503 = VECTOR('',#504,1.);
#504 = DIRECTION('',(0.,-1.));
#505 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#506 = ORIENTED_EDGE('',*,*,#286,.F.);
#507 = ADVANCED_FACE('',(#508),#274,.F.);
#508 = FACE_BOUND('',#509,.F.);
#509 = EDGE_LOOP('',(#510,#511,#512));
#510 = ORIENTED_EDGE('',*,*,#486,.F.);
#511 = ORIENTED_EDGE('',*,*,#460,.F.);
#512 = ORIENTED_EDGE('',*,*,#223,.F.);
#513 = ( GEOMETRIC_REPRESENTATION_CONTEXT(3) 
GLOBAL_UNCERTAINTY_ASSIGNED_CONTEXT((#517)) GLOBAL_UNIT_ASSIGNED_CONTEXT
((#514,#515,#516)) REPRESENTATION_CONTEXT('Context #1',
  '3D Context with UNIT and UNCERTAINTY') );
#514 = ( LENGTH_UNIT() NAMED_UNIT(*) SI_UNIT(.MILLI.,.METRE.) );
#515 = ( NAMED_UNIT(*) PLANE_ANGLE_UNIT() SI_UNIT($,.RADIAN.) );
#516 = ( NAMED_UNIT(*) SI_UNIT($,.STERADIAN.) SOLID_ANGLE_UNIT() );
#517 = UNCERTAINTY_MEASURE_WITH_UNIT(LENGTH_MEASURE(1.E-07),#514,
  'distance_accuracy_value','confusion accuracy');
#518 = CONTEXT_DEPENDENT_SHAPE_REPRESENTATION(#519,#521);
#519 = ( REPRESENTATION_RELATIONSHIP('','',#157,#10) 
REPRESENTATION_RELATIONSHIP_WITH_TRANSFORMATION(#520) 
SHAPE_REPRESENTATION_RELATIONSHIP() );
#520 = ITEM_DEFINED_TRANSFORMATION('','',#11,#19);
#521 = PRODUCT_DEFINITION_SHAPE('Placement','Placement of an item',#522
  );
#522 = NEXT_ASSEMBLY_USAGE_OCCURRENCE('109','=>[0:1:1:3]','',#5,#152,$);
#523 = PRODUCT_RELATED_PRODUCT_CATEGORY('part',$,(#154));
#524 = SHAPE_DEFINITION_REPRESENTATION(#525,#531);
#525 = PRODUCT_DEFINITION_SHAPE('','',#526);
#526 = PRODUCT_DEFINITION('design','',#527,#530);
#527 = PRODUCT_DEFINITION_FORMATION('','',#528);
#528 = PRODUCT('PC2-P2_Part3','PC2-P2_Part3','',(#529));
#529 = PRODUCT_CONTEXT('',#2,'mechanical');
#530 = PRODUCT_DEFINITION_CONTEXT('part definition',#2,'design');
#531 = ADVANCED_BREP_SHAPE_REPRESENTATION('',(#11,#532),#862);
#532 = MANIFOLD_SOLID_BREP('',#533);
#533 = CLOSED_SHELL('',(#534,#656,#731,#778,#828,#835));
#534 = ADVANCED_FACE('',(#535),#550,.T.);
#535 = FACE_BOUND('',#536,.T.);
#536 = EDGE_LOOP('',(#537,#573,#599,#632));
#537 = ORIENTED_EDGE('',*,*,#538,.T.);
#538 = EDGE_CURVE('',#539,#541,#543,.T.);
#539 = VERTEX_POINT('',#540);
#540 = CARTESIAN_POINT('',(51.380289556504,115.11337690165,13.836624));
#541 = VERTEX_POINT('',#542);
#542 = CARTESIAN_POINT('',(55.381847324821,121.80591725359,13.836624));
#543 = SURFACE_CURVE('',#544,(#549,#561),.PCURVE_S1.);
#544 = CIRCLE('',#545,4.316451884327);
#545 = AXIS2_PLACEMENT_3D('',#546,#547,#548);
#546 = CARTESIAN_POINT('',(51.791249,119.410221,13.836624));
#547 = DIRECTION('',(0.,0.,1.));
#548 = DIRECTION('',(-9.520769708762E-02,-0.995457429735,0.));
#549 = PCURVE('',#550,#555);
#550 = CYLINDRICAL_SURFACE('',#551,4.316451884327);
#551 = AXIS2_PLACEMENT_3D('',#552,#553,#554);
#552 = CARTESIAN_POINT('',(51.791249,119.410221,13.836624));
#553 = DIRECTION('',(0.,0.,1.));
#554 = DIRECTION('',(1.,0.,0.));
#555 = DEFINITIONAL_REPRESENTATION('',(#556),#560);
#556 = LINE('',#557,#558);
#557 = CARTESIAN_POINT('',(4.617036858287,0.));
#558 = VECTOR('',#559,1.);
#559 = DIRECTION('',(1.,0.));
#560 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#561 = PCURVE('',#562,#567);
#562 = PLANE('',#563);
#563 = AXIS2_PLACEMENT_3D('',#564,#565,#566);
#564 = CARTESIAN_POINT('',(51.791249,119.410221,13.836624));
#565 = DIRECTION('',(0.,0.,1.));
#566 = DIRECTION('',(1.,0.,0.));
#567 = DEFINITIONAL_REPRESENTATION('',(#568),#572);
#568 = CIRCLE('',#569,4.316451884327);
#569 = AXIS2_PLACEMENT_2D('',#570,#571);
#570 = CARTESIAN_POINT('',(0.,0.));
#571 = DIRECTION('',(-9.520769708762E-02,-0.995457429735));
#572 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#573 = ORIENTED_EDGE('',*,*,#574,.T.);
#574 = EDGE_CURVE('',#541,#575,#577,.T.);
#575 = VERTEX_POINT('',#576);
#576 = CARTESIAN_POINT('',(55.381847324821,121.80591725359,32.5));
#577 = SURFACE_CURVE('',#578,(#582,#588),.PCURVE_S1.);
#578 = LINE('',#579,#580);
#579 = CARTESIAN_POINT('',(55.381847324821,121.80591725359,13.836624));
#580 = VECTOR('',#581,1.);
#581 = DIRECTION('',(0.,0.,1.));
#582 = PCURVE('',#550,#583);
#583 = DEFINITIONAL_REPRESENTATION('',(#584),#587);
#584 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#585,#586),.UNSPECIFIED.,.F.,.F.,
  (2,2),(0.,18.663376),.PIECEWISE_BEZIER_KNOTS.);
#585 = CARTESIAN_POINT('',(6.871566503537,0.));
#586 = CARTESIAN_POINT('',(6.871566503537,18.663376));
#587 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#588 = PCURVE('',#589,#594);
#589 = PLANE('',#590);
#590 = AXIS2_PLACEMENT_3D('',#591,#592,#593);
#591 = CARTESIAN_POINT('',(51.008936,121.80569,42.5));
#592 = DIRECTION('',(5.196848995186E-05,-0.99999999865,0.));
#593 = DIRECTION('',(0.99999999865,5.196848995186E-05,0.));
#594 = DEFINITIONAL_REPRESENTATION('',(#595),#598);
#595 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#596,#597),.UNSPECIFIED.,.F.,.F.,
  (2,2),(-5.999999999062E-07,18.6633766),.PIECEWISE_BEZIER_KNOTS.);
#596 = CARTESIAN_POINT('',(4.372911330726,-28.6633766));
#597 = CARTESIAN_POINT('',(4.372911330726,-9.9999994));
#598 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#599 = ORIENTED_EDGE('',*,*,#600,.T.);
#600 = EDGE_CURVE('',#575,#601,#603,.T.);
#601 = VERTEX_POINT('',#602);
#602 = CARTESIAN_POINT('',(51.380289556504,115.11337690165,32.5));
#603 = SURFACE_CURVE('',#604,(#609,#616),.PCURVE_S1.);
#604 = CIRCLE('',#605,4.316451884327);
#605 = AXIS2_PLACEMENT_3D('',#606,#607,#608);
#606 = CARTESIAN_POINT('',(51.791249,119.410221,32.5));
#607 = DIRECTION('',(0.,0.,-1.));
#608 = DIRECTION('',(0.831840229207,0.555015164723,0.));
#609 = PCURVE('',#550,#610);
#610 = DEFINITIONAL_REPRESENTATION('',(#611),#615);
#611 = LINE('',#612,#613);
#612 = CARTESIAN_POINT('',(6.871566503537,18.663376));
#613 = VECTOR('',#614,1.);
#614 = DIRECTION('',(-1.,-0.));
#615 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#616 = PCURVE('',#617,#622);
#617 = PLANE('',#618);
#618 = AXIS2_PLACEMENT_3D('',#619,#620,#621);
#619 = CARTESIAN_POINT('',(51.791249,119.410221,32.5));
#620 = DIRECTION('',(0.,0.,1.));
#621 = DIRECTION('',(1.,0.,0.));
#622 = DEFINITIONAL_REPRESENTATION('',(#623),#631);
#623 = ( BOUNDED_CURVE() B_SPLINE_CURVE(2,(#624,#625,#626,#627,#628,#629
,#630),.UNSPECIFIED.,.T.,.F.) B_SPLINE_CURVE_WITH_KNOTS((1,2,2,2,2,1),(
    -2.094395102393,0.,2.094395102393,4.188790204786,6.28318530718,
8.377580409573),.UNSPECIFIED.) CURVE() GEOMETRIC_REPRESENTATION_ITEM() 
RATIONAL_B_SPLINE_CURVE((1.,0.5,1.,0.5,1.,0.5,1.)) REPRESENTATION_ITEM(
  '') );
#624 = CARTESIAN_POINT('',(3.590598324821,2.395696253599));
#625 = CARTESIAN_POINT('',(7.740065955556,-3.823402474563));
#626 = CARTESIAN_POINT('',(0.279434652957,-4.30739749088));
#627 = CARTESIAN_POINT('',(-7.181196649642,-4.791392507197));
#628 = CARTESIAN_POINT('',(-3.870032977778,1.911701237282));
#629 = CARTESIAN_POINT('',(-0.558869305914,8.61479498176));
#630 = CARTESIAN_POINT('',(3.590598324821,2.395696253599));
#631 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#632 = ORIENTED_EDGE('',*,*,#633,.F.);
#633 = EDGE_CURVE('',#539,#601,#634,.T.);
#634 = SURFACE_CURVE('',#635,(#639,#645),.PCURVE_S1.);
#635 = LINE('',#636,#637);
#636 = CARTESIAN_POINT('',(51.380289556504,115.11337690165,13.836624));
#637 = VECTOR('',#638,1.);
#638 = DIRECTION('',(0.,0.,1.));
#639 = PCURVE('',#550,#640);
#640 = DEFINITIONAL_REPRESENTATION('',(#641),#644);
#641 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#642,#643),.UNSPECIFIED.,.F.,.F.,
  (2,2),(0.,18.663376),.PIECEWISE_BEZIER_KNOTS.);
#642 = CARTESIAN_POINT('',(4.617036858287,0.));
#643 = CARTESIAN_POINT('',(4.617036858287,18.663376));
#644 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#645 = PCURVE('',#646,#651);
#646 = PLANE('',#647);
#647 = AXIS2_PLACEMENT_3D('',#648,#649,#650);
#648 = CARTESIAN_POINT('',(51.468253,113.528152,42.5));
#649 = DIRECTION('',(0.998464000178,5.54043351018E-02,0.));
#650 = DIRECTION('',(-5.54043351018E-02,0.998464000178,0.));
#651 = DEFINITIONAL_REPRESENTATION('',(#652),#655);
#652 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#653,#654),.UNSPECIFIED.,.F.,.F.,
  (2,2),(-5.999999999062E-07,18.6633766),.PIECEWISE_BEZIER_KNOTS.);
#653 = CARTESIAN_POINT('',(1.587663552585,-28.6633766));
#654 = CARTESIAN_POINT('',(1.587663552585,-9.9999994));
#655 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#656 = ADVANCED_FACE('',(#657,#700),#562,.F.);
#657 = FACE_BOUND('',#658,.F.);
#658 = EDGE_LOOP('',(#659,#660,#681));
#659 = ORIENTED_EDGE('',*,*,#538,.T.);
#660 = ORIENTED_EDGE('',*,*,#661,.F.);
#661 = EDGE_CURVE('',#662,#541,#664,.T.);
#662 = VERTEX_POINT('',#663);
#663 = CARTESIAN_POINT('',(51.008936,121.80569,13.836624));
#664 = SURFACE_CURVE('',#665,(#669,#675),.PCURVE_S1.);
#665 = LINE('',#666,#667);
#666 = CARTESIAN_POINT('',(51.40003025449,121.80571032457,13.836624));
#667 = VECTOR('',#668,1.);
#668 = DIRECTION('',(0.99999999865,5.196848995186E-05,-0.));
#669 = PCURVE('',#562,#670);
#670 = DEFINITIONAL_REPRESENTATION('',(#671),#674);
#671 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#672,#673),.UNSPECIFIED.,.F.,.F.,
  (2,2),(-0.391094255018,4.707670636194),.PIECEWISE_BEZIER_KNOTS.);
#672 = CARTESIAN_POINT('',(-0.782313,2.395469));
#673 = CARTESIAN_POINT('',(4.316451884327,2.395733975112));
#674 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#675 = PCURVE('',#589,#676);
#676 = DEFINITIONAL_REPRESENTATION('',(#677),#680);
#677 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#678,#679),.UNSPECIFIED.,.F.,.F.,
  (2,2),(-0.391094255018,4.707670636194),.PIECEWISE_BEZIER_KNOTS.);
#678 = CARTESIAN_POINT('',(-7.105427357601E-15,-28.663376));
#679 = CARTESIAN_POINT('',(5.098764891213,-28.663376));
#680 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#681 = ORIENTED_EDGE('',*,*,#682,.F.);
#682 = EDGE_CURVE('',#539,#662,#683,.T.);
#683 = SURFACE_CURVE('',#684,(#688,#694),.PCURVE_S1.);
#684 = LINE('',#685,#686);
#685 = CARTESIAN_POINT('',(51.306052964914,116.45122463617,13.836624));
#686 = VECTOR('',#687,1.);
#687 = DIRECTION('',(-5.54043351018E-02,0.998464000178,0.));
#688 = PCURVE('',#562,#689);
#689 = DEFINITIONAL_REPRESENTATION('',(#690),#693);
#690 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#691,#692),.UNSPECIFIED.,.F.,.F.,
  (2,2),(-1.359543779503,5.362702473871),.PIECEWISE_BEZIER_KNOTS.);
#691 = CARTESIAN_POINT('',(-0.409871415941,-4.316451884327));
#692 = CARTESIAN_POINT('',(-0.782313,2.395469));
#693 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#694 = PCURVE('',#646,#695);
#695 = DEFINITIONAL_REPRESENTATION('',(#696),#699);
#696 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#697,#698),.UNSPECIFIED.,.F.,.F.,
  (2,2),(-1.359543779503,5.362702473871),.PIECEWISE_BEZIER_KNOTS.);
#697 = CARTESIAN_POINT('',(1.568025602719,-28.663376));
#698 = CARTESIAN_POINT('',(8.290271856093,-28.663376));
#699 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#700 = FACE_BOUND('',#701,.F.);
#701 = EDGE_LOOP('',(#702));
#702 = ORIENTED_EDGE('',*,*,#703,.F.);
#703 = EDGE_CURVE('',#704,#704,#706,.T.);
#704 = VERTEX_POINT('',#705);
#705 = CARTESIAN_POINT('',(52.066249,119.410221,13.836624));
#706 = SURFACE_CURVE('',#707,(#712,#719),.PCURVE_S1.);
#707 = CIRCLE('',#708,0.275);
#708 = AXIS2_PLACEMENT_3D('',#709,#710,#711);
#709 = CARTESIAN_POINT('',(51.791249,119.410221,13.836624));
#710 = DIRECTION('',(0.,0.,1.));
#711 = DIRECTION('',(1.,0.,0.));
#712 = PCURVE('',#562,#713);
#713 = DEFINITIONAL_REPRESENTATION('',(#714),#718);
#714 = CIRCLE('',#715,0.275);
#715 = AXIS2_PLACEMENT_2D('',#716,#717);
#716 = CARTESIAN_POINT('',(0.,0.));
#717 = DIRECTION('',(1.,0.));
#718 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#719 = PCURVE('',#720,#725);
#720 = CYLINDRICAL_SURFACE('',#721,0.275);
#721 = AXIS2_PLACEMENT_3D('',#722,#723,#724);
#722 = CARTESIAN_POINT('',(51.791249,119.410221,13.836624));
#723 = DIRECTION('',(0.,0.,1.));
#724 = DIRECTION('',(1.,0.,0.));
#725 = DEFINITIONAL_REPRESENTATION('',(#726),#730);
#726 = LINE('',#727,#728);
#727 = CARTESIAN_POINT('',(0.,0.));
#728 = VECTOR('',#729,1.);
#729 = DIRECTION('',(1.,0.));
#730 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#731 = ADVANCED_FACE('',(#732),#646,.F.);
#732 = FACE_BOUND('',#733,.F.);
#733 = EDGE_LOOP('',(#734,#757,#776,#777));
#734 = ORIENTED_EDGE('',*,*,#735,.F.);
#735 = EDGE_CURVE('',#736,#662,#738,.T.);
#736 = VERTEX_POINT('',#737);
#737 = CARTESIAN_POINT('',(51.008936,121.80569,32.5));
#738 = SURFACE_CURVE('',#739,(#743,#750),.PCURVE_S1.);
#739 = LINE('',#740,#741);
#740 = CARTESIAN_POINT('',(51.008936,121.80569,42.5));
#741 = VECTOR('',#742,1.);
#742 = DIRECTION('',(0.,0.,-1.));
#743 = PCURVE('',#646,#744);
#744 = DEFINITIONAL_REPRESENTATION('',(#745),#749);
#745 = LINE('',#746,#747);
#746 = CARTESIAN_POINT('',(8.290271856093,0.));
#747 = VECTOR('',#748,1.);
#748 = DIRECTION('',(0.,-1.));
#749 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#750 = PCURVE('',#589,#751);
#751 = DEFINITIONAL_REPRESENTATION('',(#752),#756);
#752 = LINE('',#753,#754);
#753 = CARTESIAN_POINT('',(0.,0.));
#754 = VECTOR('',#755,1.);
#755 = DIRECTION('',(0.,-1.));
#756 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#757 = ORIENTED_EDGE('',*,*,#758,.F.);
#758 = EDGE_CURVE('',#601,#736,#759,.T.);
#759 = SURFACE_CURVE('',#760,(#764,#770),.PCURVE_S1.);
#760 = LINE('',#761,#762);
#761 = CARTESIAN_POINT('',(51.306052964914,116.45122463617,32.5));
#762 = VECTOR('',#763,1.);
#763 = DIRECTION('',(-5.54043351018E-02,0.998464000178,0.));
#764 = PCURVE('',#646,#765);
#765 = DEFINITIONAL_REPRESENTATION('',(#766),#769);
#766 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#767,#768),.UNSPECIFIED.,.F.,.F.,
  (2,2),(-1.359543779503,5.362702473871),.PIECEWISE_BEZIER_KNOTS.);
#767 = CARTESIAN_POINT('',(1.568025602719,-10.));
#768 = CARTESIAN_POINT('',(8.290271856093,-10.));
#769 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#770 = PCURVE('',#617,#771);
#771 = DEFINITIONAL_REPRESENTATION('',(#772),#775);
#772 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#773,#774),.UNSPECIFIED.,.F.,.F.,
  (2,2),(-1.359543779503,5.362702473871),.PIECEWISE_BEZIER_KNOTS.);
#773 = CARTESIAN_POINT('',(-0.409871415941,-4.316451884327));
#774 = CARTESIAN_POINT('',(-0.782313,2.395469));
#775 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#776 = ORIENTED_EDGE('',*,*,#633,.F.);
#777 = ORIENTED_EDGE('',*,*,#682,.T.);
#778 = ADVANCED_FACE('',(#779,#802),#617,.T.);
#779 = FACE_BOUND('',#780,.T.);
#780 = EDGE_LOOP('',(#781,#782,#783));
#781 = ORIENTED_EDGE('',*,*,#758,.F.);
#782 = ORIENTED_EDGE('',*,*,#600,.F.);
#783 = ORIENTED_EDGE('',*,*,#784,.F.);
#784 = EDGE_CURVE('',#736,#575,#785,.T.);
#785 = SURFACE_CURVE('',#786,(#790,#796),.PCURVE_S1.);
#786 = LINE('',#787,#788);
#787 = CARTESIAN_POINT('',(51.40003025449,121.80571032457,32.5));
#788 = VECTOR('',#789,1.);
#789 = DIRECTION('',(0.99999999865,5.196848995186E-05,-0.));
#790 = PCURVE('',#617,#791);
#791 = DEFINITIONAL_REPRESENTATION('',(#792),#795);
#792 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#793,#794),.UNSPECIFIED.,.F.,.F.,
  (2,2),(-0.391094255018,4.707670636194),.PIECEWISE_BEZIER_KNOTS.);
#793 = CARTESIAN_POINT('',(-0.782313,2.395469));
#794 = CARTESIAN_POINT('',(4.316451884327,2.395733975112));
#795 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#796 = PCURVE('',#589,#797);
#797 = DEFINITIONAL_REPRESENTATION('',(#798),#801);
#798 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#799,#800),.UNSPECIFIED.,.F.,.F.,
  (2,2),(-0.391094255018,4.707670636194),.PIECEWISE_BEZIER_KNOTS.);
#799 = CARTESIAN_POINT('',(-7.105427357601E-15,-10.));
#800 = CARTESIAN_POINT('',(5.098764891213,-10.));
#801 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#802 = FACE_BOUND('',#803,.T.);
#803 = EDGE_LOOP('',(#804));
#804 = ORIENTED_EDGE('',*,*,#805,.F.);
#805 = EDGE_CURVE('',#806,#806,#808,.T.);
#806 = VERTEX_POINT('',#807);
#807 = CARTESIAN_POINT('',(52.066249,119.410221,32.5));
#808 = SURFACE_CURVE('',#809,(#814,#821),.PCURVE_S1.);
#809 = CIRCLE('',#810,0.275);
#810 = AXIS2_PLACEMENT_3D('',#811,#812,#813);
#811 = CARTESIAN_POINT('',(51.791249,119.410221,32.5));
#812 = DIRECTION('',(0.,0.,1.));
#813 = DIRECTION('',(1.,0.,0.));
#814 = PCURVE('',#617,#815);
#815 = DEFINITIONAL_REPRESENTATION('',(#816),#820);
#816 = CIRCLE('',#817,0.275);
#817 = AXIS2_PLACEMENT_2D('',#818,#819);
#818 = CARTESIAN_POINT('',(0.,0.));
#819 = DIRECTION('',(1.,0.));
#820 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#821 = PCURVE('',#720,#822);
#822 = DEFINITIONAL_REPRESENTATION('',(#823),#827);
#823 = LINE('',#824,#825);
#824 = CARTESIAN_POINT('',(0.,18.663376));
#825 = VECTOR('',#826,1.);
#826 = DIRECTION('',(1.,0.));
#827 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#828 = ADVANCED_FACE('',(#829),#589,.F.);
#829 = FACE_BOUND('',#830,.F.);
#830 = EDGE_LOOP('',(#831,#832,#833,#834));
#831 = ORIENTED_EDGE('',*,*,#784,.F.);
#832 = ORIENTED_EDGE('',*,*,#735,.T.);
#833 = ORIENTED_EDGE('',*,*,#661,.T.);
#834 = ORIENTED_EDGE('',*,*,#574,.T.);
#835 = ADVANCED_FACE('',(#836),#720,.F.);
#836 = FACE_BOUND('',#837,.F.);
#837 = EDGE_LOOP('',(#838,#839,#860,#861));
#838 = ORIENTED_EDGE('',*,*,#805,.F.);
#839 = ORIENTED_EDGE('',*,*,#840,.F.);
#840 = EDGE_CURVE('',#704,#806,#841,.T.);
#841 = SEAM_CURVE('',#842,(#846,#853),.PCURVE_S1.);
#842 = LINE('',#843,#844);
#843 = CARTESIAN_POINT('',(52.066249,119.410221,13.836624));
#844 = VECTOR('',#845,1.);
#845 = DIRECTION('',(0.,0.,1.));
#846 = PCURVE('',#720,#847);
#847 = DEFINITIONAL_REPRESENTATION('',(#848),#852);
#848 = LINE('',#849,#850);
#849 = CARTESIAN_POINT('',(6.28318530718,-0.));
#850 = VECTOR('',#851,1.);
#851 = DIRECTION('',(0.,1.));
#852 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#853 = PCURVE('',#720,#854);
#854 = DEFINITIONAL_REPRESENTATION('',(#855),#859);
#855 = LINE('',#856,#857);
#856 = CARTESIAN_POINT('',(0.,-0.));
#857 = VECTOR('',#858,1.);
#858 = DIRECTION('',(0.,1.));
#859 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#860 = ORIENTED_EDGE('',*,*,#703,.T.);
#861 = ORIENTED_EDGE('',*,*,#840,.T.);
#862 = ( GEOMETRIC_REPRESENTATION_CONTEXT(3) 
GLOBAL_UNCERTAINTY_ASSIGNED_CONTEXT((#866)) GLOBAL_UNIT_ASSIGNED_CONTEXT
((#863,#864,#865)) REPRESENTATION_CONTEXT('Context #1',
  '3D Context with UNIT and UNCERTAINTY') );
#863 = ( LENGTH_UNIT() NAMED_UNIT(*) SI_UNIT(.MILLI.,.METRE.) );
#864 = ( NAMED_UNIT(*) PLANE_ANGLE_UNIT() SI_UNIT($,.RADIAN.) );
#865 = ( NAMED_UNIT(*) SI_UNIT($,.STERADIAN.) SOLID_ANGLE_UNIT() );
#866 = UNCERTAINTY_MEASURE_WITH_UNIT(LENGTH_MEASURE(1.E-07),#863,
  'distance_accuracy_value','confusion accuracy');
#867 = CONTEXT_DEPENDENT_SHAPE_REPRESENTATION(#868,#870);
#868 = ( REPRESENTATION_RELATIONSHIP('','',#531,#10) 
REPRESENTATION_RELATIONSHIP_WITH_TRANSFORMATION(#869) 
SHAPE_REPRESENTATION_RELATIONSHIP() );
#869 = ITEM_DEFINED_TRANSFORMATION('','',#11,#23);
#870 = PRODUCT_DEFINITION_SHAPE('Placement','Placement of an item',#871
  );
#871 = NEXT_ASSEMBLY_USAGE_OCCURRENCE('110','=>[0:1:1:4]','',#5,#526,$);
#872 = PRODUCT_RELATED_PRODUCT_CATEGORY('part',$,(#528));
#873 = MECHANICAL_DESIGN_GEOMETRIC_PRESENTATION_REPRESENTATION('',(#874)
  ,#862);
#874 = STYLED_ITEM('color',(#875),#532);
#875 = PRESENTATION_STYLE_ASSIGNMENT((#876));
#876 = SURFACE_STYLE_USAGE(.BOTH.,#877);
#877 = SURFACE_SIDE_STYLE('',(#878));
#878 = SURFACE_STYLE_FILL_AREA(#879);
#879 = FILL_AREA_STYLE('',(#880));
#880 = FILL_AREA_STYLE_COLOUR('',#881);
#881 = DRAUGHTING_PRE_DEFINED_COLOUR('red');
#882 = MECHANICAL_DESIGN_GEOMETRIC_PRESENTATION_REPRESENTATION('',(#883)
  ,#139);
#883 = STYLED_ITEM('color',(#884),#41);
#884 = PRESENTATION_STYLE_ASSIGNMENT((#885));
#885 = SURFACE_STYLE_USAGE(.BOTH.,#886);
#886 = SURFACE_SIDE_STYLE('',(#887));
#887 = SURFACE_STYLE_FILL_AREA(#888);
#888 = FILL_AREA_STYLE('',(#889));
#889 = FILL_AREA_STYLE_COLOUR('',#890);
#890 = DRAUGHTING_PRE_DEFINED_COLOUR('blue');
#891 = MECHANICAL_DESIGN_GEOMETRIC_PRESENTATION_REPRESENTATION('',(#892)
  ,#513);
#892 = STYLED_ITEM('color',(#893),#158);
#893 = PRESENTATION_STYLE_ASSIGNMENT((#894));
#894 = SURFACE_STYLE_USAGE(.BOTH.,#895);
#895 = SURFACE_SIDE_STYLE('',(#896));
#896 = SURFACE_STYLE_FILL_AREA(#897);
#897 = FILL_AREA_STYLE('',(#898));
#898 = FILL_AREA_STYLE_COLOUR('',#899);
#899 = DRAUGHTING_PRE_DEFINED_COLOUR('green');
ENDSEC;
END-ISO-10303-21;
