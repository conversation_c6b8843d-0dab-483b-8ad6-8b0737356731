ISO-10303-21;
HEADER;
FILE_DESCRIPTION(('Open CASCADE Model'),'2;1');
FILE_NAME('Pile_PC2-P6','2025-08-25T20:08:32',('Author'),('Open CASCADE'
    ),'Open CASCADE STEP processor 7.8','build123d','Unknown');
FILE_SCHEMA(('AUTOMOTIVE_DESIGN { 1 0 10303 214 1 1 1 1 }'));
ENDSEC;
DATA;
#1 = APPLICATION_PROTOCOL_DEFINITION('international standard',
  'automotive_design',2000,#2);
#2 = APPLICATION_CONTEXT(
  'core data for automotive mechanical design processes');
#3 = SHAPE_DEFINITION_REPRESENTATION(#4,#10);
#4 = PRODUCT_DEFINITION_SHAPE('','',#5);
#5 = PRODUCT_DEFINITION('design','',#6,#9);
#6 = PRODUCT_DEFINITION_FORMATION('','',#7);
#7 = PRODUCT('Pile_PC2-P6','Pile_PC2-P6','',(#8));
#8 = PRODUCT_CONTEXT('',#2,'mechanical');
#9 = PRODUCT_DEFINITION_CONTEXT('part definition',#2,'design');
#10 = SHAPE_REPRESENTATION('',(#11,#15,#19,#23),#27);
#11 = AXIS2_PLACEMENT_3D('',#12,#13,#14);
#12 = CARTESIAN_POINT('',(0.,0.,0.));
#13 = DIRECTION('',(0.,0.,1.));
#14 = DIRECTION('',(1.,0.,-0.));
#15 = AXIS2_PLACEMENT_3D('',#16,#17,#18);
#16 = CARTESIAN_POINT('',(53.316931,113.40648,9.41011));
#17 = DIRECTION('',(0.,0.,1.));
#18 = DIRECTION('',(1.,0.,0.));
#19 = AXIS2_PLACEMENT_3D('',#20,#21,#22);
#20 = CARTESIAN_POINT('',(0.,0.,0.));
#21 = DIRECTION('',(0.,0.,1.));
#22 = DIRECTION('',(1.,0.,-0.));
#23 = AXIS2_PLACEMENT_3D('',#24,#25,#26);
#24 = CARTESIAN_POINT('',(0.,0.,0.));
#25 = DIRECTION('',(0.,0.,1.));
#26 = DIRECTION('',(1.,0.,-0.));
#27 = ( GEOMETRIC_REPRESENTATION_CONTEXT(3) 
GLOBAL_UNCERTAINTY_ASSIGNED_CONTEXT((#31)) GLOBAL_UNIT_ASSIGNED_CONTEXT(
(#28,#29,#30)) REPRESENTATION_CONTEXT('Context #1',
  '3D Context with UNIT and UNCERTAINTY') );
#28 = ( LENGTH_UNIT() NAMED_UNIT(*) SI_UNIT(.MILLI.,.METRE.) );
#29 = ( NAMED_UNIT(*) PLANE_ANGLE_UNIT() SI_UNIT($,.RADIAN.) );
#30 = ( NAMED_UNIT(*) SI_UNIT($,.STERADIAN.) SOLID_ANGLE_UNIT() );
#31 = UNCERTAINTY_MEASURE_WITH_UNIT(LENGTH_MEASURE(1.E-07),#28,
  'distance_accuracy_value','confusion accuracy');
#32 = PRODUCT_RELATED_PRODUCT_CATEGORY('part',$,(#7));
#33 = SHAPE_DEFINITION_REPRESENTATION(#34,#40);
#34 = PRODUCT_DEFINITION_SHAPE('','',#35);
#35 = PRODUCT_DEFINITION('design','',#36,#39);
#36 = PRODUCT_DEFINITION_FORMATION('','',#37);
#37 = PRODUCT('PC2-P6_Part1','PC2-P6_Part1','',(#38));
#38 = PRODUCT_CONTEXT('',#2,'mechanical');
#39 = PRODUCT_DEFINITION_CONTEXT('part definition',#2,'design');
#40 = ADVANCED_BREP_SHAPE_REPRESENTATION('',(#11,#41),#139);
#41 = MANIFOLD_SOLID_BREP('',#42);
#42 = CLOSED_SHELL('',(#43,#131,#135));
#43 = ADVANCED_FACE('',(#44),#57,.T.);
#44 = FACE_BOUND('',#45,.T.);
#45 = EDGE_LOOP('',(#46,#80,#103,#130));
#46 = ORIENTED_EDGE('',*,*,#47,.F.);
#47 = EDGE_CURVE('',#48,#48,#50,.T.);
#48 = VERTEX_POINT('',#49);
#49 = CARTESIAN_POINT('',(0.275,-6.73555739531E-17,23.08989));
#50 = SURFACE_CURVE('',#51,(#56,#68),.PCURVE_S1.);
#51 = CIRCLE('',#52,0.275);
#52 = AXIS2_PLACEMENT_3D('',#53,#54,#55);
#53 = CARTESIAN_POINT('',(0.,0.,23.08989));
#54 = DIRECTION('',(0.,0.,1.));
#55 = DIRECTION('',(1.,0.,-0.));
#56 = PCURVE('',#57,#62);
#57 = CYLINDRICAL_SURFACE('',#58,0.275);
#58 = AXIS2_PLACEMENT_3D('',#59,#60,#61);
#59 = CARTESIAN_POINT('',(0.,0.,0.));
#60 = DIRECTION('',(0.,0.,1.));
#61 = DIRECTION('',(1.,0.,-0.));
#62 = DEFINITIONAL_REPRESENTATION('',(#63),#67);
#63 = LINE('',#64,#65);
#64 = CARTESIAN_POINT('',(0.,23.08989));
#65 = VECTOR('',#66,1.);
#66 = DIRECTION('',(1.,0.));
#67 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#68 = PCURVE('',#69,#74);
#69 = PLANE('',#70);
#70 = AXIS2_PLACEMENT_3D('',#71,#72,#73);
#71 = CARTESIAN_POINT('',(0.,0.,23.08989));
#72 = DIRECTION('',(0.,0.,1.));
#73 = DIRECTION('',(1.,0.,-0.));
#74 = DEFINITIONAL_REPRESENTATION('',(#75),#79);
#75 = CIRCLE('',#76,0.275);
#76 = AXIS2_PLACEMENT_2D('',#77,#78);
#77 = CARTESIAN_POINT('',(0.,0.));
#78 = DIRECTION('',(1.,0.));
#79 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#80 = ORIENTED_EDGE('',*,*,#81,.F.);
#81 = EDGE_CURVE('',#82,#48,#84,.T.);
#82 = VERTEX_POINT('',#83);
#83 = CARTESIAN_POINT('',(0.275,-6.73555739531E-17,0.));
#84 = SEAM_CURVE('',#85,(#89,#96),.PCURVE_S1.);
#85 = LINE('',#86,#87);
#86 = CARTESIAN_POINT('',(0.275,-6.73555739531E-17,0.));
#87 = VECTOR('',#88,1.);
#88 = DIRECTION('',(0.,0.,1.));
#89 = PCURVE('',#57,#90);
#90 = DEFINITIONAL_REPRESENTATION('',(#91),#95);
#91 = LINE('',#92,#93);
#92 = CARTESIAN_POINT('',(6.28318530718,-0.));
#93 = VECTOR('',#94,1.);
#94 = DIRECTION('',(0.,1.));
#95 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#96 = PCURVE('',#57,#97);
#97 = DEFINITIONAL_REPRESENTATION('',(#98),#102);
#98 = LINE('',#99,#100);
#99 = CARTESIAN_POINT('',(0.,-0.));
#100 = VECTOR('',#101,1.);
#101 = DIRECTION('',(0.,1.));
#102 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#103 = ORIENTED_EDGE('',*,*,#104,.T.);
#104 = EDGE_CURVE('',#82,#82,#105,.T.);
#105 = SURFACE_CURVE('',#106,(#111,#118),.PCURVE_S1.);
#106 = CIRCLE('',#107,0.275);
#107 = AXIS2_PLACEMENT_3D('',#108,#109,#110);
#108 = CARTESIAN_POINT('',(0.,0.,0.));
#109 = DIRECTION('',(0.,0.,1.));
#110 = DIRECTION('',(1.,0.,-0.));
#111 = PCURVE('',#57,#112);
#112 = DEFINITIONAL_REPRESENTATION('',(#113),#117);
#113 = LINE('',#114,#115);
#114 = CARTESIAN_POINT('',(0.,0.));
#115 = VECTOR('',#116,1.);
#116 = DIRECTION('',(1.,0.));
#117 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#118 = PCURVE('',#119,#124);
#119 = PLANE('',#120);
#120 = AXIS2_PLACEMENT_3D('',#121,#122,#123);
#121 = CARTESIAN_POINT('',(0.,0.,0.));
#122 = DIRECTION('',(0.,0.,1.));
#123 = DIRECTION('',(1.,0.,-0.));
#124 = DEFINITIONAL_REPRESENTATION('',(#125),#129);
#125 = CIRCLE('',#126,0.275);
#126 = AXIS2_PLACEMENT_2D('',#127,#128);
#127 = CARTESIAN_POINT('',(0.,0.));
#128 = DIRECTION('',(1.,0.));
#129 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#130 = ORIENTED_EDGE('',*,*,#81,.T.);
#131 = ADVANCED_FACE('',(#132),#69,.T.);
#132 = FACE_BOUND('',#133,.T.);
#133 = EDGE_LOOP('',(#134));
#134 = ORIENTED_EDGE('',*,*,#47,.T.);
#135 = ADVANCED_FACE('',(#136),#119,.F.);
#136 = FACE_BOUND('',#137,.T.);
#137 = EDGE_LOOP('',(#138));
#138 = ORIENTED_EDGE('',*,*,#104,.F.);
#139 = ( GEOMETRIC_REPRESENTATION_CONTEXT(3) 
GLOBAL_UNCERTAINTY_ASSIGNED_CONTEXT((#143)) GLOBAL_UNIT_ASSIGNED_CONTEXT
((#140,#141,#142)) REPRESENTATION_CONTEXT('Context #1',
  '3D Context with UNIT and UNCERTAINTY') );
#140 = ( LENGTH_UNIT() NAMED_UNIT(*) SI_UNIT(.MILLI.,.METRE.) );
#141 = ( NAMED_UNIT(*) PLANE_ANGLE_UNIT() SI_UNIT($,.RADIAN.) );
#142 = ( NAMED_UNIT(*) SI_UNIT($,.STERADIAN.) SOLID_ANGLE_UNIT() );
#143 = UNCERTAINTY_MEASURE_WITH_UNIT(LENGTH_MEASURE(1.E-07),#140,
  'distance_accuracy_value','confusion accuracy');
#144 = CONTEXT_DEPENDENT_SHAPE_REPRESENTATION(#145,#147);
#145 = ( REPRESENTATION_RELATIONSHIP('','',#40,#10) 
REPRESENTATION_RELATIONSHIP_WITH_TRANSFORMATION(#146) 
SHAPE_REPRESENTATION_RELATIONSHIP() );
#146 = ITEM_DEFINED_TRANSFORMATION('','',#11,#15);
#147 = PRODUCT_DEFINITION_SHAPE('Placement','Placement of an item',#148
  );
#148 = NEXT_ASSEMBLY_USAGE_OCCURRENCE('120','=>[0:1:1:2]','',#5,#35,$);
#149 = PRODUCT_RELATED_PRODUCT_CATEGORY('part',$,(#37));
#150 = SHAPE_DEFINITION_REPRESENTATION(#151,#157);
#151 = PRODUCT_DEFINITION_SHAPE('','',#152);
#152 = PRODUCT_DEFINITION('design','',#153,#156);
#153 = PRODUCT_DEFINITION_FORMATION('','',#154);
#154 = PRODUCT('PC2-P6_Part2','PC2-P6_Part2','',(#155));
#155 = PRODUCT_CONTEXT('',#2,'mechanical');
#156 = PRODUCT_DEFINITION_CONTEXT('part definition',#2,'design');
#157 = ADVANCED_BREP_SHAPE_REPRESENTATION('',(#11,#158),#587);
#158 = MANIFOLD_SOLID_BREP('',#159);
#159 = CLOSED_SHELL('',(#160,#409,#459,#528,#554,#581));
#160 = ADVANCED_FACE('',(#161),#175,.T.);
#161 = FACE_BOUND('',#162,.T.);
#162 = EDGE_LOOP('',(#163,#193,#222,#285,#318,#360,#382,#383));
#163 = ORIENTED_EDGE('',*,*,#164,.T.);
#164 = EDGE_CURVE('',#165,#167,#169,.T.);
#165 = VERTEX_POINT('',#166);
#166 = CARTESIAN_POINT('',(53.591931,113.40648,9.41011));
#167 = VERTEX_POINT('',#168);
#168 = CARTESIAN_POINT('',(57.633382884327,113.40648,16.41011));
#169 = SEAM_CURVE('',#170,(#174,#186),.PCURVE_S1.);
#170 = LINE('',#171,#172);
#171 = CARTESIAN_POINT('',(53.591931,113.40648,9.41011));
#172 = VECTOR('',#173,1.);
#173 = DIRECTION('',(0.5,-1.224646799147E-16,0.866025403784));
#174 = PCURVE('',#175,#180);
#175 = CONICAL_SURFACE('',#176,0.275,0.523598775598);
#176 = AXIS2_PLACEMENT_3D('',#177,#178,#179);
#177 = CARTESIAN_POINT('',(53.316931,113.40648,9.41011));
#178 = DIRECTION('',(0.,0.,1.));
#179 = DIRECTION('',(1.,0.,0.));
#180 = DEFINITIONAL_REPRESENTATION('',(#181),#185);
#181 = LINE('',#182,#183);
#182 = CARTESIAN_POINT('',(6.28318530718,-0.));
#183 = VECTOR('',#184,1.);
#184 = DIRECTION('',(0.,1.));
#185 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#186 = PCURVE('',#175,#187);
#187 = DEFINITIONAL_REPRESENTATION('',(#188),#192);
#188 = LINE('',#189,#190);
#189 = CARTESIAN_POINT('',(0.,-0.));
#190 = VECTOR('',#191,1.);
#191 = DIRECTION('',(0.,1.));
#192 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#193 = ORIENTED_EDGE('',*,*,#194,.F.);
#194 = EDGE_CURVE('',#195,#167,#197,.T.);
#195 = VERTEX_POINT('',#196);
#196 = CARTESIAN_POINT('',(57.604322032841,112.906445,16.41011));
#197 = SURFACE_CURVE('',#198,(#203,#210),.PCURVE_S1.);
#198 = CIRCLE('',#199,4.316451884327);
#199 = AXIS2_PLACEMENT_3D('',#200,#201,#202);
#200 = CARTESIAN_POINT('',(53.316931,113.40648,16.41011));
#201 = DIRECTION('',(0.,0.,1.));
#202 = DIRECTION('',(1.,0.,0.));
#203 = PCURVE('',#175,#204);
#204 = DEFINITIONAL_REPRESENTATION('',(#205),#209);
#205 = LINE('',#206,#207);
#206 = CARTESIAN_POINT('',(0.,7.));
#207 = VECTOR('',#208,1.);
#208 = DIRECTION('',(1.,0.));
#209 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#210 = PCURVE('',#211,#216);
#211 = PLANE('',#212);
#212 = AXIS2_PLACEMENT_3D('',#213,#214,#215);
#213 = CARTESIAN_POINT('',(53.316931,113.40648,16.41011));
#214 = DIRECTION('',(0.,0.,1.));
#215 = DIRECTION('',(1.,0.,0.));
#216 = DEFINITIONAL_REPRESENTATION('',(#217),#221);
#217 = CIRCLE('',#218,4.316451884327);
#218 = AXIS2_PLACEMENT_2D('',#219,#220);
#219 = CARTESIAN_POINT('',(0.,0.));
#220 = DIRECTION('',(1.,0.));
#221 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#222 = ORIENTED_EDGE('',*,*,#223,.F.);
#223 = EDGE_CURVE('',#224,#195,#226,.T.);
#224 = VERTEX_POINT('',#225);
#225 = CARTESIAN_POINT('',(51.710833,112.906445,11.847343240594));
#226 = SURFACE_CURVE('',#227,(#232,#273),.PCURVE_S1.);
#227 = HYPERBOLA('',#228,0.866086025563,0.500035);
#228 = AXIS2_PLACEMENT_3D('',#229,#230,#231);
#229 = CARTESIAN_POINT('',(53.316931,112.906445,8.933796027919));
#230 = DIRECTION('',(0.,1.,0.));
#231 = DIRECTION('',(0.,-0.,1.));
#232 = PCURVE('',#175,#233);
#233 = DEFINITIONAL_REPRESENTATION('',(#234),#272);
#234 = B_SPLINE_CURVE_WITH_KNOTS('',8,(#235,#236,#237,#238,#239,#240,
    #241,#242,#243,#244,#245,#246,#247,#248,#249,#250,#251,#252,#253,
    #254,#255,#256,#257,#258,#259,#260,#261,#262,#263,#264,#265,#266,
    #267,#268,#269,#270,#271),.UNSPECIFIED.,.F.,.F.,(9,7,7,7,7,9),(
    -2.706235271841,-1.318354919557,-0.277444655344,0.503238042816,
    1.674262090055,2.845286137295),.UNSPECIFIED.);
#235 = CARTESIAN_POINT('',(3.274970117774,6.036474752791));
#236 = CARTESIAN_POINT('',(3.298040568641,4.91663837529));
#237 = CARTESIAN_POINT('',(3.325644467935,4.020819979634));
#238 = CARTESIAN_POINT('',(3.358803157113,3.29766207341));
#239 = CARTESIAN_POINT('',(3.398794173814,2.710187165798));
#240 = CARTESIAN_POINT('',(3.44723560179,2.230866447538));
#241 = CARTESIAN_POINT('',(3.506100269986,1.83894025674));
#242 = CARTESIAN_POINT('',(3.577855722005,1.51862130036));
#243 = CARTESIAN_POINT('',(3.729471554035,1.062456063416));
#244 = CARTESIAN_POINT('',(3.802820675171,0.900511510995));
#245 = CARTESIAN_POINT('',(3.885383398781,0.767078413596));
#246 = CARTESIAN_POINT('',(3.977840636526,0.658325209764));
#247 = CARTESIAN_POINT('',(4.080524051482,0.571359203569));
#248 = CARTESIAN_POINT('',(4.192953007231,0.504062002893));
#249 = CARTESIAN_POINT('',(4.313174998284,0.454987963116));
#250 = CARTESIAN_POINT('',(4.532383032369,0.399569280779));
#251 = CARTESIAN_POINT('',(4.629163532085,0.385609412327));
#252 = CARTESIAN_POINT('',(4.72761665333,0.381095911784));
#253 = CARTESIAN_POINT('',(4.82631145933,0.385882969243));
#254 = CARTESIAN_POINT('',(4.923688112076,0.40001336242));
#255 = CARTESIAN_POINT('',(5.018339409806,0.423717649487));
#256 = CARTESIAN_POINT('',(5.109228700267,0.457423216709));
#257 = CARTESIAN_POINT('',(5.325255613929,0.568299207164));
#258 = CARTESIAN_POINT('',(5.444796739983,0.658775679351));
#259 = CARTESIAN_POINT('',(5.551855360249,0.775375041208));
#260 = CARTESIAN_POINT('',(5.645742510245,0.921364329567));
#261 = CARTESIAN_POINT('',(5.727479718505,1.10125359679));
#262 = CARTESIAN_POINT('',(5.798299060949,1.321072290816));
#263 = CARTESIAN_POINT('',(5.859579489648,1.588797049908));
#264 = CARTESIAN_POINT('',(5.965606263385,2.241364895551));
#265 = CARTESIAN_POINT('',(6.010352810023,2.626207579432));
#266 = CARTESIAN_POINT('',(6.048111683753,3.08026490768));
#267 = CARTESIAN_POINT('',(6.080071989212,3.61685893408));
#268 = CARTESIAN_POINT('',(6.107218961174,4.252738860094));
#269 = CARTESIAN_POINT('',(6.130348707547,5.009075409858));
#270 = CARTESIAN_POINT('',(6.150123628896,5.912999987334));
#271 = CARTESIAN_POINT('',(6.167080640846,7.));
#272 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#273 = PCURVE('',#274,#279);
#274 = PLANE('',#275);
#275 = AXIS2_PLACEMENT_3D('',#276,#277,#278);
#276 = CARTESIAN_POINT('',(72.917494,112.906445,42.5));
#277 = DIRECTION('',(0.,1.,0.));
#278 = DIRECTION('',(-1.,0.,0.));
#279 = DEFINITIONAL_REPRESENTATION('',(#280),#284);
#280 = ( BOUNDED_CURVE() B_SPLINE_CURVE(2,(#281,#282,#283),
.UNSPECIFIED.,.F.,.F.) B_SPLINE_CURVE_WITH_KNOTS((3,3),(-2.706235271841,
2.845286137295),.PIECEWISE_BEZIER_KNOTS.) CURVE() 
GEOMETRIC_REPRESENTATION_ITEM() RATIONAL_B_SPLINE_CURVE((1.,
8.056567179697,1.)) REPRESENTATION_ITEM('') );
#281 = CARTESIAN_POINT('',(23.3273271,-27.0534152472));
#282 = CARTESIAN_POINT('',(19.596244390892,-33.45844342262));
#283 = CARTESIAN_POINT('',(15.313171967159,-26.08989));
#284 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#285 = ORIENTED_EDGE('',*,*,#286,.F.);
#286 = EDGE_CURVE('',#287,#224,#289,.T.);
#287 = VERTEX_POINT('',#288);
#288 = CARTESIAN_POINT('',(51.468253,113.528152,12.142727823673));
#289 = SURFACE_CURVE('',#290,(#295,#306),.PCURVE_S1.);
#290 = HYPERBOLA('',#291,2.906372723362,1.677995074198);
#291 = AXIS2_PLACEMENT_3D('',#292,#293,#294);
#292 = CARTESIAN_POINT('',(51.753716526733,112.79653903596,
    8.933796027919));
#293 = DIRECTION('',(0.931596580528,0.363493894237,0.));
#294 = DIRECTION('',(-0.,0.,1.));
#295 = PCURVE('',#175,#296);
#296 = DEFINITIONAL_REPRESENTATION('',(#297),#305);
#297 = B_SPLINE_CURVE_WITH_KNOTS('',6,(#298,#299,#300,#301,#302,#303,
    #304),.UNSPECIFIED.,.F.,.F.,(7,7),(-0.488167957091,
    -3.05318618963E-02),.PIECEWISE_BEZIER_KNOTS.);
#298 = CARTESIAN_POINT('',(3.043748597506,2.783296770832));
#299 = CARTESIAN_POINT('',(3.11175574975,2.670731629893));
#300 = CARTESIAN_POINT('',(3.182581029459,2.580921914002));
#301 = CARTESIAN_POINT('',(3.255804832477,2.512688922938));
#302 = CARTESIAN_POINT('',(3.330819951384,2.465250951804));
#303 = CARTESIAN_POINT('',(3.406843987388,2.438182767368));
#304 = CARTESIAN_POINT('',(3.483081133233,2.431413508983));
#305 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#306 = PCURVE('',#307,#312);
#307 = PLANE('',#308);
#308 = AXIS2_PLACEMENT_3D('',#309,#310,#311);
#309 = CARTESIAN_POINT('',(51.710833,112.906445,42.5));
#310 = DIRECTION('',(0.931596580528,0.363493894237,0.));
#311 = DIRECTION('',(-0.363493894237,0.931596580528,0.));
#312 = DEFINITIONAL_REPRESENTATION('',(#313),#317);
#313 = ( BOUNDED_CURVE() B_SPLINE_CURVE(2,(#314,#315,#316),
.UNSPECIFIED.,.F.,.F.) B_SPLINE_CURVE_WITH_KNOTS((3,3),(-0.488167957091,
-3.05318618963E-02),.PIECEWISE_BEZIER_KNOTS.) CURVE() 
GEOMETRIC_REPRESENTATION_ITEM() RATIONAL_B_SPLINE_CURVE((1.,
1.026293271013,1.)) REPRESENTATION_ITEM('') );
#314 = CARTESIAN_POINT('',(0.73409211057,-30.30659322916));
#315 = CARTESIAN_POINT('',(0.310832243008,-30.63851586998));
#316 = CARTESIAN_POINT('',(-6.673564641546E-02,-30.65847649101));
#317 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#318 = ORIENTED_EDGE('',*,*,#319,.F.);
#319 = EDGE_CURVE('',#320,#287,#322,.T.);
#320 = VERTEX_POINT('',#321);
#321 = CARTESIAN_POINT('',(51.264301435687,117.20364588279,16.41011));
#322 = SURFACE_CURVE('',#323,(#328,#348),.PCURVE_S1.);
#323 = HYPERBOLA('',#324,3.185409919774,1.839097274661);
#324 = AXIS2_PLACEMENT_3D('',#325,#326,#327);
#325 = CARTESIAN_POINT('',(51.480658578425,113.30458603831,
    8.933796027919));
#326 = DIRECTION('',(0.998464000178,5.54043351018E-02,0.));
#327 = DIRECTION('',(-0.,0.,1.));
#328 = PCURVE('',#175,#329);
#329 = DEFINITIONAL_REPRESENTATION('',(#330),#347);
#330 = B_SPLINE_CURVE_WITH_KNOTS('',8,(#331,#332,#333,#334,#335,#336,
    #337,#338,#339,#340,#341,#342,#343,#344,#345,#346),.UNSPECIFIED.,.F.
  ,.F.,(9,7,9),(-1.497479038359,-0.587057234657,0.323364569045),
  .UNSPECIFIED.);
#331 = CARTESIAN_POINT('',(2.06636990209,7.));
#332 = CARTESIAN_POINT('',(2.114857476574,6.230265804533));
#333 = CARTESIAN_POINT('',(2.169049576633,5.571190045175));
#334 = CARTESIAN_POINT('',(2.229560553635,5.007582540303));
#335 = CARTESIAN_POINT('',(2.296972295985,4.527308552315));
#336 = CARTESIAN_POINT('',(2.371812497571,4.1206651082));
#337 = CARTESIAN_POINT('',(2.454355463967,3.779953348762));
#338 = CARTESIAN_POINT('',(2.544383322466,3.499195653458));
#339 = CARTESIAN_POINT('',(2.737707681559,3.048696922132));
#340 = CARTESIAN_POINT('',(2.84100532853,2.878955952626));
#341 = CARTESIAN_POINT('',(2.950090863487,2.760278096858));
#342 = CARTESIAN_POINT('',(3.063519320009,2.68975169661));
#343 = CARTESIAN_POINT('',(3.17912940054,2.665814550315));
#344 = CARTESIAN_POINT('',(3.294299751356,2.688175430226));
#345 = CARTESIAN_POINT('',(3.406796002298,2.757816888692));
#346 = CARTESIAN_POINT('',(3.51489750975,2.877092831658));
#347 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#348 = PCURVE('',#349,#354);
#349 = PLANE('',#350);
#350 = AXIS2_PLACEMENT_3D('',#351,#352,#353);
#351 = CARTESIAN_POINT('',(51.468253,113.528152,42.5));
#352 = DIRECTION('',(0.998464000178,5.54043351018E-02,0.));
#353 = DIRECTION('',(-5.54043351018E-02,0.998464000178,0.));
#354 = DEFINITIONAL_REPRESENTATION('',(#355),#359);
#355 = ( BOUNDED_CURVE() B_SPLINE_CURVE(2,(#356,#357,#358),
.UNSPECIFIED.,.F.,.F.) B_SPLINE_CURVE_WITH_KNOTS((3,3),(-1.497479038359,
0.323364569045),.PIECEWISE_BEZIER_KNOTS.) CURVE() 
GEOMETRIC_REPRESENTATION_ITEM() RATIONAL_B_SPLINE_CURVE((1.,
1.443862562245,1.)) REPRESENTATION_ITEM('') );
#356 = CARTESIAN_POINT('',(3.681148125654,-26.08989));
#357 = CARTESIAN_POINT('',(0.567541838462,-30.96882361335));
#358 = CARTESIAN_POINT('',(-0.829027185609,-30.21279716834));
#359 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#360 = ORIENTED_EDGE('',*,*,#361,.F.);
#361 = EDGE_CURVE('',#167,#320,#362,.T.);
#362 = SURFACE_CURVE('',#363,(#368,#375),.PCURVE_S1.);
#363 = CIRCLE('',#364,4.316451884327);
#364 = AXIS2_PLACEMENT_3D('',#365,#366,#367);
#365 = CARTESIAN_POINT('',(53.316931,113.40648,16.41011));
#366 = DIRECTION('',(0.,0.,1.));
#367 = DIRECTION('',(1.,0.,0.));
#368 = PCURVE('',#175,#369);
#369 = DEFINITIONAL_REPRESENTATION('',(#370),#374);
#370 = LINE('',#371,#372);
#371 = CARTESIAN_POINT('',(0.,7.));
#372 = VECTOR('',#373,1.);
#373 = DIRECTION('',(1.,0.));
#374 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#375 = PCURVE('',#211,#376);
#376 = DEFINITIONAL_REPRESENTATION('',(#377),#381);
#377 = CIRCLE('',#378,4.316451884327);
#378 = AXIS2_PLACEMENT_2D('',#379,#380);
#379 = CARTESIAN_POINT('',(0.,0.));
#380 = DIRECTION('',(1.,0.));
#381 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#382 = ORIENTED_EDGE('',*,*,#164,.F.);
#383 = ORIENTED_EDGE('',*,*,#384,.T.);
#384 = EDGE_CURVE('',#165,#165,#385,.T.);
#385 = SURFACE_CURVE('',#386,(#391,#398),.PCURVE_S1.);
#386 = CIRCLE('',#387,0.275);
#387 = AXIS2_PLACEMENT_3D('',#388,#389,#390);
#388 = CARTESIAN_POINT('',(53.316931,113.40648,9.41011));
#389 = DIRECTION('',(0.,0.,1.));
#390 = DIRECTION('',(1.,0.,0.));
#391 = PCURVE('',#175,#392);
#392 = DEFINITIONAL_REPRESENTATION('',(#393),#397);
#393 = LINE('',#394,#395);
#394 = CARTESIAN_POINT('',(0.,0.));
#395 = VECTOR('',#396,1.);
#396 = DIRECTION('',(1.,0.));
#397 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#398 = PCURVE('',#399,#404);
#399 = CYLINDRICAL_SURFACE('',#400,0.275);
#400 = AXIS2_PLACEMENT_3D('',#401,#402,#403);
#401 = CARTESIAN_POINT('',(53.316931,113.40648,9.41011));
#402 = DIRECTION('',(0.,0.,1.));
#403 = DIRECTION('',(1.,0.,0.));
#404 = DEFINITIONAL_REPRESENTATION('',(#405),#408);
#405 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#406,#407),.UNSPECIFIED.,.F.,.F.,
  (2,2),(0.,6.28318530718),.PIECEWISE_BEZIER_KNOTS.);
#406 = CARTESIAN_POINT('',(0.,0.));
#407 = CARTESIAN_POINT('',(6.28318530718,0.));
#408 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#409 = ADVANCED_FACE('',(#410),#399,.F.);
#410 = FACE_BOUND('',#411,.F.);
#411 = EDGE_LOOP('',(#412,#436,#457,#458));
#412 = ORIENTED_EDGE('',*,*,#413,.F.);
#413 = EDGE_CURVE('',#414,#414,#416,.T.);
#414 = VERTEX_POINT('',#415);
#415 = CARTESIAN_POINT('',(53.591931,113.40648,16.41011));
#416 = SURFACE_CURVE('',#417,(#422,#429),.PCURVE_S1.);
#417 = CIRCLE('',#418,0.275);
#418 = AXIS2_PLACEMENT_3D('',#419,#420,#421);
#419 = CARTESIAN_POINT('',(53.316931,113.40648,16.41011));
#420 = DIRECTION('',(0.,0.,1.));
#421 = DIRECTION('',(1.,0.,0.));
#422 = PCURVE('',#399,#423);
#423 = DEFINITIONAL_REPRESENTATION('',(#424),#428);
#424 = LINE('',#425,#426);
#425 = CARTESIAN_POINT('',(0.,7.));
#426 = VECTOR('',#427,1.);
#427 = DIRECTION('',(1.,0.));
#428 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#429 = PCURVE('',#211,#430);
#430 = DEFINITIONAL_REPRESENTATION('',(#431),#435);
#431 = CIRCLE('',#432,0.275);
#432 = AXIS2_PLACEMENT_2D('',#433,#434);
#433 = CARTESIAN_POINT('',(0.,0.));
#434 = DIRECTION('',(1.,0.));
#435 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#436 = ORIENTED_EDGE('',*,*,#437,.F.);
#437 = EDGE_CURVE('',#165,#414,#438,.T.);
#438 = SEAM_CURVE('',#439,(#443,#450),.PCURVE_S1.);
#439 = LINE('',#440,#441);
#440 = CARTESIAN_POINT('',(53.591931,113.40648,9.41011));
#441 = VECTOR('',#442,1.);
#442 = DIRECTION('',(0.,0.,1.));
#443 = PCURVE('',#399,#444);
#444 = DEFINITIONAL_REPRESENTATION('',(#445),#449);
#445 = LINE('',#446,#447);
#446 = CARTESIAN_POINT('',(6.28318530718,-0.));
#447 = VECTOR('',#448,1.);
#448 = DIRECTION('',(0.,1.));
#449 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#450 = PCURVE('',#399,#451);
#451 = DEFINITIONAL_REPRESENTATION('',(#452),#456);
#452 = LINE('',#453,#454);
#453 = CARTESIAN_POINT('',(0.,-0.));
#454 = VECTOR('',#455,1.);
#455 = DIRECTION('',(0.,1.));
#456 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#457 = ORIENTED_EDGE('',*,*,#384,.T.);
#458 = ORIENTED_EDGE('',*,*,#437,.T.);
#459 = ADVANCED_FACE('',(#460,#525),#211,.T.);
#460 = FACE_BOUND('',#461,.T.);
#461 = EDGE_LOOP('',(#462,#463,#464,#485,#506));
#462 = ORIENTED_EDGE('',*,*,#194,.T.);
#463 = ORIENTED_EDGE('',*,*,#361,.T.);
#464 = ORIENTED_EDGE('',*,*,#465,.F.);
#465 = EDGE_CURVE('',#466,#320,#468,.T.);
#466 = VERTEX_POINT('',#467);
#467 = CARTESIAN_POINT('',(51.468253,113.528152,16.41011));
#468 = SURFACE_CURVE('',#469,(#473,#479),.PCURVE_S1.);
#469 = LINE('',#470,#471);
#470 = CARTESIAN_POINT('',(51.474455789213,113.41636901915,16.41011));
#471 = VECTOR('',#472,1.);
#472 = DIRECTION('',(-5.54043351018E-02,0.998464000178,0.));
#473 = PCURVE('',#211,#474);
#474 = DEFINITIONAL_REPRESENTATION('',(#475),#478);
#475 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#476,#477),.UNSPECIFIED.,.F.,.F.,
  (2,2),(0.111954943618,4.31318792105),.PIECEWISE_BEZIER_KNOTS.);
#476 = CARTESIAN_POINT('',(-1.848678,0.121672));
#477 = CARTESIAN_POINT('',(-2.081444519722,4.316451884327));
#478 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#479 = PCURVE('',#349,#480);
#480 = DEFINITIONAL_REPRESENTATION('',(#481),#484);
#481 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#482,#483),.UNSPECIFIED.,.F.,.F.,
  (2,2),(0.111954943618,4.31318792105),.PIECEWISE_BEZIER_KNOTS.);
#482 = CARTESIAN_POINT('',(-1.418309913959E-14,-26.08989));
#483 = CARTESIAN_POINT('',(4.201232977432,-26.08989));
#484 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#485 = ORIENTED_EDGE('',*,*,#486,.F.);
#486 = EDGE_CURVE('',#487,#466,#489,.T.);
#487 = VERTEX_POINT('',#488);
#488 = CARTESIAN_POINT('',(51.710833,112.906445,16.41011));
#489 = SURFACE_CURVE('',#490,(#494,#500),.PCURVE_S1.);
#490 = LINE('',#491,#492);
#491 = CARTESIAN_POINT('',(51.732274763367,112.85149201798,16.41011));
#492 = VECTOR('',#493,1.);
#493 = DIRECTION('',(-0.363493894237,0.931596580528,0.));
#494 = PCURVE('',#211,#495);
#495 = DEFINITIONAL_REPRESENTATION('',(#496),#499);
#496 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#497,#498),.UNSPECIFIED.,.F.,.F.,
  (2,2),(5.89879602011E-02,0.726344424356),.PIECEWISE_BEZIER_KNOTS.);
#497 = CARTESIAN_POINT('',(-1.606098,-0.500035));
#498 = CARTESIAN_POINT('',(-1.848678,0.121672));
#499 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#500 = PCURVE('',#307,#501);
#501 = DEFINITIONAL_REPRESENTATION('',(#502),#505);
#502 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#503,#504),.UNSPECIFIED.,.F.,.F.,
  (2,2),(5.89879602011E-02,0.726344424356),.PIECEWISE_BEZIER_KNOTS.);
#503 = CARTESIAN_POINT('',(0.,-26.08989));
#504 = CARTESIAN_POINT('',(0.667356464155,-26.08989));
#505 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#506 = ORIENTED_EDGE('',*,*,#507,.F.);
#507 = EDGE_CURVE('',#195,#487,#508,.T.);
#508 = SURFACE_CURVE('',#509,(#513,#519),.PCURVE_S1.);
#509 = LINE('',#510,#511);
#510 = CARTESIAN_POINT('',(63.1172125,112.906445,16.41011));
#511 = VECTOR('',#512,1.);
#512 = DIRECTION('',(-1.,0.,0.));
#513 = PCURVE('',#211,#514);
#514 = DEFINITIONAL_REPRESENTATION('',(#515),#518);
#515 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#516,#517),.UNSPECIFIED.,.F.,.F.,
  (2,2),(5.483829615673,11.4063795),.PIECEWISE_BEZIER_KNOTS.);
#516 = CARTESIAN_POINT('',(4.316451884327,-0.500035));
#517 = CARTESIAN_POINT('',(-1.606098,-0.500035));
#518 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#519 = PCURVE('',#274,#520);
#520 = DEFINITIONAL_REPRESENTATION('',(#521),#524);
#521 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#522,#523),.UNSPECIFIED.,.F.,.F.,
  (2,2),(5.483829615673,11.4063795),.PIECEWISE_BEZIER_KNOTS.);
#522 = CARTESIAN_POINT('',(15.284111115673,-26.08989));
#523 = CARTESIAN_POINT('',(21.206661,-26.08989));
#524 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#525 = FACE_BOUND('',#526,.T.);
#526 = EDGE_LOOP('',(#527));
#527 = ORIENTED_EDGE('',*,*,#413,.F.);
#528 = ADVANCED_FACE('',(#529),#349,.F.);
#529 = FACE_BOUND('',#530,.F.);
#530 = EDGE_LOOP('',(#531,#532,#553));
#531 = ORIENTED_EDGE('',*,*,#465,.F.);
#532 = ORIENTED_EDGE('',*,*,#533,.T.);
#533 = EDGE_CURVE('',#466,#287,#534,.T.);
#534 = SURFACE_CURVE('',#535,(#539,#546),.PCURVE_S1.);
#535 = LINE('',#536,#537);
#536 = CARTESIAN_POINT('',(51.468253,113.528152,42.5));
#537 = VECTOR('',#538,1.);
#538 = DIRECTION('',(0.,0.,-1.));
#539 = PCURVE('',#349,#540);
#540 = DEFINITIONAL_REPRESENTATION('',(#541),#545);
#541 = LINE('',#542,#543);
#542 = CARTESIAN_POINT('',(0.,0.));
#543 = VECTOR('',#544,1.);
#544 = DIRECTION('',(0.,-1.));
#545 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#546 = PCURVE('',#307,#547);
#547 = DEFINITIONAL_REPRESENTATION('',(#548),#552);
#548 = LINE('',#549,#550);
#549 = CARTESIAN_POINT('',(0.667356464155,0.));
#550 = VECTOR('',#551,1.);
#551 = DIRECTION('',(0.,-1.));
#552 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#553 = ORIENTED_EDGE('',*,*,#319,.F.);
#554 = ADVANCED_FACE('',(#555),#307,.F.);
#555 = FACE_BOUND('',#556,.F.);
#556 = EDGE_LOOP('',(#557,#558,#579,#580));
#557 = ORIENTED_EDGE('',*,*,#486,.F.);
#558 = ORIENTED_EDGE('',*,*,#559,.T.);
#559 = EDGE_CURVE('',#487,#224,#560,.T.);
#560 = SURFACE_CURVE('',#561,(#565,#572),.PCURVE_S1.);
#561 = LINE('',#562,#563);
#562 = CARTESIAN_POINT('',(51.710833,112.906445,42.5));
#563 = VECTOR('',#564,1.);
#564 = DIRECTION('',(0.,0.,-1.));
#565 = PCURVE('',#307,#566);
#566 = DEFINITIONAL_REPRESENTATION('',(#567),#571);
#567 = LINE('',#568,#569);
#568 = CARTESIAN_POINT('',(0.,0.));
#569 = VECTOR('',#570,1.);
#570 = DIRECTION('',(0.,-1.));
#571 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#572 = PCURVE('',#274,#573);
#573 = DEFINITIONAL_REPRESENTATION('',(#574),#578);
#574 = LINE('',#575,#576);
#575 = CARTESIAN_POINT('',(21.206661,0.));
#576 = VECTOR('',#577,1.);
#577 = DIRECTION('',(0.,-1.));
#578 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#579 = ORIENTED_EDGE('',*,*,#286,.F.);
#580 = ORIENTED_EDGE('',*,*,#533,.F.);
#581 = ADVANCED_FACE('',(#582),#274,.F.);
#582 = FACE_BOUND('',#583,.F.);
#583 = EDGE_LOOP('',(#584,#585,#586));
#584 = ORIENTED_EDGE('',*,*,#559,.F.);
#585 = ORIENTED_EDGE('',*,*,#507,.F.);
#586 = ORIENTED_EDGE('',*,*,#223,.F.);
#587 = ( GEOMETRIC_REPRESENTATION_CONTEXT(3) 
GLOBAL_UNCERTAINTY_ASSIGNED_CONTEXT((#591)) GLOBAL_UNIT_ASSIGNED_CONTEXT
((#588,#589,#590)) REPRESENTATION_CONTEXT('Context #1',
  '3D Context with UNIT and UNCERTAINTY') );
#588 = ( LENGTH_UNIT() NAMED_UNIT(*) SI_UNIT(.MILLI.,.METRE.) );
#589 = ( NAMED_UNIT(*) PLANE_ANGLE_UNIT() SI_UNIT($,.RADIAN.) );
#590 = ( NAMED_UNIT(*) SI_UNIT($,.STERADIAN.) SOLID_ANGLE_UNIT() );
#591 = UNCERTAINTY_MEASURE_WITH_UNIT(LENGTH_MEASURE(1.E-07),#588,
  'distance_accuracy_value','confusion accuracy');
#592 = CONTEXT_DEPENDENT_SHAPE_REPRESENTATION(#593,#595);
#593 = ( REPRESENTATION_RELATIONSHIP('','',#157,#10) 
REPRESENTATION_RELATIONSHIP_WITH_TRANSFORMATION(#594) 
SHAPE_REPRESENTATION_RELATIONSHIP() );
#594 = ITEM_DEFINED_TRANSFORMATION('','',#11,#19);
#595 = PRODUCT_DEFINITION_SHAPE('Placement','Placement of an item',#596
  );
#596 = NEXT_ASSEMBLY_USAGE_OCCURRENCE('121','=>[0:1:1:3]','',#5,#152,$);
#597 = PRODUCT_RELATED_PRODUCT_CATEGORY('part',$,(#154));
#598 = SHAPE_DEFINITION_REPRESENTATION(#599,#605);
#599 = PRODUCT_DEFINITION_SHAPE('','',#600);
#600 = PRODUCT_DEFINITION('design','',#601,#604);
#601 = PRODUCT_DEFINITION_FORMATION('','',#602);
#602 = PRODUCT('PC2-P6_Part3','PC2-P6_Part3','',(#603));
#603 = PRODUCT_CONTEXT('',#2,'mechanical');
#604 = PRODUCT_DEFINITION_CONTEXT('part definition',#2,'design');
#605 = ADVANCED_BREP_SHAPE_REPRESENTATION('',(#11,#606),#1010);
#606 = MANIFOLD_SOLID_BREP('',#607);
#607 = CLOSED_SHELL('',(#608,#730,#831,#878,#949,#976,#983));
#608 = ADVANCED_FACE('',(#609),#624,.T.);
#609 = FACE_BOUND('',#610,.T.);
#610 = EDGE_LOOP('',(#611,#647,#673,#706));
#611 = ORIENTED_EDGE('',*,*,#612,.T.);
#612 = EDGE_CURVE('',#613,#615,#617,.T.);
#613 = VERTEX_POINT('',#614);
#614 = CARTESIAN_POINT('',(57.604322032841,112.906445,16.41011));
#615 = VERTEX_POINT('',#616);
#616 = CARTESIAN_POINT('',(51.264301435687,117.20364588279,16.41011));
#617 = SURFACE_CURVE('',#618,(#623,#635),.PCURVE_S1.);
#618 = CIRCLE('',#619,4.316451884327);
#619 = AXIS2_PLACEMENT_3D('',#620,#621,#622);
#620 = CARTESIAN_POINT('',(53.316931,113.40648,16.41011));
#621 = DIRECTION('',(-0.,0.,1.));
#622 = DIRECTION('',(0.993267421423,-0.115843987933,0.));
#623 = PCURVE('',#624,#629);
#624 = CYLINDRICAL_SURFACE('',#625,4.316451884327);
#625 = AXIS2_PLACEMENT_3D('',#626,#627,#628);
#626 = CARTESIAN_POINT('',(53.316931,113.40648,16.41011));
#627 = DIRECTION('',(0.,0.,1.));
#628 = DIRECTION('',(1.,0.,0.));
#629 = DEFINITIONAL_REPRESENTATION('',(#630),#634);
#630 = LINE('',#631,#632);
#631 = CARTESIAN_POINT('',(6.167080640846,0.));
#632 = VECTOR('',#633,1.);
#633 = DIRECTION('',(1.,0.));
#634 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#635 = PCURVE('',#636,#641);
#636 = PLANE('',#637);
#637 = AXIS2_PLACEMENT_3D('',#638,#639,#640);
#638 = CARTESIAN_POINT('',(53.316931,113.40648,16.41011));
#639 = DIRECTION('',(0.,0.,1.));
#640 = DIRECTION('',(1.,0.,0.));
#641 = DEFINITIONAL_REPRESENTATION('',(#642),#646);
#642 = CIRCLE('',#643,4.316451884327);
#643 = AXIS2_PLACEMENT_2D('',#644,#645);
#644 = CARTESIAN_POINT('',(0.,0.));
#645 = DIRECTION('',(0.993267421423,-0.115843987933));
#646 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#647 = ORIENTED_EDGE('',*,*,#648,.T.);
#648 = EDGE_CURVE('',#615,#649,#651,.T.);
#649 = VERTEX_POINT('',#650);
#650 = CARTESIAN_POINT('',(51.264301435687,117.20364588279,32.5));
#651 = SURFACE_CURVE('',#652,(#656,#662),.PCURVE_S1.);
#652 = LINE('',#653,#654);
#653 = CARTESIAN_POINT('',(51.264301435687,117.20364588279,16.41011));
#654 = VECTOR('',#655,1.);
#655 = DIRECTION('',(0.,0.,1.));
#656 = PCURVE('',#624,#657);
#657 = DEFINITIONAL_REPRESENTATION('',(#658),#661);
#658 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#659,#660),.UNSPECIFIED.,.F.,.F.,
  (2,2),(0.,16.08989),.PIECEWISE_BEZIER_KNOTS.);
#659 = CARTESIAN_POINT('',(8.34955520927,0.));
#660 = CARTESIAN_POINT('',(8.34955520927,16.08989));
#661 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#662 = PCURVE('',#663,#668);
#663 = PLANE('',#664);
#664 = AXIS2_PLACEMENT_3D('',#665,#666,#667);
#665 = CARTESIAN_POINT('',(51.468253,113.528152,42.5));
#666 = DIRECTION('',(0.998464000178,5.54043351018E-02,0.));
#667 = DIRECTION('',(-5.54043351018E-02,0.998464000178,0.));
#668 = DEFINITIONAL_REPRESENTATION('',(#669),#672);
#669 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#670,#671),.UNSPECIFIED.,.F.,.F.,
  (2,2),(-5.999999999062E-07,16.0898906),.PIECEWISE_BEZIER_KNOTS.);
#670 = CARTESIAN_POINT('',(3.681148125654,-26.0898906));
#671 = CARTESIAN_POINT('',(3.681148125654,-9.9999994));
#672 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#673 = ORIENTED_EDGE('',*,*,#674,.T.);
#674 = EDGE_CURVE('',#649,#675,#677,.T.);
#675 = VERTEX_POINT('',#676);
#676 = CARTESIAN_POINT('',(57.604322032841,112.906445,32.5));
#677 = SURFACE_CURVE('',#678,(#683,#690),.PCURVE_S1.);
#678 = CIRCLE('',#679,4.316451884327);
#679 = AXIS2_PLACEMENT_3D('',#680,#681,#682);
#680 = CARTESIAN_POINT('',(53.316931,113.40648,32.5));
#681 = DIRECTION('',(0.,0.,-1.));
#682 = DIRECTION('',(-0.475536301416,0.879696098682,0.));
#683 = PCURVE('',#624,#684);
#684 = DEFINITIONAL_REPRESENTATION('',(#685),#689);
#685 = LINE('',#686,#687);
#686 = CARTESIAN_POINT('',(8.34955520927,16.08989));
#687 = VECTOR('',#688,1.);
#688 = DIRECTION('',(-1.,-0.));
#689 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#690 = PCURVE('',#691,#696);
#691 = PLANE('',#692);
#692 = AXIS2_PLACEMENT_3D('',#693,#694,#695);
#693 = CARTESIAN_POINT('',(53.316931,113.40648,32.5));
#694 = DIRECTION('',(0.,0.,1.));
#695 = DIRECTION('',(1.,0.,0.));
#696 = DEFINITIONAL_REPRESENTATION('',(#697),#705);
#697 = ( BOUNDED_CURVE() B_SPLINE_CURVE(2,(#698,#699,#700,#701,#702,#703
,#704),.UNSPECIFIED.,.T.,.F.) B_SPLINE_CURVE_WITH_KNOTS((1,2,2,2,2,1),(
    -2.094395102393,0.,2.094395102393,4.188790204786,6.28318530718,
8.377580409573),.UNSPECIFIED.) CURVE() GEOMETRIC_REPRESENTATION_ITEM() 
RATIONAL_B_SPLINE_CURVE((1.,0.5,1.,0.5,1.,0.5,1.)) REPRESENTATION_ITEM(
  '') );
#698 = CARTESIAN_POINT('',(-2.052629564313,3.79716588279));
#699 = CARTESIAN_POINT('',(4.524254669446,7.352424577298));
#700 = CARTESIAN_POINT('',(4.314756899036,-0.120953594141));
#701 = CARTESIAN_POINT('',(4.105259128626,-7.594331765579));
#702 = CARTESIAN_POINT('',(-2.262127334723,-3.676212288649));
#703 = CARTESIAN_POINT('',(-8.629513798072,0.241907188281));
#704 = CARTESIAN_POINT('',(-2.052629564313,3.79716588279));
#705 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#706 = ORIENTED_EDGE('',*,*,#707,.F.);
#707 = EDGE_CURVE('',#613,#675,#708,.T.);
#708 = SURFACE_CURVE('',#709,(#713,#719),.PCURVE_S1.);
#709 = LINE('',#710,#711);
#710 = CARTESIAN_POINT('',(57.604322032841,112.906445,16.41011));
#711 = VECTOR('',#712,1.);
#712 = DIRECTION('',(0.,0.,1.));
#713 = PCURVE('',#624,#714);
#714 = DEFINITIONAL_REPRESENTATION('',(#715),#718);
#715 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#716,#717),.UNSPECIFIED.,.F.,.F.,
  (2,2),(0.,16.08989),.PIECEWISE_BEZIER_KNOTS.);
#716 = CARTESIAN_POINT('',(6.167080640846,0.));
#717 = CARTESIAN_POINT('',(6.167080640846,16.08989));
#718 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#719 = PCURVE('',#720,#725);
#720 = PLANE('',#721);
#721 = AXIS2_PLACEMENT_3D('',#722,#723,#724);
#722 = CARTESIAN_POINT('',(72.917494,112.906445,42.5));
#723 = DIRECTION('',(0.,1.,0.));
#724 = DIRECTION('',(-1.,0.,0.));
#725 = DEFINITIONAL_REPRESENTATION('',(#726),#729);
#726 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#727,#728),.UNSPECIFIED.,.F.,.F.,
  (2,2),(-5.999999999062E-07,16.0898906),.PIECEWISE_BEZIER_KNOTS.);
#727 = CARTESIAN_POINT('',(15.313171967159,-26.0898906));
#728 = CARTESIAN_POINT('',(15.313171967159,-9.9999994));
#729 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#730 = ADVANCED_FACE('',(#731,#800),#636,.F.);
#731 = FACE_BOUND('',#732,.F.);
#732 = EDGE_LOOP('',(#733,#734,#755,#781));
#733 = ORIENTED_EDGE('',*,*,#612,.T.);
#734 = ORIENTED_EDGE('',*,*,#735,.F.);
#735 = EDGE_CURVE('',#736,#615,#738,.T.);
#736 = VERTEX_POINT('',#737);
#737 = CARTESIAN_POINT('',(51.468253,113.528152,16.41011));
#738 = SURFACE_CURVE('',#739,(#743,#749),.PCURVE_S1.);
#739 = LINE('',#740,#741);
#740 = CARTESIAN_POINT('',(51.474455789213,113.41636901915,16.41011));
#741 = VECTOR('',#742,1.);
#742 = DIRECTION('',(-5.54043351018E-02,0.998464000178,0.));
#743 = PCURVE('',#636,#744);
#744 = DEFINITIONAL_REPRESENTATION('',(#745),#748);
#745 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#746,#747),.UNSPECIFIED.,.F.,.F.,
  (2,2),(0.111954943618,4.31318792105),.PIECEWISE_BEZIER_KNOTS.);
#746 = CARTESIAN_POINT('',(-1.848678,0.121672));
#747 = CARTESIAN_POINT('',(-2.081444519722,4.316451884327));
#748 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#749 = PCURVE('',#663,#750);
#750 = DEFINITIONAL_REPRESENTATION('',(#751),#754);
#751 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#752,#753),.UNSPECIFIED.,.F.,.F.,
  (2,2),(0.111954943618,4.31318792105),.PIECEWISE_BEZIER_KNOTS.);
#752 = CARTESIAN_POINT('',(-1.418309913959E-14,-26.08989));
#753 = CARTESIAN_POINT('',(4.201232977432,-26.08989));
#754 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#755 = ORIENTED_EDGE('',*,*,#756,.F.);
#756 = EDGE_CURVE('',#757,#736,#759,.T.);
#757 = VERTEX_POINT('',#758);
#758 = CARTESIAN_POINT('',(51.710833,112.906445,16.41011));
#759 = SURFACE_CURVE('',#760,(#764,#770),.PCURVE_S1.);
#760 = LINE('',#761,#762);
#761 = CARTESIAN_POINT('',(51.732274763367,112.85149201798,16.41011));
#762 = VECTOR('',#763,1.);
#763 = DIRECTION('',(-0.363493894237,0.931596580528,0.));
#764 = PCURVE('',#636,#765);
#765 = DEFINITIONAL_REPRESENTATION('',(#766),#769);
#766 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#767,#768),.UNSPECIFIED.,.F.,.F.,
  (2,2),(5.89879602011E-02,0.726344424356),.PIECEWISE_BEZIER_KNOTS.);
#767 = CARTESIAN_POINT('',(-1.606098,-0.500035));
#768 = CARTESIAN_POINT('',(-1.848678,0.121672));
#769 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#770 = PCURVE('',#771,#776);
#771 = PLANE('',#772);
#772 = AXIS2_PLACEMENT_3D('',#773,#774,#775);
#773 = CARTESIAN_POINT('',(51.710833,112.906445,42.5));
#774 = DIRECTION('',(0.931596580528,0.363493894237,0.));
#775 = DIRECTION('',(-0.363493894237,0.931596580528,0.));
#776 = DEFINITIONAL_REPRESENTATION('',(#777),#780);
#777 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#778,#779),.UNSPECIFIED.,.F.,.F.,
  (2,2),(5.89879602011E-02,0.726344424356),.PIECEWISE_BEZIER_KNOTS.);
#778 = CARTESIAN_POINT('',(0.,-26.08989));
#779 = CARTESIAN_POINT('',(0.667356464155,-26.08989));
#780 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#781 = ORIENTED_EDGE('',*,*,#782,.F.);
#782 = EDGE_CURVE('',#613,#757,#783,.T.);
#783 = SURFACE_CURVE('',#784,(#788,#794),.PCURVE_S1.);
#784 = LINE('',#785,#786);
#785 = CARTESIAN_POINT('',(63.1172125,112.906445,16.41011));
#786 = VECTOR('',#787,1.);
#787 = DIRECTION('',(-1.,0.,0.));
#788 = PCURVE('',#636,#789);
#789 = DEFINITIONAL_REPRESENTATION('',(#790),#793);
#790 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#791,#792),.UNSPECIFIED.,.F.,.F.,
  (2,2),(5.483829615673,11.4063795),.PIECEWISE_BEZIER_KNOTS.);
#791 = CARTESIAN_POINT('',(4.316451884327,-0.500035));
#792 = CARTESIAN_POINT('',(-1.606098,-0.500035));
#793 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#794 = PCURVE('',#720,#795);
#795 = DEFINITIONAL_REPRESENTATION('',(#796),#799);
#796 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#797,#798),.UNSPECIFIED.,.F.,.F.,
  (2,2),(5.483829615673,11.4063795),.PIECEWISE_BEZIER_KNOTS.);
#797 = CARTESIAN_POINT('',(15.284111115673,-26.08989));
#798 = CARTESIAN_POINT('',(21.206661,-26.08989));
#799 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#800 = FACE_BOUND('',#801,.F.);
#801 = EDGE_LOOP('',(#802));
#802 = ORIENTED_EDGE('',*,*,#803,.F.);
#803 = EDGE_CURVE('',#804,#804,#806,.T.);
#804 = VERTEX_POINT('',#805);
#805 = CARTESIAN_POINT('',(53.591931,113.40648,16.41011));
#806 = SURFACE_CURVE('',#807,(#812,#819),.PCURVE_S1.);
#807 = CIRCLE('',#808,0.275);
#808 = AXIS2_PLACEMENT_3D('',#809,#810,#811);
#809 = CARTESIAN_POINT('',(53.316931,113.40648,16.41011));
#810 = DIRECTION('',(0.,0.,1.));
#811 = DIRECTION('',(1.,0.,0.));
#812 = PCURVE('',#636,#813);
#813 = DEFINITIONAL_REPRESENTATION('',(#814),#818);
#814 = CIRCLE('',#815,0.275);
#815 = AXIS2_PLACEMENT_2D('',#816,#817);
#816 = CARTESIAN_POINT('',(0.,0.));
#817 = DIRECTION('',(1.,0.));
#818 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#819 = PCURVE('',#820,#825);
#820 = CYLINDRICAL_SURFACE('',#821,0.275);
#821 = AXIS2_PLACEMENT_3D('',#822,#823,#824);
#822 = CARTESIAN_POINT('',(53.316931,113.40648,16.41011));
#823 = DIRECTION('',(0.,0.,1.));
#824 = DIRECTION('',(1.,0.,0.));
#825 = DEFINITIONAL_REPRESENTATION('',(#826),#830);
#826 = LINE('',#827,#828);
#827 = CARTESIAN_POINT('',(0.,0.));
#828 = VECTOR('',#829,1.);
#829 = DIRECTION('',(1.,0.));
#830 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#831 = ADVANCED_FACE('',(#832),#720,.F.);
#832 = FACE_BOUND('',#833,.F.);
#833 = EDGE_LOOP('',(#834,#857,#876,#877));
#834 = ORIENTED_EDGE('',*,*,#835,.F.);
#835 = EDGE_CURVE('',#836,#757,#838,.T.);
#836 = VERTEX_POINT('',#837);
#837 = CARTESIAN_POINT('',(51.710833,112.906445,32.5));
#838 = SURFACE_CURVE('',#839,(#843,#850),.PCURVE_S1.);
#839 = LINE('',#840,#841);
#840 = CARTESIAN_POINT('',(51.710833,112.906445,42.5));
#841 = VECTOR('',#842,1.);
#842 = DIRECTION('',(0.,0.,-1.));
#843 = PCURVE('',#720,#844);
#844 = DEFINITIONAL_REPRESENTATION('',(#845),#849);
#845 = LINE('',#846,#847);
#846 = CARTESIAN_POINT('',(21.206661,0.));
#847 = VECTOR('',#848,1.);
#848 = DIRECTION('',(0.,-1.));
#849 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#850 = PCURVE('',#771,#851);
#851 = DEFINITIONAL_REPRESENTATION('',(#852),#856);
#852 = LINE('',#853,#854);
#853 = CARTESIAN_POINT('',(0.,0.));
#854 = VECTOR('',#855,1.);
#855 = DIRECTION('',(0.,-1.));
#856 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#857 = ORIENTED_EDGE('',*,*,#858,.F.);
#858 = EDGE_CURVE('',#675,#836,#859,.T.);
#859 = SURFACE_CURVE('',#860,(#864,#870),.PCURVE_S1.);
#860 = LINE('',#861,#862);
#861 = CARTESIAN_POINT('',(63.1172125,112.906445,32.5));
#862 = VECTOR('',#863,1.);
#863 = DIRECTION('',(-1.,0.,0.));
#864 = PCURVE('',#720,#865);
#865 = DEFINITIONAL_REPRESENTATION('',(#866),#869);
#866 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#867,#868),.UNSPECIFIED.,.F.,.F.,
  (2,2),(5.483829615673,11.4063795),.PIECEWISE_BEZIER_KNOTS.);
#867 = CARTESIAN_POINT('',(15.284111115673,-10.));
#868 = CARTESIAN_POINT('',(21.206661,-10.));
#869 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#870 = PCURVE('',#691,#871);
#871 = DEFINITIONAL_REPRESENTATION('',(#872),#875);
#872 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#873,#874),.UNSPECIFIED.,.F.,.F.,
  (2,2),(5.483829615673,11.4063795),.PIECEWISE_BEZIER_KNOTS.);
#873 = CARTESIAN_POINT('',(4.316451884327,-0.500035));
#874 = CARTESIAN_POINT('',(-1.606098,-0.500035));
#875 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#876 = ORIENTED_EDGE('',*,*,#707,.F.);
#877 = ORIENTED_EDGE('',*,*,#782,.T.);
#878 = ADVANCED_FACE('',(#879,#923),#691,.T.);
#879 = FACE_BOUND('',#880,.T.);
#880 = EDGE_LOOP('',(#881,#882,#883,#904));
#881 = ORIENTED_EDGE('',*,*,#858,.F.);
#882 = ORIENTED_EDGE('',*,*,#674,.F.);
#883 = ORIENTED_EDGE('',*,*,#884,.F.);
#884 = EDGE_CURVE('',#885,#649,#887,.T.);
#885 = VERTEX_POINT('',#886);
#886 = CARTESIAN_POINT('',(51.468253,113.528152,32.5));
#887 = SURFACE_CURVE('',#888,(#892,#898),.PCURVE_S1.);
#888 = LINE('',#889,#890);
#889 = CARTESIAN_POINT('',(51.474455789213,113.41636901915,32.5));
#890 = VECTOR('',#891,1.);
#891 = DIRECTION('',(-5.54043351018E-02,0.998464000178,0.));
#892 = PCURVE('',#691,#893);
#893 = DEFINITIONAL_REPRESENTATION('',(#894),#897);
#894 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#895,#896),.UNSPECIFIED.,.F.,.F.,
  (2,2),(0.111954943618,4.31318792105),.PIECEWISE_BEZIER_KNOTS.);
#895 = CARTESIAN_POINT('',(-1.848678,0.121672));
#896 = CARTESIAN_POINT('',(-2.081444519722,4.316451884327));
#897 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#898 = PCURVE('',#663,#899);
#899 = DEFINITIONAL_REPRESENTATION('',(#900),#903);
#900 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#901,#902),.UNSPECIFIED.,.F.,.F.,
  (2,2),(0.111954943618,4.31318792105),.PIECEWISE_BEZIER_KNOTS.);
#901 = CARTESIAN_POINT('',(-1.418309913959E-14,-10.));
#902 = CARTESIAN_POINT('',(4.201232977432,-10.));
#903 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#904 = ORIENTED_EDGE('',*,*,#905,.F.);
#905 = EDGE_CURVE('',#836,#885,#906,.T.);
#906 = SURFACE_CURVE('',#907,(#911,#917),.PCURVE_S1.);
#907 = LINE('',#908,#909);
#908 = CARTESIAN_POINT('',(51.732274763367,112.85149201798,32.5));
#909 = VECTOR('',#910,1.);
#910 = DIRECTION('',(-0.363493894237,0.931596580528,0.));
#911 = PCURVE('',#691,#912);
#912 = DEFINITIONAL_REPRESENTATION('',(#913),#916);
#913 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#914,#915),.UNSPECIFIED.,.F.,.F.,
  (2,2),(5.89879602011E-02,0.726344424356),.PIECEWISE_BEZIER_KNOTS.);
#914 = CARTESIAN_POINT('',(-1.606098,-0.500035));
#915 = CARTESIAN_POINT('',(-1.848678,0.121672));
#916 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#917 = PCURVE('',#771,#918);
#918 = DEFINITIONAL_REPRESENTATION('',(#919),#922);
#919 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#920,#921),.UNSPECIFIED.,.F.,.F.,
  (2,2),(5.89879602011E-02,0.726344424356),.PIECEWISE_BEZIER_KNOTS.);
#920 = CARTESIAN_POINT('',(0.,-10.));
#921 = CARTESIAN_POINT('',(0.667356464155,-10.));
#922 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#923 = FACE_BOUND('',#924,.T.);
#924 = EDGE_LOOP('',(#925));
#925 = ORIENTED_EDGE('',*,*,#926,.F.);
#926 = EDGE_CURVE('',#927,#927,#929,.T.);
#927 = VERTEX_POINT('',#928);
#928 = CARTESIAN_POINT('',(53.591931,113.40648,32.5));
#929 = SURFACE_CURVE('',#930,(#935,#942),.PCURVE_S1.);
#930 = CIRCLE('',#931,0.275);
#931 = AXIS2_PLACEMENT_3D('',#932,#933,#934);
#932 = CARTESIAN_POINT('',(53.316931,113.40648,32.5));
#933 = DIRECTION('',(0.,0.,1.));
#934 = DIRECTION('',(1.,0.,0.));
#935 = PCURVE('',#691,#936);
#936 = DEFINITIONAL_REPRESENTATION('',(#937),#941);
#937 = CIRCLE('',#938,0.275);
#938 = AXIS2_PLACEMENT_2D('',#939,#940);
#939 = CARTESIAN_POINT('',(0.,0.));
#940 = DIRECTION('',(1.,0.));
#941 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#942 = PCURVE('',#820,#943);
#943 = DEFINITIONAL_REPRESENTATION('',(#944),#948);
#944 = LINE('',#945,#946);
#945 = CARTESIAN_POINT('',(0.,16.08989));
#946 = VECTOR('',#947,1.);
#947 = DIRECTION('',(1.,0.));
#948 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#949 = ADVANCED_FACE('',(#950),#663,.F.);
#950 = FACE_BOUND('',#951,.F.);
#951 = EDGE_LOOP('',(#952,#953,#974,#975));
#952 = ORIENTED_EDGE('',*,*,#884,.F.);
#953 = ORIENTED_EDGE('',*,*,#954,.T.);
#954 = EDGE_CURVE('',#885,#736,#955,.T.);
#955 = SURFACE_CURVE('',#956,(#960,#967),.PCURVE_S1.);
#956 = LINE('',#957,#958);
#957 = CARTESIAN_POINT('',(51.468253,113.528152,42.5));
#958 = VECTOR('',#959,1.);
#959 = DIRECTION('',(0.,0.,-1.));
#960 = PCURVE('',#663,#961);
#961 = DEFINITIONAL_REPRESENTATION('',(#962),#966);
#962 = LINE('',#963,#964);
#963 = CARTESIAN_POINT('',(0.,0.));
#964 = VECTOR('',#965,1.);
#965 = DIRECTION('',(0.,-1.));
#966 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#967 = PCURVE('',#771,#968);
#968 = DEFINITIONAL_REPRESENTATION('',(#969),#973);
#969 = LINE('',#970,#971);
#970 = CARTESIAN_POINT('',(0.667356464155,0.));
#971 = VECTOR('',#972,1.);
#972 = DIRECTION('',(0.,-1.));
#973 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#974 = ORIENTED_EDGE('',*,*,#735,.T.);
#975 = ORIENTED_EDGE('',*,*,#648,.T.);
#976 = ADVANCED_FACE('',(#977),#771,.F.);
#977 = FACE_BOUND('',#978,.F.);
#978 = EDGE_LOOP('',(#979,#980,#981,#982));
#979 = ORIENTED_EDGE('',*,*,#905,.F.);
#980 = ORIENTED_EDGE('',*,*,#835,.T.);
#981 = ORIENTED_EDGE('',*,*,#756,.T.);
#982 = ORIENTED_EDGE('',*,*,#954,.F.);
#983 = ADVANCED_FACE('',(#984),#820,.F.);
#984 = FACE_BOUND('',#985,.F.);
#985 = EDGE_LOOP('',(#986,#987,#1008,#1009));
#986 = ORIENTED_EDGE('',*,*,#926,.F.);
#987 = ORIENTED_EDGE('',*,*,#988,.F.);
#988 = EDGE_CURVE('',#804,#927,#989,.T.);
#989 = SEAM_CURVE('',#990,(#994,#1001),.PCURVE_S1.);
#990 = LINE('',#991,#992);
#991 = CARTESIAN_POINT('',(53.591931,113.40648,16.41011));
#992 = VECTOR('',#993,1.);
#993 = DIRECTION('',(0.,0.,1.));
#994 = PCURVE('',#820,#995);
#995 = DEFINITIONAL_REPRESENTATION('',(#996),#1000);
#996 = LINE('',#997,#998);
#997 = CARTESIAN_POINT('',(6.28318530718,-0.));
#998 = VECTOR('',#999,1.);
#999 = DIRECTION('',(0.,1.));
#1000 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1001 = PCURVE('',#820,#1002);
#1002 = DEFINITIONAL_REPRESENTATION('',(#1003),#1007);
#1003 = LINE('',#1004,#1005);
#1004 = CARTESIAN_POINT('',(0.,-0.));
#1005 = VECTOR('',#1006,1.);
#1006 = DIRECTION('',(0.,1.));
#1007 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1008 = ORIENTED_EDGE('',*,*,#803,.T.);
#1009 = ORIENTED_EDGE('',*,*,#988,.T.);
#1010 = ( GEOMETRIC_REPRESENTATION_CONTEXT(3) 
GLOBAL_UNCERTAINTY_ASSIGNED_CONTEXT((#1014)) 
GLOBAL_UNIT_ASSIGNED_CONTEXT((#1011,#1012,#1013)) REPRESENTATION_CONTEXT
('Context #1','3D Context with UNIT and UNCERTAINTY') );
#1011 = ( LENGTH_UNIT() NAMED_UNIT(*) SI_UNIT(.MILLI.,.METRE.) );
#1012 = ( NAMED_UNIT(*) PLANE_ANGLE_UNIT() SI_UNIT($,.RADIAN.) );
#1013 = ( NAMED_UNIT(*) SI_UNIT($,.STERADIAN.) SOLID_ANGLE_UNIT() );
#1014 = UNCERTAINTY_MEASURE_WITH_UNIT(LENGTH_MEASURE(1.E-07),#1011,
  'distance_accuracy_value','confusion accuracy');
#1015 = CONTEXT_DEPENDENT_SHAPE_REPRESENTATION(#1016,#1018);
#1016 = ( REPRESENTATION_RELATIONSHIP('','',#605,#10) 
REPRESENTATION_RELATIONSHIP_WITH_TRANSFORMATION(#1017) 
SHAPE_REPRESENTATION_RELATIONSHIP() );
#1017 = ITEM_DEFINED_TRANSFORMATION('','',#11,#23);
#1018 = PRODUCT_DEFINITION_SHAPE('Placement','Placement of an item',
  #1019);
#1019 = NEXT_ASSEMBLY_USAGE_OCCURRENCE('122','=>[0:1:1:4]','',#5,#600,$
  );
#1020 = PRODUCT_RELATED_PRODUCT_CATEGORY('part',$,(#602));
#1021 = MECHANICAL_DESIGN_GEOMETRIC_PRESENTATION_REPRESENTATION('',(
    #1022),#587);
#1022 = STYLED_ITEM('color',(#1023),#158);
#1023 = PRESENTATION_STYLE_ASSIGNMENT((#1024));
#1024 = SURFACE_STYLE_USAGE(.BOTH.,#1025);
#1025 = SURFACE_SIDE_STYLE('',(#1026));
#1026 = SURFACE_STYLE_FILL_AREA(#1027);
#1027 = FILL_AREA_STYLE('',(#1028));
#1028 = FILL_AREA_STYLE_COLOUR('',#1029);
#1029 = DRAUGHTING_PRE_DEFINED_COLOUR('green');
#1030 = MECHANICAL_DESIGN_GEOMETRIC_PRESENTATION_REPRESENTATION('',(
    #1031),#139);
#1031 = STYLED_ITEM('color',(#1032),#41);
#1032 = PRESENTATION_STYLE_ASSIGNMENT((#1033));
#1033 = SURFACE_STYLE_USAGE(.BOTH.,#1034);
#1034 = SURFACE_SIDE_STYLE('',(#1035));
#1035 = SURFACE_STYLE_FILL_AREA(#1036);
#1036 = FILL_AREA_STYLE('',(#1037));
#1037 = FILL_AREA_STYLE_COLOUR('',#1038);
#1038 = DRAUGHTING_PRE_DEFINED_COLOUR('blue');
#1039 = MECHANICAL_DESIGN_GEOMETRIC_PRESENTATION_REPRESENTATION('',(
    #1040),#1010);
#1040 = STYLED_ITEM('color',(#1041),#606);
#1041 = PRESENTATION_STYLE_ASSIGNMENT((#1042));
#1042 = SURFACE_STYLE_USAGE(.BOTH.,#1043);
#1043 = SURFACE_SIDE_STYLE('',(#1044));
#1044 = SURFACE_STYLE_FILL_AREA(#1045);
#1045 = FILL_AREA_STYLE('',(#1046));
#1046 = FILL_AREA_STYLE_COLOUR('',#1047);
#1047 = DRAUGHTING_PRE_DEFINED_COLOUR('red');
ENDSEC;
END-ISO-10303-21;
