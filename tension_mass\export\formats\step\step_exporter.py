#!/usr/bin/env python3
"""
STEP Exporter Module

Orchestrates STEP file export operations using specialized components.
Implements build123d best practices with improved modularity and error handling.

This is the refactored version that replaces the original monolithic implementation.
Legacy version is available as step_exporter_legacy.py for backward compatibility.
"""

from pathlib import Path
from typing import Dict, Any, List, Optional, Tuple
import logging
import time
from dataclasses import dataclass

from ...core.config import ExportConfig, get_config
from ...core.path_manager import PathManager
from ...geometry.geometry_operations import BooleanOperationsEngine, GeometryError
from ...components.component_manager import ComponentCollector, ComponentValidationError
from .step_writer import STEPFileWriter, STEPExportError


@dataclass
class ExportSummary:
    """Summary of export operations."""
    individual_exports: int
    master_assembly_path: Optional[Path]
    group_unions: int
    total_volume: float
    export_time: float
    errors: List[str]


class StepExporter:
    """
    Refactored STEP exporter with modular design and build123d best practices.
    
    Orchestrates specialized components for geometry operations, component management,
    and file export while maintaining the same public interface.
    """
    
    def __init__(self, 
                 config: Optional[ExportConfig] = None,
                 path_manager: Optional[PathManager] = None):
        """
        Initialize STEP exporter with specialized components.
        
        Args:
            config: Export configuration (uses default if None)
            path_manager: Path manager instance (creates new if None)
        """
        self.config = config or get_config()
        self.path_manager = path_manager or PathManager(self.config)
        self.logger = self.config.get_logger(f"{__name__}.StepExporter")
        
        # Initialize specialized components
        self.geometry_engine = BooleanOperationsEngine(self.logger)
        self.component_collector = ComponentCollector(self.logger)
        
        # Note: step_writer will be initialized when paths are available
        self.step_writer = None
        
        # Performance tracking
        self._start_time = None
        self._errors = []
    
    def _ensure_step_writer_initialized(self):
        """Initialize step writer with correct output directory when paths are available."""
        if self.step_writer is None:
            try:
                # Use the step_files directory from path manager
                step_files_dir = self.path_manager.get_path('step_files')
                self.step_writer = STEPFileWriter(
                    output_directory=step_files_dir,
                    default_colors=self.config.default_colors,
                    logger=self.logger
                )
                self.logger.debug(f"Initialized step writer with output directory: {step_files_dir}")
            except ValueError as e:
                # If paths not available, create a fallback
                fallback_dir = Path(self.config.base_output_dir) / 'step_files'
                self.step_writer = STEPFileWriter(
                    output_directory=fallback_dir,
                    default_colors=self.config.default_colors,
                    logger=self.logger
                )
                self.logger.warning(f"Using fallback step writer directory: {fallback_dir}")
    
    def export_individual_piles(self, 
                               all_pile_components: Dict[str, Dict[str, Any]],
                               selected_piles: Optional[List[str]] = None) -> Dict[str, float]:
        """
        Export individual pile assemblies to STEP format.
        
        Args:
            all_pile_components: Dictionary of all pile components
            selected_piles: List of selected pile marks (exports all if None)
            
        Returns:
            Dictionary with volume summary statistics
            
        Raises:
            RuntimeError: If export fails
        """
        from datetime import datetime
        
        individual_export_start = datetime.now()
        self.logger.info("🔧 Starting individual pile export...")
        self._start_timing()
        
        # Ensure step writer is initialized with correct paths
        self._ensure_step_writer_initialized()
        
        try:
            # Validate and determine export list
            export_ids = self.component_collector.validate_export_selection(
                all_pile_components, selected_piles
            )
            
            self.logger.info(f"📊 Exporting {len(export_ids)} individual piles...")
            
            exported_count = 0
            total_volume = 0.0
            
            for i, pile_id in enumerate(export_ids, 1):
                if i % 5 == 0 or i == len(export_ids):  # Log every 5 piles or the last one
                    self.logger.info(f"🔧 Processing individual pile {i}/{len(export_ids)}: {pile_id}")
                    
                try:
                    # Extract geometry components
                    components = self.component_collector.extract_geometry_objects(
                        all_pile_components[pile_id]
                    )
                    
                    if not components:
                        self.logger.warning(f"⚠️  No valid components for pile {pile_id}")
                        continue
                    
                    # Export pile to trimmed subdirectory
                    result = self.step_writer.export_individual_pile(
                        pile_id, 
                        components,
                        subdirectory="individual_piles/trimmed"
                    )
                    
                    exported_count += 1
                    total_volume += result.total_volume
                    
                except (STEPExportError, ComponentValidationError) as e:
                    self.logger.error(f"❌ Failed to export pile {pile_id}: {e}")
                    self._errors.append(f"Pile {pile_id}: {e}")
                    continue
            
            individual_export_duration = datetime.now() - individual_export_start
            
            if exported_count == 0:
                raise RuntimeError("No piles were successfully exported")
                
            self.logger.info(f"✅ Individual pile export completed in {individual_export_duration.total_seconds():.2f}s")
            
            # Export count logging removed for cleaner output
            
            return {
                'exported_count': exported_count,
                'total_volume_m3': total_volume,
                'average_volume_m3': total_volume / exported_count if exported_count > 0 else 0.0
            }
            
        except (ComponentValidationError, GeometryError) as e:
            raise RuntimeError(f"Individual pile export failed: {e}") from e
    
    def export_original_pile_parts(self, 
                                  pile_id: str, 
                                  original_components: Dict[str, Any]) -> Dict[str, Any]:
        """
        Export original pile parts (before clipping) to individual_piles directory.
        
        Args:
            pile_id: Pile identifier
            original_components: Dictionary of original pile components before clipping
            
        Returns:
            Dictionary with export results
            
        Raises:
            RuntimeError: If export fails
        """
        self.logger.debug(f"Exporting original parts for pile {pile_id}...")
        
        # Ensure step writer is initialized with correct paths
        self._ensure_step_writer_initialized()
        
        try:
            # Extract geometry objects using component collector
            geometry_objects = self.component_collector.extract_geometry_objects(original_components)
            
            if not geometry_objects:
                self.logger.warning(f"No valid geometry objects for original pile {pile_id}")
                return {'exported': False, 'reason': 'No valid geometry'}
            
            # Export original parts to original subdirectory
            result = self.step_writer.export_individual_pile(
                f"{pile_id}_original", 
                geometry_objects,
                subdirectory="individual_piles/original"
            )
            
            self.logger.debug(f"Exported original parts for pile {pile_id}: {result.part_count} components, {result.total_volume:.3f} m³")
            
            return {
                'exported': True,
                'file_path': result.file_path,
                'part_count': result.part_count,
                'total_volume': result.total_volume
            }
            
        except Exception as e:
            self.logger.error(f"Failed to export original parts for pile {pile_id}: {e}")
            return {'exported': False, 'reason': str(e)}
    
    def export_master_assembly(self, 
                              all_pile_components: Dict[str, Dict[str, Any]],
                              selected_piles: Optional[List[str]] = None,
                              pile_group_boundaries: Optional[Dict[int, List[Tuple[float, float]]]] = None) -> Path:
        """
        Export master assembly with all processed piles.
        
        Args:
            all_pile_components: Dictionary of all pile components
            selected_piles: List of selected pile marks
            pile_group_boundaries: Dictionary mapping pile group IDs to boundary coordinates
            
        Returns:
            Path to exported master assembly file
            
        Raises:
            RuntimeError: If export fails
        """
        self.logger.debug("Exporting master assembly STEP file...")
        
        # Ensure step writer is initialized with correct paths
        self._ensure_step_writer_initialized()
        
        try:
            # Validate export selection
            export_ids = self.component_collector.validate_export_selection(
                all_pile_components, selected_piles
            )
            
            # Collect all parts efficiently
            all_parts = self._collect_all_parts_vectorized(all_pile_components, export_ids)
            
            if not all_parts:
                raise RuntimeError("No parts available for master assembly")
            
            # Generate filename with simplified naming
            pile_count = len(export_ids)
            filename = self.path_manager.generate_filename(
                "All_Individual_Piles",
                "step",
                pile_count=0,  # Simplified naming without pile count
                include_timestamp=False
            )
            
            # Export master assembly
            result = self.step_writer.export_master_assembly(
                all_parts, filename, pile_group_boundaries
            )
            
            return result.file_path
            
        except (ComponentValidationError, STEPExportError, GeometryError) as e:
            raise RuntimeError(f"Master assembly export failed: {e}") from e

    def export_union_assembly(self, 
                             union_parts: List[Any],
                             pile_group_boundaries: Optional[Dict[int, List[Tuple[float, float]]]] = None) -> Path:
        """
        Export all union parts as a single assembly STEP file.
        
        Args:
            union_parts: List of all union geometry parts
            pile_group_boundaries: Optional pile group boundaries
            
        Returns:
            Path to exported STEP file
            
        Raises:
            RuntimeError: If export fails
        """
        try:
            self.logger.info(f"Exporting union assembly with {len(union_parts)} union parts...")
            
            if not union_parts:
                raise RuntimeError("No union parts available for assembly")
            
            # Generate filename for union assembly
            filename = self.path_manager.generate_filename(
                "All_Union_Pile",
                "step",
                pile_count=0,  # Simplified naming without pile count
                include_timestamp=False
            )
            
            # Export union assembly
            result = self.step_writer.export_union_assembly(
                union_parts, filename, pile_group_boundaries
            )
            
            self.logger.info(f"Successfully exported union assembly: {filename}")
            return result.file_path
            
        except (ComponentValidationError, STEPExportError, GeometryError) as e:
            raise RuntimeError(f"Union assembly export failed: {e}") from e
    
    def export_group_unions(self, 
                          all_pile_components: Dict[str, Dict[str, Any]],
                          overlap_result: Any,
                          pile_data: Optional[Any] = None,
                          selected_piles: Optional[List[str]] = None) -> Tuple[Dict[str, Dict[str, Any]], List[Any]]:
        """
        Export group-based STEP files with boolean operations.
        
        Args:
            all_pile_components: Dictionary of all pile components
            overlap_result: OverlapResult from overlap detection
            pile_data: Original pile data for material determination
            selected_piles: List of selected pile marks for fallback individual groups
            
        Returns:
            Tuple of (group statistics dictionary, list of all union parts)
            
        Raises:
            RuntimeError: If export fails
        """
        from datetime import datetime
        
        group_export_start = datetime.now()
        self.logger.info("🔧 Starting group union export (most time-consuming operation)...")
        
        # Ensure step writer is initialized with correct paths
        self._ensure_step_writer_initialized()
        
        try:
            # Determine groups to process
            overlapping_groups = []
            individual_groups = []
            
            if overlap_result and hasattr(overlap_result, 'groups') and overlap_result.groups:
                # Get overlapping groups (groups with size > 1)
                overlapping_groups = [
                    members for members in overlap_result.groups.values() 
                    if len(members) > 1
                ]
                
                # Get individual piles (groups with size == 1 or piles not in any group)
                all_grouped_piles = set()
                for members in overlap_result.groups.values():
                    all_grouped_piles.update(members)
                
                if selected_piles:
                    individual_piles = [pile for pile in selected_piles if pile not in all_grouped_piles]
                    # Also include single-pile groups from overlap result
                    single_pile_groups = [
                        members[0] for members in overlap_result.groups.values() 
                        if len(members) == 1
                    ]
                    individual_piles.extend(single_pile_groups)
                    individual_groups = [[pile] for pile in individual_piles]
                    
                self.logger.info(f"📊 Found {len(overlapping_groups)} overlapping groups and {len(individual_groups)} individual groups")
            else:
                # No overlap result - create individual groups for all selected piles
                if selected_piles:
                    individual_groups = [[pile] for pile in selected_piles]
                    self.logger.info(f"ℹ️  No overlaps detected - creating {len(individual_groups)} individual groups")
                else:
                    self.logger.warning("⚠️  No overlap result and no selected piles provided")
                    return {}, []
            
            # Combine all groups to process
            all_groups = overlapping_groups + individual_groups
            
            if not all_groups:
                self.logger.warning("⚠️  No groups to process")
                return {}, []
            
            self.logger.info(f"🔄 Processing {len(all_groups)} total groups ({len(overlapping_groups)} overlapping + {len(individual_groups)} individual)")
            
            group_stats = {}
            union_parts = []  # Collect all union parts for union assembly
            group_id_counter = 1
            
            # Process overlapping groups first (most time-consuming)
            if overlapping_groups:
                self.logger.info(f"⏱️  Processing {len(overlapping_groups)} overlapping groups (complex unions)...")
                overlapping_start = datetime.now()
                
            for i, group_members in enumerate(overlapping_groups, 1):
                group_start = datetime.now()
                self.logger.info(f"🔧 Processing overlapping group {i}/{len(overlapping_groups)} with {len(group_members)} piles...")
                try:
                    stats, parts = self._export_single_group_union_optimized(
                        group_id_counter, group_members, all_pile_components, pile_data
                    )
                    group_duration = datetime.now() - group_start
                    if stats:
                        group_stats[f"Group_{group_id_counter}"] = stats
                        union_parts.extend(parts)  # Collect union parts
                        self.logger.info(f"✅ Group {group_id_counter} completed in {group_duration.total_seconds():.2f}s ({len(parts)} union parts)")
                        group_id_counter += 1
                    else:
                        self.logger.warning(f"⚠️  Group {group_id_counter} produced no results")
                        group_id_counter += 1
                        
                except (GeometryError, STEPExportError) as e:
                    group_duration = datetime.now() - group_start
                    error_msg = f"🚨 OVERLAPPING GROUP UNION FAILED - Group {group_id_counter} ({len(group_members)} piles): {type(e).__name__}: {str(e)}"
                    self.logger.error(error_msg)
                    self.logger.error(f"❌ Failed after {group_duration.total_seconds():.2f}s")
                    self.logger.error(f"❌ GROUP UNION ERROR: {error_msg}")
                    self._errors.append(f"Overlapping Group {group_id_counter}: {e}")
                    group_id_counter += 1
                    continue
                    
            if overlapping_groups:
                overlapping_duration = datetime.now() - overlapping_start
                self.logger.info(f"✅ Overlapping groups completed in {overlapping_duration.total_seconds():.2f}s")
            
            # Process individual groups (faster)
            if individual_groups:
                self.logger.info(f"⏱️  Processing {len(individual_groups)} individual groups (simple exports)...")
                individual_start = datetime.now()
                
            for i, group_members in enumerate(individual_groups, 1):
                if i % 5 == 0 or i == len(individual_groups):  # Log every 5 groups or the last one
                    self.logger.info(f"🔧 Processing individual groups {i}/{len(individual_groups)}...")
                try:
                    stats, parts = self._export_single_group_union_optimized(
                        group_id_counter, group_members, all_pile_components, pile_data
                    )
                    if stats:
                        group_stats[f"Group_{group_id_counter}"] = stats
                        union_parts.extend(parts)  # Collect union parts
                        group_id_counter += 1
                        
                except (GeometryError, STEPExportError) as e:
                    error_msg = f"🚨 INDIVIDUAL GROUP UNION FAILED - Group {group_id_counter} ({len(group_members)} piles): {type(e).__name__}: {str(e)}"
                    self.logger.error(error_msg)
                    self.logger.error(f"❌ GROUP UNION ERROR: {error_msg}")
                    self._errors.append(f"Individual Group {group_id_counter}: {e}")
                    group_id_counter += 1
                    continue
                    
            if individual_groups:
                individual_duration = datetime.now() - individual_start
                self.logger.info(f"✅ Individual groups completed in {individual_duration.total_seconds():.2f}s")
            
            total_group_duration = datetime.now() - group_export_start
            overlapping_count = len(overlapping_groups)
            individual_count = len(individual_groups)
            self.logger.info(f"🎉 Group union export completed in {total_group_duration.total_seconds():.2f}s")
            self.logger.info(f"📊 Exported {len(group_stats)} group union STEP files ({overlapping_count} overlapping groups, {individual_count} individual groups)")
            self.logger.info(f"📦 Collected {len(union_parts)} union parts for union assembly")
            return group_stats, union_parts
            
        except Exception as e:
            raise RuntimeError(f"Group union export failed: {e}") from e
    
    def _collect_all_parts_vectorized(self, 
                                    all_pile_components: Dict[str, Dict[str, Any]],
                                    export_ids: List[str]) -> List[Any]:
        """
        Collect all parts using vectorized operations for better performance.
        
        Args:
            all_pile_components: All pile components
            export_ids: List of pile IDs to export
            
        Returns:
            List of all geometry parts
        """
        all_parts = []
        
        # Collect all geometry objects in a single pass
        for pile_id in export_ids:
            components = all_pile_components[pile_id]
            pile_parts = self.component_collector.extract_geometry_objects(components)
            
            if pile_parts:
                # Assign colors to avoid warnings
                colored_parts = self.step_writer._assign_component_colors(pile_parts)
                all_parts.extend(colored_parts)
        
        # Removed verbose parts collection log
        return all_parts
    
    def _export_single_group_union_optimized(self,
                                           group_id: int,
                                           group_members: List[str],
                                           all_pile_components: Dict[str, Dict[str, Any]],
                                           pile_data: Optional[Any]) -> Tuple[Dict[str, Any], List[Any]]:
        """
        Export a single group union with optimized boolean operations.

        Args:
            group_id: Group identifier
            group_members: List of pile IDs in group
            all_pile_components: All pile components
            pile_data: Original pile data

        Returns:
            Tuple of (group statistics dictionary, list of union parts)
        """
        from datetime import datetime

        single_group_start = datetime.now()
        self.logger.info(f"🔧 Processing Group {group_id} with {len(group_members)} piles:")
        self.logger.info(f"📝 Group members: {', '.join(group_members[:10])}{'...' if len(group_members) > 10 else ''}")

        # 🔍 PART2 DEBUG: Log initial component analysis for this group
        self.logger.info(f"🔍 PART2 DEBUG: Analyzing initial components for Group {group_id}")
        part2_initial_count = 0
        part2_initial_volume = 0.0
        for pile_id in group_members:
            pile_components = all_pile_components.get(pile_id, {})
            part2_component = pile_components.get('part2')
            if part2_component is not None:
                part2_initial_count += 1
                part2_vol = getattr(part2_component, 'volume', 0.0)
                part2_initial_volume += part2_vol
                self.logger.info(f"🔍 PART2 DEBUG: Pile {pile_id} has Part2 with volume {part2_vol:.3f} m³")

        self.logger.info(f"🔍 PART2 DEBUG: Group {group_id} initial Part2 summary: {part2_initial_count} components, {part2_initial_volume:.3f} m³ total")

        # Organize components by type
        self.logger.info("⏱️  Organizing components by type...")
        organize_start = datetime.now()
        group_components = self.component_collector.organize_group_components(
            group_members, all_pile_components
        )
        organize_duration = datetime.now() - organize_start
        self.logger.info(f"✅ Component organization completed in {organize_duration.total_seconds():.2f}s")

        # 🔍 PART2 DEBUG: Log organized Part2 components
        part2_organized = group_components.get('part2', [])
        part2_organized_volume = sum(getattr(comp, 'volume', 0.0) for comp in part2_organized)
        self.logger.info(f"🔍 PART2 DEBUG: After organization - Group {group_id} has {len(part2_organized)} Part2 components, {part2_organized_volume:.3f} m³ total")

        if part2_initial_count != len(part2_organized):
            self.logger.warning(f"🔍 PART2 DEBUG: COMPONENT COUNT MISMATCH! Initial: {part2_initial_count}, Organized: {len(part2_organized)}")
        if abs(part2_initial_volume - part2_organized_volume) > 0.001:
            self.logger.warning(f"🔍 PART2 DEBUG: VOLUME MISMATCH! Initial: {part2_initial_volume:.3f}, Organized: {part2_organized_volume:.3f}")
        
        if not any(group_components.values()):
            raise GeometryError(f"No valid components for group {group_id}")
        
        # Calculate original volumes before processing/trimming
        self.logger.info("⏱️  Calculating original volumes...")
        volumes_start = datetime.now()
        original_volumes = self._calculate_original_volumes(group_components)
        volumes_duration = datetime.now() - volumes_start
        self.logger.info(f"✅ Volume calculation completed in {volumes_duration.total_seconds():.2f}s")
        
        # Process with geometry engine (most time-consuming part)
        self.logger.info("⏱️  Starting geometry processing (complex union operations)...")
        geometry_start = datetime.now()
        geometry_result = self.geometry_engine.process_group_components(
            group_components, group_members
        )
        geometry_duration = datetime.now() - geometry_start
        self.logger.info(f"✅ Geometry processing completed in {geometry_duration.total_seconds():.2f}s")

        # 🔍 PART2 DEBUG: Log Part2 results after geometry processing
        part2_result_volume = geometry_result.volumes.get('part2_volume', 0.0)
        part2_parts_count = len([p for p in geometry_result.parts if getattr(p, '_part_type', None) == 'Part2'])
        self.logger.info(f"🔍 PART2 DEBUG: After geometry processing - Group {group_id} Part2 volume: {part2_result_volume:.3f} m³, parts count: {part2_parts_count}")

        # Count Part2 sub-objects
        part2_sub_objects = [obj for obj in geometry_result.sub_objects if obj.get('part_type') == 'Part2']
        part2_sub_volume = sum(obj.get('volume', 0.0) for obj in part2_sub_objects)
        self.logger.info(f"🔍 PART2 DEBUG: Part2 sub-objects: {len(part2_sub_objects)}, total volume: {part2_sub_volume:.3f} m³")

        if part2_organized_volume > 0.1 and part2_result_volume < 0.001:
            self.logger.error(f"🔍 PART2 DEBUG: CRITICAL - Group {group_id} Part2 ELIMINATED! Input: {part2_organized_volume:.3f} m³ → Output: {part2_result_volume:.3f} m³")
        
        # Export group assembly
        self.logger.info("⏱️  Exporting group assembly to STEP file...")
        export_start = datetime.now()
        export_result = self.step_writer.export_group_union(
            geometry_result.parts, group_id, {
                'sub_objects': geometry_result.sub_objects,
                'group_members': group_members,
                **geometry_result.metadata
            }, all_pile_components
        )
        export_duration = datetime.now() - export_start
        self.logger.info(f"✅ STEP file export completed in {export_duration.total_seconds():.2f}s")
        
        # Calculate volume reduction percentage
        trimmed_total = sum(geometry_result.volumes.values())
        original_total = sum(original_volumes.values())
        volume_reduction_percent = ((original_total - trimmed_total) / original_total * 100) if original_total > 0 else 0.0
        
        total_duration = datetime.now() - single_group_start
        self.logger.info(f"🎉 Group {group_id} processing completed in {total_duration.total_seconds():.2f}s")
        self.logger.info(f"📊 Result: {len(geometry_result.parts)} parts, {trimmed_total:.2f} m³ (reduced by {volume_reduction_percent:.1f}%)")
        
        # Calculate and return statistics and union parts
        part1_sub_objects = [obj for obj in geometry_result.sub_objects if obj.get('part_type') == 'Part1']
        part2_sub_objects = [obj for obj in geometry_result.sub_objects if obj.get('part_type') == 'Part2']
        part3_sub_objects = [obj for obj in geometry_result.sub_objects if obj.get('part_type') == 'Part3']
        
        # 🔍 DEBUG: Log sub-object filtering results
        self.logger.info(f"🔍 DEBUG: Total sub-objects in geometry_result: {len(geometry_result.sub_objects)}")
        self.logger.info(f"🔍 DEBUG: Part1 sub-objects: {len(part1_sub_objects)}")
        self.logger.info(f"🔍 DEBUG: Part2 sub-objects: {len(part2_sub_objects)}")
        self.logger.info(f"🔍 DEBUG: Part3 sub-objects: {len(part3_sub_objects)}")
        
        for i, obj in enumerate(part3_sub_objects):
            obj_label = obj.get('label', 'unknown')
            obj_volume = obj.get('volume', 0.0)
            self.logger.info(f"🔍 DEBUG: Part3 sub-object {i+1}: {obj_label} = {obj_volume:.3f} m³")
        
        stats = {
            'members': group_members,
            'member_count': len(group_members),
            'total_volume': sum(geometry_result.volumes.values()),
            'part_count': len(geometry_result.parts),
            'step_filename': export_result.file_path.name,
            'sub_objects': geometry_result.sub_objects,
            'part1_sub_objects': part1_sub_objects,
            'part2_sub_objects': part2_sub_objects,
            'part3_sub_objects': part3_sub_objects,
            'volume_reduction_percent': volume_reduction_percent,
            **geometry_result.volumes,
            **original_volumes
        }
        
        # Use trimmed parts for union assembly if available, otherwise use original parts
        union_parts = export_result.trimmed_parts if export_result.trimmed_parts else geometry_result.parts
        
        return stats, union_parts
    
    def _calculate_original_volumes(self, group_components: Dict[str, List[Any]]) -> Dict[str, float]:
        """
        Calculate original volumes before any trimming operations.
        
        Args:
            group_components: Components organized by type
            
        Returns:
            Dictionary with original volumes by part type
        """
        original_volumes = {
            'original_part1_volume': 0.0,
            'original_part2_volume': 0.0,
            'original_part3_volume': 0.0
        }
        
        try:
            # Calculate Part 1 original volume
            part1_components = group_components.get('part1', [])
            if part1_components:
                part1_volume = sum(getattr(comp, 'volume', 0.0) for comp in part1_components 
                                 if hasattr(comp, 'volume'))
                original_volumes['original_part1_volume'] = part1_volume
            
            # Calculate Part 2 original volume
            part2_components = group_components.get('part2', [])
            if part2_components:
                part2_volume = sum(getattr(comp, 'volume', 0.0) for comp in part2_components 
                                 if hasattr(comp, 'volume'))
                original_volumes['original_part2_volume'] = part2_volume
            
            # Calculate Part 3 original volume
            part3_components = group_components.get('part3', [])
            if part3_components:
                part3_volume = sum(getattr(comp, 'volume', 0.0) for comp in part3_components 
                                 if hasattr(comp, 'volume'))
                original_volumes['original_part3_volume'] = part3_volume
                
            self.logger.debug(f"Original volumes: {original_volumes}")
            
        except Exception as e:
            self.logger.warning(f"Failed to calculate original volumes: {e}")
        
        return original_volumes
    
    def _start_timing(self) -> None:
        """Start performance timing."""
        self._start_time = time.time()
    
    def _get_elapsed_time(self) -> float:
        """Get elapsed time since timing started."""
        if self._start_time is None:
            return 0.0
        return time.time() - self._start_time
    
    def get_export_summary(self) -> ExportSummary:
        """Get summary of all export operations."""
        return ExportSummary(
            individual_exports=0,  # Would track this during operations
            master_assembly_path=None,  # Would track this during operations
            group_unions=0,  # Would track this during operations
            total_volume=0.0,  # Would track this during operations
            export_time=self._get_elapsed_time(),
            errors=list(self._errors)
        )
