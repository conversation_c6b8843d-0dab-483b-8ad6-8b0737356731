ISO-10303-21;
HEADER;
FILE_DESCRIPTION(('Open CASCADE Model'),'2;1');
FILE_NAME('Pile_PC1-P6','2025-08-25T20:08:32',('Author'),('Open CASCADE'
    ),'Open CASCADE STEP processor 7.8','build123d','Unknown');
FILE_SCHEMA(('AUTOMOTIVE_DESIGN { 1 0 10303 214 1 1 1 1 }'));
ENDSEC;
DATA;
#1 = APPLICATION_PROTOCOL_DEFINITION('international standard',
  'automotive_design',2000,#2);
#2 = APPLICATION_CONTEXT(
  'core data for automotive mechanical design processes');
#3 = SHAPE_DEFINITION_REPRESENTATION(#4,#10);
#4 = PRODUCT_DEFINITION_SHAPE('','',#5);
#5 = PRODUCT_DEFINITION('design','',#6,#9);
#6 = PRODUCT_DEFINITION_FORMATION('','',#7);
#7 = PRODUCT('Pile_PC1-P6','Pile_PC1-P6','',(#8));
#8 = PRODUCT_CONTEXT('',#2,'mechanical');
#9 = PRODUCT_DEFINITION_CONTEXT('part definition',#2,'design');
#10 = SHAPE_REPRESENTATION('',(#11,#15,#19,#23),#27);
#11 = AXIS2_PLACEMENT_3D('',#12,#13,#14);
#12 = CARTESIAN_POINT('',(0.,0.,0.));
#13 = DIRECTION('',(0.,0.,1.));
#14 = DIRECTION('',(1.,0.,-0.));
#15 = AXIS2_PLACEMENT_3D('',#16,#17,#18);
#16 = CARTESIAN_POINT('',(51.889186,125.797167,4.274863));
#17 = DIRECTION('',(0.,0.,1.));
#18 = DIRECTION('',(1.,0.,0.));
#19 = AXIS2_PLACEMENT_3D('',#20,#21,#22);
#20 = CARTESIAN_POINT('',(0.,0.,0.));
#21 = DIRECTION('',(0.,0.,1.));
#22 = DIRECTION('',(1.,0.,-0.));
#23 = AXIS2_PLACEMENT_3D('',#24,#25,#26);
#24 = CARTESIAN_POINT('',(0.,0.,0.));
#25 = DIRECTION('',(0.,0.,1.));
#26 = DIRECTION('',(1.,0.,-0.));
#27 = ( GEOMETRIC_REPRESENTATION_CONTEXT(3) 
GLOBAL_UNCERTAINTY_ASSIGNED_CONTEXT((#31)) GLOBAL_UNIT_ASSIGNED_CONTEXT(
(#28,#29,#30)) REPRESENTATION_CONTEXT('Context #1',
  '3D Context with UNIT and UNCERTAINTY') );
#28 = ( LENGTH_UNIT() NAMED_UNIT(*) SI_UNIT(.MILLI.,.METRE.) );
#29 = ( NAMED_UNIT(*) PLANE_ANGLE_UNIT() SI_UNIT($,.RADIAN.) );
#30 = ( NAMED_UNIT(*) SI_UNIT($,.STERADIAN.) SOLID_ANGLE_UNIT() );
#31 = UNCERTAINTY_MEASURE_WITH_UNIT(LENGTH_MEASURE(1.E-07),#28,
  'distance_accuracy_value','confusion accuracy');
#32 = PRODUCT_RELATED_PRODUCT_CATEGORY('part',$,(#7));
#33 = SHAPE_DEFINITION_REPRESENTATION(#34,#40);
#34 = PRODUCT_DEFINITION_SHAPE('','',#35);
#35 = PRODUCT_DEFINITION('design','',#36,#39);
#36 = PRODUCT_DEFINITION_FORMATION('','',#37);
#37 = PRODUCT('PC1-P6_Part1','PC1-P6_Part1','',(#38));
#38 = PRODUCT_CONTEXT('',#2,'mechanical');
#39 = PRODUCT_DEFINITION_CONTEXT('part definition',#2,'design');
#40 = ADVANCED_BREP_SHAPE_REPRESENTATION('',(#11,#41),#139);
#41 = MANIFOLD_SOLID_BREP('',#42);
#42 = CLOSED_SHELL('',(#43,#131,#135));
#43 = ADVANCED_FACE('',(#44),#57,.T.);
#44 = FACE_BOUND('',#45,.T.);
#45 = EDGE_LOOP('',(#46,#80,#103,#130));
#46 = ORIENTED_EDGE('',*,*,#47,.F.);
#47 = EDGE_CURVE('',#48,#48,#50,.T.);
#48 = VERTEX_POINT('',#49);
#49 = CARTESIAN_POINT('',(0.275,-6.73555739531E-17,13.725137));
#50 = SURFACE_CURVE('',#51,(#56,#68),.PCURVE_S1.);
#51 = CIRCLE('',#52,0.275);
#52 = AXIS2_PLACEMENT_3D('',#53,#54,#55);
#53 = CARTESIAN_POINT('',(0.,0.,13.725137));
#54 = DIRECTION('',(0.,0.,1.));
#55 = DIRECTION('',(1.,0.,-0.));
#56 = PCURVE('',#57,#62);
#57 = CYLINDRICAL_SURFACE('',#58,0.275);
#58 = AXIS2_PLACEMENT_3D('',#59,#60,#61);
#59 = CARTESIAN_POINT('',(0.,0.,0.));
#60 = DIRECTION('',(0.,0.,1.));
#61 = DIRECTION('',(1.,0.,-0.));
#62 = DEFINITIONAL_REPRESENTATION('',(#63),#67);
#63 = LINE('',#64,#65);
#64 = CARTESIAN_POINT('',(0.,13.725137));
#65 = VECTOR('',#66,1.);
#66 = DIRECTION('',(1.,0.));
#67 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#68 = PCURVE('',#69,#74);
#69 = PLANE('',#70);
#70 = AXIS2_PLACEMENT_3D('',#71,#72,#73);
#71 = CARTESIAN_POINT('',(0.,0.,13.725137));
#72 = DIRECTION('',(0.,0.,1.));
#73 = DIRECTION('',(1.,0.,-0.));
#74 = DEFINITIONAL_REPRESENTATION('',(#75),#79);
#75 = CIRCLE('',#76,0.275);
#76 = AXIS2_PLACEMENT_2D('',#77,#78);
#77 = CARTESIAN_POINT('',(0.,0.));
#78 = DIRECTION('',(1.,0.));
#79 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#80 = ORIENTED_EDGE('',*,*,#81,.F.);
#81 = EDGE_CURVE('',#82,#48,#84,.T.);
#82 = VERTEX_POINT('',#83);
#83 = CARTESIAN_POINT('',(0.275,-6.73555739531E-17,0.));
#84 = SEAM_CURVE('',#85,(#89,#96),.PCURVE_S1.);
#85 = LINE('',#86,#87);
#86 = CARTESIAN_POINT('',(0.275,-6.73555739531E-17,0.));
#87 = VECTOR('',#88,1.);
#88 = DIRECTION('',(0.,0.,1.));
#89 = PCURVE('',#57,#90);
#90 = DEFINITIONAL_REPRESENTATION('',(#91),#95);
#91 = LINE('',#92,#93);
#92 = CARTESIAN_POINT('',(6.28318530718,-0.));
#93 = VECTOR('',#94,1.);
#94 = DIRECTION('',(0.,1.));
#95 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#96 = PCURVE('',#57,#97);
#97 = DEFINITIONAL_REPRESENTATION('',(#98),#102);
#98 = LINE('',#99,#100);
#99 = CARTESIAN_POINT('',(0.,-0.));
#100 = VECTOR('',#101,1.);
#101 = DIRECTION('',(0.,1.));
#102 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#103 = ORIENTED_EDGE('',*,*,#104,.T.);
#104 = EDGE_CURVE('',#82,#82,#105,.T.);
#105 = SURFACE_CURVE('',#106,(#111,#118),.PCURVE_S1.);
#106 = CIRCLE('',#107,0.275);
#107 = AXIS2_PLACEMENT_3D('',#108,#109,#110);
#108 = CARTESIAN_POINT('',(0.,0.,0.));
#109 = DIRECTION('',(0.,0.,1.));
#110 = DIRECTION('',(1.,0.,-0.));
#111 = PCURVE('',#57,#112);
#112 = DEFINITIONAL_REPRESENTATION('',(#113),#117);
#113 = LINE('',#114,#115);
#114 = CARTESIAN_POINT('',(0.,0.));
#115 = VECTOR('',#116,1.);
#116 = DIRECTION('',(1.,0.));
#117 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#118 = PCURVE('',#119,#124);
#119 = PLANE('',#120);
#120 = AXIS2_PLACEMENT_3D('',#121,#122,#123);
#121 = CARTESIAN_POINT('',(0.,0.,0.));
#122 = DIRECTION('',(0.,0.,1.));
#123 = DIRECTION('',(1.,0.,-0.));
#124 = DEFINITIONAL_REPRESENTATION('',(#125),#129);
#125 = CIRCLE('',#126,0.275);
#126 = AXIS2_PLACEMENT_2D('',#127,#128);
#127 = CARTESIAN_POINT('',(0.,0.));
#128 = DIRECTION('',(1.,0.));
#129 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#130 = ORIENTED_EDGE('',*,*,#81,.T.);
#131 = ADVANCED_FACE('',(#132),#69,.T.);
#132 = FACE_BOUND('',#133,.T.);
#133 = EDGE_LOOP('',(#134));
#134 = ORIENTED_EDGE('',*,*,#47,.T.);
#135 = ADVANCED_FACE('',(#136),#119,.F.);
#136 = FACE_BOUND('',#137,.T.);
#137 = EDGE_LOOP('',(#138));
#138 = ORIENTED_EDGE('',*,*,#104,.F.);
#139 = ( GEOMETRIC_REPRESENTATION_CONTEXT(3) 
GLOBAL_UNCERTAINTY_ASSIGNED_CONTEXT((#143)) GLOBAL_UNIT_ASSIGNED_CONTEXT
((#140,#141,#142)) REPRESENTATION_CONTEXT('Context #1',
  '3D Context with UNIT and UNCERTAINTY') );
#140 = ( LENGTH_UNIT() NAMED_UNIT(*) SI_UNIT(.MILLI.,.METRE.) );
#141 = ( NAMED_UNIT(*) PLANE_ANGLE_UNIT() SI_UNIT($,.RADIAN.) );
#142 = ( NAMED_UNIT(*) SI_UNIT($,.STERADIAN.) SOLID_ANGLE_UNIT() );
#143 = UNCERTAINTY_MEASURE_WITH_UNIT(LENGTH_MEASURE(1.E-07),#140,
  'distance_accuracy_value','confusion accuracy');
#144 = CONTEXT_DEPENDENT_SHAPE_REPRESENTATION(#145,#147);
#145 = ( REPRESENTATION_RELATIONSHIP('','',#40,#10) 
REPRESENTATION_RELATIONSHIP_WITH_TRANSFORMATION(#146) 
SHAPE_REPRESENTATION_RELATIONSHIP() );
#146 = ITEM_DEFINED_TRANSFORMATION('','',#11,#15);
#147 = PRODUCT_DEFINITION_SHAPE('Placement','Placement of an item',#148
  );
#148 = NEXT_ASSEMBLY_USAGE_OCCURRENCE('96','=>[0:1:1:2]','',#5,#35,$);
#149 = PRODUCT_RELATED_PRODUCT_CATEGORY('part',$,(#37));
#150 = SHAPE_DEFINITION_REPRESENTATION(#151,#157);
#151 = PRODUCT_DEFINITION_SHAPE('','',#152);
#152 = PRODUCT_DEFINITION('design','',#153,#156);
#153 = PRODUCT_DEFINITION_FORMATION('','',#154);
#154 = PRODUCT('PC1-P6_Part2','PC1-P6_Part2','',(#155));
#155 = PRODUCT_CONTEXT('',#2,'mechanical');
#156 = PRODUCT_DEFINITION_CONTEXT('part definition',#2,'design');
#157 = ADVANCED_BREP_SHAPE_REPRESENTATION('',(#11,#158),#491);
#158 = MANIFOLD_SOLID_BREP('',#159);
#159 = CLOSED_SHELL('',(#160,#361,#411,#459,#485));
#160 = ADVANCED_FACE('',(#161),#175,.T.);
#161 = FACE_BOUND('',#162,.T.);
#162 = EDGE_LOOP('',(#163,#193,#222,#256,#312,#334,#335));
#163 = ORIENTED_EDGE('',*,*,#164,.T.);
#164 = EDGE_CURVE('',#165,#167,#169,.T.);
#165 = VERTEX_POINT('',#166);
#166 = CARTESIAN_POINT('',(52.164186,125.797167,4.274863));
#167 = VERTEX_POINT('',#168);
#168 = CARTESIAN_POINT('',(56.205637884327,125.797167,11.274863));
#169 = SEAM_CURVE('',#170,(#174,#186),.PCURVE_S1.);
#170 = LINE('',#171,#172);
#171 = CARTESIAN_POINT('',(52.164186,125.797167,4.274863));
#172 = VECTOR('',#173,1.);
#173 = DIRECTION('',(0.5,-1.224646799147E-16,0.866025403784));
#174 = PCURVE('',#175,#180);
#175 = CONICAL_SURFACE('',#176,0.275,0.523598775598);
#176 = AXIS2_PLACEMENT_3D('',#177,#178,#179);
#177 = CARTESIAN_POINT('',(51.889186,125.797167,4.274863));
#178 = DIRECTION('',(0.,0.,1.));
#179 = DIRECTION('',(1.,0.,0.));
#180 = DEFINITIONAL_REPRESENTATION('',(#181),#185);
#181 = LINE('',#182,#183);
#182 = CARTESIAN_POINT('',(6.28318530718,-0.));
#183 = VECTOR('',#184,1.);
#184 = DIRECTION('',(0.,1.));
#185 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#186 = PCURVE('',#175,#187);
#187 = DEFINITIONAL_REPRESENTATION('',(#188),#192);
#188 = LINE('',#189,#190);
#189 = CARTESIAN_POINT('',(0.,-0.));
#190 = VECTOR('',#191,1.);
#191 = DIRECTION('',(0.,1.));
#192 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#193 = ORIENTED_EDGE('',*,*,#194,.F.);
#194 = EDGE_CURVE('',#195,#167,#197,.T.);
#195 = VERTEX_POINT('',#196);
#196 = CARTESIAN_POINT('',(53.53263213443,121.80582115267,11.274863));
#197 = SURFACE_CURVE('',#198,(#203,#210),.PCURVE_S1.);
#198 = CIRCLE('',#199,4.316451884327);
#199 = AXIS2_PLACEMENT_3D('',#200,#201,#202);
#200 = CARTESIAN_POINT('',(51.889186,125.797167,11.274863));
#201 = DIRECTION('',(0.,0.,1.));
#202 = DIRECTION('',(1.,0.,0.));
#203 = PCURVE('',#175,#204);
#204 = DEFINITIONAL_REPRESENTATION('',(#205),#209);
#205 = LINE('',#206,#207);
#206 = CARTESIAN_POINT('',(0.,7.));
#207 = VECTOR('',#208,1.);
#208 = DIRECTION('',(1.,0.));
#209 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#210 = PCURVE('',#211,#216);
#211 = PLANE('',#212);
#212 = AXIS2_PLACEMENT_3D('',#213,#214,#215);
#213 = CARTESIAN_POINT('',(51.889186,125.797167,11.274863));
#214 = DIRECTION('',(0.,0.,1.));
#215 = DIRECTION('',(1.,0.,0.));
#216 = DEFINITIONAL_REPRESENTATION('',(#217),#221);
#217 = CIRCLE('',#218,4.316451884327);
#218 = AXIS2_PLACEMENT_2D('',#219,#220);
#219 = CARTESIAN_POINT('',(0.,0.));
#220 = DIRECTION('',(1.,0.));
#221 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#222 = ORIENTED_EDGE('',*,*,#223,.F.);
#223 = EDGE_CURVE('',#224,#195,#226,.T.);
#224 = VERTEX_POINT('',#225);
#225 = CARTESIAN_POINT('',(51.008936,121.80569,10.878110180426));
#226 = SURFACE_CURVE('',#227,(#232,#244),.PCURVE_S1.);
#227 = HYPERBOLA('',#228,6.913361718787,3.991431249347);
#228 = AXIS2_PLACEMENT_3D('',#229,#230,#231);
#229 = CARTESIAN_POINT('',(51.889393428655,121.80573575604,
    3.798549027919));
#230 = DIRECTION('',(-5.196848995186E-05,0.99999999865,0.));
#231 = DIRECTION('',(0.,0.,1.));
#232 = PCURVE('',#175,#233);
#233 = DEFINITIONAL_REPRESENTATION('',(#234),#243);
#234 = B_SPLINE_CURVE_WITH_KNOTS('',7,(#235,#236,#237,#238,#239,#240,
    #241,#242),.UNSPECIFIED.,.F.,.F.,(8,8),(-0.400868689999,
    0.400868689999),.PIECEWISE_BEZIER_KNOTS.);
#235 = CARTESIAN_POINT('',(4.321896416479,7.));
#236 = CARTESIAN_POINT('',(4.427806141743,6.674016681587));
#237 = CARTESIAN_POINT('',(4.539103632585,6.462453466464));
#238 = CARTESIAN_POINT('',(4.654174955952,6.358326172249));
#239 = CARTESIAN_POINT('',(4.770706941798,6.358326172245));
#240 = CARTESIAN_POINT('',(4.885778265164,6.462453466466));
#241 = CARTESIAN_POINT('',(4.997075756006,6.674016681587));
#242 = CARTESIAN_POINT('',(5.102985481271,7.));
#243 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#244 = PCURVE('',#245,#250);
#245 = PLANE('',#246);
#246 = AXIS2_PLACEMENT_3D('',#247,#248,#249);
#247 = CARTESIAN_POINT('',(77.717428,121.807078,28.));
#248 = DIRECTION('',(-5.196848995186E-05,0.99999999865,0.));
#249 = DIRECTION('',(-0.99999999865,-5.196848995186E-05,0.));
#250 = DEFINITIONAL_REPRESENTATION('',(#251),#255);
#251 = ( BOUNDED_CURVE() B_SPLINE_CURVE(2,(#252,#253,#254),
.UNSPECIFIED.,.F.,.F.) B_SPLINE_CURVE_WITH_KNOTS((3,3),(-0.400868689999,
0.400868689999),.PIECEWISE_BEZIER_KNOTS.) CURVE() 
GEOMETRIC_REPRESENTATION_ITEM() RATIONAL_B_SPLINE_CURVE((1.,
1.081429596222,1.)) REPRESENTATION_ITEM('') );
#252 = CARTESIAN_POINT('',(27.471273314217,-16.725137));
#253 = CARTESIAN_POINT('',(25.828034606222,-17.8086522703));
#254 = CARTESIAN_POINT('',(24.184795898228,-16.725137));
#255 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#256 = ORIENTED_EDGE('',*,*,#257,.F.);
#257 = EDGE_CURVE('',#258,#224,#260,.T.);
#258 = VERTEX_POINT('',#259);
#259 = CARTESIAN_POINT('',(50.559579738223,129.90373540419,11.274863));
#260 = SURFACE_CURVE('',#261,(#266,#300),.PCURVE_S1.);
#261 = HYPERBOLA('',#262,1.905329831981,1.100042691389);
#262 = AXIS2_PLACEMENT_3D('',#263,#264,#265);
#263 = CARTESIAN_POINT('',(50.790832968198,125.73621997045,
    3.798549027919));
#264 = DIRECTION('',(0.998464005442,5.540424023454E-02,0.));
#265 = DIRECTION('',(-0.,0.,1.));
#266 = PCURVE('',#175,#267);
#267 = DEFINITIONAL_REPRESENTATION('',(#268),#299);
#268 = B_SPLINE_CURVE_WITH_KNOTS('',8,(#269,#270,#271,#272,#273,#274,
    #275,#276,#277,#278,#279,#280,#281,#282,#283,#284,#285,#286,#287,
    #288,#289,#290,#291,#292,#293,#294,#295,#296,#297,#298),
  .UNSPECIFIED.,.F.,.F.,(9,7,7,7,9),(-2.04358458549,-1.021792292745,
    -0.255448073186,0.894068256152,2.04358458549),.UNSPECIFIED.);
#269 = CARTESIAN_POINT('',(1.883920314336,7.));
#270 = CARTESIAN_POINT('',(1.916470635001,6.076625096722));
#271 = CARTESIAN_POINT('',(1.953615373932,5.292637826035));
#272 = CARTESIAN_POINT('',(1.99605897198,4.625085539533));
#273 = CARTESIAN_POINT('',(2.044602529658,4.055862062085));
#274 = CARTESIAN_POINT('',(2.100135853619,3.57052319277));
#275 = CARTESIAN_POINT('',(2.163603060963,3.157488436848));
#276 = CARTESIAN_POINT('',(2.235848558739,2.807506457323));
#277 = CARTESIAN_POINT('',(2.378301459031,2.292578192696));
#278 = CARTESIAN_POINT('',(2.444503178026,2.10324693513));
#279 = CARTESIAN_POINT('',(2.516015624417,1.942182211821));
#280 = CARTESIAN_POINT('',(2.592853294915,1.806911812135));
#281 = CARTESIAN_POINT('',(2.674810446558,1.695487106298));
#282 = CARTESIAN_POINT('',(2.761349104081,1.606421459238));
#283 = CARTESIAN_POINT('',(2.851560499222,1.538651979402));
#284 = CARTESIAN_POINT('',(3.083436455001,1.420820943301));
#285 = CARTESIAN_POINT('',(3.228274777996,1.396555738247));
#286 = CARTESIAN_POINT('',(3.374991308132,1.416499638643));
#287 = CARTESIAN_POINT('',(3.518625777724,1.480473643941));
#288 = CARTESIAN_POINT('',(3.653831881804,1.590196756081));
#289 = CARTESIAN_POINT('',(3.778180652595,1.749364992471));
#290 = CARTESIAN_POINT('',(3.890667788564,1.963921366108));
#291 = CARTESIAN_POINT('',(4.092051492598,2.521347874967));
#292 = CARTESIAN_POINT('',(4.180948253803,2.864217645796));
#293 = CARTESIAN_POINT('',(4.258097967311,3.280015164643));
#294 = CARTESIAN_POINT('',(4.32459893098,3.780327996989));
#295 = CARTESIAN_POINT('',(4.381824168099,4.380167060364));
#296 = CARTESIAN_POINT('',(4.431077657701,5.098818749236));
#297 = CARTESIAN_POINT('',(4.473511131186,5.961203233812));
#298 = CARTESIAN_POINT('',(4.510130241934,7.));
#299 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#300 = PCURVE('',#301,#306);
#301 = PLANE('',#302);
#302 = AXIS2_PLACEMENT_3D('',#303,#304,#305);
#303 = CARTESIAN_POINT('',(51.008936,121.80569,28.));
#304 = DIRECTION('',(0.998464005442,5.540424023454E-02,0.));
#305 = DIRECTION('',(-5.540424023454E-02,0.998464005442,0.));
#306 = DEFINITIONAL_REPRESENTATION('',(#307),#311);
#307 = ( BOUNDED_CURVE() B_SPLINE_CURVE(2,(#308,#309,#310),
.UNSPECIFIED.,.F.,.F.) B_SPLINE_CURVE_WITH_KNOTS((3,3),(-2.04358458549,
2.04358458549),.PIECEWISE_BEZIER_KNOTS.) CURVE() 
GEOMETRIC_REPRESENTATION_ITEM() RATIONAL_B_SPLINE_CURVE((1.,
3.923894879821,1.)) REPRESENTATION_ITEM('') );
#308 = CARTESIAN_POINT('',(8.110503092808,-16.725137));
#309 = CARTESIAN_POINT('',(3.936576530585,-23.71587990294));
#310 = CARTESIAN_POINT('',(-0.237350031639,-16.725137));
#311 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#312 = ORIENTED_EDGE('',*,*,#313,.F.);
#313 = EDGE_CURVE('',#167,#258,#314,.T.);
#314 = SURFACE_CURVE('',#315,(#320,#327),.PCURVE_S1.);
#315 = CIRCLE('',#316,4.316451884327);
#316 = AXIS2_PLACEMENT_3D('',#317,#318,#319);
#317 = CARTESIAN_POINT('',(51.889186,125.797167,11.274863));
#318 = DIRECTION('',(0.,0.,1.));
#319 = DIRECTION('',(1.,0.,0.));
#320 = PCURVE('',#175,#321);
#321 = DEFINITIONAL_REPRESENTATION('',(#322),#326);
#322 = LINE('',#323,#324);
#323 = CARTESIAN_POINT('',(0.,7.));
#324 = VECTOR('',#325,1.);
#325 = DIRECTION('',(1.,0.));
#326 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#327 = PCURVE('',#211,#328);
#328 = DEFINITIONAL_REPRESENTATION('',(#329),#333);
#329 = CIRCLE('',#330,4.316451884327);
#330 = AXIS2_PLACEMENT_2D('',#331,#332);
#331 = CARTESIAN_POINT('',(0.,0.));
#332 = DIRECTION('',(1.,0.));
#333 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#334 = ORIENTED_EDGE('',*,*,#164,.F.);
#335 = ORIENTED_EDGE('',*,*,#336,.T.);
#336 = EDGE_CURVE('',#165,#165,#337,.T.);
#337 = SURFACE_CURVE('',#338,(#343,#350),.PCURVE_S1.);
#338 = CIRCLE('',#339,0.275);
#339 = AXIS2_PLACEMENT_3D('',#340,#341,#342);
#340 = CARTESIAN_POINT('',(51.889186,125.797167,4.274863));
#341 = DIRECTION('',(0.,0.,1.));
#342 = DIRECTION('',(1.,0.,0.));
#343 = PCURVE('',#175,#344);
#344 = DEFINITIONAL_REPRESENTATION('',(#345),#349);
#345 = LINE('',#346,#347);
#346 = CARTESIAN_POINT('',(0.,0.));
#347 = VECTOR('',#348,1.);
#348 = DIRECTION('',(1.,0.));
#349 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#350 = PCURVE('',#351,#356);
#351 = CYLINDRICAL_SURFACE('',#352,0.275);
#352 = AXIS2_PLACEMENT_3D('',#353,#354,#355);
#353 = CARTESIAN_POINT('',(51.889186,125.797167,4.274863));
#354 = DIRECTION('',(0.,0.,1.));
#355 = DIRECTION('',(1.,0.,0.));
#356 = DEFINITIONAL_REPRESENTATION('',(#357),#360);
#357 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#358,#359),.UNSPECIFIED.,.F.,.F.,
  (2,2),(0.,6.28318530718),.PIECEWISE_BEZIER_KNOTS.);
#358 = CARTESIAN_POINT('',(0.,0.));
#359 = CARTESIAN_POINT('',(6.28318530718,0.));
#360 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#361 = ADVANCED_FACE('',(#362),#351,.F.);
#362 = FACE_BOUND('',#363,.F.);
#363 = EDGE_LOOP('',(#364,#388,#409,#410));
#364 = ORIENTED_EDGE('',*,*,#365,.F.);
#365 = EDGE_CURVE('',#366,#366,#368,.T.);
#366 = VERTEX_POINT('',#367);
#367 = CARTESIAN_POINT('',(52.164186,125.797167,11.274863));
#368 = SURFACE_CURVE('',#369,(#374,#381),.PCURVE_S1.);
#369 = CIRCLE('',#370,0.275);
#370 = AXIS2_PLACEMENT_3D('',#371,#372,#373);
#371 = CARTESIAN_POINT('',(51.889186,125.797167,11.274863));
#372 = DIRECTION('',(0.,0.,1.));
#373 = DIRECTION('',(1.,0.,0.));
#374 = PCURVE('',#351,#375);
#375 = DEFINITIONAL_REPRESENTATION('',(#376),#380);
#376 = LINE('',#377,#378);
#377 = CARTESIAN_POINT('',(0.,7.));
#378 = VECTOR('',#379,1.);
#379 = DIRECTION('',(1.,0.));
#380 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#381 = PCURVE('',#211,#382);
#382 = DEFINITIONAL_REPRESENTATION('',(#383),#387);
#383 = CIRCLE('',#384,0.275);
#384 = AXIS2_PLACEMENT_2D('',#385,#386);
#385 = CARTESIAN_POINT('',(0.,0.));
#386 = DIRECTION('',(1.,0.));
#387 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#388 = ORIENTED_EDGE('',*,*,#389,.F.);
#389 = EDGE_CURVE('',#165,#366,#390,.T.);
#390 = SEAM_CURVE('',#391,(#395,#402),.PCURVE_S1.);
#391 = LINE('',#392,#393);
#392 = CARTESIAN_POINT('',(52.164186,125.797167,4.274863));
#393 = VECTOR('',#394,1.);
#394 = DIRECTION('',(0.,0.,1.));
#395 = PCURVE('',#351,#396);
#396 = DEFINITIONAL_REPRESENTATION('',(#397),#401);
#397 = LINE('',#398,#399);
#398 = CARTESIAN_POINT('',(6.28318530718,-0.));
#399 = VECTOR('',#400,1.);
#400 = DIRECTION('',(0.,1.));
#401 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#402 = PCURVE('',#351,#403);
#403 = DEFINITIONAL_REPRESENTATION('',(#404),#408);
#404 = LINE('',#405,#406);
#405 = CARTESIAN_POINT('',(0.,-0.));
#406 = VECTOR('',#407,1.);
#407 = DIRECTION('',(0.,1.));
#408 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#409 = ORIENTED_EDGE('',*,*,#336,.T.);
#410 = ORIENTED_EDGE('',*,*,#389,.T.);
#411 = ADVANCED_FACE('',(#412,#456),#211,.T.);
#412 = FACE_BOUND('',#413,.T.);
#413 = EDGE_LOOP('',(#414,#415,#416,#437));
#414 = ORIENTED_EDGE('',*,*,#194,.T.);
#415 = ORIENTED_EDGE('',*,*,#313,.T.);
#416 = ORIENTED_EDGE('',*,*,#417,.F.);
#417 = EDGE_CURVE('',#418,#258,#420,.T.);
#418 = VERTEX_POINT('',#419);
#419 = CARTESIAN_POINT('',(51.008936,121.80569,11.274863));
#420 = SURFACE_CURVE('',#421,(#425,#431),.PCURVE_S1.);
#421 = LINE('',#422,#423);
#422 = CARTESIAN_POINT('',(50.899884484099,123.77095498522,11.274863));
#423 = VECTOR('',#424,1.);
#424 = DIRECTION('',(-5.540424023454E-02,0.998464005442,0.));
#425 = PCURVE('',#211,#426);
#426 = DEFINITIONAL_REPRESENTATION('',(#427),#430);
#427 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#428,#429),.UNSPECIFIED.,.F.,.F.,
  (2,2),(-1.968288265292,6.352421183464),.PIECEWISE_BEZIER_KNOTS.);
#428 = CARTESIAN_POINT('',(-0.88025,-3.991477));
#429 = CARTESIAN_POINT('',(-1.341252585221,4.316451884327));
#430 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#431 = PCURVE('',#301,#432);
#432 = DEFINITIONAL_REPRESENTATION('',(#433),#436);
#433 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#434,#435),.UNSPECIFIED.,.F.,.F.,
  (2,2),(-1.968288265292,6.352421183464),.PIECEWISE_BEZIER_KNOTS.);
#434 = CARTESIAN_POINT('',(0.,-16.725137));
#435 = CARTESIAN_POINT('',(8.320709448756,-16.725137));
#436 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#437 = ORIENTED_EDGE('',*,*,#438,.F.);
#438 = EDGE_CURVE('',#195,#418,#439,.T.);
#439 = SURFACE_CURVE('',#440,(#444,#450),.PCURVE_S1.);
#440 = LINE('',#441,#442);
#441 = CARTESIAN_POINT('',(64.803410714327,121.80640687802,11.274863));
#442 = VECTOR('',#443,1.);
#443 = DIRECTION('',(-0.99999999865,-5.196848995186E-05,0.));
#444 = PCURVE('',#211,#445);
#445 = DEFINITIONAL_REPRESENTATION('',(#446),#449);
#446 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#447,#448),.UNSPECIFIED.,.F.,.F.,
  (2,2),(8.59777284161,13.794474732955),.PIECEWISE_BEZIER_KNOTS.);
#447 = CARTESIAN_POINT('',(4.316451884327,-3.99120693525));
#448 = CARTESIAN_POINT('',(-0.88025,-3.991477));
#449 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#450 = PCURVE('',#245,#451);
#451 = DEFINITIONAL_REPRESENTATION('',(#452),#455);
#452 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#453,#454),.UNSPECIFIED.,.F.,.F.,
  (2,2),(8.59777284161,13.794474732955),.PIECEWISE_BEZIER_KNOTS.);
#453 = CARTESIAN_POINT('',(21.511790144721,-16.725137));
#454 = CARTESIAN_POINT('',(26.708492036066,-16.725137));
#455 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#456 = FACE_BOUND('',#457,.T.);
#457 = EDGE_LOOP('',(#458));
#458 = ORIENTED_EDGE('',*,*,#365,.F.);
#459 = ADVANCED_FACE('',(#460),#301,.F.);
#460 = FACE_BOUND('',#461,.F.);
#461 = EDGE_LOOP('',(#462,#463,#484));
#462 = ORIENTED_EDGE('',*,*,#417,.F.);
#463 = ORIENTED_EDGE('',*,*,#464,.T.);
#464 = EDGE_CURVE('',#418,#224,#465,.T.);
#465 = SURFACE_CURVE('',#466,(#470,#477),.PCURVE_S1.);
#466 = LINE('',#467,#468);
#467 = CARTESIAN_POINT('',(51.008936,121.80569,28.));
#468 = VECTOR('',#469,1.);
#469 = DIRECTION('',(0.,0.,-1.));
#470 = PCURVE('',#301,#471);
#471 = DEFINITIONAL_REPRESENTATION('',(#472),#476);
#472 = LINE('',#473,#474);
#473 = CARTESIAN_POINT('',(0.,0.));
#474 = VECTOR('',#475,1.);
#475 = DIRECTION('',(0.,-1.));
#476 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#477 = PCURVE('',#245,#478);
#478 = DEFINITIONAL_REPRESENTATION('',(#479),#483);
#479 = LINE('',#480,#481);
#480 = CARTESIAN_POINT('',(26.708492036066,0.));
#481 = VECTOR('',#482,1.);
#482 = DIRECTION('',(-0.,-1.));
#483 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#484 = ORIENTED_EDGE('',*,*,#257,.F.);
#485 = ADVANCED_FACE('',(#486),#245,.F.);
#486 = FACE_BOUND('',#487,.F.);
#487 = EDGE_LOOP('',(#488,#489,#490));
#488 = ORIENTED_EDGE('',*,*,#464,.F.);
#489 = ORIENTED_EDGE('',*,*,#438,.F.);
#490 = ORIENTED_EDGE('',*,*,#223,.F.);
#491 = ( GEOMETRIC_REPRESENTATION_CONTEXT(3) 
GLOBAL_UNCERTAINTY_ASSIGNED_CONTEXT((#495)) GLOBAL_UNIT_ASSIGNED_CONTEXT
((#492,#493,#494)) REPRESENTATION_CONTEXT('Context #1',
  '3D Context with UNIT and UNCERTAINTY') );
#492 = ( LENGTH_UNIT() NAMED_UNIT(*) SI_UNIT(.MILLI.,.METRE.) );
#493 = ( NAMED_UNIT(*) PLANE_ANGLE_UNIT() SI_UNIT($,.RADIAN.) );
#494 = ( NAMED_UNIT(*) SI_UNIT($,.STERADIAN.) SOLID_ANGLE_UNIT() );
#495 = UNCERTAINTY_MEASURE_WITH_UNIT(LENGTH_MEASURE(1.E-07),#492,
  'distance_accuracy_value','confusion accuracy');
#496 = CONTEXT_DEPENDENT_SHAPE_REPRESENTATION(#497,#499);
#497 = ( REPRESENTATION_RELATIONSHIP('','',#157,#10) 
REPRESENTATION_RELATIONSHIP_WITH_TRANSFORMATION(#498) 
SHAPE_REPRESENTATION_RELATIONSHIP() );
#498 = ITEM_DEFINED_TRANSFORMATION('','',#11,#19);
#499 = PRODUCT_DEFINITION_SHAPE('Placement','Placement of an item',#500
  );
#500 = NEXT_ASSEMBLY_USAGE_OCCURRENCE('97','=>[0:1:1:3]','',#5,#152,$);
#501 = PRODUCT_RELATED_PRODUCT_CATEGORY('part',$,(#154));
#502 = SHAPE_DEFINITION_REPRESENTATION(#503,#509);
#503 = PRODUCT_DEFINITION_SHAPE('','',#504);
#504 = PRODUCT_DEFINITION('design','',#505,#508);
#505 = PRODUCT_DEFINITION_FORMATION('','',#506);
#506 = PRODUCT('PC1-P6_Part3','PC1-P6_Part3','',(#507));
#507 = PRODUCT_CONTEXT('',#2,'mechanical');
#508 = PRODUCT_DEFINITION_CONTEXT('part definition',#2,'design');
#509 = ADVANCED_BREP_SHAPE_REPRESENTATION('',(#11,#510),#840);
#510 = MANIFOLD_SOLID_BREP('',#511);
#511 = CLOSED_SHELL('',(#512,#634,#709,#756,#806,#813));
#512 = ADVANCED_FACE('',(#513),#528,.T.);
#513 = FACE_BOUND('',#514,.T.);
#514 = EDGE_LOOP('',(#515,#551,#577,#610));
#515 = ORIENTED_EDGE('',*,*,#516,.T.);
#516 = EDGE_CURVE('',#517,#519,#521,.T.);
#517 = VERTEX_POINT('',#518);
#518 = CARTESIAN_POINT('',(53.53263213443,121.80582115267,11.274863));
#519 = VERTEX_POINT('',#520);
#520 = CARTESIAN_POINT('',(50.559579738223,129.90373540419,11.274863));
#521 = SURFACE_CURVE('',#522,(#527,#539),.PCURVE_S1.);
#522 = CIRCLE('',#523,4.316451884327);
#523 = AXIS2_PLACEMENT_3D('',#524,#525,#526);
#524 = CARTESIAN_POINT('',(51.889186,125.797167,11.274863));
#525 = DIRECTION('',(-0.,0.,1.));
#526 = DIRECTION('',(0.380740056526,-0.924682112597,0.));
#527 = PCURVE('',#528,#533);
#528 = CYLINDRICAL_SURFACE('',#529,4.316451884327);
#529 = AXIS2_PLACEMENT_3D('',#530,#531,#532);
#530 = CARTESIAN_POINT('',(51.889186,125.797167,11.274863));
#531 = DIRECTION('',(0.,0.,1.));
#532 = DIRECTION('',(1.,0.,0.));
#533 = DEFINITIONAL_REPRESENTATION('',(#534),#538);
#534 = LINE('',#535,#536);
#535 = CARTESIAN_POINT('',(5.102985481271,0.));
#536 = VECTOR('',#537,1.);
#537 = DIRECTION('',(1.,0.));
#538 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#539 = PCURVE('',#540,#545);
#540 = PLANE('',#541);
#541 = AXIS2_PLACEMENT_3D('',#542,#543,#544);
#542 = CARTESIAN_POINT('',(51.889186,125.797167,11.274863));
#543 = DIRECTION('',(0.,0.,1.));
#544 = DIRECTION('',(1.,0.,0.));
#545 = DEFINITIONAL_REPRESENTATION('',(#546),#550);
#546 = CIRCLE('',#547,4.316451884327);
#547 = AXIS2_PLACEMENT_2D('',#548,#549);
#548 = CARTESIAN_POINT('',(0.,0.));
#549 = DIRECTION('',(0.380740056526,-0.924682112597));
#550 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#551 = ORIENTED_EDGE('',*,*,#552,.T.);
#552 = EDGE_CURVE('',#519,#553,#555,.T.);
#553 = VERTEX_POINT('',#554);
#554 = CARTESIAN_POINT('',(50.559579738223,129.90373540419,18.));
#555 = SURFACE_CURVE('',#556,(#560,#566),.PCURVE_S1.);
#556 = LINE('',#557,#558);
#557 = CARTESIAN_POINT('',(50.559579738223,129.90373540419,11.274863));
#558 = VECTOR('',#559,1.);
#559 = DIRECTION('',(0.,0.,1.));
#560 = PCURVE('',#528,#561);
#561 = DEFINITIONAL_REPRESENTATION('',(#562),#565);
#562 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#563,#564),.UNSPECIFIED.,.F.,.F.,
  (2,2),(0.,6.725137),.PIECEWISE_BEZIER_KNOTS.);
#563 = CARTESIAN_POINT('',(8.167105621515,0.));
#564 = CARTESIAN_POINT('',(8.167105621515,6.725137));
#565 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#566 = PCURVE('',#567,#572);
#567 = PLANE('',#568);
#568 = AXIS2_PLACEMENT_3D('',#569,#570,#571);
#569 = CARTESIAN_POINT('',(51.008936,121.80569,28.));
#570 = DIRECTION('',(0.998464005442,5.540424023454E-02,0.));
#571 = DIRECTION('',(-5.540424023454E-02,0.998464005442,0.));
#572 = DEFINITIONAL_REPRESENTATION('',(#573),#576);
#573 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#574,#575),.UNSPECIFIED.,.F.,.F.,
  (2,2),(-5.999999999062E-07,6.7251376),.PIECEWISE_BEZIER_KNOTS.);
#574 = CARTESIAN_POINT('',(8.110503092808,-16.7251376));
#575 = CARTESIAN_POINT('',(8.110503092808,-9.9999994));
#576 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#577 = ORIENTED_EDGE('',*,*,#578,.T.);
#578 = EDGE_CURVE('',#553,#579,#581,.T.);
#579 = VERTEX_POINT('',#580);
#580 = CARTESIAN_POINT('',(53.53263213443,121.80582115267,18.));
#581 = SURFACE_CURVE('',#582,(#587,#594),.PCURVE_S1.);
#582 = CIRCLE('',#583,4.316451884327);
#583 = AXIS2_PLACEMENT_3D('',#584,#585,#586);
#584 = CARTESIAN_POINT('',(51.889186,125.797167,18.));
#585 = DIRECTION('',(0.,0.,-1.));
#586 = DIRECTION('',(-0.308032221234,0.951375925006,0.));
#587 = PCURVE('',#528,#588);
#588 = DEFINITIONAL_REPRESENTATION('',(#589),#593);
#589 = LINE('',#590,#591);
#590 = CARTESIAN_POINT('',(8.167105621515,6.725137));
#591 = VECTOR('',#592,1.);
#592 = DIRECTION('',(-1.,-0.));
#593 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#594 = PCURVE('',#595,#600);
#595 = PLANE('',#596);
#596 = AXIS2_PLACEMENT_3D('',#597,#598,#599);
#597 = CARTESIAN_POINT('',(51.889186,125.797167,18.));
#598 = DIRECTION('',(0.,0.,1.));
#599 = DIRECTION('',(1.,0.,0.));
#600 = DEFINITIONAL_REPRESENTATION('',(#601),#609);
#601 = ( BOUNDED_CURVE() B_SPLINE_CURVE(2,(#602,#603,#604,#605,#606,#607
,#608),.UNSPECIFIED.,.T.,.F.) B_SPLINE_CURVE_WITH_KNOTS((1,2,2,2,2,1),(
    -2.094395102393,0.,2.094395102393,4.188790204786,6.28318530718,
8.377580409573),.UNSPECIFIED.) CURVE() GEOMETRIC_REPRESENTATION_ITEM() 
RATIONAL_B_SPLINE_CURVE((1.,0.5,1.,0.5,1.,0.5,1.)) REPRESENTATION_ITEM(
  '') );
#602 = CARTESIAN_POINT('',(-1.329606261777,4.106568404198));
#603 = CARTESIAN_POINT('',(5.783178859051,6.409514003657));
#604 = CARTESIAN_POINT('',(4.221195691302,-0.901811402369));
#605 = CARTESIAN_POINT('',(2.659212523554,-8.213136808396));
#606 = CARTESIAN_POINT('',(-2.891589429525,-3.204757001829));
#607 = CARTESIAN_POINT('',(-8.442391382605,1.803622804739));
#608 = CARTESIAN_POINT('',(-1.329606261777,4.106568404198));
#609 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#610 = ORIENTED_EDGE('',*,*,#611,.F.);
#611 = EDGE_CURVE('',#517,#579,#612,.T.);
#612 = SURFACE_CURVE('',#613,(#617,#623),.PCURVE_S1.);
#613 = LINE('',#614,#615);
#614 = CARTESIAN_POINT('',(53.53263213443,121.80582115267,11.274863));
#615 = VECTOR('',#616,1.);
#616 = DIRECTION('',(0.,0.,1.));
#617 = PCURVE('',#528,#618);
#618 = DEFINITIONAL_REPRESENTATION('',(#619),#622);
#619 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#620,#621),.UNSPECIFIED.,.F.,.F.,
  (2,2),(0.,6.725137),.PIECEWISE_BEZIER_KNOTS.);
#620 = CARTESIAN_POINT('',(5.102985481271,0.));
#621 = CARTESIAN_POINT('',(5.102985481271,6.725137));
#622 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#623 = PCURVE('',#624,#629);
#624 = PLANE('',#625);
#625 = AXIS2_PLACEMENT_3D('',#626,#627,#628);
#626 = CARTESIAN_POINT('',(77.717428,121.807078,28.));
#627 = DIRECTION('',(-5.196848995186E-05,0.99999999865,0.));
#628 = DIRECTION('',(-0.99999999865,-5.196848995186E-05,0.));
#629 = DEFINITIONAL_REPRESENTATION('',(#630),#633);
#630 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#631,#632),.UNSPECIFIED.,.F.,.F.,
  (2,2),(-5.999999999062E-07,6.7251376),.PIECEWISE_BEZIER_KNOTS.);
#631 = CARTESIAN_POINT('',(24.184795898228,-16.7251376));
#632 = CARTESIAN_POINT('',(24.184795898228,-9.9999994));
#633 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#634 = ADVANCED_FACE('',(#635,#678),#540,.F.);
#635 = FACE_BOUND('',#636,.F.);
#636 = EDGE_LOOP('',(#637,#638,#659));
#637 = ORIENTED_EDGE('',*,*,#516,.T.);
#638 = ORIENTED_EDGE('',*,*,#639,.F.);
#639 = EDGE_CURVE('',#640,#519,#642,.T.);
#640 = VERTEX_POINT('',#641);
#641 = CARTESIAN_POINT('',(51.008936,121.80569,11.274863));
#642 = SURFACE_CURVE('',#643,(#647,#653),.PCURVE_S1.);
#643 = LINE('',#644,#645);
#644 = CARTESIAN_POINT('',(50.899884484099,123.77095498522,11.274863));
#645 = VECTOR('',#646,1.);
#646 = DIRECTION('',(-5.540424023454E-02,0.998464005442,0.));
#647 = PCURVE('',#540,#648);
#648 = DEFINITIONAL_REPRESENTATION('',(#649),#652);
#649 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#650,#651),.UNSPECIFIED.,.F.,.F.,
  (2,2),(-1.968288265292,6.352421183464),.PIECEWISE_BEZIER_KNOTS.);
#650 = CARTESIAN_POINT('',(-0.88025,-3.991477));
#651 = CARTESIAN_POINT('',(-1.341252585221,4.316451884327));
#652 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#653 = PCURVE('',#567,#654);
#654 = DEFINITIONAL_REPRESENTATION('',(#655),#658);
#655 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#656,#657),.UNSPECIFIED.,.F.,.F.,
  (2,2),(-1.968288265292,6.352421183464),.PIECEWISE_BEZIER_KNOTS.);
#656 = CARTESIAN_POINT('',(0.,-16.725137));
#657 = CARTESIAN_POINT('',(8.320709448756,-16.725137));
#658 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#659 = ORIENTED_EDGE('',*,*,#660,.F.);
#660 = EDGE_CURVE('',#517,#640,#661,.T.);
#661 = SURFACE_CURVE('',#662,(#666,#672),.PCURVE_S1.);
#662 = LINE('',#663,#664);
#663 = CARTESIAN_POINT('',(64.803410714327,121.80640687802,11.274863));
#664 = VECTOR('',#665,1.);
#665 = DIRECTION('',(-0.99999999865,-5.196848995186E-05,0.));
#666 = PCURVE('',#540,#667);
#667 = DEFINITIONAL_REPRESENTATION('',(#668),#671);
#668 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#669,#670),.UNSPECIFIED.,.F.,.F.,
  (2,2),(8.59777284161,13.794474732955),.PIECEWISE_BEZIER_KNOTS.);
#669 = CARTESIAN_POINT('',(4.316451884327,-3.99120693525));
#670 = CARTESIAN_POINT('',(-0.88025,-3.991477));
#671 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#672 = PCURVE('',#624,#673);
#673 = DEFINITIONAL_REPRESENTATION('',(#674),#677);
#674 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#675,#676),.UNSPECIFIED.,.F.,.F.,
  (2,2),(8.59777284161,13.794474732955),.PIECEWISE_BEZIER_KNOTS.);
#675 = CARTESIAN_POINT('',(21.511790144721,-16.725137));
#676 = CARTESIAN_POINT('',(26.708492036066,-16.725137));
#677 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#678 = FACE_BOUND('',#679,.F.);
#679 = EDGE_LOOP('',(#680));
#680 = ORIENTED_EDGE('',*,*,#681,.F.);
#681 = EDGE_CURVE('',#682,#682,#684,.T.);
#682 = VERTEX_POINT('',#683);
#683 = CARTESIAN_POINT('',(52.164186,125.797167,11.274863));
#684 = SURFACE_CURVE('',#685,(#690,#697),.PCURVE_S1.);
#685 = CIRCLE('',#686,0.275);
#686 = AXIS2_PLACEMENT_3D('',#687,#688,#689);
#687 = CARTESIAN_POINT('',(51.889186,125.797167,11.274863));
#688 = DIRECTION('',(0.,0.,1.));
#689 = DIRECTION('',(1.,0.,0.));
#690 = PCURVE('',#540,#691);
#691 = DEFINITIONAL_REPRESENTATION('',(#692),#696);
#692 = CIRCLE('',#693,0.275);
#693 = AXIS2_PLACEMENT_2D('',#694,#695);
#694 = CARTESIAN_POINT('',(0.,0.));
#695 = DIRECTION('',(1.,0.));
#696 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#697 = PCURVE('',#698,#703);
#698 = CYLINDRICAL_SURFACE('',#699,0.275);
#699 = AXIS2_PLACEMENT_3D('',#700,#701,#702);
#700 = CARTESIAN_POINT('',(51.889186,125.797167,11.274863));
#701 = DIRECTION('',(0.,0.,1.));
#702 = DIRECTION('',(1.,0.,0.));
#703 = DEFINITIONAL_REPRESENTATION('',(#704),#708);
#704 = LINE('',#705,#706);
#705 = CARTESIAN_POINT('',(0.,0.));
#706 = VECTOR('',#707,1.);
#707 = DIRECTION('',(1.,0.));
#708 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#709 = ADVANCED_FACE('',(#710),#624,.F.);
#710 = FACE_BOUND('',#711,.F.);
#711 = EDGE_LOOP('',(#712,#735,#754,#755));
#712 = ORIENTED_EDGE('',*,*,#713,.F.);
#713 = EDGE_CURVE('',#714,#640,#716,.T.);
#714 = VERTEX_POINT('',#715);
#715 = CARTESIAN_POINT('',(51.008936,121.80569,18.));
#716 = SURFACE_CURVE('',#717,(#721,#728),.PCURVE_S1.);
#717 = LINE('',#718,#719);
#718 = CARTESIAN_POINT('',(51.008936,121.80569,28.));
#719 = VECTOR('',#720,1.);
#720 = DIRECTION('',(0.,0.,-1.));
#721 = PCURVE('',#624,#722);
#722 = DEFINITIONAL_REPRESENTATION('',(#723),#727);
#723 = LINE('',#724,#725);
#724 = CARTESIAN_POINT('',(26.708492036066,0.));
#725 = VECTOR('',#726,1.);
#726 = DIRECTION('',(-0.,-1.));
#727 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#728 = PCURVE('',#567,#729);
#729 = DEFINITIONAL_REPRESENTATION('',(#730),#734);
#730 = LINE('',#731,#732);
#731 = CARTESIAN_POINT('',(0.,0.));
#732 = VECTOR('',#733,1.);
#733 = DIRECTION('',(0.,-1.));
#734 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#735 = ORIENTED_EDGE('',*,*,#736,.F.);
#736 = EDGE_CURVE('',#579,#714,#737,.T.);
#737 = SURFACE_CURVE('',#738,(#742,#748),.PCURVE_S1.);
#738 = LINE('',#739,#740);
#739 = CARTESIAN_POINT('',(64.803410714327,121.80640687802,18.));
#740 = VECTOR('',#741,1.);
#741 = DIRECTION('',(-0.99999999865,-5.196848995186E-05,0.));
#742 = PCURVE('',#624,#743);
#743 = DEFINITIONAL_REPRESENTATION('',(#744),#747);
#744 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#745,#746),.UNSPECIFIED.,.F.,.F.,
  (2,2),(8.59777284161,13.794474732955),.PIECEWISE_BEZIER_KNOTS.);
#745 = CARTESIAN_POINT('',(21.511790144721,-10.));
#746 = CARTESIAN_POINT('',(26.708492036066,-10.));
#747 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#748 = PCURVE('',#595,#749);
#749 = DEFINITIONAL_REPRESENTATION('',(#750),#753);
#750 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#751,#752),.UNSPECIFIED.,.F.,.F.,
  (2,2),(8.59777284161,13.794474732955),.PIECEWISE_BEZIER_KNOTS.);
#751 = CARTESIAN_POINT('',(4.316451884327,-3.99120693525));
#752 = CARTESIAN_POINT('',(-0.88025,-3.991477));
#753 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#754 = ORIENTED_EDGE('',*,*,#611,.F.);
#755 = ORIENTED_EDGE('',*,*,#660,.T.);
#756 = ADVANCED_FACE('',(#757,#780),#595,.T.);
#757 = FACE_BOUND('',#758,.T.);
#758 = EDGE_LOOP('',(#759,#760,#761));
#759 = ORIENTED_EDGE('',*,*,#736,.F.);
#760 = ORIENTED_EDGE('',*,*,#578,.F.);
#761 = ORIENTED_EDGE('',*,*,#762,.F.);
#762 = EDGE_CURVE('',#714,#553,#763,.T.);
#763 = SURFACE_CURVE('',#764,(#768,#774),.PCURVE_S1.);
#764 = LINE('',#765,#766);
#765 = CARTESIAN_POINT('',(50.899884484099,123.77095498522,18.));
#766 = VECTOR('',#767,1.);
#767 = DIRECTION('',(-5.540424023454E-02,0.998464005442,0.));
#768 = PCURVE('',#595,#769);
#769 = DEFINITIONAL_REPRESENTATION('',(#770),#773);
#770 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#771,#772),.UNSPECIFIED.,.F.,.F.,
  (2,2),(-1.968288265292,6.352421183464),.PIECEWISE_BEZIER_KNOTS.);
#771 = CARTESIAN_POINT('',(-0.88025,-3.991477));
#772 = CARTESIAN_POINT('',(-1.341252585221,4.316451884327));
#773 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#774 = PCURVE('',#567,#775);
#775 = DEFINITIONAL_REPRESENTATION('',(#776),#779);
#776 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#777,#778),.UNSPECIFIED.,.F.,.F.,
  (2,2),(-1.968288265292,6.352421183464),.PIECEWISE_BEZIER_KNOTS.);
#777 = CARTESIAN_POINT('',(0.,-10.));
#778 = CARTESIAN_POINT('',(8.320709448756,-10.));
#779 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#780 = FACE_BOUND('',#781,.T.);
#781 = EDGE_LOOP('',(#782));
#782 = ORIENTED_EDGE('',*,*,#783,.F.);
#783 = EDGE_CURVE('',#784,#784,#786,.T.);
#784 = VERTEX_POINT('',#785);
#785 = CARTESIAN_POINT('',(52.164186,125.797167,18.));
#786 = SURFACE_CURVE('',#787,(#792,#799),.PCURVE_S1.);
#787 = CIRCLE('',#788,0.275);
#788 = AXIS2_PLACEMENT_3D('',#789,#790,#791);
#789 = CARTESIAN_POINT('',(51.889186,125.797167,18.));
#790 = DIRECTION('',(0.,0.,1.));
#791 = DIRECTION('',(1.,0.,0.));
#792 = PCURVE('',#595,#793);
#793 = DEFINITIONAL_REPRESENTATION('',(#794),#798);
#794 = CIRCLE('',#795,0.275);
#795 = AXIS2_PLACEMENT_2D('',#796,#797);
#796 = CARTESIAN_POINT('',(0.,0.));
#797 = DIRECTION('',(1.,0.));
#798 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#799 = PCURVE('',#698,#800);
#800 = DEFINITIONAL_REPRESENTATION('',(#801),#805);
#801 = LINE('',#802,#803);
#802 = CARTESIAN_POINT('',(0.,6.725137));
#803 = VECTOR('',#804,1.);
#804 = DIRECTION('',(1.,0.));
#805 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#806 = ADVANCED_FACE('',(#807),#567,.F.);
#807 = FACE_BOUND('',#808,.F.);
#808 = EDGE_LOOP('',(#809,#810,#811,#812));
#809 = ORIENTED_EDGE('',*,*,#762,.F.);
#810 = ORIENTED_EDGE('',*,*,#713,.T.);
#811 = ORIENTED_EDGE('',*,*,#639,.T.);
#812 = ORIENTED_EDGE('',*,*,#552,.T.);
#813 = ADVANCED_FACE('',(#814),#698,.F.);
#814 = FACE_BOUND('',#815,.F.);
#815 = EDGE_LOOP('',(#816,#817,#838,#839));
#816 = ORIENTED_EDGE('',*,*,#783,.F.);
#817 = ORIENTED_EDGE('',*,*,#818,.F.);
#818 = EDGE_CURVE('',#682,#784,#819,.T.);
#819 = SEAM_CURVE('',#820,(#824,#831),.PCURVE_S1.);
#820 = LINE('',#821,#822);
#821 = CARTESIAN_POINT('',(52.164186,125.797167,11.274863));
#822 = VECTOR('',#823,1.);
#823 = DIRECTION('',(0.,0.,1.));
#824 = PCURVE('',#698,#825);
#825 = DEFINITIONAL_REPRESENTATION('',(#826),#830);
#826 = LINE('',#827,#828);
#827 = CARTESIAN_POINT('',(6.28318530718,-0.));
#828 = VECTOR('',#829,1.);
#829 = DIRECTION('',(0.,1.));
#830 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#831 = PCURVE('',#698,#832);
#832 = DEFINITIONAL_REPRESENTATION('',(#833),#837);
#833 = LINE('',#834,#835);
#834 = CARTESIAN_POINT('',(0.,-0.));
#835 = VECTOR('',#836,1.);
#836 = DIRECTION('',(0.,1.));
#837 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#838 = ORIENTED_EDGE('',*,*,#681,.T.);
#839 = ORIENTED_EDGE('',*,*,#818,.T.);
#840 = ( GEOMETRIC_REPRESENTATION_CONTEXT(3) 
GLOBAL_UNCERTAINTY_ASSIGNED_CONTEXT((#844)) GLOBAL_UNIT_ASSIGNED_CONTEXT
((#841,#842,#843)) REPRESENTATION_CONTEXT('Context #1',
  '3D Context with UNIT and UNCERTAINTY') );
#841 = ( LENGTH_UNIT() NAMED_UNIT(*) SI_UNIT(.MILLI.,.METRE.) );
#842 = ( NAMED_UNIT(*) PLANE_ANGLE_UNIT() SI_UNIT($,.RADIAN.) );
#843 = ( NAMED_UNIT(*) SI_UNIT($,.STERADIAN.) SOLID_ANGLE_UNIT() );
#844 = UNCERTAINTY_MEASURE_WITH_UNIT(LENGTH_MEASURE(1.E-07),#841,
  'distance_accuracy_value','confusion accuracy');
#845 = CONTEXT_DEPENDENT_SHAPE_REPRESENTATION(#846,#848);
#846 = ( REPRESENTATION_RELATIONSHIP('','',#509,#10) 
REPRESENTATION_RELATIONSHIP_WITH_TRANSFORMATION(#847) 
SHAPE_REPRESENTATION_RELATIONSHIP() );
#847 = ITEM_DEFINED_TRANSFORMATION('','',#11,#23);
#848 = PRODUCT_DEFINITION_SHAPE('Placement','Placement of an item',#849
  );
#849 = NEXT_ASSEMBLY_USAGE_OCCURRENCE('98','=>[0:1:1:4]','',#5,#504,$);
#850 = PRODUCT_RELATED_PRODUCT_CATEGORY('part',$,(#506));
#851 = MECHANICAL_DESIGN_GEOMETRIC_PRESENTATION_REPRESENTATION('',(#852)
  ,#139);
#852 = STYLED_ITEM('color',(#853),#41);
#853 = PRESENTATION_STYLE_ASSIGNMENT((#854));
#854 = SURFACE_STYLE_USAGE(.BOTH.,#855);
#855 = SURFACE_SIDE_STYLE('',(#856));
#856 = SURFACE_STYLE_FILL_AREA(#857);
#857 = FILL_AREA_STYLE('',(#858));
#858 = FILL_AREA_STYLE_COLOUR('',#859);
#859 = DRAUGHTING_PRE_DEFINED_COLOUR('blue');
#860 = MECHANICAL_DESIGN_GEOMETRIC_PRESENTATION_REPRESENTATION('',(#861)
  ,#840);
#861 = STYLED_ITEM('color',(#862),#510);
#862 = PRESENTATION_STYLE_ASSIGNMENT((#863));
#863 = SURFACE_STYLE_USAGE(.BOTH.,#864);
#864 = SURFACE_SIDE_STYLE('',(#865));
#865 = SURFACE_STYLE_FILL_AREA(#866);
#866 = FILL_AREA_STYLE('',(#867));
#867 = FILL_AREA_STYLE_COLOUR('',#868);
#868 = DRAUGHTING_PRE_DEFINED_COLOUR('red');
#869 = MECHANICAL_DESIGN_GEOMETRIC_PRESENTATION_REPRESENTATION('',(#870)
  ,#491);
#870 = STYLED_ITEM('color',(#871),#158);
#871 = PRESENTATION_STYLE_ASSIGNMENT((#872));
#872 = SURFACE_STYLE_USAGE(.BOTH.,#873);
#873 = SURFACE_SIDE_STYLE('',(#874));
#874 = SURFACE_STYLE_FILL_AREA(#875);
#875 = FILL_AREA_STYLE('',(#876));
#876 = FILL_AREA_STYLE_COLOUR('',#877);
#877 = DRAUGHTING_PRE_DEFINED_COLOUR('green');
ENDSEC;
END-ISO-10303-21;
