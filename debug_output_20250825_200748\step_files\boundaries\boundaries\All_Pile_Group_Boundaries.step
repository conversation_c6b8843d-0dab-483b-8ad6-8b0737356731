ISO-10303-21;
HEADER;
FILE_DESCRIPTION(('Open CASCADE Model'),'2;1');
FILE_NAME('Pile_Group_Boundaries','2025-08-25T20:07:51',('Author'),(
    'Open CASCADE'),'Open CASCADE STEP processor 7.8','build123d',
  'Unknown');
FILE_SCHEMA(('AUTOMOTIVE_DESIGN { 1 0 10303 214 1 1 1 1 }'));
ENDSEC;
DATA;
#1 = APPLICATION_PROTOCOL_DEFINITION('international standard',
  'automotive_design',2000,#2);
#2 = APPLICATION_CONTEXT(
  'core data for automotive mechanical design processes');
#3 = SHAPE_DEFINITION_REPRESENTATION(#4,#10);
#4 = PRODUCT_DEFINITION_SHAPE('','',#5);
#5 = PRODUCT_DEFINITION('design','',#6,#9);
#6 = PRODUCT_DEFINITION_FORMATION('','',#7);
#7 = PRODUCT('Pile_Group_Boundaries','Pile_Group_Boundaries','',(#8));
#8 = PRODUCT_CONTEXT('',#2,'mechanical');
#9 = PRODUCT_DEFINITION_CONTEXT('part definition',#2,'design');
#10 = SHAPE_REPRESENTATION('',(#11,#15),#19);
#11 = AXIS2_PLACEMENT_3D('',#12,#13,#14);
#12 = CARTESIAN_POINT('',(0.,0.,0.));
#13 = DIRECTION('',(0.,0.,1.));
#14 = DIRECTION('',(1.,0.,-0.));
#15 = AXIS2_PLACEMENT_3D('',#16,#17,#18);
#16 = CARTESIAN_POINT('',(0.,0.,0.));
#17 = DIRECTION('',(0.,0.,1.));
#18 = DIRECTION('',(1.,0.,-0.));
#19 = ( GEOMETRIC_REPRESENTATION_CONTEXT(3) 
GLOBAL_UNCERTAINTY_ASSIGNED_CONTEXT((#23)) GLOBAL_UNIT_ASSIGNED_CONTEXT(
(#20,#21,#22)) REPRESENTATION_CONTEXT('Context #1',
  '3D Context with UNIT and UNCERTAINTY') );
#20 = ( LENGTH_UNIT() NAMED_UNIT(*) SI_UNIT(.MILLI.,.METRE.) );
#21 = ( NAMED_UNIT(*) PLANE_ANGLE_UNIT() SI_UNIT($,.RADIAN.) );
#22 = ( NAMED_UNIT(*) SI_UNIT($,.STERADIAN.) SOLID_ANGLE_UNIT() );
#23 = UNCERTAINTY_MEASURE_WITH_UNIT(LENGTH_MEASURE(1.E-07),#20,
  'distance_accuracy_value','confusion accuracy');
#24 = PRODUCT_RELATED_PRODUCT_CATEGORY('part',$,(#7));
#25 = SHAPE_DEFINITION_REPRESENTATION(#26,#32);
#26 = PRODUCT_DEFINITION_SHAPE('','',#27);
#27 = PRODUCT_DEFINITION('design','',#28,#31);
#28 = PRODUCT_DEFINITION_FORMATION('','',#29);
#29 = PRODUCT('Pile_Group_Boundary_1','Pile_Group_Boundary_1','',(#30));
#30 = PRODUCT_CONTEXT('',#2,'mechanical');
#31 = PRODUCT_DEFINITION_CONTEXT('part definition',#2,'design');
#32 = SHAPE_REPRESENTATION('',(#11,#33),#37);
#33 = AXIS2_PLACEMENT_3D('',#34,#35,#36);
#34 = CARTESIAN_POINT('',(0.,0.,0.));
#35 = DIRECTION('',(0.,0.,1.));
#36 = DIRECTION('',(1.,0.,-0.));
#37 = ( GEOMETRIC_REPRESENTATION_CONTEXT(3) 
GLOBAL_UNCERTAINTY_ASSIGNED_CONTEXT((#41)) GLOBAL_UNIT_ASSIGNED_CONTEXT(
(#38,#39,#40)) REPRESENTATION_CONTEXT('Context #1',
  '3D Context with UNIT and UNCERTAINTY') );
#38 = ( LENGTH_UNIT() NAMED_UNIT(*) SI_UNIT(.MILLI.,.METRE.) );
#39 = ( NAMED_UNIT(*) PLANE_ANGLE_UNIT() SI_UNIT($,.RADIAN.) );
#40 = ( NAMED_UNIT(*) SI_UNIT($,.STERADIAN.) SOLID_ANGLE_UNIT() );
#41 = UNCERTAINTY_MEASURE_WITH_UNIT(LENGTH_MEASURE(1.E-07),#38,
  'distance_accuracy_value','confusion accuracy');
#42 = CONTEXT_DEPENDENT_SHAPE_REPRESENTATION(#43,#45);
#43 = ( REPRESENTATION_RELATIONSHIP('','',#32,#10) 
REPRESENTATION_RELATIONSHIP_WITH_TRANSFORMATION(#44) 
SHAPE_REPRESENTATION_RELATIONSHIP() );
#44 = ITEM_DEFINED_TRANSFORMATION('','',#11,#15);
#45 = PRODUCT_DEFINITION_SHAPE('Placement','Placement of an item',#46);
#46 = NEXT_ASSEMBLY_USAGE_OCCURRENCE('86','=>[0:1:1:2]','',#5,#27,$);
#47 = PRODUCT_RELATED_PRODUCT_CATEGORY('part',$,(#29));
#48 = SHAPE_DEFINITION_REPRESENTATION(#49,#55);
#49 = PRODUCT_DEFINITION_SHAPE('','',#50);
#50 = PRODUCT_DEFINITION('design','',#51,#54);
#51 = PRODUCT_DEFINITION_FORMATION('','',#52);
#52 = PRODUCT('SOLID','SOLID','',(#53));
#53 = PRODUCT_CONTEXT('',#2,'mechanical');
#54 = PRODUCT_DEFINITION_CONTEXT('part definition',#2,'design');
#55 = ADVANCED_BREP_SHAPE_REPRESENTATION('',(#11,#56),#1166);
#56 = MANIFOLD_SOLID_BREP('',#57);
#57 = CLOSED_SHELL('',(#58,#178,#254,#330,#406,#482,#558,#634,#710,#786,
    #862,#938,#1014,#1085,#1132,#1149));
#58 = ADVANCED_FACE('',(#59),#73,.F.);
#59 = FACE_BOUND('',#60,.F.);
#60 = EDGE_LOOP('',(#61,#96,#124,#152));
#61 = ORIENTED_EDGE('',*,*,#62,.T.);
#62 = EDGE_CURVE('',#63,#65,#67,.T.);
#63 = VERTEX_POINT('',#64);
#64 = CARTESIAN_POINT('',(46.945771,143.583803,28.));
#65 = VERTEX_POINT('',#66);
#66 = CARTESIAN_POINT('',(46.945771,143.583803,-13.767115));
#67 = SURFACE_CURVE('',#68,(#72,#84),.PCURVE_S1.);
#68 = LINE('',#69,#70);
#69 = CARTESIAN_POINT('',(46.945771,143.583803,28.));
#70 = VECTOR('',#71,1.);
#71 = DIRECTION('',(0.,0.,-1.));
#72 = PCURVE('',#73,#78);
#73 = PLANE('',#74);
#74 = AXIS2_PLACEMENT_3D('',#75,#76,#77);
#75 = CARTESIAN_POINT('',(46.945771,143.583803,28.));
#76 = DIRECTION('',(0.132487231294,-0.991184712122,0.));
#77 = DIRECTION('',(0.991184712122,0.132487231294,0.));
#78 = DEFINITIONAL_REPRESENTATION('',(#79),#83);
#79 = LINE('',#80,#81);
#80 = CARTESIAN_POINT('',(0.,0.));
#81 = VECTOR('',#82,1.);
#82 = DIRECTION('',(0.,-1.));
#83 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#84 = PCURVE('',#85,#90);
#85 = PLANE('',#86);
#86 = AXIS2_PLACEMENT_3D('',#87,#88,#89);
#87 = CARTESIAN_POINT('',(48.301331,131.800116,28.));
#88 = DIRECTION('',(0.993448200572,0.11428330053,0.));
#89 = DIRECTION('',(-0.11428330053,0.993448200572,0.));
#90 = DEFINITIONAL_REPRESENTATION('',(#91),#95);
#91 = LINE('',#92,#93);
#92 = CARTESIAN_POINT('',(11.861400517121,0.));
#93 = VECTOR('',#94,1.);
#94 = DIRECTION('',(0.,-1.));
#95 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#96 = ORIENTED_EDGE('',*,*,#97,.T.);
#97 = EDGE_CURVE('',#65,#98,#100,.T.);
#98 = VERTEX_POINT('',#99);
#99 = CARTESIAN_POINT('',(71.189538,146.824359,-13.767115));
#100 = SURFACE_CURVE('',#101,(#105,#112),.PCURVE_S1.);
#101 = LINE('',#102,#103);
#102 = CARTESIAN_POINT('',(46.945771,143.583803,-13.767115));
#103 = VECTOR('',#104,1.);
#104 = DIRECTION('',(0.991184712122,0.132487231294,0.));
#105 = PCURVE('',#73,#106);
#106 = DEFINITIONAL_REPRESENTATION('',(#107),#111);
#107 = LINE('',#108,#109);
#108 = CARTESIAN_POINT('',(-7.042790969946E-15,-41.767115));
#109 = VECTOR('',#110,1.);
#110 = DIRECTION('',(1.,0.));
#111 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#112 = PCURVE('',#113,#118);
#113 = PLANE('',#114);
#114 = AXIS2_PLACEMENT_3D('',#115,#116,#117);
#115 = CARTESIAN_POINT('',(80.007160191567,135.247798948,-13.767115));
#116 = DIRECTION('',(0.,0.,-1.));
#117 = DIRECTION('',(-1.,0.,0.));
#118 = DEFINITIONAL_REPRESENTATION('',(#119),#123);
#119 = LINE('',#120,#121);
#120 = CARTESIAN_POINT('',(33.061389191567,8.336004051997));
#121 = VECTOR('',#122,1.);
#122 = DIRECTION('',(-0.991184712122,0.132487231294));
#123 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#124 = ORIENTED_EDGE('',*,*,#125,.F.);
#125 = EDGE_CURVE('',#126,#98,#128,.T.);
#126 = VERTEX_POINT('',#127);
#127 = CARTESIAN_POINT('',(71.189538,146.824359,28.));
#128 = SURFACE_CURVE('',#129,(#133,#140),.PCURVE_S1.);
#129 = LINE('',#130,#131);
#130 = CARTESIAN_POINT('',(71.189538,146.824359,28.));
#131 = VECTOR('',#132,1.);
#132 = DIRECTION('',(0.,0.,-1.));
#133 = PCURVE('',#73,#134);
#134 = DEFINITIONAL_REPRESENTATION('',(#135),#139);
#135 = LINE('',#136,#137);
#136 = CARTESIAN_POINT('',(24.459383506937,0.));
#137 = VECTOR('',#138,1.);
#138 = DIRECTION('',(0.,-1.));
#139 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#140 = PCURVE('',#141,#146);
#141 = PLANE('',#142);
#142 = AXIS2_PLACEMENT_3D('',#143,#144,#145);
#143 = CARTESIAN_POINT('',(71.189538,146.824359,28.));
#144 = DIRECTION('',(0.993746145311,0.111662879591,0.));
#145 = DIRECTION('',(-0.111662879591,0.993746145311,0.));
#146 = DEFINITIONAL_REPRESENTATION('',(#147),#151);
#147 = LINE('',#148,#149);
#148 = CARTESIAN_POINT('',(0.,0.));
#149 = VECTOR('',#150,1.);
#150 = DIRECTION('',(0.,-1.));
#151 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#152 = ORIENTED_EDGE('',*,*,#153,.F.);
#153 = EDGE_CURVE('',#63,#126,#154,.T.);
#154 = SURFACE_CURVE('',#155,(#159,#166),.PCURVE_S1.);
#155 = LINE('',#156,#157);
#156 = CARTESIAN_POINT('',(46.945771,143.583803,28.));
#157 = VECTOR('',#158,1.);
#158 = DIRECTION('',(0.991184712122,0.132487231294,0.));
#159 = PCURVE('',#73,#160);
#160 = DEFINITIONAL_REPRESENTATION('',(#161),#165);
#161 = LINE('',#162,#163);
#162 = CARTESIAN_POINT('',(-7.042790969946E-15,0.));
#163 = VECTOR('',#164,1.);
#164 = DIRECTION('',(1.,0.));
#165 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#166 = PCURVE('',#167,#172);
#167 = PLANE('',#168);
#168 = AXIS2_PLACEMENT_3D('',#169,#170,#171);
#169 = CARTESIAN_POINT('',(80.007160191567,135.247798948,28.));
#170 = DIRECTION('',(0.,0.,-1.));
#171 = DIRECTION('',(-1.,0.,0.));
#172 = DEFINITIONAL_REPRESENTATION('',(#173),#177);
#173 = LINE('',#174,#175);
#174 = CARTESIAN_POINT('',(33.061389191567,8.336004051997));
#175 = VECTOR('',#176,1.);
#176 = DIRECTION('',(-0.991184712122,0.132487231294));
#177 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#178 = ADVANCED_FACE('',(#179),#141,.F.);
#179 = FACE_BOUND('',#180,.F.);
#180 = EDGE_LOOP('',(#181,#182,#205,#233));
#181 = ORIENTED_EDGE('',*,*,#125,.T.);
#182 = ORIENTED_EDGE('',*,*,#183,.T.);
#183 = EDGE_CURVE('',#98,#184,#186,.T.);
#184 = VERTEX_POINT('',#185);
#185 = CARTESIAN_POINT('',(71.017293,148.357257,-13.767115));
#186 = SURFACE_CURVE('',#187,(#191,#198),.PCURVE_S1.);
#187 = LINE('',#188,#189);
#188 = CARTESIAN_POINT('',(71.189538,146.824359,-13.767115));
#189 = VECTOR('',#190,1.);
#190 = DIRECTION('',(-0.111662879591,0.993746145311,0.));
#191 = PCURVE('',#141,#192);
#192 = DEFINITIONAL_REPRESENTATION('',(#193),#197);
#193 = LINE('',#194,#195);
#194 = CARTESIAN_POINT('',(-2.824396418963E-14,-41.767115));
#195 = VECTOR('',#196,1.);
#196 = DIRECTION('',(1.,0.));
#197 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#198 = PCURVE('',#113,#199);
#199 = DEFINITIONAL_REPRESENTATION('',(#200),#204);
#200 = LINE('',#201,#202);
#201 = CARTESIAN_POINT('',(8.817622191567,11.576560051997));
#202 = VECTOR('',#203,1.);
#203 = DIRECTION('',(0.111662879591,0.993746145311));
#204 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#205 = ORIENTED_EDGE('',*,*,#206,.F.);
#206 = EDGE_CURVE('',#207,#184,#209,.T.);
#207 = VERTEX_POINT('',#208);
#208 = CARTESIAN_POINT('',(71.017293,148.357257,28.));
#209 = SURFACE_CURVE('',#210,(#214,#221),.PCURVE_S1.);
#210 = LINE('',#211,#212);
#211 = CARTESIAN_POINT('',(71.017293,148.357257,28.));
#212 = VECTOR('',#213,1.);
#213 = DIRECTION('',(0.,0.,-1.));
#214 = PCURVE('',#141,#215);
#215 = DEFINITIONAL_REPRESENTATION('',(#216),#220);
#216 = LINE('',#217,#218);
#217 = CARTESIAN_POINT('',(1.542544851351,0.));
#218 = VECTOR('',#219,1.);
#219 = DIRECTION('',(0.,-1.));
#220 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#221 = PCURVE('',#222,#227);
#222 = PLANE('',#223);
#223 = AXIS2_PLACEMENT_3D('',#224,#225,#226);
#224 = CARTESIAN_POINT('',(71.017293,148.357257,28.));
#225 = DIRECTION('',(-7.3194503953E-03,-0.999973212464,-0.));
#226 = DIRECTION('',(0.999973212464,-7.3194503953E-03,0.));
#227 = DEFINITIONAL_REPRESENTATION('',(#228),#232);
#228 = LINE('',#229,#230);
#229 = CARTESIAN_POINT('',(0.,0.));
#230 = VECTOR('',#231,1.);
#231 = DIRECTION('',(0.,-1.));
#232 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#233 = ORIENTED_EDGE('',*,*,#234,.F.);
#234 = EDGE_CURVE('',#126,#207,#235,.T.);
#235 = SURFACE_CURVE('',#236,(#240,#247),.PCURVE_S1.);
#236 = LINE('',#237,#238);
#237 = CARTESIAN_POINT('',(71.189538,146.824359,28.));
#238 = VECTOR('',#239,1.);
#239 = DIRECTION('',(-0.111662879591,0.993746145311,0.));
#240 = PCURVE('',#141,#241);
#241 = DEFINITIONAL_REPRESENTATION('',(#242),#246);
#242 = LINE('',#243,#244);
#243 = CARTESIAN_POINT('',(-2.824396418963E-14,0.));
#244 = VECTOR('',#245,1.);
#245 = DIRECTION('',(1.,0.));
#246 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#247 = PCURVE('',#167,#248);
#248 = DEFINITIONAL_REPRESENTATION('',(#249),#253);
#249 = LINE('',#250,#251);
#250 = CARTESIAN_POINT('',(8.817622191567,11.576560051997));
#251 = VECTOR('',#252,1.);
#252 = DIRECTION('',(0.111662879591,0.993746145311));
#253 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#254 = ADVANCED_FACE('',(#255),#222,.F.);
#255 = FACE_BOUND('',#256,.F.);
#256 = EDGE_LOOP('',(#257,#258,#281,#309));
#257 = ORIENTED_EDGE('',*,*,#206,.T.);
#258 = ORIENTED_EDGE('',*,*,#259,.T.);
#259 = EDGE_CURVE('',#184,#260,#262,.T.);
#260 = VERTEX_POINT('',#261);
#261 = CARTESIAN_POINT('',(91.496834,148.207354,-13.767115));
#262 = SURFACE_CURVE('',#263,(#267,#274),.PCURVE_S1.);
#263 = LINE('',#264,#265);
#264 = CARTESIAN_POINT('',(71.017293,148.357257,-13.767115));
#265 = VECTOR('',#266,1.);
#266 = DIRECTION('',(0.999973212464,-7.3194503953E-03,0.));
#267 = PCURVE('',#222,#268);
#268 = DEFINITIONAL_REPRESENTATION('',(#269),#273);
#269 = LINE('',#270,#271);
#270 = CARTESIAN_POINT('',(-1.421047404142E-14,-41.767115));
#271 = VECTOR('',#272,1.);
#272 = DIRECTION('',(1.,0.));
#273 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#274 = PCURVE('',#113,#275);
#275 = DEFINITIONAL_REPRESENTATION('',(#276),#280);
#276 = LINE('',#277,#278);
#277 = CARTESIAN_POINT('',(8.989867191567,13.109458051997));
#278 = VECTOR('',#279,1.);
#279 = DIRECTION('',(-0.999973212464,-7.3194503953E-03));
#280 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#281 = ORIENTED_EDGE('',*,*,#282,.F.);
#282 = EDGE_CURVE('',#283,#260,#285,.T.);
#283 = VERTEX_POINT('',#284);
#284 = CARTESIAN_POINT('',(91.496834,148.207354,28.));
#285 = SURFACE_CURVE('',#286,(#290,#297),.PCURVE_S1.);
#286 = LINE('',#287,#288);
#287 = CARTESIAN_POINT('',(91.496834,148.207354,28.));
#288 = VECTOR('',#289,1.);
#289 = DIRECTION('',(0.,0.,-1.));
#290 = PCURVE('',#222,#291);
#291 = DEFINITIONAL_REPRESENTATION('',(#292),#296);
#292 = LINE('',#293,#294);
#293 = CARTESIAN_POINT('',(20.480089611134,0.));
#294 = VECTOR('',#295,1.);
#295 = DIRECTION('',(0.,-1.));
#296 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#297 = PCURVE('',#298,#303);
#298 = PLANE('',#299);
#299 = AXIS2_PLACEMENT_3D('',#300,#301,#302);
#300 = CARTESIAN_POINT('',(91.496834,148.207354,28.));
#301 = DIRECTION('',(-0.175155373413,-0.984540804215,-0.));
#302 = DIRECTION('',(0.984540804215,-0.175155373413,0.));
#303 = DEFINITIONAL_REPRESENTATION('',(#304),#308);
#304 = LINE('',#305,#306);
#305 = CARTESIAN_POINT('',(0.,0.));
#306 = VECTOR('',#307,1.);
#307 = DIRECTION('',(0.,-1.));
#308 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#309 = ORIENTED_EDGE('',*,*,#310,.F.);
#310 = EDGE_CURVE('',#207,#283,#311,.T.);
#311 = SURFACE_CURVE('',#312,(#316,#323),.PCURVE_S1.);
#312 = LINE('',#313,#314);
#313 = CARTESIAN_POINT('',(71.017293,148.357257,28.));
#314 = VECTOR('',#315,1.);
#315 = DIRECTION('',(0.999973212464,-7.3194503953E-03,0.));
#316 = PCURVE('',#222,#317);
#317 = DEFINITIONAL_REPRESENTATION('',(#318),#322);
#318 = LINE('',#319,#320);
#319 = CARTESIAN_POINT('',(-1.421047404142E-14,0.));
#320 = VECTOR('',#321,1.);
#321 = DIRECTION('',(1.,0.));
#322 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#323 = PCURVE('',#167,#324);
#324 = DEFINITIONAL_REPRESENTATION('',(#325),#329);
#325 = LINE('',#326,#327);
#326 = CARTESIAN_POINT('',(8.989867191567,13.109458051997));
#327 = VECTOR('',#328,1.);
#328 = DIRECTION('',(-0.999973212464,-7.3194503953E-03));
#329 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#330 = ADVANCED_FACE('',(#331),#298,.F.);
#331 = FACE_BOUND('',#332,.F.);
#332 = EDGE_LOOP('',(#333,#334,#357,#385));
#333 = ORIENTED_EDGE('',*,*,#282,.T.);
#334 = ORIENTED_EDGE('',*,*,#335,.T.);
#335 = EDGE_CURVE('',#260,#336,#338,.T.);
#336 = VERTEX_POINT('',#337);
#337 = CARTESIAN_POINT('',(96.153156,147.378968,-13.767115));
#338 = SURFACE_CURVE('',#339,(#343,#350),.PCURVE_S1.);
#339 = LINE('',#340,#341);
#340 = CARTESIAN_POINT('',(91.496834,148.207354,-13.767115));
#341 = VECTOR('',#342,1.);
#342 = DIRECTION('',(0.984540804215,-0.175155373413,0.));
#343 = PCURVE('',#298,#344);
#344 = DEFINITIONAL_REPRESENTATION('',(#345),#349);
#345 = LINE('',#346,#347);
#346 = CARTESIAN_POINT('',(1.399116632989E-14,-41.767115));
#347 = VECTOR('',#348,1.);
#348 = DIRECTION('',(1.,0.));
#349 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#350 = PCURVE('',#113,#351);
#351 = DEFINITIONAL_REPRESENTATION('',(#352),#356);
#352 = LINE('',#353,#354);
#353 = CARTESIAN_POINT('',(-11.48967380843,12.959555051997));
#354 = VECTOR('',#355,1.);
#355 = DIRECTION('',(-0.984540804215,-0.175155373413));
#356 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#357 = ORIENTED_EDGE('',*,*,#358,.F.);
#358 = EDGE_CURVE('',#359,#336,#361,.T.);
#359 = VERTEX_POINT('',#360);
#360 = CARTESIAN_POINT('',(96.153156,147.378968,28.));
#361 = SURFACE_CURVE('',#362,(#366,#373),.PCURVE_S1.);
#362 = LINE('',#363,#364);
#363 = CARTESIAN_POINT('',(96.153156,147.378968,28.));
#364 = VECTOR('',#365,1.);
#365 = DIRECTION('',(0.,0.,-1.));
#366 = PCURVE('',#298,#367);
#367 = DEFINITIONAL_REPRESENTATION('',(#368),#372);
#368 = LINE('',#369,#370);
#369 = CARTESIAN_POINT('',(4.729435265725,0.));
#370 = VECTOR('',#371,1.);
#371 = DIRECTION('',(0.,-1.));
#372 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#373 = PCURVE('',#374,#379);
#374 = PLANE('',#375);
#375 = AXIS2_PLACEMENT_3D('',#376,#377,#378);
#376 = CARTESIAN_POINT('',(96.153156,147.378968,28.));
#377 = DIRECTION('',(-3.993835685532E-03,-0.999992024606,-0.));
#378 = DIRECTION('',(0.999992024606,-3.993835685532E-03,0.));
#379 = DEFINITIONAL_REPRESENTATION('',(#380),#384);
#380 = LINE('',#381,#382);
#381 = CARTESIAN_POINT('',(0.,0.));
#382 = VECTOR('',#383,1.);
#383 = DIRECTION('',(0.,-1.));
#384 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#385 = ORIENTED_EDGE('',*,*,#386,.F.);
#386 = EDGE_CURVE('',#283,#359,#387,.T.);
#387 = SURFACE_CURVE('',#388,(#392,#399),.PCURVE_S1.);
#388 = LINE('',#389,#390);
#389 = CARTESIAN_POINT('',(91.496834,148.207354,28.));
#390 = VECTOR('',#391,1.);
#391 = DIRECTION('',(0.984540804215,-0.175155373413,0.));
#392 = PCURVE('',#298,#393);
#393 = DEFINITIONAL_REPRESENTATION('',(#394),#398);
#394 = LINE('',#395,#396);
#395 = CARTESIAN_POINT('',(1.399116632989E-14,0.));
#396 = VECTOR('',#397,1.);
#397 = DIRECTION('',(1.,0.));
#398 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#399 = PCURVE('',#167,#400);
#400 = DEFINITIONAL_REPRESENTATION('',(#401),#405);
#401 = LINE('',#402,#403);
#402 = CARTESIAN_POINT('',(-11.48967380843,12.959555051997));
#403 = VECTOR('',#404,1.);
#404 = DIRECTION('',(-0.984540804215,-0.175155373413));
#405 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#406 = ADVANCED_FACE('',(#407),#374,.F.);
#407 = FACE_BOUND('',#408,.F.);
#408 = EDGE_LOOP('',(#409,#410,#433,#461));
#409 = ORIENTED_EDGE('',*,*,#358,.T.);
#410 = ORIENTED_EDGE('',*,*,#411,.T.);
#411 = EDGE_CURVE('',#336,#412,#414,.T.);
#412 = VERTEX_POINT('',#413);
#413 = CARTESIAN_POINT('',(112.117381,147.315209,-13.767115));
#414 = SURFACE_CURVE('',#415,(#419,#426),.PCURVE_S1.);
#415 = LINE('',#416,#417);
#416 = CARTESIAN_POINT('',(96.153156,147.378968,-13.767115));
#417 = VECTOR('',#418,1.);
#418 = DIRECTION('',(0.999992024606,-3.993835685532E-03,0.));
#419 = PCURVE('',#374,#420);
#420 = DEFINITIONAL_REPRESENTATION('',(#421),#425);
#421 = LINE('',#422,#423);
#422 = CARTESIAN_POINT('',(1.421074137804E-14,-41.767115));
#423 = VECTOR('',#424,1.);
#424 = DIRECTION('',(1.,0.));
#425 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#426 = PCURVE('',#113,#427);
#427 = DEFINITIONAL_REPRESENTATION('',(#428),#432);
#428 = LINE('',#429,#430);
#429 = CARTESIAN_POINT('',(-16.14599580843,12.131169051997));
#430 = VECTOR('',#431,1.);
#431 = DIRECTION('',(-0.999992024606,-3.993835685532E-03));
#432 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#433 = ORIENTED_EDGE('',*,*,#434,.F.);
#434 = EDGE_CURVE('',#435,#412,#437,.T.);
#435 = VERTEX_POINT('',#436);
#436 = CARTESIAN_POINT('',(112.117381,147.315209,28.));
#437 = SURFACE_CURVE('',#438,(#442,#449),.PCURVE_S1.);
#438 = LINE('',#439,#440);
#439 = CARTESIAN_POINT('',(112.117381,147.315209,28.));
#440 = VECTOR('',#441,1.);
#441 = DIRECTION('',(0.,0.,-1.));
#442 = PCURVE('',#374,#443);
#443 = DEFINITIONAL_REPRESENTATION('',(#444),#448);
#444 = LINE('',#445,#446);
#445 = CARTESIAN_POINT('',(15.964352321992,0.));
#446 = VECTOR('',#447,1.);
#447 = DIRECTION('',(0.,-1.));
#448 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#449 = PCURVE('',#450,#455);
#450 = PLANE('',#451);
#451 = AXIS2_PLACEMENT_3D('',#452,#453,#454);
#452 = CARTESIAN_POINT('',(112.117381,147.315209,28.));
#453 = DIRECTION('',(-0.999997968285,-2.015794036709E-03,-0.));
#454 = DIRECTION('',(2.015794036709E-03,-0.999997968285,0.));
#455 = DEFINITIONAL_REPRESENTATION('',(#456),#460);
#456 = LINE('',#457,#458);
#457 = CARTESIAN_POINT('',(0.,0.));
#458 = VECTOR('',#459,1.);
#459 = DIRECTION('',(0.,-1.));
#460 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#461 = ORIENTED_EDGE('',*,*,#462,.F.);
#462 = EDGE_CURVE('',#359,#435,#463,.T.);
#463 = SURFACE_CURVE('',#464,(#468,#475),.PCURVE_S1.);
#464 = LINE('',#465,#466);
#465 = CARTESIAN_POINT('',(96.153156,147.378968,28.));
#466 = VECTOR('',#467,1.);
#467 = DIRECTION('',(0.999992024606,-3.993835685532E-03,0.));
#468 = PCURVE('',#374,#469);
#469 = DEFINITIONAL_REPRESENTATION('',(#470),#474);
#470 = LINE('',#471,#472);
#471 = CARTESIAN_POINT('',(1.421074137804E-14,0.));
#472 = VECTOR('',#473,1.);
#473 = DIRECTION('',(1.,0.));
#474 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#475 = PCURVE('',#167,#476);
#476 = DEFINITIONAL_REPRESENTATION('',(#477),#481);
#477 = LINE('',#478,#479);
#478 = CARTESIAN_POINT('',(-16.14599580843,12.131169051997));
#479 = VECTOR('',#480,1.);
#480 = DIRECTION('',(-0.999992024606,-3.993835685532E-03));
#481 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#482 = ADVANCED_FACE('',(#483),#450,.F.);
#483 = FACE_BOUND('',#484,.F.);
#484 = EDGE_LOOP('',(#485,#486,#509,#537));
#485 = ORIENTED_EDGE('',*,*,#434,.T.);
#486 = ORIENTED_EDGE('',*,*,#487,.T.);
#487 = EDGE_CURVE('',#412,#488,#490,.T.);
#488 = VERTEX_POINT('',#489);
#489 = CARTESIAN_POINT('',(112.160196,126.075483,-13.767115));
#490 = SURFACE_CURVE('',#491,(#495,#502),.PCURVE_S1.);
#491 = LINE('',#492,#493);
#492 = CARTESIAN_POINT('',(112.117381,147.315209,-13.767115));
#493 = VECTOR('',#494,1.);
#494 = DIRECTION('',(2.015794036709E-03,-0.999997968285,0.));
#495 = PCURVE('',#450,#496);
#496 = DEFINITIONAL_REPRESENTATION('',(#497),#501);
#497 = LINE('',#498,#499);
#498 = CARTESIAN_POINT('',(2.842165168559E-14,-41.767115));
#499 = VECTOR('',#500,1.);
#500 = DIRECTION('',(1.,0.));
#501 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#502 = PCURVE('',#113,#503);
#503 = DEFINITIONAL_REPRESENTATION('',(#504),#508);
#504 = LINE('',#505,#506);
#505 = CARTESIAN_POINT('',(-32.11022080843,12.067410051997));
#506 = VECTOR('',#507,1.);
#507 = DIRECTION('',(-2.015794036709E-03,-0.999997968285));
#508 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#509 = ORIENTED_EDGE('',*,*,#510,.F.);
#510 = EDGE_CURVE('',#511,#488,#513,.T.);
#511 = VERTEX_POINT('',#512);
#512 = CARTESIAN_POINT('',(112.160196,126.075483,28.));
#513 = SURFACE_CURVE('',#514,(#518,#525),.PCURVE_S1.);
#514 = LINE('',#515,#516);
#515 = CARTESIAN_POINT('',(112.160196,126.075483,28.));
#516 = VECTOR('',#517,1.);
#517 = DIRECTION('',(0.,0.,-1.));
#518 = PCURVE('',#450,#519);
#519 = DEFINITIONAL_REPRESENTATION('',(#520),#524);
#520 = LINE('',#521,#522);
#521 = CARTESIAN_POINT('',(21.239769153155,0.));
#522 = VECTOR('',#523,1.);
#523 = DIRECTION('',(0.,-1.));
#524 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#525 = PCURVE('',#526,#531);
#526 = PLANE('',#527);
#527 = AXIS2_PLACEMENT_3D('',#528,#529,#530);
#528 = CARTESIAN_POINT('',(112.160196,126.075483,28.));
#529 = DIRECTION('',(0.,1.,0.));
#530 = DIRECTION('',(-1.,0.,0.));
#531 = DEFINITIONAL_REPRESENTATION('',(#532),#536);
#532 = LINE('',#533,#534);
#533 = CARTESIAN_POINT('',(0.,0.));
#534 = VECTOR('',#535,1.);
#535 = DIRECTION('',(0.,-1.));
#536 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#537 = ORIENTED_EDGE('',*,*,#538,.F.);
#538 = EDGE_CURVE('',#435,#511,#539,.T.);
#539 = SURFACE_CURVE('',#540,(#544,#551),.PCURVE_S1.);
#540 = LINE('',#541,#542);
#541 = CARTESIAN_POINT('',(112.117381,147.315209,28.));
#542 = VECTOR('',#543,1.);
#543 = DIRECTION('',(2.015794036709E-03,-0.999997968285,0.));
#544 = PCURVE('',#450,#545);
#545 = DEFINITIONAL_REPRESENTATION('',(#546),#550);
#546 = LINE('',#547,#548);
#547 = CARTESIAN_POINT('',(2.842165168559E-14,0.));
#548 = VECTOR('',#549,1.);
#549 = DIRECTION('',(1.,0.));
#550 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#551 = PCURVE('',#167,#552);
#552 = DEFINITIONAL_REPRESENTATION('',(#553),#557);
#553 = LINE('',#554,#555);
#554 = CARTESIAN_POINT('',(-32.11022080843,12.067410051997));
#555 = VECTOR('',#556,1.);
#556 = DIRECTION('',(-2.015794036709E-03,-0.999997968285));
#557 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#558 = ADVANCED_FACE('',(#559),#526,.F.);
#559 = FACE_BOUND('',#560,.F.);
#560 = EDGE_LOOP('',(#561,#562,#585,#613));
#561 = ORIENTED_EDGE('',*,*,#510,.T.);
#562 = ORIENTED_EDGE('',*,*,#563,.T.);
#563 = EDGE_CURVE('',#488,#564,#566,.T.);
#564 = VERTEX_POINT('',#565);
#565 = CARTESIAN_POINT('',(85.917422,126.075483,-13.767115));
#566 = SURFACE_CURVE('',#567,(#571,#578),.PCURVE_S1.);
#567 = LINE('',#568,#569);
#568 = CARTESIAN_POINT('',(112.160196,126.075483,-13.767115));
#569 = VECTOR('',#570,1.);
#570 = DIRECTION('',(-1.,0.,0.));
#571 = PCURVE('',#526,#572);
#572 = DEFINITIONAL_REPRESENTATION('',(#573),#577);
#573 = LINE('',#574,#575);
#574 = CARTESIAN_POINT('',(0.,-41.767115));
#575 = VECTOR('',#576,1.);
#576 = DIRECTION('',(1.,0.));
#577 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#578 = PCURVE('',#113,#579);
#579 = DEFINITIONAL_REPRESENTATION('',(#580),#584);
#580 = LINE('',#581,#582);
#581 = CARTESIAN_POINT('',(-32.15303580843,-9.172315948003));
#582 = VECTOR('',#583,1.);
#583 = DIRECTION('',(1.,0.));
#584 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#585 = ORIENTED_EDGE('',*,*,#586,.F.);
#586 = EDGE_CURVE('',#587,#564,#589,.T.);
#587 = VERTEX_POINT('',#588);
#588 = CARTESIAN_POINT('',(85.917422,126.075483,28.));
#589 = SURFACE_CURVE('',#590,(#594,#601),.PCURVE_S1.);
#590 = LINE('',#591,#592);
#591 = CARTESIAN_POINT('',(85.917422,126.075483,28.));
#592 = VECTOR('',#593,1.);
#593 = DIRECTION('',(0.,0.,-1.));
#594 = PCURVE('',#526,#595);
#595 = DEFINITIONAL_REPRESENTATION('',(#596),#600);
#596 = LINE('',#597,#598);
#597 = CARTESIAN_POINT('',(26.242774,0.));
#598 = VECTOR('',#599,1.);
#599 = DIRECTION('',(0.,-1.));
#600 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#601 = PCURVE('',#602,#607);
#602 = PLANE('',#603);
#603 = AXIS2_PLACEMENT_3D('',#604,#605,#606);
#604 = CARTESIAN_POINT('',(85.917422,126.075483,28.));
#605 = DIRECTION('',(-1.,-0.,-0.));
#606 = DIRECTION('',(0.,-1.,0.));
#607 = DEFINITIONAL_REPRESENTATION('',(#608),#612);
#608 = LINE('',#609,#610);
#609 = CARTESIAN_POINT('',(0.,0.));
#610 = VECTOR('',#611,1.);
#611 = DIRECTION('',(0.,-1.));
#612 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#613 = ORIENTED_EDGE('',*,*,#614,.F.);
#614 = EDGE_CURVE('',#511,#587,#615,.T.);
#615 = SURFACE_CURVE('',#616,(#620,#627),.PCURVE_S1.);
#616 = LINE('',#617,#618);
#617 = CARTESIAN_POINT('',(112.160196,126.075483,28.));
#618 = VECTOR('',#619,1.);
#619 = DIRECTION('',(-1.,0.,0.));
#620 = PCURVE('',#526,#621);
#621 = DEFINITIONAL_REPRESENTATION('',(#622),#626);
#622 = LINE('',#623,#624);
#623 = CARTESIAN_POINT('',(0.,0.));
#624 = VECTOR('',#625,1.);
#625 = DIRECTION('',(1.,0.));
#626 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#627 = PCURVE('',#167,#628);
#628 = DEFINITIONAL_REPRESENTATION('',(#629),#633);
#629 = LINE('',#630,#631);
#630 = CARTESIAN_POINT('',(-32.15303580843,-9.172315948003));
#631 = VECTOR('',#632,1.);
#632 = DIRECTION('',(1.,0.));
#633 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#634 = ADVANCED_FACE('',(#635),#602,.F.);
#635 = FACE_BOUND('',#636,.F.);
#636 = EDGE_LOOP('',(#637,#638,#661,#689));
#637 = ORIENTED_EDGE('',*,*,#586,.T.);
#638 = ORIENTED_EDGE('',*,*,#639,.T.);
#639 = EDGE_CURVE('',#564,#640,#642,.T.);
#640 = VERTEX_POINT('',#641);
#641 = CARTESIAN_POINT('',(85.917422,122.856505,-13.767115));
#642 = SURFACE_CURVE('',#643,(#647,#654),.PCURVE_S1.);
#643 = LINE('',#644,#645);
#644 = CARTESIAN_POINT('',(85.917422,126.075483,-13.767115));
#645 = VECTOR('',#646,1.);
#646 = DIRECTION('',(0.,-1.,0.));
#647 = PCURVE('',#602,#648);
#648 = DEFINITIONAL_REPRESENTATION('',(#649),#653);
#649 = LINE('',#650,#651);
#650 = CARTESIAN_POINT('',(-1.42108547152E-14,-41.767115));
#651 = VECTOR('',#652,1.);
#652 = DIRECTION('',(1.,0.));
#653 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#654 = PCURVE('',#113,#655);
#655 = DEFINITIONAL_REPRESENTATION('',(#656),#660);
#656 = LINE('',#657,#658);
#657 = CARTESIAN_POINT('',(-5.910261808433,-9.172315948003));
#658 = VECTOR('',#659,1.);
#659 = DIRECTION('',(0.,-1.));
#660 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#661 = ORIENTED_EDGE('',*,*,#662,.F.);
#662 = EDGE_CURVE('',#663,#640,#665,.T.);
#663 = VERTEX_POINT('',#664);
#664 = CARTESIAN_POINT('',(85.917422,122.856505,28.));
#665 = SURFACE_CURVE('',#666,(#670,#677),.PCURVE_S1.);
#666 = LINE('',#667,#668);
#667 = CARTESIAN_POINT('',(85.917422,122.856505,28.));
#668 = VECTOR('',#669,1.);
#669 = DIRECTION('',(0.,0.,-1.));
#670 = PCURVE('',#602,#671);
#671 = DEFINITIONAL_REPRESENTATION('',(#672),#676);
#672 = LINE('',#673,#674);
#673 = CARTESIAN_POINT('',(3.218978,0.));
#674 = VECTOR('',#675,1.);
#675 = DIRECTION('',(0.,-1.));
#676 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#677 = PCURVE('',#678,#683);
#678 = PLANE('',#679);
#679 = AXIS2_PLACEMENT_3D('',#680,#681,#682);
#680 = CARTESIAN_POINT('',(85.917422,122.856505,28.));
#681 = DIRECTION('',(0.,1.,0.));
#682 = DIRECTION('',(-1.,0.,0.));
#683 = DEFINITIONAL_REPRESENTATION('',(#684),#688);
#684 = LINE('',#685,#686);
#685 = CARTESIAN_POINT('',(0.,0.));
#686 = VECTOR('',#687,1.);
#687 = DIRECTION('',(0.,-1.));
#688 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#689 = ORIENTED_EDGE('',*,*,#690,.F.);
#690 = EDGE_CURVE('',#587,#663,#691,.T.);
#691 = SURFACE_CURVE('',#692,(#696,#703),.PCURVE_S1.);
#692 = LINE('',#693,#694);
#693 = CARTESIAN_POINT('',(85.917422,126.075483,28.));
#694 = VECTOR('',#695,1.);
#695 = DIRECTION('',(0.,-1.,0.));
#696 = PCURVE('',#602,#697);
#697 = DEFINITIONAL_REPRESENTATION('',(#698),#702);
#698 = LINE('',#699,#700);
#699 = CARTESIAN_POINT('',(-1.42108547152E-14,0.));
#700 = VECTOR('',#701,1.);
#701 = DIRECTION('',(1.,0.));
#702 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#703 = PCURVE('',#167,#704);
#704 = DEFINITIONAL_REPRESENTATION('',(#705),#709);
#705 = LINE('',#706,#707);
#706 = CARTESIAN_POINT('',(-5.910261808433,-9.172315948003));
#707 = VECTOR('',#708,1.);
#708 = DIRECTION('',(0.,-1.));
#709 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#710 = ADVANCED_FACE('',(#711),#678,.F.);
#711 = FACE_BOUND('',#712,.F.);
#712 = EDGE_LOOP('',(#713,#714,#737,#765));
#713 = ORIENTED_EDGE('',*,*,#662,.T.);
#714 = ORIENTED_EDGE('',*,*,#715,.T.);
#715 = EDGE_CURVE('',#640,#716,#718,.T.);
#716 = VERTEX_POINT('',#717);
#717 = CARTESIAN_POINT('',(77.717435,122.856505,-13.767115));
#718 = SURFACE_CURVE('',#719,(#723,#730),.PCURVE_S1.);
#719 = LINE('',#720,#721);
#720 = CARTESIAN_POINT('',(85.917422,122.856505,-13.767115));
#721 = VECTOR('',#722,1.);
#722 = DIRECTION('',(-1.,0.,0.));
#723 = PCURVE('',#678,#724);
#724 = DEFINITIONAL_REPRESENTATION('',(#725),#729);
#725 = LINE('',#726,#727);
#726 = CARTESIAN_POINT('',(0.,-41.767115));
#727 = VECTOR('',#728,1.);
#728 = DIRECTION('',(1.,0.));
#729 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#730 = PCURVE('',#113,#731);
#731 = DEFINITIONAL_REPRESENTATION('',(#732),#736);
#732 = LINE('',#733,#734);
#733 = CARTESIAN_POINT('',(-5.910261808433,-12.391293948));
#734 = VECTOR('',#735,1.);
#735 = DIRECTION('',(1.,0.));
#736 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#737 = ORIENTED_EDGE('',*,*,#738,.F.);
#738 = EDGE_CURVE('',#739,#716,#741,.T.);
#739 = VERTEX_POINT('',#740);
#740 = CARTESIAN_POINT('',(77.717435,122.856505,28.));
#741 = SURFACE_CURVE('',#742,(#746,#753),.PCURVE_S1.);
#742 = LINE('',#743,#744);
#743 = CARTESIAN_POINT('',(77.717435,122.856505,28.));
#744 = VECTOR('',#745,1.);
#745 = DIRECTION('',(0.,0.,-1.));
#746 = PCURVE('',#678,#747);
#747 = DEFINITIONAL_REPRESENTATION('',(#748),#752);
#748 = LINE('',#749,#750);
#749 = CARTESIAN_POINT('',(8.199987,0.));
#750 = VECTOR('',#751,1.);
#751 = DIRECTION('',(0.,-1.));
#752 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#753 = PCURVE('',#754,#759);
#754 = PLANE('',#755);
#755 = AXIS2_PLACEMENT_3D('',#756,#757,#758);
#756 = CARTESIAN_POINT('',(77.717435,122.856505,28.));
#757 = DIRECTION('',(-0.999999999978,6.670306744902E-06,0.));
#758 = DIRECTION('',(-6.670306744902E-06,-0.999999999978,0.));
#759 = DEFINITIONAL_REPRESENTATION('',(#760),#764);
#760 = LINE('',#761,#762);
#761 = CARTESIAN_POINT('',(0.,0.));
#762 = VECTOR('',#763,1.);
#763 = DIRECTION('',(-0.,-1.));
#764 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#765 = ORIENTED_EDGE('',*,*,#766,.F.);
#766 = EDGE_CURVE('',#663,#739,#767,.T.);
#767 = SURFACE_CURVE('',#768,(#772,#779),.PCURVE_S1.);
#768 = LINE('',#769,#770);
#769 = CARTESIAN_POINT('',(85.917422,122.856505,28.));
#770 = VECTOR('',#771,1.);
#771 = DIRECTION('',(-1.,0.,0.));
#772 = PCURVE('',#678,#773);
#773 = DEFINITIONAL_REPRESENTATION('',(#774),#778);
#774 = LINE('',#775,#776);
#775 = CARTESIAN_POINT('',(0.,0.));
#776 = VECTOR('',#777,1.);
#777 = DIRECTION('',(1.,0.));
#778 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#779 = PCURVE('',#167,#780);
#780 = DEFINITIONAL_REPRESENTATION('',(#781),#785);
#781 = LINE('',#782,#783);
#782 = CARTESIAN_POINT('',(-5.910261808433,-12.391293948));
#783 = VECTOR('',#784,1.);
#784 = DIRECTION('',(1.,0.));
#785 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#786 = ADVANCED_FACE('',(#787),#754,.F.);
#787 = FACE_BOUND('',#788,.F.);
#788 = EDGE_LOOP('',(#789,#790,#813,#841));
#789 = ORIENTED_EDGE('',*,*,#738,.T.);
#790 = ORIENTED_EDGE('',*,*,#791,.T.);
#791 = EDGE_CURVE('',#716,#792,#794,.T.);
#792 = VERTEX_POINT('',#793);
#793 = CARTESIAN_POINT('',(77.717428,121.807078,-13.767115));
#794 = SURFACE_CURVE('',#795,(#799,#806),.PCURVE_S1.);
#795 = LINE('',#796,#797);
#796 = CARTESIAN_POINT('',(77.717435,122.856505,-13.767115));
#797 = VECTOR('',#798,1.);
#798 = DIRECTION('',(-6.670306744902E-06,-0.999999999978,0.));
#799 = PCURVE('',#754,#800);
#800 = DEFINITIONAL_REPRESENTATION('',(#801),#805);
#801 = LINE('',#802,#803);
#802 = CARTESIAN_POINT('',(2.842170942977E-14,-41.767115));
#803 = VECTOR('',#804,1.);
#804 = DIRECTION('',(1.,0.));
#805 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#806 = PCURVE('',#113,#807);
#807 = DEFINITIONAL_REPRESENTATION('',(#808),#812);
#808 = LINE('',#809,#810);
#809 = CARTESIAN_POINT('',(2.289725191567,-12.391293948));
#810 = VECTOR('',#811,1.);
#811 = DIRECTION('',(6.670306744902E-06,-0.999999999978));
#812 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#813 = ORIENTED_EDGE('',*,*,#814,.F.);
#814 = EDGE_CURVE('',#815,#792,#817,.T.);
#815 = VERTEX_POINT('',#816);
#816 = CARTESIAN_POINT('',(77.717428,121.807078,28.));
#817 = SURFACE_CURVE('',#818,(#822,#829),.PCURVE_S1.);
#818 = LINE('',#819,#820);
#819 = CARTESIAN_POINT('',(77.717428,121.807078,28.));
#820 = VECTOR('',#821,1.);
#821 = DIRECTION('',(0.,0.,-1.));
#822 = PCURVE('',#754,#823);
#823 = DEFINITIONAL_REPRESENTATION('',(#824),#828);
#824 = LINE('',#825,#826);
#825 = CARTESIAN_POINT('',(1.049427000023,0.));
#826 = VECTOR('',#827,1.);
#827 = DIRECTION('',(-0.,-1.));
#828 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#829 = PCURVE('',#830,#835);
#830 = PLANE('',#831);
#831 = AXIS2_PLACEMENT_3D('',#832,#833,#834);
#832 = CARTESIAN_POINT('',(77.717428,121.807078,28.));
#833 = DIRECTION('',(-5.196848995186E-05,0.99999999865,0.));
#834 = DIRECTION('',(-0.99999999865,-5.196848995186E-05,0.));
#835 = DEFINITIONAL_REPRESENTATION('',(#836),#840);
#836 = LINE('',#837,#838);
#837 = CARTESIAN_POINT('',(0.,0.));
#838 = VECTOR('',#839,1.);
#839 = DIRECTION('',(-0.,-1.));
#840 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#841 = ORIENTED_EDGE('',*,*,#842,.F.);
#842 = EDGE_CURVE('',#739,#815,#843,.T.);
#843 = SURFACE_CURVE('',#844,(#848,#855),.PCURVE_S1.);
#844 = LINE('',#845,#846);
#845 = CARTESIAN_POINT('',(77.717435,122.856505,28.));
#846 = VECTOR('',#847,1.);
#847 = DIRECTION('',(-6.670306744902E-06,-0.999999999978,0.));
#848 = PCURVE('',#754,#849);
#849 = DEFINITIONAL_REPRESENTATION('',(#850),#854);
#850 = LINE('',#851,#852);
#851 = CARTESIAN_POINT('',(2.842170942977E-14,0.));
#852 = VECTOR('',#853,1.);
#853 = DIRECTION('',(1.,0.));
#854 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#855 = PCURVE('',#167,#856);
#856 = DEFINITIONAL_REPRESENTATION('',(#857),#861);
#857 = LINE('',#858,#859);
#858 = CARTESIAN_POINT('',(2.289725191567,-12.391293948));
#859 = VECTOR('',#860,1.);
#860 = DIRECTION('',(6.670306744902E-06,-0.999999999978));
#861 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#862 = ADVANCED_FACE('',(#863),#830,.F.);
#863 = FACE_BOUND('',#864,.F.);
#864 = EDGE_LOOP('',(#865,#866,#889,#917));
#865 = ORIENTED_EDGE('',*,*,#814,.T.);
#866 = ORIENTED_EDGE('',*,*,#867,.T.);
#867 = EDGE_CURVE('',#792,#868,#870,.T.);
#868 = VERTEX_POINT('',#869);
#869 = CARTESIAN_POINT('',(51.008936,121.80569,-13.767115));
#870 = SURFACE_CURVE('',#871,(#875,#882),.PCURVE_S1.);
#871 = LINE('',#872,#873);
#872 = CARTESIAN_POINT('',(77.717428,121.807078,-13.767115));
#873 = VECTOR('',#874,1.);
#874 = DIRECTION('',(-0.99999999865,-5.196848995186E-05,0.));
#875 = PCURVE('',#830,#876);
#876 = DEFINITIONAL_REPRESENTATION('',(#877),#881);
#877 = LINE('',#878,#879);
#878 = CARTESIAN_POINT('',(-0.,-41.767115));
#879 = VECTOR('',#880,1.);
#880 = DIRECTION('',(1.,0.));
#881 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#882 = PCURVE('',#113,#883);
#883 = DEFINITIONAL_REPRESENTATION('',(#884),#888);
#884 = LINE('',#885,#886);
#885 = CARTESIAN_POINT('',(2.289732191567,-13.440720948));
#886 = VECTOR('',#887,1.);
#887 = DIRECTION('',(0.99999999865,-5.196848995186E-05));
#888 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#889 = ORIENTED_EDGE('',*,*,#890,.F.);
#890 = EDGE_CURVE('',#891,#868,#893,.T.);
#891 = VERTEX_POINT('',#892);
#892 = CARTESIAN_POINT('',(51.008936,121.80569,28.));
#893 = SURFACE_CURVE('',#894,(#898,#905),.PCURVE_S1.);
#894 = LINE('',#895,#896);
#895 = CARTESIAN_POINT('',(51.008936,121.80569,28.));
#896 = VECTOR('',#897,1.);
#897 = DIRECTION('',(0.,0.,-1.));
#898 = PCURVE('',#830,#899);
#899 = DEFINITIONAL_REPRESENTATION('',(#900),#904);
#900 = LINE('',#901,#902);
#901 = CARTESIAN_POINT('',(26.708492036066,0.));
#902 = VECTOR('',#903,1.);
#903 = DIRECTION('',(-0.,-1.));
#904 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#905 = PCURVE('',#906,#911);
#906 = PLANE('',#907);
#907 = AXIS2_PLACEMENT_3D('',#908,#909,#910);
#908 = CARTESIAN_POINT('',(51.008936,121.80569,28.));
#909 = DIRECTION('',(0.998464005442,5.540424023454E-02,0.));
#910 = DIRECTION('',(-5.540424023454E-02,0.998464005442,0.));
#911 = DEFINITIONAL_REPRESENTATION('',(#912),#916);
#912 = LINE('',#913,#914);
#913 = CARTESIAN_POINT('',(0.,0.));
#914 = VECTOR('',#915,1.);
#915 = DIRECTION('',(0.,-1.));
#916 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#917 = ORIENTED_EDGE('',*,*,#918,.F.);
#918 = EDGE_CURVE('',#815,#891,#919,.T.);
#919 = SURFACE_CURVE('',#920,(#924,#931),.PCURVE_S1.);
#920 = LINE('',#921,#922);
#921 = CARTESIAN_POINT('',(77.717428,121.807078,28.));
#922 = VECTOR('',#923,1.);
#923 = DIRECTION('',(-0.99999999865,-5.196848995186E-05,0.));
#924 = PCURVE('',#830,#925);
#925 = DEFINITIONAL_REPRESENTATION('',(#926),#930);
#926 = LINE('',#927,#928);
#927 = CARTESIAN_POINT('',(0.,0.));
#928 = VECTOR('',#929,1.);
#929 = DIRECTION('',(1.,0.));
#930 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#931 = PCURVE('',#167,#932);
#932 = DEFINITIONAL_REPRESENTATION('',(#933),#937);
#933 = LINE('',#934,#935);
#934 = CARTESIAN_POINT('',(2.289732191567,-13.440720948));
#935 = VECTOR('',#936,1.);
#936 = DIRECTION('',(0.99999999865,-5.196848995186E-05));
#937 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#938 = ADVANCED_FACE('',(#939),#906,.F.);
#939 = FACE_BOUND('',#940,.F.);
#940 = EDGE_LOOP('',(#941,#942,#965,#993));
#941 = ORIENTED_EDGE('',*,*,#890,.T.);
#942 = ORIENTED_EDGE('',*,*,#943,.T.);
#943 = EDGE_CURVE('',#868,#944,#946,.T.);
#944 = VERTEX_POINT('',#945);
#945 = CARTESIAN_POINT('',(50.449147,131.893892,-13.767115));
#946 = SURFACE_CURVE('',#947,(#951,#958),.PCURVE_S1.);
#947 = LINE('',#948,#949);
#948 = CARTESIAN_POINT('',(51.008936,121.80569,-13.767115));
#949 = VECTOR('',#950,1.);
#950 = DIRECTION('',(-5.540424023454E-02,0.998464005442,0.));
#951 = PCURVE('',#906,#952);
#952 = DEFINITIONAL_REPRESENTATION('',(#953),#957);
#953 = LINE('',#954,#955);
#954 = CARTESIAN_POINT('',(2.83780538394E-14,-41.767115));
#955 = VECTOR('',#956,1.);
#956 = DIRECTION('',(1.,0.));
#957 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#958 = PCURVE('',#113,#959);
#959 = DEFINITIONAL_REPRESENTATION('',(#960),#964);
#960 = LINE('',#961,#962);
#961 = CARTESIAN_POINT('',(28.998224191567,-13.442108948));
#962 = VECTOR('',#963,1.);
#963 = DIRECTION('',(5.540424023454E-02,0.998464005442));
#964 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#965 = ORIENTED_EDGE('',*,*,#966,.F.);
#966 = EDGE_CURVE('',#967,#944,#969,.T.);
#967 = VERTEX_POINT('',#968);
#968 = CARTESIAN_POINT('',(50.449147,131.893892,28.));
#969 = SURFACE_CURVE('',#970,(#974,#981),.PCURVE_S1.);
#970 = LINE('',#971,#972);
#971 = CARTESIAN_POINT('',(50.449147,131.893892,28.));
#972 = VECTOR('',#973,1.);
#973 = DIRECTION('',(0.,0.,-1.));
#974 = PCURVE('',#906,#975);
#975 = DEFINITIONAL_REPRESENTATION('',(#976),#980);
#976 = LINE('',#977,#978);
#977 = CARTESIAN_POINT('',(10.103721260868,0.));
#978 = VECTOR('',#979,1.);
#979 = DIRECTION('',(0.,-1.));
#980 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#981 = PCURVE('',#982,#987);
#982 = PLANE('',#983);
#983 = AXIS2_PLACEMENT_3D('',#984,#985,#986);
#984 = CARTESIAN_POINT('',(50.449147,131.893892,28.));
#985 = DIRECTION('',(-4.361953975718E-02,0.999048214928,0.));
#986 = DIRECTION('',(-0.999048214928,-4.361953975718E-02,0.));
#987 = DEFINITIONAL_REPRESENTATION('',(#988),#992);
#988 = LINE('',#989,#990);
#989 = CARTESIAN_POINT('',(0.,0.));
#990 = VECTOR('',#991,1.);
#991 = DIRECTION('',(-0.,-1.));
#992 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#993 = ORIENTED_EDGE('',*,*,#994,.F.);
#994 = EDGE_CURVE('',#891,#967,#995,.T.);
#995 = SURFACE_CURVE('',#996,(#1000,#1007),.PCURVE_S1.);
#996 = LINE('',#997,#998);
#997 = CARTESIAN_POINT('',(51.008936,121.80569,28.));
#998 = VECTOR('',#999,1.);
#999 = DIRECTION('',(-5.540424023454E-02,0.998464005442,0.));
#1000 = PCURVE('',#906,#1001);
#1001 = DEFINITIONAL_REPRESENTATION('',(#1002),#1006);
#1002 = LINE('',#1003,#1004);
#1003 = CARTESIAN_POINT('',(2.83780538394E-14,0.));
#1004 = VECTOR('',#1005,1.);
#1005 = DIRECTION('',(1.,0.));
#1006 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1007 = PCURVE('',#167,#1008);
#1008 = DEFINITIONAL_REPRESENTATION('',(#1009),#1013);
#1009 = LINE('',#1010,#1011);
#1010 = CARTESIAN_POINT('',(28.998224191567,-13.442108948));
#1011 = VECTOR('',#1012,1.);
#1012 = DIRECTION('',(5.540424023454E-02,0.998464005442));
#1013 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1014 = ADVANCED_FACE('',(#1015),#982,.F.);
#1015 = FACE_BOUND('',#1016,.F.);
#1016 = EDGE_LOOP('',(#1017,#1018,#1041,#1064));
#1017 = ORIENTED_EDGE('',*,*,#966,.T.);
#1018 = ORIENTED_EDGE('',*,*,#1019,.T.);
#1019 = EDGE_CURVE('',#944,#1020,#1022,.T.);
#1020 = VERTEX_POINT('',#1021);
#1021 = CARTESIAN_POINT('',(48.301331,131.800116,-13.767115));
#1022 = SURFACE_CURVE('',#1023,(#1027,#1034),.PCURVE_S1.);
#1023 = LINE('',#1024,#1025);
#1024 = CARTESIAN_POINT('',(50.449147,131.893892,-13.767115));
#1025 = VECTOR('',#1026,1.);
#1026 = DIRECTION('',(-0.999048214928,-4.361953975718E-02,0.));
#1027 = PCURVE('',#982,#1028);
#1028 = DEFINITIONAL_REPRESENTATION('',(#1029),#1033);
#1029 = LINE('',#1030,#1031);
#1030 = CARTESIAN_POINT('',(7.098664517914E-15,-41.767115));
#1031 = VECTOR('',#1032,1.);
#1032 = DIRECTION('',(1.,0.));
#1033 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1034 = PCURVE('',#113,#1035);
#1035 = DEFINITIONAL_REPRESENTATION('',(#1036),#1040);
#1036 = LINE('',#1037,#1038);
#1037 = CARTESIAN_POINT('',(29.558013191567,-3.353906948003));
#1038 = VECTOR('',#1039,1.);
#1039 = DIRECTION('',(0.999048214928,-4.361953975718E-02));
#1040 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1041 = ORIENTED_EDGE('',*,*,#1042,.F.);
#1042 = EDGE_CURVE('',#1043,#1020,#1045,.T.);
#1043 = VERTEX_POINT('',#1044);
#1044 = CARTESIAN_POINT('',(48.301331,131.800116,28.));
#1045 = SURFACE_CURVE('',#1046,(#1050,#1057),.PCURVE_S1.);
#1046 = LINE('',#1047,#1048);
#1047 = CARTESIAN_POINT('',(48.301331,131.800116,28.));
#1048 = VECTOR('',#1049,1.);
#1049 = DIRECTION('',(0.,0.,-1.));
#1050 = PCURVE('',#982,#1051);
#1051 = DEFINITIONAL_REPRESENTATION('',(#1052),#1056);
#1052 = LINE('',#1053,#1054);
#1053 = CARTESIAN_POINT('',(2.149862206755,0.));
#1054 = VECTOR('',#1055,1.);
#1055 = DIRECTION('',(-0.,-1.));
#1056 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1057 = PCURVE('',#85,#1058);
#1058 = DEFINITIONAL_REPRESENTATION('',(#1059),#1063);
#1059 = LINE('',#1060,#1061);
#1060 = CARTESIAN_POINT('',(0.,0.));
#1061 = VECTOR('',#1062,1.);
#1062 = DIRECTION('',(0.,-1.));
#1063 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1064 = ORIENTED_EDGE('',*,*,#1065,.F.);
#1065 = EDGE_CURVE('',#967,#1043,#1066,.T.);
#1066 = SURFACE_CURVE('',#1067,(#1071,#1078),.PCURVE_S1.);
#1067 = LINE('',#1068,#1069);
#1068 = CARTESIAN_POINT('',(50.449147,131.893892,28.));
#1069 = VECTOR('',#1070,1.);
#1070 = DIRECTION('',(-0.999048214928,-4.361953975718E-02,0.));
#1071 = PCURVE('',#982,#1072);
#1072 = DEFINITIONAL_REPRESENTATION('',(#1073),#1077);
#1073 = LINE('',#1074,#1075);
#1074 = CARTESIAN_POINT('',(7.098664517914E-15,0.));
#1075 = VECTOR('',#1076,1.);
#1076 = DIRECTION('',(1.,0.));
#1077 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1078 = PCURVE('',#167,#1079);
#1079 = DEFINITIONAL_REPRESENTATION('',(#1080),#1084);
#1080 = LINE('',#1081,#1082);
#1081 = CARTESIAN_POINT('',(29.558013191567,-3.353906948003));
#1082 = VECTOR('',#1083,1.);
#1083 = DIRECTION('',(0.999048214928,-4.361953975718E-02));
#1084 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1085 = ADVANCED_FACE('',(#1086),#85,.F.);
#1086 = FACE_BOUND('',#1087,.F.);
#1087 = EDGE_LOOP('',(#1088,#1089,#1110,#1111));
#1088 = ORIENTED_EDGE('',*,*,#1042,.T.);
#1089 = ORIENTED_EDGE('',*,*,#1090,.T.);
#1090 = EDGE_CURVE('',#1020,#65,#1091,.T.);
#1091 = SURFACE_CURVE('',#1092,(#1096,#1103),.PCURVE_S1.);
#1092 = LINE('',#1093,#1094);
#1093 = CARTESIAN_POINT('',(48.301331,131.800116,-13.767115));
#1094 = VECTOR('',#1095,1.);
#1095 = DIRECTION('',(-0.11428330053,0.993448200572,0.));
#1096 = PCURVE('',#85,#1097);
#1097 = DEFINITIONAL_REPRESENTATION('',(#1098),#1102);
#1098 = LINE('',#1099,#1100);
#1099 = CARTESIAN_POINT('',(0.,-41.767115));
#1100 = VECTOR('',#1101,1.);
#1101 = DIRECTION('',(1.,0.));
#1102 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1103 = PCURVE('',#113,#1104);
#1104 = DEFINITIONAL_REPRESENTATION('',(#1105),#1109);
#1105 = LINE('',#1106,#1107);
#1106 = CARTESIAN_POINT('',(31.705829191567,-3.447682948003));
#1107 = VECTOR('',#1108,1.);
#1108 = DIRECTION('',(0.11428330053,0.993448200572));
#1109 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1110 = ORIENTED_EDGE('',*,*,#62,.F.);
#1111 = ORIENTED_EDGE('',*,*,#1112,.F.);
#1112 = EDGE_CURVE('',#1043,#63,#1113,.T.);
#1113 = SURFACE_CURVE('',#1114,(#1118,#1125),.PCURVE_S1.);
#1114 = LINE('',#1115,#1116);
#1115 = CARTESIAN_POINT('',(48.301331,131.800116,28.));
#1116 = VECTOR('',#1117,1.);
#1117 = DIRECTION('',(-0.11428330053,0.993448200572,0.));
#1118 = PCURVE('',#85,#1119);
#1119 = DEFINITIONAL_REPRESENTATION('',(#1120),#1124);
#1120 = LINE('',#1121,#1122);
#1121 = CARTESIAN_POINT('',(0.,0.));
#1122 = VECTOR('',#1123,1.);
#1123 = DIRECTION('',(1.,0.));
#1124 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1125 = PCURVE('',#167,#1126);
#1126 = DEFINITIONAL_REPRESENTATION('',(#1127),#1131);
#1127 = LINE('',#1128,#1129);
#1128 = CARTESIAN_POINT('',(31.705829191567,-3.447682948003));
#1129 = VECTOR('',#1130,1.);
#1130 = DIRECTION('',(0.11428330053,0.993448200572));
#1131 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1132 = ADVANCED_FACE('',(#1133),#167,.F.);
#1133 = FACE_BOUND('',#1134,.F.);
#1134 = EDGE_LOOP('',(#1135,#1136,#1137,#1138,#1139,#1140,#1141,#1142,
    #1143,#1144,#1145,#1146,#1147,#1148));
#1135 = ORIENTED_EDGE('',*,*,#153,.T.);
#1136 = ORIENTED_EDGE('',*,*,#234,.T.);
#1137 = ORIENTED_EDGE('',*,*,#310,.T.);
#1138 = ORIENTED_EDGE('',*,*,#386,.T.);
#1139 = ORIENTED_EDGE('',*,*,#462,.T.);
#1140 = ORIENTED_EDGE('',*,*,#538,.T.);
#1141 = ORIENTED_EDGE('',*,*,#614,.T.);
#1142 = ORIENTED_EDGE('',*,*,#690,.T.);
#1143 = ORIENTED_EDGE('',*,*,#766,.T.);
#1144 = ORIENTED_EDGE('',*,*,#842,.T.);
#1145 = ORIENTED_EDGE('',*,*,#918,.T.);
#1146 = ORIENTED_EDGE('',*,*,#994,.T.);
#1147 = ORIENTED_EDGE('',*,*,#1065,.T.);
#1148 = ORIENTED_EDGE('',*,*,#1112,.T.);
#1149 = ADVANCED_FACE('',(#1150),#113,.T.);
#1150 = FACE_BOUND('',#1151,.T.);
#1151 = EDGE_LOOP('',(#1152,#1153,#1154,#1155,#1156,#1157,#1158,#1159,
    #1160,#1161,#1162,#1163,#1164,#1165));
#1152 = ORIENTED_EDGE('',*,*,#97,.T.);
#1153 = ORIENTED_EDGE('',*,*,#183,.T.);
#1154 = ORIENTED_EDGE('',*,*,#259,.T.);
#1155 = ORIENTED_EDGE('',*,*,#335,.T.);
#1156 = ORIENTED_EDGE('',*,*,#411,.T.);
#1157 = ORIENTED_EDGE('',*,*,#487,.T.);
#1158 = ORIENTED_EDGE('',*,*,#563,.T.);
#1159 = ORIENTED_EDGE('',*,*,#639,.T.);
#1160 = ORIENTED_EDGE('',*,*,#715,.T.);
#1161 = ORIENTED_EDGE('',*,*,#791,.T.);
#1162 = ORIENTED_EDGE('',*,*,#867,.T.);
#1163 = ORIENTED_EDGE('',*,*,#943,.T.);
#1164 = ORIENTED_EDGE('',*,*,#1019,.T.);
#1165 = ORIENTED_EDGE('',*,*,#1090,.T.);
#1166 = ( GEOMETRIC_REPRESENTATION_CONTEXT(3) 
GLOBAL_UNCERTAINTY_ASSIGNED_CONTEXT((#1170)) 
GLOBAL_UNIT_ASSIGNED_CONTEXT((#1167,#1168,#1169)) REPRESENTATION_CONTEXT
('Context #1','3D Context with UNIT and UNCERTAINTY') );
#1167 = ( LENGTH_UNIT() NAMED_UNIT(*) SI_UNIT(.MILLI.,.METRE.) );
#1168 = ( NAMED_UNIT(*) PLANE_ANGLE_UNIT() SI_UNIT($,.RADIAN.) );
#1169 = ( NAMED_UNIT(*) SI_UNIT($,.STERADIAN.) SOLID_ANGLE_UNIT() );
#1170 = UNCERTAINTY_MEASURE_WITH_UNIT(LENGTH_MEASURE(1.E-07),#1167,
  'distance_accuracy_value','confusion accuracy');
#1171 = CONTEXT_DEPENDENT_SHAPE_REPRESENTATION(#1172,#1174);
#1172 = ( REPRESENTATION_RELATIONSHIP('','',#55,#32) 
REPRESENTATION_RELATIONSHIP_WITH_TRANSFORMATION(#1173) 
SHAPE_REPRESENTATION_RELATIONSHIP() );
#1173 = ITEM_DEFINED_TRANSFORMATION('','',#11,#33);
#1174 = PRODUCT_DEFINITION_SHAPE('Placement','Placement of an item',
  #1175);
#1175 = NEXT_ASSEMBLY_USAGE_OCCURRENCE('85','=>[0:1:1:3]','',#27,#50,$);
#1176 = PRODUCT_RELATED_PRODUCT_CATEGORY('part',$,(#52));
#1177 = MECHANICAL_DESIGN_GEOMETRIC_PRESENTATION_REPRESENTATION('',(
    #1178),#1166);
#1178 = STYLED_ITEM('color',(#1179),#56);
#1179 = PRESENTATION_STYLE_ASSIGNMENT((#1180));
#1180 = SURFACE_STYLE_USAGE(.BOTH.,#1181);
#1181 = SURFACE_SIDE_STYLE('',(#1182));
#1182 = SURFACE_STYLE_FILL_AREA(#1183);
#1183 = FILL_AREA_STYLE('',(#1184));
#1184 = FILL_AREA_STYLE_COLOUR('',#1185);
#1185 = DRAUGHTING_PRE_DEFINED_COLOUR('cyan');
ENDSEC;
END-ISO-10303-21;
