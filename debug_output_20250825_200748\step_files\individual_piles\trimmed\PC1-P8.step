ISO-10303-21;
HEADER;
FILE_DESCRIPTION(('Open CASCADE Model'),'2;1');
FILE_NAME('Pile_PC1-P8','2025-08-25T20:07:48',('Author'),('Open CASCADE'
    ),'Open CASCADE STEP processor 7.8','build123d','Unknown');
FILE_SCHEMA(('AUTOMOTIVE_DESIGN { 1 0 10303 214 1 1 1 1 }'));
ENDSEC;
DATA;
#1 = APPLICATION_PROTOCOL_DEFINITION('international standard',
  'automotive_design',2000,#2);
#2 = APPLICATION_CONTEXT(
  'core data for automotive mechanical design processes');
#3 = SHAPE_DEFINITION_REPRESENTATION(#4,#10);
#4 = PRODUCT_DEFINITION_SHAPE('','',#5);
#5 = PRODUCT_DEFINITION('design','',#6,#9);
#6 = PRODUCT_DEFINITION_FORMATION('','',#7);
#7 = PRODUCT('Pile_PC1-P8','Pile_PC1-P8','',(#8));
#8 = PRODUCT_CONTEXT('',#2,'mechanical');
#9 = PRODUCT_DEFINITION_CONTEXT('part definition',#2,'design');
#10 = SHAPE_REPRESENTATION('',(#11,#15,#19,#23),#27);
#11 = AXIS2_PLACEMENT_3D('',#12,#13,#14);
#12 = CARTESIAN_POINT('',(0.,0.,0.));
#13 = DIRECTION('',(0.,0.,1.));
#14 = DIRECTION('',(1.,0.,-0.));
#15 = AXIS2_PLACEMENT_3D('',#16,#17,#18);
#16 = CARTESIAN_POINT('',(52.072031,122.502036,0.561178));
#17 = DIRECTION('',(0.,0.,1.));
#18 = DIRECTION('',(1.,0.,0.));
#19 = AXIS2_PLACEMENT_3D('',#20,#21,#22);
#20 = CARTESIAN_POINT('',(0.,0.,0.));
#21 = DIRECTION('',(0.,0.,1.));
#22 = DIRECTION('',(1.,0.,-0.));
#23 = AXIS2_PLACEMENT_3D('',#24,#25,#26);
#24 = CARTESIAN_POINT('',(0.,0.,0.));
#25 = DIRECTION('',(0.,0.,1.));
#26 = DIRECTION('',(1.,0.,-0.));
#27 = ( GEOMETRIC_REPRESENTATION_CONTEXT(3) 
GLOBAL_UNCERTAINTY_ASSIGNED_CONTEXT((#31)) GLOBAL_UNIT_ASSIGNED_CONTEXT(
(#28,#29,#30)) REPRESENTATION_CONTEXT('Context #1',
  '3D Context with UNIT and UNCERTAINTY') );
#28 = ( LENGTH_UNIT() NAMED_UNIT(*) SI_UNIT(.MILLI.,.METRE.) );
#29 = ( NAMED_UNIT(*) PLANE_ANGLE_UNIT() SI_UNIT($,.RADIAN.) );
#30 = ( NAMED_UNIT(*) SI_UNIT($,.STERADIAN.) SOLID_ANGLE_UNIT() );
#31 = UNCERTAINTY_MEASURE_WITH_UNIT(LENGTH_MEASURE(1.E-07),#28,
  'distance_accuracy_value','confusion accuracy');
#32 = PRODUCT_RELATED_PRODUCT_CATEGORY('part',$,(#7));
#33 = SHAPE_DEFINITION_REPRESENTATION(#34,#40);
#34 = PRODUCT_DEFINITION_SHAPE('','',#35);
#35 = PRODUCT_DEFINITION('design','',#36,#39);
#36 = PRODUCT_DEFINITION_FORMATION('','',#37);
#37 = PRODUCT('PC1-P8_Part1','PC1-P8_Part1','',(#38));
#38 = PRODUCT_CONTEXT('',#2,'mechanical');
#39 = PRODUCT_DEFINITION_CONTEXT('part definition',#2,'design');
#40 = ADVANCED_BREP_SHAPE_REPRESENTATION('',(#11,#41),#139);
#41 = MANIFOLD_SOLID_BREP('',#42);
#42 = CLOSED_SHELL('',(#43,#131,#135));
#43 = ADVANCED_FACE('',(#44),#57,.T.);
#44 = FACE_BOUND('',#45,.T.);
#45 = EDGE_LOOP('',(#46,#80,#103,#130));
#46 = ORIENTED_EDGE('',*,*,#47,.F.);
#47 = EDGE_CURVE('',#48,#48,#50,.T.);
#48 = VERTEX_POINT('',#49);
#49 = CARTESIAN_POINT('',(0.275,-6.73555739531E-17,17.438822));
#50 = SURFACE_CURVE('',#51,(#56,#68),.PCURVE_S1.);
#51 = CIRCLE('',#52,0.275);
#52 = AXIS2_PLACEMENT_3D('',#53,#54,#55);
#53 = CARTESIAN_POINT('',(0.,0.,17.438822));
#54 = DIRECTION('',(0.,0.,1.));
#55 = DIRECTION('',(1.,0.,-0.));
#56 = PCURVE('',#57,#62);
#57 = CYLINDRICAL_SURFACE('',#58,0.275);
#58 = AXIS2_PLACEMENT_3D('',#59,#60,#61);
#59 = CARTESIAN_POINT('',(0.,0.,0.));
#60 = DIRECTION('',(0.,0.,1.));
#61 = DIRECTION('',(1.,0.,-0.));
#62 = DEFINITIONAL_REPRESENTATION('',(#63),#67);
#63 = LINE('',#64,#65);
#64 = CARTESIAN_POINT('',(0.,17.438822));
#65 = VECTOR('',#66,1.);
#66 = DIRECTION('',(1.,0.));
#67 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#68 = PCURVE('',#69,#74);
#69 = PLANE('',#70);
#70 = AXIS2_PLACEMENT_3D('',#71,#72,#73);
#71 = CARTESIAN_POINT('',(0.,0.,17.438822));
#72 = DIRECTION('',(0.,0.,1.));
#73 = DIRECTION('',(1.,0.,-0.));
#74 = DEFINITIONAL_REPRESENTATION('',(#75),#79);
#75 = CIRCLE('',#76,0.275);
#76 = AXIS2_PLACEMENT_2D('',#77,#78);
#77 = CARTESIAN_POINT('',(0.,0.));
#78 = DIRECTION('',(1.,0.));
#79 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#80 = ORIENTED_EDGE('',*,*,#81,.F.);
#81 = EDGE_CURVE('',#82,#48,#84,.T.);
#82 = VERTEX_POINT('',#83);
#83 = CARTESIAN_POINT('',(0.275,-6.73555739531E-17,0.));
#84 = SEAM_CURVE('',#85,(#89,#96),.PCURVE_S1.);
#85 = LINE('',#86,#87);
#86 = CARTESIAN_POINT('',(0.275,-6.73555739531E-17,0.));
#87 = VECTOR('',#88,1.);
#88 = DIRECTION('',(0.,0.,1.));
#89 = PCURVE('',#57,#90);
#90 = DEFINITIONAL_REPRESENTATION('',(#91),#95);
#91 = LINE('',#92,#93);
#92 = CARTESIAN_POINT('',(6.28318530718,-0.));
#93 = VECTOR('',#94,1.);
#94 = DIRECTION('',(0.,1.));
#95 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#96 = PCURVE('',#57,#97);
#97 = DEFINITIONAL_REPRESENTATION('',(#98),#102);
#98 = LINE('',#99,#100);
#99 = CARTESIAN_POINT('',(0.,-0.));
#100 = VECTOR('',#101,1.);
#101 = DIRECTION('',(0.,1.));
#102 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#103 = ORIENTED_EDGE('',*,*,#104,.T.);
#104 = EDGE_CURVE('',#82,#82,#105,.T.);
#105 = SURFACE_CURVE('',#106,(#111,#118),.PCURVE_S1.);
#106 = CIRCLE('',#107,0.275);
#107 = AXIS2_PLACEMENT_3D('',#108,#109,#110);
#108 = CARTESIAN_POINT('',(0.,0.,0.));
#109 = DIRECTION('',(0.,0.,1.));
#110 = DIRECTION('',(1.,0.,-0.));
#111 = PCURVE('',#57,#112);
#112 = DEFINITIONAL_REPRESENTATION('',(#113),#117);
#113 = LINE('',#114,#115);
#114 = CARTESIAN_POINT('',(0.,0.));
#115 = VECTOR('',#116,1.);
#116 = DIRECTION('',(1.,0.));
#117 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#118 = PCURVE('',#119,#124);
#119 = PLANE('',#120);
#120 = AXIS2_PLACEMENT_3D('',#121,#122,#123);
#121 = CARTESIAN_POINT('',(0.,0.,0.));
#122 = DIRECTION('',(0.,0.,1.));
#123 = DIRECTION('',(1.,0.,-0.));
#124 = DEFINITIONAL_REPRESENTATION('',(#125),#129);
#125 = CIRCLE('',#126,0.275);
#126 = AXIS2_PLACEMENT_2D('',#127,#128);
#127 = CARTESIAN_POINT('',(0.,0.));
#128 = DIRECTION('',(1.,0.));
#129 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#130 = ORIENTED_EDGE('',*,*,#81,.T.);
#131 = ADVANCED_FACE('',(#132),#69,.T.);
#132 = FACE_BOUND('',#133,.T.);
#133 = EDGE_LOOP('',(#134));
#134 = ORIENTED_EDGE('',*,*,#47,.T.);
#135 = ADVANCED_FACE('',(#136),#119,.F.);
#136 = FACE_BOUND('',#137,.T.);
#137 = EDGE_LOOP('',(#138));
#138 = ORIENTED_EDGE('',*,*,#104,.F.);
#139 = ( GEOMETRIC_REPRESENTATION_CONTEXT(3) 
GLOBAL_UNCERTAINTY_ASSIGNED_CONTEXT((#143)) GLOBAL_UNIT_ASSIGNED_CONTEXT
((#140,#141,#142)) REPRESENTATION_CONTEXT('Context #1',
  '3D Context with UNIT and UNCERTAINTY') );
#140 = ( LENGTH_UNIT() NAMED_UNIT(*) SI_UNIT(.MILLI.,.METRE.) );
#141 = ( NAMED_UNIT(*) PLANE_ANGLE_UNIT() SI_UNIT($,.RADIAN.) );
#142 = ( NAMED_UNIT(*) SI_UNIT($,.STERADIAN.) SOLID_ANGLE_UNIT() );
#143 = UNCERTAINTY_MEASURE_WITH_UNIT(LENGTH_MEASURE(1.E-07),#140,
  'distance_accuracy_value','confusion accuracy');
#144 = CONTEXT_DEPENDENT_SHAPE_REPRESENTATION(#145,#147);
#145 = ( REPRESENTATION_RELATIONSHIP('','',#40,#10) 
REPRESENTATION_RELATIONSHIP_WITH_TRANSFORMATION(#146) 
SHAPE_REPRESENTATION_RELATIONSHIP() );
#146 = ITEM_DEFINED_TRANSFORMATION('','',#11,#15);
#147 = PRODUCT_DEFINITION_SHAPE('Placement','Placement of an item',#148
  );
#148 = NEXT_ASSEMBLY_USAGE_OCCURRENCE('54','=>[0:1:1:2]','',#5,#35,$);
#149 = PRODUCT_RELATED_PRODUCT_CATEGORY('part',$,(#37));
#150 = SHAPE_DEFINITION_REPRESENTATION(#151,#157);
#151 = PRODUCT_DEFINITION_SHAPE('','',#152);
#152 = PRODUCT_DEFINITION('design','',#153,#156);
#153 = PRODUCT_DEFINITION_FORMATION('','',#154);
#154 = PRODUCT('PC1-P8_Part2','PC1-P8_Part2','',(#155));
#155 = PRODUCT_CONTEXT('',#2,'mechanical');
#156 = PRODUCT_DEFINITION_CONTEXT('part definition',#2,'design');
#157 = ADVANCED_BREP_SHAPE_REPRESENTATION('',(#11,#158),#520);
#158 = MANIFOLD_SOLID_BREP('',#159);
#159 = CLOSED_SHELL('',(#160,#390,#440,#488,#514));
#160 = ADVANCED_FACE('',(#161),#175,.T.);
#161 = FACE_BOUND('',#162,.T.);
#162 = EDGE_LOOP('',(#163,#193,#222,#285,#341,#363,#364));
#163 = ORIENTED_EDGE('',*,*,#164,.T.);
#164 = EDGE_CURVE('',#165,#167,#169,.T.);
#165 = VERTEX_POINT('',#166);
#166 = CARTESIAN_POINT('',(52.347031,122.502036,0.561178));
#167 = VERTEX_POINT('',#168);
#168 = CARTESIAN_POINT('',(59.275234230276,122.502036,12.561178));
#169 = SEAM_CURVE('',#170,(#174,#186),.PCURVE_S1.);
#170 = LINE('',#171,#172);
#171 = CARTESIAN_POINT('',(52.347031,122.502036,0.561178));
#172 = VECTOR('',#173,1.);
#173 = DIRECTION('',(0.5,-1.224646799147E-16,0.866025403784));
#174 = PCURVE('',#175,#180);
#175 = CONICAL_SURFACE('',#176,0.275,0.523598775598);
#176 = AXIS2_PLACEMENT_3D('',#177,#178,#179);
#177 = CARTESIAN_POINT('',(52.072031,122.502036,0.561178));
#178 = DIRECTION('',(0.,0.,1.));
#179 = DIRECTION('',(1.,0.,0.));
#180 = DEFINITIONAL_REPRESENTATION('',(#181),#185);
#181 = LINE('',#182,#183);
#182 = CARTESIAN_POINT('',(6.28318530718,-0.));
#183 = VECTOR('',#184,1.);
#184 = DIRECTION('',(0.,1.));
#185 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#186 = PCURVE('',#175,#187);
#187 = DEFINITIONAL_REPRESENTATION('',(#188),#192);
#188 = LINE('',#189,#190);
#189 = CARTESIAN_POINT('',(0.,-0.));
#190 = VECTOR('',#191,1.);
#191 = DIRECTION('',(0.,1.));
#192 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#193 = ORIENTED_EDGE('',*,*,#194,.F.);
#194 = EDGE_CURVE('',#195,#167,#197,.T.);
#195 = VERTEX_POINT('',#196);
#196 = CARTESIAN_POINT('',(59.241538283317,121.80611783591,12.561178));
#197 = SURFACE_CURVE('',#198,(#203,#210),.PCURVE_S1.);
#198 = CIRCLE('',#199,7.203203230276);
#199 = AXIS2_PLACEMENT_3D('',#200,#201,#202);
#200 = CARTESIAN_POINT('',(52.072031,122.502036,12.561178));
#201 = DIRECTION('',(0.,0.,1.));
#202 = DIRECTION('',(1.,0.,0.));
#203 = PCURVE('',#175,#204);
#204 = DEFINITIONAL_REPRESENTATION('',(#205),#209);
#205 = LINE('',#206,#207);
#206 = CARTESIAN_POINT('',(0.,12.));
#207 = VECTOR('',#208,1.);
#208 = DIRECTION('',(1.,0.));
#209 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#210 = PCURVE('',#211,#216);
#211 = PLANE('',#212);
#212 = AXIS2_PLACEMENT_3D('',#213,#214,#215);
#213 = CARTESIAN_POINT('',(52.072031,122.502036,12.561178));
#214 = DIRECTION('',(0.,0.,1.));
#215 = DIRECTION('',(1.,0.,0.));
#216 = DEFINITIONAL_REPRESENTATION('',(#217),#221);
#217 = CIRCLE('',#218,7.203203230276);
#218 = AXIS2_PLACEMENT_2D('',#219,#220);
#219 = CARTESIAN_POINT('',(0.,0.));
#220 = DIRECTION('',(1.,0.));
#221 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#222 = ORIENTED_EDGE('',*,*,#223,.F.);
#223 = EDGE_CURVE('',#224,#195,#226,.T.);
#224 = VERTEX_POINT('',#225);
#225 = CARTESIAN_POINT('',(51.008936,121.80569,2.286046935408));
#226 = SURFACE_CURVE('',#227,(#232,#273),.PCURVE_S1.);
#227 = HYPERBOLA('',#228,1.206010958642,0.696290751618);
#228 = AXIS2_PLACEMENT_3D('',#229,#230,#231);
#229 = CARTESIAN_POINT('',(52.072067185179,121.80574524932,
    8.486402791856E-02));
#230 = DIRECTION('',(-5.196848995186E-05,0.99999999865,0.));
#231 = DIRECTION('',(0.,0.,1.));
#232 = PCURVE('',#175,#233);
#233 = DEFINITIONAL_REPRESENTATION('',(#234),#272);
#234 = B_SPLINE_CURVE_WITH_KNOTS('',8,(#235,#236,#237,#238,#239,#240,
    #241,#242,#243,#244,#245,#246,#247,#248,#249,#250,#251,#252,#253,
    #254,#255,#256,#257,#258,#259,#260,#261,#262,#263,#264,#265,#266,
    #267,#268,#269,#270,#271),.UNSPECIFIED.,.F.,.F.,(9,7,7,7,7,9),(
    -2.381191868529,-1.029064714466,-1.496934891958E-02,0.745602175241,
    1.886459461481,3.027316747721),.UNSPECIFIED.);
#235 = CARTESIAN_POINT('',(3.326001360872,6.102613835399));
#236 = CARTESIAN_POINT('',(3.356984376133,5.009513046375));
#237 = CARTESIAN_POINT('',(3.393851003693,4.131196317832));
#238 = CARTESIAN_POINT('',(3.437856652736,3.420081809582));
#239 = CARTESIAN_POINT('',(3.490555458728,2.841674147631));
#240 = CARTESIAN_POINT('',(3.553800176292,2.37022676901));
#241 = CARTESIAN_POINT('',(3.629818436355,1.986353043227));
#242 = CARTESIAN_POINT('',(3.720929123746,1.675435125053));
#243 = CARTESIAN_POINT('',(3.9083801112,1.240050051284));
#244 = CARTESIAN_POINT('',(3.997718093616,1.088402545061));
#245 = CARTESIAN_POINT('',(4.096452090605,0.96713231323));
#246 = CARTESIAN_POINT('',(4.204503531437,0.872868428661));
#247 = CARTESIAN_POINT('',(4.32115112168,0.803203123433));
#248 = CARTESIAN_POINT('',(4.444252344136,0.756558982635));
#249 = CARTESIAN_POINT('',(4.570724439458,0.732120655028));
#250 = CARTESIAN_POINT('',(4.792532948626,0.72811570368));
#251 = CARTESIAN_POINT('',(4.887747625269,0.738858586078));
#252 = CARTESIAN_POINT('',(4.981812242505,0.762036945516));
#253 = CARTESIAN_POINT('',(5.073389639769,0.797867658611));
#254 = CARTESIAN_POINT('',(5.161351585635,0.846806824315));
#255 = CARTESIAN_POINT('',(5.244905184558,0.909560738401));
#256 = CARTESIAN_POINT('',(5.323603547058,0.987109309782));
#257 = CARTESIAN_POINT('',(5.507696487782,1.221190832047));
#258 = CARTESIAN_POINT('',(5.606766093943,1.397828938759));
#259 = CARTESIAN_POINT('',(5.693761338451,1.61500860323));
#260 = CARTESIAN_POINT('',(5.769300442109,1.878652603735));
#261 = CARTESIAN_POINT('',(5.834690496216,2.196536008787));
#262 = CARTESIAN_POINT('',(5.891195549737,2.57874317537));
#263 = CARTESIAN_POINT('',(5.940044493573,3.038344971187));
#264 = CARTESIAN_POINT('',(6.024582728899,4.1466844663));
#265 = CARTESIAN_POINT('',(6.060272230999,4.795421613605));
#266 = CARTESIAN_POINT('',(6.090463130971,5.555903307208));
#267 = CARTESIAN_POINT('',(6.116096170252,6.449395042153));
#268 = CARTESIAN_POINT('',(6.137939972223,7.502410212094));
#269 = CARTESIAN_POINT('',(6.156615790827,8.74821506082));
#270 = CARTESIAN_POINT('',(6.172637073537,10.229120217757));
#271 = CARTESIAN_POINT('',(6.186422058354,12.));
#272 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#273 = PCURVE('',#274,#279);
#274 = PLANE('',#275);
#275 = AXIS2_PLACEMENT_3D('',#276,#277,#278);
#276 = CARTESIAN_POINT('',(77.717428,121.807078,28.));
#277 = DIRECTION('',(-5.196848995186E-05,0.99999999865,0.));
#278 = DIRECTION('',(-0.99999999865,-5.196848995186E-05,0.));
#279 = DEFINITIONAL_REPRESENTATION('',(#280),#284);
#280 = ( BOUNDED_CURVE() B_SPLINE_CURVE(2,(#281,#282,#283),
.UNSPECIFIED.,.F.,.F.) B_SPLINE_CURVE_WITH_KNOTS((3,3),(-2.381191868529,
3.027316747721),.PIECEWISE_BEZIER_KNOTS.) CURVE() 
GEOMETRIC_REPRESENTATION_ITEM() RATIONAL_B_SPLINE_CURVE((1.,
7.50504487093,1.)) REPRESENTATION_ITEM('') );
#281 = CARTESIAN_POINT('',(29.379341239673,-21.3362081646));
#282 = CARTESIAN_POINT('',(25.614864195098,-27.74598368207));
#283 = CARTESIAN_POINT('',(18.475889741632,-15.438822));
#284 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#285 = ORIENTED_EDGE('',*,*,#286,.F.);
#286 = EDGE_CURVE('',#287,#224,#289,.T.);
#287 = VERTEX_POINT('',#288);
#288 = CARTESIAN_POINT('',(50.579271284871,129.54886544376,12.561178));
#289 = SURFACE_CURVE('',#290,(#295,#329),.PCURVE_S1.);
#290 = HYPERBOLA('',#291,1.905329696096,1.100042612936);
#291 = AXIS2_PLACEMENT_3D('',#292,#293,#294);
#292 = CARTESIAN_POINT('',(50.97367804653,122.4410889748,
    8.486402791856E-02));
#293 = DIRECTION('',(0.998464005442,5.540424023454E-02,0.));
#294 = DIRECTION('',(-0.,0.,1.));
#295 = PCURVE('',#175,#296);
#296 = DEFINITIONAL_REPRESENTATION('',(#297),#328);
#297 = B_SPLINE_CURVE_WITH_KNOTS('',8,(#298,#299,#300,#301,#302,#303,
    #304,#305,#306,#307,#308,#309,#310,#311,#312,#313,#314,#315,#316,
    #317,#318,#319,#320,#321,#322,#323,#324,#325,#326,#327),
  .UNSPECIFIED.,.F.,.F.,(9,7,7,7,9),(-2.566441893749,-1.6265588507,
    -0.921646568414,0.135721855015,1.193090278444),.UNSPECIFIED.);
#298 = CARTESIAN_POINT('',(1.77954463189,12.));
#299 = CARTESIAN_POINT('',(1.797486500345,10.551408932734));
#300 = CARTESIAN_POINT('',(1.8178092146,9.299627150538));
#301 = CARTESIAN_POINT('',(1.840872097726,8.21418761677));
#302 = CARTESIAN_POINT('',(1.867096467721,7.270414629039));
#303 = CARTESIAN_POINT('',(1.896969899295,6.448090044373));
#304 = CARTESIAN_POINT('',(1.931059965875,5.730515006948));
#305 = CARTESIAN_POINT('',(1.970009686555,5.103843445926));
#306 = CARTESIAN_POINT('',(2.047844814811,4.146103734674));
#307 = CARTESIAN_POINT('',(2.084311862798,3.780301604302));
#308 = CARTESIAN_POINT('',(2.124169837934,3.454301939611));
#309 = CARTESIAN_POINT('',(2.16770677464,3.16398669886));
#310 = CARTESIAN_POINT('',(2.215206124119,2.905859485833));
#311 = CARTESIAN_POINT('',(2.266919432554,2.676952433579));
#312 = CARTESIAN_POINT('',(2.323022523861,2.47475556483));
#313 = CARTESIAN_POINT('',(2.474354181756,2.030788225536));
#314 = CARTESIAN_POINT('',(2.57511894045,1.819779805388));
#315 = CARTESIAN_POINT('',(2.685993552261,1.65705241284));
#316 = CARTESIAN_POINT('',(2.806395156938,1.537578594487));
#317 = CARTESIAN_POINT('',(2.934794881709,1.457998343316));
#318 = CARTESIAN_POINT('',(3.067738324985,1.416413985197));
#319 = CARTESIAN_POINT('',(3.201369362791,1.412307381016));
#320 = CARTESIAN_POINT('',(3.463295369486,1.480874952114));
#321 = CARTESIAN_POINT('',(3.591588299541,1.553549023551));
#322 = CARTESIAN_POINT('',(3.713864932211,1.665526697464));
#323 = CARTESIAN_POINT('',(3.827199573075,1.819149306673));
#324 = CARTESIAN_POINT('',(3.930222169995,2.018245151741));
#325 = CARTESIAN_POINT('',(4.022834258334,2.268302401994));
#326 = CARTESIAN_POINT('',(4.105472974782,2.5768063646));
#327 = CARTESIAN_POINT('',(4.178890375852,2.953791540218));
#328 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#329 = PCURVE('',#330,#335);
#330 = PLANE('',#331);
#331 = AXIS2_PLACEMENT_3D('',#332,#333,#334);
#332 = CARTESIAN_POINT('',(51.008936,121.80569,28.));
#333 = DIRECTION('',(0.998464005442,5.540424023454E-02,0.));
#334 = DIRECTION('',(-5.540424023454E-02,0.998464005442,0.));
#335 = DEFINITIONAL_REPRESENTATION('',(#336),#340);
#336 = ( BOUNDED_CURVE() B_SPLINE_CURVE(2,(#337,#338,#339),
.UNSPECIFIED.,.F.,.F.) B_SPLINE_CURVE_WITH_KNOTS((3,3),(-2.566441893749,
1.193090278444),.PIECEWISE_BEZIER_KNOTS.) CURVE() 
GEOMETRIC_REPRESENTATION_ITEM() RATIONAL_B_SPLINE_CURVE((1.,
3.352298944221,1.)) REPRESENTATION_ITEM('') );
#337 = CARTESIAN_POINT('',(7.755087215524,-15.438822));
#338 = CARTESIAN_POINT('',(0.879836442536,-27.20742332729));
#339 = CARTESIAN_POINT('',(-1.010372126087,-24.48503045978));
#340 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#341 = ORIENTED_EDGE('',*,*,#342,.F.);
#342 = EDGE_CURVE('',#167,#287,#343,.T.);
#343 = SURFACE_CURVE('',#344,(#349,#356),.PCURVE_S1.);
#344 = CIRCLE('',#345,7.203203230276);
#345 = AXIS2_PLACEMENT_3D('',#346,#347,#348);
#346 = CARTESIAN_POINT('',(52.072031,122.502036,12.561178));
#347 = DIRECTION('',(0.,0.,1.));
#348 = DIRECTION('',(1.,0.,0.));
#349 = PCURVE('',#175,#350);
#350 = DEFINITIONAL_REPRESENTATION('',(#351),#355);
#351 = LINE('',#352,#353);
#352 = CARTESIAN_POINT('',(0.,12.));
#353 = VECTOR('',#354,1.);
#354 = DIRECTION('',(1.,0.));
#355 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#356 = PCURVE('',#211,#357);
#357 = DEFINITIONAL_REPRESENTATION('',(#358),#362);
#358 = CIRCLE('',#359,7.203203230276);
#359 = AXIS2_PLACEMENT_2D('',#360,#361);
#360 = CARTESIAN_POINT('',(0.,0.));
#361 = DIRECTION('',(1.,0.));
#362 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#363 = ORIENTED_EDGE('',*,*,#164,.F.);
#364 = ORIENTED_EDGE('',*,*,#365,.T.);
#365 = EDGE_CURVE('',#165,#165,#366,.T.);
#366 = SURFACE_CURVE('',#367,(#372,#379),.PCURVE_S1.);
#367 = CIRCLE('',#368,0.275);
#368 = AXIS2_PLACEMENT_3D('',#369,#370,#371);
#369 = CARTESIAN_POINT('',(52.072031,122.502036,0.561178));
#370 = DIRECTION('',(0.,0.,1.));
#371 = DIRECTION('',(1.,0.,0.));
#372 = PCURVE('',#175,#373);
#373 = DEFINITIONAL_REPRESENTATION('',(#374),#378);
#374 = LINE('',#375,#376);
#375 = CARTESIAN_POINT('',(0.,0.));
#376 = VECTOR('',#377,1.);
#377 = DIRECTION('',(1.,0.));
#378 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#379 = PCURVE('',#380,#385);
#380 = CYLINDRICAL_SURFACE('',#381,0.275);
#381 = AXIS2_PLACEMENT_3D('',#382,#383,#384);
#382 = CARTESIAN_POINT('',(52.072031,122.502036,0.561178));
#383 = DIRECTION('',(0.,0.,1.));
#384 = DIRECTION('',(1.,0.,0.));
#385 = DEFINITIONAL_REPRESENTATION('',(#386),#389);
#386 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#387,#388),.UNSPECIFIED.,.F.,.F.,
  (2,2),(0.,6.28318530718),.PIECEWISE_BEZIER_KNOTS.);
#387 = CARTESIAN_POINT('',(0.,0.));
#388 = CARTESIAN_POINT('',(6.28318530718,0.));
#389 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#390 = ADVANCED_FACE('',(#391),#380,.F.);
#391 = FACE_BOUND('',#392,.F.);
#392 = EDGE_LOOP('',(#393,#417,#438,#439));
#393 = ORIENTED_EDGE('',*,*,#394,.F.);
#394 = EDGE_CURVE('',#395,#395,#397,.T.);
#395 = VERTEX_POINT('',#396);
#396 = CARTESIAN_POINT('',(52.347031,122.502036,12.561178));
#397 = SURFACE_CURVE('',#398,(#403,#410),.PCURVE_S1.);
#398 = CIRCLE('',#399,0.275);
#399 = AXIS2_PLACEMENT_3D('',#400,#401,#402);
#400 = CARTESIAN_POINT('',(52.072031,122.502036,12.561178));
#401 = DIRECTION('',(0.,0.,1.));
#402 = DIRECTION('',(1.,0.,0.));
#403 = PCURVE('',#380,#404);
#404 = DEFINITIONAL_REPRESENTATION('',(#405),#409);
#405 = LINE('',#406,#407);
#406 = CARTESIAN_POINT('',(0.,12.));
#407 = VECTOR('',#408,1.);
#408 = DIRECTION('',(1.,0.));
#409 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#410 = PCURVE('',#211,#411);
#411 = DEFINITIONAL_REPRESENTATION('',(#412),#416);
#412 = CIRCLE('',#413,0.275);
#413 = AXIS2_PLACEMENT_2D('',#414,#415);
#414 = CARTESIAN_POINT('',(0.,0.));
#415 = DIRECTION('',(1.,0.));
#416 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#417 = ORIENTED_EDGE('',*,*,#418,.F.);
#418 = EDGE_CURVE('',#165,#395,#419,.T.);
#419 = SEAM_CURVE('',#420,(#424,#431),.PCURVE_S1.);
#420 = LINE('',#421,#422);
#421 = CARTESIAN_POINT('',(52.347031,122.502036,0.561178));
#422 = VECTOR('',#423,1.);
#423 = DIRECTION('',(0.,0.,1.));
#424 = PCURVE('',#380,#425);
#425 = DEFINITIONAL_REPRESENTATION('',(#426),#430);
#426 = LINE('',#427,#428);
#427 = CARTESIAN_POINT('',(6.28318530718,-0.));
#428 = VECTOR('',#429,1.);
#429 = DIRECTION('',(0.,1.));
#430 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#431 = PCURVE('',#380,#432);
#432 = DEFINITIONAL_REPRESENTATION('',(#433),#437);
#433 = LINE('',#434,#435);
#434 = CARTESIAN_POINT('',(0.,-0.));
#435 = VECTOR('',#436,1.);
#436 = DIRECTION('',(0.,1.));
#437 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#438 = ORIENTED_EDGE('',*,*,#365,.T.);
#439 = ORIENTED_EDGE('',*,*,#418,.T.);
#440 = ADVANCED_FACE('',(#441,#485),#211,.T.);
#441 = FACE_BOUND('',#442,.T.);
#442 = EDGE_LOOP('',(#443,#444,#445,#466));
#443 = ORIENTED_EDGE('',*,*,#194,.T.);
#444 = ORIENTED_EDGE('',*,*,#342,.T.);
#445 = ORIENTED_EDGE('',*,*,#446,.F.);
#446 = EDGE_CURVE('',#447,#287,#449,.T.);
#447 = VERTEX_POINT('',#448);
#448 = CARTESIAN_POINT('',(51.008936,121.80569,12.561178));
#449 = SURFACE_CURVE('',#450,(#454,#460),.PCURVE_S1.);
#450 = LINE('',#451,#452);
#451 = CARTESIAN_POINT('',(50.991307023265,122.1233894874,12.561178));
#452 = VECTOR('',#453,1.);
#453 = DIRECTION('',(-5.540424023454E-02,0.998464005442,0.));
#454 = PCURVE('',#211,#455);
#455 = DEFINITIONAL_REPRESENTATION('',(#456),#459);
#456 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#457,#458),.UNSPECIFIED.,.F.,.F.,
  (2,2),(-0.318188222781,7.593513338034),.PIECEWISE_BEZIER_KNOTS.);
#457 = CARTESIAN_POINT('',(-1.063095,-0.696346));
#458 = CARTESIAN_POINT('',(-1.501436813939,7.203203230276));
#459 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#460 = PCURVE('',#330,#461);
#461 = DEFINITIONAL_REPRESENTATION('',(#462),#465);
#462 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#463,#464),.UNSPECIFIED.,.F.,.F.,
  (2,2),(-0.318188222781,7.593513338034),.PIECEWISE_BEZIER_KNOTS.);
#463 = CARTESIAN_POINT('',(0.,-15.438822));
#464 = CARTESIAN_POINT('',(7.911701560814,-15.438822));
#465 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#466 = ORIENTED_EDGE('',*,*,#467,.F.);
#467 = EDGE_CURVE('',#195,#447,#468,.T.);
#468 = SURFACE_CURVE('',#469,(#473,#479),.PCURVE_S1.);
#469 = LINE('',#470,#471);
#470 = CARTESIAN_POINT('',(64.894747592589,121.80641162466,12.561178));
#471 = VECTOR('',#472,1.);
#472 = DIRECTION('',(-0.99999999865,-5.196848995186E-05,0.));
#473 = PCURVE('',#211,#474);
#474 = DEFINITIONAL_REPRESENTATION('',(#475),#478);
#475 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#476,#477),.UNSPECIFIED.,.F.,.F.,
  (2,2),(5.619513369902,13.88581161134),.PIECEWISE_BEZIER_KNOTS.);
#476 = CARTESIAN_POINT('',(7.203203230276,-0.695916412963));
#477 = CARTESIAN_POINT('',(-1.063095,-0.696346));
#478 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#479 = PCURVE('',#274,#480);
#480 = DEFINITIONAL_REPRESENTATION('',(#481),#484);
#481 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#482,#483),.UNSPECIFIED.,.F.,.F.,
  (2,2),(5.619513369902,13.88581161134),.PIECEWISE_BEZIER_KNOTS.);
#482 = CARTESIAN_POINT('',(18.442193794628,-15.438822));
#483 = CARTESIAN_POINT('',(26.708492036066,-15.438822));
#484 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#485 = FACE_BOUND('',#486,.T.);
#486 = EDGE_LOOP('',(#487));
#487 = ORIENTED_EDGE('',*,*,#394,.F.);
#488 = ADVANCED_FACE('',(#489),#330,.F.);
#489 = FACE_BOUND('',#490,.F.);
#490 = EDGE_LOOP('',(#491,#492,#513));
#491 = ORIENTED_EDGE('',*,*,#446,.F.);
#492 = ORIENTED_EDGE('',*,*,#493,.T.);
#493 = EDGE_CURVE('',#447,#224,#494,.T.);
#494 = SURFACE_CURVE('',#495,(#499,#506),.PCURVE_S1.);
#495 = LINE('',#496,#497);
#496 = CARTESIAN_POINT('',(51.008936,121.80569,28.));
#497 = VECTOR('',#498,1.);
#498 = DIRECTION('',(0.,0.,-1.));
#499 = PCURVE('',#330,#500);
#500 = DEFINITIONAL_REPRESENTATION('',(#501),#505);
#501 = LINE('',#502,#503);
#502 = CARTESIAN_POINT('',(0.,0.));
#503 = VECTOR('',#504,1.);
#504 = DIRECTION('',(0.,-1.));
#505 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#506 = PCURVE('',#274,#507);
#507 = DEFINITIONAL_REPRESENTATION('',(#508),#512);
#508 = LINE('',#509,#510);
#509 = CARTESIAN_POINT('',(26.708492036066,0.));
#510 = VECTOR('',#511,1.);
#511 = DIRECTION('',(-0.,-1.));
#512 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#513 = ORIENTED_EDGE('',*,*,#286,.F.);
#514 = ADVANCED_FACE('',(#515),#274,.F.);
#515 = FACE_BOUND('',#516,.F.);
#516 = EDGE_LOOP('',(#517,#518,#519));
#517 = ORIENTED_EDGE('',*,*,#493,.F.);
#518 = ORIENTED_EDGE('',*,*,#467,.F.);
#519 = ORIENTED_EDGE('',*,*,#223,.F.);
#520 = ( GEOMETRIC_REPRESENTATION_CONTEXT(3) 
GLOBAL_UNCERTAINTY_ASSIGNED_CONTEXT((#524)) GLOBAL_UNIT_ASSIGNED_CONTEXT
((#521,#522,#523)) REPRESENTATION_CONTEXT('Context #1',
  '3D Context with UNIT and UNCERTAINTY') );
#521 = ( LENGTH_UNIT() NAMED_UNIT(*) SI_UNIT(.MILLI.,.METRE.) );
#522 = ( NAMED_UNIT(*) PLANE_ANGLE_UNIT() SI_UNIT($,.RADIAN.) );
#523 = ( NAMED_UNIT(*) SI_UNIT($,.STERADIAN.) SOLID_ANGLE_UNIT() );
#524 = UNCERTAINTY_MEASURE_WITH_UNIT(LENGTH_MEASURE(1.E-07),#521,
  'distance_accuracy_value','confusion accuracy');
#525 = CONTEXT_DEPENDENT_SHAPE_REPRESENTATION(#526,#528);
#526 = ( REPRESENTATION_RELATIONSHIP('','',#157,#10) 
REPRESENTATION_RELATIONSHIP_WITH_TRANSFORMATION(#527) 
SHAPE_REPRESENTATION_RELATIONSHIP() );
#527 = ITEM_DEFINED_TRANSFORMATION('','',#11,#19);
#528 = PRODUCT_DEFINITION_SHAPE('Placement','Placement of an item',#529
  );
#529 = NEXT_ASSEMBLY_USAGE_OCCURRENCE('55','=>[0:1:1:3]','',#5,#152,$);
#530 = PRODUCT_RELATED_PRODUCT_CATEGORY('part',$,(#154));
#531 = SHAPE_DEFINITION_REPRESENTATION(#532,#538);
#532 = PRODUCT_DEFINITION_SHAPE('','',#533);
#533 = PRODUCT_DEFINITION('design','',#534,#537);
#534 = PRODUCT_DEFINITION_FORMATION('','',#535);
#535 = PRODUCT('PC1-P8_Part3','PC1-P8_Part3','',(#536));
#536 = PRODUCT_CONTEXT('',#2,'mechanical');
#537 = PRODUCT_DEFINITION_CONTEXT('part definition',#2,'design');
#538 = ADVANCED_BREP_SHAPE_REPRESENTATION('',(#11,#539),#869);
#539 = MANIFOLD_SOLID_BREP('',#540);
#540 = CLOSED_SHELL('',(#541,#663,#738,#785,#835,#842));
#541 = ADVANCED_FACE('',(#542),#557,.T.);
#542 = FACE_BOUND('',#543,.T.);
#543 = EDGE_LOOP('',(#544,#580,#606,#639));
#544 = ORIENTED_EDGE('',*,*,#545,.T.);
#545 = EDGE_CURVE('',#546,#548,#550,.T.);
#546 = VERTEX_POINT('',#547);
#547 = CARTESIAN_POINT('',(59.241538283317,121.80611783591,12.561178));
#548 = VERTEX_POINT('',#549);
#549 = CARTESIAN_POINT('',(50.579271284871,129.54886544376,12.561178));
#550 = SURFACE_CURVE('',#551,(#556,#568),.PCURVE_S1.);
#551 = CIRCLE('',#552,7.203203230276);
#552 = AXIS2_PLACEMENT_3D('',#553,#554,#555);
#553 = CARTESIAN_POINT('',(52.072031,122.502036,12.561178));
#554 = DIRECTION('',(-0.,0.,1.));
#555 = DIRECTION('',(0.995322088537,-9.661231841487E-02,0.));
#556 = PCURVE('',#557,#562);
#557 = CYLINDRICAL_SURFACE('',#558,7.203203230276);
#558 = AXIS2_PLACEMENT_3D('',#559,#560,#561);
#559 = CARTESIAN_POINT('',(52.072031,122.502036,12.561178));
#560 = DIRECTION('',(0.,0.,1.));
#561 = DIRECTION('',(1.,0.,0.));
#562 = DEFINITIONAL_REPRESENTATION('',(#563),#567);
#563 = LINE('',#564,#565);
#564 = CARTESIAN_POINT('',(6.186422058354,0.));
#565 = VECTOR('',#566,1.);
#566 = DIRECTION('',(1.,0.));
#567 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#568 = PCURVE('',#569,#574);
#569 = PLANE('',#570);
#570 = AXIS2_PLACEMENT_3D('',#571,#572,#573);
#571 = CARTESIAN_POINT('',(52.072031,122.502036,12.561178));
#572 = DIRECTION('',(0.,0.,1.));
#573 = DIRECTION('',(1.,0.,0.));
#574 = DEFINITIONAL_REPRESENTATION('',(#575),#579);
#575 = CIRCLE('',#576,7.203203230276);
#576 = AXIS2_PLACEMENT_2D('',#577,#578);
#577 = CARTESIAN_POINT('',(0.,0.));
#578 = DIRECTION('',(0.995322088537,-9.661231841487E-02));
#579 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#580 = ORIENTED_EDGE('',*,*,#581,.T.);
#581 = EDGE_CURVE('',#548,#582,#584,.T.);
#582 = VERTEX_POINT('',#583);
#583 = CARTESIAN_POINT('',(50.579271284871,129.54886544376,18.));
#584 = SURFACE_CURVE('',#585,(#589,#595),.PCURVE_S1.);
#585 = LINE('',#586,#587);
#586 = CARTESIAN_POINT('',(50.579271284871,129.54886544376,12.561178));
#587 = VECTOR('',#588,1.);
#588 = DIRECTION('',(0.,0.,1.));
#589 = PCURVE('',#557,#590);
#590 = DEFINITIONAL_REPRESENTATION('',(#591),#594);
#591 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#592,#593),.UNSPECIFIED.,.F.,.F.,
  (2,2),(0.,5.438822),.PIECEWISE_BEZIER_KNOTS.);
#592 = CARTESIAN_POINT('',(8.062729939069,0.));
#593 = CARTESIAN_POINT('',(8.062729939069,5.438822));
#594 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#595 = PCURVE('',#596,#601);
#596 = PLANE('',#597);
#597 = AXIS2_PLACEMENT_3D('',#598,#599,#600);
#598 = CARTESIAN_POINT('',(51.008936,121.80569,28.));
#599 = DIRECTION('',(0.998464005442,5.540424023454E-02,0.));
#600 = DIRECTION('',(-5.540424023454E-02,0.998464005442,0.));
#601 = DEFINITIONAL_REPRESENTATION('',(#602),#605);
#602 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#603,#604),.UNSPECIFIED.,.F.,.F.,
  (2,2),(-5.999999999062E-07,5.4388226),.PIECEWISE_BEZIER_KNOTS.);
#603 = CARTESIAN_POINT('',(7.755087215524,-15.4388226));
#604 = CARTESIAN_POINT('',(7.755087215524,-9.9999994));
#605 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#606 = ORIENTED_EDGE('',*,*,#607,.T.);
#607 = EDGE_CURVE('',#582,#608,#610,.T.);
#608 = VERTEX_POINT('',#609);
#609 = CARTESIAN_POINT('',(59.241538283317,121.80611783591,18.));
#610 = SURFACE_CURVE('',#611,(#616,#623),.PCURVE_S1.);
#611 = CIRCLE('',#612,7.203203230276);
#612 = AXIS2_PLACEMENT_3D('',#613,#614,#615);
#613 = CARTESIAN_POINT('',(52.072031,122.502036,18.));
#614 = DIRECTION('',(0.,0.,-1.));
#615 = DIRECTION('',(-0.207235540552,0.978291076691,0.));
#616 = PCURVE('',#557,#617);
#617 = DEFINITIONAL_REPRESENTATION('',(#618),#622);
#618 = LINE('',#619,#620);
#619 = CARTESIAN_POINT('',(8.062729939069,5.438822));
#620 = VECTOR('',#621,1.);
#621 = DIRECTION('',(-1.,-0.));
#622 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#623 = PCURVE('',#624,#629);
#624 = PLANE('',#625);
#625 = AXIS2_PLACEMENT_3D('',#626,#627,#628);
#626 = CARTESIAN_POINT('',(52.072031,122.502036,18.));
#627 = DIRECTION('',(0.,0.,1.));
#628 = DIRECTION('',(1.,0.,0.));
#629 = DEFINITIONAL_REPRESENTATION('',(#630),#638);
#630 = ( BOUNDED_CURVE() B_SPLINE_CURVE(2,(#631,#632,#633,#634,#635,#636
,#637),.UNSPECIFIED.,.T.,.F.) B_SPLINE_CURVE_WITH_KNOTS((1,2,2,2,2,1),(
    -2.094395102393,0.,2.094395102393,4.188790204786,6.28318530718,
8.377580409573),.UNSPECIFIED.) CURVE() GEOMETRIC_REPRESENTATION_ITEM() 
RATIONAL_B_SPLINE_CURVE((1.,0.5,1.,0.5,1.,0.5,1.)) REPRESENTATION_ITEM(
  '') );
#631 = CARTESIAN_POINT('',(-1.492759715129,7.046829443767));
#632 = CARTESIAN_POINT('',(10.712706913748,9.632365113862));
#633 = CARTESIAN_POINT('',(6.849113172003,-2.230646886836));
#634 = CARTESIAN_POINT('',(2.985519430257,-14.09365888753));
#635 = CARTESIAN_POINT('',(-5.356353456874,-4.816182556931));
#636 = CARTESIAN_POINT('',(-13.698226344,4.461293773672));
#637 = CARTESIAN_POINT('',(-1.492759715129,7.046829443767));
#638 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#639 = ORIENTED_EDGE('',*,*,#640,.F.);
#640 = EDGE_CURVE('',#546,#608,#641,.T.);
#641 = SURFACE_CURVE('',#642,(#646,#652),.PCURVE_S1.);
#642 = LINE('',#643,#644);
#643 = CARTESIAN_POINT('',(59.241538283317,121.80611783591,12.561178));
#644 = VECTOR('',#645,1.);
#645 = DIRECTION('',(0.,0.,1.));
#646 = PCURVE('',#557,#647);
#647 = DEFINITIONAL_REPRESENTATION('',(#648),#651);
#648 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#649,#650),.UNSPECIFIED.,.F.,.F.,
  (2,2),(0.,5.438822),.PIECEWISE_BEZIER_KNOTS.);
#649 = CARTESIAN_POINT('',(6.186422058354,0.));
#650 = CARTESIAN_POINT('',(6.186422058354,5.438822));
#651 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#652 = PCURVE('',#653,#658);
#653 = PLANE('',#654);
#654 = AXIS2_PLACEMENT_3D('',#655,#656,#657);
#655 = CARTESIAN_POINT('',(77.717428,121.807078,28.));
#656 = DIRECTION('',(-5.196848995186E-05,0.99999999865,0.));
#657 = DIRECTION('',(-0.99999999865,-5.196848995186E-05,0.));
#658 = DEFINITIONAL_REPRESENTATION('',(#659),#662);
#659 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#660,#661),.UNSPECIFIED.,.F.,.F.,
  (2,2),(-5.999999999062E-07,5.4388226),.PIECEWISE_BEZIER_KNOTS.);
#660 = CARTESIAN_POINT('',(18.475889741632,-15.4388226));
#661 = CARTESIAN_POINT('',(18.475889741632,-9.9999994));
#662 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#663 = ADVANCED_FACE('',(#664,#707),#569,.F.);
#664 = FACE_BOUND('',#665,.F.);
#665 = EDGE_LOOP('',(#666,#667,#688));
#666 = ORIENTED_EDGE('',*,*,#545,.T.);
#667 = ORIENTED_EDGE('',*,*,#668,.F.);
#668 = EDGE_CURVE('',#669,#548,#671,.T.);
#669 = VERTEX_POINT('',#670);
#670 = CARTESIAN_POINT('',(51.008936,121.80569,12.561178));
#671 = SURFACE_CURVE('',#672,(#676,#682),.PCURVE_S1.);
#672 = LINE('',#673,#674);
#673 = CARTESIAN_POINT('',(50.991307023265,122.1233894874,12.561178));
#674 = VECTOR('',#675,1.);
#675 = DIRECTION('',(-5.540424023454E-02,0.998464005442,0.));
#676 = PCURVE('',#569,#677);
#677 = DEFINITIONAL_REPRESENTATION('',(#678),#681);
#678 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#679,#680),.UNSPECIFIED.,.F.,.F.,
  (2,2),(-0.318188222781,7.593513338034),.PIECEWISE_BEZIER_KNOTS.);
#679 = CARTESIAN_POINT('',(-1.063095,-0.696346));
#680 = CARTESIAN_POINT('',(-1.501436813939,7.203203230276));
#681 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#682 = PCURVE('',#596,#683);
#683 = DEFINITIONAL_REPRESENTATION('',(#684),#687);
#684 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#685,#686),.UNSPECIFIED.,.F.,.F.,
  (2,2),(-0.318188222781,7.593513338034),.PIECEWISE_BEZIER_KNOTS.);
#685 = CARTESIAN_POINT('',(0.,-15.438822));
#686 = CARTESIAN_POINT('',(7.911701560814,-15.438822));
#687 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#688 = ORIENTED_EDGE('',*,*,#689,.F.);
#689 = EDGE_CURVE('',#546,#669,#690,.T.);
#690 = SURFACE_CURVE('',#691,(#695,#701),.PCURVE_S1.);
#691 = LINE('',#692,#693);
#692 = CARTESIAN_POINT('',(64.894747592589,121.80641162466,12.561178));
#693 = VECTOR('',#694,1.);
#694 = DIRECTION('',(-0.99999999865,-5.196848995186E-05,0.));
#695 = PCURVE('',#569,#696);
#696 = DEFINITIONAL_REPRESENTATION('',(#697),#700);
#697 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#698,#699),.UNSPECIFIED.,.F.,.F.,
  (2,2),(5.619513369902,13.88581161134),.PIECEWISE_BEZIER_KNOTS.);
#698 = CARTESIAN_POINT('',(7.203203230276,-0.695916412963));
#699 = CARTESIAN_POINT('',(-1.063095,-0.696346));
#700 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#701 = PCURVE('',#653,#702);
#702 = DEFINITIONAL_REPRESENTATION('',(#703),#706);
#703 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#704,#705),.UNSPECIFIED.,.F.,.F.,
  (2,2),(5.619513369902,13.88581161134),.PIECEWISE_BEZIER_KNOTS.);
#704 = CARTESIAN_POINT('',(18.442193794628,-15.438822));
#705 = CARTESIAN_POINT('',(26.708492036066,-15.438822));
#706 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#707 = FACE_BOUND('',#708,.F.);
#708 = EDGE_LOOP('',(#709));
#709 = ORIENTED_EDGE('',*,*,#710,.F.);
#710 = EDGE_CURVE('',#711,#711,#713,.T.);
#711 = VERTEX_POINT('',#712);
#712 = CARTESIAN_POINT('',(52.347031,122.502036,12.561178));
#713 = SURFACE_CURVE('',#714,(#719,#726),.PCURVE_S1.);
#714 = CIRCLE('',#715,0.275);
#715 = AXIS2_PLACEMENT_3D('',#716,#717,#718);
#716 = CARTESIAN_POINT('',(52.072031,122.502036,12.561178));
#717 = DIRECTION('',(0.,0.,1.));
#718 = DIRECTION('',(1.,0.,0.));
#719 = PCURVE('',#569,#720);
#720 = DEFINITIONAL_REPRESENTATION('',(#721),#725);
#721 = CIRCLE('',#722,0.275);
#722 = AXIS2_PLACEMENT_2D('',#723,#724);
#723 = CARTESIAN_POINT('',(0.,0.));
#724 = DIRECTION('',(1.,0.));
#725 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#726 = PCURVE('',#727,#732);
#727 = CYLINDRICAL_SURFACE('',#728,0.275);
#728 = AXIS2_PLACEMENT_3D('',#729,#730,#731);
#729 = CARTESIAN_POINT('',(52.072031,122.502036,12.561178));
#730 = DIRECTION('',(0.,0.,1.));
#731 = DIRECTION('',(1.,0.,0.));
#732 = DEFINITIONAL_REPRESENTATION('',(#733),#737);
#733 = LINE('',#734,#735);
#734 = CARTESIAN_POINT('',(0.,0.));
#735 = VECTOR('',#736,1.);
#736 = DIRECTION('',(1.,0.));
#737 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#738 = ADVANCED_FACE('',(#739),#653,.F.);
#739 = FACE_BOUND('',#740,.F.);
#740 = EDGE_LOOP('',(#741,#764,#783,#784));
#741 = ORIENTED_EDGE('',*,*,#742,.F.);
#742 = EDGE_CURVE('',#743,#669,#745,.T.);
#743 = VERTEX_POINT('',#744);
#744 = CARTESIAN_POINT('',(51.008936,121.80569,18.));
#745 = SURFACE_CURVE('',#746,(#750,#757),.PCURVE_S1.);
#746 = LINE('',#747,#748);
#747 = CARTESIAN_POINT('',(51.008936,121.80569,28.));
#748 = VECTOR('',#749,1.);
#749 = DIRECTION('',(0.,0.,-1.));
#750 = PCURVE('',#653,#751);
#751 = DEFINITIONAL_REPRESENTATION('',(#752),#756);
#752 = LINE('',#753,#754);
#753 = CARTESIAN_POINT('',(26.708492036066,0.));
#754 = VECTOR('',#755,1.);
#755 = DIRECTION('',(-0.,-1.));
#756 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#757 = PCURVE('',#596,#758);
#758 = DEFINITIONAL_REPRESENTATION('',(#759),#763);
#759 = LINE('',#760,#761);
#760 = CARTESIAN_POINT('',(0.,0.));
#761 = VECTOR('',#762,1.);
#762 = DIRECTION('',(0.,-1.));
#763 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#764 = ORIENTED_EDGE('',*,*,#765,.F.);
#765 = EDGE_CURVE('',#608,#743,#766,.T.);
#766 = SURFACE_CURVE('',#767,(#771,#777),.PCURVE_S1.);
#767 = LINE('',#768,#769);
#768 = CARTESIAN_POINT('',(64.894747592589,121.80641162466,18.));
#769 = VECTOR('',#770,1.);
#770 = DIRECTION('',(-0.99999999865,-5.196848995186E-05,0.));
#771 = PCURVE('',#653,#772);
#772 = DEFINITIONAL_REPRESENTATION('',(#773),#776);
#773 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#774,#775),.UNSPECIFIED.,.F.,.F.,
  (2,2),(5.619513369902,13.88581161134),.PIECEWISE_BEZIER_KNOTS.);
#774 = CARTESIAN_POINT('',(18.442193794628,-10.));
#775 = CARTESIAN_POINT('',(26.708492036066,-10.));
#776 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#777 = PCURVE('',#624,#778);
#778 = DEFINITIONAL_REPRESENTATION('',(#779),#782);
#779 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#780,#781),.UNSPECIFIED.,.F.,.F.,
  (2,2),(5.619513369902,13.88581161134),.PIECEWISE_BEZIER_KNOTS.);
#780 = CARTESIAN_POINT('',(7.203203230276,-0.695916412963));
#781 = CARTESIAN_POINT('',(-1.063095,-0.696346));
#782 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#783 = ORIENTED_EDGE('',*,*,#640,.F.);
#784 = ORIENTED_EDGE('',*,*,#689,.T.);
#785 = ADVANCED_FACE('',(#786,#809),#624,.T.);
#786 = FACE_BOUND('',#787,.T.);
#787 = EDGE_LOOP('',(#788,#789,#790));
#788 = ORIENTED_EDGE('',*,*,#765,.F.);
#789 = ORIENTED_EDGE('',*,*,#607,.F.);
#790 = ORIENTED_EDGE('',*,*,#791,.F.);
#791 = EDGE_CURVE('',#743,#582,#792,.T.);
#792 = SURFACE_CURVE('',#793,(#797,#803),.PCURVE_S1.);
#793 = LINE('',#794,#795);
#794 = CARTESIAN_POINT('',(50.991307023265,122.1233894874,18.));
#795 = VECTOR('',#796,1.);
#796 = DIRECTION('',(-5.540424023454E-02,0.998464005442,0.));
#797 = PCURVE('',#624,#798);
#798 = DEFINITIONAL_REPRESENTATION('',(#799),#802);
#799 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#800,#801),.UNSPECIFIED.,.F.,.F.,
  (2,2),(-0.318188222781,7.593513338034),.PIECEWISE_BEZIER_KNOTS.);
#800 = CARTESIAN_POINT('',(-1.063095,-0.696346));
#801 = CARTESIAN_POINT('',(-1.501436813939,7.203203230276));
#802 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#803 = PCURVE('',#596,#804);
#804 = DEFINITIONAL_REPRESENTATION('',(#805),#808);
#805 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#806,#807),.UNSPECIFIED.,.F.,.F.,
  (2,2),(-0.318188222781,7.593513338034),.PIECEWISE_BEZIER_KNOTS.);
#806 = CARTESIAN_POINT('',(0.,-10.));
#807 = CARTESIAN_POINT('',(7.911701560814,-10.));
#808 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#809 = FACE_BOUND('',#810,.T.);
#810 = EDGE_LOOP('',(#811));
#811 = ORIENTED_EDGE('',*,*,#812,.F.);
#812 = EDGE_CURVE('',#813,#813,#815,.T.);
#813 = VERTEX_POINT('',#814);
#814 = CARTESIAN_POINT('',(52.347031,122.502036,18.));
#815 = SURFACE_CURVE('',#816,(#821,#828),.PCURVE_S1.);
#816 = CIRCLE('',#817,0.275);
#817 = AXIS2_PLACEMENT_3D('',#818,#819,#820);
#818 = CARTESIAN_POINT('',(52.072031,122.502036,18.));
#819 = DIRECTION('',(0.,0.,1.));
#820 = DIRECTION('',(1.,0.,0.));
#821 = PCURVE('',#624,#822);
#822 = DEFINITIONAL_REPRESENTATION('',(#823),#827);
#823 = CIRCLE('',#824,0.275);
#824 = AXIS2_PLACEMENT_2D('',#825,#826);
#825 = CARTESIAN_POINT('',(0.,0.));
#826 = DIRECTION('',(1.,0.));
#827 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#828 = PCURVE('',#727,#829);
#829 = DEFINITIONAL_REPRESENTATION('',(#830),#834);
#830 = LINE('',#831,#832);
#831 = CARTESIAN_POINT('',(0.,5.438822));
#832 = VECTOR('',#833,1.);
#833 = DIRECTION('',(1.,0.));
#834 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#835 = ADVANCED_FACE('',(#836),#596,.F.);
#836 = FACE_BOUND('',#837,.F.);
#837 = EDGE_LOOP('',(#838,#839,#840,#841));
#838 = ORIENTED_EDGE('',*,*,#791,.F.);
#839 = ORIENTED_EDGE('',*,*,#742,.T.);
#840 = ORIENTED_EDGE('',*,*,#668,.T.);
#841 = ORIENTED_EDGE('',*,*,#581,.T.);
#842 = ADVANCED_FACE('',(#843),#727,.F.);
#843 = FACE_BOUND('',#844,.F.);
#844 = EDGE_LOOP('',(#845,#846,#867,#868));
#845 = ORIENTED_EDGE('',*,*,#812,.F.);
#846 = ORIENTED_EDGE('',*,*,#847,.F.);
#847 = EDGE_CURVE('',#711,#813,#848,.T.);
#848 = SEAM_CURVE('',#849,(#853,#860),.PCURVE_S1.);
#849 = LINE('',#850,#851);
#850 = CARTESIAN_POINT('',(52.347031,122.502036,12.561178));
#851 = VECTOR('',#852,1.);
#852 = DIRECTION('',(0.,0.,1.));
#853 = PCURVE('',#727,#854);
#854 = DEFINITIONAL_REPRESENTATION('',(#855),#859);
#855 = LINE('',#856,#857);
#856 = CARTESIAN_POINT('',(6.28318530718,-0.));
#857 = VECTOR('',#858,1.);
#858 = DIRECTION('',(0.,1.));
#859 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#860 = PCURVE('',#727,#861);
#861 = DEFINITIONAL_REPRESENTATION('',(#862),#866);
#862 = LINE('',#863,#864);
#863 = CARTESIAN_POINT('',(0.,-0.));
#864 = VECTOR('',#865,1.);
#865 = DIRECTION('',(0.,1.));
#866 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#867 = ORIENTED_EDGE('',*,*,#710,.T.);
#868 = ORIENTED_EDGE('',*,*,#847,.T.);
#869 = ( GEOMETRIC_REPRESENTATION_CONTEXT(3) 
GLOBAL_UNCERTAINTY_ASSIGNED_CONTEXT((#873)) GLOBAL_UNIT_ASSIGNED_CONTEXT
((#870,#871,#872)) REPRESENTATION_CONTEXT('Context #1',
  '3D Context with UNIT and UNCERTAINTY') );
#870 = ( LENGTH_UNIT() NAMED_UNIT(*) SI_UNIT(.MILLI.,.METRE.) );
#871 = ( NAMED_UNIT(*) PLANE_ANGLE_UNIT() SI_UNIT($,.RADIAN.) );
#872 = ( NAMED_UNIT(*) SI_UNIT($,.STERADIAN.) SOLID_ANGLE_UNIT() );
#873 = UNCERTAINTY_MEASURE_WITH_UNIT(LENGTH_MEASURE(1.E-07),#870,
  'distance_accuracy_value','confusion accuracy');
#874 = CONTEXT_DEPENDENT_SHAPE_REPRESENTATION(#875,#877);
#875 = ( REPRESENTATION_RELATIONSHIP('','',#538,#10) 
REPRESENTATION_RELATIONSHIP_WITH_TRANSFORMATION(#876) 
SHAPE_REPRESENTATION_RELATIONSHIP() );
#876 = ITEM_DEFINED_TRANSFORMATION('','',#11,#23);
#877 = PRODUCT_DEFINITION_SHAPE('Placement','Placement of an item',#878
  );
#878 = NEXT_ASSEMBLY_USAGE_OCCURRENCE('56','=>[0:1:1:4]','',#5,#533,$);
#879 = PRODUCT_RELATED_PRODUCT_CATEGORY('part',$,(#535));
#880 = MECHANICAL_DESIGN_GEOMETRIC_PRESENTATION_REPRESENTATION('',(#881)
  ,#520);
#881 = STYLED_ITEM('color',(#882),#158);
#882 = PRESENTATION_STYLE_ASSIGNMENT((#883));
#883 = SURFACE_STYLE_USAGE(.BOTH.,#884);
#884 = SURFACE_SIDE_STYLE('',(#885));
#885 = SURFACE_STYLE_FILL_AREA(#886);
#886 = FILL_AREA_STYLE('',(#887));
#887 = FILL_AREA_STYLE_COLOUR('',#888);
#888 = DRAUGHTING_PRE_DEFINED_COLOUR('green');
#889 = MECHANICAL_DESIGN_GEOMETRIC_PRESENTATION_REPRESENTATION('',(#890)
  ,#869);
#890 = STYLED_ITEM('color',(#891),#539);
#891 = PRESENTATION_STYLE_ASSIGNMENT((#892));
#892 = SURFACE_STYLE_USAGE(.BOTH.,#893);
#893 = SURFACE_SIDE_STYLE('',(#894));
#894 = SURFACE_STYLE_FILL_AREA(#895);
#895 = FILL_AREA_STYLE('',(#896));
#896 = FILL_AREA_STYLE_COLOUR('',#897);
#897 = DRAUGHTING_PRE_DEFINED_COLOUR('red');
#898 = MECHANICAL_DESIGN_GEOMETRIC_PRESENTATION_REPRESENTATION('',(#899)
  ,#139);
#899 = STYLED_ITEM('color',(#900),#41);
#900 = PRESENTATION_STYLE_ASSIGNMENT((#901));
#901 = SURFACE_STYLE_USAGE(.BOTH.,#902);
#902 = SURFACE_SIDE_STYLE('',(#903));
#903 = SURFACE_STYLE_FILL_AREA(#904);
#904 = FILL_AREA_STYLE('',(#905));
#905 = FILL_AREA_STYLE_COLOUR('',#906);
#906 = DRAUGHTING_PRE_DEFINED_COLOUR('blue');
ENDSEC;
END-ISO-10303-21;
